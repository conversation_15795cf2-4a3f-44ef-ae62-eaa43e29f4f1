using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;
using System.IO;
using IPIS.Utils;
using IPIS.Services;
using IPIS.Repositories;
using IPIS.Models;
using UserModel = IPIS.Models.User;

namespace IPIS.Forms.User
{
    public partial class LoginForm : Form
    {
        private Panel mainPanel;
        private Panel leftPanel;
        private Panel rightPanel;
        private Label titleLabel;
        private Label subtitleLabel;
        private Panel usernamePanel;
        private Panel passwordPanel;
        private TextBox txtUsername;
        private TextBox txtPassword;
        private Button btnLogin;
        private Button btnCancel;
        private Label lblUsername;
        private Label lblPassword;
        private PictureBox logoBox;
        private Label welcomeLabel;
        private Label systemLabel;
        private Panel buttonPanel;
        private Label poweredByLabel;
        private readonly ToastNotification toast;
        private readonly UserService userService;
        private readonly LoginConfigurationService configService;
        private LoginConfiguration loginConfig;

        public LoginForm()
        {
            // Initialize services and configuration BEFORE InitializeComponent
            userService = new UserService(new SQLiteUserRepository());
            configService = new LoginConfigurationService(new SQLiteLoginConfigurationRepository());
            loginConfig = configService.GetLoginConfiguration();

            // Now initialize the component with configuration available
            InitializeComponent();

            // Initialize toast notification AFTER the form is initialized
            toast = new ToastNotification(this);
        }

        private void InitializeComponent()
        {
            // Form properties
            this.Size = new Size(900, 600);
            this.FormBorderStyle = FormBorderStyle.None;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = loginConfig.GetBackgroundColor();
            this.Text = $"{loginConfig.StationName} - Login";

            CreateMainLayout();
            CreateLeftPanel();
            CreateRightPanel();
            SetupEventHandlers();
        }

        private void CreateMainLayout()
        {
            // Main container panel
            mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.Transparent
            };
            this.Controls.Add(mainPanel);

            // Left panel for branding
            leftPanel = new Panel
            {
                Size = new Size(450, 600),
                Location = new Point(0, 0),
                BackColor = loginConfig.GetPrimaryColor()
            };

            // Add background image if configured
            if (loginConfig.UseBackgroundImage && !string.IsNullOrEmpty(loginConfig.BackgroundImagePath) && File.Exists(loginConfig.BackgroundImagePath))
            {
                try
                {
                    leftPanel.BackgroundImage = Image.FromFile(loginConfig.BackgroundImagePath);
                    leftPanel.BackgroundImageLayout = ImageLayout.Stretch;
                }
                catch
                {
                    // If image loading fails, keep the solid color background
                }
            }

            mainPanel.Controls.Add(leftPanel);

            // Right panel for login form
            rightPanel = new Panel
            {
                Size = new Size(450, 600),
                BackColor = Color.White
            };
            rightPanel.Location = new Point(450, 0);
            mainPanel.Controls.Add(rightPanel);
        }

        private void CreateLeftPanel()
        {
            // Logo/Icon
            logoBox = new PictureBox
            {
                Size = new Size(80, 80),
                Location = new Point(60, 50),
                SizeMode = PictureBoxSizeMode.StretchImage,
                Image = GetLogoImage()
            };
            leftPanel.Controls.Add(logoBox);

            // Welcome label
            welcomeLabel = new Label
            {
                Text = loginConfig.WelcomeMessage,
                Font = new Font("Segoe UI", 24, FontStyle.Regular),
                ForeColor = loginConfig.GetStationTextColor(),
                Location = new Point(50, 150),
                AutoSize = true,
                BackColor = Color.Transparent
            };
            leftPanel.Controls.Add(welcomeLabel);

            // System title (Station Name)
            systemLabel = new Label
            {
                Text = loginConfig.StationName,
                Font = new Font("Segoe UI", 48, FontStyle.Bold),
                ForeColor = loginConfig.GetStationTextColor(),
                Location = new Point(35, 190),
                AutoSize = true,
                BackColor = Color.Transparent
            };
            leftPanel.Controls.Add(systemLabel);

            // Subtitle - aligned with welcome message start position
            subtitleLabel = new Label
            {
                Text = loginConfig.SubtitleMessage,
                Font = new Font("Segoe UI", 16, FontStyle.Regular),
                ForeColor = loginConfig.GetStationTextColor(),
                Location = new Point(50, 290),
                AutoSize = true,
                BackColor = Color.Transparent
            };
            leftPanel.Controls.Add(subtitleLabel);
        }

        private void CreateRightPanel()
        {
            // Login title
            titleLabel = new Label
            {
                Text = "Sign In",
                Font = new Font("Segoe UI", 32, FontStyle.Bold),
                ForeColor = Color.FromArgb(33, 37, 41),
                Location = new Point(60, 80),
                AutoSize = true
            };
            rightPanel.Controls.Add(titleLabel);

            // Username section
            CreateUsernameSection();

            // Password section
            CreatePasswordSection();

            // Button section
            CreateButtonSection();

            // Close button (X)
            CreateCloseButton();

            // Powered by Digispin footer
            CreatePoweredByFooter();
        }

        private void CreateUsernameSection()
        {
            // Username label
            lblUsername = new Label
            {
                Text = "Username",
                Font = new Font("Segoe UI", 12, FontStyle.Regular),
                ForeColor = Color.FromArgb(73, 80, 87),
                Location = new Point(60, 180),
                AutoSize = true
            };
            rightPanel.Controls.Add(lblUsername);

            // Username panel with border
            usernamePanel = new Panel
            {
                Size = new Size(320, 50),
                Location = new Point(60, 210),
                BackColor = Color.White,
                BorderStyle = BorderStyle.None
            };
            usernamePanel.Paint += (s, e) => DrawTextBoxBorder(e, usernamePanel, txtUsername.Focused);
            rightPanel.Controls.Add(usernamePanel);

            // Username textbox
            txtUsername = new TextBox
            {
                Size = new Size(300, 30),
                Location = new Point(10, 10),
                Font = new Font("Segoe UI", 12),
                BorderStyle = BorderStyle.None,
                BackColor = Color.White,
                TabIndex = 0
            };
            txtUsername.GotFocus += (s, e) => usernamePanel.Invalidate();
            txtUsername.LostFocus += (s, e) => usernamePanel.Invalidate();
            usernamePanel.Controls.Add(txtUsername);
        }

        private void CreatePasswordSection()
        {
            // Password label
            lblPassword = new Label
            {
                Text = "Password",
                Font = new Font("Segoe UI", 12, FontStyle.Regular),
                ForeColor = Color.FromArgb(73, 80, 87),
                Location = new Point(60, 290),
                AutoSize = true
            };
            rightPanel.Controls.Add(lblPassword);

            // Password panel with border
            passwordPanel = new Panel
            {
                Size = new Size(320, 50),
                Location = new Point(60, 320),
                BackColor = Color.White,
                BorderStyle = BorderStyle.None
            };
            passwordPanel.Paint += (s, e) => DrawTextBoxBorder(e, passwordPanel, txtPassword.Focused);
            rightPanel.Controls.Add(passwordPanel);

            // Password textbox
            txtPassword = new TextBox
            {
                Size = new Size(300, 30),
                Location = new Point(10, 10),
                Font = new Font("Segoe UI", 12),
                BorderStyle = BorderStyle.None,
                BackColor = Color.White,
                PasswordChar = '●',
                TabIndex = 1
            };
            txtPassword.GotFocus += (s, e) => passwordPanel.Invalidate();
            txtPassword.LostFocus += (s, e) => passwordPanel.Invalidate();
            passwordPanel.Controls.Add(txtPassword);
        }

        private void CreateButtonSection()
        {
            // Button panel
            buttonPanel = new Panel
            {
                Size = new Size(320, 100),
                Location = new Point(60, 400),
                BackColor = Color.Transparent
            };
            rightPanel.Controls.Add(buttonPanel);

            // Login button
            btnLogin = new Button
            {
                Text = "Sign In",
                Size = new Size(320, 45),
                Location = new Point(0, 0),
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                TabIndex = 2,
                FlatStyle = FlatStyle.Flat,
                BackColor = loginConfig.GetPrimaryColor(),
                ForeColor = Color.White,
                Cursor = Cursors.Hand
            };
            btnLogin.FlatAppearance.BorderSize = 0;

            // Create darker shades for hover and click effects
            Color primaryColor = loginConfig.GetPrimaryColor();
            Color hoverColor = ControlPaint.Dark(primaryColor, 0.1f);
            Color clickColor = ControlPaint.Dark(primaryColor, 0.2f);

            btnLogin.FlatAppearance.MouseOverBackColor = hoverColor;
            btnLogin.FlatAppearance.MouseDownBackColor = clickColor;
            buttonPanel.Controls.Add(btnLogin);

            // Cancel button
            btnCancel = new Button
            {
                Text = "Cancel",
                Size = new Size(320, 40),
                Location = new Point(0, 55),
                Font = new Font("Segoe UI", 11, FontStyle.Regular),
                TabIndex = 3,
                FlatStyle = FlatStyle.Flat,
                BackColor = Color.Transparent,
                ForeColor = Color.FromArgb(108, 117, 125),
                Cursor = Cursors.Hand,
                DialogResult = DialogResult.Cancel
            };
            btnCancel.FlatAppearance.BorderSize = 1;
            btnCancel.FlatAppearance.BorderColor = Color.FromArgb(206, 212, 218);
            btnCancel.FlatAppearance.MouseOverBackColor = Color.FromArgb(248, 249, 250);
            buttonPanel.Controls.Add(btnCancel);
        }

        private void CreateCloseButton()
        {
            Button closeButton = new Button
            {
                Text = "×",
                Size = new Size(30, 40),
                Location = new Point(410, 10),
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                FlatStyle = FlatStyle.Flat,
                BackColor = Color.Transparent,
                ForeColor = Color.FromArgb(108, 117, 125),
                Cursor = Cursors.Hand
            };
            closeButton.FlatAppearance.BorderSize = 0;
            closeButton.FlatAppearance.MouseOverBackColor = Color.FromArgb(248, 249, 250);
            closeButton.Click += (s, e) => this.Close();
            rightPanel.Controls.Add(closeButton);
        }

        private void CreatePoweredByFooter()
        {
            poweredByLabel = new Label
            {
                Text = "Powered by Digispin",
                Font = new Font("Segoe UI", 9, FontStyle.Regular),
                ForeColor = Color.FromArgb(108, 117, 125),
                Location = new Point(60, 550),
                AutoSize = true,
                BackColor = Color.Transparent
            };
            rightPanel.Controls.Add(poweredByLabel);
        }

        private Image GetLogoImage()
        {
            // Try to load custom logo if configured
            if (loginConfig.UseCustomLogo && !string.IsNullOrEmpty(loginConfig.LogoPath) && File.Exists(loginConfig.LogoPath))
            {
                try
                {
                    return Image.FromFile(loginConfig.LogoPath);
                }
                catch
                {
                    // If custom logo fails to load, fall back to default
                }
            }

            // Create default train/railway icon
            return CreateDefaultLogoImage();
        }

        private Image CreateDefaultLogoImage()
        {
            // Create a simple train/railway icon
            Bitmap logo = new Bitmap(80, 80);
            using (Graphics g = Graphics.FromImage(logo))
            {
                g.SmoothingMode = SmoothingMode.AntiAlias;
                g.Clear(Color.Transparent);

                // Draw a simple train icon
                using (Brush brush = new SolidBrush(Color.White))
                {
                    // Train body
                    g.FillRectangle(brush, 10, 30, 60, 25);
                    // Train front
                    g.FillRectangle(brush, 5, 35, 10, 15);
                    // Wheels
                    g.FillEllipse(brush, 15, 50, 8, 8);
                    g.FillEllipse(brush, 30, 50, 8, 8);
                    g.FillEllipse(brush, 45, 50, 8, 8);
                    g.FillEllipse(brush, 60, 50, 8, 8);
                }
            }
            return logo;
        }

        private void DrawTextBoxBorder(PaintEventArgs e, Panel panel, bool focused)
        {
            Color borderColor = focused ? loginConfig.GetPrimaryColor() : Color.FromArgb(206, 212, 218);
            int borderWidth = focused ? 2 : 1;

            using (Pen pen = new Pen(borderColor, borderWidth))
            {
                Rectangle rect = new Rectangle(0, 0, panel.Width - 1, panel.Height - 1);
                e.Graphics.DrawRectangle(pen, rect);
            }
        }

        private void SetupEventHandlers()
        {
            // Form events
            this.KeyPreview = true;
            this.KeyDown += LoginForm_KeyDown;

            // Button events
            btnLogin.Click += BtnLogin_Click;
            btnCancel.Click += (s, e) => this.Close();

            // Enter key handling
            txtPassword.KeyDown += (s, e) =>
            {
                if (e.KeyCode == Keys.Enter)
                {
                    BtnLogin_Click(s, e);
                }
            };

            // Set accept and cancel buttons
            this.AcceptButton = btnLogin;
            this.CancelButton = btnCancel;
        }

        private void LoginForm_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Escape)
            {
                this.Close();
            }
        }

        private void BtnLogin_Click(object sender, EventArgs e)
        {
            // Validate input
            if (string.IsNullOrWhiteSpace(txtUsername.Text))
            {
                toast.ShowError("Please enter your username.");
                txtUsername.Focus();
                return;
            }

            if (string.IsNullOrWhiteSpace(txtPassword.Text))
            {
                toast.ShowError("Please enter your password.");
                txtPassword.Focus();
                return;
            }

            // Disable login button to prevent multiple clicks
            btnLogin.Enabled = false;
            btnLogin.Text = "Signing In...";

            try
            {
                // Authenticate user
                UserModel authenticatedUser = userService.AuthenticateUser(txtUsername.Text, txtPassword.Text);

                if (authenticatedUser != null)
                {
                    // Set up session
                    SessionManager.Login(authenticatedUser);

                    toast.ShowSuccess($"Welcome, {authenticatedUser.Username}!");
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    toast.ShowError("Invalid username or password.");
                    txtPassword.Clear();
                    txtUsername.Focus();
                }
            }
            catch (Exception ex)
            {
                toast.ShowError($"Login error: {ex.Message}");
            }
            finally
            {
                // Re-enable login button
                btnLogin.Enabled = true;
                btnLogin.Text = "Sign In";
            }
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            base.OnPaint(e);

            // Add subtle shadow effect to the form
            using (Pen shadowPen = new Pen(Color.FromArgb(50, 0, 0, 0), 2))
            {
                e.Graphics.DrawRectangle(shadowPen, 2, 2, this.Width - 4, this.Height - 4);
            }
        }
    }
}