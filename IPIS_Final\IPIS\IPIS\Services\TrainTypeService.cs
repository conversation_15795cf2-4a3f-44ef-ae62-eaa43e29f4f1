using System;
using System.Data;
using System.Collections.Generic;
using IPIS.Repositories.Interfaces;
using IPIS.Models;

namespace IPIS.Services
{
    public class TrainTypeService
    {
        private readonly ITrainTypeRepository trainTypeRepository;

        public TrainTypeService(ITrainTypeRepository trainTypeRepository)
        {
            this.trainTypeRepository = trainTypeRepository;
        }

        public DataTable GetAllTrainTypes()
        {
            return trainTypeRepository.GetAllTrainTypes();
        }

        public List<TrainType> GetActiveTrainTypes()
        {
            return trainTypeRepository.GetActiveTrainTypes();
        }

        public TrainType GetTrainTypeById(string id)
        {
            return trainTypeRepository.GetTrainTypeById(id);
        }

        public TrainType GetTrainTypeByName(string name)
        {
            return trainTypeRepository.GetTrainTypeByName(name);
        }

        public void AddTrainType(TrainType trainType)
        {
            // Validate business rules
            if (string.IsNullOrWhiteSpace(trainType.Name))
            {
                throw new ArgumentException("Train type name is required.");
            }

            if (trainType.Name.Trim().Length < 2)
            {
                throw new ArgumentException("Train type name must be at least 2 characters long.");
            }

            if (trainTypeRepository.TrainTypeExists(trainType.Name.Trim()))
            {
                throw new InvalidOperationException($"Train type '{trainType.Name}' already exists.");
            }

            // Normalize data
            trainType.Name = trainType.Name.Trim();
            trainType.Description = trainType.Description?.Trim() ?? string.Empty;

            trainTypeRepository.AddTrainType(trainType);
        }

        public void UpdateTrainType(TrainType trainType)
        {
            // Validate business rules
            if (string.IsNullOrWhiteSpace(trainType.Name))
            {
                throw new ArgumentException("Train type name is required.");
            }

            if (trainType.Name.Trim().Length < 2)
            {
                throw new ArgumentException("Train type name must be at least 2 characters long.");
            }

            if (trainTypeRepository.TrainTypeExists(trainType.Name.Trim(), trainType.ID))
            {
                throw new InvalidOperationException($"Train type '{trainType.Name}' already exists.");
            }

            // Normalize data
            trainType.Name = trainType.Name.Trim();
            trainType.Description = trainType.Description?.Trim() ?? string.Empty;

            trainTypeRepository.UpdateTrainType(trainType);
        }

        public void DeleteTrainType(string id)
        {
            if (string.IsNullOrWhiteSpace(id))
            {
                throw new ArgumentException("Train type ID is required.");
            }

            var trainType = trainTypeRepository.GetTrainTypeById(id);
            if (trainType == null)
            {
                throw new InvalidOperationException("Train type not found.");
            }

            // TODO: Add validation to check if train type is being used by any trains
            // This would require checking the Train_Data table for references

            trainTypeRepository.DeleteTrainType(id);
        }

        public List<string> GetActiveTrainTypeNames()
        {
            var trainTypes = GetActiveTrainTypes();
            var names = new List<string>();
            foreach (var trainType in trainTypes)
            {
                names.Add(trainType.Name);
            }
            return names;
        }
    }
}
