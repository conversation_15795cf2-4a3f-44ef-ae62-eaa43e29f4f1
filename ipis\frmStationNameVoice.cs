// Decompiled with JetBrains decompiler
// Type: ipis.frmStationNameVoice
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using ipis.My;
using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Runtime.CompilerServices;
using System.Windows.Forms;
using System.Xml;

namespace ipis
{

[DesignerGenerated]
public class frmStationNameVoice : Form
{
  private static List<WeakReference> __ENCList = new List<WeakReference>();
  private IContainer components;
  [AccessedThroughProperty("lblLang")]
  private Label _lblLang;
  [AccessedThroughProperty("cmbLang")]
  private ComboBox _cmbLang;
  [AccessedThroughProperty("btnExit")]
  private Button _btnExit;
  [AccessedThroughProperty("lblStationName")]
  private Label _lblStationName;
  [AccessedThroughProperty("btnBrowse")]
  private Button _btnBrowse;
  [AccessedThroughProperty("lblFile")]
  private Label _lblFile;
  [AccessedThroughProperty("txtFilePath")]
  private TextBox _txtFilePath;
  [AccessedThroughProperty("btnSave")]
  private Button _btnSave;
  [AccessedThroughProperty("cmbStationName")]
  private ComboBox _cmbStationName;
  private StreamWriter sw;

  [DebuggerNonUserCode]
  static frmStationNameVoice()
  {
  }

  [DebuggerNonUserCode]
  public frmStationNameVoice()
  {
    this.FormClosing += new FormClosingEventHandler(this.frmStationNameVoice_FormClosing);
    this.Load += new EventHandler(this.frmStationNameVoice_Load);
    frmStationNameVoice.__ENCAddToList((object) this);
    this.InitializeComponent();
  }

  [DebuggerNonUserCode]
  private static void __ENCAddToList(object value)
  {
    lock (frmStationNameVoice.__ENCList)
    {
      if (frmStationNameVoice.__ENCList.Count == frmStationNameVoice.__ENCList.Capacity)
      {
        int index1 = 0;
        int num = checked (frmStationNameVoice.__ENCList.Count - 1);
        int index2 = 0;
        while (index2 <= num)
        {
          if (frmStationNameVoice.__ENCList[index2].IsAlive)
          {
            if (index2 != index1)
              frmStationNameVoice.__ENCList[index1] = frmStationNameVoice.__ENCList[index2];
            checked { ++index1; }
          }
          checked { ++index2; }
        }
        frmStationNameVoice.__ENCList.RemoveRange(index1, checked (frmStationNameVoice.__ENCList.Count - index1));
        frmStationNameVoice.__ENCList.Capacity = frmStationNameVoice.__ENCList.Count;
      }
      frmStationNameVoice.__ENCList.Add(new WeakReference(RuntimeHelpers.GetObjectValue(value)));
    }
  }

  [DebuggerNonUserCode]
  protected override void Dispose(bool disposing)
  {
    try
    {
      if (!disposing || this.components == null)
        return;
      this.components.Dispose();
    }
    finally
    {
      base.Dispose(disposing);
    }
  }

  [DebuggerStepThrough]
  private void InitializeComponent()
  {
    this.lblLang = new Label();
    this.cmbLang = new ComboBox();
    this.btnExit = new Button();
    this.lblStationName = new Label();
    this.btnBrowse = new Button();
    this.lblFile = new Label();
    this.txtFilePath = new TextBox();
    this.btnSave = new Button();
    this.cmbStationName = new ComboBox();
    this.SuspendLayout();
    this.lblLang.AutoSize = true;
    this.lblLang.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblLang1 = this.lblLang;
    Point point1 = new Point(32 /*0x20*/, 62);
    Point point2 = point1;
    lblLang1.Location = point2;
    this.lblLang.Name = "lblLang";
    Label lblLang2 = this.lblLang;
    Size size1 = new Size(77, 16 /*0x10*/);
    Size size2 = size1;
    lblLang2.Size = size2;
    this.lblLang.TabIndex = 32 /*0x20*/;
    this.lblLang.Text = "Language";
    this.cmbLang.FormattingEnabled = true;
    this.cmbLang.Items.AddRange(new object[3]
    {
      (object) "English",
      (object) "Hindi",
      (object) "Regional"
    });
    ComboBox cmbLang1 = this.cmbLang;
    point1 = new Point(130, 59);
    Point point3 = point1;
    cmbLang1.Location = point3;
    this.cmbLang.Name = "cmbLang";
    ComboBox cmbLang2 = this.cmbLang;
    size1 = new Size(73, 21);
    Size size3 = size1;
    cmbLang2.Size = size3;
    this.cmbLang.TabIndex = 2;
    this.btnExit.BackColor = Color.SeaShell;
    this.btnExit.DialogResult = DialogResult.Cancel;
    this.btnExit.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.btnExit.ForeColor = SystemColors.ControlText;
    Button btnExit1 = this.btnExit;
    point1 = new Point(242, 164);
    Point point4 = point1;
    btnExit1.Location = point4;
    this.btnExit.Name = "btnExit";
    Button btnExit2 = this.btnExit;
    size1 = new Size(60, 25);
    Size size4 = size1;
    btnExit2.Size = size4;
    this.btnExit.TabIndex = 6;
    this.btnExit.Text = "Exit";
    this.btnExit.UseVisualStyleBackColor = false;
    this.lblStationName.AutoSize = true;
    this.lblStationName.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.lblStationName.ForeColor = SystemColors.ControlText;
    Label lblStationName1 = this.lblStationName;
    point1 = new Point(19, 21);
    Point point5 = point1;
    lblStationName1.Location = point5;
    this.lblStationName.Name = "lblStationName";
    Label lblStationName2 = this.lblStationName;
    size1 = new Size(97, 16 /*0x10*/);
    Size size5 = size1;
    lblStationName2.Size = size5;
    this.lblStationName.TabIndex = 30;
    this.lblStationName.Text = "StationName";
    this.btnBrowse.BackColor = Color.SeaShell;
    this.btnBrowse.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.btnBrowse.ForeColor = SystemColors.ControlText;
    Button btnBrowse1 = this.btnBrowse;
    point1 = new Point(339, 105);
    Point point6 = point1;
    btnBrowse1.Location = point6;
    this.btnBrowse.Name = "btnBrowse";
    Button btnBrowse2 = this.btnBrowse;
    size1 = new Size(76, 23);
    Size size6 = size1;
    btnBrowse2.Size = size6;
    this.btnBrowse.TabIndex = 4;
    this.btnBrowse.Text = "Browse";
    this.btnBrowse.UseVisualStyleBackColor = false;
    this.lblFile.AutoSize = true;
    this.lblFile.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.lblFile.ForeColor = SystemColors.ControlText;
    Label lblFile1 = this.lblFile;
    point1 = new Point(69, 112 /*0x70*/);
    Point point7 = point1;
    lblFile1.Location = point7;
    this.lblFile.Name = "lblFile";
    Label lblFile2 = this.lblFile;
    size1 = new Size(34, 16 /*0x10*/);
    Size size7 = size1;
    lblFile2.Size = size7;
    this.lblFile.TabIndex = 29;
    this.lblFile.Text = "File";
    this.txtFilePath.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.txtFilePath.ForeColor = SystemColors.ControlText;
    TextBox txtFilePath1 = this.txtFilePath;
    point1 = new Point(131, 105);
    Point point8 = point1;
    txtFilePath1.Location = point8;
    this.txtFilePath.Name = "txtFilePath";
    TextBox txtFilePath2 = this.txtFilePath;
    size1 = new Size(184, 20);
    Size size8 = size1;
    txtFilePath2.Size = size8;
    this.txtFilePath.TabIndex = 3;
    this.btnSave.BackColor = Color.SeaShell;
    this.btnSave.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.btnSave.ForeColor = SystemColors.ControlText;
    Button btnSave1 = this.btnSave;
    point1 = new Point(134, 164);
    Point point9 = point1;
    btnSave1.Location = point9;
    this.btnSave.Name = "btnSave";
    Button btnSave2 = this.btnSave;
    size1 = new Size(60, 25);
    Size size9 = size1;
    btnSave2.Size = size9;
    this.btnSave.TabIndex = 5;
    this.btnSave.Text = "Save\r\n\r\n";
    this.btnSave.UseVisualStyleBackColor = false;
    this.cmbStationName.FormattingEnabled = true;
    ComboBox cmbStationName1 = this.cmbStationName;
    point1 = new Point(130, 18);
    Point point10 = point1;
    cmbStationName1.Location = point10;
    this.cmbStationName.Name = "cmbStationName";
    ComboBox cmbStationName2 = this.cmbStationName;
    size1 = new Size(121, 21);
    Size size10 = size1;
    cmbStationName2.Size = size10;
    this.cmbStationName.TabIndex = 1;
    this.AcceptButton = (IButtonControl) this.btnSave;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.BackColor = Color.PowderBlue;
    this.CancelButton = (IButtonControl) this.btnExit;
    size1 = new Size(444, 205);
    this.ClientSize = size1;
    this.Controls.Add((Control) this.cmbStationName);
    this.Controls.Add((Control) this.lblLang);
    this.Controls.Add((Control) this.cmbLang);
    this.Controls.Add((Control) this.btnExit);
    this.Controls.Add((Control) this.lblStationName);
    this.Controls.Add((Control) this.btnBrowse);
    this.Controls.Add((Control) this.lblFile);
    this.Controls.Add((Control) this.txtFilePath);
    this.Controls.Add((Control) this.btnSave);
    this.FormBorderStyle = FormBorderStyle.FixedToolWindow;
    this.Name = "frmStationNameVoice";
    this.Text = "StationNameVoice";
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  internal virtual Label lblLang
  {
    [DebuggerNonUserCode] get { return this._lblLang; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._lblLang = value; }
  }

  internal virtual ComboBox cmbLang
  {
    [DebuggerNonUserCode] get { return this._cmbLang; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbLang = value; }
  }

  internal virtual Button btnExit
  {
    [DebuggerNonUserCode] get { return this._btnExit; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnExit_Click);
      if (this._btnExit != null)
        this._btnExit.Click -= eventHandler;
      this._btnExit = value;
      if (this._btnExit == null)
        return;
      this._btnExit.Click += eventHandler;
    }
  }

  internal virtual Label lblStationName
  {
    [DebuggerNonUserCode] get { return this._lblStationName; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblStationName = value;
    }
  }

  internal virtual Button btnBrowse
  {
    [DebuggerNonUserCode] get { return this._btnBrowse; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnBrowse_Click);
      if (this._btnBrowse != null)
        this._btnBrowse.Click -= eventHandler;
      this._btnBrowse = value;
      if (this._btnBrowse == null)
        return;
      this._btnBrowse.Click += eventHandler;
    }
  }

  internal virtual Label lblFile
  {
    [DebuggerNonUserCode] get { return this._lblFile; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._lblFile = value; }
  }

  internal virtual TextBox txtFilePath
  {
    [DebuggerNonUserCode] get { return this._txtFilePath; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtFilePath = value;
    }
  }

  internal virtual Button btnSave
  {
    [DebuggerNonUserCode] get { return this._btnSave; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnSave_Click);
      if (this._btnSave != null)
        this._btnSave.Click -= eventHandler;
      this._btnSave = value;
      if (this._btnSave == null)
        return;
      this._btnSave.Click += eventHandler;
    }
  }

  internal virtual ComboBox cmbStationName
  {
    [DebuggerNonUserCode] get { return this._cmbStationName; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._cmbStationName = value;
    }
  }

  private void btnSave_Click(object sender, EventArgs e)
  {
    string str1 = "C:\\IPIS\\voice\\StationNames\\StationNameVoiceXml.xml";
    string str2 = "C:\\IPIS\\voice\\regional\\StationNames\\StationNameVoiceXml.xml";
    string str3 = "C:\\IPIS\\voice\\hindi\\StationNames\\StationNameVoiceXml.xml";
    string str4 = "C:\\IPIS\\voice\\regional\\oriya\\StationNames\\StationNameVoiceXml.xml";
    string str5 = "C:\\IPIS\\voice\\regional\\Marathi\\StationNames\\StationNameVoiceXml.xml";
    string str6 = "C:\\IPIS\\voice\\regional\\Bengali\\StationNames\\StationNameVoiceXml.xml";
    string str7 = "C:\\IPIS\\voice\\regional\\Chattisgarh\\StationNames\\StationNameVoiceXml.xml";
    string text1 = this.cmbStationName.Text;
    string text2 = this.txtFilePath.Text;
    if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(this.cmbLang.Text, "English", false) == 0)
    {
      if (File.Exists(str1))
        this.update_playlist_stationname(str1, text1, text2);
      else
        this.create_playlist_stationname(str1, text1, text2);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(this.cmbLang.Text, "Hindi", false) == 0)
    {
      if (File.Exists(str3))
        this.update_playlist_stationname(str3, text1, text2);
      else
        this.create_playlist_stationname(str3, text1, text2);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(this.cmbLang.Text, "Regional", false) == 0)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Strings.Trim(frmMainFormIPIS.language_selection.regional_language_name), "Telugu", false) == 0)
      {
        if (File.Exists(str2))
          this.update_playlist_stationname(str2, text1, text2);
        else
          this.create_playlist_stationname(str2, text1, text2);
      }
      else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Strings.Trim(frmMainFormIPIS.language_selection.regional_language_name), "Oriya", false) == 0)
      {
        if (File.Exists(str4))
          this.update_playlist_stationname(str4, text1, text2);
        else
          this.create_playlist_stationname(str4, text1, text2);
      }
      else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Strings.Trim(frmMainFormIPIS.language_selection.regional_language_name), "Marathi", false) == 0)
      {
        if (File.Exists(str5))
          this.update_playlist_stationname(str5, text1, text2);
        else
          this.create_playlist_stationname(str5, text1, text2);
      }
      else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Strings.Trim(frmMainFormIPIS.language_selection.regional_language_name), "Bengali", false) == 0)
      {
        if (File.Exists(str6))
          this.update_playlist_stationname(str6, text1, text2);
        else
          this.create_playlist_stationname(str6, text1, text2);
      }
      else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Strings.Trim(frmMainFormIPIS.language_selection.regional_language_name), "Chattisgarh", false) == 0)
      {
        if (File.Exists(str7))
          this.update_playlist_stationname(str7, text1, text2);
        else
          this.create_playlist_stationname(str7, text1, text2);
      }
    }
    this.txtFilePath.Text = string.Empty;
    this.cmbLang.Text = string.Empty;
    this.cmbStationName.Text = string.Empty;
  }

  private void create_playlist_stationname(
    string FileName,
    string station_name,
    string station_name_path)
  {
    FileStream fileStream = new FileStream(FileName, FileMode.Create, FileAccess.ReadWrite, FileShare.ReadWrite);
    this.sw = new StreamWriter((Stream) fileStream);
    try
    {
      this.sw.WriteLine("<?xml version=\"1.0\" encoding=\"utf-8\" ?>");
      this.sw.WriteLine("<sounds>");
      this.station_name_data(station_name, station_name_path);
      this.sw.WriteLine("</sounds>");
      FileName += " Successfully created.";
      int num = (int) MessageBox.Show(FileName, "Create Playlist");
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      int num = (int) MessageBox.Show(ex.Message, "Create Playlist: Error");
      ProjectData.ClearProjectError();
    }
    finally
    {
      this.sw.Close();
      fileStream.Close();
    }
  }

  private void update_playlist_stationname(
    string file_name,
    string station_name,
    string station_name_path)
  {
    StreamReader streamReader = new StreamReader(file_name);
    string end = streamReader.ReadToEnd();
    bool found = false;
    try
    {
      streamReader.Close();
      this.station_name_voice(station_name, station_name_path, ref found);
      if (found)
        return;
      string contents = end.Replace("</sounds>", " ");
      File.WriteAllText(file_name, contents);
      this.sw = File.AppendText(file_name);
      this.station_name_data(station_name, station_name_path);
      this.sw.WriteLine("</sounds>");
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Station name voice  added", "Msg Box", 0, 0, 0);
      this.sw.Close();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void station_name_data(string name, string path)
  {
    this.sw.WriteLine("\t<Stationname>");
    this.sw.WriteLine("\t\t<label>{name}</label>");
    this.sw.WriteLine("\t\t<data>{path}</data>");
    this.sw.WriteLine("\t</Stationname>");
  }

  private void station_name_voice(string station_name, string station_name_path, ref bool found)
  {
    int index = 0;
    int num1 = 0;
    found = false;
    string str = string.Empty;
    DataSet dataSet = new DataSet();
    if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(this.cmbLang.Text, "English", false) == 0)
    {
      str = "C:\\IPIS\\voice\\StationNames\\StationNameVoiceXml.xml";
      int num2 = (int) dataSet.ReadXml(str);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(this.cmbLang.Text, "Hindi", false) == 0)
    {
      str = "C:\\IPIS\\voice\\hindi\\StationNames\\StationNameVoiceXml.xml";
      int num3 = (int) dataSet.ReadXml(str);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Strings.Trim(frmMainFormIPIS.language_selection.regional_language_name), "Telugu", false) == 0)
    {
      str = "C:\\IPIS\\voice\\regional\\StationNames\\StationNameVoiceXml.xml";
      int num4 = (int) dataSet.ReadXml(str);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Strings.Trim(frmMainFormIPIS.language_selection.regional_language_name), "Oriya", false) == 0)
      str = "C:\\IPIS\\voice\\regional\\oriya\\StationNames\\StationNameVoiceXml.xml";
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Strings.Trim(frmMainFormIPIS.language_selection.regional_language_name), "Marathi", false) == 0)
      str = "C:\\IPIS\\voice\\regional\\Marathi\\StationNames\\StationNameVoiceXml.xml";
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Strings.Trim(frmMainFormIPIS.language_selection.regional_language_name), "Bengali", false) == 0)
      str = "C:\\IPIS\\voice\\regional\\Bengali\\StationNames\\StationNameVoiceXml.xml";
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Strings.Trim(frmMainFormIPIS.language_selection.regional_language_name), "Chattisgarh", false) == 0)
      str = "C:\\IPIS\\voice\\regional\\Chattisgarh\\StationNames\\StationNameVoiceXml.xml";
    string xml = dataSet.GetXml();
    XmlDocument xmlDocument = new XmlDocument();
    xmlDocument.LoadXml(xml);
    XmlNodeList elementsByTagName = xmlDocument.GetElementsByTagName("Stationname");
    num1 = elementsByTagName.Count;
    station_name = Strings.Trim(station_name);
    while (index < elementsByTagName.Count)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(station_name, elementsByTagName.Item(index).FirstChild.InnerText, false) == 0 && Interaction.MsgBox((object) "StationName voice is already there \r\nOverwrite(y/n)", MsgBoxStyle.YesNo) == MsgBoxResult.Yes)
      {
        elementsByTagName.Item(index).LastChild.InnerText = station_name_path;
        xmlDocument.Save(str);
        int num5 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Station name voice  updated", "Msg Box", 0, 0, 0);
        found = true;
        break;
      }
      checked { ++index; }
    }
  }

  private void btnBrowse_Click(object sender, EventArgs e)
  {
    OpenFileDialog openFileDialog = new OpenFileDialog();
    network_db_read.get_language_details();
    try
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(this.cmbLang.Text, "English", false) == 0)
        openFileDialog.InitialDirectory = "C:\\IPIS\\voice\\StationNames";
      else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(this.cmbLang.Text, "Hindi", false) == 0)
        openFileDialog.InitialDirectory = "C:\\IPIS\\voice\\hindi\\StationNames";
      else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(this.cmbLang.Text, "Regional", false) == 0)
      {
        if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Strings.Trim(frmMainFormIPIS.language_selection.regional_language_name), "Telugu", false) == 0)
          openFileDialog.InitialDirectory = "C:\\IPIS\\voice\\regional\\StationNames";
        else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Strings.Trim(frmMainFormIPIS.language_selection.regional_language_name), "Oriya", false) == 0)
          openFileDialog.InitialDirectory = "C:\\IPIS\\voice\\regional\\oriya\\StationNames";
        else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Strings.Trim(frmMainFormIPIS.language_selection.regional_language_name), "Marathi", false) == 0)
          openFileDialog.InitialDirectory = "C:\\IPIS\\voice\\regional\\Marathi\\StationNames";
        else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Strings.Trim(frmMainFormIPIS.language_selection.regional_language_name), "Bengali", false) == 0)
          openFileDialog.InitialDirectory = "C:\\IPIS\\voice\\regional\\Bengali\\StationNames";
        else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Strings.Trim(frmMainFormIPIS.language_selection.regional_language_name), "Chattisgarh", false) == 0)
          openFileDialog.InitialDirectory = "C:\\IPIS\\voice\\regional\\Chattisgarh\\StationNames";
      }
      openFileDialog.Filter = "txt files (*.txt)|*.txt|All files (*.*)|*.*";
      openFileDialog.FilterIndex = 2;
      openFileDialog.RestoreDirectory = true;
      if (openFileDialog.ShowDialog() != DialogResult.OK)
        return;
      string fileName = openFileDialog.FileName;
      openFileDialog.OpenFile();
      this.txtFilePath.Text = fileName;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void frmStationNameVoice_FormClosing(object sender, FormClosingEventArgs e)
  {
    MyProject.Forms.frmVoice.BringToFront();
  }

  private void frmStationNameVoice_Load(object sender, EventArgs e)
  {
    object obj = (object) 0;
    string[] strArray1 = new string[601];
    string[] strArray2 = new string[601];
    string str = Conversions.ToString(obj);
    network_db_read.get_Station_codes(ref strArray2, ref strArray1, ref str);
    object Right = (object) str;
    int Left = 0;
    while (Microsoft.VisualBasic.CompilerServices.Operators.ConditionalCompareObjectLess((object) Left, Right, false))
    {
      this.cmbStationName.Items.Add((object) strArray1[Left]);
      checked { ++Left; }
    }
  }

  private void btnExit_Click(object sender, EventArgs e)
  {
    this.Close();
    MyProject.Forms.frmVoice.BringToFront();
  }
}

}