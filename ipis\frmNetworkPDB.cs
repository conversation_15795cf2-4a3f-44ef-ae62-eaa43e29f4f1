// Decompiled with JetBrains decompiler
// Type: ipis.frmNetworkPDB
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

namespace ipis
{

[DesignerGenerated]
public class frmNetworkPDB : Form
{
  private static List<WeakReference> __ENCList = new List<WeakReference>();
  private IContainer components;
  [AccessedThroughProperty("lblSharedPfno")]
  private Label _lblSharedPfno;
  [AccessedThroughProperty("txtPdbMsgSwDly")]
  private TextBox _txtPdbMsgSwDly;
  [AccessedThroughProperty("lblMsgSwDly")]
  private Label _lblMsgSwDly;
  [AccessedThroughProperty("lblPfno")]
  private Label _lblPfno;
  [AccessedThroughProperty("btnExit")]
  private Button _btnExit;
  [AccessedThroughProperty("btnOk")]
  private Button _btnOk;
  [AccessedThroughProperty("txtPdbAddress")]
  private TextBox _txtPdbAddress;
  [AccessedThroughProperty("txtPdbName")]
  private TextBox _txtPdbName;
  [AccessedThroughProperty("lblAddress")]
  private Label _lblAddress;
  [AccessedThroughProperty("lblName")]
  private Label _lblName;
  [AccessedThroughProperty("txtMultiCastAddress")]
  private TextBox _txtMultiCastAddress;
  [AccessedThroughProperty("lblMultiCastAddress")]
  private Label _lblMultiCastAddress;
  [AccessedThroughProperty("cmbPdbPdno")]
  private ComboBox _cmbPdbPdno;
  [AccessedThroughProperty("cmbPdbSharedPlatformNo")]
  private ComboBox _cmbPdbSharedPlatformNo;
  [AccessedThroughProperty("chkPDBSharedPfNo")]
  private CheckBox _chkPDBSharedPfNo;
  [AccessedThroughProperty("lblSharedPlatform")]
  private Label _lblSharedPlatform;
  public static string pdb_name;
  public static byte pdb_addr;
  public static string pdb_platform_no;
  public static bool pdb_shared_platform;
  public static string pdb_shared_platform_no;
  public static byte pdb_msg_sw_dly;

  [DebuggerNonUserCode]
  static frmNetworkPDB()
  {
  }

  [DebuggerNonUserCode]
  public frmNetworkPDB()
  {
    this.Load += new EventHandler(this.frmNetworkPDB_Load);
    frmNetworkPDB.__ENCAddToList((object) this);
    this.InitializeComponent();
  }

  [DebuggerNonUserCode]
  private static void __ENCAddToList(object value)
  {
    lock (frmNetworkPDB.__ENCList)
    {
      if (frmNetworkPDB.__ENCList.Count == frmNetworkPDB.__ENCList.Capacity)
      {
        int index1 = 0;
        int num = checked (frmNetworkPDB.__ENCList.Count - 1);
        int index2 = 0;
        while (index2 <= num)
        {
          if (frmNetworkPDB.__ENCList[index2].IsAlive)
          {
            if (index2 != index1)
              frmNetworkPDB.__ENCList[index1] = frmNetworkPDB.__ENCList[index2];
            checked { ++index1; }
          }
          checked { ++index2; }
        }
        frmNetworkPDB.__ENCList.RemoveRange(index1, checked (frmNetworkPDB.__ENCList.Count - index1));
        frmNetworkPDB.__ENCList.Capacity = frmNetworkPDB.__ENCList.Count;
      }
      frmNetworkPDB.__ENCList.Add(new WeakReference(RuntimeHelpers.GetObjectValue(value)));
    }
  }

  [DebuggerNonUserCode]
  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  [DebuggerStepThrough]
  private void InitializeComponent()
  {
    this.lblSharedPfno = new Label();
    this.txtPdbMsgSwDly = new TextBox();
    this.lblMsgSwDly = new Label();
    this.lblPfno = new Label();
    this.btnExit = new Button();
    this.btnOk = new Button();
    this.txtPdbAddress = new TextBox();
    this.txtPdbName = new TextBox();
    this.lblAddress = new Label();
    this.lblName = new Label();
    this.txtMultiCastAddress = new TextBox();
    this.lblMultiCastAddress = new Label();
    this.cmbPdbPdno = new ComboBox();
    this.cmbPdbSharedPlatformNo = new ComboBox();
    this.chkPDBSharedPfNo = new CheckBox();
    this.lblSharedPlatform = new Label();
    this.SuspendLayout();
    this.lblSharedPfno.AutoSize = true;
    this.lblSharedPfno.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblSharedPfno1 = this.lblSharedPfno;
    Point point1 = new Point(59, 270);
    Point point2 = point1;
    lblSharedPfno1.Location = point2;
    this.lblSharedPfno.Name = "lblSharedPfno";
    Label lblSharedPfno2 = this.lblSharedPfno;
    Size size1 = new Size(143, 32 /*0x20*/);
    Size size2 = size1;
    lblSharedPfno2.Size = size2;
    this.lblSharedPfno.TabIndex = 70;
    this.lblSharedPfno.Text = "Shared Platform No\r\n\r\n";
    this.lblSharedPfno.Visible = false;
    this.txtPdbMsgSwDly.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtPdbMsgSwDly1 = this.txtPdbMsgSwDly;
    point1 = new Point((int) byte.MaxValue, 319);
    Point point3 = point1;
    txtPdbMsgSwDly1.Location = point3;
    this.txtPdbMsgSwDly.MaxLength = 2;
    this.txtPdbMsgSwDly.Name = "txtPdbMsgSwDly";
    TextBox txtPdbMsgSwDly2 = this.txtPdbMsgSwDly;
    size1 = new Size(45, 22);
    Size size3 = size1;
    txtPdbMsgSwDly2.Size = size3;
    this.txtPdbMsgSwDly.TabIndex = 7;
    this.txtPdbMsgSwDly.TextAlign = HorizontalAlignment.Center;
    this.lblMsgSwDly.AutoSize = true;
    this.lblMsgSwDly.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblMsgSwDly1 = this.lblMsgSwDly;
    point1 = new Point(16 /*0x10*/, 322);
    Point point4 = point1;
    lblMsgSwDly1.Location = point4;
    this.lblMsgSwDly.Name = "lblMsgSwDly";
    Label lblMsgSwDly2 = this.lblMsgSwDly;
    size1 = new Size(186, 16 /*0x10*/);
    Size size4 = size1;
    lblMsgSwDly2.Size = size4;
    this.lblMsgSwDly.TabIndex = 69;
    this.lblMsgSwDly.Text = "Message Switching Delay";
    this.lblPfno.AutoSize = true;
    this.lblPfno.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblPfno1 = this.lblPfno;
    point1 = new Point(113, 165);
    Point point5 = point1;
    lblPfno1.Location = point5;
    this.lblPfno.Name = "lblPfno";
    Label lblPfno2 = this.lblPfno;
    size1 = new Size(89, 16 /*0x10*/);
    Size size5 = size1;
    lblPfno2.Size = size5;
    this.lblPfno.TabIndex = 68;
    this.lblPfno.Text = "Platform No";
    this.btnExit.BackColor = Color.SeaShell;
    this.btnExit.DialogResult = DialogResult.Cancel;
    this.btnExit.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnExit1 = this.btnExit;
    point1 = new Point(215, 376);
    Point point6 = point1;
    btnExit1.Location = point6;
    this.btnExit.Name = "btnExit";
    Button btnExit2 = this.btnExit;
    size1 = new Size(50, 25);
    Size size6 = size1;
    btnExit2.Size = size6;
    this.btnExit.TabIndex = 9;
    this.btnExit.Text = "Exit";
    this.btnExit.UseVisualStyleBackColor = false;
    this.btnOk.BackColor = Color.SeaShell;
    this.btnOk.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnOk1 = this.btnOk;
    point1 = new Point(107, 376);
    Point point7 = point1;
    btnOk1.Location = point7;
    this.btnOk.Name = "btnOk";
    Button btnOk2 = this.btnOk;
    size1 = new Size(50, 25);
    Size size7 = size1;
    btnOk2.Size = size7;
    this.btnOk.TabIndex = 8;
    this.btnOk.Text = "Ok";
    this.btnOk.UseVisualStyleBackColor = false;
    this.txtPdbAddress.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtPdbAddress1 = this.txtPdbAddress;
    point1 = new Point((int) byte.MaxValue, 62);
    Point point8 = point1;
    txtPdbAddress1.Location = point8;
    this.txtPdbAddress.MaxLength = 3;
    this.txtPdbAddress.Name = "txtPdbAddress";
    TextBox txtPdbAddress2 = this.txtPdbAddress;
    size1 = new Size(45, 22);
    Size size8 = size1;
    txtPdbAddress2.Size = size8;
    this.txtPdbAddress.TabIndex = 2;
    this.txtPdbName.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtPdbName1 = this.txtPdbName;
    point1 = new Point((int) byte.MaxValue, 18);
    Point point9 = point1;
    txtPdbName1.Location = point9;
    this.txtPdbName.MaxLength = 15;
    this.txtPdbName.Name = "txtPdbName";
    this.txtPdbName.RightToLeft = RightToLeft.No;
    TextBox txtPdbName2 = this.txtPdbName;
    size1 = new Size(100, 22);
    Size size9 = size1;
    txtPdbName2.Size = size9;
    this.txtPdbName.TabIndex = 1;
    this.lblAddress.AutoSize = true;
    this.lblAddress.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblAddress1 = this.lblAddress;
    point1 = new Point(136, 68);
    Point point10 = point1;
    lblAddress1.Location = point10;
    this.lblAddress.Name = "lblAddress";
    Label lblAddress2 = this.lblAddress;
    size1 = new Size(66, 16 /*0x10*/);
    Size size10 = size1;
    lblAddress2.Size = size10;
    this.lblAddress.TabIndex = 67;
    this.lblAddress.Text = "Address";
    this.lblName.AutoSize = true;
    this.lblName.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblName1 = this.lblName;
    point1 = new Point(153, 18);
    Point point11 = point1;
    lblName1.Location = point11;
    this.lblName.Name = "lblName";
    Label lblName2 = this.lblName;
    size1 = new Size(49, 16 /*0x10*/);
    Size size11 = size1;
    lblName2.Size = size11;
    this.lblName.TabIndex = 66;
    this.lblName.Text = "Name";
    this.txtMultiCastAddress.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox multiCastAddress1 = this.txtMultiCastAddress;
    point1 = new Point((int) byte.MaxValue, 118);
    Point point12 = point1;
    multiCastAddress1.Location = point12;
    this.txtMultiCastAddress.MaxLength = 3;
    this.txtMultiCastAddress.Name = "txtMultiCastAddress";
    TextBox multiCastAddress2 = this.txtMultiCastAddress;
    size1 = new Size(45, 22);
    Size size12 = size1;
    multiCastAddress2.Size = size12;
    this.txtMultiCastAddress.TabIndex = 3;
    this.lblMultiCastAddress.AutoSize = true;
    this.lblMultiCastAddress.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label multiCastAddress3 = this.lblMultiCastAddress;
    point1 = new Point(65, 118);
    Point point13 = point1;
    multiCastAddress3.Location = point13;
    this.lblMultiCastAddress.Name = "lblMultiCastAddress";
    Label multiCastAddress4 = this.lblMultiCastAddress;
    size1 = new Size(137, 16 /*0x10*/);
    Size size13 = size1;
    multiCastAddress4.Size = size13;
    this.lblMultiCastAddress.TabIndex = 72;
    this.lblMultiCastAddress.Text = "Multi Cast Address";
    this.cmbPdbPdno.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.cmbPdbPdno.FormattingEnabled = true;
    ComboBox cmbPdbPdno1 = this.cmbPdbPdno;
    point1 = new Point((int) byte.MaxValue, 165);
    Point point14 = point1;
    cmbPdbPdno1.Location = point14;
    this.cmbPdbPdno.Name = "cmbPdbPdno";
    ComboBox cmbPdbPdno2 = this.cmbPdbPdno;
    size1 = new Size(72, 24);
    Size size14 = size1;
    cmbPdbPdno2.Size = size14;
    this.cmbPdbPdno.TabIndex = 4;
    this.cmbPdbSharedPlatformNo.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.cmbPdbSharedPlatformNo.FormattingEnabled = true;
    ComboBox sharedPlatformNo1 = this.cmbPdbSharedPlatformNo;
    point1 = new Point((int) byte.MaxValue, 267);
    Point point15 = point1;
    sharedPlatformNo1.Location = point15;
    this.cmbPdbSharedPlatformNo.Name = "cmbPdbSharedPlatformNo";
    ComboBox sharedPlatformNo2 = this.cmbPdbSharedPlatformNo;
    size1 = new Size(72, 24);
    Size size15 = size1;
    sharedPlatformNo2.Size = size15;
    this.cmbPdbSharedPlatformNo.TabIndex = 6;
    this.cmbPdbSharedPlatformNo.Visible = false;
    this.chkPDBSharedPfNo.AutoSize = true;
    this.chkPDBSharedPfNo.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    CheckBox chkPdbSharedPfNo1 = this.chkPDBSharedPfNo;
    point1 = new Point((int) byte.MaxValue, 217);
    Point point16 = point1;
    chkPdbSharedPfNo1.Location = point16;
    this.chkPDBSharedPfNo.Name = "chkPDBSharedPfNo";
    CheckBox chkPdbSharedPfNo2 = this.chkPDBSharedPfNo;
    size1 = new Size(15, 14);
    Size size16 = size1;
    chkPdbSharedPfNo2.Size = size16;
    this.chkPDBSharedPfNo.TabIndex = 5;
    this.chkPDBSharedPfNo.UseVisualStyleBackColor = true;
    this.lblSharedPlatform.AutoSize = true;
    this.lblSharedPlatform.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblSharedPlatform1 = this.lblSharedPlatform;
    point1 = new Point(83, 215);
    Point point17 = point1;
    lblSharedPlatform1.Location = point17;
    this.lblSharedPlatform.Name = "lblSharedPlatform";
    Label lblSharedPlatform2 = this.lblSharedPlatform;
    size1 = new Size(119, 16 /*0x10*/);
    Size size17 = size1;
    lblSharedPlatform2.Size = size17;
    this.lblSharedPlatform.TabIndex = 170;
    this.lblSharedPlatform.Text = "Shared Platform\r\n";
    this.AcceptButton = (IButtonControl) this.btnOk;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.BackColor = Color.PowderBlue;
    this.CancelButton = (IButtonControl) this.btnExit;
    size1 = new Size(385, 427);
    this.ClientSize = size1;
    this.Controls.Add((Control) this.chkPDBSharedPfNo);
    this.Controls.Add((Control) this.lblSharedPlatform);
    this.Controls.Add((Control) this.cmbPdbSharedPlatformNo);
    this.Controls.Add((Control) this.cmbPdbPdno);
    this.Controls.Add((Control) this.txtMultiCastAddress);
    this.Controls.Add((Control) this.lblMultiCastAddress);
    this.Controls.Add((Control) this.lblSharedPfno);
    this.Controls.Add((Control) this.txtPdbMsgSwDly);
    this.Controls.Add((Control) this.lblMsgSwDly);
    this.Controls.Add((Control) this.lblPfno);
    this.Controls.Add((Control) this.btnExit);
    this.Controls.Add((Control) this.btnOk);
    this.Controls.Add((Control) this.txtPdbAddress);
    this.Controls.Add((Control) this.txtPdbName);
    this.Controls.Add((Control) this.lblAddress);
    this.Controls.Add((Control) this.lblName);
    this.FormBorderStyle = FormBorderStyle.FixedToolWindow;
    this.Name = "frmNetworkPDB";
    this.Text = "PDB";
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  internal virtual Label lblSharedPfno
  {
    [DebuggerNonUserCode] get { return this._lblSharedPfno; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblSharedPfno = value;
    }
  }

  internal virtual TextBox txtPdbMsgSwDly
  {
    [DebuggerNonUserCode] get { return this._txtPdbMsgSwDly; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtPdbMsgSwDly = value;
    }
  }

  internal virtual Label lblMsgSwDly
  {
    [DebuggerNonUserCode] get { return this._lblMsgSwDly; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblMsgSwDly = value;
    }
  }

  internal virtual Label lblPfno
  {
    [DebuggerNonUserCode] get { return this._lblPfno; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._lblPfno = value; }
  }

  internal virtual Button btnExit
  {
    [DebuggerNonUserCode] get { return this._btnExit; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnExit_Click);
      if (this._btnExit != null)
        this._btnExit.Click -= eventHandler;
      this._btnExit = value;
      if (this._btnExit == null)
        return;
      this._btnExit.Click += eventHandler;
    }
  }

  internal virtual Button btnOk
  {
    [DebuggerNonUserCode] get { return this._btnOk; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnOk_Click);
      if (this._btnOk != null)
        this._btnOk.Click -= eventHandler;
      this._btnOk = value;
      if (this._btnOk == null)
        return;
      this._btnOk.Click += eventHandler;
    }
  }

  internal virtual TextBox txtPdbAddress
  {
    [DebuggerNonUserCode] get { return this._txtPdbAddress; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtPdbAddress = value;
    }
  }

  internal virtual TextBox txtPdbName
  {
    [DebuggerNonUserCode] get { return this._txtPdbName; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtPdbName = value;
    }
  }

  internal virtual Label lblAddress
  {
    [DebuggerNonUserCode] get { return this._lblAddress; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblAddress = value;
    }
  }

  internal virtual Label lblName
  {
    [DebuggerNonUserCode] get { return this._lblName; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._lblName = value; }
  }

  internal virtual TextBox txtMultiCastAddress
  {
    [DebuggerNonUserCode] get { return this._txtMultiCastAddress; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtMultiCastAddress = value;
    }
  }

  internal virtual Label lblMultiCastAddress
  {
    [DebuggerNonUserCode] get { return this._lblMultiCastAddress; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblMultiCastAddress = value;
    }
  }

  internal virtual ComboBox cmbPdbPdno
  {
    [DebuggerNonUserCode] get { return this._cmbPdbPdno; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._cmbPdbPdno = value;
    }
  }

  internal virtual ComboBox cmbPdbSharedPlatformNo
  {
    [DebuggerNonUserCode] get { return this._cmbPdbSharedPlatformNo; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.cmbPdbSharedPlatformNo_DropDown);
      if (this._cmbPdbSharedPlatformNo != null)
        this._cmbPdbSharedPlatformNo.DropDown -= eventHandler;
      this._cmbPdbSharedPlatformNo = value;
      if (this._cmbPdbSharedPlatformNo == null)
        return;
      this._cmbPdbSharedPlatformNo.DropDown += eventHandler;
    }
  }

  internal virtual CheckBox chkPDBSharedPfNo
  {
    [DebuggerNonUserCode] get { return this._chkPDBSharedPfNo; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.chkPDBSharedPfNo_CheckedChanged);
      if (this._chkPDBSharedPfNo != null)
        this._chkPDBSharedPfNo.CheckedChanged -= eventHandler;
      this._chkPDBSharedPfNo = value;
      if (this._chkPDBSharedPfNo == null)
        return;
      this._chkPDBSharedPfNo.CheckedChanged += eventHandler;
    }
  }

  internal virtual Label lblSharedPlatform
  {
    [DebuggerNonUserCode] get { return this._lblSharedPlatform; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblSharedPlatform = value;
    }
  }

  private void btnExit_Click(object sender, EventArgs e)
{
  this.Close();
}

  private void btnOk_Click(object sender, EventArgs e)
  {
    try
    {
      if (Operators.CompareString(this.txtPdbName.Text, "", false) == 0)
      {
        int num1 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Please Enter the Name", "Msg Box", 0, 0, 0);
      }
      else if (Operators.CompareString(this.txtPdbAddress.Text, "", false) == 0)
      {
        int num2 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Please Enter the Address", "Msg Box", 0, 0, 0);
      }
      else if (Operators.CompareString(this.txtMultiCastAddress.Text, "", false) == 0)
      {
        int num3 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Please Enter the MultiCast Address", "Msg Box", 0, 0, 0);
      }
      else if (Operators.CompareString(this.cmbPdbPdno.Text, "", false) == 0)
      {
        int num4 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Please Enter the Platform No", "Msg Box", 0, 0, 0);
      }
      else if (Operators.CompareString(this.txtPdbMsgSwDly.Text, "", false) == 0)
      {
        int num5 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Please Enter the Message Switching delay", "Msg Box", 0, 0, 0);
      }
      else
      {
        if (frmNetworkMDCH.hub_type)
        {
          frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].system_type[(int) frmNetworkMDCH.mdch_system_num] = "PDB";
          frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].dis_board[(int) frmNetworkMDCH.mdch_system_num].dis_board_name = this.txtPdbName.Text;
          frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].dis_board[(int) frmNetworkMDCH.mdch_system_num].dis_board_addr = Conversions.ToByte(this.txtPdbAddress.Text);
          frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].dis_board[(int) frmNetworkMDCH.mdch_system_num].multicast_addr = Conversions.ToByte(this.txtMultiCastAddress.Text);
          frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].dis_board[(int) frmNetworkMDCH.mdch_system_num].dis_board_type = "PDB";
          frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].dis_board[(int) frmNetworkMDCH.mdch_system_num].platform_no = Operators.CompareString(this.cmbPdbPdno.Text, "", false) != 0 ? this.cmbPdbPdno.Text : string.Empty;
          if (!this.chkPDBSharedPfNo.Checked)
          {
            frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].dis_board[(int) frmNetworkMDCH.mdch_system_num].shared_platform_no = string.Empty;
            frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].dis_board[(int) frmNetworkMDCH.mdch_system_num].shared_platform = false;
          }
          else
          {
            frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].dis_board[(int) frmNetworkMDCH.mdch_system_num].shared_platform = true;
            frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].dis_board[(int) frmNetworkMDCH.mdch_system_num].shared_platform_no = this.cmbPdbSharedPlatformNo.Text;
          }
          frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].dis_board[(int) frmNetworkMDCH.mdch_system_num].switching_time = Conversions.ToByte(this.txtPdbMsgSwDly.Text);
        }
        else
        {
          checked { --frmNetworkPDCH.pdch_port_num; }
          checked { --frmNetworkPDCH.pdch_system_num; }
          frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].pdch_port[(int) frmNetworkPDCH.pdch_port_num].dis_board[(int) frmNetworkPDCH.pdch_system_num].dis_board_addr = Conversions.ToByte(this.txtPdbAddress.Text);
          frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].pdch_port[(int) frmNetworkPDCH.pdch_port_num].dis_board[(int) frmNetworkPDCH.pdch_system_num].dis_board_name = this.txtPdbName.Text;
          frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].pdch_port[(int) frmNetworkPDCH.pdch_port_num].dis_board[(int) frmNetworkPDCH.pdch_system_num].multicast_addr = Conversions.ToByte(this.txtMultiCastAddress.Text);
          frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].pdch_port[(int) frmNetworkPDCH.pdch_port_num].dis_board[(int) frmNetworkPDCH.pdch_system_num].dis_board_type = "PDB";
          frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].pdch_port[(int) frmNetworkPDCH.pdch_port_num].dis_board[(int) frmNetworkPDCH.pdch_system_num].platform_no = Operators.CompareString(this.cmbPdbPdno.Text, "", false) != 0 ? this.cmbPdbPdno.Text : string.Empty;
          if (!this.chkPDBSharedPfNo.Checked)
          {
            frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].pdch_port[(int) frmNetworkPDCH.pdch_port_num].dis_board[(int) frmNetworkPDCH.pdch_system_num].shared_platform = false;
            frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].pdch_port[(int) frmNetworkPDCH.pdch_port_num].dis_board[(int) frmNetworkPDCH.pdch_system_num].shared_platform_no = string.Empty;
          }
          else
          {
            frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].pdch_port[(int) frmNetworkPDCH.pdch_port_num].dis_board[(int) frmNetworkPDCH.pdch_system_num].shared_platform = true;
            frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].pdch_port[(int) frmNetworkPDCH.pdch_port_num].dis_board[(int) frmNetworkPDCH.pdch_system_num].shared_platform_no = this.cmbPdbSharedPlatformNo.Text;
          }
          frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].pdch_port[(int) frmNetworkPDCH.pdch_port_num].dis_board[(int) frmNetworkPDCH.pdch_system_num].switching_time = Conversions.ToByte(this.txtPdbMsgSwDly.Text);
        }
        this.Close();
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void cmbPdbSharedPlatformNo_DropDown(object sender, EventArgs e)
  {
    int index = 0;
    this.cmbPdbSharedPlatformNo.Items.Clear();
    while (index < frmMainFormIPIS.pfno_cnt)
    {
      this.cmbPdbSharedPlatformNo.Items.Add((object) frmMainFormIPIS.platform_nos[index]);
      checked { ++index; }
    }
  }

  private void chkPDBSharedPfNo_CheckedChanged(object sender, EventArgs e)
  {
    if (this.chkPDBSharedPfNo.Checked)
    {
      this.cmbPdbSharedPlatformNo.Visible = true;
      this.lblSharedPfno.Visible = true;
    }
    else
    {
      this.cmbPdbSharedPlatformNo.Visible = false;
      this.lblSharedPfno.Visible = false;
    }
  }

  private void frmNetworkPDB_Load(object sender, EventArgs e)
  {
    int index = 0;
    this.cmbPdbPdno.Items.Clear();
    while (index < frmMainFormIPIS.pfno_cnt)
    {
      this.cmbPdbPdno.Items.Add((object) frmMainFormIPIS.platform_nos[index]);
      checked { ++index; }
    }
  }
}

}