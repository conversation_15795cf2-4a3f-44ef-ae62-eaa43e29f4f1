using System;

namespace IPIS.Models
{
    public class UserActivityLog
    {
        public int Id { get; set; }
        public DateTime Timestamp { get; set; }
        public int UserId { get; set; }
        public string Username { get; set; }
        public string Action { get; set; }
        public LogCategory Category { get; set; }
        public string EntityType { get; set; }
        public string EntityId { get; set; }
        public string OldValues { get; set; }
        public string NewValues { get; set; }
        public string IPAddress { get; set; }
        public string UserAgent { get; set; }
        public string SessionId { get; set; }
        public DateTime CreatedAt { get; set; }

        public UserActivityLog()
        {
            Timestamp = DateTime.Now;
            CreatedAt = DateTime.Now;
        }

        public UserActivityLog(int userId, string username, string action, LogCategory category, 
                              string entityType = null, string entityId = null, 
                              string oldValues = null, string newValues = null)
        {
            Timestamp = DateTime.Now;
            CreatedAt = DateTime.Now;
            UserId = userId;
            Username = username;
            Action = action;
            Category = category;
            EntityType = entityType;
            EntityId = entityId;
            OldValues = oldValues;
            NewValues = newValues;
        }

        public string GetCategoryDisplayName()
        {
            return Category switch
            {
                LogCategory.TrainManagement => "Train Management",
                LogCategory.StationManagement => "Station Management",
                LogCategory.UserManagement => "User Management",
                LogCategory.Announcement => "Announcement",
                LogCategory.Advertising => "Advertising",
                LogCategory.System => "System",
                LogCategory.Database => "Database",
                LogCategory.Audio => "Audio",
                LogCategory.Error => "Error",
                LogCategory.Security => "Security",
                _ => "Unknown"
            };
        }
    }
}
