﻿using System;
using System.Data;
using System.Data.Common;
using System.Data.OleDb;
using System.Globalization;
using System.Windows.Forms;

namespace Announcement
{
	// Token: 0x02000006 RID: 6
	internal class Class_Database
	{
		// Token: 0x0600002C RID: 44 RVA: 0x00007B3C File Offset: 0x00005D3C
		public bool Insert_Database(string Query)
		{
			bool result;
			try
			{
				this.Conn.Open();
				this.Cmd = new OleDbCommand();
				this.Cmd.Connection = this.Conn;
				this.Cmd.CommandText = Query;
				this.Cmd.ExecuteNonQuery();
				this.Conn.Close();
				result = true;
			}
			catch (Exception ex)
			{
				this.Conn.Close();
				result = false;
			}
			return result;
		}

		// Token: 0x0600002D RID: 45 RVA: 0x00007BC0 File Offset: 0x00005DC0
		public DataTable Read_Database(string Query)
		{
			try
			{
				this.DTable = new DataTable();
				this.Adpt = new OleDbDataAdapter(Query, this.Conn);
				this.Adpt.Fill(this.DTable);
			}
			catch (Exception ex)
			{
			}
			return this.DTable;
		}

		// Token: 0x0600002E RID: 46 RVA: 0x00007C20 File Offset: 0x00005E20
		public void Update_Database(string Query)
		{
			this.DTable = new DataTable();
			this.Cmd = new OleDbCommand();
			this.Cmd.Connection = this.Conn;
			this.Conn.Open();
			for (int i = 0; i < Main.Online_TrainsGV.Rows.Count; i++)
			{
				try
				{
					this.DTable = new DataTable();
					DbCommand cmd = this.Cmd;
					string str = "SELECT Train_NameEng FROM Online_Trains WHERE Train_No = '";
					object obj = Main.Online_TrainsGV.Rows[i]["Train_No"];
					cmd.CommandText = str + ((obj != null) ? obj.ToString() : null) + "'";
					this.Cmd.ExecuteNonQuery();
					this.Adpt = new OleDbDataAdapter(this.Cmd.CommandText, this.Conn);
					this.Adpt.Fill(this.DTable);
					bool flag = this.DTable.Rows.Count > 0;
					if (flag)
					{
						bool flag2 = Main.Online_TrainsGV.Rows[i]["AN"].ToString() != "True";
						if (flag2)
						{
							Main.Online_TrainsGV.Rows[i]["AN"] = "False";
						}
						DbCommand cmd2 = this.Cmd;
						string[] array = new string[23];
						array[0] = "UPDATE Online_Trains SET Train_AD = '";
						int num = 1;
						object obj2 = Main.Online_TrainsGV.Rows[i]["Train_AD"];
						array[num] = ((obj2 != null) ? obj2.ToString() : null);
						array[2] = "',Train_Status = '";
						int num2 = 3;
						object obj3 = Main.Online_TrainsGV.Rows[i]["Train_Status"];
						array[num2] = ((obj3 != null) ? obj3.ToString() : null);
						array[4] = "',Sch_AT='";
						int num3 = 5;
						object obj4 = Main.Online_TrainsGV.Rows[i]["Sch_AT"];
						array[num3] = ((obj4 != null) ? obj4.ToString() : null);
						array[6] = "',Sch_DT='";
						int num4 = 7;
						object obj5 = Main.Online_TrainsGV.Rows[i]["Sch_DT"];
						array[num4] = ((obj5 != null) ? obj5.ToString() : null);
						array[8] = "',Late='";
						int num5 = 9;
						object obj6 = Main.Online_TrainsGV.Rows[i]["Late"];
						array[num5] = ((obj6 != null) ? obj6.ToString() : null);
						array[10] = "',Exp_AT='";
						int num6 = 11;
						object obj7 = Main.Online_TrainsGV.Rows[i]["Exp_AT"];
						array[num6] = ((obj7 != null) ? obj7.ToString() : null);
						array[12] = "',Exp_DT = '";
						int num7 = 13;
						object obj8 = Main.Online_TrainsGV.Rows[i]["Exp_DT"];
						array[num7] = ((obj8 != null) ? obj8.ToString() : null);
						array[14] = "',Sch_PF='";
						int num8 = 15;
						object obj9 = Main.Online_TrainsGV.Rows[i]["Sch_PF"];
						array[num8] = ((obj9 != null) ? obj9.ToString() : null);
						array[16] = "',AN='";
						int num9 = 17;
						object obj10 = Main.Online_TrainsGV.Rows[i]["AN"];
						array[num9] = ((obj10 != null) ? obj10.ToString() : null);
						array[18] = "',Div_City='";
						int num10 = 19;
						object obj11 = Main.Online_TrainsGV.Rows[i]["Div_City"];
						array[num10] = ((obj11 != null) ? obj11.ToString() : null);
						array[20] = "'WHERE Train_No = '";
						int num11 = 21;
						object obj12 = Main.Online_TrainsGV.Rows[i]["Train_No"];
						array[num11] = ((obj12 != null) ? obj12.ToString() : null);
						array[22] = "'";
						cmd2.CommandText = string.Concat(array);
						this.Cmd.ExecuteNonQuery();
					}
					else
					{
						string text = DateTime.Now.ToString("ddMMyyyy");
						string a = Main.Online_TrainsGV.Rows[i]["Train_AD"].ToString();
						string s = Main.Online_TrainsGV.Rows[i]["Sch_AT"].ToString();
						string s2 = Main.Online_TrainsGV.Rows[i]["Sch_DT"].ToString();
						bool flag3 = DateTime.Now.Hour > 21;
						if (flag3)
						{
							try
							{
								bool flag4 = a == "A";
								if (flag4)
								{
									DateTime t = DateTime.ParseExact(s, "HH:mm", CultureInfo.InvariantCulture, DateTimeStyles.None);
									bool flag5 = t < DateTime.Now;
									if (flag5)
									{
										text = DateTime.Now.AddDays(1.0).ToString("ddMMyyyy");
									}
								}
								else
								{
									bool flag6 = a == "D";
									if (flag6)
									{
										DateTime t2 = DateTime.ParseExact(s2, "HH:mm", CultureInfo.InvariantCulture, DateTimeStyles.None);
										bool flag7 = t2 < DateTime.Now;
										if (flag7)
										{
											text = DateTime.Now.AddDays(1.0).ToString("ddMMyyyy");
										}
									}
								}
							}
							catch
							{
							}
						}
						DbCommand cmd3 = this.Cmd;
						string[] array2 = new string[25];
						array2[0] = "INSERT INTO Online_Trains (Train_No,Train_NameEng,Train_AD,Train_Status,Train_Date,Sch_AT,Sch_DT,Late,Exp_AT,Exp_DT,Sch_PF,AN,Div_City)VALUES('";
						int num12 = 1;
						object obj13 = Main.Online_TrainsGV.Rows[i]["Train_No"];
						array2[num12] = ((obj13 != null) ? obj13.ToString() : null);
						array2[2] = "','";
						int num13 = 3;
						object obj14 = Main.Online_TrainsGV.Rows[i]["Train_NameEng"];
						array2[num13] = ((obj14 != null) ? obj14.ToString() : null);
						array2[4] = "','";
						int num14 = 5;
						object obj15 = Main.Online_TrainsGV.Rows[i]["Train_AD"];
						array2[num14] = ((obj15 != null) ? obj15.ToString() : null);
						array2[6] = "','";
						int num15 = 7;
						object obj16 = Main.Online_TrainsGV.Rows[i]["Train_Status"];
						array2[num15] = ((obj16 != null) ? obj16.ToString() : null);
						array2[8] = "','";
						array2[9] = text;
						array2[10] = "','";
						int num16 = 11;
						object obj17 = Main.Online_TrainsGV.Rows[i]["Sch_AT"];
						array2[num16] = ((obj17 != null) ? obj17.ToString() : null);
						array2[12] = "','";
						int num17 = 13;
						object obj18 = Main.Online_TrainsGV.Rows[i]["Sch_DT"];
						array2[num17] = ((obj18 != null) ? obj18.ToString() : null);
						array2[14] = "','";
						int num18 = 15;
						object obj19 = Main.Online_TrainsGV.Rows[i]["Late"];
						array2[num18] = ((obj19 != null) ? obj19.ToString() : null);
						array2[16] = "','";
						int num19 = 17;
						object obj20 = Main.Online_TrainsGV.Rows[i]["Exp_AT"];
						array2[num19] = ((obj20 != null) ? obj20.ToString() : null);
						array2[18] = "','";
						int num20 = 19;
						object obj21 = Main.Online_TrainsGV.Rows[i]["Exp_DT"];
						array2[num20] = ((obj21 != null) ? obj21.ToString() : null);
						array2[20] = "','";
						int num21 = 21;
						object obj22 = Main.Online_TrainsGV.Rows[i]["Sch_PF"];
						array2[num21] = ((obj22 != null) ? obj22.ToString() : null);
						array2[22] = "','";
						int num22 = 23;
						object obj23 = Main.Online_TrainsGV.Rows[i]["AN"];
						array2[num22] = ((obj23 != null) ? obj23.ToString() : null);
						array2[24] = "','')";
						cmd3.CommandText = string.Concat(array2);
						this.Cmd.ExecuteNonQuery();
					}
				}
				catch (Exception ex)
				{
					Misc_Functions misc_Functions = new Misc_Functions();
					misc_Functions.Write_Log("ERROR", null, "Error - " + ex.ToString());
				}
			}
			Main.Flag_DatabaseInUse = false;
			this.Conn.Close();
		}

		// Token: 0x0400003A RID: 58
		public OleDbConnection Conn = new OleDbConnection("Provider=Microsoft.ACE.OLEDB.12.0;Data Source=" + Application.StartupPath + "\\Data\\Database.accdb;Persist Security Info=False;Jet OLEDB:Database Password=EDS@0615");

		// Token: 0x0400003B RID: 59
		private OleDbCommandBuilder CBuld;

		// Token: 0x0400003C RID: 60
		private DataTable DTable;

		// Token: 0x0400003D RID: 61
		private OleDbDataAdapter Adpt;

		// Token: 0x0400003E RID: 62
		private OleDbCommand Cmd;
	}
}
