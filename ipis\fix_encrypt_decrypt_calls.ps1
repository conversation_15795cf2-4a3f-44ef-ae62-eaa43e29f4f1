# PowerShell script to fix all EncryptingTextFileData and DecryptingTextFileData calls

Write-Host "=== FIXING ENCRYPT/DECRYPT METHOD CALLS ==="

$file = "WriteDisplayMessageStruct.cs"
$content = Get-Content $file -Raw

if ([string]::IsNullOrEmpty($content)) { 
    Write-Host "File is empty or not found"
    exit 
}

$originalContent = $content

# Fix all EncryptingTextFileData calls
$content = $content -replace 'WriteDisplayMessageStruct\.EncryptingTextFileData\(([^,]+),\s*([^)]+)\)', 'WriteDisplayMessageStruct.EncryptingTextFileData($1, ref $2)'

# Fix all DecryptingTextFileData calls
$content = $content -replace 'WriteDisplayMessageStruct\.DecryptingTextFileData\(([^,]+),\s*([^,]+),\s*([^)]+)\)', 'WriteDisplayMessageStruct.DecryptingTextFileData($1, ref $2, $3)'

# Fix double ref issues
$content = $content -replace 'ref\s+ref\s+', 'ref '

if ($content -ne $originalContent) {
    Set-Content -Path $file -Value $content -NoNewline
    Write-Host "Fixed encrypt/decrypt method calls in $file"
} else {
    Write-Host "No changes needed in $file"
}

Write-Host "Encrypt/decrypt method call fixes complete!"
