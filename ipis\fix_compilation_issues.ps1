# PowerShell script to fix common compilation issues in the IPIS project

# Get all .cs files in the current directory and subdirectories
$csFiles = Get-ChildItem -Path "." -Filter "*.cs" -Recurse

Write-Host "Found $($csFiles.Count) C# files to process..."

foreach ($file in $csFiles) {
    Write-Host "Processing: $($file.Name)"

    # Read the file content
    $content = Get-Content $file.FullName -Raw

    # Skip if file is empty
    if ([string]::IsNullOrWhiteSpace($content)) {
        continue
    }

    $modified = $false

    # Fix backtick characters that were incorrectly introduced
    if ($content -match 'namespace ipis`r`n\{') {
        $content = $content -replace 'namespace ipis`r`n\{', "namespace ipis`r`n{"
        $modified = $true
        Write-Host "  - Fixed backtick characters in namespace"
    }

    # Fix #nullable disable and namespace issues
    if ($content -match '#nullable disable\s*\r?\nnamespace ipis;') {
        $content = $content -replace '#nullable disable\s*\r?\nnamespace ipis;', "namespace ipis`r`n{"
        $modified = $true
        Write-Host "  - Fixed #nullable disable and namespace"
    }

    # Save the file if modified
    if ($modified) {
        Set-Content -Path $file.FullName -Value $content -NoNewline
        Write-Host "  - File updated"
    }
}

Write-Host "Processing complete!"
