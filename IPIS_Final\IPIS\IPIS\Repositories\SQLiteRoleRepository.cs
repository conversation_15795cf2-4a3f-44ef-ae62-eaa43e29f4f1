using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SQLite;
using IPIS.Repositories.Interfaces;
using IPIS.Utils;
using IPIS.Models;

namespace IPIS.Repositories
{
    public class SQLiteRoleRepository : IRoleRepository
    {
        private readonly string connectionString;

        public SQLiteRoleRepository()
        {
            connectionString = Database.ConnectionString;
        }

        public void AddRole(string name, string description, List<string> permissions)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
                {
                    try
                    {
                        string insertRoleQuery = "INSERT INTO Roles (Name, Description, IsActive, CreatedAt) VALUES (@Name, @Description, @IsActive, @CreatedAt)";
                        using (var command = new SQLiteCommand(insertRoleQuery, connection, transaction))
                        {
                            command.Parameters.AddWithValue("@Name", name);
                            command.Parameters.AddWithValue("@Description", description);
                            command.Parameters.AddWithValue("@IsActive", true);
                            command.Parameters.AddWithValue("@CreatedAt", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                            command.ExecuteNonQuery();
                        }

                        long roleId;
                        using (var command = new SQLiteCommand("SELECT last_insert_rowid()", connection, transaction))
                        {
                            roleId = Convert.ToInt64(command.ExecuteScalar());
                        }

                        string insertPermissionQuery = "INSERT INTO RolePermissions (RoleId, Permission) VALUES (@RoleId, @Permission)";
                        foreach (string permission in permissions)
                        {
                            using (var command = new SQLiteCommand(insertPermissionQuery, connection, transaction))
                            {
                                command.Parameters.AddWithValue("@RoleId", roleId);
                                command.Parameters.AddWithValue("@Permission", permission);
                                command.ExecuteNonQuery();
                            }
                        }

                        transaction.Commit();
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        throw new Exception("Error adding role: " + ex.Message, ex);
                    }
                }
            }
        }

        public void UpdateRole(long roleId, string name, string description, List<string> permissions)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
                {
                    try
                    {
                        string updateRoleQuery = "UPDATE Roles SET Name = @Name, Description = @Description, UpdatedAt = @UpdatedAt WHERE Id = @RoleId";
                        using (var command = new SQLiteCommand(updateRoleQuery, connection, transaction))
                        {
                            command.Parameters.AddWithValue("@Name", name);
                            command.Parameters.AddWithValue("@Description", description);
                            command.Parameters.AddWithValue("@UpdatedAt", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                            command.Parameters.AddWithValue("@RoleId", roleId);
                            command.ExecuteNonQuery();
                        }

                        string deletePermissionsQuery = "DELETE FROM RolePermissions WHERE RoleId = @RoleId";
                        using (var command = new SQLiteCommand(deletePermissionsQuery, connection, transaction))
                        {
                            command.Parameters.AddWithValue("@RoleId", roleId);
                            command.ExecuteNonQuery();
                        }

                        string insertPermissionQuery = "INSERT INTO RolePermissions (RoleId, Permission) VALUES (@RoleId, @Permission)";
                        foreach (string permission in permissions)
                        {
                            using (var command = new SQLiteCommand(insertPermissionQuery, connection, transaction))
                            {
                                command.Parameters.AddWithValue("@RoleId", roleId);
                                command.Parameters.AddWithValue("@Permission", permission);
                                command.ExecuteNonQuery();
                            }
                        }

                        transaction.Commit();
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        throw new Exception("Error updating role: " + ex.Message, ex);
                    }
                }
            }
        }

        public void DeleteRole(long roleId)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
                {
                    try
                    {
                        string deletePermissionsQuery = "DELETE FROM RolePermissions WHERE RoleId = @RoleId";
                        using (var command = new SQLiteCommand(deletePermissionsQuery, connection, transaction))
                        {
                            command.Parameters.AddWithValue("@RoleId", roleId);
                            command.ExecuteNonQuery();
                        }

                        string deleteRoleQuery = "DELETE FROM Roles WHERE Id = @RoleId";
                        using (var command = new SQLiteCommand(deleteRoleQuery, connection, transaction))
                        {
                            command.Parameters.AddWithValue("@RoleId", roleId);
                            command.ExecuteNonQuery();
                        }

                        transaction.Commit();
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        throw new Exception("Error deleting role: " + ex.Message, ex);
                    }
                }
            }
        }

        public DataTable GetAllRoles()
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = "SELECT r.Id, r.Name, r.Description, r.IsActive, r.CreatedAt, r.UpdatedAt, GROUP_CONCAT(rp.Permission) AS Permissions FROM Roles r LEFT JOIN RolePermissions rp ON r.Id = rp.RoleId GROUP BY r.Id";
                using (var command = new SQLiteCommand(query, connection))
                {
                    using (var adapter = new SQLiteDataAdapter(command))
                    {
                        var dataTable = new DataTable();
                        adapter.Fill(dataTable);
                        return dataTable;
                    }
                }
            }
        }

        public Role GetRoleById(long roleId)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = "SELECT Id, Name, Description, IsActive, CreatedAt, UpdatedAt FROM Roles WHERE Id = @RoleId";
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@RoleId", roleId);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            var role = new Role
                            {
                                Id = reader.GetInt64("Id"),
                                Name = reader.GetString("Name"),
                                Description = reader.GetString("Description"),
                                IsActive = reader.GetBoolean("IsActive")
                            };

                            if (!reader.IsDBNull("CreatedAt"))
                            {
                                role.CreatedAt = DateTime.Parse(reader.GetString("CreatedAt"));
                            }

                            if (!reader.IsDBNull("UpdatedAt"))
                            {
                                role.UpdatedAt = DateTime.Parse(reader.GetString("UpdatedAt"));
                            }

                            // Load permissions
                            role.Permissions = GetRolePermissions(roleId);
                            return role;
                        }
                    }
                }
            }
            return null;
        }

        public Role GetRoleByName(string name)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = "SELECT Id, Name, Description, IsActive, CreatedAt, UpdatedAt FROM Roles WHERE Name = @Name";
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Name", name);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            var role = new Role
                            {
                                Id = reader.GetInt64("Id"),
                                Name = reader.GetString("Name"),
                                Description = reader.GetString("Description"),
                                IsActive = reader.GetBoolean("IsActive")
                            };

                            if (!reader.IsDBNull("CreatedAt"))
                            {
                                role.CreatedAt = DateTime.Parse(reader.GetString("CreatedAt"));
                            }

                            if (!reader.IsDBNull("UpdatedAt"))
                            {
                                role.UpdatedAt = DateTime.Parse(reader.GetString("UpdatedAt"));
                            }

                            // Load permissions
                            role.Permissions = GetRolePermissions(role.Id);
                            return role;
                        }
                    }
                }
            }
            return null;
        }

        public List<string> GetRolePermissions(long roleId)
        {
            var permissions = new List<string>();
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = "SELECT Permission FROM RolePermissions WHERE RoleId = @RoleId";
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@RoleId", roleId);
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            permissions.Add(reader.GetString(0));
                        }
                    }
                }
            }
            return permissions;
        }

        public bool RoleExists(string name)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = "SELECT COUNT(*) FROM Roles WHERE Name = @Name";
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Name", name);
                    long count = (long)command.ExecuteScalar();
                    return count > 0;
                }
            }
        }

        public bool IsRoleInUse(long roleId)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = "SELECT COUNT(*) FROM Users WHERE Role = (SELECT Name FROM Roles WHERE Id = @RoleId)";
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@RoleId", roleId);
                    long count = (long)command.ExecuteScalar();
                    return count > 0;
                }
            }
        }
    }
} 