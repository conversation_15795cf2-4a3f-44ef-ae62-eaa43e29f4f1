using System.Data;
using System.Collections.Generic;

namespace IPIS.Repositories
{
    public interface IAdvertisingRepository
    {
        DataTable GetAllAdvertisements();
        void AddAdvertisement(string annType, string adverName, string hindiWave, string engWave, string adverTime, string adverCount,
            string platform, string timeSlot, string trainNumber, string playPosition, int rank, int randomize, int monthQuota, int weekQuota, int dayQuota, int slotQuota, int extraQuota, string days);
        void UpdateAdvertisement(string annType, string adverName, string hindiWave, string engWave, string adverTime, string adverCount,
            string platform, string timeSlot, string trainNumber, string playPosition, int rank, int randomize, int monthQuota, int weekQuota, int dayQuota, int slotQuota, int extraQuota, string days);
        void DeleteAdvertisement(string annType, string adverName);
        Dictionary<string, string> GetAdvertisementLanguageWaves(string annType, string adverName);
        void SaveAdvertisementLanguageWaves(string annType, string adverName, Dictionary<string, string> languageWaves);
        Dictionary<string, string> GetAdvertisementLanguageWaves(int advertisingId);
        void SaveAdvertisementLanguageWaves(int advertisingId, string annType, string adverName, Dictionary<string, string> languageWaves);
        void UpdateTotalDuration(string annType, string adverName, double totalDuration, string formattedDuration);
        void IncrementQuotaUsed(string annType, string adverName);
    }
}