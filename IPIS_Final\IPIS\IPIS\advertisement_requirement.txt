this module need to implement for audio advertisement in between announcements of trains.
preference should be always for announcements over advertisements.
advertisement can be in multiple languages. which should be driven by active languages in language management module.

advertisement can be configure as:
    1. its either play before announcement, after announcement or both.
    2. advertisement play count quota can be configured.
        1. month wise counts
        2. daily limit count.
        3. if any slot configured for day, so count for that slot.
        all points should be related which should not exceed overall month count.
there should be a sequence management of advertisements if multiple associated.
advertisement should be platform driven. which configure advertisement there should be a platform selection of that station. all or platform selection.

advertisement can be configure in 2 ways
first, time slot wise, in selected time slot we can selected advertisement, its count of number of play and there sequence.
second, train wise: which advertisement will play before and after train announcement, and its sequence.
there should be a full track record in form of logs. advertisement add, edit delete, play time, skipped or postponed because of train announcement etc. for all cases.
plus which user trigger the advertisement should also be logged.
we will create a separate db file for logs data.

// User related tables
CREATE TABLE "Users" (
	"Id"	INTEGER,
	"Username"	TEXT NOT NULL UNIQUE,
	"Password"	TEXT NOT NULL,
	"Role"	TEXT NOT NULL,
	"LastLogin"	TEXT,
	"Status"	TEXT,
	PRIMARY KEY("Id" AUTOINCREMENT)
)
CREATE TABLE User_Details (
    User_Name TEXT PRIMARY KEY,
    Pass TEXT,
    User_type TEXT,
    Hint_Pass TEXT,
    Chk_Adver INTEGER,      -- 0/1 for boolean
    Chk_TDEntry INTEGER,
    Chk_Reports INTEGER,
    Chk_SD INTEGER,
    Chk_AUser INTEGER,
    Chk_ASCode INTEGER,
    Chk_Rep INTEGER
)
CREATE TABLE "UserPermissions" (
	"Id"	INTEGER,
	"UserId"	INTEGER NOT NULL,
	"Permission"	TEXT NOT NULL,
	PRIMARY KEY("Id" AUTOINCREMENT),
	FOREIGN KEY("UserId") REFERENCES "Users"("Id")
)

