﻿using System;
using System.CodeDom.Compiler;
using System.Configuration;
using System.Runtime.CompilerServices;

namespace Announcement.Properties
{
	// Token: 0x02000013 RID: 19
	[CompilerGenerated]
	[GeneratedCode("Microsoft.VisualStudio.Editors.SettingsDesigner.SettingsSingleFileGenerator", "17.6.0.0")]
	internal sealed partial class Settings : ApplicationSettingsBase
	{
		// Token: 0x1700001D RID: 29
		// (get) Token: 0x060000EE RID: 238 RVA: 0x0001F3F4 File Offset: 0x0001D5F4
		public static Settings Default
		{
			get
			{
				return Settings.defaultInstance;
			}
		}

		// Token: 0x040001B5 RID: 437
		private static Settings defaultInstance = (Settings)SettingsBase.Synchronized(new Settings());
	}
}
