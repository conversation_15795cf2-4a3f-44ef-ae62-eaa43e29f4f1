using System;
using System.Collections.Generic;
using System.Data;
using IPIS.Models;

namespace IPIS.Repositories.Interfaces
{
    public interface ILoggingRepository
    {
        // System Logs
        void AddSystemLog(LogEntry logEntry);
        DataTable GetSystemLogs(DateTime? startDate = null, DateTime? endDate = null, LogLevel? level = null,
                               LogCategory? category = null, string searchText = null, int limit = 1000, int offset = 0);

        // User Activity Logs
        void AddUserActivityLog(UserActivityLog activityLog);
        DataTable GetUserActivityLogs(DateTime? startDate = null, DateTime? endDate = null, LogCategory? category = null,
                                     string username = null, string action = null, int limit = 1000, int offset = 0);

        // Statistics
        int GetLogCount(LogLevel? level = null, LogCategory? category = null, DateTime? startDate = null, DateTime? endDate = null);
        Dictionary<LogLevel, int> GetLogCountByLevel(DateTime? startDate = null, DateTime? endDate = null);
        Dictionary<LogCategory, int> GetLogCountByCategory(DateTime? startDate = null, DateTime? endDate = null);

        // Maintenance
        void DeleteOldLogs(DateTime cutoffDate);
        DataTable ExportLogs(DateTime? startDate = null, DateTime? endDate = null, LogLevel? level = null);
    }
}
