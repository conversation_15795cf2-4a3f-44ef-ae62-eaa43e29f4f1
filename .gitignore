# Dependencies
node_modules/
.pnp/
.pnp.js

# Testing
coverage/

# Production
build/
dist/
out/

# TypeScript
*.tsbuildinfo

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Debug logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE and editor files
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Cache directories
.cache/
.npm/
.eslintcache

# Railway specific
.railway/

# .NET specific
*.user
*.userosscache
*.suo
*.userprefs
.vs/
bin/
obj/
*.dll
*.exe
*.pdb
*.cache
*.log
*.vspscc
*.vssscc
.builds
*.pidb
*.svclog
*.scc

# Visual Studio files
.vs/
*.ncrunchsolution
*.ncrunchproject
*.DotSettings
*.DotSettings.user
IPIS_Final/IPIS/IPIS/context/
IPIS_Final/IPIS/IPIS/audio_to_text_converter.py
IPIS_Final/IPIS/IPIS/requirements.txt
IPIS_Final/IPIS/IPIS/run_audio_conversion.bat
IPIS_Final/IPIS/IPIS/std_audio_text_table.csv
IPIS_Final/IPIS/IPIS/audio_to_text_converter_enhanced.py
IPIS_Final/IPIS/IPIS/convert_hindi_std.py
