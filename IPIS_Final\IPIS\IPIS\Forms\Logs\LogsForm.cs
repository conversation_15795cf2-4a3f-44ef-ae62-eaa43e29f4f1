using System;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using IPIS.Models;
using IPIS.Services;
using IPIS.Services.Interfaces;
using IPIS.Repositories;
using IPIS.Utils;

namespace IPIS.Forms.Logs
{
    public partial class LogsForm : Form
    {
        private readonly ILoggingService loggingService;
        private readonly PaginationHelper paginationHelper;
        private DataGridView logsDataGridView = null!;
        private ComboBox logTypeComboBox = null!;
        private ComboBox categoryComboBox = null!;
        private DateTimePicker startDatePicker = null!;
        private DateTimePicker endDatePicker = null!;
        private TextBox searchTextBox = null!;
        private Button refreshButton = null!;
        private Button exportButton = null!;
        private Button clearFiltersButton = null!;
        private Label recordCountLabel = null!;
        private ProgressBar loadingProgressBar = null!;

        // Pagination controls
        private Button firstPageButton = null!;
        private Button previousPageButton = null!;
        private Button nextPageButton = null!;
        private Button lastPageButton = null!;
        private TextBox currentPageTextBox = null!;
        private Label pageInfoLabel = null!;
        private ComboBox pageSizeComboBox = null!;

        public LogsForm()
        {
            loggingService = new LoggingService(new SQLiteLoggingRepository());
            paginationHelper = new PaginationHelper(50); // Default page size of 50
            InitializeComponent();
            this.Load += LogsForm_Load;
        }

        private void InitializeComponent()
        {
            this.Text = "System Logs";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterParent;

            // Create main panel
            var mainPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 4,
                Padding = new Padding(10)
            };

            // Set row styles
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 120)); // Filter panel
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 100)); // Data grid
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 50));  // Pagination panel
            mainPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 40));  // Status panel

            this.Controls.Add(mainPanel);

            // Create filter panel
            CreateFilterPanel(mainPanel);

            // Create data grid
            CreateDataGrid(mainPanel);

            // Create pagination panel
            CreatePaginationPanel(mainPanel);

            // Create status panel
            CreateStatusPanel(mainPanel);
        }

        private void CreateFilterPanel(TableLayoutPanel mainPanel)
        {
            var filterPanel = new GroupBox
            {
                Text = "Filters",
                Dock = DockStyle.Fill,
                Padding = new Padding(10)
            };

            var filterLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 6,
                RowCount = 2,
                Padding = new Padding(5)
            };

            // Set column styles
            for (int i = 0; i < 6; i++)
            {
                filterLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 16.67f));
            }
            filterLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 50));
            filterLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 50));

            // Row 1: Log Type, Category, Start Date
            filterLayout.Controls.Add(new Label { Text = "Log Type:", Anchor = AnchorStyles.Left }, 0, 0);
            logTypeComboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Anchor = AnchorStyles.Left | AnchorStyles.Right,
                Width = 150
            };
            logTypeComboBox.Items.AddRange(new[] { "All Logs", "Info Logs", "Error Logs" });
            logTypeComboBox.SelectedIndex = 0;
            logTypeComboBox.SelectedIndexChanged += OnFiltersChanged;
            filterLayout.Controls.Add(logTypeComboBox, 0, 1);

            filterLayout.Controls.Add(new Label { Text = "Category:", Anchor = AnchorStyles.Left }, 1, 0);
            categoryComboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Anchor = AnchorStyles.Left | AnchorStyles.Right,
                Width = 150
            };
            PopulateCategoryComboBox();
            categoryComboBox.SelectedIndexChanged += OnFiltersChanged;
            filterLayout.Controls.Add(categoryComboBox, 1, 1);

            filterLayout.Controls.Add(new Label { Text = "Start Date:", Anchor = AnchorStyles.Left }, 2, 0);
            startDatePicker = new DateTimePicker
            {
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Today.AddDays(-7),
                Anchor = AnchorStyles.Left | AnchorStyles.Right
            };
            startDatePicker.ValueChanged += OnFiltersChanged;
            filterLayout.Controls.Add(startDatePicker, 2, 1);

            // Row 2: End Date, Search, Buttons
            filterLayout.Controls.Add(new Label { Text = "End Date:", Anchor = AnchorStyles.Left }, 3, 0);
            endDatePicker = new DateTimePicker
            {
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Today.AddDays(1),
                Anchor = AnchorStyles.Left | AnchorStyles.Right
            };
            endDatePicker.ValueChanged += OnFiltersChanged;
            filterLayout.Controls.Add(endDatePicker, 3, 1);

            filterLayout.Controls.Add(new Label { Text = "Search:", Anchor = AnchorStyles.Left }, 4, 0);
            searchTextBox = new TextBox
            {
                Anchor = AnchorStyles.Left | AnchorStyles.Right,
                PlaceholderText = "Search in messages..."
            };
            searchTextBox.TextChanged += OnSearchTextChanged;
            filterLayout.Controls.Add(searchTextBox, 4, 1);

            // Buttons panel
            var buttonsPanel = new FlowLayoutPanel
            {
                FlowDirection = FlowDirection.LeftToRight,
                Anchor = AnchorStyles.Left | AnchorStyles.Right,
                Height = 60
            };

            refreshButton = new Button
            {
                Text = "Refresh",
                Size = new Size(80, 30),
                UseVisualStyleBackColor = true
            };
            refreshButton.Click += RefreshButton_Click;
            buttonsPanel.Controls.Add(refreshButton);

            exportButton = new Button
            {
                Text = "Export",
                Size = new Size(80, 30),
                UseVisualStyleBackColor = true
            };
            exportButton.Click += ExportButton_Click;
            buttonsPanel.Controls.Add(exportButton);

            clearFiltersButton = new Button
            {
                Text = "Clear Filters",
                Size = new Size(100, 30),
                UseVisualStyleBackColor = true
            };
            clearFiltersButton.Click += ClearFiltersButton_Click;
            buttonsPanel.Controls.Add(clearFiltersButton);

            filterLayout.Controls.Add(buttonsPanel, 5, 1);

            filterPanel.Controls.Add(filterLayout);
            mainPanel.Controls.Add(filterPanel, 0, 0);
        }

        private void CreateDataGrid(TableLayoutPanel mainPanel)
        {
            logsDataGridView = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                RowHeadersVisible = false,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D
            };

            // Configure columns
            logsDataGridView.AutoGenerateColumns = false;
            SetupDataGridColumns();

            // Add loading progress bar
            loadingProgressBar = new ProgressBar
            {
                Style = ProgressBarStyle.Marquee,
                Visible = false,
                Dock = DockStyle.Top,
                Height = 5
            };

            var gridPanel = new Panel { Dock = DockStyle.Fill };
            gridPanel.Controls.Add(logsDataGridView);
            gridPanel.Controls.Add(loadingProgressBar);

            mainPanel.Controls.Add(gridPanel, 0, 1);
        }

        private void SetupDataGridColumns()
        {
            logsDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Timestamp",
                HeaderText = "Timestamp",
                DataPropertyName = "Timestamp",
                FillWeight = 15,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "yyyy-MM-dd HH:mm:ss" }
            });

            logsDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Level",
                HeaderText = "Level",
                DataPropertyName = "Level",
                FillWeight = 8
            });

            logsDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Category",
                HeaderText = "Category",
                DataPropertyName = "Category",
                FillWeight = 12
            });

            logsDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Message",
                HeaderText = "Message",
                DataPropertyName = "Message",
                FillWeight = 35
            });

            logsDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Username",
                HeaderText = "User",
                DataPropertyName = "Username",
                FillWeight = 10
            });

            logsDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Source",
                HeaderText = "Source",
                DataPropertyName = "Source",
                FillWeight = 15
            });

            logsDataGridView.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Details",
                HeaderText = "Details",
                DataPropertyName = "Details",
                FillWeight = 5,
                Visible = false // Hidden by default, can be shown via context menu
            });

            // Add row formatting based on log level
            logsDataGridView.CellFormatting += LogsDataGridView_CellFormatting;
            logsDataGridView.CellDoubleClick += LogsDataGridView_CellDoubleClick;
        }

        private void CreatePaginationPanel(TableLayoutPanel mainPanel)
        {
            var paginationPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = SystemColors.Control,
                BorderStyle = BorderStyle.FixedSingle
            };

            // Create pagination controls layout
            var paginationLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 8,
                RowCount = 1,
                Padding = new Padding(5)
            };

            // Set column styles for pagination layout
            paginationLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize)); // First button
            paginationLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize)); // Previous button
            paginationLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize)); // Page input
            paginationLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize)); // Page info
            paginationLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize)); // Next button
            paginationLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize)); // Last button
            paginationLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100)); // Spacer
            paginationLayout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize)); // Page size combo

            // Create pagination buttons
            firstPageButton = new Button
            {
                Text = "<<",
                Size = new Size(40, 30),
                Anchor = AnchorStyles.Left | AnchorStyles.Top,
                UseVisualStyleBackColor = true
            };
            firstPageButton.Click += FirstPageButton_Click;

            previousPageButton = new Button
            {
                Text = "<",
                Size = new Size(40, 30),
                Anchor = AnchorStyles.Left | AnchorStyles.Top,
                UseVisualStyleBackColor = true
            };
            previousPageButton.Click += PreviousPageButton_Click;

            currentPageTextBox = new TextBox
            {
                Size = new Size(60, 30),
                TextAlign = HorizontalAlignment.Center,
                Anchor = AnchorStyles.Left | AnchorStyles.Top
            };
            currentPageTextBox.KeyPress += CurrentPageTextBox_KeyPress;

            pageInfoLabel = new Label
            {
                Text = "of 0",
                AutoSize = true,
                Anchor = AnchorStyles.Left | AnchorStyles.Top,
                TextAlign = ContentAlignment.MiddleLeft,
                Margin = new Padding(5, 8, 5, 0)
            };

            nextPageButton = new Button
            {
                Text = ">",
                Size = new Size(40, 30),
                Anchor = AnchorStyles.Left | AnchorStyles.Top,
                UseVisualStyleBackColor = true
            };
            nextPageButton.Click += NextPageButton_Click;

            lastPageButton = new Button
            {
                Text = ">>",
                Size = new Size(40, 30),
                Anchor = AnchorStyles.Left | AnchorStyles.Top,
                UseVisualStyleBackColor = true
            };
            lastPageButton.Click += LastPageButton_Click;

            // Page size combo box
            pageSizeComboBox = new ComboBox
            {
                DropDownStyle = ComboBoxStyle.DropDownList,
                Size = new Size(100, 30),
                Anchor = AnchorStyles.Right | AnchorStyles.Top
            };
            pageSizeComboBox.Items.AddRange(new object[] { "25", "50", "100", "200" });
            pageSizeComboBox.SelectedItem = "50";
            pageSizeComboBox.SelectedIndexChanged += PageSizeComboBox_SelectedIndexChanged;

            // Add controls to pagination layout
            paginationLayout.Controls.Add(firstPageButton, 0, 0);
            paginationLayout.Controls.Add(previousPageButton, 1, 0);
            paginationLayout.Controls.Add(currentPageTextBox, 2, 0);
            paginationLayout.Controls.Add(pageInfoLabel, 3, 0);
            paginationLayout.Controls.Add(nextPageButton, 4, 0);
            paginationLayout.Controls.Add(lastPageButton, 5, 0);

            var pageSizeLabel = new Label
            {
                Text = "Page Size:",
                AutoSize = true,
                Anchor = AnchorStyles.Right | AnchorStyles.Top,
                TextAlign = ContentAlignment.MiddleRight,
                Margin = new Padding(5, 8, 5, 0)
            };

            var pageSizePanel = new Panel
            {
                Dock = DockStyle.Fill
            };
            pageSizePanel.Controls.Add(pageSizeLabel);
            pageSizePanel.Controls.Add(pageSizeComboBox);
            pageSizeLabel.Location = new Point(0, 8);
            pageSizeComboBox.Location = new Point(pageSizeLabel.Width + 5, 5);

            paginationLayout.Controls.Add(pageSizePanel, 7, 0);

            paginationPanel.Controls.Add(paginationLayout);
            mainPanel.Controls.Add(paginationPanel, 0, 2);
        }

        private void CreateStatusPanel(TableLayoutPanel mainPanel)
        {
            var statusPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = SystemColors.Control,
                BorderStyle = BorderStyle.FixedSingle
            };

            recordCountLabel = new Label
            {
                Text = "Ready",
                AutoSize = true,
                Location = new Point(10, 10),
                ForeColor = Color.DarkBlue
            };

            statusPanel.Controls.Add(recordCountLabel);
            mainPanel.Controls.Add(statusPanel, 0, 3);
        }

        private void PopulateCategoryComboBox()
        {
            categoryComboBox.Items.Add("All Categories");

            foreach (LogCategory category in Enum.GetValues<LogCategory>())
            {
                var logEntry = new LogEntry { Category = category };
                categoryComboBox.Items.Add(logEntry.GetCategoryDisplayName());
            }

            categoryComboBox.SelectedIndex = 0;
        }

        private void LoadInitialData()
        {
            LoadLogs();
        }

        private async void LoadLogs()
        {
            try
            {
                loadingProgressBar.Visible = true;
                recordCountLabel.Text = "Loading logs...";

                await System.Threading.Tasks.Task.Run(() =>
                {
                    DateTime startDate = DateTime.MinValue;
                    DateTime endDate = DateTime.MinValue;
                    string? searchText = null;
                    int categoryIndex = 0;
                    int logTypeIndex = 0;

                    // Marshal all UI access to the UI thread
                    this.Invoke(new Action(() =>
                    {
                        startDate = startDatePicker.Value.Date;
                        endDate = endDatePicker.Value.Date.AddDays(1).AddSeconds(-1);
                        searchText = string.IsNullOrWhiteSpace(searchTextBox.Text) ? null : searchTextBox.Text.Trim();
                        categoryIndex = categoryComboBox.SelectedIndex;
                        logTypeIndex = logTypeComboBox.SelectedIndex;
                    }));

                    LogCategory? selectedCategory = null;
                    if (categoryIndex > 0)
                    {
                        selectedCategory = (LogCategory)(categoryIndex - 1);
                    }

                    // First, get the total count for pagination
                    int totalCount;
                    switch (logTypeIndex)
                    {
                        case 1: // Info Logs - count Debug, Info, and Warning levels
                            totalCount = loggingService.GetLogCount(LogLevel.Debug, selectedCategory, startDate, endDate) +
                                        loggingService.GetLogCount(LogLevel.Info, selectedCategory, startDate, endDate) +
                                        loggingService.GetLogCount(LogLevel.Warning, selectedCategory, startDate, endDate);
                            break;
                        case 2: // Error Logs - count Error and Critical levels
                            totalCount = loggingService.GetLogCount(LogLevel.Error, selectedCategory, startDate, endDate) +
                                        loggingService.GetLogCount(LogLevel.Critical, selectedCategory, startDate, endDate);
                            break;
                        default: // All Logs
                            totalCount = loggingService.GetLogCount(null, selectedCategory, startDate, endDate);
                            break;
                    }

                    // Update pagination helper with total count
                    this.Invoke(new Action(() =>
                    {
                        paginationHelper.SetTotalRecords(totalCount);
                        UpdatePaginationControls();
                    }));

                    // Get paginated data
                    DataTable logs;
                    switch (logTypeIndex)
                    {
                        case 1: // Info Logs
                            logs = loggingService.GetInfoLogs(startDate, endDate, selectedCategory, searchText ?? string.Empty,
                                                            paginationHelper.PageSize, paginationHelper.Offset);
                            break;
                        case 2: // Error Logs
                            logs = loggingService.GetErrorLogs(startDate, endDate, selectedCategory, searchText ?? string.Empty,
                                                             paginationHelper.PageSize, paginationHelper.Offset);
                            break;
                        default: // All Logs
                            logs = loggingService.GetSystemLogs(startDate, endDate, null, selectedCategory, searchText ?? string.Empty,
                                                              paginationHelper.PageSize, paginationHelper.Offset);
                            break;
                    }

                    this.Invoke(new Action(() =>
                    {
                        logsDataGridView.DataSource = logs;
                        UpdatePaginationControls();
                        loadingProgressBar.Visible = false;
                    }));
                });
            }
            catch (Exception ex)
            {
                loadingProgressBar.Visible = false;
                recordCountLabel.Text = "Error loading logs";
                MessageBox.Show($"Error loading logs: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LogsDataGridView_CellFormatting(object? sender, DataGridViewCellFormattingEventArgs e)
        {
            if (logsDataGridView.Columns[e.ColumnIndex].Name == "Level" && e.Value != null)
            {
                string level = e.Value.ToString() ?? "";
                switch (level.ToLower())
                {
                    case "error":
                    case "critical":
                        e.CellStyle!.BackColor = Color.LightCoral;
                        e.CellStyle!.ForeColor = Color.DarkRed;
                        break;
                    case "warning":
                        e.CellStyle!.BackColor = Color.LightYellow;
                        e.CellStyle!.ForeColor = Color.DarkOrange;
                        break;
                    case "info":
                        e.CellStyle!.BackColor = Color.LightBlue;
                        e.CellStyle!.ForeColor = Color.DarkBlue;
                        break;
                    case "debug":
                        e.CellStyle!.BackColor = Color.LightGray;
                        e.CellStyle!.ForeColor = Color.DarkGray;
                        break;
                }
            }
        }

        private void LogsDataGridView_CellDoubleClick(object? sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0 && logsDataGridView.Rows[e.RowIndex].DataBoundItem is DataRowView rowView)
            {
                ShowLogDetails(rowView.Row);
            }
        }

        private void ShowLogDetails(DataRow logRow)
        {
            var detailsForm = new Form
            {
                Text = "Log Details",
                Size = new Size(600, 400),
                StartPosition = FormStartPosition.CenterParent,
                MaximizeBox = false,
                MinimizeBox = false
            };

            var textBox = new TextBox
            {
                Multiline = true,
                ScrollBars = ScrollBars.Both,
                Dock = DockStyle.Fill,
                ReadOnly = true,
                Font = new Font("Consolas", 9)
            };

            var details = new StringBuilder();
            details.AppendLine($"Timestamp: {logRow["Timestamp"]}");
            details.AppendLine($"Level: {logRow["Level"]}");
            details.AppendLine($"Category: {logRow["Category"]}");
            details.AppendLine($"User: {logRow["Username"]}");
            details.AppendLine($"Source: {logRow["Source"]}");
            details.AppendLine($"Message: {logRow["Message"]}");
            details.AppendLine();
            details.AppendLine("Details:");
            details.AppendLine(logRow["Details"]?.ToString() ?? "No additional details");

            if (!string.IsNullOrEmpty(logRow["Exception"]?.ToString()))
            {
                details.AppendLine();
                details.AppendLine("Exception:");
                details.AppendLine(logRow["Exception"].ToString());
            }

            textBox.Text = details.ToString();
            detailsForm.Controls.Add(textBox);
            detailsForm.ShowDialog();
        }

        private void OnFiltersChanged(object? sender, EventArgs e)
        {
            paginationHelper.Reset();
            UpdatePaginationControls();
            LoadLogs();
        }

        private System.Windows.Forms.Timer? searchTimer;
        private void OnSearchTextChanged(object? sender, EventArgs e)
        {
            // Debounce search to avoid too many database calls
            searchTimer?.Stop();
            searchTimer = new System.Windows.Forms.Timer { Interval = 500 };
            searchTimer.Tick += (s, args) =>
            {
                searchTimer.Stop();
                paginationHelper.Reset();
                UpdatePaginationControls();
                LoadLogs();
            };
            searchTimer.Start();
        }

        private void RefreshButton_Click(object? sender, EventArgs e)
        {
            paginationHelper.Reset();
            UpdatePaginationControls();
            LoadLogs();
        }

        private void ExportButton_Click(object? sender, EventArgs e)
        {
            try
            {
                using (var saveDialog = new SaveFileDialog())
                {
                    saveDialog.Filter = "CSV files (*.csv)|*.csv|All files (*.*)|*.*";
                    saveDialog.DefaultExt = "csv";
                    saveDialog.FileName = $"IPIS_Logs_{DateTime.Now:yyyyMMdd_HHmmss}.csv";

                    if (saveDialog.ShowDialog() == DialogResult.OK)
                    {
                        var startDate = startDatePicker.Value.Date;
                        var endDate = endDatePicker.Value.Date.AddDays(1).AddSeconds(-1);

                        LogLevel? level = null;
                        if (logTypeComboBox.SelectedIndex == 2) // Error logs only
                        {
                            level = LogLevel.Error;
                        }

                        loggingService.ExportLogs(saveDialog.FileName, startDate, endDate, level);
                        MessageBox.Show("Logs exported successfully!", "Export Complete",
                                      MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error exporting logs: {ex.Message}", "Export Error",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ClearFiltersButton_Click(object? sender, EventArgs e)
        {
            logTypeComboBox.SelectedIndex = 0;
            categoryComboBox.SelectedIndex = 0;
            startDatePicker.Value = DateTime.Today.AddDays(-7);
            endDatePicker.Value = DateTime.Today.AddDays(1);
            searchTextBox.Clear();
            paginationHelper.Reset();
            UpdatePaginationControls();
            LoadLogs();
        }

        private void LogsForm_Load(object? sender, EventArgs e)
        {
            UpdatePaginationControls();
            LoadInitialData();
        }

        #region Pagination Event Handlers

        private void FirstPageButton_Click(object? sender, EventArgs e)
        {
            if (paginationHelper.GoToFirstPage())
            {
                UpdatePaginationControls();
                LoadLogs();
            }
        }

        private void PreviousPageButton_Click(object? sender, EventArgs e)
        {
            if (paginationHelper.GoToPreviousPage())
            {
                UpdatePaginationControls();
                LoadLogs();
            }
        }

        private void NextPageButton_Click(object? sender, EventArgs e)
        {
            if (paginationHelper.GoToNextPage())
            {
                UpdatePaginationControls();
                LoadLogs();
            }
        }

        private void LastPageButton_Click(object? sender, EventArgs e)
        {
            if (paginationHelper.GoToLastPage())
            {
                UpdatePaginationControls();
                LoadLogs();
            }
        }

        private void CurrentPageTextBox_KeyPress(object? sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                if (int.TryParse(currentPageTextBox.Text, out int page))
                {
                    if (paginationHelper.GoToPage(page))
                    {
                        UpdatePaginationControls();
                        LoadLogs();
                    }
                    else
                    {
                        // Reset to current page if invalid
                        currentPageTextBox.Text = paginationHelper.CurrentPage.ToString();
                    }
                }
                else
                {
                    // Reset to current page if invalid
                    currentPageTextBox.Text = paginationHelper.CurrentPage.ToString();
                }
                e.Handled = true;
            }
            else if (!char.IsControl(e.KeyChar) && !char.IsDigit(e.KeyChar))
            {
                e.Handled = true;
            }
        }

        private void PageSizeComboBox_SelectedIndexChanged(object? sender, EventArgs e)
        {
            if (int.TryParse(pageSizeComboBox.SelectedItem?.ToString(), out int pageSize))
            {
                paginationHelper.SetPageSize(pageSize);
                UpdatePaginationControls();
                LoadLogs();
            }
        }

        private void UpdatePaginationControls()
        {
            currentPageTextBox.Text = paginationHelper.CurrentPage.ToString();
            pageInfoLabel.Text = $"of {paginationHelper.TotalPages}";

            firstPageButton.Enabled = paginationHelper.HasPreviousPage;
            previousPageButton.Enabled = paginationHelper.HasPreviousPage;
            nextPageButton.Enabled = paginationHelper.HasNextPage;
            lastPageButton.Enabled = paginationHelper.HasNextPage;

            recordCountLabel.Text = paginationHelper.GetDisplayText();
        }

        #endregion

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                searchTimer?.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}