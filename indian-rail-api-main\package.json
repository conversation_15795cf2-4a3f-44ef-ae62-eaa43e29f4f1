{"name": "irctc-v2", "version": "1.2.0", "description": "", "main": "index.js", "type": "module", "scripts": {"start": "node app.js", "commit": "git-cz", "dev": "node --watch app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"cheerio": "^1.0.0-rc.12", "dotenv": "^16.0.3", "express": "^4.18.2", "user-agents": "^1.0.1190"}, "devDependencies": {"cz-conventional-changelog": "^3.3.0"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}}