# Train Loading and Auto-Refresh System

## Overview
The Announcement Board Form now includes intelligent train loading and auto-refresh functionality that filters trains based on running days and current time.

## Features

### 1. Smart Train Loading
- **Current Day & Time Mode (Default)**: Loads only trains that run on the current day and are within a 2-hour time window around the current time
- **Current Day Only**: Loads all trains that run on the current day regardless of time
- **All Trains**: Loads all trains from the master data (filtered for current day)

### 2. Auto-Refresh Configuration
- **Enable/Disable**: Toggle auto-refresh on/off
- **Configurable Interval**: Set refresh interval from 1 to 60 minutes (default: 2 minutes)
- **Runtime Configuration**: All settings can be changed while the application is running

### 3. Time Window Logic
- **Default Window**: ±1 hour around current time (2-hour total window)
- **Midnight Handling**: <PERSON>perly handles time windows that cross midnight
- **Arrival/Departure Times**: Considers both scheduled arrival and departure times

## Database Methods

### New Repository Methods
1. `GetTrainsForCurrentDay()`: Returns trains that run on the current day
2. `GetTrainsForCurrentTimeWindow(int timeWindowMinutes)`: Returns trains within the specified time window
3. `GetTrainsForCurrentDayAndTime(int timeWindowMinutes)`: Returns trains that run on current day AND within time window

### Train Filtering Logic
- Checks `All_Days` flag first
- If `All_Days` is false, checks the specific day column (`Chk_Mon`, `Chk_Tue`, etc.)
- Filters by time window around current time
- Orders results by scheduled arrival time

## UI Controls

### Sidebar Configuration
- **Enable Auto Refresh**: Checkbox to enable/disable auto-refresh
- **Interval (mins)**: NumericUpDown to set refresh interval (1-60 minutes)
- **Load Mode**: Dropdown to select loading mode
  - Current Day & Time
  - Current Day Only  
  - All Trains

### Status Display
- Shows current loading mode
- Displays number of trains loaded
- Shows current time window information
- Auto-refresh status and timing

## Usage

### Default Behavior
1. Application starts with auto-refresh enabled (2-minute interval)
2. Loads trains for current day and time window
3. Automatically refreshes data every 2 minutes
4. Shows relevant trains based on current time

### Manual Control
1. **Load Button**: Manually add a specific train to the online list
2. **Refresh Button**: Manually refresh all data
3. **Clear Button**: Clear the grid
4. **Configuration**: Change settings at any time

### Train Addition
- Only allows adding trains that run on the current day
- Validates train exists in master data
- Prevents duplicate entries in online list

## Technical Implementation

### Timer Management
- Uses `System.Windows.Forms.Timer` for auto-refresh
- Properly disposes of timers in `Dispose()` method
- Thread-safe timer handling

### Data Filtering
- Client-side filtering for current day when loading all trains
- Server-side filtering for time-based queries
- Efficient database queries with proper indexing

### Error Handling
- Graceful handling of missing audio files
- Database connection error handling
- User-friendly error messages

## Configuration

### Default Settings
- Auto-refresh interval: 2 minutes
- Time window: ±1 hour (2-hour total)
- Load mode: Current Day & Time
- Auto-refresh: Enabled

### Runtime Changes
All settings can be modified while the application is running:
- Changing refresh interval immediately updates the timer
- Switching load modes immediately reloads data
- Enabling/disabling auto-refresh starts/stops the timer

## Benefits

1. **Performance**: Only loads relevant trains, reducing memory usage
2. **Accuracy**: Shows trains that are actually running at current time
3. **Flexibility**: Multiple loading modes for different use cases
4. **Automation**: Reduces manual intervention with auto-refresh
5. **User Control**: Full control over refresh behavior and timing 