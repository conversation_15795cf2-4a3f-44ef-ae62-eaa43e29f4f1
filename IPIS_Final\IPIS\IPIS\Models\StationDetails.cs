namespace IPIS.Models
{
    public class StationDetails
    {
        public string StationName { get; set; }
        public string StationCode { get; set; }
        public bool AutoLoad { get; set; }
        public int AutoLoadInterval { get; set; }
        public int AutoDeleteInterval { get; set; }
        public int AutoDeletePostInterval { get; set; }
        public bool AutoDelete { get; set; }
        public int AvailablePF { get; set; }
        public string P1 { get; set; }
        public string P2 { get; set; }
        public string P3 { get; set; }
        public string P4 { get; set; }
        public string P5 { get; set; }
        public string P6 { get; set; }
        public string P7 { get; set; }
        public string P8 { get; set; }
        public string P9 { get; set; }
        public string P10 { get; set; }
        public bool Lang1Enb { get; set; }
        public bool Lang2Enb { get; set; }
        public string FirstLanguage { get; set; }
        public string SecondLanguage { get; set; }
        public bool English { get; set; }
        public bool Hindi { get; set; }
        public string EngWaveFile { get; set; }
        public string HindiWaveFile { get; set; }
        public bool IsCurrent { get; set; }
    }
} 