# PowerShell script to fix backtick issues in C# files

# Get all .cs files in the current directory and subdirectories
$csFiles = Get-ChildItem -Path "." -Filter "*.cs" -Recurse

Write-Host "Found $($csFiles.Count) C# files to process..."

foreach ($file in $csFiles) {
    Write-Host "Processing: $($file.Name)"
    
    # Read the file content as lines
    $lines = Get-Content $file.FullName
    
    # Skip if file is empty
    if ($lines -eq $null -or $lines.Count -eq 0) {
        continue
    }
    
    $modified = $false
    
    # Fix backtick characters in namespace declarations
    for ($i = 0; $i -lt $lines.Count; $i++) {
        if ($lines[$i] -match 'namespace ipis`r`n\{') {
            $lines[$i] = $lines[$i] -replace 'namespace ipis`r`n\{', 'namespace ipis'
            if ($i + 1 -lt $lines.Count -and $lines[$i + 1] -notmatch '^\s*\{') {
                # Insert opening brace on next line if not already there
                $lines = $lines[0..$i] + '{' + $lines[($i+1)..($lines.Count-1)]
            }
            $modified = $true
            Write-Host "  - Fixed backtick characters in namespace"
        }
    }
    
    # Save the file if modified
    if ($modified) {
        Set-Content -Path $file.FullName -Value $lines
        Write-Host "  - File updated"
    }
}

Write-Host "Processing complete!"
