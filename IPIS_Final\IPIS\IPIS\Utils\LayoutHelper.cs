using System.Drawing;
using System.Windows.Forms;

namespace IPIS.Utils
{
    public static class LayoutHelper
    {
        /// <summary>
        /// Creates a standard form layout with header, content, and footer sections
        /// </summary>
        public static TableLayoutPanel CreateStandardFormLayout(int contentRows = 1)
        {
            TableLayoutPanel mainLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 3 + contentRows, // Header + Content Rows + Footer + Status
                Padding = new Padding(16),
                CellBorderStyle = TableLayoutPanelCellBorderStyle.None,
            };

            // Header row
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 60F));
            
            // Content rows
            for (int i = 0; i < contentRows; i++)
            {
                mainLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 100F / contentRows));
            }
            
            // Footer row (buttons)
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 80F));
            
            // Status row
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 40F));

            return mainLayout;
        }

        /// <summary>
        /// Creates a card-style panel with consistent styling
        /// </summary>
        public static Panel CreateCardPanel(string title = null)
        {
            Panel cardPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White
            };
            UIStyler.ApplyPanelStyle(cardPanel, "card");

            if (!string.IsNullOrEmpty(title))
            {
                Label titleLabel = new Label { Text = title };
                UIStyler.ApplyLabelStyle(titleLabel, "h4");
                titleLabel.Margin = new Padding(0, 0, 0, 8);
                cardPanel.Controls.Add(titleLabel);
            }

            return cardPanel;
        }

        /// <summary>
        /// Creates a form section with a GroupBox
        /// </summary>
        public static GroupBox CreateFormSection(string title)
        {
            GroupBox groupBox = new GroupBox { Text = title, Dock = DockStyle.Fill };
            UIStyler.ApplyGroupBoxStyle(groupBox);
            return groupBox;
        }

        /// <summary>
        /// Creates a standard button panel with common actions
        /// </summary>
        public static FlowLayoutPanel CreateButtonPanel(params (string text, string style, EventHandler click)[] buttons)
        {
            FlowLayoutPanel buttonPanel = new FlowLayoutPanel
            {
                Dock = DockStyle.Fill,
                FlowDirection = FlowDirection.LeftToRight,
                Padding = new Padding(8),
                Margin = new Padding(0)
            };

            foreach (var (text, style, click) in buttons)
            {
                Button button = new Button { Text = text };
                ButtonStyler.ApplyStandardStyle(button, style, "medium");
                button.Click += click;
                buttonPanel.Controls.Add(button);
            }

            return buttonPanel;
        }

        /// <summary>
        /// Creates a standard form header with title and subtitle
        /// </summary>
        public static Panel CreateFormHeader(string title, string subtitle = null)
        {
            Panel headerPanel = CreateCardPanel();

            TableLayoutPanel headerLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = subtitle != null ? 2 : 1,
                Padding = new Padding(12)
            };

            Label titleLabel = new Label { Text = title };
            UIStyler.ApplyLabelStyle(titleLabel, "h3");

            headerLayout.Controls.Add(titleLabel, 0, 0);

            if (subtitle != null)
            {
                Label subtitleLabel = new Label { Text = subtitle };
                UIStyler.ApplyLabelStyle(subtitleLabel, "small");
                headerLayout.Controls.Add(subtitleLabel, 0, 1);
            }

            headerPanel.Controls.Add(headerLayout);
            return headerPanel;
        }

        /// <summary>
        /// Creates a two-column form layout for labels and controls
        /// </summary>
        public static TableLayoutPanel CreateTwoColumnLayout(int rows)
        {
            TableLayoutPanel layout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = rows,
                CellBorderStyle = TableLayoutPanelCellBorderStyle.None,
                Padding = new Padding(0)
            };

            layout.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 140F));
            layout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));

            return layout;
        }

        /// <summary>
        /// Creates a four-column form layout for complex forms
        /// </summary>
        public static TableLayoutPanel CreateFourColumnLayout(int rows)
        {
            TableLayoutPanel layout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 4,
                RowCount = rows,
                CellBorderStyle = TableLayoutPanelCellBorderStyle.None,
                Padding = new Padding(12)
            };

            layout.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 140F));
            layout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));
            layout.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 140F));
            layout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));

            return layout;
        }

        /// <summary>
        /// Creates a status bar with consistent styling
        /// </summary>
        public static Label CreateStatusBar()
        {
            Label statusLabel = new Label 
            { 
                Text = "Ready", 
                TextAlign = ContentAlignment.MiddleLeft,
                Dock = DockStyle.Fill
            };
            UIStyler.ApplyLabelStyle(statusLabel, "small");
            return statusLabel;
        }

        /// <summary>
        /// Creates a time input panel with hour and minute controls
        /// </summary>
        public static Panel CreateTimeInputPanel(out NumericUpDown hour, out NumericUpDown minute)
        {
            Panel timePanel = new Panel { AutoSize = true };
            FlowLayoutPanel flowPanel = new FlowLayoutPanel
            {
                FlowDirection = FlowDirection.LeftToRight,
                AutoSize = true,
                Margin = new Padding(0)
            };

            hour = new NumericUpDown { Minimum = 0, Maximum = 23, Width = 30 };
            minute = new NumericUpDown { Minimum = 0, Maximum = 59, Width = 30 };
            
            UIStyler.ApplyNumericUpDownStyle(hour, "small");
            UIStyler.ApplyNumericUpDownStyle(minute, "small");

            Label colonLabel = new Label { Text = ":" };
            UIStyler.ApplyLabelStyle(colonLabel, "body");
            colonLabel.Margin = new Padding(4, 6, 4, 0);

            flowPanel.Controls.Add(hour);
            flowPanel.Controls.Add(colonLabel);
            flowPanel.Controls.Add(minute);

            timePanel.Controls.Add(flowPanel);
            return timePanel;
        }

        /// <summary>
        /// Creates a label-control pair with consistent styling
        /// </summary>
        public static void AddLabelControlPair(TableLayoutPanel layout, string labelText, Control control, int row)
        {
            Label label = new Label { Text = labelText };
            UIStyler.ApplyLabelStyle(label, "body");
            
            if (control is TextBox textBox)
                UIStyler.ApplyTextBoxStyle(textBox, "medium");
            else if (control is ComboBox comboBox)
                UIStyler.ApplyComboBoxStyle(comboBox, "medium");
            else if (control is NumericUpDown numericUpDown)
                UIStyler.ApplyNumericUpDownStyle(numericUpDown, "medium");

            layout.Controls.Add(label, 0, row);
            layout.Controls.Add(control, 1, row);
        }

        /// <summary>
        /// Creates a checkbox grid for days of the week
        /// </summary>
        public static TableLayoutPanel CreateDaysCheckBoxGrid(out CheckBox[] checkBoxes)
        {
            TableLayoutPanel layout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 3,
                Padding = new Padding(0)
            };

            string[] dayNames = { "All Days", "Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday" };
            checkBoxes = new CheckBox[dayNames.Length];

            for (int i = 0; i < dayNames.Length; i++)
            {
                checkBoxes[i] = new CheckBox { Text = dayNames[i] };
                UIStyler.ApplyCheckBoxStyle(checkBoxes[i]);
                layout.Controls.Add(checkBoxes[i], i % 4, i / 4);
            }

            return layout;
        }

        /// <summary>
        /// Creates a responsive grid layout that adapts to form size
        /// </summary>
        public static TableLayoutPanel CreateResponsiveGrid(int columns, int rows)
        {
            TableLayoutPanel layout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = columns,
                RowCount = rows,
                CellBorderStyle = TableLayoutPanelCellBorderStyle.None,
                Padding = new Padding(8)
            };

            // Add flexible column styles
            for (int i = 0; i < columns; i++)
            {
                layout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F / columns));
            }

            // Add flexible row styles
            for (int i = 0; i < rows; i++)
            {
                layout.RowStyles.Add(new RowStyle(SizeType.Percent, 100F / rows));
            }

            return layout;
        }

        /// <summary>
        /// Creates a single-column form layout for vertically stacked controls
        /// </summary>
        public static TableLayoutPanel CreateSingleColumnLayout(int rows)
        {
            TableLayoutPanel layout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = rows,
                CellBorderStyle = TableLayoutPanelCellBorderStyle.None,
                Padding = new Padding(0)
            };
            layout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            return layout;
        }

        /// <summary>
        /// Creates a checkbox grid for platforms with an "All" option
        /// </summary>
        public static TableLayoutPanel CreatePlatformCheckBoxGrid(out CheckBox chkAllPlatforms, out CheckBox[] platformCheckBoxes, string[] platforms)
        {
            int columnCount = 5;
            int rowCount = (platforms.Length + columnCount - 1) / columnCount + 1; // +1 for "All" checkbox

            TableLayoutPanel layout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = columnCount,
                RowCount = rowCount,
                // Padding = new Padding(12),
                AutoSize = true,
                AutoSizeMode = AutoSizeMode.GrowAndShrink
            };

            // Clear and set column styles
            layout.ColumnStyles.Clear();
            for (int i = 0; i < columnCount; i++)
                layout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F / columnCount));

            // "All" checkbox
            chkAllPlatforms = new CheckBox { Text = "All Platforms" };
            UIStyler.ApplyCheckBoxStyle(chkAllPlatforms);
            layout.Controls.Add(chkAllPlatforms, 0, 0);

            // Platform checkboxes
            platformCheckBoxes = new CheckBox[platforms.Length];
            for (int i = 0; i < platforms.Length; i++)
            {
                platformCheckBoxes[i] = new CheckBox { Text = platforms[i] };
                UIStyler.ApplyCheckBoxStyle(platformCheckBoxes[i]);
                layout.Controls.Add(platformCheckBoxes[i], i % columnCount, (i / columnCount) + 1); // +1 for row after "All"
            }

            return layout;
        }
    }
} 
