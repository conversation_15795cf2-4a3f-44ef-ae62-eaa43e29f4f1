using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Media;
using System.Threading.Tasks;
using System.Windows.Forms;
using IPIS.Models;
using IPIS.Repositories;

namespace IPIS.Forms.Settings.Announcement
{
    public partial class SequenceItemManagementForm : Form
    {
        private readonly int _sequenceId;
        private readonly string _sequenceName;
        private readonly SQLiteSequenceItemRepository _itemRepository;
        private readonly SQLiteAnnouncementSequenceRepository _sequenceRepository;

        // Controls
        private DataGridView dgvItems;
        private Button btnAddAudioFile;
        private Button btnAddPlaceholder;
        private Button btnMoveUp;
        private Button btnMoveDown;
        private Button btnDeleteItem;
        private Button btnPlayItem;
        private Button btnPlayAll;
        private Button btnStop;
        private Button btnSave;
        private Button btnCancel;
        private StatusStrip statusStrip;
        private ToolStripStatusLabel statusLabel;

        // Test data controls
        private GroupBox grpTestData;
        private TextBox txtTrainNumber;
        private TextBox txtTrainName;
        private TextBox txtFromStation;
        private TextBox txtToStation;
        private TextBox txtPlatformNumber;
        private TextBox txtExpectedTime;

        private List<SequenceItem> _items;
        private SoundPlayer _soundPlayer;

        // Audio queue management - similar to AnnouncementBoardForm
        private List<string> audioQueue;
        private int currentAudioIndex = 0;
        private bool isPlaying = false;
        private System.Windows.Forms.Timer audioTimer;

        public SequenceItemManagementForm(int sequenceId, string sequenceName)
        {
            _sequenceId = sequenceId;
            _sequenceName = sequenceName;
            _itemRepository = new SQLiteSequenceItemRepository();
            _sequenceRepository = new SQLiteAnnouncementSequenceRepository();
            _items = new List<SequenceItem>();
            _soundPlayer = new SoundPlayer();

            // Initialize audio queue management
            audioQueue = new List<string>();
            InitializeAudioTimer();

            InitializeComponent();
            LoadItemsAsync();
        }

        private void InitializeComponent()
        {
            // Form
            this.ClientSize = new Size(1000, 800);
            this.Name = "SequenceItemManagementForm";
            this.Text = $"Sequence Items - {_sequenceName}";
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            // Title Label
            var lblTitle = new Label
            {
                AutoSize = true,
                Location = new Point(20, 20),
                Text = $"Sequence: {_sequenceName}",
                Font = new Font("Segoe UI", 14F, FontStyle.Bold),
                ForeColor = Color.FromArgb(64, 64, 64)
            };

            // DataGridView
            this.dgvItems = new DataGridView
            {
                Location = new Point(20, 60),
                Size = new Size(600, 650),
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = true,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D,
                GridColor = Color.LightGray,
                RowHeadersVisible = false,
                Font = new Font("Segoe UI", 9F, FontStyle.Regular)
            };

            // Configure DataGridView columns
            this.dgvItems.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn { Name = "Id", HeaderText = "ID", Width = 50, Visible = false },
                new DataGridViewTextBoxColumn { Name = "OrderIndex", HeaderText = "Order", Width = 60 },
                new DataGridViewTextBoxColumn { Name = "Type", HeaderText = "Type", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "Content", HeaderText = "Content", Width = 300 },
                new DataGridViewTextBoxColumn { Name = "Description", HeaderText = "Description", Width = 200 },
            });

            // Buttons Panel
            var pnlButtons = new Panel
            {
                Location = new Point(640, 60),
                Size = new Size(320, 700),
                BorderStyle = BorderStyle.FixedSingle
            };

            // Add Audio File Button
            this.btnAddAudioFile = new Button
            {
                Location = new Point(20, 20),
                Size = new Size(140, 35),
                Text = "Add Audio File",
                Font = new Font("Segoe UI", 9F, FontStyle.Regular),
                BackColor = Color.FromArgb(0, 122, 204),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            this.btnAddAudioFile.Click += new EventHandler(this.btnAddAudioFile_Click);

            // Add Placeholder Button
            this.btnAddPlaceholder = new Button
            {
                Location = new Point(170, 20),
                Size = new Size(130, 35),
                Text = "Add Placeholder",
                Font = new Font("Segoe UI", 9F, FontStyle.Regular),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            this.btnAddPlaceholder.Click += new EventHandler(this.btnAddPlaceholder_Click);

            // Move Up Button
            this.btnMoveUp = new Button
            {
                Location = new Point(20, 70),
                Size = new Size(140, 35),
                Text = "Move Up",
                Font = new Font("Segoe UI", 9F, FontStyle.Regular),
                BackColor = Color.FromArgb(255, 193, 7),
                ForeColor = Color.Black,
                FlatStyle = FlatStyle.Flat
            };
            this.btnMoveUp.Click += new EventHandler(this.btnMoveUp_Click);

            // Move Down Button
            this.btnMoveDown = new Button
            {
                Location = new Point(170, 70),
                Size = new Size(130, 35),
                Text = "Move Down",
                Font = new Font("Segoe UI", 9F, FontStyle.Regular),
                BackColor = Color.FromArgb(255, 193, 7),
                ForeColor = Color.Black,
                FlatStyle = FlatStyle.Flat
            };
            this.btnMoveDown.Click += new EventHandler(this.btnMoveDown_Click);

            // Delete Item Button
            this.btnDeleteItem = new Button
            {
                Location = new Point(20, 120),
                Size = new Size(140, 35),
                Text = "Delete Item",
                Font = new Font("Segoe UI", 9F, FontStyle.Regular),
                BackColor = Color.FromArgb(220, 53, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            this.btnDeleteItem.Click += new EventHandler(this.btnDeleteItem_Click);

            // Play Item Button
            this.btnPlayItem = new Button
            {
                Location = new Point(170, 120),
                Size = new Size(130, 35),
                Text = "Play Item",
                Font = new Font("Segoe UI", 9F, FontStyle.Regular),
                BackColor = Color.FromArgb(23, 162, 184),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            this.btnPlayItem.Click += new EventHandler(this.btnPlayItem_Click);

            // Play All Button
            this.btnPlayAll = new Button
            {
                Location = new Point(20, 170),
                Size = new Size(280, 40),
                Text = "Play Complete Sequence",
                Font = new Font("Segoe UI", 10F, FontStyle.Bold),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            this.btnPlayAll.Click += new EventHandler(this.btnPlayAll_Click);

            // Stop Button
            this.btnStop = new Button
            {
                Location = new Point(20, 220),
                Size = new Size(280, 35),
                Text = "Stop Playback",
                Font = new Font("Segoe UI", 9F, FontStyle.Regular),
                BackColor = Color.FromArgb(220, 53, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            this.btnStop.Click += new EventHandler(this.btnStop_Click);

            // Test Data GroupBox
            this.grpTestData = new GroupBox
            {
                Location = new Point(20, 270),
                Size = new Size(280, 150),
                Text = "Test Data",
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };

            // Test Data Labels and TextBoxes
            var lblTrainNumber = new Label
            {
                AutoSize = true,
                Location = new Point(10, 25),
                Text = "Train Number:",
                Font = new Font("Segoe UI", 8F, FontStyle.Regular)
            };

            this.txtTrainNumber = new TextBox
            {
                Location = new Point(120, 22),
                Size = new Size(150, 20),
                Text = "01115",
                Font = new Font("Segoe UI", 8F, FontStyle.Regular)
            };

            var lblTrainName = new Label
            {
                AutoSize = true,
                Location = new Point(10, 50),
                Text = "Train Name:",
                Font = new Font("Segoe UI", 8F, FontStyle.Regular)
            };

            this.txtTrainName = new TextBox
            {
                Location = new Point(120, 47),
                Size = new Size(150, 20),
                Text = "01115",
                Font = new Font("Segoe UI", 8F, FontStyle.Regular)
            };

            var lblFromStation = new Label
            {
                AutoSize = true,
                Location = new Point(10, 75),
                Text = "From Station:",
                Font = new Font("Segoe UI", 8F, FontStyle.Regular)
            };

            this.txtFromStation = new TextBox
            {
                Location = new Point(120, 72),
                Size = new Size(150, 20),
                Text = "ROK",
                Font = new Font("Segoe UI", 8F, FontStyle.Regular)
            };

            var lblToStation = new Label
            {
                AutoSize = true,
                Location = new Point(10, 100),
                Text = "To Station:",
                Font = new Font("Segoe UI", 8F, FontStyle.Regular)
            };

            this.txtToStation = new TextBox
            {
                Location = new Point(120, 97),
                Size = new Size(150, 20),
                Text = "RE",
                Font = new Font("Segoe UI", 8F, FontStyle.Regular)
            };

            var lblPlatformNumber = new Label
            {
                AutoSize = true,
                Location = new Point(10, 125),
                Text = "Platform:",
                Font = new Font("Segoe UI", 8F, FontStyle.Regular)
            };

            this.txtPlatformNumber = new TextBox
            {
                Location = new Point(120, 122),
                Size = new Size(150, 20),
                Text = "5",
                Font = new Font("Segoe UI", 8F, FontStyle.Regular)
            };

            // Add test data controls to group box
            this.grpTestData.Controls.AddRange(new Control[]
            {
                lblTrainNumber, this.txtTrainNumber,
                lblTrainName, this.txtTrainName,
                lblFromStation, this.txtFromStation,
                lblToStation, this.txtToStation,
                lblPlatformNumber, this.txtPlatformNumber
            });

            // Save and Cancel Buttons
            this.btnSave = new Button
            {
                Location = new Point(20, 440),
                Size = new Size(130, 35),
                Text = "Save Changes",
                Font = new Font("Segoe UI", 9F, FontStyle.Regular),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            this.btnSave.Click += new EventHandler(this.btnSave_Click);

            this.btnCancel = new Button
            {
                Location = new Point(170, 440),
                Size = new Size(130, 35),
                Text = "Cancel",
                Font = new Font("Segoe UI", 9F, FontStyle.Regular),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            this.btnCancel.Click += new EventHandler(this.btnCancel_Click);

            // Add buttons to panel
            pnlButtons.Controls.AddRange(new Control[]
            {
                this.btnAddAudioFile,
                this.btnAddPlaceholder,
                this.btnMoveUp,
                this.btnMoveDown,
                this.btnDeleteItem,
                this.btnPlayItem,
                this.btnPlayAll,
                this.btnStop,
                this.grpTestData,
                this.btnSave,
                this.btnCancel
            });

            // StatusStrip
            this.statusStrip = new StatusStrip();
            this.statusLabel = new ToolStripStatusLabel();
            this.statusStrip.Items.AddRange(new ToolStripItem[] { this.statusLabel });
            this.statusStrip.Location = new Point(0, 778);
            this.statusStrip.Size = new Size(1000, 22);
            this.statusLabel.Text = "Ready";

            // Add controls to form
            this.Controls.AddRange(new Control[]
            {
                lblTitle,
                this.dgvItems,
                pnlButtons,
                this.statusStrip
            });

            // Wire up events
            this.dgvItems.SelectionChanged += new EventHandler(this.dgvItems_SelectionChanged);
            this.FormClosing += new FormClosingEventHandler(this.SequenceItemManagementForm_FormClosing);
        }

        private async void LoadItemsAsync()
        {
            try
            {
                this.statusLabel.Text = "Loading items...";
                this.Cursor = Cursors.WaitCursor;

                var items = await _itemRepository.GetItemsBySequenceOrderedAsync(_sequenceId);
                _items = new List<SequenceItem>(items);

                RefreshDataGridView();
                this.statusLabel.Text = $"Loaded {_items.Count} items";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading items: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                this.statusLabel.Text = "Error loading items";
            }
            finally
            {
                this.Cursor = Cursors.Default;
            }
        }

        private void RefreshDataGridView()
        {
            this.dgvItems.Rows.Clear();

            foreach (var item in _items)
            {
                var row = new DataGridViewRow();
                row.Cells.AddRange(new DataGridViewCell[]
                {
                    new DataGridViewTextBoxCell { Value = item.Id },
                    new DataGridViewTextBoxCell { Value = item.OrderIndex },
                    new DataGridViewTextBoxCell { Value = item.Type.ToString() },
                    new DataGridViewTextBoxCell { Value = item.Content },
                    new DataGridViewTextBoxCell { Value = item.Description }
                });
                this.dgvItems.Rows.Add(row);
            }
        }

        private string GetTestValueForPlaceholder(string placeholder)
        {
            switch (placeholder)
            {
                case "TRAIN_NUMBER":
                    return this.txtTrainNumber.Text;
                case "TRAIN_NAME":
                    return this.txtTrainName.Text;
                case "FROM_STATION":
                    return this.txtFromStation.Text;
                case "TO_STATION":
                    return this.txtToStation.Text;
                case "PLATFORM_NUMBER":
                    return this.txtPlatformNumber.Text;
                case "UPDATED_PLATFORM_NUMBER":
                    return "2"; // Default test value for updated platform
                case "DIVERTED_STATION":
                    return "JIND"; // Default test value for diverted station
                default:
                    return "Test Value";
            }
        }

        private void btnAddAudioFile_Click(object sender, EventArgs e)
        {
            using (var openFileDialog = new OpenFileDialog())
            {
                openFileDialog.Filter = "Audio files (*.wav)|*.wav|All files (*.*)|*.*";
                openFileDialog.Title = "Select Audio File";
                openFileDialog.InitialDirectory = Path.Combine(Application.StartupPath, "data", "WAVE");

                if (openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    var relativePath = GetRelativePath(openFileDialog.FileName);
                    var fileName = Path.GetFileNameWithoutExtension(openFileDialog.FileName);

                    var newItem = new SequenceItem
                    {
                        SequenceId = _sequenceId,
                        OrderIndex = _items.Count + 1,
                        Type = ItemType.AudioFile,
                        Content = relativePath,
                        Description = $"Audio: {fileName}"
                    };

                    _items.Add(newItem);
                    RefreshDataGridView();
                    this.statusLabel.Text = $"Added audio file: {fileName}";
                }
            }
        }

        private void btnAddPlaceholder_Click(object sender, EventArgs e)
        {
            var placeholders = new string[]
            {
                "TRAIN_NUMBER",
                "TRAIN_NAME",
                "FROM_STATION",
                "TO_STATION",
                "PLATFORM_NUMBER",
                "UPDATED_PLATFORM_NUMBER",
                "DIVERTED_STATION",
                "EXPECTED_TIME",
                "DELAY_TIME",
                "VIA_STATION_1",
                "VIA_STATION_2",
                "VIA_STATION_3",
                "VIA_STATION_4"
            };

            using (var placeholderForm = new PlaceholderSelectionForm(placeholders))
            {
                if (placeholderForm.ShowDialog() == DialogResult.OK)
                {
                    var placeholder = placeholderForm.SelectedPlaceholder;
                    var description = GetPlaceholderDescription(placeholder);

                    var newItem = new SequenceItem
                    {
                        SequenceId = _sequenceId,
                        OrderIndex = _items.Count + 1,
                        Type = ItemType.Placeholder,
                        Content = placeholder,
                        Description = description
                    };

                    _items.Add(newItem);
                    RefreshDataGridView();
                    this.statusLabel.Text = $"Added placeholder: {placeholder}";
                }
            }
        }

        private string GetPlaceholderDescription(string placeholder)
        {
            switch (placeholder)
            {
                case "TRAIN_NUMBER":
                    return "Train Number (e.g., 12345)";
                case "TRAIN_NAME":
                    return "Train Name (e.g., Rajdhani Express)";
                case "FROM_STATION":
                    return "Source Station Name";
                case "TO_STATION":
                    return "Destination Station Name";
                case "PLATFORM_NUMBER":
                    return "Platform Number";
                case "UPDATED_PLATFORM_NUMBER":
                    return "Updated Platform Number";
                case "DIVERTED_STATION":
                    return "Diverted Station";
                case "EXPECTED_TIME":
                    return "Expected Arrival/Departure Time";
                case "DELAY_TIME":
                    return "Delay Time";
                case "VIA_STATION_1":
                    return "Via Station 1";
                case "VIA_STATION_2":
                    return "Via Station 2";
                case "VIA_STATION_3":
                    return "Via Station 3";
                case "VIA_STATION_4":
                    return "Via Station 4";
                default:
                    return "Unknown Placeholder";
            }
        }

        private void btnMoveUp_Click(object sender, EventArgs e)
        {
            if (this.dgvItems.SelectedRows.Count > 0)
            {
                var selectedIndex = this.dgvItems.SelectedRows[0].Index;
                if (selectedIndex > 0)
                {
                    var item = _items[selectedIndex];
                    _items.RemoveAt(selectedIndex);
                    _items.Insert(selectedIndex - 1, item);

                    // Update order indices
                    for (int i = 0; i < _items.Count; i++)
                    {
                        _items[i].OrderIndex = i + 1;
                    }

                    RefreshDataGridView();
                    this.dgvItems.Rows[selectedIndex - 1].Selected = true;
                    this.statusLabel.Text = "Item moved up";
                }
            }
        }

        private void btnMoveDown_Click(object sender, EventArgs e)
        {
            if (this.dgvItems.SelectedRows.Count > 0)
            {
                var selectedIndex = this.dgvItems.SelectedRows[0].Index;
                if (selectedIndex < _items.Count - 1)
                {
                    var item = _items[selectedIndex];
                    _items.RemoveAt(selectedIndex);
                    _items.Insert(selectedIndex + 1, item);

                    // Update order indices
                    for (int i = 0; i < _items.Count; i++)
                    {
                        _items[i].OrderIndex = i + 1;
                    }

                    RefreshDataGridView();
                    this.dgvItems.Rows[selectedIndex + 1].Selected = true;
                    this.statusLabel.Text = "Item moved down";
                }
            }
        }

        private async void btnDeleteItem_Click(object sender, EventArgs e)
        {
            if (this.dgvItems.SelectedRows.Count > 0)
            {
                var selectedIndex = this.dgvItems.SelectedRows[0].Index;
                var item = _items[selectedIndex];

                var result = MessageBox.Show($"Are you sure you want to delete this item?\n\nType: {item.Type}\nContent: {item.Content}", 
                    "Confirm Delete", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    try
                    {
                        if (item.Id > 0)
                        {
                            await _itemRepository.DeleteItemAsync(item.Id);
                        }

                        _items.RemoveAt(selectedIndex);

                        // Update order indices
                        for (int i = 0; i < _items.Count; i++)
                        {
                            _items[i].OrderIndex = i + 1;
                        }

                        RefreshDataGridView();
                        this.statusLabel.Text = "Item deleted";
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Error deleting item: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private async void btnPlayItem_Click(object sender, EventArgs e)
        {
            if (this.dgvItems.SelectedRows.Count > 0)
            {
                var selectedIndex = this.dgvItems.SelectedRows[0].Index;
                var item = _items[selectedIndex];

                // Stop any current playback
                StopAudioPlayback();

                // Build queue for single item
                audioQueue.Clear();
                if (item.Type == ItemType.AudioFile)
                {
                    string relativePath = item.Content;
                    
                    // If the audioPath contains a complete path with "WAVE" folder, extract only the portion after WAVE
                    var waveIndex = item.Content.IndexOf("WAVE");
                    if (waveIndex >= 0)
                    {
                        relativePath = item.Content.Substring(waveIndex + 5);
                        relativePath = relativePath.TrimStart('\\', '/');
                    }
                    
                    var fullPath = Path.Combine(Application.StartupPath, "data", "WAVE", relativePath);
                    if (File.Exists(fullPath))
                    {
                        audioQueue.Add(fullPath);
                        StartAudioPlayback();
                    }
                    else
                    {
                        MessageBox.Show($"Audio file not found: {fullPath}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                else if (item.Type == ItemType.Placeholder)
                {
                    var testValue = GetTestValueForPlaceholder(item.Content);
                    var audioPaths = GetAudioPathsForPlaceholder(item.Content, testValue);
                    
                    foreach (var audioPath in audioPaths)
                    {
                        if (!string.IsNullOrEmpty(audioPath))
                        {
                            string relativePath = audioPath;
                            
                            var waveIndex = audioPath.IndexOf("WAVE");
                            if (waveIndex >= 0)
                            {
                                relativePath = audioPath.Substring(waveIndex + 5);
                                relativePath = relativePath.TrimStart('\\', '/');
                            }
                            
                            var fullPath = Path.Combine(Application.StartupPath, "data", "WAVE", relativePath);
                            if (File.Exists(fullPath))
                            {
                                audioQueue.Add(fullPath);
                            }
                        }
                    }
                    
                    if (audioQueue.Count > 0)
                    {
                        StartAudioPlayback();
                    }
                    else
                    {
                        this.statusLabel.Text = $"No audio found for placeholder: {item.Content}";
                    }
                }
            }
        }

        private async void btnPlayAll_Click(object sender, EventArgs e)
        {
            // Stop any current playback
            StopAudioPlayback();
            
            // Build complete audio queue
            await BuildAudioQueueAsync();
            
            // Start playback
            StartAudioPlayback();
        }

        private void btnStop_Click(object sender, EventArgs e)
        {
            StopAudioPlayback();
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            SaveSequenceAsync();
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void dgvItems_SelectionChanged(object sender, EventArgs e)
        {
            var hasSelection = this.dgvItems.SelectedRows.Count > 0;
            this.btnMoveUp.Enabled = hasSelection;
            this.btnMoveDown.Enabled = hasSelection;
            this.btnDeleteItem.Enabled = hasSelection;
            this.btnPlayItem.Enabled = hasSelection;
        }

        private void SequenceItemManagementForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            // Stop any ongoing playback
            StopAudioPlayback();
            
            // Dispose of timer
            if (audioTimer != null)
            {
                audioTimer.Stop();
                audioTimer.Dispose();
            }
            
            if (_soundPlayer != null)
            {
                _soundPlayer.Dispose();
            }
        }

        private string GetRelativePath(string fullPath)
        {
            var basePath = Path.Combine(Application.StartupPath, "data", "WAVE");
            if (fullPath.StartsWith(basePath))
            {
                return fullPath.Substring(basePath.Length + 1); // +1 for the directory separator
            }
            
            // Handle case where the path contains "WAVE" folder somewhere in the middle
            var waveIndex = fullPath.IndexOf("WAVE");
            if (waveIndex >= 0)
            {
                var afterWave = fullPath.Substring(waveIndex + 5); // "WAVE" is 4 characters + 1 for separator
                return afterWave.TrimStart('\\', '/'); // Remove leading separators
            }
            
            return fullPath;
        }

        private async Task BuildAudioQueueAsync()
        {
            audioQueue.Clear();
            LogDebug("Building audio queue for sequence");

            foreach (var item in _items)
            {
                if (item.Type == ItemType.AudioFile)
                {
                    // Add audio file directly
                    string relativePath = item.Content;
                    
                    // If the audioPath contains a complete path with "WAVE" folder, extract only the portion after WAVE
                    var waveIndex = item.Content.IndexOf("WAVE");
                    if (waveIndex >= 0)
                    {
                        relativePath = item.Content.Substring(waveIndex + 5); // "WAVE" is 4 characters + 1 for separator
                        relativePath = relativePath.TrimStart('\\', '/'); // Remove leading separators
                    }
                    
                    var fullPath = Path.Combine(Application.StartupPath, "data", "WAVE", relativePath);
                    if (File.Exists(fullPath))
                    {
                        audioQueue.Add(fullPath);
                        LogDebug($"Added audio file: {relativePath}");
                    }
                    else
                    {
                        LogDebug($"Audio file not found: {fullPath}");
                    }
                }
                else if (item.Type == ItemType.Placeholder)
                {
                    // Process placeholder and add audio files
                    var testValue = GetTestValueForPlaceholder(item.Content);
                    var audioPaths = GetAudioPathsForPlaceholder(item.Content, testValue);
                    
                    foreach (var audioPath in audioPaths)
                    {
                        if (!string.IsNullOrEmpty(audioPath))
                        {
                            string relativePath = audioPath;
                            
                            // If the audioPath contains a complete path with "WAVE" folder, extract only the portion after WAVE
                            var waveIndex = audioPath.IndexOf("WAVE");
                            if (waveIndex >= 0)
                            {
                                relativePath = audioPath.Substring(waveIndex + 5); // "WAVE" is 4 characters + 1 for separator
                                relativePath = relativePath.TrimStart('\\', '/'); // Remove leading separators
                            }
                            
                            var fullPath = Path.Combine(Application.StartupPath, "data", "WAVE", relativePath);
                            if (File.Exists(fullPath))
                            {
                                audioQueue.Add(fullPath);
                                LogDebug($"Added placeholder audio: {relativePath}");
                            }
                            else
                            {
                                LogDebug($"Placeholder audio file not found: {fullPath}");
                            }
                        }
                    }
                }
            }

            LogDebug($"Audio queue built with {audioQueue.Count} files");
        }

        private void StartAudioPlayback()
        {
            if (audioQueue.Count == 0)
            {
                this.statusLabel.Text = "No audio files in queue";
                return;
            }

            isPlaying = true;
            currentAudioIndex = 0;
            audioTimer.Start();
            PlayNextAudio();
        }

        private void StopAudioPlayback()
        {
            if (_soundPlayer != null)
            {
                _soundPlayer.Stop();
            }
            isPlaying = false;
            audioTimer.Stop();
            audioQueue.Clear();
            currentAudioIndex = 0;
            this.statusLabel.Text = "Audio playback stopped";
        }

        private void InitializeAudioTimer()
        {
            audioTimer = new System.Windows.Forms.Timer();
            audioTimer.Interval = 100; // Check every 100ms
            audioTimer.Tick += AudioTimer_Tick;
        }

        private void AudioTimer_Tick(object sender, EventArgs e)
        {
            if (_soundPlayer != null && !isPlaying && audioQueue.Count > 0)
            {
                PlayNextAudio();
            }
        }

        private void PlayNextAudio()
        {
            try
            {
                if (audioQueue == null || audioQueue.Count == 0 || currentAudioIndex >= audioQueue.Count)
                {
                    // Queue completed
                    isPlaying = false;
                    audioTimer.Stop();
                    this.statusLabel.Text = "Audio playback completed";
                    return;
                }

                string audioFile = audioQueue[currentAudioIndex];
                if (string.IsNullOrEmpty(audioFile) || !File.Exists(audioFile))
                {
                    LogDebug($"Audio file not found or empty: {audioFile}");
                    currentAudioIndex++;
                    PlayNextAudio();
                    return;
                }

                LogDebug($"Playing: {Path.GetFileName(audioFile)}");
                this.statusLabel.Text = $"Playing: {Path.GetFileName(audioFile)}";
                
                _soundPlayer.SoundLocation = audioFile;
                _soundPlayer.PlaySync(); // Use PlaySync to wait for completion
                
                // Move to next audio file
                currentAudioIndex++;
                if (currentAudioIndex < audioQueue.Count && isPlaying)
                {
                    // Small delay between audio files
                    System.Threading.Thread.Sleep(100);
                    PlayNextAudio();
                }
                else
                {
                    // Queue completed
                    isPlaying = false;
                    audioTimer.Stop();
                    this.statusLabel.Text = "Audio playback completed";
                }
            }
            catch (Exception ex)
            {
                LogDebug($"Error in PlayNextAudio: {ex.Message}");
                currentAudioIndex++;
                PlayNextAudio();
            }
        }

        private void LogDebug(string message)
        {
            System.Diagnostics.Debug.WriteLine($"[SequenceItemManagementForm] {message}");
        }

        private List<string> GetAudioPathsForPlaceholder(string placeholder, string value)
        {
            var audioPaths = new List<string>();
            
            switch (placeholder)
            {
                case "TRAIN_NUMBER":
                    // For train numbers, play each digit separately
                    audioPaths.AddRange(GetDigitAudioPaths(value));
                    break;
                case "TRAIN_NAME":
                    // Look for train name audio files
                    var trainNamePath = FindAudioFileForValue(value, "TRNAME");
                    if (!string.IsNullOrEmpty(trainNamePath))
                        audioPaths.Add(trainNamePath);
                    break;
                case "FROM_STATION":
                case "TO_STATION":
                case "VIA_STATION_1":
                case "VIA_STATION_2":
                case "VIA_STATION_3":
                case "VIA_STATION_4":
                    // Look for station name audio files
                    var stationPath = FindAudioFileForValue(value, "CITY");
                    if (!string.IsNullOrEmpty(stationPath))
                        audioPaths.Add(stationPath);
                    break;
                case "PLATFORM_NUMBER":
                    // Look for platform number audio files
                    var platformPath = FindAudioFileForValue(value, "PF");
                    if (!string.IsNullOrEmpty(platformPath))
                        audioPaths.Add(platformPath);
                    break;
                case "UPDATED_PLATFORM_NUMBER":
                    // Look for updated platform number audio files
                    var updatedPlatformPath = FindAudioFileForValue(value, "PF");
                    if (!string.IsNullOrEmpty(updatedPlatformPath))
                        audioPaths.Add(updatedPlatformPath);
                    break;
                case "DIVERTED_STATION":
                    // Look for diverted station audio files
                    var divertedStationPath = FindAudioFileForValue(value, "CITY");
                    if (!string.IsNullOrEmpty(divertedStationPath))
                        audioPaths.Add(divertedStationPath);
                    break;
                case "EXPECTED_TIME":
                    // Look for time audio files
                    var timePath = FindAudioFileForValue(value, "HOUR");
                    if (!string.IsNullOrEmpty(timePath))
                        audioPaths.Add(timePath);
                    break;
                case "DELAY_TIME":
                    // Look for delay time audio files
                    var delayPath = FindAudioFileForValue(value, "DELAY");
                    if (!string.IsNullOrEmpty(delayPath))
                        audioPaths.Add(delayPath);
                    break;
            }
            
            return audioPaths;
        }

        private List<string> GetDigitAudioPaths(string value)
        {
            var audioPaths = new List<string>();
            
            // Convert value to string and process each character
            var digits = value.ToString().ToCharArray();
            
            foreach (var digit in digits)
            {
                if (char.IsDigit(digit))
                {
                    var digitPath = FindAudioFileForValue(digit.ToString(), "TRNO");
                    if (!string.IsNullOrEmpty(digitPath))
                    {
                        audioPaths.Add(digitPath);
                    }
                }
            }
            
            return audioPaths;
        }

        private string FindAudioFileForValue(string value, string category)
        {
            try
            {
                var wavePath = Path.Combine(Application.StartupPath, "data", "WAVE");
                var searchPaths = new string[]
                {
                    Path.Combine(wavePath, "ENGLISH", category),
                    Path.Combine(wavePath, "HINDI", category)
                };

                foreach (var searchPath in searchPaths)
                {
                    if (Directory.Exists(searchPath))
                    {
                        // Look for exact match first
                        var exactMatch = Path.Combine(searchPath, $"{value}.wav");
                        if (File.Exists(exactMatch))
                        {
                            return GetRelativePath(exactMatch);
                        }

                        // Look for partial matches
                        var files = Directory.GetFiles(searchPath, "*.wav");
                        foreach (var file in files)
                        {
                            var fileName = Path.GetFileNameWithoutExtension(file);
                            if (fileName.Contains(value, StringComparison.OrdinalIgnoreCase))
                            {
                                return GetRelativePath(file);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Log error but don't throw
                System.Diagnostics.Debug.WriteLine($"Error finding audio file for {value} in {category}: {ex.Message}");
            }

            return null;
        }

        private async void SaveSequenceAsync()
        {
            try
            {
                this.statusLabel.Text = "Saving changes...";
                this.Cursor = Cursors.WaitCursor;

                // Save all items
                for (int i = 0; i < _items.Count; i++)
                {
                    var item = _items[i];
                    item.OrderIndex = i + 1;

                    if (item.Id > 0)
                    {
                        // Update existing item
                        await _itemRepository.UpdateItemAsync(item);
                    }
                    else
                    {
                        // Create new item
                        var newId = await _itemRepository.AddItemAsync(item);
                        item.Id = newId;
                    }
                }

                this.statusLabel.Text = "Changes saved successfully";
                MessageBox.Show("Sequence items saved successfully!", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving changes: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                this.statusLabel.Text = "Error saving changes";
            }
            finally
            {
                this.Cursor = Cursors.Default;
            }
        }
    }

    public class PlaceholderSelectionForm : Form
    {
        private ComboBox cboPlaceholders;
        private Button btnOK;
        private Button btnCancel;

        public string SelectedPlaceholder { get; private set; }

        public PlaceholderSelectionForm(string[] placeholders)
        {
            InitializeComponent(placeholders);
        }

        private void InitializeComponent(string[] placeholders)
        {
            // Form
            this.ClientSize = new Size(400, 150);
            this.Name = "PlaceholderSelectionForm";
            this.Text = "Select Placeholder";
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            // Title Label
            var lblTitle = new Label
            {
                AutoSize = true,
                Location = new Point(20, 20),
                Text = "Select a placeholder to add:",
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };

            // ComboBox
            this.cboPlaceholders = new ComboBox
            {
                Location = new Point(20, 50),
                Size = new Size(350, 25),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Segoe UI", 9F, FontStyle.Regular)
            };

            // Add placeholders to ComboBox
            foreach (var placeholder in placeholders)
            {
                this.cboPlaceholders.Items.Add(placeholder);
            }

            if (this.cboPlaceholders.Items.Count > 0)
            {
                this.cboPlaceholders.SelectedIndex = 0;
            }

            // Buttons
            this.btnOK = new Button
            {
                Location = new Point(200, 100),
                Size = new Size(75, 30),
                Text = "OK",
                DialogResult = DialogResult.OK,
                BackColor = Color.FromArgb(0, 122, 204),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            this.btnOK.Click += new EventHandler(this.btnOK_Click);

            this.btnCancel = new Button
            {
                Location = new Point(295, 100),
                Size = new Size(75, 30),
                Text = "Cancel",
                DialogResult = DialogResult.Cancel,
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            // Add controls to form
            this.Controls.AddRange(new Control[]
            {
                lblTitle,
                this.cboPlaceholders,
                this.btnOK,
                this.btnCancel
            });
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (this.cboPlaceholders.SelectedItem != null)
            {
                this.SelectedPlaceholder = this.cboPlaceholders.SelectedItem.ToString();
            }
        }
    }
} 