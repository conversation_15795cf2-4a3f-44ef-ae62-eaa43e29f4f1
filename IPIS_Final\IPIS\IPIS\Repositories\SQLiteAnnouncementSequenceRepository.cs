using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SQLite;
using System.Threading.Tasks;
using IPIS.Models;
using IPIS.Repositories.Interfaces;
using IPIS.Utils;

namespace IPIS.Repositories
{
    public class SQLiteAnnouncementSequenceRepository : IAnnouncementSequenceRepository
    {
        public async Task<IEnumerable<AnnouncementSequence>> GetAllSequencesAsync()
        {
            var sequences = new List<AnnouncementSequence>();
            var query = @"SELECT s.Id, s.TemplateId, s.LanguageId, s.Name, s.IsActive, s.CreatedAt, s.UpdatedAt,
                         t.Name as TemplateName, l.Name as LanguageName
                         FROM AnnouncementSequences s
                         INNER JOIN AnnouncementTemplates t ON s.TemplateId = t.Id
                         INNER JOIN Languages l ON s.LanguageId = l.Id
                         ORDER BY t.Name, l.Name";

            using (var connection = Database.GetConnection())
            {
                using (var command = new SQLiteCommand(query, connection))
                {
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            sequences.Add(new AnnouncementSequence
                            {
                                Id = reader.GetInt32("Id"),
                                TemplateId = reader.GetInt32("TemplateId"),
                                LanguageId = reader.GetInt32("LanguageId"),
                                Name = reader.GetString("Name"),
                                IsActive = reader.GetBoolean("IsActive"),
                                CreatedAt = reader.GetDateTime("CreatedAt"),
                                UpdatedAt = reader.IsDBNull("UpdatedAt") ? null : (DateTime?)reader.GetDateTime("UpdatedAt"),
                                Template = new AnnouncementTemplate { Id = reader.GetInt32("TemplateId"), Name = reader.GetString("TemplateName") },
                                Language = new Language { Id = reader.GetInt32("LanguageId"), Name = reader.GetString("LanguageName") }
                            });
                        }
                    }
                }
            }

            return sequences;
        }

        public async Task<IEnumerable<AnnouncementSequence>> GetSequencesByTemplateAsync(int templateId)
        {
            var sequences = new List<AnnouncementSequence>();
            var query = @"SELECT s.Id, s.TemplateId, s.LanguageId, s.Name, s.IsActive, s.CreatedAt, s.UpdatedAt,
                         t.Name as TemplateName, l.Name as LanguageName
                         FROM AnnouncementSequences s
                         INNER JOIN AnnouncementTemplates t ON s.TemplateId = t.Id
                         INNER JOIN Languages l ON s.LanguageId = l.Id
                         WHERE s.TemplateId = @TemplateId
                         ORDER BY l.Name";

            using (var connection = Database.GetConnection())
            {
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@TemplateId", templateId);

                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            sequences.Add(new AnnouncementSequence
                            {
                                Id = reader.GetInt32("Id"),
                                TemplateId = reader.GetInt32("TemplateId"),
                                LanguageId = reader.GetInt32("LanguageId"),
                                Name = reader.GetString("Name"),
                                IsActive = reader.GetBoolean("IsActive"),
                                CreatedAt = reader.GetDateTime("CreatedAt"),
                                UpdatedAt = reader.IsDBNull("UpdatedAt") ? null : (DateTime?)reader.GetDateTime("UpdatedAt"),
                                Template = new AnnouncementTemplate { Id = reader.GetInt32("TemplateId"), Name = reader.GetString("TemplateName") },
                                Language = new Language { Id = reader.GetInt32("LanguageId"), Name = reader.GetString("LanguageName") }
                            });
                        }
                    }
                }
            }

            return sequences;
        }

        public async Task<IEnumerable<AnnouncementSequence>> GetSequencesByLanguageAsync(int languageId)
        {
            var sequences = new List<AnnouncementSequence>();
            var query = @"SELECT s.Id, s.TemplateId, s.LanguageId, s.Name, s.IsActive, s.CreatedAt, s.UpdatedAt,
                         t.Name as TemplateName, l.Name as LanguageName
                         FROM AnnouncementSequences s
                         INNER JOIN AnnouncementTemplates t ON s.TemplateId = t.Id
                         INNER JOIN Languages l ON s.LanguageId = l.Id
                         WHERE s.LanguageId = @LanguageId
                         ORDER BY t.Name";

            using (var connection = Database.GetConnection())
            {
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@LanguageId", languageId);

                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            sequences.Add(new AnnouncementSequence
                            {
                                Id = reader.GetInt32("Id"),
                                TemplateId = reader.GetInt32("TemplateId"),
                                LanguageId = reader.GetInt32("LanguageId"),
                                Name = reader.GetString("Name"),
                                IsActive = reader.GetBoolean("IsActive"),
                                CreatedAt = reader.GetDateTime("CreatedAt"),
                                UpdatedAt = reader.IsDBNull("UpdatedAt") ? null : (DateTime?)reader.GetDateTime("UpdatedAt"),
                                Template = new AnnouncementTemplate { Id = reader.GetInt32("TemplateId"), Name = reader.GetString("TemplateName") },
                                Language = new Language { Id = reader.GetInt32("LanguageId"), Name = reader.GetString("LanguageName") }
                            });
                        }
                    }
                }
            }

            return sequences;
        }

        public async Task<AnnouncementSequence> GetSequenceByIdAsync(int id)
        {
            var query = @"SELECT s.Id, s.TemplateId, s.LanguageId, s.Name, s.IsActive, s.CreatedAt, s.UpdatedAt,
                         t.Name as TemplateName, l.Name as LanguageName
                         FROM AnnouncementSequences s
                         INNER JOIN AnnouncementTemplates t ON s.TemplateId = t.Id
                         INNER JOIN Languages l ON s.LanguageId = l.Id
                         WHERE s.Id = @Id";

            using (var connection = Database.GetConnection())
            {
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Id", id);

                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        if (await reader.ReadAsync())
                        {
                            return new AnnouncementSequence
                            {
                                Id = reader.GetInt32("Id"),
                                TemplateId = reader.GetInt32("TemplateId"),
                                LanguageId = reader.GetInt32("LanguageId"),
                                Name = reader.GetString("Name"),
                                IsActive = reader.GetBoolean("IsActive"),
                                CreatedAt = reader.GetDateTime("CreatedAt"),
                                UpdatedAt = reader.IsDBNull("UpdatedAt") ? null : (DateTime?)reader.GetDateTime("UpdatedAt"),
                                Template = new AnnouncementTemplate { Id = reader.GetInt32("TemplateId"), Name = reader.GetString("TemplateName") },
                                Language = new Language { Id = reader.GetInt32("LanguageId"), Name = reader.GetString("LanguageName") }
                            };
                        }
                    }
                }
            }

            return null;
        }

        public async Task<AnnouncementSequence> GetSequenceByTemplateAndLanguageAsync(int templateId, int languageId)
        {
            var query = @"SELECT s.Id, s.TemplateId, s.LanguageId, s.Name, s.IsActive, s.CreatedAt, s.UpdatedAt,
                         t.Name as TemplateName, l.Name as LanguageName
                         FROM AnnouncementSequences s
                         INNER JOIN AnnouncementTemplates t ON s.TemplateId = t.Id
                         INNER JOIN Languages l ON s.LanguageId = l.Id
                         WHERE s.TemplateId = @TemplateId AND s.LanguageId = @LanguageId";

            using (var connection = Database.GetConnection())
            {
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@TemplateId", templateId);
                    command.Parameters.AddWithValue("@LanguageId", languageId);

                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        if (await reader.ReadAsync())
                        {
                            return new AnnouncementSequence
                            {
                                Id = reader.GetInt32("Id"),
                                TemplateId = reader.GetInt32("TemplateId"),
                                LanguageId = reader.GetInt32("LanguageId"),
                                Name = reader.GetString("Name"),
                                IsActive = reader.GetBoolean("IsActive"),
                                CreatedAt = reader.GetDateTime("CreatedAt"),
                                UpdatedAt = reader.IsDBNull("UpdatedAt") ? null : (DateTime?)reader.GetDateTime("UpdatedAt"),
                                Template = new AnnouncementTemplate { Id = reader.GetInt32("TemplateId"), Name = reader.GetString("TemplateName") },
                                Language = new Language { Id = reader.GetInt32("LanguageId"), Name = reader.GetString("LanguageName") }
                            };
                        }
                    }
                }
            }

            return null;
        }

        public async Task<int> AddSequenceAsync(AnnouncementSequence sequence)
        {
            var query = @"INSERT INTO AnnouncementSequences (TemplateId, LanguageId, Name, IsActive, CreatedAt) 
                         VALUES (@TemplateId, @LanguageId, @Name, @IsActive, @CreatedAt);
                         SELECT last_insert_rowid();";

            using (var connection = Database.GetConnection())
            {
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@TemplateId", sequence.TemplateId);
                    command.Parameters.AddWithValue("@LanguageId", sequence.LanguageId);
                    command.Parameters.AddWithValue("@Name", sequence.Name);
                    command.Parameters.AddWithValue("@IsActive", sequence.IsActive);
                    command.Parameters.AddWithValue("@CreatedAt", sequence.CreatedAt);

                    var result = await command.ExecuteScalarAsync();
                    return Convert.ToInt32(result);
                }
            }
        }

        public async Task<bool> UpdateSequenceAsync(AnnouncementSequence sequence)
        {
            var query = @"UPDATE AnnouncementSequences 
                         SET TemplateId = @TemplateId, LanguageId = @LanguageId, Name = @Name, 
                             IsActive = @IsActive, UpdatedAt = @UpdatedAt 
                         WHERE Id = @Id";

            using (var connection = Database.GetConnection())
            {
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Id", sequence.Id);
                    command.Parameters.AddWithValue("@TemplateId", sequence.TemplateId);
                    command.Parameters.AddWithValue("@LanguageId", sequence.LanguageId);
                    command.Parameters.AddWithValue("@Name", sequence.Name);
                    command.Parameters.AddWithValue("@IsActive", sequence.IsActive);
                    command.Parameters.AddWithValue("@UpdatedAt", DateTime.Now);

                    var rowsAffected = await command.ExecuteNonQueryAsync();
                    return rowsAffected > 0;
                }
            }
        }

        public async Task<bool> DeleteSequenceAsync(int id)
        {
            var query = "DELETE FROM AnnouncementSequences WHERE Id = @Id";

            using (var connection = Database.GetConnection())
            {
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Id", id);

                    var rowsAffected = await command.ExecuteNonQueryAsync();
                    return rowsAffected > 0;
                }
            }
        }

        public async Task<bool> ToggleSequenceStatusAsync(int id)
        {
            var query = "UPDATE AnnouncementSequences SET IsActive = NOT IsActive, UpdatedAt = @UpdatedAt WHERE Id = @Id";

            using (var connection = Database.GetConnection())
            {
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Id", id);
                    command.Parameters.AddWithValue("@UpdatedAt", DateTime.Now);

                    var rowsAffected = await command.ExecuteNonQueryAsync();
                    return rowsAffected > 0;
                }
            }
        }
    }
} 