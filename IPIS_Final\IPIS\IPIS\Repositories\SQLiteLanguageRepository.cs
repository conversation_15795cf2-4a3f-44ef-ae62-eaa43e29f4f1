using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SQLite;
using System.Threading.Tasks;
using IPIS.Models;
using IPIS.Repositories.Interfaces;
using IPIS.Utils;

namespace IPIS.Repositories
{
    public class SQLiteLanguageRepository : ILanguageRepository
    {
        private readonly string _connectionString;

        public SQLiteLanguageRepository()
        {
            _connectionString = Database.ConnectionString;
            InitializeTable();
        }

        private void InitializeTable()
        {
            using (var connection = new SQLiteConnection(_connectionString))
            {
                connection.Open();
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = @"
                        CREATE TABLE IF NOT EXISTS Languages (
                            Id INTEGER PRIMARY KEY AUTOINCREMENT,
                            Name TEXT NOT NULL,
                            Code TEXT UNIQUE NOT NULL,
                            NativeName TEXT,
                            IsActive INTEGER DEFAULT 1,
                            IsDefault INTEGER DEFAULT 0,
                            WaveFolderPath TEXT,
                            CreatedAt TEXT NOT NULL,
                            UpdatedAt TEXT
                        )";
                    command.ExecuteNonQuery();

                    // Insert default languages if table is empty
                    command.CommandText = "SELECT COUNT(*) FROM Languages";
                    var count = Convert.ToInt32(command.ExecuteScalar());
                    
                    if (count == 0)
                    {
                        InsertDefaultLanguages(command);
                    }
                }
            }
        }

        private void InsertDefaultLanguages(SQLiteCommand command)
        {
            var defaultLanguages = new[]
            {
                new { Name = "English", Code = "EN", NativeName = "English", WaveFolderPath = "ENGLISH", IsDefault = true },
                new { Name = "Hindi", Code = "HI", NativeName = "हिंदी", WaveFolderPath = "HINDI", IsDefault = false }
            };

            foreach (var lang in defaultLanguages)
            {
                command.CommandText = @"
                    INSERT INTO Languages (Name, Code, NativeName, IsActive, IsDefault, WaveFolderPath, CreatedAt)
                    VALUES (@Name, @Code, @NativeName, 1, @IsDefault, @WaveFolderPath, @CreatedAt)";
                
                command.Parameters.Clear();
                command.Parameters.AddWithValue("@Name", lang.Name);
                command.Parameters.AddWithValue("@Code", lang.Code);
                command.Parameters.AddWithValue("@NativeName", lang.NativeName);
                command.Parameters.AddWithValue("@IsDefault", lang.IsDefault ? 1 : 0);
                command.Parameters.AddWithValue("@WaveFolderPath", lang.WaveFolderPath);
                command.Parameters.AddWithValue("@CreatedAt", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                
                command.ExecuteNonQuery();
            }
        }

        public async Task<List<Language>> GetAllAsync()
        {
            var languages = new List<Language>();
            
            using (var connection = new SQLiteConnection(_connectionString))
            {
                await connection.OpenAsync();
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = "SELECT * FROM Languages ORDER BY Name";
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            languages.Add(MapReaderToLanguage(reader));
                        }
                    }
                }
            }
            
            return languages;
        }

        public async Task<List<Language>> GetActiveAsync()
        {
            var languages = new List<Language>();
            
            using (var connection = new SQLiteConnection(_connectionString))
            {
                await connection.OpenAsync();
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = "SELECT * FROM Languages WHERE IsActive = 1 ORDER BY Name";
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            languages.Add(MapReaderToLanguage(reader));
                        }
                    }
                }
            }
            
            return languages;
        }

        public async Task<Language> GetByIdAsync(int id)
        {
            using (var connection = new SQLiteConnection(_connectionString))
            {
                await connection.OpenAsync();
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = "SELECT * FROM Languages WHERE Id = @Id";
                    command.Parameters.AddWithValue("@Id", id);
                    
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        if (await reader.ReadAsync())
                        {
                            return MapReaderToLanguage(reader);
                        }
                    }
                }
            }
            
            return null;
        }

        public async Task<Language> GetByCodeAsync(string code)
        {
            using (var connection = new SQLiteConnection(_connectionString))
            {
                await connection.OpenAsync();
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = "SELECT * FROM Languages WHERE Code = @Code";
                    command.Parameters.AddWithValue("@Code", code);
                    
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        if (await reader.ReadAsync())
                        {
                            return MapReaderToLanguage(reader);
                        }
                    }
                }
            }
            
            return null;
        }

        public async Task<Language> GetDefaultAsync()
        {
            using (var connection = new SQLiteConnection(_connectionString))
            {
                await connection.OpenAsync();
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = "SELECT * FROM Languages WHERE IsDefault = 1 LIMIT 1";
                    
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        if (await reader.ReadAsync())
                        {
                            return MapReaderToLanguage(reader);
                        }
                    }
                }
            }
            
            return null;
        }

        public async Task<int> AddAsync(Language language)
        {
            using (var connection = new SQLiteConnection(_connectionString))
            {
                await connection.OpenAsync();
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = @"
                        INSERT INTO Languages (Name, Code, NativeName, IsActive, IsDefault, WaveFolderPath, CreatedAt)
                        VALUES (@Name, @Code, @NativeName, @IsActive, @IsDefault, @WaveFolderPath, @CreatedAt);
                        SELECT last_insert_rowid();";
                    
                    command.Parameters.AddWithValue("@Name", language.Name);
                    command.Parameters.AddWithValue("@Code", language.Code);
                    command.Parameters.AddWithValue("@NativeName", language.NativeName ?? "");
                    command.Parameters.AddWithValue("@IsActive", language.IsActive ? 1 : 0);
                    command.Parameters.AddWithValue("@IsDefault", language.IsDefault ? 1 : 0);
                    command.Parameters.AddWithValue("@WaveFolderPath", language.WaveFolderPath ?? "");
                    command.Parameters.AddWithValue("@CreatedAt", language.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss"));
                    
                    return Convert.ToInt32(await command.ExecuteScalarAsync());
                }
            }
        }

        public async Task<bool> UpdateAsync(Language language)
        {
            using (var connection = new SQLiteConnection(_connectionString))
            {
                await connection.OpenAsync();
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = @"
                        UPDATE Languages 
                        SET Name = @Name, Code = @Code, NativeName = @NativeName, 
                            IsActive = @IsActive, IsDefault = @IsDefault, 
                            WaveFolderPath = @WaveFolderPath, UpdatedAt = @UpdatedAt
                        WHERE Id = @Id";
                    
                    command.Parameters.AddWithValue("@Id", language.Id);
                    command.Parameters.AddWithValue("@Name", language.Name);
                    command.Parameters.AddWithValue("@Code", language.Code);
                    command.Parameters.AddWithValue("@NativeName", language.NativeName ?? "");
                    command.Parameters.AddWithValue("@IsActive", language.IsActive ? 1 : 0);
                    command.Parameters.AddWithValue("@IsDefault", language.IsDefault ? 1 : 0);
                    command.Parameters.AddWithValue("@WaveFolderPath", language.WaveFolderPath ?? "");
                    command.Parameters.AddWithValue("@UpdatedAt", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                    
                    return await command.ExecuteNonQueryAsync() > 0;
                }
            }
        }

        public async Task<bool> DeleteAsync(int id)
        {
            using (var connection = new SQLiteConnection(_connectionString))
            {
                await connection.OpenAsync();
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = "DELETE FROM Languages WHERE Id = @Id";
                    command.Parameters.AddWithValue("@Id", id);
                    
                    return await command.ExecuteNonQueryAsync() > 0;
                }
            }
        }

        public async Task<bool> SetDefaultAsync(int id)
        {
            using (var connection = new SQLiteConnection(_connectionString))
            {
                await connection.OpenAsync();
                using (var transaction = connection.BeginTransaction())
                {
                    try
                    {
                        using (var command = connection.CreateCommand())
                        {
                            // Reset all languages to non-default
                            command.CommandText = "UPDATE Languages SET IsDefault = 0";
                            await command.ExecuteNonQueryAsync();
                            
                            // Set the specified language as default
                            command.CommandText = "UPDATE Languages SET IsDefault = 1 WHERE Id = @Id";
                            command.Parameters.AddWithValue("@Id", id);
                            await command.ExecuteNonQueryAsync();
                        }
                        
                        transaction.Commit();
                        return true;
                    }
                    catch
                    {
                        transaction.Rollback();
                        throw;
                    }
                }
            }
        }

        public async Task<bool> ExistsAsync(string code)
        {
            using (var connection = new SQLiteConnection(_connectionString))
            {
                await connection.OpenAsync();
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = "SELECT COUNT(*) FROM Languages WHERE Code = @Code";
                    command.Parameters.AddWithValue("@Code", code);
                    
                    var count = Convert.ToInt32(await command.ExecuteScalarAsync());
                    return count > 0;
                }
            }
        }

        public async Task<bool> ExistsAsync(int id)
        {
            using (var connection = new SQLiteConnection(_connectionString))
            {
                await connection.OpenAsync();
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = "SELECT COUNT(*) FROM Languages WHERE Id = @Id";
                    command.Parameters.AddWithValue("@Id", id);
                    
                    var count = Convert.ToInt32(await command.ExecuteScalarAsync());
                    return count > 0;
                }
            }
        }

        private Language MapReaderToLanguage(System.Data.Common.DbDataReader reader)
        {
            return new Language
            {
                Id = Convert.ToInt32(reader["Id"]),
                Name = reader["Name"].ToString(),
                Code = reader["Code"].ToString(),
                NativeName = reader["NativeName"].ToString(),
                IsActive = Convert.ToBoolean(reader["IsActive"]),
                IsDefault = Convert.ToBoolean(reader["IsDefault"]),
                WaveFolderPath = reader["WaveFolderPath"].ToString(),
                CreatedAt = DateTime.Parse(reader["CreatedAt"].ToString()),
                UpdatedAt = reader["UpdatedAt"] != DBNull.Value ? DateTime.Parse(reader["UpdatedAt"].ToString()) : null
            };
        }
    }
} 