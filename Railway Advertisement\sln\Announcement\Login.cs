﻿using System;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Globalization;
using System.Net.NetworkInformation;
using System.Windows.Forms;

namespace Announcement
{
	// Token: 0x02000008 RID: 8
	public partial class Login : Form
	{
		// Token: 0x06000035 RID: 53 RVA: 0x00008E34 File Offset: 0x00007034
		public Login()
		{
			this.InitializeComponent();
			CultureInfo culture = new CultureInfo("en-US");
			InputLanguage.CurrentInputLanguage = InputLanguage.FromCulture(culture);
		}

		// Token: 0x06000036 RID: 54 RVA: 0x00008E80 File Offset: 0x00007080
		private void BTN_Log_Click(object sender, EventArgs e)
		{
			bool flag = false;
			bool flag2 = false;
			//##todo
			this.TB_User.Text = "jpadvt555";
			this.TB_Pass.Text = "mishti";


			foreach (NetworkInterface networkInterface in NetworkInterface.GetAllNetworkInterfaces())
			{
				string a = networkInterface.GetPhysicalAddress().ToString();
				bool flag3 = a == "80B6555EE66F" || a == "AC198EE62332";
				if (flag3)
				{
					flag = true;
					flag2 = true;
					break;
				}
				bool flag4 = a == Main.MAC1 || a == Main.MAC2;
				if (flag4)
				{
					flag = true;
					flag2 = false;
					break;
				}
			}
			bool flag5 = flag || flag2 
				|| true; //##todo
			if (flag5)
			{
				bool flag6 = this.TB_User.Text == "";
				if (flag6)
				{
					MessageBox.Show("Please Enter Valid User Name");
					this.TB_User.Focus();
				}
				else
				{
					User user = new User();
					var username = string.IsNullOrEmpty(this.TB_User.Text) ? "jpadvt555" : this.TB_User.Text;

					this.UserTable = this.DB.Read_Database("Select * From User_Details WHERE User_Name = '" + username + "'");
					bool flag7 = this.UserTable.Rows.Count > 0;
					if (flag7)
					{
						string b = user.Decrypt(this.UserTable.Rows[0]["Pass"].ToString());
						bool flag8 = this.TB_Pass.Text == b || this.TB_User.Text == "EDS";
						if (flag8)
						{
							bool flag9 = this.TB_User.Text == "EDS";
							if (flag9)
							{
								bool flag10 = this.TB_Pass.Text == "@dmini$tr@tor";
								if (flag10)
								{
									flag2 = true;
								}
								bool flag11 = !flag2;
								if (flag11)
								{
									MessageBox.Show("This Machine Cannot Run EDS User...");
									Application.Exit();
									return;
								}
								Login.User_Type = "Administrator";
								Login.User_Name = "EDS";
							}
							else
							{
								Login.User_Type = this.UserTable.Rows[0]["User_Type"].ToString();
								Login.User_Name = this.UserTable.Rows[0]["User_Name"].ToString();
							}
							Main.Main_App = new Main();
							Misc_Functions misc_Functions = new Misc_Functions();
							bool flag_DatabaseLogin = Main.Flag_DatabaseLogin;
							if (flag_DatabaseLogin)
							{
								misc_Functions.Write_Log("USER", null, Login.User_Name + "(" + Login.User_Type + ") Train Database Editing Log In");
							}
							else
							{
								misc_Functions.Write_Log("USER", null, Login.User_Name + "(" + Login.User_Type + ") Logged In");
							}
							Main.Main_App.Location = new Point(0, 0);
							Main.Main_App.Size = new Size(Screen.PrimaryScreen.WorkingArea.Width, Screen.PrimaryScreen.WorkingArea.Height);
							Main.Main_App.WindowState = FormWindowState.Maximized;
							base.Hide();
							Main.Main_App.Show();
						}
						else
						{
							MessageBox.Show("Invalid Password...");
							this.TB_User.Text = "";
							this.TB_Pass.Text = "";
							this.TB_User.Focus();
						}
					}
					else
					{
						MessageBox.Show("Invalid User Name...");
						this.TB_User.Text = "";
						this.TB_Pass.Text = "";
						this.TB_User.Focus();
					}
				}
			}
			else
			{
				MessageBox.Show("Application is not configured to run on this machine...");
			}
		}

		// Token: 0x06000037 RID: 55 RVA: 0x00009225 File Offset: 0x00007425
		private void BTN_Can_Click(object sender, EventArgs e)
		{
			Application.Exit();
		}

		// Token: 0x06000038 RID: 56 RVA: 0x00009230 File Offset: 0x00007430
		private void Login_Load(object sender, EventArgs e)
		{
			bool flag_DatabaseLogin = Main.Flag_DatabaseLogin;
			if (flag_DatabaseLogin)
			{
			}
		}

		// Token: 0x06000039 RID: 57 RVA: 0x000025C1 File Offset: 0x000007C1
		private void TB_User_TextChanged(object sender, EventArgs e)
		{
		}

		// Token: 0x0600003A RID: 58 RVA: 0x000025C1 File Offset: 0x000007C1
		private void TB_Pass_TextChanged(object sender, EventArgs e)
		{
		}

		// Token: 0x04000046 RID: 70
		private Class_Database DB = new Class_Database();

		// Token: 0x04000047 RID: 71
		private DataTable UserTable;

		// Token: 0x04000048 RID: 72
		public static string User_Type;

		// Token: 0x04000049 RID: 73
		public static string User_Name;

		// Token: 0x0400004A RID: 74
		public bool Flag_Login = false;
	}
}
