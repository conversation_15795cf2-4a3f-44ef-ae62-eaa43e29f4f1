// Decompiled with JetBrains decompiler
// Type: ipis.ConfigAdmin
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

namespace ipis
{

[DesignerGenerated]
public class ConfigAdmin : Form
{
  private static List<WeakReference> __ENCList = new List<WeakReference>();
  private IContainer components;
  [AccessedThroughProperty("Label2")]
  private Label _Label2;
  [AccessedThroughProperty("btnExit")]
  private Button _btnExit;
  [AccessedThroughProperty("btnOk")]
  private Button _btnOk;
  [AccessedThroughProperty("txtPassword")]
  private TextBox _txtPassword;
  [AccessedThroughProperty("txtUserName")]
  private TextBox _txtUserName;
  [AccessedThroughProperty("PasswordLabel")]
  private Label _PasswordLabel;
  [AccessedThroughProperty("UsernameLabel")]
  private Label _UsernameLabel;
  [AccessedThroughProperty("PictureBox1")]
  private PictureBox _PictureBox1;
  [AccessedThroughProperty("addtrain_event")]
  private frmTrainConfig _addtrain_event;

  [DebuggerNonUserCode]
  static ConfigAdmin()
  {
  }

  [DebuggerNonUserCode]
  public ConfigAdmin()
  {
    ConfigAdmin.__ENCAddToList((object) this);
    this.InitializeComponent();
  }

  [DebuggerNonUserCode]
  private static void __ENCAddToList(object value)
  {
    lock (ConfigAdmin.__ENCList)
    {
      if (ConfigAdmin.__ENCList.Count == ConfigAdmin.__ENCList.Capacity)
      {
        int index1 = 0;
        int num = checked (ConfigAdmin.__ENCList.Count - 1);
        int index2 = 0;
        while (index2 <= num)
        {
          if (ConfigAdmin.__ENCList[index2].IsAlive)
          {
            if (index2 != index1)
              ConfigAdmin.__ENCList[index1] = ConfigAdmin.__ENCList[index2];
            checked { ++index1; }
          }
          checked { ++index2; }
        }
        ConfigAdmin.__ENCList.RemoveRange(index1, checked (ConfigAdmin.__ENCList.Count - index1));
        ConfigAdmin.__ENCList.Capacity = ConfigAdmin.__ENCList.Count;
      }
      ConfigAdmin.__ENCList.Add(new WeakReference(RuntimeHelpers.GetObjectValue(value)));
    }
  }

  [DebuggerNonUserCode]
  protected override void Dispose(bool disposing)
  {
    try
    {
      if (!disposing || this.components == null)
        return;
      this.components.Dispose();
    }
    finally
    {
      base.Dispose(disposing);
    }
  }

  [DebuggerStepThrough]
  private void InitializeComponent()
  {
    ComponentResourceManager componentResourceManager = new ComponentResourceManager(typeof (ConfigAdmin));
    this.Label2 = new Label();
    this.btnExit = new Button();
    this.btnOk = new Button();
    this.txtPassword = new TextBox();
    this.txtUserName = new TextBox();
    this.PasswordLabel = new Label();
    this.UsernameLabel = new Label();
    this.PictureBox1 = new PictureBox();
    ((ISupportInitialize) this.PictureBox1).BeginInit();
    this.SuspendLayout();
    this.Label2.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label label2_1 = this.Label2;
    Point point1 = new Point(52, 269);
    Point point2 = point1;
    label2_1.Location = point2;
    this.Label2.Name = "Label2";
    Label label2_2 = this.Label2;
    Size size1 = new Size(269, 23);
    Size size2 = size1;
    label2_2.Size = size2;
    this.Label2.TabIndex = 25;
    this.Label2.Text = "Powered by Ninja Media Creations";
    this.Label2.TextAlign = ContentAlignment.MiddleLeft;
    this.btnExit.BackColor = Color.SeaShell;
    this.btnExit.DialogResult = DialogResult.Cancel;
    this.btnExit.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnExit1 = this.btnExit;
    point1 = new Point(567, 278);
    Point point3 = point1;
    btnExit1.Location = point3;
    this.btnExit.Name = "btnExit";
    Button btnExit2 = this.btnExit;
    size1 = new Size(67, 23);
    Size size3 = size1;
    btnExit2.Size = size3;
    this.btnExit.TabIndex = 22;
    this.btnExit.Text = "&Exit";
    this.btnExit.UseVisualStyleBackColor = false;
    this.btnOk.BackColor = Color.SeaShell;
    this.btnOk.DialogResult = DialogResult.OK;
    this.btnOk.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnOk1 = this.btnOk;
    point1 = new Point(446, 278);
    Point point4 = point1;
    btnOk1.Location = point4;
    this.btnOk.Name = "btnOk";
    Button btnOk2 = this.btnOk;
    size1 = new Size(65, 23);
    Size size4 = size1;
    btnOk2.Size = size4;
    this.btnOk.TabIndex = 21;
    this.btnOk.Text = "&OK";
    this.btnOk.UseVisualStyleBackColor = false;
    this.txtPassword.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtPassword1 = this.txtPassword;
    point1 = new Point(440, 205);
    Point point5 = point1;
    txtPassword1.Location = point5;
    this.txtPassword.Name = "txtPassword";
    this.txtPassword.PasswordChar = '*';
    TextBox txtPassword2 = this.txtPassword;
    size1 = new Size(220, 22);
    Size size5 = size1;
    txtPassword2.Size = size5;
    this.txtPassword.TabIndex = 20;
    this.txtUserName.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtUserName1 = this.txtUserName;
    point1 = new Point(440, 112 /*0x70*/);
    Point point6 = point1;
    txtUserName1.Location = point6;
    this.txtUserName.Name = "txtUserName";
    TextBox txtUserName2 = this.txtUserName;
    size1 = new Size(220, 22);
    Size size6 = size1;
    txtUserName2.Size = size6;
    this.txtUserName.TabIndex = 19;
    this.PasswordLabel.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label passwordLabel1 = this.PasswordLabel;
    point1 = new Point(440, 168);
    Point point7 = point1;
    passwordLabel1.Location = point7;
    this.PasswordLabel.Name = "PasswordLabel";
    Label passwordLabel2 = this.PasswordLabel;
    size1 = new Size(84, 23);
    Size size7 = size1;
    passwordLabel2.Size = size7;
    this.PasswordLabel.TabIndex = 24;
    this.PasswordLabel.Text = "Password";
    this.PasswordLabel.TextAlign = ContentAlignment.MiddleLeft;
    this.UsernameLabel.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label usernameLabel1 = this.UsernameLabel;
    point1 = new Point(440, 70);
    Point point8 = point1;
    usernameLabel1.Location = point8;
    this.UsernameLabel.Name = "UsernameLabel";
    Label usernameLabel2 = this.UsernameLabel;
    size1 = new Size(93, 23);
    Size size8 = size1;
    usernameLabel2.Size = size8;
    this.UsernameLabel.TabIndex = 23;
    this.UsernameLabel.Text = "User Name";
    this.UsernameLabel.TextAlign = ContentAlignment.MiddleLeft;
    this.PictureBox1.ErrorImage = (Image) componentResourceManager.GetObject("PictureBox1.ErrorImage");
    this.PictureBox1.Image = (Image) componentResourceManager.GetObject("PictureBox1.Image");
    PictureBox pictureBox1_1 = this.PictureBox1;
    point1 = new Point(52, 92);
    Point point9 = point1;
    pictureBox1_1.Location = point9;
    this.PictureBox1.Name = "PictureBox1";
    PictureBox pictureBox1_2 = this.PictureBox1;
    size1 = new Size(337, 165);
    Size size9 = size1;
    pictureBox1_2.Size = size9;
    this.PictureBox1.TabIndex = 18;
    this.PictureBox1.TabStop = false;
    this.AcceptButton = (IButtonControl) this.btnOk;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.BackColor = Color.LightBlue;
    size1 = new Size(713, 370);
    this.ClientSize = size1;
    this.Controls.Add((Control) this.Label2);
    this.Controls.Add((Control) this.btnExit);
    this.Controls.Add((Control) this.btnOk);
    this.Controls.Add((Control) this.txtPassword);
    this.Controls.Add((Control) this.txtUserName);
    this.Controls.Add((Control) this.PasswordLabel);
    this.Controls.Add((Control) this.UsernameLabel);
    this.Controls.Add((Control) this.PictureBox1);
    this.Name = "ConfigAdmin";
    this.Text = "ConfigAdmin";
    ((ISupportInitialize) this.PictureBox1).EndInit();
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  internal virtual Label Label2
  {
    [DebuggerNonUserCode] get { return this._Label2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Label2 = value; }
  }

  internal virtual Button btnExit
  {
    [DebuggerNonUserCode] get { return this._btnExit; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._btnExit = value; }
  }

  internal virtual Button btnOk
  {
    [DebuggerNonUserCode] get { return this._btnOk; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnOk_Click);
      if (this._btnOk != null)
        this._btnOk.Click -= eventHandler;
      this._btnOk = value;
      if (this._btnOk == null)
        return;
      this._btnOk.Click += eventHandler;
    }
  }

  internal virtual TextBox txtPassword
  {
    [DebuggerNonUserCode] get { return this._txtPassword; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtPassword = value;
    }
  }

  internal virtual TextBox txtUserName
  {
    [DebuggerNonUserCode] get { return this._txtUserName; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtUserName = value;
    }
  }

  internal virtual Label PasswordLabel
  {
    [DebuggerNonUserCode] get { return this._PasswordLabel; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._PasswordLabel = value;
    }
  }

  internal virtual Label UsernameLabel
  {
    [DebuggerNonUserCode] get { return this._UsernameLabel; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._UsernameLabel = value;
    }
  }

  internal virtual PictureBox PictureBox1
  {
    [DebuggerNonUserCode] get { return this._PictureBox1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._PictureBox1 = value;
    }
  }

  protected virtual frmTrainConfig addtrain_event
  {
    [DebuggerNonUserCode] get { return this._addtrain_event; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._addtrain_event = value;
    }
  }

  private void btnOk_Click(object sender, EventArgs e)
  {
    if (Operators.CompareString(Strings.Trim(this.txtUserName.Text), "admin", false) == 0 & Operators.CompareString(Strings.Trim(this.txtPassword.Text), "Welcome@123", false) == 0)
    {
      this.Close();
      if (!Information.IsNothing((object) this.addtrain_event))
      {
        if (!this.addtrain_event.IsDisposed)
        {
          this.addtrain_event.WindowState = FormWindowState.Normal;
          this.addtrain_event.BringToFront();
        }
        else
        {
          this.addtrain_event = new frmTrainConfig();
          this.addtrain_event.Show();
        }
      }
      else
      {
        this.addtrain_event = new frmTrainConfig();
        this.addtrain_event.Show();
        this.addtrain_event.BringToFront();
      }
    }
    else
    {
      int num = (int) MessageBox.Show("You do not have permission to Configure");
    }
  }
}
}