// Decompiled with JetBrains decompiler
// Type: ipis.frmAddTrainNameVoice
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using ipis.My;
using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Runtime.CompilerServices;
using System.Windows.Forms;
using System.Xml;

namespace ipis
{

[DesignerGenerated]
public class frmAddTrainNameVoice : Form
{
  private static List<WeakReference> __ENCList = new List<WeakReference>();
  private IContainer components;
  [AccessedThroughProperty("btnExit")]
  private Button _btnExit;
  [AccessedThroughProperty("lblTrainName")]
  private Label _lblTrainName;
  [AccessedThroughProperty("btnBrowse")]
  private Button _btnBrowse;
  [AccessedThroughProperty("lblFile")]
  private Label _lblFile;
  [AccessedThroughProperty("txtFilePath")]
  private TextBox _txtFilePath;
  [AccessedThroughProperty("txttrainNo")]
  private TextBox _txttrainNo;
  [AccessedThroughProperty("lblLang")]
  private Label _lblLang;
  [AccessedThroughProperty("cmbLang")]
  private ComboBox _cmbLang;
  [AccessedThroughProperty("btnSave")]
  private Button _btnSave;
  private StreamWriter sw;

  [DebuggerNonUserCode]
  static frmAddTrainNameVoice()
  {
  }

  [DebuggerNonUserCode]
  public frmAddTrainNameVoice()
  {
    this.FormClosing += new FormClosingEventHandler(this.frmAddTrainNameVoice_FormClosing);
    frmAddTrainNameVoice.__ENCAddToList((object) this);
    this.InitializeComponent();
  }

  [DebuggerNonUserCode]
  private static void __ENCAddToList(object value)
  {
    lock (frmAddTrainNameVoice.__ENCList)
    {
      if (frmAddTrainNameVoice.__ENCList.Count == frmAddTrainNameVoice.__ENCList.Capacity)
      {
        int index1 = 0;
        int num = checked (frmAddTrainNameVoice.__ENCList.Count - 1);
        int index2 = 0;
        while (index2 <= num)
        {
          if (frmAddTrainNameVoice.__ENCList[index2].IsAlive)
          {
            if (index2 != index1)
              frmAddTrainNameVoice.__ENCList[index1] = frmAddTrainNameVoice.__ENCList[index2];
            checked { ++index1; }
          }
          checked { ++index2; }
        }
        frmAddTrainNameVoice.__ENCList.RemoveRange(index1, checked (frmAddTrainNameVoice.__ENCList.Count - index1));
        frmAddTrainNameVoice.__ENCList.Capacity = frmAddTrainNameVoice.__ENCList.Count;
      }
      frmAddTrainNameVoice.__ENCList.Add(new WeakReference(RuntimeHelpers.GetObjectValue(value)));
    }
  }

  [DebuggerNonUserCode]
  protected override void Dispose(bool disposing)
  {
    try
    {
      if (!disposing || this.components == null)
        return;
      this.components.Dispose();
    }
    finally
    {
      base.Dispose(disposing);
    }
  }

  [DebuggerStepThrough]
  private void InitializeComponent()
  {
    this.btnExit = new Button();
    this.lblTrainName = new Label();
    this.btnBrowse = new Button();
    this.lblFile = new Label();
    this.txtFilePath = new TextBox();
    this.txttrainNo = new TextBox();
    this.lblLang = new Label();
    this.cmbLang = new ComboBox();
    this.btnSave = new Button();
    this.SuspendLayout();
    this.btnExit.BackColor = SystemColors.ButtonFace;
    this.btnExit.DialogResult = DialogResult.Cancel;
    this.btnExit.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.btnExit.ForeColor = SystemColors.ControlText;
    Button btnExit1 = this.btnExit;
    Point point1 = new Point(199, 154);
    Point point2 = point1;
    btnExit1.Location = point2;
    this.btnExit.Name = "btnExit";
    Button btnExit2 = this.btnExit;
    Size size1 = new Size(60, 25);
    Size size2 = size1;
    btnExit2.Size = size2;
    this.btnExit.TabIndex = 28;
    this.btnExit.Text = "Exit";
    this.btnExit.UseVisualStyleBackColor = false;
    this.lblTrainName.AutoSize = true;
    this.lblTrainName.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.lblTrainName.ForeColor = SystemColors.ControlText;
    Label lblTrainName1 = this.lblTrainName;
    point1 = new Point(26, 16 /*0x10*/);
    Point point3 = point1;
    lblTrainName1.Location = point3;
    this.lblTrainName.Name = "lblTrainName";
    Label lblTrainName2 = this.lblTrainName;
    size1 = new Size(68, 16 /*0x10*/);
    Size size3 = size1;
    lblTrainName2.Size = size3;
    this.lblTrainName.TabIndex = 30;
    this.lblTrainName.Text = "Train No";
    this.btnBrowse.BackColor = SystemColors.ButtonFace;
    this.btnBrowse.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.btnBrowse.ForeColor = SystemColors.ControlText;
    Button btnBrowse1 = this.btnBrowse;
    point1 = new Point(307, 97);
    Point point4 = point1;
    btnBrowse1.Location = point4;
    this.btnBrowse.Name = "btnBrowse";
    Button btnBrowse2 = this.btnBrowse;
    size1 = new Size(75, 23);
    Size size4 = size1;
    btnBrowse2.Size = size4;
    this.btnBrowse.TabIndex = 26;
    this.btnBrowse.Text = "Browse";
    this.btnBrowse.UseVisualStyleBackColor = false;
    this.lblFile.AutoSize = true;
    this.lblFile.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.lblFile.ForeColor = SystemColors.ControlText;
    Label lblFile1 = this.lblFile;
    point1 = new Point(55, 101);
    Point point5 = point1;
    lblFile1.Location = point5;
    this.lblFile.Name = "lblFile";
    Label lblFile2 = this.lblFile;
    size1 = new Size(34, 16 /*0x10*/);
    Size size5 = size1;
    lblFile2.Size = size5;
    this.lblFile.TabIndex = 29;
    this.lblFile.Text = "File";
    this.txtFilePath.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.txtFilePath.ForeColor = SystemColors.ControlText;
    TextBox txtFilePath1 = this.txtFilePath;
    point1 = new Point(100, 98);
    Point point6 = point1;
    txtFilePath1.Location = point6;
    this.txtFilePath.Name = "txtFilePath";
    TextBox txtFilePath2 = this.txtFilePath;
    size1 = new Size(184, 22);
    Size size6 = size1;
    txtFilePath2.Size = size6;
    this.txtFilePath.TabIndex = 25;
    this.txttrainNo.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.txttrainNo.ForeColor = SystemColors.ControlText;
    TextBox txttrainNo1 = this.txttrainNo;
    point1 = new Point(101, 13);
    Point point7 = point1;
    txttrainNo1.Location = point7;
    this.txttrainNo.MaxLength = 30;
    this.txttrainNo.Name = "txttrainNo";
    TextBox txttrainNo2 = this.txttrainNo;
    size1 = new Size(73, 22);
    Size size7 = size1;
    txttrainNo2.Size = size7;
    this.txttrainNo.TabIndex = 22;
    this.lblLang.AutoSize = true;
    this.lblLang.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblLang1 = this.lblLang;
    point1 = new Point(19, 56);
    Point point8 = point1;
    lblLang1.Location = point8;
    this.lblLang.Name = "lblLang";
    Label lblLang2 = this.lblLang;
    size1 = new Size(77, 16 /*0x10*/);
    Size size8 = size1;
    lblLang2.Size = size8;
    this.lblLang.TabIndex = 33;
    this.lblLang.Text = "Language";
    this.cmbLang.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.cmbLang.FormattingEnabled = true;
    this.cmbLang.Items.AddRange(new object[3]
    {
      (object) "English",
      (object) "Hindi",
      (object) "Regional"
    });
    ComboBox cmbLang1 = this.cmbLang;
    point1 = new Point(101, 53);
    Point point9 = point1;
    cmbLang1.Location = point9;
    this.cmbLang.Name = "cmbLang";
    ComboBox cmbLang2 = this.cmbLang;
    size1 = new Size(73, 24);
    Size size9 = size1;
    cmbLang2.Size = size9;
    this.cmbLang.TabIndex = 32 /*0x20*/;
    this.btnSave.BackColor = SystemColors.ButtonFace;
    this.btnSave.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.btnSave.ForeColor = SystemColors.ControlText;
    Button btnSave1 = this.btnSave;
    point1 = new Point(109, 154);
    Point point10 = point1;
    btnSave1.Location = point10;
    this.btnSave.Name = "btnSave";
    Button btnSave2 = this.btnSave;
    size1 = new Size(60, 25);
    Size size10 = size1;
    btnSave2.Size = size10;
    this.btnSave.TabIndex = 34;
    this.btnSave.Text = "Save";
    this.btnSave.UseVisualStyleBackColor = false;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.BackColor = Color.PowderBlue;
    this.CancelButton = (IButtonControl) this.btnExit;
    size1 = new Size(394, 191);
    this.ClientSize = size1;
    this.Controls.Add((Control) this.btnSave);
    this.Controls.Add((Control) this.lblLang);
    this.Controls.Add((Control) this.cmbLang);
    this.Controls.Add((Control) this.btnExit);
    this.Controls.Add((Control) this.lblTrainName);
    this.Controls.Add((Control) this.btnBrowse);
    this.Controls.Add((Control) this.lblFile);
    this.Controls.Add((Control) this.txtFilePath);
    this.Controls.Add((Control) this.txttrainNo);
    this.FormBorderStyle = FormBorderStyle.FixedToolWindow;
    this.Name = "frmAddTrainNameVoice";
    this.Text = "AddTrainNameVoice";
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  internal virtual Button btnExit
  {
    [DebuggerNonUserCode] get { return this._btnExit; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnExit_Click);
      if (this._btnExit != null)
        this._btnExit.Click -= eventHandler;
      this._btnExit = value;
      if (this._btnExit == null)
        return;
      this._btnExit.Click += eventHandler;
    }
  }

  internal virtual Label lblTrainName
  {
    [DebuggerNonUserCode] get { return this._lblTrainName; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblTrainName = value;
    }
  }

  internal virtual Button btnBrowse
  {
    [DebuggerNonUserCode] get { return this._btnBrowse; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnBrowse_Click);
      if (this._btnBrowse != null)
        this._btnBrowse.Click -= eventHandler;
      this._btnBrowse = value;
      if (this._btnBrowse == null)
        return;
      this._btnBrowse.Click += eventHandler;
    }
  }

  internal virtual Label lblFile
  {
    [DebuggerNonUserCode] get { return this._lblFile; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._lblFile = value; }
  }

  internal virtual TextBox txtFilePath
  {
    [DebuggerNonUserCode] get { return this._txtFilePath; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtFilePath = value;
    }
  }

  internal virtual TextBox txttrainNo
  {
    [DebuggerNonUserCode] get { return this._txttrainNo; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txttrainNo = value;
    }
  }

  internal virtual Label lblLang
  {
    [DebuggerNonUserCode] get { return this._lblLang; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._lblLang = value; }
  }

  internal virtual ComboBox cmbLang
  {
    [DebuggerNonUserCode] get { return this._cmbLang; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbLang = value; }
  }

  internal virtual Button btnSave
  {
    [DebuggerNonUserCode] get { return this._btnSave; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnSave_Click);
      if (this._btnSave != null)
        this._btnSave.Click -= eventHandler;
      this._btnSave = value;
      if (this._btnSave == null)
        return;
      this._btnSave.Click += eventHandler;
    }
  }

  private void btnBrowse_Click(object sender, EventArgs e)
  {
    OpenFileDialog openFileDialog = new OpenFileDialog();
    try
    {
      network_db_read.get_language_details();
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(this.cmbLang.Text, "English", false) == 0)
        openFileDialog.InitialDirectory = "C:\\IPIS\\voice\\TrainNames";
      else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(this.cmbLang.Text, "Hindi", false) == 0)
        openFileDialog.InitialDirectory = "C:\\IPIS\\voice\\hindi\\TrainNames";
      else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(this.cmbLang.Text, "Regional", false) == 0)
      {
        if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Strings.Trim(frmMainFormIPIS.language_selection.regional_language_name), "Telugu", false) == 0)
          openFileDialog.InitialDirectory = "C:\\IPIS\\voice\\regional\\TrainNames";
        else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Strings.Trim(frmMainFormIPIS.language_selection.regional_language_name), "Oriya", false) == 0)
          openFileDialog.InitialDirectory = "C:\\IPIS\\voice\\regional\\oriya\\TrainNames";
        else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Strings.Trim(frmMainFormIPIS.language_selection.regional_language_name), "Marathi", false) == 0)
          openFileDialog.InitialDirectory = "C:\\IPIS\\voice\\regional\\Marathi\\TrainNames";
        else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Strings.Trim(frmMainFormIPIS.language_selection.regional_language_name), "Bengali", false) == 0)
          openFileDialog.InitialDirectory = "C:\\IPIS\\voice\\regional\\Bengali\\TrainNames";
        else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Strings.Trim(frmMainFormIPIS.language_selection.regional_language_name), "Chattisgarh", false) == 0)
          openFileDialog.InitialDirectory = "C:\\IPIS\\voice\\regional\\Chattisgarh\\TrainNames";
      }
      openFileDialog.Filter = "txt files (*.txt)|*.txt|All files (*.*)|*.*";
      openFileDialog.FilterIndex = 2;
      openFileDialog.RestoreDirectory = true;
      if (openFileDialog.ShowDialog() != DialogResult.OK)
        return;
      string fileName = openFileDialog.FileName;
      openFileDialog.OpenFile();
      this.txtFilePath.Text = fileName;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void btnSave_Click(object sender, EventArgs e)
  {
    string text1 = this.txtFilePath.Text;
    string empty = string.Empty;
    string str1 = "C:\\IPIS\\voice\\TrainNames\\TrainNameVoiceXml.xml";
    string str2 = "C:\\IPIS\\voice\\regional\\TrainNames\\reg_TrainNameVoiceXml.xml";
    string str3 = "C:\\IPIS\\voice\\hindi\\TrainNames\\hin_TrainNameVoiceXml.xml";
    string str4 = "C:\\IPIS\\voice\\regional\\oriya\\TrainNames\\oriya_TrainNameVoiceXml.xml";
    string str5 = "C:\\IPIS\\voice\\regional\\Marathi\\TrainNames\\Marathi_TrainNameVoiceXml.xml";
    string str6 = "C:\\IPIS\\voice\\regional\\bengali\\TrainNames\\Bengali_TrainNameVoiceXml.xml";
    string str7 = "C:\\IPIS\\voice\\regional\\Chattisgarh\\TrainNames\\Chattisgarh_TrainNameVoiceXml.xml";
    string text2 = this.txttrainNo.Text;
    string text3 = this.txtFilePath.Text;
    network_db_read.get_language_details();
    if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(this.cmbLang.Text, "English", false) == 0)
    {
      if (File.Exists(str1))
        this.update_playlist_trainname(str1, text2, text3);
      else
        this.Create_PlayList(str1, text2, text3);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(this.cmbLang.Text, "Hindi", false) == 0)
    {
      if (File.Exists(str3))
        this.update_playlist_trainname(str3, text2, text3);
      else
        this.Create_PlayList(str3, text2, text3);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(this.cmbLang.Text, "Regional", false) == 0)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Strings.Trim(frmMainFormIPIS.language_selection.regional_language_name), "Telugu", false) == 0)
      {
        if (File.Exists(str2))
          this.update_playlist_trainname(str2, text2, text3);
        else
          this.Create_PlayList(str2, text2, text3);
      }
      else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Strings.Trim(frmMainFormIPIS.language_selection.regional_language_name), "Oriya", false) == 0)
      {
        if (File.Exists(str4))
          this.update_playlist_trainname(str4, text2, text3);
        else
          this.Create_PlayList(str4, text2, text3);
      }
      else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Strings.Trim(frmMainFormIPIS.language_selection.regional_language_name), "Marathi", false) == 0)
      {
        if (File.Exists(str5))
          this.update_playlist_trainname(str5, text2, text3);
        else
          this.Create_PlayList(str5, text2, text3);
      }
      else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Strings.Trim(frmMainFormIPIS.language_selection.regional_language_name), "Chattisgarh", false) == 0)
      {
        if (File.Exists(str7))
          this.update_playlist_trainname(str7, text2, text3);
        else
          this.Create_PlayList(str7, text2, text3);
      }
      else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Strings.Trim(frmMainFormIPIS.language_selection.regional_language_name), "Bengali", false) == 0)
      {
        if (File.Exists(str6))
          this.update_playlist_trainname(str6, text2, text3);
        else
          this.Create_PlayList(str6, text2, text3);
      }
    }
    this.txtFilePath.Text = string.Empty;
    this.cmbLang.Text = string.Empty;
  }

  private void Create_PlayList(string FileName, string train_name, string train_name_path)
  {
    FileStream fileStream = new FileStream(FileName, FileMode.Create, FileAccess.ReadWrite, FileShare.ReadWrite);
    this.sw = new StreamWriter((Stream) fileStream);
    try
    {
      this.sw.WriteLine("<?xml version=\"1.0\" encoding=\"utf-8\" ?>");
      this.sw.WriteLine("<sounds>");
      this.train_name_data(train_name, train_name_path);
      this.sw.WriteLine("</sounds>");
      FileName += " Successfully created.";
      int num = (int) MessageBox.Show(FileName, "Create Playlist");
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      int num = (int) MessageBox.Show(ex.Message, "Create Playlist: Error");
      ProjectData.ClearProjectError();
    }
    finally
    {
      this.sw.Close();
      fileStream.Close();
    }
  }

  private void update_playlist_trainname(string file_name, string train_no, string train_name_path)
  {
    StreamReader streamReader = new StreamReader(file_name);
    string end = streamReader.ReadToEnd();
    bool found = false;
    try
    {
      streamReader.Close();
      this.train_name_voice(train_no, train_name_path, ref found);
      if (found)
        return;
      string contents = end.Replace("</sounds>", " ");
      File.WriteAllText(file_name, contents);
      this.sw = File.AppendText(file_name);
      this.train_name_data(train_no, train_name_path);
      this.sw.WriteLine("</sounds>");
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Train name voice  added", "Msg Box", 0, 0, 0);
      this.sw.Close();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void train_name_voice(string trainno, string train_name_path, ref bool found)
  {
    int index = 0;
    int num1 = 0;
    found = false;
    string str = string.Empty;
    DataSet dataSet = new DataSet();
    if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(this.cmbLang.Text, "English", false) == 0)
    {
      str = "C:\\IPIS\\voice\\TrainNames\\TrainNameVoiceXml.xml";
      int num2 = (int) dataSet.ReadXml(str);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(this.cmbLang.Text, "Hindi", false) == 0)
    {
      str = "C:\\IPIS\\voice\\hindi\\TrainNames\\hin_TrainNameVoiceXml.xml";
      int num3 = (int) dataSet.ReadXml(str);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(this.cmbLang.Text, "Regional", false) == 0)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Strings.Trim(frmMainFormIPIS.language_selection.regional_language_name), "Telugu", false) == 0)
      {
        str = "C:\\IPIS\\voice\\regional\\TrainNames\\reg_TrainNameVoiceXml.xml";
        int num4 = (int) dataSet.ReadXml(str);
      }
      else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Strings.Trim(frmMainFormIPIS.language_selection.regional_language_name), "Oriya", false) == 0)
      {
        str = "C:\\IPIS\\voice\\regional\\oriya\\TrainNames\\oriya_TrainNameVoiceXml.xml";
        int num5 = (int) dataSet.ReadXml(str);
      }
      else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Strings.Trim(frmMainFormIPIS.language_selection.regional_language_name), "Marathi", false) == 0)
      {
        str = "C:\\IPIS\\voice\\regional\\Marathi\\TrainNames\\marathi_TrainNameVoiceXml.xml";
        int num6 = (int) dataSet.ReadXml(str);
      }
      else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Strings.Trim(frmMainFormIPIS.language_selection.regional_language_name), "Bengali", false) == 0)
      {
        str = "C:\\IPIS\\voice\\regional\\Bengali\\TrainNames\\Bengali_TrainNameVoiceXml.xml";
        int num7 = (int) dataSet.ReadXml(str);
      }
      else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Strings.Trim(frmMainFormIPIS.language_selection.regional_language_name), "Chattisgarh", false) == 0)
      {
        str = "C:\\IPIS\\voice\\regional\\Chattisgarh\\TrainNames\\Chattisgarh_TrainNameVoiceXml.xml";
        int num8 = (int) dataSet.ReadXml(str);
      }
    }
    string xml = dataSet.GetXml();
    XmlDocument xmlDocument = new XmlDocument();
    xmlDocument.LoadXml(xml);
    XmlNodeList elementsByTagName = xmlDocument.GetElementsByTagName("Trainname");
    num1 = elementsByTagName.Count;
    trainno = Strings.Trim(trainno);
    while (index < elementsByTagName.Count)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(trainno, elementsByTagName.Item(index).FirstChild.InnerText, false) == 0 && Interaction.MsgBox((object) "TrainNo voice is already there \r\nOverwrite(y/n)", MsgBoxStyle.YesNo) == MsgBoxResult.Yes)
      {
        elementsByTagName.Item(index).LastChild.InnerText = train_name_path;
        xmlDocument.Save(str);
        int num9 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Train name voice  updated", "Msg Box", 0, 0, 0);
        found = true;
        break;
      }
      checked { ++index; }
    }
  }

  private void train_name_data(string trainno, string path)
  {
    this.sw.WriteLine("\t<Trainname>");
    this.sw.WriteLine("\t\t<label>{trainno}</label>");
    this.sw.WriteLine("\t\t<data>{path}</data>");
    this.sw.WriteLine("\t</Trainname>");
  }

  private void frmAddTrainNameVoice_FormClosing(object sender, FormClosingEventArgs e)
  {
    MyProject.Forms.frmVoice.BringToFront();
  }

  private void btnExit_Click(object sender, EventArgs e)
  {
    this.Close();
    MyProject.Forms.frmVoice.BringToFront();
  }
}

}