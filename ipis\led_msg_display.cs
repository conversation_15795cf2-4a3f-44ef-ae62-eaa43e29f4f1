// Decompiled with JetBrains decompiler
// Type: ipis.led_msg_display
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Diagnostics;
using System.Drawing;

namespace ipis
{

public class led_msg_display
{
  [DebuggerNonUserCode]
  public led_msg_display()
  {
  }

  public static int GetFontType(string sType)
  {
    int fontType = 0; // Default to Regular
    try
    {
      string Left = sType;
      if (Operators.CompareString(Left, "Bold", false) == 0)
        fontType = 1;
      else if (Operators.CompareString(Left, "Italic", false) == 0)
        fontType = 2;
      else if (Operators.CompareString(Left, "Regular", false) == 0)
        fontType = 0;
      else if (Operators.CompareString(Left, "BoldItalic", false) == 0)
        fontType = 3;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    return fontType;
  }

  public static void english_led_msg_display(
    string message,
    int msg_length,
    ref byte[] def_msg_bitmap,
    ref int def_msg_length)
  {
    Led_byte_Display ledByteDisplay = new Led_byte_Display();
    try
    {
      ledByteDisplay.TADDBTimeSize = 38;
      ledByteDisplay.Columns = checked ((int) Math.Round(unchecked ((double) message.Length * Conversions.ToDouble(frmMainFormIPIS.eng_font_size))));
      ledByteDisplay.DisplayFont = new Font(frmMainFormIPIS.eng_font_name, Conversions.ToSingle(frmMainFormIPIS.eng_font_size), (FontStyle) led_msg_display.GetFontType(frmMainFormIPIS.eng_font_type), GraphicsUnit.Point);
      ledByteDisplay.DisplayTimeModes = (byte) 1;
      ledByteDisplay.XPos = 0;
      ledByteDisplay.YPos = -1;
      ledByteDisplay.Lines = 1;
      byte[] displayMsgByteArray = ledByteDisplay.GetDisplayMsgByteArray(message);
      def_msg_length = displayMsgByteArray.Length;
      int index = 0;
      while (index < displayMsgByteArray.Length)
      {
        def_msg_bitmap[index] = displayMsgByteArray[index];
        checked { ++index; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  public static void reglang_led_msg_display(
    string message,
    int msg_length,
    ref byte[] def_msg_bitmap,
    ref int def_msg_length)
  {
    Led_byte_Display ledByteDisplay = new Led_byte_Display();
    try
    {
      int num = (double) checked (24 * msg_length) / 2.0 > 336.0 ? checked ((int) Math.Round(unchecked (2.0 * Conversions.ToDouble(frmMainFormIPIS.reg_font_size) * (double) msg_length / 2.0))) : checked ((int) Math.Round(unchecked ((double) message.Length * Conversions.ToDouble(frmMainFormIPIS.reg_font_size))));
      ledByteDisplay.TADDBTimeSize = 38;
      ledByteDisplay.Columns = num;
      ledByteDisplay.DisplayFont = new Font(frmMainFormIPIS.reg_font_name, Conversions.ToSingle(frmMainFormIPIS.reg_font_size), (FontStyle) led_msg_display.GetFontType(frmMainFormIPIS.reg_font_type), GraphicsUnit.Point);
      ledByteDisplay.DisplayTimeModes = (byte) 1;
      ledByteDisplay.XPos = 0;
      if (Operators.CompareString(frmMainFormIPIS.language_selection.regional_language_name, "Telugu", false) == 0)
        ledByteDisplay.YPos = -2;
      else if (Operators.CompareString(frmMainFormIPIS.language_selection.regional_language_name, "Oriya", false) == 0)
        ledByteDisplay.YPos = -1;
      ledByteDisplay.Lines = 1;
      byte[] displayMsgByteArray = ledByteDisplay.GetDisplayMsgByteArray(message);
      def_msg_length = displayMsgByteArray.Length;
      int index = 0;
      while (index < displayMsgByteArray.Length)
      {
        def_msg_bitmap[index] = displayMsgByteArray[index];
        checked { ++index; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  public static void hindi_led_msg_display(
    string message,
    int msg_length,
    ref byte[] def_msg_bitmap,
    ref int def_msg_length)
  {
    Led_byte_Display ledByteDisplay = new Led_byte_Display();
    byte[] numArray = new byte[1000];
    try
    {
      ledByteDisplay.TADDBTimeSize = 38;
      ledByteDisplay.Columns = checked ((int) Math.Round(unchecked (2.0 * Conversions.ToDouble(frmMainFormIPIS.hindi_font_size) * (double) msg_length / 2.0)));
      ledByteDisplay.DisplayFont = new Font(frmMainFormIPIS.hindi_font_name, Conversions.ToSingle(frmMainFormIPIS.hindi_font_size), (FontStyle) led_msg_display.GetFontType(frmMainFormIPIS.hindi_font_type), GraphicsUnit.Point);
      ledByteDisplay.DisplayTimeModes = (byte) 1;
      ledByteDisplay.XPos = 0;
      ledByteDisplay.YPos = -1;
      ledByteDisplay.Lines = 1;
      byte[] displayMsgByteArray = ledByteDisplay.GetDisplayMsgByteArray(message);
      def_msg_length = displayMsgByteArray.Length;
      int index = 0;
      while (index < displayMsgByteArray.Length)
      {
        def_msg_bitmap[index] = displayMsgByteArray[index];
        checked { ++index; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  public static void reg_trainname_display(
    string message,
    ref byte[] reg_msg_bitmap,
    ref int def_msg_length)
  {
    Led_byte_Display ledByteDisplay = new Led_byte_Display();
    byte[] numArray = new byte[369];
    try
    {
      ledByteDisplay.TADDBTimeSize = 38;
      ledByteDisplay.Columns = 184;
      ledByteDisplay.DisplayFont = new Font(frmMainFormIPIS.reg_font_name, Conversions.ToSingle(frmMainFormIPIS.reg_font_size), (FontStyle) led_msg_display.GetFontType(frmMainFormIPIS.reg_font_type), GraphicsUnit.Point);
      ledByteDisplay.DisplayTimeModes = (byte) 1;
      ledByteDisplay.XPos = 0;
      ledByteDisplay.YPos = -2;
      ledByteDisplay.Lines = 1;
      ledByteDisplay.TADDBWordSpace = 0;
      byte[] trainNameByteArray = ledByteDisplay.GetDisplayTrainNameByteArray(message);
      string str = string.Empty;
      def_msg_length = trainNameByteArray.Length;
      int index = 0;
      while (index < trainNameByteArray.Length)
      {
        reg_msg_bitmap[index] = trainNameByteArray[index];
        str = "{str},{Conversions.ToString(reg_msg_bitmap[index])}";
        checked { ++index; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  public static void hin_trainname_display(
    string message,
    ref byte[] hin_msg_bitmap,
    ref int def_msg_length)
  {
    Led_byte_Display ledByteDisplay = new Led_byte_Display();
    byte[] numArray = new byte[369];
    try
    {
      ledByteDisplay.TADDBTimeSize = 38;
      ledByteDisplay.Columns = 184;
      ledByteDisplay.DisplayFont = new Font(frmMainFormIPIS.hindi_font_name, Conversions.ToSingle(frmMainFormIPIS.hindi_font_size), (FontStyle) led_msg_display.GetFontType(frmMainFormIPIS.hindi_font_type), GraphicsUnit.Point);
      ledByteDisplay.DisplayTimeModes = (byte) 1;
      ledByteDisplay.XPos = 0;
      ledByteDisplay.YPos = -1;
      ledByteDisplay.Lines = 1;
      ledByteDisplay.TADDBWordSpace = 0;
      byte[] trainNameByteArray = ledByteDisplay.GetDisplayTrainNameByteArray(message);
      string str = string.Empty;
      def_msg_length = trainNameByteArray.Length;
      int index = 0;
      while (index < trainNameByteArray.Length)
      {
        hin_msg_bitmap[index] = trainNameByteArray[index];
        str = "{str},{Conversions.ToString(hin_msg_bitmap[index])}";
        checked { ++index; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  public static void hin_train_status_display(
    string message,
    ref byte[] hin_msg_bitmap,
    ref int def_msg_length)
  {
    Led_byte_Display ledByteDisplay = new Led_byte_Display();
    byte[] numArray = new byte[157];
    try
    {
      ledByteDisplay.TADDBTimeSize = 38;
      ledByteDisplay.Columns = 78;
      ledByteDisplay.DisplayFont = new Font(frmMainFormIPIS.hindi_font_name, Conversions.ToSingle(frmMainFormIPIS.hindi_font_size), (FontStyle) led_msg_display.GetFontType(frmMainFormIPIS.hindi_font_type), GraphicsUnit.Point);
      ledByteDisplay.DisplayTimeModes = (byte) 1;
      ledByteDisplay.XPos = 0;
      ledByteDisplay.YPos = -1;
      ledByteDisplay.Lines = 1;
      ledByteDisplay.TADDBWordSpace = 0;
      byte[] trainNameByteArray = ledByteDisplay.GetDisplayTrainNameByteArray(message);
      string str = string.Empty;
      def_msg_length = trainNameByteArray.Length;
      int index = 0;
      while (index < trainNameByteArray.Length)
      {
        hin_msg_bitmap[index] = trainNameByteArray[index];
        str = "{str},{Conversions.ToString(hin_msg_bitmap[index])}";
        checked { ++index; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  public static void reg_lang_train_status_display(
    string message,
    ref byte[] reg_msg_bitmap,
    ref int def_msg_length)
  {
    Led_byte_Display ledByteDisplay = new Led_byte_Display();
    byte[] numArray = new byte[157];
    try
    {
      ledByteDisplay.TADDBTimeSize = 38;
      ledByteDisplay.Columns = 78;
      ledByteDisplay.DisplayFont = new Font(frmMainFormIPIS.reg_font_name, Conversions.ToSingle(frmMainFormIPIS.reg_font_size), (FontStyle) led_msg_display.GetFontType(frmMainFormIPIS.reg_font_type), GraphicsUnit.Point);
      ledByteDisplay.DisplayTimeModes = (byte) 1;
      ledByteDisplay.XPos = 0;
      ledByteDisplay.YPos = -2;
      ledByteDisplay.Lines = 1;
      ledByteDisplay.TADDBWordSpace = 0;
      byte[] trainNameByteArray = ledByteDisplay.GetDisplayTrainNameByteArray(message);
      string str = string.Empty;
      def_msg_length = trainNameByteArray.Length;
      int index = 0;
      while (index < trainNameByteArray.Length)
      {
        reg_msg_bitmap[index] = trainNameByteArray[index];
        str = "{str},{Conversions.ToString(reg_msg_bitmap[index])}";
        checked { ++index; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }
}

}