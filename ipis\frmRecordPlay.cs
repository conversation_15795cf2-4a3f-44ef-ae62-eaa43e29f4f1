// Decompiled with JetBrains decompiler
// Type: ipis.frmRecordPlay
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using ipis.My;
using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;

namespace ipis
{

[DesignerGenerated]
public class frmRecordPlay : Form
{
  private static List<WeakReference> __ENCList = new List<WeakReference>();
  private IContainer components;
  [AccessedThroughProperty("btnExit")]
  private Button _btnExit;
  [AccessedThroughProperty("Panel15")]
  private Panel _Panel15;
  [AccessedThroughProperty("btnPlay")]
  private Button _btnPlay;
  [AccessedThroughProperty("cmbRepeat")]
  private ComboBox _cmbRepeat;
  [AccessedThroughProperty("btnRecord")]
  private Button _btnRecord;
  [AccessedThroughProperty("btnStop")]
  private Button _btnStop;
  private byte repeat;
  private string announcefile;
  private Thread rec_voice_thread;

  [DebuggerNonUserCode]
  static frmRecordPlay()
  {
  }

  public frmRecordPlay()
  {
    frmRecordPlay.__ENCAddToList((object) this);
    this.repeat = (byte) 0;
    this.announcefile = string.Empty;
    this.InitializeComponent();
  }

  [DebuggerNonUserCode]
  private static void __ENCAddToList(object value)
  {
    lock (frmRecordPlay.__ENCList)
    {
      if (frmRecordPlay.__ENCList.Count == frmRecordPlay.__ENCList.Capacity)
      {
        int index1 = 0;
        int num = checked (frmRecordPlay.__ENCList.Count - 1);
        int index2 = 0;
        while (index2 <= num)
        {
          if (frmRecordPlay.__ENCList[index2].IsAlive)
          {
            if (index2 != index1)
              frmRecordPlay.__ENCList[index1] = frmRecordPlay.__ENCList[index2];
            checked { ++index1; }
          }
          checked { ++index2; }
        }
        frmRecordPlay.__ENCList.RemoveRange(index1, checked (frmRecordPlay.__ENCList.Count - index1));
        frmRecordPlay.__ENCList.Capacity = frmRecordPlay.__ENCList.Count;
      }
      frmRecordPlay.__ENCList.Add(new WeakReference(RuntimeHelpers.GetObjectValue(value)));
    }
  }

  [DebuggerNonUserCode]
  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  [DebuggerStepThrough]
  private void InitializeComponent()
  {
    ComponentResourceManager componentResourceManager = new ComponentResourceManager(typeof (frmRecordPlay));
    this.btnExit = new Button();
    this.Panel15 = new Panel();
    this.btnPlay = new Button();
    this.cmbRepeat = new ComboBox();
    this.btnRecord = new Button();
    this.btnStop = new Button();
    this.Panel15.SuspendLayout();
    this.SuspendLayout();
    this.btnExit.BackColor = SystemColors.ButtonFace;
    this.btnExit.DialogResult = DialogResult.Cancel;
    this.btnExit.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnExit1 = this.btnExit;
    Point point1 = new Point(123, 125);
    Point point2 = point1;
    btnExit1.Location = point2;
    this.btnExit.Name = "btnExit";
    Button btnExit2 = this.btnExit;
    Size size1 = new Size(60, 25);
    Size size2 = size1;
    btnExit2.Size = size2;
    this.btnExit.TabIndex = 5;
    this.btnExit.Text = "Exit";
    this.btnExit.UseVisualStyleBackColor = false;
    this.Panel15.BorderStyle = BorderStyle.Fixed3D;
    this.Panel15.Controls.Add((Control) this.btnPlay);
    this.Panel15.Controls.Add((Control) this.cmbRepeat);
    this.Panel15.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Panel panel15_1 = this.Panel15;
    point1 = new Point(49, 57);
    Point point3 = point1;
    panel15_1.Location = point3;
    this.Panel15.Name = "Panel15";
    Panel panel15_2 = this.Panel15;
    size1 = new Size(134, 43);
    Size size3 = size1;
    panel15_2.Size = size3;
    this.Panel15.TabIndex = 26;
    this.btnPlay.BackColor = SystemColors.ButtonFace;
    this.btnPlay.BackgroundImage = (Image) componentResourceManager.GetObject("btnPlay.BackgroundImage");
    this.btnPlay.BackgroundImageLayout = ImageLayout.Center;
    this.btnPlay.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
    this.btnPlay.ForeColor = Color.Black;
    this.btnPlay.ImageKey = "(none)";
    Button btnPlay1 = this.btnPlay;
    point1 = new Point(63 /*0x3F*/, 5);
    Point point4 = point1;
    btnPlay1.Location = point4;
    this.btnPlay.Name = "btnPlay";
    Button btnPlay2 = this.btnPlay;
    size1 = new Size(47, 25);
    Size size4 = size1;
    btnPlay2.Size = size4;
    this.btnPlay.TabIndex = 2;
    this.btnPlay.Text = "&v";
    this.btnPlay.UseVisualStyleBackColor = true;
    this.cmbRepeat.FormattingEnabled = true;
    this.cmbRepeat.Items.AddRange(new object[10]
    {
      (object) "1",
      (object) "2",
      (object) "3",
      (object) "4",
      (object) "5",
      (object) "6",
      (object) "7",
      (object) "8",
      (object) "9",
      (object) "10"
    });
    ComboBox cmbRepeat1 = this.cmbRepeat;
    point1 = new Point(6, 8);
    Point point5 = point1;
    cmbRepeat1.Location = point5;
    this.cmbRepeat.Name = "cmbRepeat";
    ComboBox cmbRepeat2 = this.cmbRepeat;
    size1 = new Size(43, 21);
    Size size5 = size1;
    cmbRepeat2.Size = size5;
    this.cmbRepeat.TabIndex = 3;
    this.cmbRepeat.Text = "1";
    this.btnRecord.BackColor = SystemColors.ButtonFace;
    this.btnRecord.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnRecord1 = this.btnRecord;
    point1 = new Point(79, 12);
    Point point6 = point1;
    btnRecord1.Location = point6;
    this.btnRecord.Name = "btnRecord";
    Button btnRecord2 = this.btnRecord;
    size1 = new Size(75, 25);
    Size size6 = size1;
    btnRecord2.Size = size6;
    this.btnRecord.TabIndex = 1;
    this.btnRecord.Text = "&Record";
    this.btnRecord.UseVisualStyleBackColor = false;
    this.btnStop.BackColor = SystemColors.ButtonFace;
    this.btnStop.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnStop1 = this.btnStop;
    point1 = new Point(42, 125);
    Point point7 = point1;
    btnStop1.Location = point7;
    this.btnStop.Name = "btnStop";
    Button btnStop2 = this.btnStop;
    size1 = new Size(60, 25);
    Size size7 = size1;
    btnStop2.Size = size7;
    this.btnStop.TabIndex = 4;
    this.btnStop.Text = "Stop";
    this.btnStop.UseVisualStyleBackColor = false;
    this.AcceptButton = (IButtonControl) this.btnRecord;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.BackColor = Color.PowderBlue;
    this.CancelButton = (IButtonControl) this.btnExit;
    size1 = new Size(215, 162);
    this.ClientSize = size1;
    this.Controls.Add((Control) this.btnStop);
    this.Controls.Add((Control) this.btnExit);
    this.Controls.Add((Control) this.Panel15);
    this.Controls.Add((Control) this.btnRecord);
    this.FormBorderStyle = FormBorderStyle.FixedToolWindow;
    this.Name = "frmRecordPlay";
    this.Text = "Record Play";
    this.Panel15.ResumeLayout(false);
    this.ResumeLayout(false);
  }

  internal virtual Button btnExit
  {
    [DebuggerNonUserCode] get { return this._btnExit; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.Button2_Click);
      if (this._btnExit != null)
        this._btnExit.Click -= eventHandler;
      this._btnExit = value;
      if (this._btnExit == null)
        return;
      this._btnExit.Click += eventHandler;
    }
  }

  internal virtual Panel Panel15
  {
    [DebuggerNonUserCode] get { return this._Panel15; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Panel15 = value; }
  }

  internal virtual Button btnPlay
  {
    [DebuggerNonUserCode] get { return this._btnPlay; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnPlay_Click);
      if (this._btnPlay != null)
        this._btnPlay.Click -= eventHandler;
      this._btnPlay = value;
      if (this._btnPlay == null)
        return;
      this._btnPlay.Click += eventHandler;
    }
  }

  internal virtual ComboBox cmbRepeat
  {
    [DebuggerNonUserCode] get { return this._cmbRepeat; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.cmbRepeat_SelectedIndexChanged);
      if (this._cmbRepeat != null)
        this._cmbRepeat.SelectedIndexChanged -= eventHandler;
      this._cmbRepeat = value;
      if (this._cmbRepeat == null)
        return;
      this._cmbRepeat.SelectedIndexChanged += eventHandler;
    }
  }

  internal virtual Button btnRecord
  {
    [DebuggerNonUserCode] get { return this._btnRecord; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnRecord_Click);
      if (this._btnRecord != null)
        this._btnRecord.Click -= eventHandler;
      this._btnRecord = value;
      if (this._btnRecord == null)
        return;
      this._btnRecord.Click += eventHandler;
    }
  }

  internal virtual Button btnStop
  {
    [DebuggerNonUserCode] get { return this._btnStop; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnStop_Click);
      if (this._btnStop != null)
        this._btnStop.Click -= eventHandler;
      this._btnStop = value;
      if (this._btnStop == null)
        return;
      this._btnStop.Click += eventHandler;
    }
  }

  private void btnRecord_Click(object sender, EventArgs e)
  {
    Process process = new Process();
    try
    {
      string str = "C:\\WINDOWS\\system32\\soundrecorder.exe";
      ProcessStartInfo processStartInfo = !File.Exists(str) ? new ProcessStartInfo("C:\\WINDOWS\\system32\\sndrec32.exe") : new ProcessStartInfo(str);
      process.StartInfo = processStartInfo;
      process.Start();
      process.WaitForExit();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void btnPlay_Click(object sender, EventArgs e)
  {
    OpenFileDialog openFileDialog = new OpenFileDialog();
    try
    {
      openFileDialog.InitialDirectory = "C:\\IPIS\\record_play_msg";
      openFileDialog.Filter = "All files (*.*)|*.*";
      openFileDialog.FilterIndex = 2;
      openFileDialog.RestoreDirectory = true;
      if (openFileDialog.ShowDialog() != DialogResult.OK)
        return;
      this.announcefile = openFileDialog.FileName;
      openFileDialog.OpenFile();
      this.rec_voice_thread = new Thread(new ThreadStart(this.rec_play));
      this.rec_voice_thread.Start();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  public void rec_play()
  {
    int num1 = 0;
    while (num1 < (int) this.repeat)
    {
      try
      {
        new Process()
        {
          StartInfo = {
            FileName = this.announcefile
          }
        }.Start();
      }
      catch (Exception ex)
      {
        ProjectData.SetProjectError(ex);
        int num2 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, this.announcefile + " is not a voice file", "Msg Box", 0, 0, 0);
        ProjectData.ClearProjectError();
      }
      checked { ++num1; }
    }
    this.rec_voice_thread.Abort();
  }

  private void Button2_Click(object sender, EventArgs e)
{
  this.Close();
}

  private void cmbRepeat_SelectedIndexChanged(object sender, EventArgs e)
  {
    this.repeat = Conversions.ToByte(this.cmbRepeat.Text);
  }

  private void btnStop_Click(object sender, EventArgs e)
  {
    try
    {
      if (!this.rec_voice_thread.IsAlive)
        return;
      MyProject.Computer.Audio.Stop();
      this.rec_voice_thread.Abort();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }
}

}