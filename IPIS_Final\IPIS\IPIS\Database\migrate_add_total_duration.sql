-- Migration: Add TotalDuration column to Advertising table
-- This migration adds a column to store the total duration of all wave files for an advertisement

-- Add TotalDuration column to Advertising table
ALTER TABLE Advertising ADD COLUMN TotalDuration REAL DEFAULT 0.0;

-- Add TotalDurationFormatted column to store formatted duration (MM:SS)
ALTER TABLE Advertising ADD COLUMN TotalDurationFormatted TEXT DEFAULT '00:00';

-- Create index for better performance when querying by duration
CREATE INDEX IF NOT EXISTS idx_advertising_total_duration 
ON Advertising(TotalDuration);

-- Update existing records to calculate their total duration
-- This will be handled by the application when it loads existing advertisements 