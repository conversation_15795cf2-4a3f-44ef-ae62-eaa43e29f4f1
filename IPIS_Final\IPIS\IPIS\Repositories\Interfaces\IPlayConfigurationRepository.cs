using System.Data;

namespace IPIS.Repositories.Interfaces
{
    public interface IPlayConfigurationRepository
    {
        DataTable GetPlayConfiguration(string trainStatus);
        void AddPlayConfiguration(string trainStatus, string trainType, bool pfAvl, bool[] languages);
        void UpdatePlayConfiguration(string trainStatus, string trainType, bool pfAvl, bool[] languages);
        void DeletePlayConfiguration(string trainStatus);
        DataTable GetAllPlayConfigurations();
    }
} 