using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SQLite;
using IPIS.Repositories.Interfaces;
using IPIS.Utils;
using IPIS.Models;

namespace IPIS.Repositories
{
    public class SQLiteStationRepository : IStationRepository
    {
        private readonly string connectionString;

        public SQLiteStationRepository()
        {
            connectionString = Database.ConnectionString;
        }

        public StationDetails GetStationDetails(string stationName)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = "SELECT * FROM Station_Details WHERE Station_Name = @StationName";
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@StationName", stationName);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            return new StationDetails
                            {
                                StationName = reader["Station_Name"].ToString(),
                                StationCode = reader["Station_Code"].ToString(),
                                AutoLoad = Convert.ToBoolean(reader["Auto_Load"]),
                                AutoLoadInterval = Convert.ToInt32(reader["AutoLoad_Interval"]),
                                AutoDeleteInterval = Convert.ToInt32(reader["AutoDelete_Interval"]),
                                AutoDeletePostInterval = Convert.ToInt32(reader["AutoDeletePost_Interval"]),
                                AutoDelete = Convert.ToBoolean(reader["Auto_Delete"]),
                                AvailablePF = Convert.ToInt32(reader["Avilable_PF"]),
                                P1 = reader["P1"].ToString(),
                                P2 = reader["P2"].ToString(),
                                P3 = reader["P3"].ToString(),
                                P4 = reader["P4"].ToString(),
                                P5 = reader["P5"].ToString(),
                                P6 = reader["P6"].ToString(),
                                P7 = reader["P7"].ToString(),
                                P8 = reader["P8"].ToString(),
                                P9 = reader["P9"].ToString(),
                                P10 = reader["P10"].ToString(),
                                Lang1Enb = Convert.ToBoolean(reader["Lang1_Enb"]),
                                Lang2Enb = Convert.ToBoolean(reader["Lang2_Enb"]),
                                FirstLanguage = reader["First_Lang"].ToString(),
                                SecondLanguage = reader["Second_Lang"].ToString(),
                                English = Convert.ToBoolean(reader["English"]),
                                Hindi = Convert.ToBoolean(reader["Hindi"]),
                                EngWaveFile = reader["EngWave_File"].ToString(),
                                HindiWaveFile = reader["HindiWave_File"].ToString(),
                                IsCurrent = reader["Is_Current"] != DBNull.Value && Convert.ToBoolean(reader["Is_Current"])
                            };
                        }
                    }
                }
            }
            return null; // Station not found
        }

        public void AddStation(StationDetails station)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = @"INSERT INTO Station_Details (
                    Station_Name, Station_Code, Auto_Load, AutoLoad_Interval, 
                    AutoDelete_Interval, AutoDeletePost_Interval, Auto_Delete, 
                    Avilable_PF, P1, P2, P3, P4, P5, P6, P7, P8, P9, P10,
                    Lang1_Enb, Lang2_Enb, First_Lang, Second_Lang, English, Hindi,
                    EngWave_File, HindiWave_File, Is_Current
                ) VALUES (
                    @StationName, @StationCode, @AutoLoad, @AutoLoadInterval,
                    @AutoDeleteInterval, @AutoDeletePostInterval, @AutoDelete,
                    @AvailablePF, @P1, @P2, @P3, @P4, @P5, @P6, @P7, @P8, @P9, @P10,
                    @Lang1Enb, @Lang2Enb, @FirstLang, @SecondLang, @English, @Hindi,
                    @EngWaveFile, @HindiWaveFile, @IsCurrent
                )";
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@StationName", station.StationName);
                    command.Parameters.AddWithValue("@StationCode", station.StationCode);
                    command.Parameters.AddWithValue("@AutoLoad", station.AutoLoad);
                    command.Parameters.AddWithValue("@AutoLoadInterval", station.AutoLoadInterval);
                    command.Parameters.AddWithValue("@AutoDeleteInterval", station.AutoDeleteInterval);
                    command.Parameters.AddWithValue("@AutoDeletePostInterval", station.AutoDeletePostInterval);
                    command.Parameters.AddWithValue("@AutoDelete", station.AutoDelete);
                    command.Parameters.AddWithValue("@AvailablePF", station.AvailablePF);
                    command.Parameters.AddWithValue("@P1", station.P1);
                    command.Parameters.AddWithValue("@P2", station.P2);
                    command.Parameters.AddWithValue("@P3", station.P3);
                    command.Parameters.AddWithValue("@P4", station.P4);
                    command.Parameters.AddWithValue("@P5", station.P5);
                    command.Parameters.AddWithValue("@P6", station.P6);
                    command.Parameters.AddWithValue("@P7", station.P7);
                    command.Parameters.AddWithValue("@P8", station.P8);
                    command.Parameters.AddWithValue("@P9", station.P9);
                    command.Parameters.AddWithValue("@P10", station.P10);
                    command.Parameters.AddWithValue("@Lang1Enb", station.Lang1Enb);
                    command.Parameters.AddWithValue("@Lang2Enb", station.Lang2Enb);
                    command.Parameters.AddWithValue("@FirstLang", station.FirstLanguage);
                    command.Parameters.AddWithValue("@SecondLang", station.SecondLanguage);
                    command.Parameters.AddWithValue("@English", station.English);
                    command.Parameters.AddWithValue("@Hindi", station.Hindi);
                    command.Parameters.AddWithValue("@EngWaveFile", station.EngWaveFile);
                    command.Parameters.AddWithValue("@HindiWaveFile", station.HindiWaveFile);
                    command.Parameters.AddWithValue("@IsCurrent", station.IsCurrent);
                    command.ExecuteNonQuery();
                }
            }
        }

        public void UpdateStation(StationDetails station)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = @"
                    UPDATE Station_Details 
                    SET Station_Code = @StationCode,
                        Auto_Load = @AutoLoad,
                        AutoLoad_Interval = @AutoLoadInterval,
                        Auto_Delete = @AutoDelete,
                        AutoDelete_Interval = @AutoDeleteInterval,
                        AutoDeletePost_Interval = @AutoDeletePostInterval,
                        Avilable_PF = @AvailablePF,
                        P1 = @P1,
                        P2 = @P2,
                        P3 = @P3,
                        P4 = @P4,
                        P5 = @P5,
                        P6 = @P6,
                        P7 = @P7,
                        P8 = @P8,
                        P9 = @P9,
                        P10 = @P10,
                        English = @English,
                        Hindi = @Hindi,
                        First_Lang = @FirstLanguage,
                        Second_Lang = @SecondLanguage,
                        Lang1_Enb = @Lang1Enb,
                        Lang2_Enb = @Lang2Enb,
                        EngWave_File = @EngWaveFile,
                        HindiWave_File = @HindiWaveFile,
                        Is_Current = @IsCurrent
                    WHERE Station_Name = @StationName";

                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@StationName", station.StationName);
                    command.Parameters.AddWithValue("@StationCode", station.StationCode);
                    command.Parameters.AddWithValue("@AutoLoad", station.AutoLoad);
                    command.Parameters.AddWithValue("@AutoLoadInterval", station.AutoLoadInterval);
                    command.Parameters.AddWithValue("@AutoDelete", station.AutoDelete);
                    command.Parameters.AddWithValue("@AutoDeleteInterval", station.AutoDeleteInterval);
                    command.Parameters.AddWithValue("@AutoDeletePostInterval", station.AutoDeletePostInterval);
                    command.Parameters.AddWithValue("@AvailablePF", station.AvailablePF);
                    command.Parameters.AddWithValue("@P1", station.P1);
                    command.Parameters.AddWithValue("@P2", station.P2);
                    command.Parameters.AddWithValue("@P3", station.P3);
                    command.Parameters.AddWithValue("@P4", station.P4);
                    command.Parameters.AddWithValue("@P5", station.P5);
                    command.Parameters.AddWithValue("@P6", station.P6);
                    command.Parameters.AddWithValue("@P7", station.P7);
                    command.Parameters.AddWithValue("@P8", station.P8);
                    command.Parameters.AddWithValue("@P9", station.P9);
                    command.Parameters.AddWithValue("@P10", station.P10);
                    command.Parameters.AddWithValue("@English", station.English);
                    command.Parameters.AddWithValue("@Hindi", station.Hindi);
                    command.Parameters.AddWithValue("@FirstLanguage", station.FirstLanguage);
                    command.Parameters.AddWithValue("@SecondLanguage", station.SecondLanguage);
                    command.Parameters.AddWithValue("@Lang1Enb", station.Lang1Enb);
                    command.Parameters.AddWithValue("@Lang2Enb", station.Lang2Enb);
                    command.Parameters.AddWithValue("@EngWaveFile", station.EngWaveFile);
                    command.Parameters.AddWithValue("@HindiWaveFile", station.HindiWaveFile);
                    command.Parameters.AddWithValue("@IsCurrent", station.IsCurrent);
                    command.ExecuteNonQuery();
                }
            }
        }

        public void DeleteStation(string stationName)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = "DELETE FROM Station_Details WHERE Station_Name = @StationName";
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@StationName", stationName);
                    command.ExecuteNonQuery();
                }
            }
        }

        public List<string> GetAllStationNames()
        {
            List<string> stationNames = new List<string>();
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = "SELECT Station_Name FROM Station_Details";
                using (var command = new SQLiteCommand(query, connection))
                {
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            stationNames.Add(reader["Station_Name"].ToString());
                        }
                    }
                }
            }
            return stationNames;
        }

        public Dictionary<string, string> GetStationNamesAndCodes()
        {
            Dictionary<string, string> stationNamesAndCodes = new Dictionary<string, string>();
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = "SELECT Station_Name, Station_Code FROM Station_Details";
                using (var command = new SQLiteCommand(query, connection))
                {
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            string stationName = reader["Station_Name"].ToString();
                            string stationCode = reader["Station_Code"].ToString();
                            stationNamesAndCodes[stationName] = stationCode;
                        }
                    }
                }
            }
            return stationNamesAndCodes;
        }

        public List<StationDetails> GetAllStations()
        {
            var stations = new List<StationDetails>();
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = "SELECT * FROM Station_Details";
                using (var command = new SQLiteCommand(query, connection))
                {
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            stations.Add(new StationDetails
                            {
                                StationName = reader["Station_Name"].ToString(),
                                StationCode = reader["Station_Code"].ToString(),
                                AutoLoad = Convert.ToBoolean(reader["Auto_Load"]),
                                AutoLoadInterval = Convert.ToInt32(reader["AutoLoad_Interval"]),
                                AutoDeleteInterval = Convert.ToInt32(reader["AutoDelete_Interval"]),
                                AutoDeletePostInterval = Convert.ToInt32(reader["AutoDeletePost_Interval"]),
                                AutoDelete = Convert.ToBoolean(reader["Auto_Delete"]),
                                AvailablePF = Convert.ToInt32(reader["Avilable_PF"]),
                                P1 = reader["P1"].ToString(),
                                P2 = reader["P2"].ToString(),
                                P3 = reader["P3"].ToString(),
                                P4 = reader["P4"].ToString(),
                                P5 = reader["P5"].ToString(),
                                P6 = reader["P6"].ToString(),
                                P7 = reader["P7"].ToString(),
                                P8 = reader["P8"].ToString(),
                                P9 = reader["P9"].ToString(),
                                P10 = reader["P10"].ToString(),
                                Lang1Enb = Convert.ToBoolean(reader["Lang1_Enb"]),
                                Lang2Enb = Convert.ToBoolean(reader["Lang2_Enb"]),
                                FirstLanguage = reader["First_Lang"].ToString(),
                                SecondLanguage = reader["Second_Lang"].ToString(),
                                English = Convert.ToBoolean(reader["English"]),
                                Hindi = Convert.ToBoolean(reader["Hindi"]),
                                EngWaveFile = reader["EngWave_File"].ToString(),
                                HindiWaveFile = reader["HindiWave_File"].ToString(),
                                IsCurrent = reader["Is_Current"] != DBNull.Value && Convert.ToBoolean(reader["Is_Current"])
                            });
                        }
                    }
                }
            }
            return stations;
        }

        public StationDetails GetCurrentStation()
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = "SELECT * FROM Station_Details WHERE Is_Current = 1 LIMIT 1";
                using (var command = new SQLiteCommand(query, connection))
                {
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            return new StationDetails
                            {
                                StationName = reader["Station_Name"].ToString(),
                                StationCode = reader["Station_Code"].ToString(),
                                AutoLoad = Convert.ToBoolean(reader["Auto_Load"]),
                                AutoLoadInterval = Convert.ToInt32(reader["AutoLoad_Interval"]),
                                AutoDeleteInterval = Convert.ToInt32(reader["AutoDelete_Interval"]),
                                AutoDeletePostInterval = Convert.ToInt32(reader["AutoDeletePost_Interval"]),
                                AutoDelete = Convert.ToBoolean(reader["Auto_Delete"]),
                                AvailablePF = Convert.ToInt32(reader["Avilable_PF"]),
                                P1 = reader["P1"].ToString(),
                                P2 = reader["P2"].ToString(),
                                P3 = reader["P3"].ToString(),
                                P4 = reader["P4"].ToString(),
                                P5 = reader["P5"].ToString(),
                                P6 = reader["P6"].ToString(),
                                P7 = reader["P7"].ToString(),
                                P8 = reader["P8"].ToString(),
                                P9 = reader["P9"].ToString(),
                                P10 = reader["P10"].ToString(),
                                Lang1Enb = Convert.ToBoolean(reader["Lang1_Enb"]),
                                Lang2Enb = Convert.ToBoolean(reader["Lang2_Enb"]),
                                FirstLanguage = reader["First_Lang"].ToString(),
                                SecondLanguage = reader["Second_Lang"].ToString(),
                                English = Convert.ToBoolean(reader["English"]),
                                Hindi = Convert.ToBoolean(reader["Hindi"]),
                                EngWaveFile = reader["EngWave_File"].ToString(),
                                HindiWaveFile = reader["HindiWave_File"].ToString(),
                                IsCurrent = true
                            };
                        }
                    }
                }
            }
            return null;
        }

        public void SetCurrentStation(string stationName)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                // Unset all current stations
                string unsetQuery = "UPDATE Station_Details SET Is_Current = 0 WHERE Is_Current = 1";
                using (var unsetCommand = new SQLiteCommand(unsetQuery, connection))
                {
                    unsetCommand.ExecuteNonQuery();
                }
                // Set the selected station as current
                string setQuery = "UPDATE Station_Details SET Is_Current = 1 WHERE Station_Name = @StationName";
                using (var setCommand = new SQLiteCommand(setQuery, connection))
                {
                    setCommand.Parameters.AddWithValue("@StationName", stationName);
                    setCommand.ExecuteNonQuery();
                }
            }
        }

        public List<StationDetails> SearchStations(string searchTerm)
        {
            var stations = new List<StationDetails>();
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = "SELECT * FROM Station_Details WHERE Station_Name LIKE @Search OR Station_Code LIKE @Search";
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Search", "%" + searchTerm + "%");
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            stations.Add(new StationDetails
                            {
                                StationName = reader["Station_Name"].ToString(),
                                StationCode = reader["Station_Code"].ToString(),
                                AutoLoad = Convert.ToBoolean(reader["Auto_Load"]),
                                AutoLoadInterval = Convert.ToInt32(reader["AutoLoad_Interval"]),
                                AutoDeleteInterval = Convert.ToInt32(reader["AutoDelete_Interval"]),
                                AutoDeletePostInterval = Convert.ToInt32(reader["AutoDeletePost_Interval"]),
                                AutoDelete = Convert.ToBoolean(reader["Auto_Delete"]),
                                AvailablePF = Convert.ToInt32(reader["Avilable_PF"]),
                                P1 = reader["P1"].ToString(),
                                P2 = reader["P2"].ToString(),
                                P3 = reader["P3"].ToString(),
                                P4 = reader["P4"].ToString(),
                                P5 = reader["P5"].ToString(),
                                P6 = reader["P6"].ToString(),
                                P7 = reader["P7"].ToString(),
                                P8 = reader["P8"].ToString(),
                                P9 = reader["P9"].ToString(),
                                P10 = reader["P10"].ToString(),
                                Lang1Enb = Convert.ToBoolean(reader["Lang1_Enb"]),
                                Lang2Enb = Convert.ToBoolean(reader["Lang2_Enb"]),
                                FirstLanguage = reader["First_Lang"].ToString(),
                                SecondLanguage = reader["Second_Lang"].ToString(),
                                English = Convert.ToBoolean(reader["English"]),
                                Hindi = Convert.ToBoolean(reader["Hindi"]),
                                EngWaveFile = reader["EngWave_File"].ToString(),
                                HindiWaveFile = reader["HindiWave_File"].ToString(),
                                IsCurrent = reader["Is_Current"] != DBNull.Value && Convert.ToBoolean(reader["Is_Current"])
                            });
                        }
                    }
                }
            }
            return stations;
        }

        public void ClearAllStations()
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = "DELETE FROM Station_Details";
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.ExecuteNonQuery();
                }
            }
        }
    }
} 