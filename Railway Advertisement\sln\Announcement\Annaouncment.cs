﻿using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Threading;
using System.Windows.Forms;
using NAudio.Wave;

namespace Announcement
{
	// Token: 0x02000004 RID: 4
	internal class Annaouncment
	{
		// Token: 0x06000027 RID: 39 RVA: 0x00005F6C File Offset: 0x0000416C
		public void Start_Announcement()
		{
			Misc_Functions misc_Functions = new Misc_Functions();
			this.DB.Update_Database("SELECT Train_No,Train_NameEng,Train_AD,Train_Status,Sch_AT,Sch_DT,Late,Exp_AT,Exp_DT,Sch_PF,AN,Div_City FROM Online_Trains");
			Annaouncment.Ann_Table = new DataTable();
			Annaouncment.Ann_Table = this.DB.Read_Database("SELECT Train_No,Train_NameEng,Train_AD,Exp_AT,Exp_DT,Late,Train_Status,Sch_PF,Div_City From Online_Trains Where AN = 'True' AND (Train_AD = 'A' OR Train_AD = 'Both') ORDER BY Exp_AT");
			Annaouncment.Ann_Table.Columns.Add("Eff_Time");
			for (int i = 0; i < Annaouncment.Ann_Table.Rows.Count; i++)
			{
				Annaouncment.Ann_Table.Rows[i]["Eff_Time"] = Annaouncment.Ann_Table.Rows[i]["Exp_AT"];
			}
			DataTable dataTable = this.DB.Read_Database("SELECT Train_No,Train_NameEng,Train_AD,Exp_AT,Exp_DT,Late,Train_Status,Sch_PF,Div_City From Online_Trains Where AN = 'True' AND Train_AD = 'D' ORDER BY Exp_DT");
			dataTable.Columns.Add("Eff_Time");
			for (int j = 0; j < dataTable.Rows.Count; j++)
			{
				dataTable.Rows[j]["Eff_Time"] = dataTable.Rows[j]["Exp_DT"];
			}
			Annaouncment.Ann_Table.Merge(dataTable);
			Annaouncment.Ann_Table.DefaultView.Sort = "Eff_Time";
			Annaouncment.Ann_Table = Annaouncment.Ann_Table.DefaultView.ToTable();
			bool flag = Annaouncment.Ann_Table.Rows.Count == 0 && Main.No_Adv == 0 && Main.No_Slg == 0;
			if (flag)
			{
				MessageBox.Show("No Train Selected for Announcment...");
				Main.No_PlayTime = 5;
			}
			else
			{
				misc_Functions.Write_Log("ANN", Annaouncment.Ann_Table, "");
				Main.AnnStart_Thread = new Thread(new ThreadStart(this.Train_Announce));
				Main.AnnStart_Thread.Start();
			}
		}

		// Token: 0x06000028 RID: 40 RVA: 0x00006138 File Offset: 0x00004338
		public void Train_Announce()
		{
			Class_Database class_Database = new Class_Database();
			DataTable dataTable = new DataTable();
			DataTable dataTable2 = new DataTable();
			bool flag = false;
			bool flag2 = false;
			bool flag3 = false;
			string text = "";
			int num = 0;
			string[] array = new string[2];
			string[] array2 = new string[100];
			string[] array3 = new string[Annaouncment.Ann_Table.Rows.Count];
			try
			{
				Directory.Delete(Main.PATH_AUDIO + "\\WAVE\\TEMP", true);
				Directory.CreateDirectory(Main.PATH_AUDIO + "\\WAVE\\TEMP");
			}
			catch
			{
			}
			bool flag4 = !Main.Flag_PAAnn;
			if (flag4)
			{
				Array.Clear(Main.PlayList, 0, Main.PlayList.Length);
				Main.MediaCount = 0;
			}
			Main.PlayList = new string[1000];
			bool flag5 = Main.Flag_AutoDelete || Annaouncment.Ann_Table.Rows.Count == 0;
			if (flag5)
			{
				Main.PlayList[0] = Main.PATH_AUDIO + "\\WAVE\\SPL\\Contact.wav";
			}
			else
			{
				Main.PlayList[0] = Main.PATH_AUDIO + "\\WAVE\\SPL\\BLANK.wav";
			}
			Main.PlayCount = 0;
			Main.MediaCount = 0;
			try
			{
				Main.MPlayer.URL = Main.PlayList[0];
				Main.MPlayer.Ctlcontrols.play();
			}
			catch
			{
			}
			Main.MediaCount++;
			dataTable = class_Database.Read_Database("SELECT First_Lang,Second_Lang FROM Station_Details");
			bool flag6 = dataTable.Rows.Count > 0;
			if (flag6)
			{
				bool flag7 = dataTable.Rows[0]["First_Lang"].ToString() == "English" || dataTable.Rows[0]["First_Lang"].ToString() == "Hindi";
				if (flag7)
				{
					array[0] = dataTable.Rows[0]["First_Lang"].ToString();
				}
				else
				{
					array[0] = "Regional";
				}
				bool flag8 = dataTable.Rows[0]["Second_Lang"].ToString() == "English" || dataTable.Rows[0]["Second_Lang"].ToString() == "Hindi";
				if (flag8)
				{
					array[1] = dataTable.Rows[0]["Second_Lang"].ToString();
				}
				else
				{
					array[1] = "Regional";
				}
				int num2 = 2;
				dataTable = class_Database.Read_Database("SELECT Lang1_Enb,Lang2_Enb FROM Station_Details");
				bool flag9 = dataTable.Rows[0]["Lang1_Enb"].ToString() == "False";
				if (flag9)
				{
					array[0] = "N.A";
				}
				bool flag10 = dataTable.Rows[0]["Lang2_Enb"].ToString() == "False";
				if (flag10)
				{
					array[1] = "N.A";
				}
				int num3 = Main.No_Slg;
				int num4 = Main.No_Adv;
				Misc_Functions misc_Functions = new Misc_Functions();
				for (int i = 0; i < Annaouncment.Ann_Table.Rows.Count; i++)
				{
					try
					{
						for (int j = 0; j < num2; j++)
						{
							bool flag11 = array[j] == "N.A";
							if (!flag11)
							{
								string text2 = Annaouncment.Ann_Table.Rows[i]["Train_Status"].ToString();
								string text3 = Annaouncment.Ann_Table.Rows[i]["Train_AD"].ToString();
								string text4 = Annaouncment.Ann_Table.Rows[i]["Train_No"].ToString();
								string text5 = Annaouncment.Ann_Table.Rows[i]["Sch_PF"].ToString();
								bool flag12 = text3 == "BOTH" || text3 == "Both";
								if (flag12)
								{
									text3 = "A";
								}
								bool flag13 = text5 == "--" || text5 == "  ";
								if (flag13)
								{
									dataTable2 = class_Database.Read_Database(string.Concat(new string[]
									{
										"SELECT ",
										array[j],
										" FROM Play_Configuration WHERE Train_Status = '",
										text2,
										"' AND Train_Type = '",
										text3,
										"' AND PF_Avl = 'False'"
									}));
								}
								else
								{
									dataTable2 = class_Database.Read_Database(string.Concat(new string[]
									{
										"SELECT ",
										array[j],
										" FROM Play_Configuration WHERE Train_Status = '",
										text2,
										"' AND Train_Type = '",
										text3,
										"' AND PF_Avl = 'True'"
									}));
								}
								array2 = dataTable2.Rows[0][0].ToString().Split(new char[]
								{
									','
								});
								for (int k = 0; k < array2.Length; k++)
								{
									bool flag14 = array2[k].Contains("TRNO");
									if (flag14)
									{
										dataTable2 = class_Database.Read_Database("SELECT Path FROM Play_Path WHERE Type = '" + array2[k] + "'");
										bool flag15 = dataTable2.Rows.Count > 0;
										if (flag15)
										{
											string text6 = Main.PATH_AUDIO + dataTable2.Rows[0][0].ToString();
											int l = 0;
											while (l < 5)
											{
												Main.PlayList[Main.MediaCount] = text6 + text4.Substring(l, 1) + ".wav";
												l++;
												Main.MediaCount++;
											}
										}
									}
									else
									{
										bool flag16 = array2[k].Contains("TRNAME");
										if (flag16)
										{
											dataTable2 = class_Database.Read_Database("SELECT Path FROM Play_Path WHERE Type = '" + array2[k] + "'");
											bool flag17 = dataTable2.Rows.Count > 0;
											if (flag17)
											{
												string text6 = Main.PATH_AUDIO + dataTable2.Rows[0][0].ToString();
												Main.PlayList[Main.MediaCount] = text6 + text4 + ".wav";
												bool flag18 = File.Exists(Main.PlayList[Main.MediaCount]);
												if (flag18)
												{
													Main.MediaCount++;
												}
												else
												{
													DataTable dataTable3 = new DataTable();
													dataTable3 = class_Database.Read_Database(" SELECT Train_Dir,Train_Type,Src_Stn,Desti_Stn From Train_Details WHERE Train_No = '" + text4 + "'");
													bool flag19 = dataTable3.Rows.Count > 0;
													if (flag19)
													{
														Main.PlayList[Main.MediaCount] = string.Concat(new string[]
														{
															Main.PATH_AUDIO,
															"\\WAVE\\",
															array[j],
															"\\",
															dataTable3.Rows[0]["Train_Dir"].ToString(),
															".wav"
														});
														Main.MediaCount++;
														string text7 = string.Concat(new string[]
														{
															Main.PATH_AUDIO,
															"\\WAVE\\",
															array[j],
															"\\CITY\\",
															dataTable3.Rows[0]["Src_Stn"].ToString(),
															".wav"
														});
														bool flag20 = File.Exists(text7);
														if (flag20)
														{
															Main.PlayList[Main.MediaCount] = text7;
															Main.MediaCount++;
														}
														else
														{
															for (int m = k; m < array2.Length; m++)
															{
																bool flag21 = array2[m].Contains("FROM");
																if (flag21)
																{
																	array2[m] = "";
																	break;
																}
															}
														}
														text7 = string.Concat(new string[]
														{
															Main.PATH_AUDIO,
															"\\WAVE\\",
															array[j],
															"\\CITY\\",
															dataTable3.Rows[0]["Desti_Stn"].ToString(),
															".wav"
														});
														bool flag22 = File.Exists(text7);
														if (flag22)
														{
															Main.PlayList[Main.MediaCount] = text7;
															Main.MediaCount++;
														}
														else
														{
															for (int n = k; n < array2.Length; n++)
															{
																bool flag23 = array2[n].Contains("TO");
																if (flag23)
																{
																	array2[n] = "";
																	break;
																}
															}
														}
														string str = dataTable3.Rows[0]["Train_Type"].ToString();
														Main.PlayList[Main.MediaCount] = Main.PATH_AUDIO + "\\WAVE\\TRAIN\\" + str + ".wav";
														Main.MediaCount++;
													}
												}
											}
										}
										else
										{
											bool flag24 = array2[k] == "EVIA";
											if (flag24)
											{
												dataTable2 = class_Database.Read_Database("SELECT Path FROM Play_Path WHERE Type = '" + array2[k] + "'");
												bool flag25 = dataTable2.Rows.Count > 0;
												if (flag25)
												{
													flag = true;
													text = Main.PATH_AUDIO + dataTable2.Rows[0][0].ToString();
												}
											}
											else
											{
												bool flag26 = array2[k] == "HVIA" || array2[k] == "RVIA";
												if (flag26)
												{
													bool flag27 = num > 0;
													if (flag27)
													{
														num = 0;
														dataTable2 = class_Database.Read_Database("SELECT Path FROM Play_Path WHERE Type = '" + array2[k] + "'");
														bool flag28 = dataTable2.Rows.Count > 0;
														if (flag28)
														{
															flag2 = false;
															text = Main.PATH_AUDIO + dataTable2.Rows[0][0].ToString();
															Main.PlayList[Main.MediaCount] = text;
															Main.MediaCount++;
														}
													}
												}
												else
												{
													bool flag29 = array2[k].Contains("CITY");
													if (flag29)
													{
														bool flag30 = false;
														string text8 = array2[k].Substring(1, 2);
														string text9 = "";
														string text10 = text8;
														string a = text10;
														if (!(a == "SC"))
														{
															if (!(a == "DC"))
															{
																if (!(a == "DI"))
																{
																	if (!(a == "Di"))
																	{
																		bool flag31 = text8.Contains("V");
																		if (flag31)
																		{
																			flag2 = true;
																			string str2 = array2[k].Substring(2, 1);
																			text9 = "Via" + str2;
																		}
																	}
																	else
																	{
																		text9 = "Div_City";
																		flag30 = true;
																	}
																}
																else
																{
																	text9 = "Div_City";
																	flag30 = true;
																}
															}
															else
															{
																text9 = "Desti_Stn";
															}
														}
														else
														{
															text9 = "Src_Stn";
														}
														bool flag32 = flag30;
														if (flag32)
														{
															dataTable2 = class_Database.Read_Database(string.Concat(new string[]
															{
																"SELECT ",
																text9,
																" FROM Online_Trains WHERE Train_No = '",
																text4,
																"'"
															}));
														}
														else
														{
															dataTable2 = class_Database.Read_Database(string.Concat(new string[]
															{
																"SELECT ",
																text9,
																" FROM Train_Data WHERE Train_No = '",
																text4,
																"'"
															}));
														}
														bool flag33 = dataTable2.Rows.Count > 0;
														if (flag33)
														{
															string text6 = Main.PATH_AUDIO + "\\WAVE\\" + array[j] + "\\CITY\\";
															string text11 = dataTable2.Rows[0][0].ToString();
															bool flag34 = Main.Stn_Code == null;
															if (flag34)
															{
																DataTable dataTable4 = class_Database.Read_Database("SELECT Station_Code from Station_Details");
																bool flag35 = dataTable4.Rows.Count > 0;
																if (flag35)
																{
																	Main.Stn_Code = dataTable4.Rows[0][0].ToString();
																}
															}
															bool flag36 = text11 == Main.Stn_Code && text8 == "DC" && array[j].ToUpper() != "ENGLISH";
															if (flag36)
															{
																flag3 = true;
															}
															bool flag37 = flag2;
															if (flag37)
															{
																flag2 = false;
																bool flag38 = text11 != "";
																if (flag38)
																{
																	num++;
																}
															}
															bool flag39 = flag;
															if (flag39)
															{
																bool flag40 = text11 != "";
																if (flag40)
																{
																	Main.PlayList[Main.MediaCount] = text;
																	Main.MediaCount++;
																}
																flag = false;
															}
															Main.PlayList[Main.MediaCount] = text6 + text11 + ".wav";
															Main.MediaCount++;
														}
													}
													else
													{
														bool flag41 = array2[k].Contains("HOUR") || array2[k].Contains("MIN");
														if (flag41)
														{
															dataTable2 = class_Database.Read_Database("SELECT Path FROM Play_Path WHERE Type = '" + array2[k] + "'");
															bool flag42 = dataTable2.Rows.Count > 0;
															if (flag42)
															{
																string text6 = Main.PATH_AUDIO + dataTable2.Rows[0][0].ToString();
																string[] array4 = new string[2];
																bool flag43 = array2[k].Contains("DHOUR") || array2[k].Contains("DMIN");
																if (flag43)
																{
																	dataTable2 = class_Database.Read_Database("SELECT Late FROM Online_Trains WHERE Train_No = '" + text4 + "'");
																}
																else
																{
																	bool flag44 = array2[k].Contains("SHOUR") || array2[k].Contains("SMIN");
																	if (flag44)
																	{
																		bool flag45 = text3 == "A";
																		if (flag45)
																		{
																			dataTable2 = class_Database.Read_Database("SELECT Sch_AT FROM Online_Trains WHERE Train_No = '" + text4 + "'");
																		}
																		else
																		{
																			dataTable2 = class_Database.Read_Database("SELECT Sch_DT FROM Online_Trains WHERE Train_No = '" + text4 + "'");
																		}
																	}
																	else
																	{
																		bool flag46 = text3 == "A";
																		if (flag46)
																		{
																			dataTable2 = class_Database.Read_Database("SELECT Exp_AT FROM Online_Trains WHERE Train_No = '" + text4 + "'");
																		}
																		else
																		{
																			dataTable2 = class_Database.Read_Database("SELECT Exp_DT FROM Online_Trains WHERE Train_No = '" + text4 + "'");
																		}
																	}
																}
																bool flag47 = dataTable2.Rows.Count > 0;
																if (flag47)
																{
																	array4 = dataTable2.Rows[0][0].ToString().Split(new char[]
																	{
																		':'
																	});
																	int num5 = int.Parse(array4[0]);
																	int num6 = int.Parse(array4[1]);
																	bool flag48 = num6 == 0;
																	if (flag48)
																	{
																		text6 = text6.Substring(0, text6.Length - 1) + "_ONLY\\";
																		Main.PlayList[Main.MediaCount] = text6 + num5.ToString() + ".wav";
																		Main.MediaCount++;
																		k++;
																	}
																	else
																	{
																		bool flag49 = array2[k].Contains("MIN");
																		if (flag49)
																		{
																			Main.PlayList[Main.MediaCount] = text6 + num6.ToString() + ".wav";
																		}
																		else
																		{
																			Main.PlayList[Main.MediaCount] = text6 + num5.ToString() + ".wav";
																		}
																		Main.MediaCount++;
																	}
																}
															}
														}
														else
														{
															bool flag50 = array2[k].Contains("PF");
															if (flag50)
															{
																bool flag51 = text2 == "PLATFORM CHANGED";
																if (flag51)
																{
																	DataTable dataTable5 = new DataTable();
																	bool flag52 = array2[k].Contains("DPF");
																	if (flag52)
																	{
																		dataTable5 = class_Database.Read_Database("SELECT Sch_PF FROM Train_Data WHERE Train_No = '" + text4 + "'");
																		text5 = dataTable5.Rows[0]["Sch_PF"].ToString();
																	}
																	else
																	{
																		dataTable5 = class_Database.Read_Database("SELECT Sch_PF FROM Online_Trains WHERE Train_No = '" + text4 + "'");
																		text5 = dataTable5.Rows[0]["Sch_PF"].ToString();
																	}
																}
																dataTable2 = class_Database.Read_Database("SELECT Path FROM Play_Path WHERE Type = '" + array2[k] + "'");
																bool flag53 = dataTable2.Rows.Count > 0;
																if (flag53)
																{
																	string text6 = Main.PATH_AUDIO + dataTable2.Rows[0][0].ToString();
																	bool flag54 = dataTable2.Rows.Count > 0;
																	if (flag54)
																	{
																		Main.PlayList[Main.MediaCount] = text6 + text5 + ".wav";
																		Main.MediaCount++;
																	}
																}
															}
															else
															{
																dataTable2 = class_Database.Read_Database("SELECT Path FROM Play_Path WHERE Type = '" + array2[k] + "'");
																string text6 = "";
																bool flag55 = dataTable2.Rows.Count > 0;
																if (flag55)
																{
																	text6 = Main.PATH_AUDIO + dataTable2.Rows[0][0].ToString();
																}
																bool flag56 = flag3;
																if (flag56)
																{
																	flag3 = false;
																	text6 = Main.PATH_AUDIO + "\\WAVE\\" + array[j] + "\\LTO.WAV";
																}
																Main.PlayList[Main.MediaCount] = text6;
																Main.MediaCount++;
															}
														}
													}
												}
											}
										}
									}
								}
							}
						}
					}
					catch (Exception ex)
					{
					}
					bool flag57 = num3 > 0;
					if (flag57)
					{
						DataTable dataTable6 = class_Database.Read_Database("SELECT * FROM Advertising WHERE Ann_Type = 'Slogans' AND Adver_Name = '" + Main.Selected_Slogan[num3 - 1] + "'");
						bool flag58 = dataTable6.Rows.Count > 0;
						if (flag58)
						{
							misc_Functions.Write_Log("ANN", null, "Slogans - " + Main.Selected_Slogan[num3 - 1]);
							bool flag59 = File.Exists(dataTable6.Rows[0]["Eng_Wave"].ToString());
							if (flag59)
							{
								Main.PlayList[Main.MediaCount] = dataTable6.Rows[0]["Eng_Wave"].ToString();
								Main.MediaCount++;
							}
							bool flag60 = File.Exists(dataTable6.Rows[0]["Hindi_Wave"].ToString());
							if (flag60)
							{
								Main.PlayList[Main.MediaCount] = dataTable6.Rows[0]["Hindi_Wave"].ToString();
								Main.MediaCount++;
							}
							num3--;
						}
					}
					bool flag61 = num4 > 0;
					if (flag61)
					{
						DataTable dataTable7 = class_Database.Read_Database("SELECT * FROM Advertising WHERE Ann_Type = 'Advertising' AND Adver_Name = '" + Main.Selected_Adv[num4 - 1] + "'");
						bool flag62 = dataTable7.Rows.Count > 0;
						if (flag62)
						{
							int num7 = 0;
							int num8 = 0;
							try
							{
								num7 = int.Parse(dataTable7.Rows[0]["Adver_Time"].ToString());
							}
							catch
							{
							}
							try
							{
								num8 = int.Parse(dataTable7.Rows[0]["Adver_Count"].ToString());
							}
							catch
							{
							}
							bool flag63 = num8 < num7;
							if (flag63)
							{
								num8++;
								class_Database.Insert_Database(string.Concat(new string[]
								{
									"UPDATE Advertising SET Adver_Count = '",
									num8.ToString(),
									"' WHERE Ann_Type = 'Advertising' AND Adver_Name = '",
									Main.Selected_Adv[num4 - 1],
									"'"
								}));
								misc_Functions.Write_Log("ANN", null, "Advertisement - " + Main.Selected_Adv[num4 - 1]);
								bool flag64 = File.Exists(dataTable7.Rows[0]["Eng_Wave"].ToString());
								if (flag64)
								{
									Main.PlayList[Main.MediaCount] = dataTable7.Rows[0]["Eng_Wave"].ToString();
									Main.MediaCount++;
								}
								bool flag65 = File.Exists(dataTable7.Rows[0]["Hindi_Wave"].ToString());
								if (flag65)
								{
									Main.PlayList[Main.MediaCount] = dataTable7.Rows[0]["Hindi_Wave"].ToString();
									Main.MediaCount++;
								}
								num4--;
							}
							else
							{
								class_Database.Insert_Database("DELETE * FROM Advertising WHERE Ann_Type = 'Advertising' AND Adver_Name = '" + Main.Selected_Adv[num4 - 1] + "'");
								num4--;
							}
						}
						else
						{
							num4--;
						}
					}
				}
				while (num3 > 0 || num4 > 0)
				{
					bool flag66 = num3 > 0;
					if (flag66)
					{
						DataTable dataTable8 = class_Database.Read_Database("SELECT * FROM Advertising WHERE Ann_Type = 'Slogans' AND Adver_Name = '" + Main.Selected_Slogan[num3 - 1] + "'");
						bool flag67 = dataTable8.Rows.Count > 0;
						if (flag67)
						{
							misc_Functions.Write_Log("ANN", null, "Slogans - " + Main.Selected_Slogan[num3 - 1]);
							bool flag68 = File.Exists(dataTable8.Rows[0]["Eng_Wave"].ToString());
							if (flag68)
							{
								Main.PlayList[Main.MediaCount] = dataTable8.Rows[0]["Eng_Wave"].ToString();
								Main.MediaCount++;
							}
							bool flag69 = File.Exists(dataTable8.Rows[0]["Hindi_Wave"].ToString());
							if (flag69)
							{
								Main.PlayList[Main.MediaCount] = dataTable8.Rows[0]["Hindi_Wave"].ToString();
								Main.MediaCount++;
							}
							num3--;
						}
					}
					bool flag70 = num4 > 0;
					if (flag70)
					{
						DataTable dataTable9 = class_Database.Read_Database("SELECT * FROM Advertising WHERE Ann_Type = 'Advertising' AND Adver_Name = '" + Main.Selected_Adv[num4 - 1] + "'");
						bool flag71 = dataTable9.Rows.Count > 0;
						if (flag71)
						{
							int num9 = 0;
							int num10 = 0;
							try
							{
								num9 = int.Parse(dataTable9.Rows[0]["Adver_Time"].ToString());
							}
							catch
							{
							}
							try
							{
								num10 = int.Parse(dataTable9.Rows[0]["Adver_Count"].ToString());
							}
							catch
							{
							}
							bool flag72 = num10 < num9;
							if (flag72)
							{
								num10++;
								class_Database.Insert_Database(string.Concat(new string[]
								{
									"UPDATE Advertising SET Adver_Count = '",
									num10.ToString(),
									"' WHERE Ann_Type = 'Advertising' AND Adver_Name = '",
									Main.Selected_Adv[num4 - 1],
									"'"
								}));
								misc_Functions.Write_Log("ANN", null, "Advertisement - " + Main.Selected_Adv[num4 - 1]);
								bool flag73 = File.Exists(dataTable9.Rows[0]["Eng_Wave"].ToString());
								if (flag73)
								{
									Main.PlayList[Main.MediaCount] = dataTable9.Rows[0]["Eng_Wave"].ToString();
									Main.MediaCount++;
								}
								bool flag74 = File.Exists(dataTable9.Rows[0]["Hindi_Wave"].ToString());
								if (flag74)
								{
									Main.PlayList[Main.MediaCount] = dataTable9.Rows[0]["Hindi_Wave"].ToString();
									Main.MediaCount++;
								}
								num4--;
							}
							else
							{
								class_Database.Insert_Database("DELETE * FROM Advertising WHERE Ann_Type = 'Advertising' AND Adver_Name = '" + Main.Selected_Adv[num4 - 1] + "'");
								num4--;
							}
						}
						else
						{
							num4--;
						}
					}
				}
				Main.Flag_AnnStarted = true;
				Main.Flag_PlayingTrain = true;
			}
		}

		// Token: 0x06000029 RID: 41 RVA: 0x000079E4 File Offset: 0x00005BE4
		public void Concatenate(string outputFile, IEnumerable<string> sourceFiles)
		{
			byte[] array = new byte[10240];
			WaveFileWriter waveFileWriter = null;
			try
			{
				foreach (string text in sourceFiles)
				{
					bool flag = text == null;
					if (flag)
					{
						break;
					}
					bool flag2 = File.Exists(text);
					if (flag2)
					{
						using (WaveFileReader waveFileReader = new WaveFileReader(text))
						{
							bool flag3 = waveFileWriter == null;
							if (flag3)
							{
								waveFileWriter = new WaveFileWriter(outputFile, waveFileReader.WaveFormat);
							}
							else
							{
								bool flag4 = !waveFileReader.WaveFormat.Equals(waveFileWriter.WaveFormat);
								if (flag4)
								{
									throw new InvalidOperationException("Can't concatenate WAV Files " + text);
								}
							}
							int count;
							while ((count = waveFileReader.Read(array, 0, array.Length)) > 0)
							{
								waveFileWriter.WriteData(array, 0, count);
							}
						}
					}
				}
			}
			finally
			{
				bool flag5 = waveFileWriter != null;
				if (flag5)
				{
					waveFileWriter.Dispose();
				}
			}
		}

		// Token: 0x04000038 RID: 56
		private Class_Database DB = new Class_Database();

		// Token: 0x04000039 RID: 57
		public static DataTable Ann_Table;
	}
}
