I'll help you analyze the code and create a detailed document about the database structure. Let me first search through the codebase to find database-related information.

Search files...
Let me search for more specific table structures and database operations.

Search files...
Based on the code analysis, I can provide a detailed document about the database structure and suggest improvements for the MySQL migration. Here's the comprehensive breakdown:

# Database Structure Analysis

## Current Database
- Type: Microsoft Access Database (.accdb)
- Location: `Data/Database.accdb`
- Connection String: `Provider=Microsoft.ACE.OLEDB.12.0;Data Source={path}\Data\Database.accdb;Persist Security Info=False;Jet OLEDB:Database Password=EDS@0615`

## Main Tables

### 1. Online_Trains
Primary table for active train information:
```sql
CREATE TABLE Online_Trains (
    Train_No VARCHAR(50) PRIMARY KEY,
    Train_NameEng VARCHAR(100),
    Train_AD VARCHAR(10),  -- Arrival/Departure indicator
    Train_Status VARCHAR(50),
    Train_Date VARCHAR(8), -- Format: ddMMyyyy
    Sch_AT VARCHAR(5),     -- Scheduled Arrival Time
    Sch_DT VARCHAR(5),     -- Scheduled Departure Time
    Late VARCHAR(50),
    Exp_AT VARCHAR(5),     -- Expected Arrival Time
    Exp_DT VARCHAR(5),     -- Expected Departure Time
    Sch_PF VARCHAR(10),    -- Platform
    AN VARCHAR(50),        -- Announcement
    Div_City VARCHAR(100)  -- Division City
)
```

### 2. Train_Data
Master table for train schedule information:
```sql
CREATE TABLE Train_Data (
    Train_No VARCHAR(50) PRIMARY KEY,
    Train_NameEng VARCHAR(100),
    Train_Type VARCHAR(50),
    Train_AD VARCHAR(10),
    Sch_AT VARCHAR(5),
    Sch_DT VARCHAR(5),
    Sch_PF VARCHAR(10),
    Src_Stn VARCHAR(100),  -- Source Station
    Desti_Stn VARCHAR(100), -- Destination Station
    Via1 VARCHAR(100),
    Via2 VARCHAR(100),
    Via3 VARCHAR(100),
    Via4 VARCHAR(100),
    All_Days BOOLEAN,
    Chk_Mon BOOLEAN,
    Chk_Tue BOOLEAN,
    Chk_Wed BOOLEAN,
    Chk_Thu BOOLEAN,
    Chk_Fri BOOLEAN,
    Chk_Sat BOOLEAN,
    Chk_Sun BOOLEAN
)
```

### 3. Advertising
Table for announcements and advertisements:
```sql
CREATE TABLE Advertising (
    Adver_Name VARCHAR(100) PRIMARY KEY,
    Ann_Type VARCHAR(50),  -- 'Advertising' or 'Slogans'
    -- Additional fields for message content
)
```

### 4. Station_Details
Table for station configuration:
```sql
CREATE TABLE Station_Details (
    Auto_Load BOOLEAN,
    AutoLoad_Interval INT,
    Auto_Delete BOOLEAN,
    AutoDelete_Interval INT,
    AutoDeletePost_Interval INT,
    Auto_Time INT,
    English BOOLEAN,
    Hindi BOOLEAN
)
```

## Suggested Improvements for MySQL Migration

1. **Data Types and Constraints**:
   - Replace VARCHAR with appropriate lengths
   - Add proper foreign key constraints
   - Use ENUM for status fields (Train_AD, Train_Status)
   - Use DATETIME for time fields instead of VARCHAR
   - Add NOT NULL constraints where appropriate

2. **Table Structure Improvements**:
```sql
-- Improved Online_Trains table
CREATE TABLE Online_Trains (
    Train_No VARCHAR(20) PRIMARY KEY,
    Train_NameEng VARCHAR(100) NOT NULL,
    Train_AD ENUM('A', 'D', 'Both') NOT NULL,
    Train_Status ENUM('RUNNING RIGHT TIME', 'DELAYED', 'CANCELLED') NOT NULL,
    Train_Date DATE NOT NULL,
    Sch_AT TIME NOT NULL,
    Sch_DT TIME NOT NULL,
    Late INT,
    Exp_AT TIME,
    Exp_DT TIME,
    Sch_PF VARCHAR(10),
    AN TEXT,
    Div_City VARCHAR(100),
    Created_At TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    Updated_At TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (Train_No) REFERENCES Train_Data(Train_No)
);

-- Improved Train_Data table
CREATE TABLE Train_Data (
    Train_No VARCHAR(20) PRIMARY KEY,
    Train_NameEng VARCHAR(100) NOT NULL,
    Train_Type VARCHAR(50) NOT NULL,
    Train_AD ENUM('A', 'D', 'Both') NOT NULL,
    Sch_AT TIME NOT NULL,
    Sch_DT TIME NOT NULL,
    Sch_PF VARCHAR(10),
    Src_Stn VARCHAR(100) NOT NULL,
    Desti_Stn VARCHAR(100) NOT NULL,
    Via1 VARCHAR(100),
    Via2 VARCHAR(100),
    Via3 VARCHAR(100),
    Via4 VARCHAR(100),
    All_Days BOOLEAN DEFAULT FALSE,
    Chk_Mon BOOLEAN DEFAULT FALSE,
    Chk_Tue BOOLEAN DEFAULT FALSE,
    Chk_Wed BOOLEAN DEFAULT FALSE,
    Chk_Thu BOOLEAN DEFAULT FALSE,
    Chk_Fri BOOLEAN DEFAULT FALSE,
    Chk_Sat BOOLEAN DEFAULT FALSE,
    Chk_Sun BOOLEAN DEFAULT FALSE,
    Created_At TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    Updated_At TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Improved Advertising table
CREATE TABLE Advertising (
    Adver_Name VARCHAR(100) PRIMARY KEY,
    Ann_Type ENUM('Advertising', 'Slogans') NOT NULL,
    Message_Content TEXT NOT NULL,
    Is_Active BOOLEAN DEFAULT TRUE,
    Created_At TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    Updated_At TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Improved Station_Details table
CREATE TABLE Station_Details (
    Station_ID INT AUTO_INCREMENT PRIMARY KEY,
    Station_Name VARCHAR(100) NOT NULL,
    Auto_Load BOOLEAN DEFAULT FALSE,
    AutoLoad_Interval INT DEFAULT 30,
    Auto_Delete BOOLEAN DEFAULT FALSE,
    AutoDelete_Interval INT DEFAULT 60,
    AutoDeletePost_Interval INT DEFAULT 30,
    Auto_Time INT DEFAULT 30,
    English BOOLEAN DEFAULT TRUE,
    Hindi BOOLEAN DEFAULT FALSE,
    Created_At TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    Updated_At TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

3. **Additional Improvements**:
   - Add indexes for frequently queried columns
   - Implement proper transaction management
   - Add user authentication and authorization tables
   - Add audit logging for important operations
   - Implement proper error handling and logging
   - Use prepared statements to prevent SQL injection
   - Add data validation constraints
   - Implement proper backup and recovery procedures

4. **Code Changes Required**:
   - Update connection string to use MySQL
   - Replace OleDb with MySql.Data.MySqlClient
   - Update SQL queries to use MySQL syntax
   - Implement proper connection pooling
   - Add proper exception handling
   - Update data access layer to use async/await
   - Implement proper logging mechanism

5. **Security Improvements**:
   - Use parameterized queries
   - Implement proper password hashing
   - Add role-based access control
   - Implement proper session management
   - Add input validation
   - Implement proper error handling
   - Add audit logging

Would you like me to provide more specific details about any of these aspects or help with implementing any of these improvements?

Let me search for user management related code in the code-ref.

Search files...
Yes, I found the user management implementation in the code-ref. Here's a detailed breakdown of the user management system:

## User Management System Analysis



### User Types and Permissions
1. **Administrator**
   - Full access to all features
   - Can manage users
   - Can access all modules

2. **Supervisor**
   - Can manage users
   - Can access reports
   - Can access station details
   - Limited access to other features

3. **Operator**
   - Can access advertising
   - Limited access to other features

4. **User**
   - Basic access to advertising
   - Limited access to other features

### Security Features
1. **Password Encryption**
   - Uses AES encryption
   - Key: "NAKV2SPBNI99212"
   - IV: [73, 118, 97, 110, 32, 77, 101, 100, 118, 101, 100, 101, 118]

2. **MAC Address Validation**
   - System checks MAC address for authorized machines
   - Special handling for "EDS" user

3. **Password Requirements**
   - Password confirmation required
   - Password hint system
   - Username uniqueness check

### User Management Features
1. **User Creation**
   - Username validation
   - Password encryption
   - Role assignment
   - Permission setting

2. **User Modification**
   - Password change
   - Role modification
   - Permission updates

3. **User Deletion**
   - User removal from system
   - Cleanup of user data

4. **Login System**
   - Username/password validation
   - Role-based access control
   - Session management
   - Login logging

## Important Note on `context` Folder:

The `context` folder is strictly for reference purposes. **No changes should be made inside this folder.** It is read-only and contains project-related reference materials.

## Styling Guidelines:

- Do NOT copy styles (e.g., colors, specific layouts) directly from provided screenshots.
- Implement custom, clean, and modern styling for all UI elements.



data/WAVE/
├── TRAIN TYPE/
│   └── (70+ train type announcement files)
│
├── SPL/
│   ├── TADA.wav
│   ├── Contact1.wav
│   ├── Contact.wav
│   ├── Contact original.wav
│   └── Bell.wav
│
├── HINDI/
│   ├── TRNO/
│   │   ├── 0.wav to 9.wav
│   │   └── A.wav
│   │
│   ├── TRNAME/
│   │   ├── Various train name announcements (e.g., jay pur Chennai.wav, Shatabdi exp.wav)
│   │   └── Train number announcements (e.g., 22998.wav, 22997.wav)
│   │
│   ├── STD/
│   │   ├── STD1.wav to STD9.wav
│   │   ├── STD10.wav to STD50.wav
│   │   └── STD5O.wav
│   │
│   ├── CITY/
│   │   ├── City code announcements (e.g., YPR.wav, VSKP.wav)
│   │   └── Full city name announcements (e.g., Triventpuram.wav)
│   │
│   ├── SLOGAN/
│   ├── PF/
│   ├── MIN/
│   ├── HOUR_Only/
│   ├── HOUR/
│   ├── DELAY/
│   ├── ADVERTISING/
│   ├── UP.wav
│   ├── LTO.wav
│   ├── HVIA.wav
│   ├── HTRN.wav
│   ├── HTO.wav
│   ├── HFROM.wav
│   └── DN.wav
│
├── ENGLISH/
│   ├── TRNO/
│   ├── TRNAME/
│   ├── STD/
│   ├── SLOGAN/
│   ├── PF/
│   ├── MIN/
│   ├── HOUR_Only/
│   ├── HOUR/
│   ├── DELAY/
│   ├── CITY/
│   ├── ADVERTISING/
│   ├── UP.wav
│   ├── PATO.wav
│   ├── EVIA.wav
│   ├── ETRN.wav
│   ├── ETO.wav
│   ├── EFROM.wav
│   └── DN.wav
│
└── Alphabets/
    └── A.wav



STD1.wav	   may I have your attention please
STD2.wav	   is running on time it is expected to arrive here at
STD3.wav	   will depart at its schedule
STD4.wav	   on
STD5.wav	   is arriving on
STD6.wav	   is arriving shortly on
STD7.wav	   is now ready for departure from
STD8.wav	   we wish you all a very happy comfortable and safe journey
STD9.wav	   is reported running late by
STD10.wav	inconvenience caused is deeply regretted
STD11.wav	is delayed by
STD12.wav	from its scheduled time
STD13.wav	the platform number of
STD14.wav	has been changed today this train will now Departed at
STD15.wav	passengers are requested to reach the new platform
STD16.wav	the time of
STD17.wav	has not yet been decided
STD18.wav	the passengers will be informed as soon as the information is available
STD19.wav	has been changed today this train will now arrive at
STD20.wav	instead
STD21.wav	has been cancelled today
STD22.wav	which was scheduled to the part
STD23.wav	has been put back till
STD24.wav	and is expected to arrive
STD25.wav	it is likely to depart from
STD26.wav	has arrive
STD27.wav	passengers are requested to take their respective seats
STD28.wav	who will be terminated
STD29.wav	due to unavoidable reasons
STD30.wav	is diverted today this train will reach
STD32.wav	thank
STD33.wav	from
STD35.wav	passengers of train number
STD36.wav	are hereby informed that today's coach positions of train Rs follows from engine
STD37.wav	passengers of platform number
STD38.wav	are requested to keep away from the platform edge unknown stopping fast train is about to pass from this platform thank you
STD39.wav	arrive
STD40.wav	has been rescheduled at
STD41.wav	first party that
STD42.wav	from platform number
STD43.wav	is standing
STD44.wav	it is expected to arrive here at
STD45.wav	at
STD46.wav	via
STD50B.wav	Mathura railway station per yatriyon ka Swagat hai aapki Yatra Safal sukhd AVN mangalmay Ho Hamen Aisi Kamna Karte Hain
STD99.wav	Mathura Junction Railway Station`   


STD1.wav,         कृपया ध्यान दीजिए
STD2.wav,         अपने निर्धारित समय से चल रही है इसके यहां
STD3.wav,         पर पहुंचने की संभावना है
STD4.wav,         अपने निर्धारित समय
STD5.wav,         सजा
STD6.wav,         कुछ ही समय में
STD7.wav,         पर आ रही है
STD8.wav,         से चलने को तैयार है
STD9.wav,         हम आपकी सफल सुखद एवं मंगलमय यात्रा की कामना करते हैं
STD10.wav,        अपने निर्धारित समय से
STD11.wav,        की देरी से चल रही है
STD12.wav,        आपको हुई असुविधा के लिए हमें खेद है
STD13.wav,        की देरी से जाएगी
STD14.wav,        के प्लेटफार्म में आज परिवर्तन किया गया है यह गाड़ी आज
STD15.wav,        के बजा
STD16.wav,        पढ़ाई की
STD17.wav,        यात्रियों से अनुरोध है कि नए प्लेटफार्म पर पहुंचे
STD18.wav,        आज रद्द कर दी गई है
STD19.wav,        का समय अभी प्राप्त नहीं हुआ है इस गाड़ी के बारे में जैसे ही कोई जानकारी प्राप्त होगी हम आपको सूचित करेंगे
STD20.wav,        जिसके जाने का समय
STD21.wav,        था अब यह गाड़ी
STD22.wav,        पर जाएगी
STD23.wav,        की देरी
STD24.wav,        जाएगी
STD25.wav,        पर
STD26.wav,        पर आ चुकी है
STD27.wav,        आज किसी कारण व
STD28.wav,        से आगे नहीं जाएगी
STD29.wav,        के मार्ग में आज परिवर्तन किया गया है यह गाड़ी आज
STD30.wav,        मथुरा जंक्शन रेलवे स्टेशन
STD31.wav,        होती हुई
STD32.wav,        हो जाएगी
STD33.wav,        इसके
STD34.wav,        के रास्ते
STD35.wav,        से आने वाली
STD36.wav,        से चलने वाली
STD37.wav,        धन्यवाद
STD38.wav,        के यात्रियों को सूचित किया जाता है कि आज ट्रेन के कोच की स्थिति इस प्रकार से है
STD39.wav,        Engine se
STD40.wav,        कृपया प्लेटफार्म क्रमण
STD41.wav,        के किनारे से दूर खड़े रहिए अभी थोड़ी ही देर में यहां ना रुकने वाली तेज गाड़ी जाने वाली है
STD42.wav,        सजा
STD43.wav,        आएगी
STD44.wav,        जिसके आने का समय
STD45.wav,        पर आएगी
STD46.wav,        अपने निर्धारित समय
STD47.wav,        से जा चुकी है
STD48.wav,        प्लेटफॉर्म क्रमण
STD49.wav,        बाराखडी
STD50.wav,        इसके यहां
STD5O.wav,        पहुंचने वाली



Now let me test the build to see if everything compiles correctly:
do not build the code for testing



================================================================================
                    IPIS ANNOUNCEMENT SYSTEM - COMPREHENSIVE HELP GUIDE
================================================================================

TABLE OF CONTENTS
=================
1. System Overview
2. Slogans Management
3. Real-Time Updates
4. Automatic Announcement System
5. Database Structure
6. Audio File Management
7. User Interface Guide
8. Configuration Settings
9. Troubleshooting
10. Technical Specifications

================================================================================
1. SYSTEM OVERVIEW
================================================================================

The IPIS (Integrated Passenger Information System) Announcement System is a 
comprehensive railway station announcement management solution that provides:

- Real-time train status management
- Multi-language announcement support (English, Hindi, Regional)
- Automatic and manual announcement playback
- Slogan and advertising management
- Dynamic audio playlist generation
- Database-driven configuration
- Timer-based automatic operations

Key Components:
- Main Control Interface (Main.cs)
- Slogan Management (Advertising.cs)
- Announcement Engine (Annaouncment.cs)
- Database Operations (Class_Database.cs)
- Real-time Updates (Timer-based)
- Audio File Management

================================================================================
2. SLOGANS MANAGEMENT
================================================================================

2.1 Slogan System Overview
--------------------------
Slogans are pre-recorded audio messages that can be played between train 
announcements. They are managed through the Advertising form and stored in 
the Advertising table with Ann_Type = 'Slogans'.

2.2 Slogan File Structure
-------------------------
Audio files are organized in the following directory structure:
- Hindi Slogans: \Data\WAVE\HINDI\SLOGAN\
- English Slogans: \Data\WAVE\ENGLISH\SLOGAN\

2.3 Adding New Slogans
----------------------
1. Open the Advertising form
2. Select "Slogans" from the Message Type dropdown
3. Enter slogan name in the Slogan Name field
4. Click "Browse" buttons to select Hindi and English audio files
5. Click "Save" to store the slogan

2.4 Slogan Playback Logic
-------------------------
Slogans are played automatically during announcement sequences:
- Selected slogans are stored in Main.Selected_Slogan array
- Playback occurs after each train announcement
- Both Hindi and English versions are played if available
- Slogans are logged with timestamps for tracking

2.5 Slogan Selection
--------------------
- Use checkboxes in the main interface to select active slogans
- Multiple slogans can be selected simultaneously
- Selection is stored in Main.No_Slg counter
- Changes are immediately reflected in the database

================================================================================
3. REAL-TIME UPDATES
================================================================================

3.1 Timer-Based Updates
-----------------------
The system uses a 1-second timer (timer1_Tick) for continuous monitoring:

Key Timer Functions:
- Countdown management (Replay_Time, Init_Time, No_PlayTime, Ann_StartDelay)
- Automatic announcement triggering
- Real-time clock display
- Grid refresh management
- Database synchronization

3.2 Grid Refresh Mechanism
--------------------------
Grid updates are managed through the Update_OT_DGV() method:

Features:
- Alternating row colors (white/light gray)
- Dynamic serial number updates
- Status option updates based on A/D selection
- Conditional field enablement
- Dynamic column visibility

3.3 Real-Time Database Updates
------------------------------
Cell value changes trigger immediate database updates:

Supported Updates:
- Train Status: Updates Train_Status column
- A/D (Arrival/Departure): Updates Train_AD and resets status
- Platform: Updates Sch_PF column
- Announcement Flag: Updates AN column
- Late Time: Updates Late, Exp_AT, Exp_DT columns

3.4 Conflict Prevention
-----------------------
- Grid refresh is delayed if a cell is being edited
- Event handlers are temporarily disabled during updates
- Dirty cell detection prevents data loss
- 3-second delay mechanism for editing conflicts

3.5 Visual Feedback
-------------------
- Real-time status indicators
- Color-coded train statuses
- Dynamic button states
- Progress indicators for operations

================================================================================
4. AUTOMATIC ANNOUNCEMENT SYSTEM
================================================================================

4.1 Automatic Playback Trigger
------------------------------
Automatic announcements are triggered when:
- DateTime.Now.Second == 0 (at the start of each minute)
- Main.Flag_AudioPlaying == false
- Main.Replay_Time == 0
- No manual playback is in progress

4.2 Auto-Load Configuration
---------------------------
Automatic train loading is controlled by:
- Main.Flag_AutoLoad: Enable/disable auto-loading
- Main.AutoLoad_Interval: Time interval in minutes
- Day-of-week flags (Chk_Mon, Chk_Tue, etc.)
- Time window calculations

4.3 Auto-Load Logic
-------------------
The system loads trains based on:
- Current time and configured interval
- Day of week restrictions
- All_Days flag for daily trains
- Scheduled arrival/departure times

4.4 Auto-Delete Features
------------------------
Automatic deletion of completed trains:
- Main.Flag_AutoDelete: Enable/disable auto-deletion
- Main.AutoDelete_Interval: Deletion interval
- Main.AutoDeletePost_Interval: Post-deletion delay
- Time-based cleanup of old records

4.5 Playlist Generation
-----------------------
Dynamic audio playlists are generated based on:
- Current train status
- Language settings
- Platform availability
- Train type (Arrival/Departure)
- Late time calculations

================================================================================
5. DATABASE STRUCTURE
================================================================================

5.1 Core Tables
---------------
Online_Trains:
- Sl_No: Serial number
- Train_No: Train number (Primary Key)
- Train_NameEng: Train name
- Train_AD: Arrival/Departure indicator
- Train_Status: Current status
- Sch_AT: Scheduled arrival time
- Sch_DT: Scheduled departure time
- Late: Late time
- Exp_AT: Expected arrival time
- Exp_DT: Expected departure time
- Sch_PF: Platform number
- AN: Announcement flag
- Div_City: Diverted city

Advertising:
- Msg_Enable: Message enable flag
- Ann_Type: Type (Advertising/Slogans)
- Adver_Name: Name of advertisement/slogan
- Hindi_Wave: Hindi audio file path
- Eng_Wave: English audio file path
- Adver_Time: Play count limit
- Adver_Count: Current play count

5.2 Configuration Tables
------------------------
Station_Details:
- Station configuration
- Language settings
- Auto-load settings
- Platform information

Play_Configuration:
- Audio sequence templates
- Status-based configurations
- Language-specific settings

Play_Path:
- Audio file path mappings
- File type associations

5.3 Data Relationships
----------------------
- Online_Trains ↔ Train_Data (master data)
- Advertising ↔ Audio files (file system)
- Play_Configuration ↔ Play_Path (audio mapping)
- Station_Details ↔ All tables (configuration)

================================================================================
6. AUDIO FILE MANAGEMENT
================================================================================

6.1 Directory Structure
-----------------------
\Data\WAVE\
├── ENGLISH\
│   ├── ADVERTISING\
│   ├── SLOGAN\
│   ├── CITY\
│   ├── TRNAME\
│   ├── TRNO\
│   ├── HOUR\
│   ├── MIN\
│   └── PF\
├── HINDI\
│   ├── ADVERTISING\
│   ├── SLOGAN\
│   ├── CITY\
│   ├── TRNAME\
│   ├── TRNO\
│   ├── HOUR\
│   ├── MIN\
│   └── PF\
├── REGIONAL\
└── SPL\

6.2 Audio File Naming Convention
--------------------------------
- Train Numbers: Individual digit files (0.wav, 1.wav, etc.)
- Train Names: Full train number files (12345.wav)
- Cities: City code files (DEL.wav, MUM.wav, etc.)
- Time: Hour and minute files (0.wav, 1.wav, etc.)
- Platforms: Platform number files (1.wav, 2.wav, etc.)

6.3 Audio Playback System
-------------------------
- Windows Media Player integration
- NAudio library for advanced audio operations
- Wave file concatenation for seamless playback
- Volume control and pause/resume functionality
- Error handling for missing files

6.4 Audio Queue Management
--------------------------
- Main.PlayList: Array of audio file paths
- Main.MediaCount: Total number of audio files
- Main.PlayCount: Current playback position
- Main.CurrentMedia: Currently playing file index

================================================================================
7. USER INTERFACE GUIDE
================================================================================

7.1 Main Control Interface
--------------------------
Grid Controls:
- Train Number: Display and edit train numbers
- Train Name: Display train names
- A/D: Arrival/Departure selection
- Status: Dynamic status dropdown
- Times: Scheduled and expected times
- Platform: Platform number selection
- AN: Announcement checkbox
- DEL: Delete button

Control Buttons:
- PLAY: Start announcement playback
- PAUSE: Pause current playback
- STOP: Stop all playback
- LOAD: Load new train
- REFRESH: Refresh grid data
- CLEAR: Clear grid

7.2 Advertising Form
--------------------
Slogan Management:
- Message Type: Select Advertising or Slogans
- Name Fields: Enter advertisement/slogan names
- Browse Buttons: Select audio files
- Save/Edit/Delete: CRUD operations
- Checkboxes: Enable/disable messages

7.3 Status Indicators
---------------------
- System Status: Current operation status
- Announcement State: Playback status
- Time Display: Real-time clock
- Progress Indicators: Operation progress

7.4 Keyboard Shortcuts
----------------------
- F5: Refresh grid
- Ctrl+N: New train
- Ctrl+S: Save changes
- Ctrl+D: Delete selected
- Space: Toggle announcement flag

================================================================================
8. CONFIGURATION SETTINGS
================================================================================

8.1 Station Configuration
-------------------------
Language Settings:
- First_Lang: Primary language
- Second_Lang: Secondary language
- Lang1_Enb: Enable primary language
- Lang2_Enb: Enable secondary language

Auto-Load Settings:
- Auto_Load: Enable automatic loading
- AutoLoad_Interval: Loading interval (minutes)
- Auto_Delete: Enable automatic deletion
- AutoDelete_Interval: Deletion interval

8.2 Platform Configuration
--------------------------
- Avilable_PF: Number of available platforms
- Platform names: P1, P2, P3, etc.
- Platform-specific announcements

8.3 Audio Configuration
-----------------------
- Volume settings
- Audio file paths
- Playback quality
- Error handling settings

8.4 System Configuration
------------------------
- Log retention period (Main.LogDays)
- Database connection settings
- File access permissions
- Performance settings

================================================================================
9. TROUBLESHOOTING
================================================================================

9.1 Common Issues
-----------------
Audio Playback Problems:
- Check audio file existence
- Verify file permissions
- Ensure correct file format (WAV)
- Check volume settings

Database Issues:
- Verify database connection
- Check table structure
- Ensure proper permissions
- Validate data integrity

Grid Update Problems:
- Check for editing conflicts
- Verify event handler registration
- Ensure proper data binding
- Check for null values

9.2 Error Handling
------------------
- File not found: Graceful fallback to default audio
- Database errors: Logged with timestamps
- Audio errors: Automatic retry mechanism
- UI errors: User-friendly error messages

9.3 Performance Issues
----------------------
- Large dataset handling
- Memory management
- Timer optimization
- Database query optimization

9.4 Logging and Debugging
-------------------------
- Comprehensive logging system
- Error tracking with timestamps
- Debug information output
- Performance monitoring

================================================================================
10. TECHNICAL SPECIFICATIONS
================================================================================

10.1 System Requirements
------------------------
Operating System: Windows 10/11
Framework: .NET Framework 4.7.2+
Database: Microsoft Access (.accdb)
Audio: Windows Media Player, NAudio
Memory: Minimum 4GB RAM
Storage: 2GB free space

10.2 File Formats
-----------------
Audio: WAV format (8kHz, 16-bit, mono)
Database: Microsoft Access (.accdb)
Logs: Text files (.txt)
Configuration: Database tables

10.3 Performance Metrics
------------------------
- Grid refresh: < 1 second
- Audio playback: Real-time
- Database updates: Immediate
- Timer accuracy: 1-second precision

10.4 Security Features
----------------------
- Database password protection
- User authentication
- Permission-based access
- Audit logging

10.5 Integration Points
-----------------------
- External train data systems
- Audio file management
- Database synchronization
- Network connectivity

================================================================================
CONCLUSION
================================================================================

The IPIS Announcement System provides a comprehensive solution for railway 
station announcement management with real-time updates, automatic operations, 
and extensive configuration options. The system is designed for reliability, 
ease of use, and scalability to meet the demands of modern railway operations.

For additional support or technical assistance, please refer to the system 
documentation or contact the development team.

================================================================================
END OF HELP DOCUMENTATION
================================================================================