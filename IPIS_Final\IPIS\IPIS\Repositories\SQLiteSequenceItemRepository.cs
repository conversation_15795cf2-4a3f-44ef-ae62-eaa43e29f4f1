using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SQLite;
using System.Linq;
using System.Threading.Tasks;
using IPIS.Models;
using IPIS.Repositories.Interfaces;
using IPIS.Utils;

namespace IPIS.Repositories
{
    public class SQLiteSequenceItemRepository : ISequenceItemRepository
    {
        public async Task<IEnumerable<SequenceItem>> GetItemsBySequenceAsync(int sequenceId)
        {
            var items = new List<SequenceItem>();
            var query = "SELECT Id, SequenceId, OrderIndex, Type, Content, Description, IsActive, CreatedAt, UpdatedAt FROM SequenceItems WHERE SequenceId = @SequenceId ORDER BY OrderIndex";

            using (var connection = Database.GetConnection())
            {
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@SequenceId", sequenceId);

                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            items.Add(new SequenceItem
                            {
                                Id = reader.GetInt32("Id"),
                                SequenceId = reader.GetInt32("SequenceId"),
                                OrderIndex = reader.GetInt32("OrderIndex"),
                                Type = (ItemType)reader.GetInt32("Type"),
                                Content = reader.GetString("Content"),
                                Description = reader.IsDBNull("Description") ? null : reader.GetString("Description"),
                                IsActive = reader.GetBoolean("IsActive"),
                                CreatedAt = reader.GetDateTime("CreatedAt"),
                                UpdatedAt = reader.IsDBNull("UpdatedAt") ? null : (DateTime?)reader.GetDateTime("UpdatedAt")
                            });
                        }
                    }
                }
            }

            return items;
        }

        public async Task<IEnumerable<SequenceItem>> GetItemsBySequenceOrderedAsync(int sequenceId)
        {
            return await GetItemsBySequenceAsync(sequenceId);
        }

        public async Task<SequenceItem> GetItemByIdAsync(int id)
        {
            var query = "SELECT Id, SequenceId, OrderIndex, Type, Content, Description, IsActive, CreatedAt, UpdatedAt FROM SequenceItems WHERE Id = @Id";

            using (var connection = Database.GetConnection())
            {
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Id", id);

                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        if (await reader.ReadAsync())
                        {
                            return new SequenceItem
                            {
                                Id = reader.GetInt32("Id"),
                                SequenceId = reader.GetInt32("SequenceId"),
                                OrderIndex = reader.GetInt32("OrderIndex"),
                                Type = (ItemType)reader.GetInt32("Type"),
                                Content = reader.GetString("Content"),
                                Description = reader.IsDBNull("Description") ? null : reader.GetString("Description"),
                                IsActive = reader.GetBoolean("IsActive"),
                                CreatedAt = reader.GetDateTime("CreatedAt"),
                                UpdatedAt = reader.IsDBNull("UpdatedAt") ? null : (DateTime?)reader.GetDateTime("UpdatedAt")
                            };
                        }
                    }
                }
            }

            return null;
        }

        public async Task<int> AddItemAsync(SequenceItem item)
        {
            var nextOrderIndex = await GetNextOrderIndexAsync(item.SequenceId);

            var query = @"INSERT INTO SequenceItems (SequenceId, OrderIndex, Type, Content, Description, IsActive, CreatedAt) 
                         VALUES (@SequenceId, @OrderIndex, @Type, @Content, @Description, @IsActive, @CreatedAt);
                         SELECT last_insert_rowid();";

            using (var connection = Database.GetConnection())
            {
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@SequenceId", item.SequenceId);
                    command.Parameters.AddWithValue("@OrderIndex", nextOrderIndex);
                    command.Parameters.AddWithValue("@Type", (int)item.Type);
                    command.Parameters.AddWithValue("@Content", item.Content);
                    command.Parameters.AddWithValue("@Description", item.Description ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@IsActive", item.IsActive);
                    command.Parameters.AddWithValue("@CreatedAt", item.CreatedAt);

                    var result = await command.ExecuteScalarAsync();
                    return Convert.ToInt32(result);
                }
            }
        }

        public async Task<bool> UpdateItemAsync(SequenceItem item)
        {
            var query = @"UPDATE SequenceItems 
                         SET SequenceId = @SequenceId, OrderIndex = @OrderIndex, Type = @Type, 
                             Content = @Content, Description = @Description, IsActive = @IsActive, UpdatedAt = @UpdatedAt 
                         WHERE Id = @Id";

            using (var connection = Database.GetConnection())
            {
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Id", item.Id);
                    command.Parameters.AddWithValue("@SequenceId", item.SequenceId);
                    command.Parameters.AddWithValue("@OrderIndex", item.OrderIndex);
                    command.Parameters.AddWithValue("@Type", (int)item.Type);
                    command.Parameters.AddWithValue("@Content", item.Content);
                    command.Parameters.AddWithValue("@Description", item.Description ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@IsActive", item.IsActive);
                    command.Parameters.AddWithValue("@UpdatedAt", DateTime.Now);

                    var rowsAffected = await command.ExecuteNonQueryAsync();
                    return rowsAffected > 0;
                }
            }
        }

        public async Task<bool> DeleteItemAsync(int id)
        {
            var query = "DELETE FROM SequenceItems WHERE Id = @Id";

            using (var connection = Database.GetConnection())
            {
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Id", id);

                    var rowsAffected = await command.ExecuteNonQueryAsync();
                    return rowsAffected > 0;
                }
            }
        }

        public async Task<bool> ReorderItemsAsync(int sequenceId, List<int> itemIds)
        {
            using (var connection = Database.GetConnection())
            {
                using (var transaction = connection.BeginTransaction())
                {
                    try
                    {
                        for (int i = 0; i < itemIds.Count; i++)
                        {
                            var query = "UPDATE SequenceItems SET OrderIndex = @OrderIndex, UpdatedAt = @UpdatedAt WHERE Id = @Id AND SequenceId = @SequenceId";
                            using (var command = new SQLiteCommand(query, connection, transaction))
                            {
                                command.Parameters.AddWithValue("@Id", itemIds[i]);
                                command.Parameters.AddWithValue("@SequenceId", sequenceId);
                                command.Parameters.AddWithValue("@OrderIndex", i + 1);
                                command.Parameters.AddWithValue("@UpdatedAt", DateTime.Now);

                                await command.ExecuteNonQueryAsync();
                            }
                        }

                        transaction.Commit();
                        return true;
                    }
                    catch
                    {
                        transaction.Rollback();
                        return false;
                    }
                }
            }
        }

        public async Task<bool> MoveItemUpAsync(int itemId)
        {
            var item = await GetItemByIdAsync(itemId);
            if (item == null || item.OrderIndex <= 1) return false;

            var items = await GetItemsBySequenceAsync(item.SequenceId);
            var itemList = items.ToList();
            var currentIndex = itemList.FindIndex(x => x.Id == itemId);
            var previousIndex = currentIndex - 1;

            if (previousIndex >= 0)
            {
                var tempOrder = itemList[currentIndex].OrderIndex;
                itemList[currentIndex].OrderIndex = itemList[previousIndex].OrderIndex;
                itemList[previousIndex].OrderIndex = tempOrder;

                await UpdateItemAsync(itemList[currentIndex]);
                await UpdateItemAsync(itemList[previousIndex]);

                return true;
            }

            return false;
        }

        public async Task<bool> MoveItemDownAsync(int itemId)
        {
            var item = await GetItemByIdAsync(itemId);
            if (item == null) return false;

            var items = await GetItemsBySequenceAsync(item.SequenceId);
            var itemList = items.ToList();
            var currentIndex = itemList.FindIndex(x => x.Id == itemId);
            var nextIndex = currentIndex + 1;

            if (nextIndex < itemList.Count)
            {
                var tempOrder = itemList[currentIndex].OrderIndex;
                itemList[currentIndex].OrderIndex = itemList[nextIndex].OrderIndex;
                itemList[nextIndex].OrderIndex = tempOrder;

                await UpdateItemAsync(itemList[currentIndex]);
                await UpdateItemAsync(itemList[nextIndex]);

                return true;
            }

            return false;
        }

        private async Task<int> GetNextOrderIndexAsync(int sequenceId)
        {
            var query = "SELECT COALESCE(MAX(OrderIndex), 0) + 1 FROM SequenceItems WHERE SequenceId = @SequenceId";

            using (var connection = Database.GetConnection())
            {
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@SequenceId", sequenceId);

                    var result = await command.ExecuteScalarAsync();
                    return Convert.ToInt32(result);
                }
            }
        }
    }
} 