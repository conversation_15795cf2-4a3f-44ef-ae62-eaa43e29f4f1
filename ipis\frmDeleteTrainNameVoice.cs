// Decompiled with JetBrains decompiler
// Type: ipis.frmDeleteTrainNameVoice
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

namespace ipis
{

[DesignerGenerated]
public class frmDeleteTrainNameVoice : Form
{
  private static List<WeakReference> __ENCList = new List<WeakReference>();
  private IContainer components;
  [AccessedThroughProperty("lblLang")]
  private Label _lblLang;
  [AccessedThroughProperty("cmbLang")]
  private ComboBox _cmbLang;
  [AccessedThroughProperty("lblArrDep")]
  private Label _lblArrDep;
  [AccessedThroughProperty("cmbArrDep")]
  private ComboBox _cmbArrDep;
  [AccessedThroughProperty("btnExit")]
  private Button _btnExit;
  [AccessedThroughProperty("lblTrainName")]
  private Label _lblTrainName;
  [AccessedThroughProperty("txttrainNo")]
  private TextBox _txttrainNo;
  [AccessedThroughProperty("btnDelete")]
  private Button _btnDelete;

  [DebuggerNonUserCode]
  static frmDeleteTrainNameVoice()
  {
  }

  [DebuggerNonUserCode]
  public frmDeleteTrainNameVoice()
  {
    frmDeleteTrainNameVoice.__ENCAddToList((object) this);
    this.InitializeComponent();
  }

  [DebuggerNonUserCode]
  private static void __ENCAddToList(object value)
  {
    lock (frmDeleteTrainNameVoice.__ENCList)
    {
      if (frmDeleteTrainNameVoice.__ENCList.Count == frmDeleteTrainNameVoice.__ENCList.Capacity)
      {
        int index1 = 0;
        int num = checked (frmDeleteTrainNameVoice.__ENCList.Count - 1);
        int index2 = 0;
        while (index2 <= num)
        {
          if (frmDeleteTrainNameVoice.__ENCList[index2].IsAlive)
          {
            if (index2 != index1)
              frmDeleteTrainNameVoice.__ENCList[index1] = frmDeleteTrainNameVoice.__ENCList[index2];
            checked { ++index1; }
          }
          checked { ++index2; }
        }
        frmDeleteTrainNameVoice.__ENCList.RemoveRange(index1, checked (frmDeleteTrainNameVoice.__ENCList.Count - index1));
        frmDeleteTrainNameVoice.__ENCList.Capacity = frmDeleteTrainNameVoice.__ENCList.Count;
      }
      frmDeleteTrainNameVoice.__ENCList.Add(new WeakReference(RuntimeHelpers.GetObjectValue(value)));
    }
  }

  [DebuggerNonUserCode]
  protected override void Dispose(bool disposing)
  {
    try
    {
      if (!disposing || this.components == null)
        return;
      this.components.Dispose();
    }
    finally
    {
      base.Dispose(disposing);
    }
  }

  [DebuggerStepThrough]
  private void InitializeComponent()
  {
    this.lblLang = new Label();
    this.cmbLang = new ComboBox();
    this.lblArrDep = new Label();
    this.cmbArrDep = new ComboBox();
    this.btnExit = new Button();
    this.lblTrainName = new Label();
    this.txttrainNo = new TextBox();
    this.btnDelete = new Button();
    this.SuspendLayout();
    this.lblLang.AutoSize = true;
    this.lblLang.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblLang1 = this.lblLang;
    Point point1 = new Point(74, 106);
    Point point2 = point1;
    lblLang1.Location = point2;
    this.lblLang.Name = "lblLang";
    Label lblLang2 = this.lblLang;
    Size size1 = new Size(77, 16 /*0x10*/);
    Size size2 = size1;
    lblLang2.Size = size2;
    this.lblLang.TabIndex = 45;
    this.lblLang.Text = "Language";
    this.cmbLang.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.cmbLang.FormattingEnabled = true;
    this.cmbLang.Items.AddRange(new object[3]
    {
      (object) "English",
      (object) "Hindi",
      (object) "Regional"
    });
    ComboBox cmbLang1 = this.cmbLang;
    point1 = new Point(201, 103);
    Point point3 = point1;
    cmbLang1.Location = point3;
    this.cmbLang.Name = "cmbLang";
    ComboBox cmbLang2 = this.cmbLang;
    size1 = new Size(91, 24);
    Size size3 = size1;
    cmbLang2.Size = size3;
    this.cmbLang.TabIndex = 3;
    this.lblArrDep.AutoSize = true;
    this.lblArrDep.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblArrDep1 = this.lblArrDep;
    point1 = new Point(23, 60);
    Point point4 = point1;
    lblArrDep1.Location = point4;
    this.lblArrDep.Name = "lblArrDep";
    Label lblArrDep2 = this.lblArrDep;
    size1 = new Size(139, 16 /*0x10*/);
    Size size4 = size1;
    lblArrDep2.Size = size4;
    this.lblArrDep.TabIndex = 43;
    this.lblArrDep.Text = "Arrival /  Departure";
    this.cmbArrDep.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.cmbArrDep.FormattingEnabled = true;
    this.cmbArrDep.Items.AddRange(new object[2]
    {
      (object) "Arrival",
      (object) "Departure"
    });
    ComboBox cmbArrDep1 = this.cmbArrDep;
    point1 = new Point(201, 60);
    Point point5 = point1;
    cmbArrDep1.Location = point5;
    this.cmbArrDep.Name = "cmbArrDep";
    ComboBox cmbArrDep2 = this.cmbArrDep;
    size1 = new Size(91, 24);
    Size size5 = size1;
    cmbArrDep2.Size = size5;
    this.cmbArrDep.TabIndex = 2;
    this.btnExit.BackColor = Color.SeaShell;
    this.btnExit.DialogResult = DialogResult.Cancel;
    this.btnExit.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.btnExit.ForeColor = SystemColors.ControlText;
    Button btnExit1 = this.btnExit;
    point1 = new Point(180, 158);
    Point point6 = point1;
    btnExit1.Location = point6;
    this.btnExit.Name = "btnExit";
    Button btnExit2 = this.btnExit;
    size1 = new Size(60, 25);
    Size size6 = size1;
    btnExit2.Size = size6;
    this.btnExit.TabIndex = 5;
    this.btnExit.Text = "Exit";
    this.btnExit.UseVisualStyleBackColor = false;
    this.lblTrainName.AutoSize = true;
    this.lblTrainName.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.lblTrainName.ForeColor = SystemColors.ControlText;
    Label lblTrainName1 = this.lblTrainName;
    point1 = new Point(84, 15);
    Point point7 = point1;
    lblTrainName1.Location = point7;
    this.lblTrainName.Name = "lblTrainName";
    Label lblTrainName2 = this.lblTrainName;
    size1 = new Size(68, 16 /*0x10*/);
    Size size7 = size1;
    lblTrainName2.Size = size7;
    this.lblTrainName.TabIndex = 42;
    this.lblTrainName.Text = "Train No";
    this.txttrainNo.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.txttrainNo.ForeColor = SystemColors.ControlText;
    TextBox txttrainNo1 = this.txttrainNo;
    point1 = new Point(201, 15);
    Point point8 = point1;
    txttrainNo1.Location = point8;
    this.txttrainNo.MaxLength = 30;
    this.txttrainNo.Name = "txttrainNo";
    TextBox txttrainNo2 = this.txttrainNo;
    size1 = new Size(91, 22);
    Size size8 = size1;
    txttrainNo2.Size = size8;
    this.txttrainNo.TabIndex = 1;
    this.btnDelete.BackColor = Color.SeaShell;
    this.btnDelete.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.btnDelete.ForeColor = SystemColors.ControlText;
    Button btnDelete1 = this.btnDelete;
    point1 = new Point(87, 158);
    Point point9 = point1;
    btnDelete1.Location = point9;
    this.btnDelete.Name = "btnDelete";
    Button btnDelete2 = this.btnDelete;
    size1 = new Size(73, 25);
    Size size9 = size1;
    btnDelete2.Size = size9;
    this.btnDelete.TabIndex = 4;
    this.btnDelete.Text = "Delete";
    this.btnDelete.UseVisualStyleBackColor = false;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.BackColor = Color.PowderBlue;
    size1 = new Size(319, 193);
    this.ClientSize = size1;
    this.Controls.Add((Control) this.lblLang);
    this.Controls.Add((Control) this.cmbLang);
    this.Controls.Add((Control) this.lblArrDep);
    this.Controls.Add((Control) this.cmbArrDep);
    this.Controls.Add((Control) this.btnExit);
    this.Controls.Add((Control) this.lblTrainName);
    this.Controls.Add((Control) this.txttrainNo);
    this.Controls.Add((Control) this.btnDelete);
    this.FormBorderStyle = FormBorderStyle.FixedToolWindow;
    this.Name = "frmDeleteTrainNameVoice";
    this.Text = "DeleteTrainNameVoice";
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  internal virtual Label lblLang
  {
    [DebuggerNonUserCode] get { return this._lblLang; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._lblLang = value; }
  }

  internal virtual ComboBox cmbLang
  {
    [DebuggerNonUserCode] get { return this._cmbLang; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbLang = value; }
  }

  internal virtual Label lblArrDep
  {
    [DebuggerNonUserCode] get { return this._lblArrDep; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblArrDep = value;
    }
  }

  internal virtual ComboBox cmbArrDep
  {
    [DebuggerNonUserCode] get { return this._cmbArrDep; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._cmbArrDep = value;
    }
  }

  internal virtual Button btnExit
  {
    [DebuggerNonUserCode] get { return this._btnExit; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._btnExit = value; }
  }

  internal virtual Label lblTrainName
  {
    [DebuggerNonUserCode] get { return this._lblTrainName; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblTrainName = value;
    }
  }

  internal virtual TextBox txttrainNo
  {
    [DebuggerNonUserCode] get { return this._txttrainNo; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txttrainNo = value;
    }
  }

  internal virtual Button btnDelete
  {
    [DebuggerNonUserCode] get { return this._btnDelete; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._btnDelete = value;
    }
  }
}

}