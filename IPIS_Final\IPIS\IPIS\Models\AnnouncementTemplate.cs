using System;
using System.Collections.Generic;

namespace IPIS.Models
{
    public class AnnouncementTemplate
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string ArrivalDeparture { get; set; } = "A"; // A for Arrival, D for Departure
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }

        // Navigation properties
        public virtual ICollection<AnnouncementSequence> Sequences { get; set; }

        public AnnouncementTemplate()
        {
            IsActive = true;
            CreatedAt = DateTime.Now;
            ArrivalDeparture = "A"; // Default to Arrival
            Sequences = new List<AnnouncementSequence>();
        }

        public override string ToString()
        {
            return Name;
        }
    }
} 