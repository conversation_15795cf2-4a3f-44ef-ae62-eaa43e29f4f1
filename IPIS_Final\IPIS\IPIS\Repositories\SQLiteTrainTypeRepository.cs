using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SQLite;
using IPIS.Repositories.Interfaces;
using IPIS.Utils;
using IPIS.Models;

namespace IPIS.Repositories
{
    public class SQLiteTrainTypeRepository : ITrainTypeRepository
    {
        private readonly string connectionString;

        public SQLiteTrainTypeRepository()
        {
            connectionString = Database.ConnectionString;
        }

        public DataTable GetAllTrainTypes()
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = "SELECT * FROM Train_Types ORDER BY Name";
                using (var command = new SQLiteCommand(query, connection))
                {
                    using (var adapter = new SQLiteDataAdapter(command))
                    {
                        var dataTable = new DataTable();
                        adapter.Fill(dataTable);
                        return dataTable;
                    }
                }
            }
        }

        public List<TrainType> GetActiveTrainTypes()
        {
            var trainTypes = new List<TrainType>();
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = "SELECT * FROM Train_Types WHERE IsActive = 1 ORDER BY Name";
                using (var command = new SQLiteCommand(query, connection))
                {
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            trainTypes.Add(new TrainType
                            {
                                ID = reader["ID"].ToString(),
                                Name = reader["Name"].ToString(),
                                Description = reader["Description"].ToString(),
                                IsActive = Convert.ToBoolean(reader["IsActive"]),
                                CreatedDate = Convert.ToDateTime(reader["CreatedDate"]),
                                ModifiedDate = reader["ModifiedDate"] != DBNull.Value ? 
                                    Convert.ToDateTime(reader["ModifiedDate"]) : (DateTime?)null
                            });
                        }
                    }
                }
            }
            return trainTypes;
        }

        public TrainType GetTrainTypeById(string id)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = "SELECT * FROM Train_Types WHERE ID = @ID";
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@ID", id);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            return new TrainType
                            {
                                ID = reader["ID"].ToString(),
                                Name = reader["Name"].ToString(),
                                Description = reader["Description"].ToString(),
                                IsActive = Convert.ToBoolean(reader["IsActive"]),
                                CreatedDate = Convert.ToDateTime(reader["CreatedDate"]),
                                ModifiedDate = reader["ModifiedDate"] != DBNull.Value ? 
                                    Convert.ToDateTime(reader["ModifiedDate"]) : (DateTime?)null
                            };
                        }
                    }
                }
            }
            return null;
        }

        public TrainType GetTrainTypeByName(string name)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = "SELECT * FROM Train_Types WHERE Name = @Name";
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Name", name);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            return new TrainType
                            {
                                ID = reader["ID"].ToString(),
                                Name = reader["Name"].ToString(),
                                Description = reader["Description"].ToString(),
                                IsActive = Convert.ToBoolean(reader["IsActive"]),
                                CreatedDate = Convert.ToDateTime(reader["CreatedDate"]),
                                ModifiedDate = reader["ModifiedDate"] != DBNull.Value ? 
                                    Convert.ToDateTime(reader["ModifiedDate"]) : (DateTime?)null
                            };
                        }
                    }
                }
            }
            return null;
        }

        public void AddTrainType(TrainType trainType)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = @"INSERT INTO Train_Types (ID, Name, Description, IsActive, CreatedDate) 
                                VALUES (@ID, @Name, @Description, @IsActive, @CreatedDate)";
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@ID", trainType.ID);
                    command.Parameters.AddWithValue("@Name", trainType.Name);
                    command.Parameters.AddWithValue("@Description", trainType.Description ?? string.Empty);
                    command.Parameters.AddWithValue("@IsActive", trainType.IsActive);
                    command.Parameters.AddWithValue("@CreatedDate", trainType.CreatedDate);
                    command.ExecuteNonQuery();
                }
            }
        }

        public void UpdateTrainType(TrainType trainType)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = @"UPDATE Train_Types 
                                SET Name = @Name, Description = @Description, IsActive = @IsActive, ModifiedDate = @ModifiedDate 
                                WHERE ID = @ID";
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@ID", trainType.ID);
                    command.Parameters.AddWithValue("@Name", trainType.Name);
                    command.Parameters.AddWithValue("@Description", trainType.Description ?? string.Empty);
                    command.Parameters.AddWithValue("@IsActive", trainType.IsActive);
                    command.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);
                    command.ExecuteNonQuery();
                }
            }
        }

        public void DeleteTrainType(string id)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = "DELETE FROM Train_Types WHERE ID = @ID";
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@ID", id);
                    command.ExecuteNonQuery();
                }
            }
        }

        public bool TrainTypeExists(string name, string excludeId = null)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = "SELECT COUNT(*) FROM Train_Types WHERE Name = @Name";
                if (!string.IsNullOrEmpty(excludeId))
                {
                    query += " AND ID != @ExcludeID";
                }

                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Name", name);
                    if (!string.IsNullOrEmpty(excludeId))
                    {
                        command.Parameters.AddWithValue("@ExcludeID", excludeId);
                    }
                    return Convert.ToInt32(command.ExecuteScalar()) > 0;
                }
            }
        }
    }
}
