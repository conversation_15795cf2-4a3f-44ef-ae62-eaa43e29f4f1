using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions; // For basic regex parsing
using System.Threading.Tasks;
using System.Windows.Forms;
using ipis_V2_jules.ApiClients; // For ErailApiClient

namespace ipis_V2_jules
{
    public partial class PnrStatusForm : Form
    {
        private Label lblPnrNumber;
        private TextBox txtPnrNumber;
        private Button btnGetPnrStatus;
        private RichTextBox rtbPnrDetails;

        private ErailApiClient _apiClient;

        public PnrStatusForm()
        {
            InitializeComponent(); // To be defined in PnrStatusForm.Designer.cs
            _apiClient = new ErailApiClient();
        }

        private async void btnGetPnrStatus_Click(object sender, EventArgs e)
        {
            string pnrNo = txtPnrNumber.Text.Trim();

            if (string.IsNullOrWhiteSpace(pnrNo))
            {
                rtbPnrDetails.Text = "Please enter a PNR number.";
                return;
            }

            if (pnrNo.Length != 10 || !pnrNo.All(char.IsDigit))
            {
                rtbPnrDetails.Text = "PNR number must be 10 digits.";
                return;
            }

            rtbPnrDetails.Text = $"Fetching PNR status for {pnrNo}...";
            Application.DoEvents(); // Force UI update

            try
            {
                string htmlResponse = await _apiClient.GetPnrStatusHtmlAsync(pnrNo);

                if (!string.IsNullOrEmpty(htmlResponse))
                {
                    // Basic HTML Parsing Attempt
                    // IMPORTANT: This is a very rudimentary parsing attempt.
                    // For production, use a robust HTML parsing library (e.g., HtmlAgilityPack, AngleSharp)
                    // and adapt to the specific structure of the confirmtkt.com PNR page.

                    var details = new StringBuilder();
                    bool foundDetails = false;

                    // Example patterns to search for (these are guesses and will likely need adjustment)
                    string[] patterns = {
                        "Current Status:", "Train Name:", "Booking Status:", "Passenger Name:", "Journey Date:", "From Station:", "To Station:"
                    };

                    foreach (string pattern in patterns)
                    {
                        // Simple regex to find pattern and capture text until next '<' or newline
                        // This is very basic and might not work well with complex HTML structures.
                        Match match = Regex.Match(htmlResponse, $@"{Regex.Escape(pattern)}\s*(.*?)(?=<|\n)", RegexOptions.IgnoreCase | RegexOptions.Singleline);
                        if (match.Success && match.Groups.Count > 1)
                        {
                            string value = match.Groups[1].Value.Trim();
                            // Clean up common HTML entities (very basic)
                            value = value.Replace("&nbsp;", " ").Replace("&amp;", "&");
                            // Remove any remaining HTML tags (very basic)
                            value = Regex.Replace(value, "<.*?>", string.Empty).Trim();

                            if (!string.IsNullOrWhiteSpace(value))
                            {
                                details.AppendLine($"{pattern} {value}");
                                foundDetails = true;
                            }
                        }
                    }

                    if (foundDetails)
                    {
                        rtbPnrDetails.Text = "PNR Details (Basic Parse):\n--------------------------\n" + details.ToString();
                        rtbPnrDetails.AppendText("\n\nNote: This is a basic parse. For full details, refer to the source website or use a dedicated PNR app.");
                    }
                    else
                    {
                        rtbPnrDetails.Text = "Raw HTML Data (Parsing Incomplete - key details not found with basic patterns):\n-----------------------------------------------------------------\n" +
                                             htmlResponse.Substring(0, Math.Min(htmlResponse.Length, 1000)) + "...";
                        rtbPnrDetails.AppendText("\n\nNote: A proper HTML parsing library is needed for reliable extraction.");
                    }
                }
                else
                {
                    rtbPnrDetails.Text = "Failed to fetch PNR status. API response was null or empty. Check console for details.";
                }
            }
            catch (Exception ex)
            {
                rtbPnrDetails.Text = $"An error occurred while fetching PNR status: {ex.Message}\n\nCheck console output for more details.";
                Console.WriteLine($"Exception in btnGetPnrStatus_Click: {ex}");
            }
        }
    }
}
