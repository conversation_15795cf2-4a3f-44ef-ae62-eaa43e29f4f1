7:10:19 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:10:19 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND IS SUCCESSFUL
7:10:19 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:10:19 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND IS SUCCESSFUL
7:13:19 AM    MLDB Address:53 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
7:13:19 AM    MLDB Address:50LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:13:19 AM    MLDB Address:50LINK CHECK COMMAND IS SUCCESSFUL
7:14:03 AM   MDCH Address:239 SOFT RESET COMMAND IS SUCCESSFUL
7:14:08 AM   MDCH Address:239 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
7:14:08 AM   MDCH Address:239 SET CONFIGURATION COMMAND IS SUCCESSFUL
7:14:09 AM   MDCH Address:239 GET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
7:14:09 AM   MDCH Address:239 GET CONFIGURATION COMMAND IS SUCCESSFUL
7:14:10 AM   MDCH Address:239LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:14:10 AM   MDCH Address:239LINK CHECK COMMAND IS SUCCESSFUL
7:14:14 AM    MLDB Address:53 SOFT RESET COMMAND IS SUCCESSFUL
7:14:29 AM    MLDB Address:53 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
7:14:58 AM    MLDB Address:53 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
7:15:26 AM    MLDB Address:53 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
7:17:19 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:17:19 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND IS SUCCESSFUL
7:17:19 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:17:19 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND IS SUCCESSFUL
7:19:19 AM   PLATFORM NO: AGDB Address:33LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:19:19 AM   PLATFORM NO: AGDB Address:33LINK CHECK COMMAND IS SUCCESSFUL
7:19:27 AM   PLATFORM NO: AGDB Address:36 LINK FAILURE or ADDRESSED AGDB DOESn't EXIST
7:19:27 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:19:27 AM   PLATFORM NO: AGDB Address:36LINK CHECK RESPONSE PACKET: CHECKSUM FAILED 
7:21:04 AM    MLDB Address:53 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
7:21:08 AM    MLDB Address:50 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
7:21:08 AM    MLDB Address:50 SET CONFIGURATION COMMAND IS SUCCESSFUL
7:21:10 AM    MLDB Address:50 DATA PACKET STATUS: Packet Received and proceed successfully 
7:21:10 AM    MLDB Address:50 DATA PACKET IS SUCCESSFUL
7:21:12 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET STATUS: Packet Received and proceed successfully 
7:21:12 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET IS SUCCESSFUL
7:21:20 AM    MLDB Address:53 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
7:21:22 AM    MLDB Address:50 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
7:21:22 AM    MLDB Address:50 SET CONFIGURATION COMMAND IS SUCCESSFUL
7:21:24 AM    MLDB Address:50 DATA PACKET STATUS: Packet Received and proceed successfully 
7:21:24 AM    MLDB Address:50 DATA PACKET IS SUCCESSFUL
7:21:26 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET STATUS: Packet Received and proceed successfully 
7:21:26 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET IS SUCCESSFUL
7:21:34 AM    MLDB Address:53 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
7:21:35 AM    MLDB Address:50 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
7:21:35 AM    MLDB Address:50 SET CONFIGURATION COMMAND IS SUCCESSFUL
7:21:37 AM    MLDB Address:50 DATA PACKET STATUS: Packet Received and proceed successfully 
7:21:37 AM    MLDB Address:50 DATA PACKET IS SUCCESSFUL
7:21:39 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET STATUS: Packet Received and proceed successfully 
7:21:39 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET IS SUCCESSFUL
7:21:48 AM    MLDB Address:53 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
7:23:54 AM    MLDB Address:50 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
7:23:54 AM    MLDB Address:50 SET CONFIGURATION COMMAND IS SUCCESSFUL
7:23:56 AM    MLDB Address:50 DATA PACKET STATUS: Packet Received and proceed successfully 
7:23:56 AM    MLDB Address:50 DATA PACKET IS SUCCESSFUL
7:23:58 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET STATUS: Packet Received and proceed successfully 
7:23:58 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET IS SUCCESSFUL
7:25:27 AM   PLATFORM NO: CGDB Address:112LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:25:27 AM   PLATFORM NO: CGDB Address:112LINK CHECK COMMAND IS SUCCESSFUL
7:25:28 AM   PLATFORM NO: CGDB Address:111LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:25:28 AM   PLATFORM NO: CGDB Address:111LINK CHECK COMMAND IS SUCCESSFUL
7:25:28 AM   PLATFORM NO: CGDB Address:110LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:25:28 AM   PLATFORM NO: CGDB Address:110LINK CHECK COMMAND IS SUCCESSFUL
7:25:28 AM   PLATFORM NO: CGDB Address:109LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:25:28 AM   PLATFORM NO: CGDB Address:109LINK CHECK COMMAND IS SUCCESSFUL
7:25:28 AM   PLATFORM NO: CGDB Address:108LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:25:28 AM   PLATFORM NO: CGDB Address:108LINK CHECK COMMAND IS SUCCESSFUL
7:25:28 AM   PLATFORM NO: CGDB Address:107LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:25:28 AM   PLATFORM NO: CGDB Address:107LINK CHECK COMMAND IS SUCCESSFUL
7:25:28 AM   PLATFORM NO: CGDB Address:106LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:25:28 AM   PLATFORM NO: CGDB Address:106LINK CHECK COMMAND IS SUCCESSFUL
7:25:28 AM   PLATFORM NO: CGDB Address:105LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:25:28 AM   PLATFORM NO: CGDB Address:105LINK CHECK COMMAND IS SUCCESSFUL
7:25:28 AM   PLATFORM NO: CGDB Address:104LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:25:28 AM   PLATFORM NO: CGDB Address:104LINK CHECK COMMAND IS SUCCESSFUL
7:25:29 AM   PLATFORM NO: CGDB Address:103LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:25:29 AM   PLATFORM NO: CGDB Address:103LINK CHECK COMMAND IS SUCCESSFUL
7:25:29 AM   PLATFORM NO: CGDB Address:102LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:25:29 AM   PLATFORM NO: CGDB Address:102LINK CHECK COMMAND IS SUCCESSFUL
7:25:29 AM   PLATFORM NO: CGDB Address:101LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:25:29 AM   PLATFORM NO: CGDB Address:101LINK CHECK COMMAND IS SUCCESSFUL
7:25:29 AM   PLATFORM NO: CGDB Address:100LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:25:29 AM   PLATFORM NO: CGDB Address:100LINK CHECK COMMAND IS SUCCESSFUL
7:25:29 AM   PLATFORM NO: CGDB Address:99LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:25:29 AM   PLATFORM NO: CGDB Address:99LINK CHECK COMMAND IS SUCCESSFUL
7:25:29 AM   PLATFORM NO: CGDB Address:97LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:25:29 AM   PLATFORM NO: CGDB Address:97LINK CHECK COMMAND IS SUCCESSFUL
7:25:29 AM   PLATFORM NO: CGDB Address:96LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:25:29 AM   PLATFORM NO: CGDB Address:96LINK CHECK COMMAND IS SUCCESSFUL
7:25:29 AM   PLATFORM NO: CGDB Address:95LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:25:29 AM   PLATFORM NO: CGDB Address:95LINK CHECK COMMAND IS SUCCESSFUL
7:25:30 AM   PLATFORM NO: CGDB Address:94LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:25:30 AM   PLATFORM NO: CGDB Address:94LINK CHECK COMMAND IS SUCCESSFUL
7:25:30 AM   PLATFORM NO: CGDB Address:93LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:25:30 AM   PLATFORM NO: CGDB Address:93LINK CHECK COMMAND IS SUCCESSFUL
7:25:30 AM   PLATFORM NO: CGDB Address:92LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:25:30 AM   PLATFORM NO: CGDB Address:92LINK CHECK COMMAND IS SUCCESSFUL
7:25:30 AM   PLATFORM NO: CGDB Address:91LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:25:30 AM   PLATFORM NO: CGDB Address:91LINK CHECK COMMAND IS SUCCESSFUL
7:25:38 AM   PLATFORM NO: CGDB Address:90 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
7:25:46 AM   PLATFORM NO: CGDB Address:89 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
7:25:54 AM   PLATFORM NO: CGDB Address:88 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
7:25:54 AM   PLATFORM NO: CGDB Address:87LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:25:54 AM   PLATFORM NO: CGDB Address:87LINK CHECK COMMAND IS SUCCESSFUL
7:25:54 AM   PLATFORM NO: CGDB Address:86LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:25:54 AM   PLATFORM NO: CGDB Address:86LINK CHECK COMMAND IS SUCCESSFUL
7:25:54 AM   PLATFORM NO: CGDB Address:85LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:25:54 AM   PLATFORM NO: CGDB Address:85LINK CHECK COMMAND IS SUCCESSFUL
7:25:54 AM   PLATFORM NO: CGDB Address:84LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:25:54 AM   PLATFORM NO: CGDB Address:84LINK CHECK COMMAND IS SUCCESSFUL
7:25:55 AM   PLATFORM NO: CGDB Address:83LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:25:55 AM   PLATFORM NO: CGDB Address:83LINK CHECK COMMAND IS SUCCESSFUL
7:26:03 AM   PLATFORM NO: CGDB Address:82 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
7:26:03 AM   PLATFORM NO: CGDB Address:81LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:26:03 AM   PLATFORM NO: CGDB Address:81LINK CHECK COMMAND IS SUCCESSFUL
7:26:03 AM   PLATFORM NO: CGDB Address:80LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:26:03 AM   PLATFORM NO: CGDB Address:80LINK CHECK COMMAND IS SUCCESSFUL
7:26:03 AM   PLATFORM NO: CGDB Address:79LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:26:03 AM   PLATFORM NO: CGDB Address:79LINK CHECK COMMAND IS SUCCESSFUL
7:26:03 AM   PLATFORM NO: CGDB Address:78LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:26:03 AM   PLATFORM NO: CGDB Address:78LINK CHECK COMMAND IS SUCCESSFUL
7:26:03 AM   PLATFORM NO: CGDB Address:77LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:26:03 AM   PLATFORM NO: CGDB Address:77LINK CHECK COMMAND IS SUCCESSFUL
7:26:03 AM   PLATFORM NO: CGDB Address:76LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:26:03 AM   PLATFORM NO: CGDB Address:76LINK CHECK COMMAND IS SUCCESSFUL
7:26:03 AM   PLATFORM NO: CGDB Address:75LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:26:03 AM   PLATFORM NO: CGDB Address:75LINK CHECK COMMAND IS SUCCESSFUL
7:26:04 AM   PLATFORM NO: CGDB Address:74LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:26:04 AM   PLATFORM NO: CGDB Address:74LINK CHECK COMMAND IS SUCCESSFUL
7:26:04 AM   PLATFORM NO: CGDB Address:73LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:26:04 AM   PLATFORM NO: CGDB Address:73LINK CHECK COMMAND IS SUCCESSFUL
7:26:04 AM   PLATFORM NO: CGDB Address:72LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:26:04 AM   PLATFORM NO: CGDB Address:72LINK CHECK COMMAND IS SUCCESSFUL
7:26:04 AM   PLATFORM NO: CGDB Address:71LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:26:04 AM   PLATFORM NO: CGDB Address:71LINK CHECK COMMAND IS SUCCESSFUL
7:26:04 AM   PLATFORM NO: CGDB Address:70LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:26:04 AM   PLATFORM NO: CGDB Address:70LINK CHECK COMMAND IS SUCCESSFUL
7:26:04 AM   PLATFORM NO: CGDB Address:69LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:26:04 AM   PLATFORM NO: CGDB Address:69LINK CHECK COMMAND IS SUCCESSFUL
7:26:12 AM   PLATFORM NO: CGDB Address:68 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
7:26:20 AM   PLATFORM NO: CGDB Address:67 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
7:26:28 AM   PLATFORM NO: CGDB Address:66 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
7:26:36 AM   PLATFORM NO: CGDB Address:65 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
7:26:44 AM   PLATFORM NO: CGDB Address:64 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
7:28:52 AM    MLDB Address:53 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
7:28:52 AM    MLDB Address:50LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:28:52 AM    MLDB Address:50LINK CHECK COMMAND IS SUCCESSFUL
7:30:52 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:30:52 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND IS SUCCESSFUL
7:30:52 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:30:52 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND IS SUCCESSFUL
7:32:53 AM   PLATFORM NO: AGDB Address:33LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:32:53 AM   PLATFORM NO: AGDB Address:33LINK CHECK COMMAND IS SUCCESSFUL
7:32:53 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:32:53 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND IS SUCCESSFUL
7:32:53 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:32:53 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND IS SUCCESSFUL
7:34:53 AM   PLATFORM NO: CGDB Address:112LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:34:53 AM   PLATFORM NO: CGDB Address:112LINK CHECK COMMAND IS SUCCESSFUL
7:34:53 AM   PLATFORM NO: CGDB Address:111LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:34:53 AM   PLATFORM NO: CGDB Address:111LINK CHECK COMMAND IS SUCCESSFUL
7:34:53 AM   PLATFORM NO: CGDB Address:110LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:34:53 AM   PLATFORM NO: CGDB Address:110LINK CHECK COMMAND IS SUCCESSFUL
7:34:53 AM   PLATFORM NO: CGDB Address:109LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:34:53 AM   PLATFORM NO: CGDB Address:109LINK CHECK COMMAND IS SUCCESSFUL
7:34:53 AM   PLATFORM NO: CGDB Address:108LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:34:53 AM   PLATFORM NO: CGDB Address:108LINK CHECK COMMAND IS SUCCESSFUL
7:34:54 AM   PLATFORM NO: CGDB Address:107LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:34:54 AM   PLATFORM NO: CGDB Address:107LINK CHECK COMMAND IS SUCCESSFUL
7:34:54 AM   PLATFORM NO: CGDB Address:106LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:34:54 AM   PLATFORM NO: CGDB Address:106LINK CHECK COMMAND IS SUCCESSFUL
7:34:54 AM   PLATFORM NO: CGDB Address:105LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:34:54 AM   PLATFORM NO: CGDB Address:105LINK CHECK COMMAND IS SUCCESSFUL
7:34:54 AM   PLATFORM NO: CGDB Address:104LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:34:54 AM   PLATFORM NO: CGDB Address:104LINK CHECK COMMAND IS SUCCESSFUL
7:34:54 AM   PLATFORM NO: CGDB Address:103LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:34:54 AM   PLATFORM NO: CGDB Address:103LINK CHECK COMMAND IS SUCCESSFUL
7:34:54 AM   PLATFORM NO: CGDB Address:102LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:34:54 AM   PLATFORM NO: CGDB Address:102LINK CHECK COMMAND IS SUCCESSFUL
7:34:54 AM   PLATFORM NO: CGDB Address:101LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:34:54 AM   PLATFORM NO: CGDB Address:101LINK CHECK COMMAND IS SUCCESSFUL
7:34:54 AM   PLATFORM NO: CGDB Address:100LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:34:54 AM   PLATFORM NO: CGDB Address:100LINK CHECK COMMAND IS SUCCESSFUL
7:34:54 AM   PLATFORM NO: CGDB Address:99LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:34:54 AM   PLATFORM NO: CGDB Address:99LINK CHECK COMMAND IS SUCCESSFUL
7:34:55 AM   PLATFORM NO: CGDB Address:97LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:34:55 AM   PLATFORM NO: CGDB Address:97LINK CHECK COMMAND IS SUCCESSFUL
7:34:55 AM   PLATFORM NO: CGDB Address:96LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:34:55 AM   PLATFORM NO: CGDB Address:96LINK CHECK COMMAND IS SUCCESSFUL
7:34:55 AM   PLATFORM NO: CGDB Address:95LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:34:55 AM   PLATFORM NO: CGDB Address:95LINK CHECK COMMAND IS SUCCESSFUL
7:34:55 AM   PLATFORM NO: CGDB Address:94LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:34:55 AM   PLATFORM NO: CGDB Address:94LINK CHECK COMMAND IS SUCCESSFUL
7:34:55 AM   PLATFORM NO: CGDB Address:93LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:34:55 AM   PLATFORM NO: CGDB Address:93LINK CHECK COMMAND IS SUCCESSFUL
7:34:55 AM   PLATFORM NO: CGDB Address:92LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:34:55 AM   PLATFORM NO: CGDB Address:92LINK CHECK COMMAND IS SUCCESSFUL
7:34:55 AM   PLATFORM NO: CGDB Address:91LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:34:55 AM   PLATFORM NO: CGDB Address:91LINK CHECK COMMAND IS SUCCESSFUL
7:35:03 AM   PLATFORM NO: CGDB Address:90 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
7:35:11 AM   PLATFORM NO: CGDB Address:89 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
7:35:19 AM   PLATFORM NO: CGDB Address:88 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
7:35:19 AM   PLATFORM NO: CGDB Address:87LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:35:19 AM   PLATFORM NO: CGDB Address:87LINK CHECK COMMAND IS SUCCESSFUL
7:35:20 AM   PLATFORM NO: CGDB Address:86LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:35:20 AM   PLATFORM NO: CGDB Address:86LINK CHECK COMMAND IS SUCCESSFUL
7:35:20 AM   PLATFORM NO: CGDB Address:85LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:35:20 AM   PLATFORM NO: CGDB Address:85LINK CHECK COMMAND IS SUCCESSFUL
7:35:20 AM   PLATFORM NO: CGDB Address:84LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:35:20 AM   PLATFORM NO: CGDB Address:84LINK CHECK COMMAND IS SUCCESSFUL
7:35:20 AM   PLATFORM NO: CGDB Address:83LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:35:20 AM   PLATFORM NO: CGDB Address:83LINK CHECK COMMAND IS SUCCESSFUL
7:35:28 AM   PLATFORM NO: CGDB Address:82 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
7:35:28 AM   PLATFORM NO: CGDB Address:81LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:35:28 AM   PLATFORM NO: CGDB Address:81LINK CHECK COMMAND IS SUCCESSFUL
7:35:28 AM   PLATFORM NO: CGDB Address:80LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:35:28 AM   PLATFORM NO: CGDB Address:80LINK CHECK COMMAND IS SUCCESSFUL
7:35:28 AM   PLATFORM NO: CGDB Address:79LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:35:28 AM   PLATFORM NO: CGDB Address:79LINK CHECK COMMAND IS SUCCESSFUL
7:35:28 AM   PLATFORM NO: CGDB Address:78LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:35:28 AM   PLATFORM NO: CGDB Address:78LINK CHECK COMMAND IS SUCCESSFUL
7:35:29 AM   PLATFORM NO: CGDB Address:77LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:35:29 AM   PLATFORM NO: CGDB Address:77LINK CHECK COMMAND IS SUCCESSFUL
7:35:29 AM   PLATFORM NO: CGDB Address:76LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:35:29 AM   PLATFORM NO: CGDB Address:76LINK CHECK COMMAND IS SUCCESSFUL
7:35:29 AM   PLATFORM NO: CGDB Address:75LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:35:29 AM   PLATFORM NO: CGDB Address:75LINK CHECK COMMAND IS SUCCESSFUL
7:35:29 AM   PLATFORM NO: CGDB Address:74LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:35:29 AM   PLATFORM NO: CGDB Address:74LINK CHECK COMMAND IS SUCCESSFUL
7:35:29 AM   PLATFORM NO: CGDB Address:73LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:35:29 AM   PLATFORM NO: CGDB Address:73LINK CHECK COMMAND IS SUCCESSFUL
7:35:29 AM   PLATFORM NO: CGDB Address:72LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:35:29 AM   PLATFORM NO: CGDB Address:72LINK CHECK COMMAND IS SUCCESSFUL
7:35:29 AM   PLATFORM NO: CGDB Address:71LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:35:29 AM   PLATFORM NO: CGDB Address:71LINK CHECK COMMAND IS SUCCESSFUL
7:35:29 AM   PLATFORM NO: CGDB Address:70LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:35:29 AM   PLATFORM NO: CGDB Address:70LINK CHECK COMMAND IS SUCCESSFUL
7:35:30 AM   PLATFORM NO: CGDB Address:69LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:35:30 AM   PLATFORM NO: CGDB Address:69LINK CHECK COMMAND IS SUCCESSFUL
7:35:38 AM   PLATFORM NO: CGDB Address:68 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
7:35:46 AM   PLATFORM NO: CGDB Address:67 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
7:35:54 AM   PLATFORM NO: CGDB Address:66 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
7:36:02 AM   PLATFORM NO: CGDB Address:65 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
7:36:10 AM   PLATFORM NO: CGDB Address:64 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
7:38:18 AM    MLDB Address:53 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
7:38:18 AM    MLDB Address:50LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:38:18 AM    MLDB Address:50LINK CHECK COMMAND IS SUCCESSFUL
7:40:18 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:40:18 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND IS SUCCESSFUL
7:40:18 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:40:18 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND IS SUCCESSFUL
7:42:18 AM   PLATFORM NO: AGDB Address:33LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:42:18 AM   PLATFORM NO: AGDB Address:33LINK CHECK COMMAND IS SUCCESSFUL
7:42:18 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:42:18 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND IS SUCCESSFUL
7:42:18 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:42:18 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND IS SUCCESSFUL
7:44:18 AM   PLATFORM NO: CGDB Address:112LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:44:18 AM   PLATFORM NO: CGDB Address:112LINK CHECK COMMAND IS SUCCESSFUL
7:44:18 AM   PLATFORM NO: CGDB Address:111LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:44:18 AM   PLATFORM NO: CGDB Address:111LINK CHECK COMMAND IS SUCCESSFUL
7:44:19 AM   PLATFORM NO: CGDB Address:110LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:44:19 AM   PLATFORM NO: CGDB Address:110LINK CHECK COMMAND IS SUCCESSFUL
7:44:19 AM   PLATFORM NO: CGDB Address:109LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:44:19 AM   PLATFORM NO: CGDB Address:109LINK CHECK COMMAND IS SUCCESSFUL
7:44:19 AM   PLATFORM NO: CGDB Address:108LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:44:19 AM   PLATFORM NO: CGDB Address:108LINK CHECK COMMAND IS SUCCESSFUL
7:44:19 AM   PLATFORM NO: CGDB Address:107LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:44:19 AM   PLATFORM NO: CGDB Address:107LINK CHECK COMMAND IS SUCCESSFUL
7:44:19 AM   PLATFORM NO: CGDB Address:106LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:44:19 AM   PLATFORM NO: CGDB Address:106LINK CHECK COMMAND IS SUCCESSFUL
7:44:19 AM   PLATFORM NO: CGDB Address:105LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:44:19 AM   PLATFORM NO: CGDB Address:105LINK CHECK COMMAND IS SUCCESSFUL
7:44:19 AM   PLATFORM NO: CGDB Address:104LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:44:19 AM   PLATFORM NO: CGDB Address:104LINK CHECK COMMAND IS SUCCESSFUL
7:44:19 AM   PLATFORM NO: CGDB Address:103LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:44:19 AM   PLATFORM NO: CGDB Address:103LINK CHECK COMMAND IS SUCCESSFUL
7:44:20 AM   PLATFORM NO: CGDB Address:102LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:44:20 AM   PLATFORM NO: CGDB Address:102LINK CHECK COMMAND IS SUCCESSFUL
7:44:20 AM   PLATFORM NO: CGDB Address:101LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:44:20 AM   PLATFORM NO: CGDB Address:101LINK CHECK COMMAND IS SUCCESSFUL
7:44:20 AM   PLATFORM NO: CGDB Address:100LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:44:20 AM   PLATFORM NO: CGDB Address:100LINK CHECK COMMAND IS SUCCESSFUL
7:44:20 AM   PLATFORM NO: CGDB Address:99LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:44:20 AM   PLATFORM NO: CGDB Address:99LINK CHECK COMMAND IS SUCCESSFUL
7:44:20 AM   PLATFORM NO: CGDB Address:97LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:44:20 AM   PLATFORM NO: CGDB Address:97LINK CHECK COMMAND IS SUCCESSFUL
7:44:20 AM   PLATFORM NO: CGDB Address:96LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:44:20 AM   PLATFORM NO: CGDB Address:96LINK CHECK COMMAND IS SUCCESSFUL
7:44:20 AM   PLATFORM NO: CGDB Address:95LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:44:20 AM   PLATFORM NO: CGDB Address:95LINK CHECK COMMAND IS SUCCESSFUL
7:44:20 AM   PLATFORM NO: CGDB Address:94LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:44:20 AM   PLATFORM NO: CGDB Address:94LINK CHECK COMMAND IS SUCCESSFUL
7:44:21 AM   PLATFORM NO: CGDB Address:93LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:44:21 AM   PLATFORM NO: CGDB Address:93LINK CHECK COMMAND IS SUCCESSFUL
7:44:21 AM   PLATFORM NO: CGDB Address:92LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:44:21 AM   PLATFORM NO: CGDB Address:92LINK CHECK COMMAND IS SUCCESSFUL
7:44:21 AM   PLATFORM NO: CGDB Address:91LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:44:21 AM   PLATFORM NO: CGDB Address:91LINK CHECK COMMAND IS SUCCESSFUL
7:44:29 AM   PLATFORM NO: CGDB Address:90 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
7:44:37 AM   PLATFORM NO: CGDB Address:89 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
7:44:45 AM   PLATFORM NO: CGDB Address:88 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
7:44:45 AM   PLATFORM NO: CGDB Address:87LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:44:45 AM   PLATFORM NO: CGDB Address:87LINK CHECK COMMAND IS SUCCESSFUL
7:44:45 AM   PLATFORM NO: CGDB Address:86LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:44:45 AM   PLATFORM NO: CGDB Address:86LINK CHECK COMMAND IS SUCCESSFUL
7:44:45 AM   PLATFORM NO: CGDB Address:85LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:44:45 AM   PLATFORM NO: CGDB Address:85LINK CHECK COMMAND IS SUCCESSFUL
7:44:45 AM   PLATFORM NO: CGDB Address:84LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:44:45 AM   PLATFORM NO: CGDB Address:84LINK CHECK COMMAND IS SUCCESSFUL
7:44:45 AM   PLATFORM NO: CGDB Address:83LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:44:45 AM   PLATFORM NO: CGDB Address:83LINK CHECK COMMAND IS SUCCESSFUL
7:44:53 AM   PLATFORM NO: CGDB Address:82 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
7:44:54 AM   PLATFORM NO: CGDB Address:81LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:44:54 AM   PLATFORM NO: CGDB Address:81LINK CHECK COMMAND IS SUCCESSFUL
7:44:54 AM   PLATFORM NO: CGDB Address:80LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:44:54 AM   PLATFORM NO: CGDB Address:80LINK CHECK COMMAND IS SUCCESSFUL
7:44:54 AM   PLATFORM NO: CGDB Address:79LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:44:54 AM   PLATFORM NO: CGDB Address:79LINK CHECK COMMAND IS SUCCESSFUL
7:44:54 AM   PLATFORM NO: CGDB Address:78LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:44:54 AM   PLATFORM NO: CGDB Address:78LINK CHECK COMMAND IS SUCCESSFUL
7:44:54 AM   PLATFORM NO: CGDB Address:77LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:44:54 AM   PLATFORM NO: CGDB Address:77LINK CHECK COMMAND IS SUCCESSFUL
7:44:54 AM   PLATFORM NO: CGDB Address:76LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:44:54 AM   PLATFORM NO: CGDB Address:76LINK CHECK COMMAND IS SUCCESSFUL
7:44:54 AM   PLATFORM NO: CGDB Address:75LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:44:54 AM   PLATFORM NO: CGDB Address:75LINK CHECK COMMAND IS SUCCESSFUL
7:44:54 AM   PLATFORM NO: CGDB Address:74LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:44:54 AM   PLATFORM NO: CGDB Address:74LINK CHECK COMMAND IS SUCCESSFUL
7:44:55 AM   PLATFORM NO: CGDB Address:73LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:44:55 AM   PLATFORM NO: CGDB Address:73LINK CHECK COMMAND IS SUCCESSFUL
7:44:55 AM   PLATFORM NO: CGDB Address:72LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:44:55 AM   PLATFORM NO: CGDB Address:72LINK CHECK COMMAND IS SUCCESSFUL
7:44:55 AM   PLATFORM NO: CGDB Address:71LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:44:55 AM   PLATFORM NO: CGDB Address:71LINK CHECK COMMAND IS SUCCESSFUL
7:44:55 AM   PLATFORM NO: CGDB Address:70LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:44:55 AM   PLATFORM NO: CGDB Address:70LINK CHECK COMMAND IS SUCCESSFUL
7:44:55 AM   PLATFORM NO: CGDB Address:69LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:44:55 AM   PLATFORM NO: CGDB Address:69LINK CHECK COMMAND IS SUCCESSFUL
7:45:03 AM   PLATFORM NO: CGDB Address:68 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
7:45:11 AM   PLATFORM NO: CGDB Address:67 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
7:45:19 AM   PLATFORM NO: CGDB Address:66 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
7:45:27 AM   PLATFORM NO: CGDB Address:65 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
7:45:35 AM   PLATFORM NO: CGDB Address:64 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
7:47:43 AM    MLDB Address:53 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
7:47:43 AM    MLDB Address:50LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:47:43 AM    MLDB Address:50LINK CHECK COMMAND IS SUCCESSFUL
7:49:43 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:49:43 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND IS SUCCESSFUL
7:49:43 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:49:43 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND IS SUCCESSFUL
7:51:43 AM   PLATFORM NO: AGDB Address:33LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:51:43 AM   PLATFORM NO: AGDB Address:33LINK CHECK COMMAND IS SUCCESSFUL
7:51:44 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:51:44 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND IS SUCCESSFUL
7:51:44 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:51:44 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND IS SUCCESSFUL
7:53:44 AM   PLATFORM NO: CGDB Address:112LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:53:44 AM   PLATFORM NO: CGDB Address:112LINK CHECK COMMAND IS SUCCESSFUL
7:53:44 AM   PLATFORM NO: CGDB Address:111LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:53:44 AM   PLATFORM NO: CGDB Address:111LINK CHECK COMMAND IS SUCCESSFUL
7:53:44 AM   PLATFORM NO: CGDB Address:110LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:53:44 AM   PLATFORM NO: CGDB Address:110LINK CHECK COMMAND IS SUCCESSFUL
7:53:44 AM   PLATFORM NO: CGDB Address:109LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:53:44 AM   PLATFORM NO: CGDB Address:109LINK CHECK COMMAND IS SUCCESSFUL
7:53:44 AM   PLATFORM NO: CGDB Address:108LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:53:44 AM   PLATFORM NO: CGDB Address:108LINK CHECK COMMAND IS SUCCESSFUL
7:53:44 AM   PLATFORM NO: CGDB Address:107LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:53:44 AM   PLATFORM NO: CGDB Address:107LINK CHECK COMMAND IS SUCCESSFUL
7:53:45 AM   PLATFORM NO: CGDB Address:106LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:53:45 AM   PLATFORM NO: CGDB Address:106LINK CHECK COMMAND IS SUCCESSFUL
7:53:45 AM   PLATFORM NO: CGDB Address:105LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:53:45 AM   PLATFORM NO: CGDB Address:105LINK CHECK COMMAND IS SUCCESSFUL
7:53:45 AM   PLATFORM NO: CGDB Address:104LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:53:45 AM   PLATFORM NO: CGDB Address:104LINK CHECK COMMAND IS SUCCESSFUL
7:53:45 AM   PLATFORM NO: CGDB Address:103LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:53:45 AM   PLATFORM NO: CGDB Address:103LINK CHECK COMMAND IS SUCCESSFUL
7:53:45 AM   PLATFORM NO: CGDB Address:102LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:53:45 AM   PLATFORM NO: CGDB Address:102LINK CHECK COMMAND IS SUCCESSFUL
7:53:45 AM   PLATFORM NO: CGDB Address:101LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:53:45 AM   PLATFORM NO: CGDB Address:101LINK CHECK COMMAND IS SUCCESSFUL
7:53:45 AM   PLATFORM NO: CGDB Address:100LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:53:45 AM   PLATFORM NO: CGDB Address:100LINK CHECK COMMAND IS SUCCESSFUL
7:53:45 AM   PLATFORM NO: CGDB Address:99LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:53:45 AM   PLATFORM NO: CGDB Address:99LINK CHECK COMMAND IS SUCCESSFUL
7:53:46 AM   PLATFORM NO: CGDB Address:97LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:53:46 AM   PLATFORM NO: CGDB Address:97LINK CHECK COMMAND IS SUCCESSFUL
7:53:46 AM   PLATFORM NO: CGDB Address:96LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:53:46 AM   PLATFORM NO: CGDB Address:96LINK CHECK COMMAND IS SUCCESSFUL
7:53:46 AM   PLATFORM NO: CGDB Address:95LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:53:46 AM   PLATFORM NO: CGDB Address:95LINK CHECK COMMAND IS SUCCESSFUL
7:53:46 AM   PLATFORM NO: CGDB Address:94LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:53:46 AM   PLATFORM NO: CGDB Address:94LINK CHECK COMMAND IS SUCCESSFUL
7:53:46 AM   PLATFORM NO: CGDB Address:93LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:53:46 AM   PLATFORM NO: CGDB Address:93LINK CHECK COMMAND IS SUCCESSFUL
7:53:46 AM   PLATFORM NO: CGDB Address:92LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:53:46 AM   PLATFORM NO: CGDB Address:92LINK CHECK COMMAND IS SUCCESSFUL
7:53:46 AM   PLATFORM NO: CGDB Address:91LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:53:46 AM   PLATFORM NO: CGDB Address:91LINK CHECK COMMAND IS SUCCESSFUL
7:53:54 AM   PLATFORM NO: CGDB Address:90 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
7:54:02 AM   PLATFORM NO: CGDB Address:89 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
7:54:10 AM   PLATFORM NO: CGDB Address:88 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
7:54:10 AM   PLATFORM NO: CGDB Address:87LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:54:10 AM   PLATFORM NO: CGDB Address:87LINK CHECK COMMAND IS SUCCESSFUL
7:54:18 AM   PLATFORM NO: CGDB Address:86 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
7:54:18 AM   PLATFORM NO: CGDB Address:85LINK CHECK RESPONSE PACKET: IN CORRECT SOURCE ADDRESS
7:54:18 AM   PLATFORM NO: CGDB Address:85LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:54:18 AM   PLATFORM NO: CGDB Address:85LINK CHECK RESPONSE PACKET: CHECKSUM FAILED 
7:54:19 AM   PLATFORM NO: CGDB Address:84LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:54:19 AM   PLATFORM NO: CGDB Address:84LINK CHECK COMMAND IS SUCCESSFUL
7:54:19 AM   PLATFORM NO: CGDB Address:83LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:54:19 AM   PLATFORM NO: CGDB Address:83LINK CHECK COMMAND IS SUCCESSFUL
7:54:27 AM   PLATFORM NO: CGDB Address:82 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
7:54:27 AM   PLATFORM NO: CGDB Address:81LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:54:27 AM   PLATFORM NO: CGDB Address:81LINK CHECK COMMAND IS SUCCESSFUL
7:54:27 AM   PLATFORM NO: CGDB Address:80LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:54:27 AM   PLATFORM NO: CGDB Address:80LINK CHECK COMMAND IS SUCCESSFUL
7:54:27 AM   PLATFORM NO: CGDB Address:79LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:54:27 AM   PLATFORM NO: CGDB Address:79LINK CHECK COMMAND IS SUCCESSFUL
7:54:27 AM   PLATFORM NO: CGDB Address:78LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:54:27 AM   PLATFORM NO: CGDB Address:78LINK CHECK COMMAND IS SUCCESSFUL
7:54:27 AM   PLATFORM NO: CGDB Address:77LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:54:27 AM   PLATFORM NO: CGDB Address:77LINK CHECK COMMAND IS SUCCESSFUL
7:54:27 AM   PLATFORM NO: CGDB Address:76LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:54:27 AM   PLATFORM NO: CGDB Address:76LINK CHECK COMMAND IS SUCCESSFUL
7:54:28 AM   PLATFORM NO: CGDB Address:75LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:54:28 AM   PLATFORM NO: CGDB Address:75LINK CHECK COMMAND IS SUCCESSFUL
7:54:28 AM   PLATFORM NO: CGDB Address:74LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:54:28 AM   PLATFORM NO: CGDB Address:74LINK CHECK COMMAND IS SUCCESSFUL
7:54:28 AM   PLATFORM NO: CGDB Address:73LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:54:28 AM   PLATFORM NO: CGDB Address:73LINK CHECK COMMAND IS SUCCESSFUL
7:54:28 AM   PLATFORM NO: CGDB Address:72LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:54:28 AM   PLATFORM NO: CGDB Address:72LINK CHECK COMMAND IS SUCCESSFUL
7:54:28 AM   PLATFORM NO: CGDB Address:71LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:54:28 AM   PLATFORM NO: CGDB Address:71LINK CHECK COMMAND IS SUCCESSFUL
7:54:36 AM   PLATFORM NO: CGDB Address:70 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
7:54:36 AM   PLATFORM NO: CGDB Address:69LINK CHECK RESPONSE PACKET: IN CORRECT SOURCE ADDRESS
7:54:36 AM   PLATFORM NO: CGDB Address:69LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:54:36 AM   PLATFORM NO: CGDB Address:69LINK CHECK RESPONSE PACKET: CHECKSUM FAILED 
7:54:44 AM   PLATFORM NO: CGDB Address:68 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
7:54:52 AM   PLATFORM NO: CGDB Address:67 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
7:55:00 AM   PLATFORM NO: CGDB Address:66 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
7:55:08 AM   PLATFORM NO: CGDB Address:65 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
7:55:16 AM   PLATFORM NO: CGDB Address:64 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
7:57:24 AM    MLDB Address:53 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
7:57:24 AM    MLDB Address:50LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:57:24 AM    MLDB Address:50LINK CHECK COMMAND IS SUCCESSFUL
7:59:24 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:59:24 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND IS SUCCESSFUL
7:59:25 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
7:59:25 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND IS SUCCESSFUL
8:01:25 AM   PLATFORM NO: AGDB Address:33LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:01:25 AM   PLATFORM NO: AGDB Address:33LINK CHECK COMMAND IS SUCCESSFUL
8:01:25 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:01:25 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND IS SUCCESSFUL
8:01:25 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:01:25 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND IS SUCCESSFUL
8:03:25 AM   PLATFORM NO: CGDB Address:112LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:03:25 AM   PLATFORM NO: CGDB Address:112LINK CHECK COMMAND IS SUCCESSFUL
8:03:25 AM   PLATFORM NO: CGDB Address:111LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:03:25 AM   PLATFORM NO: CGDB Address:111LINK CHECK COMMAND IS SUCCESSFUL
8:03:25 AM   PLATFORM NO: CGDB Address:110LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:03:25 AM   PLATFORM NO: CGDB Address:110LINK CHECK COMMAND IS SUCCESSFUL
8:03:25 AM   PLATFORM NO: CGDB Address:109LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:03:25 AM   PLATFORM NO: CGDB Address:109LINK CHECK COMMAND IS SUCCESSFUL
8:03:25 AM   PLATFORM NO: CGDB Address:108LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:03:25 AM   PLATFORM NO: CGDB Address:108LINK CHECK COMMAND IS SUCCESSFUL
8:03:26 AM   PLATFORM NO: CGDB Address:107LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:03:26 AM   PLATFORM NO: CGDB Address:107LINK CHECK COMMAND IS SUCCESSFUL
8:03:26 AM   PLATFORM NO: CGDB Address:106LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:03:26 AM   PLATFORM NO: CGDB Address:106LINK CHECK COMMAND IS SUCCESSFUL
8:03:26 AM   PLATFORM NO: CGDB Address:105LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:03:26 AM   PLATFORM NO: CGDB Address:105LINK CHECK COMMAND IS SUCCESSFUL
8:03:26 AM   PLATFORM NO: CGDB Address:104LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:03:26 AM   PLATFORM NO: CGDB Address:104LINK CHECK COMMAND IS SUCCESSFUL
8:03:26 AM   PLATFORM NO: CGDB Address:103LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:03:26 AM   PLATFORM NO: CGDB Address:103LINK CHECK COMMAND IS SUCCESSFUL
8:03:26 AM   PLATFORM NO: CGDB Address:102LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:03:26 AM   PLATFORM NO: CGDB Address:102LINK CHECK COMMAND IS SUCCESSFUL
8:03:26 AM   PLATFORM NO: CGDB Address:101LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:03:26 AM   PLATFORM NO: CGDB Address:101LINK CHECK COMMAND IS SUCCESSFUL
8:03:26 AM   PLATFORM NO: CGDB Address:100LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:03:26 AM   PLATFORM NO: CGDB Address:100LINK CHECK COMMAND IS SUCCESSFUL
8:03:27 AM   PLATFORM NO: CGDB Address:99LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:03:27 AM   PLATFORM NO: CGDB Address:99LINK CHECK COMMAND IS SUCCESSFUL
8:03:27 AM   PLATFORM NO: CGDB Address:97LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:03:27 AM   PLATFORM NO: CGDB Address:97LINK CHECK COMMAND IS SUCCESSFUL
8:03:27 AM   PLATFORM NO: CGDB Address:96LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:03:27 AM   PLATFORM NO: CGDB Address:96LINK CHECK COMMAND IS SUCCESSFUL
8:03:27 AM   PLATFORM NO: CGDB Address:95LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:03:27 AM   PLATFORM NO: CGDB Address:95LINK CHECK COMMAND IS SUCCESSFUL
8:03:27 AM   PLATFORM NO: CGDB Address:94LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:03:27 AM   PLATFORM NO: CGDB Address:94LINK CHECK COMMAND IS SUCCESSFUL
8:03:27 AM   PLATFORM NO: CGDB Address:93LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:03:27 AM   PLATFORM NO: CGDB Address:93LINK CHECK COMMAND IS SUCCESSFUL
8:03:27 AM   PLATFORM NO: CGDB Address:92LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:03:27 AM   PLATFORM NO: CGDB Address:92LINK CHECK COMMAND IS SUCCESSFUL
8:03:27 AM   PLATFORM NO: CGDB Address:91LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:03:27 AM   PLATFORM NO: CGDB Address:91LINK CHECK COMMAND IS SUCCESSFUL
8:03:35 AM   PLATFORM NO: CGDB Address:90 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
8:03:43 AM   PLATFORM NO: CGDB Address:89 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
8:03:51 AM   PLATFORM NO: CGDB Address:88 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
8:03:52 AM   PLATFORM NO: CGDB Address:87LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:03:52 AM   PLATFORM NO: CGDB Address:87LINK CHECK COMMAND IS SUCCESSFUL
8:03:52 AM   PLATFORM NO: CGDB Address:86LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:03:52 AM   PLATFORM NO: CGDB Address:86LINK CHECK COMMAND IS SUCCESSFUL
8:03:52 AM   PLATFORM NO: CGDB Address:85LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:03:52 AM   PLATFORM NO: CGDB Address:85LINK CHECK COMMAND IS SUCCESSFUL
8:03:52 AM   PLATFORM NO: CGDB Address:84LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:03:52 AM   PLATFORM NO: CGDB Address:84LINK CHECK COMMAND IS SUCCESSFUL
8:03:52 AM   PLATFORM NO: CGDB Address:83LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:03:52 AM   PLATFORM NO: CGDB Address:83LINK CHECK COMMAND IS SUCCESSFUL
8:04:00 AM   PLATFORM NO: CGDB Address:82 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
8:04:00 AM   PLATFORM NO: CGDB Address:81LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:04:00 AM   PLATFORM NO: CGDB Address:81LINK CHECK COMMAND IS SUCCESSFUL
8:04:00 AM   PLATFORM NO: CGDB Address:80LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:04:00 AM   PLATFORM NO: CGDB Address:80LINK CHECK COMMAND IS SUCCESSFUL
8:04:08 AM   PLATFORM NO: CGDB Address:79 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
8:04:08 AM   PLATFORM NO: CGDB Address:78LINK CHECK RESPONSE PACKET: IN CORRECT SOURCE ADDRESS
8:04:08 AM   PLATFORM NO: CGDB Address:78LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:04:08 AM   PLATFORM NO: CGDB Address:78LINK CHECK RESPONSE PACKET: CHECKSUM FAILED 
8:04:09 AM   PLATFORM NO: CGDB Address:77LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:04:09 AM   PLATFORM NO: CGDB Address:77LINK CHECK COMMAND IS SUCCESSFUL
8:04:09 AM   PLATFORM NO: CGDB Address:76LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:04:09 AM   PLATFORM NO: CGDB Address:76LINK CHECK COMMAND IS SUCCESSFUL
8:04:09 AM   PLATFORM NO: CGDB Address:75LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:04:09 AM   PLATFORM NO: CGDB Address:75LINK CHECK COMMAND IS SUCCESSFUL
8:04:09 AM   PLATFORM NO: CGDB Address:74LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:04:09 AM   PLATFORM NO: CGDB Address:74LINK CHECK COMMAND IS SUCCESSFUL
8:04:09 AM   PLATFORM NO: CGDB Address:73LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:04:09 AM   PLATFORM NO: CGDB Address:73LINK CHECK COMMAND IS SUCCESSFUL
8:04:09 AM   PLATFORM NO: CGDB Address:72LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:04:09 AM   PLATFORM NO: CGDB Address:72LINK CHECK COMMAND IS SUCCESSFUL
8:04:09 AM   PLATFORM NO: CGDB Address:71LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:04:09 AM   PLATFORM NO: CGDB Address:71LINK CHECK COMMAND IS SUCCESSFUL
8:04:09 AM   PLATFORM NO: CGDB Address:70LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:04:09 AM   PLATFORM NO: CGDB Address:70LINK CHECK COMMAND IS SUCCESSFUL
8:04:09 AM   PLATFORM NO: CGDB Address:69LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:04:09 AM   PLATFORM NO: CGDB Address:69LINK CHECK COMMAND IS SUCCESSFUL
8:04:18 AM   PLATFORM NO: CGDB Address:68 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
8:04:26 AM   PLATFORM NO: CGDB Address:67 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
8:04:34 AM   PLATFORM NO: CGDB Address:66 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
8:04:42 AM   PLATFORM NO: CGDB Address:65 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
8:04:50 AM   PLATFORM NO: CGDB Address:64 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
8:06:58 AM    MLDB Address:53 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
8:06:58 AM    MLDB Address:50LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:06:58 AM    MLDB Address:50LINK CHECK COMMAND IS SUCCESSFUL
8:08:58 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:08:58 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND IS SUCCESSFUL
8:08:58 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:08:58 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND IS SUCCESSFUL
8:10:58 AM   PLATFORM NO: AGDB Address:33LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:10:58 AM   PLATFORM NO: AGDB Address:33LINK CHECK COMMAND IS SUCCESSFUL
8:10:58 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:10:58 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND IS SUCCESSFUL
8:10:58 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:10:58 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND IS SUCCESSFUL
8:12:58 AM   PLATFORM NO: CGDB Address:112LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:12:58 AM   PLATFORM NO: CGDB Address:112LINK CHECK COMMAND IS SUCCESSFUL
8:12:58 AM   PLATFORM NO: CGDB Address:111LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:12:58 AM   PLATFORM NO: CGDB Address:111LINK CHECK COMMAND IS SUCCESSFUL
8:12:59 AM   PLATFORM NO: CGDB Address:110LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:12:59 AM   PLATFORM NO: CGDB Address:110LINK CHECK COMMAND IS SUCCESSFUL
8:12:59 AM   PLATFORM NO: CGDB Address:109LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:12:59 AM   PLATFORM NO: CGDB Address:109LINK CHECK COMMAND IS SUCCESSFUL
8:12:59 AM   PLATFORM NO: CGDB Address:108LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:12:59 AM   PLATFORM NO: CGDB Address:108LINK CHECK COMMAND IS SUCCESSFUL
8:12:59 AM   PLATFORM NO: CGDB Address:107LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:12:59 AM   PLATFORM NO: CGDB Address:107LINK CHECK COMMAND IS SUCCESSFUL
8:12:59 AM   PLATFORM NO: CGDB Address:106LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:12:59 AM   PLATFORM NO: CGDB Address:106LINK CHECK COMMAND IS SUCCESSFUL
8:12:59 AM   PLATFORM NO: CGDB Address:105LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:12:59 AM   PLATFORM NO: CGDB Address:105LINK CHECK COMMAND IS SUCCESSFUL
8:12:59 AM   PLATFORM NO: CGDB Address:104LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:12:59 AM   PLATFORM NO: CGDB Address:104LINK CHECK COMMAND IS SUCCESSFUL
8:12:59 AM   PLATFORM NO: CGDB Address:103LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:12:59 AM   PLATFORM NO: CGDB Address:103LINK CHECK COMMAND IS SUCCESSFUL
8:12:59 AM   PLATFORM NO: CGDB Address:102LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:12:59 AM   PLATFORM NO: CGDB Address:102LINK CHECK COMMAND IS SUCCESSFUL
8:13:00 AM   PLATFORM NO: CGDB Address:101LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:13:00 AM   PLATFORM NO: CGDB Address:101LINK CHECK COMMAND IS SUCCESSFUL
8:13:00 AM   PLATFORM NO: CGDB Address:100LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:13:00 AM   PLATFORM NO: CGDB Address:100LINK CHECK COMMAND IS SUCCESSFUL
8:13:00 AM   PLATFORM NO: CGDB Address:99LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:13:00 AM   PLATFORM NO: CGDB Address:99LINK CHECK COMMAND IS SUCCESSFUL
8:13:00 AM   PLATFORM NO: CGDB Address:97LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:13:00 AM   PLATFORM NO: CGDB Address:97LINK CHECK COMMAND IS SUCCESSFUL
8:13:00 AM   PLATFORM NO: CGDB Address:96LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:13:00 AM   PLATFORM NO: CGDB Address:96LINK CHECK COMMAND IS SUCCESSFUL
8:13:00 AM   PLATFORM NO: CGDB Address:95LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:13:00 AM   PLATFORM NO: CGDB Address:95LINK CHECK COMMAND IS SUCCESSFUL
8:13:00 AM   PLATFORM NO: CGDB Address:94LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:13:00 AM   PLATFORM NO: CGDB Address:94LINK CHECK COMMAND IS SUCCESSFUL
8:13:00 AM   PLATFORM NO: CGDB Address:93LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:13:00 AM   PLATFORM NO: CGDB Address:93LINK CHECK COMMAND IS SUCCESSFUL
8:13:01 AM   PLATFORM NO: CGDB Address:92LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:13:01 AM   PLATFORM NO: CGDB Address:92LINK CHECK COMMAND IS SUCCESSFUL
8:13:01 AM   PLATFORM NO: CGDB Address:91LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:13:01 AM   PLATFORM NO: CGDB Address:91LINK CHECK COMMAND IS SUCCESSFUL
8:13:09 AM   PLATFORM NO: CGDB Address:90 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
8:13:17 AM   PLATFORM NO: CGDB Address:89 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
8:13:25 AM   PLATFORM NO: CGDB Address:88 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
8:13:25 AM   PLATFORM NO: CGDB Address:87LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:13:25 AM   PLATFORM NO: CGDB Address:87LINK CHECK COMMAND IS SUCCESSFUL
8:13:25 AM   PLATFORM NO: CGDB Address:86LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:13:25 AM   PLATFORM NO: CGDB Address:86LINK CHECK COMMAND IS SUCCESSFUL
8:13:25 AM   PLATFORM NO: CGDB Address:85LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:13:25 AM   PLATFORM NO: CGDB Address:85LINK CHECK COMMAND IS SUCCESSFUL
8:13:25 AM   PLATFORM NO: CGDB Address:84LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:13:25 AM   PLATFORM NO: CGDB Address:84LINK CHECK COMMAND IS SUCCESSFUL
8:13:25 AM   PLATFORM NO: CGDB Address:83LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:13:25 AM   PLATFORM NO: CGDB Address:83LINK CHECK COMMAND IS SUCCESSFUL
8:13:33 AM   PLATFORM NO: CGDB Address:82 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
8:13:33 AM   PLATFORM NO: CGDB Address:81LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:13:33 AM   PLATFORM NO: CGDB Address:81LINK CHECK COMMAND IS SUCCESSFUL
8:13:34 AM   PLATFORM NO: CGDB Address:80LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:13:34 AM   PLATFORM NO: CGDB Address:80LINK CHECK COMMAND IS SUCCESSFUL
8:13:34 AM   PLATFORM NO: CGDB Address:79LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:13:34 AM   PLATFORM NO: CGDB Address:79LINK CHECK COMMAND IS SUCCESSFUL
8:13:34 AM   PLATFORM NO: CGDB Address:78LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:13:34 AM   PLATFORM NO: CGDB Address:78LINK CHECK COMMAND IS SUCCESSFUL
8:13:34 AM   PLATFORM NO: CGDB Address:77LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:13:34 AM   PLATFORM NO: CGDB Address:77LINK CHECK COMMAND IS SUCCESSFUL
8:13:34 AM   PLATFORM NO: CGDB Address:76LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:13:34 AM   PLATFORM NO: CGDB Address:76LINK CHECK COMMAND IS SUCCESSFUL
8:13:34 AM   PLATFORM NO: CGDB Address:75LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:13:34 AM   PLATFORM NO: CGDB Address:75LINK CHECK COMMAND IS SUCCESSFUL
8:13:34 AM   PLATFORM NO: CGDB Address:74LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:13:34 AM   PLATFORM NO: CGDB Address:74LINK CHECK COMMAND IS SUCCESSFUL
8:13:34 AM   PLATFORM NO: CGDB Address:73LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:13:34 AM   PLATFORM NO: CGDB Address:73LINK CHECK COMMAND IS SUCCESSFUL
8:13:35 AM   PLATFORM NO: CGDB Address:72LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:13:35 AM   PLATFORM NO: CGDB Address:72LINK CHECK COMMAND IS SUCCESSFUL
8:13:35 AM   PLATFORM NO: CGDB Address:71LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:13:35 AM   PLATFORM NO: CGDB Address:71LINK CHECK COMMAND IS SUCCESSFUL
8:13:35 AM   PLATFORM NO: CGDB Address:70LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:13:35 AM   PLATFORM NO: CGDB Address:70LINK CHECK COMMAND IS SUCCESSFUL
8:13:35 AM   PLATFORM NO: CGDB Address:69LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:13:35 AM   PLATFORM NO: CGDB Address:69LINK CHECK COMMAND IS SUCCESSFUL
8:13:43 AM   PLATFORM NO: CGDB Address:68 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
8:13:51 AM   PLATFORM NO: CGDB Address:67 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
8:13:59 AM   PLATFORM NO: CGDB Address:66 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
8:14:07 AM   PLATFORM NO: CGDB Address:65 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
8:14:15 AM   PLATFORM NO: CGDB Address:64 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
8:14:23 AM    MLDB Address:53 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
8:14:23 AM    MLDB Address:50 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:14:23 AM    MLDB Address:50 SET CONFIGURATION COMMAND IS SUCCESSFUL
8:14:23 AM   PLATFORM NO: PDB Address:162 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:14:23 AM   PLATFORM NO: PDB Address:162 SET CONFIGURATION COMMAND IS SUCCESSFUL
8:14:23 AM   PLATFORM NO: PDB Address:162 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:14:23 AM   PLATFORM NO: PDB Address:162 SET CONFIGURATION COMMAND IS SUCCESSFUL
8:14:23 AM   PLATFORM NO: AGDB Address:33 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:14:23 AM   PLATFORM NO: AGDB Address:33 SET CONFIGURATION COMMAND IS SUCCESSFUL
8:14:23 AM   PLATFORM NO: AGDB Address:36 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:14:23 AM   PLATFORM NO: AGDB Address:36 SET CONFIGURATION COMMAND IS SUCCESSFUL
8:14:24 AM   PLATFORM NO: AGDB Address:36 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:14:24 AM   PLATFORM NO: AGDB Address:36 SET CONFIGURATION COMMAND IS SUCCESSFUL
8:14:24 AM   PLATFORM NO: CGDB Address:112 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:14:24 AM   PLATFORM NO: CGDB Address:112 SET CONFIGURATION COMMAND IS SUCCESSFUL
8:14:24 AM   PLATFORM NO: CGDB Address:111 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:14:24 AM   PLATFORM NO: CGDB Address:111 SET CONFIGURATION COMMAND IS SUCCESSFUL
8:14:24 AM   PLATFORM NO: CGDB Address:110 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:14:24 AM   PLATFORM NO: CGDB Address:110 SET CONFIGURATION COMMAND IS SUCCESSFUL
8:14:24 AM   PLATFORM NO: CGDB Address:109 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:14:24 AM   PLATFORM NO: CGDB Address:109 SET CONFIGURATION COMMAND IS SUCCESSFUL
8:14:24 AM   PLATFORM NO: CGDB Address:108 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:14:24 AM   PLATFORM NO: CGDB Address:108 SET CONFIGURATION COMMAND IS SUCCESSFUL
8:14:24 AM   PLATFORM NO: CGDB Address:107 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:14:24 AM   PLATFORM NO: CGDB Address:107 SET CONFIGURATION COMMAND IS SUCCESSFUL
8:14:24 AM   PLATFORM NO: CGDB Address:106 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:14:24 AM   PLATFORM NO: CGDB Address:106 SET CONFIGURATION COMMAND IS SUCCESSFUL
8:14:25 AM   PLATFORM NO: CGDB Address:105 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:14:25 AM   PLATFORM NO: CGDB Address:105 SET CONFIGURATION COMMAND IS SUCCESSFUL
8:14:25 AM   PLATFORM NO: CGDB Address:104 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:14:25 AM   PLATFORM NO: CGDB Address:104 SET CONFIGURATION COMMAND IS SUCCESSFUL
8:14:25 AM   PLATFORM NO: CGDB Address:103 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:14:25 AM   PLATFORM NO: CGDB Address:103 SET CONFIGURATION COMMAND IS SUCCESSFUL
8:14:25 AM   PLATFORM NO: CGDB Address:102 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:14:25 AM   PLATFORM NO: CGDB Address:102 SET CONFIGURATION COMMAND IS SUCCESSFUL
8:14:25 AM   PLATFORM NO: CGDB Address:101 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:14:25 AM   PLATFORM NO: CGDB Address:101 SET CONFIGURATION COMMAND IS SUCCESSFUL
8:14:25 AM   PLATFORM NO: CGDB Address:100 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:14:25 AM   PLATFORM NO: CGDB Address:100 SET CONFIGURATION COMMAND IS SUCCESSFUL
8:14:25 AM   PLATFORM NO: CGDB Address:99 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:14:25 AM   PLATFORM NO: CGDB Address:99 SET CONFIGURATION COMMAND IS SUCCESSFUL
8:14:25 AM   PLATFORM NO: CGDB Address:97 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:14:25 AM   PLATFORM NO: CGDB Address:97 SET CONFIGURATION COMMAND IS SUCCESSFUL
8:14:26 AM   PLATFORM NO: CGDB Address:96 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:14:26 AM   PLATFORM NO: CGDB Address:96 SET CONFIGURATION COMMAND IS SUCCESSFUL
8:14:26 AM   PLATFORM NO: CGDB Address:95 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:14:26 AM   PLATFORM NO: CGDB Address:95 SET CONFIGURATION COMMAND IS SUCCESSFUL
8:14:26 AM   PLATFORM NO: CGDB Address:94 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:14:26 AM   PLATFORM NO: CGDB Address:94 SET CONFIGURATION COMMAND IS SUCCESSFUL
8:14:26 AM   PLATFORM NO: CGDB Address:93 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:14:26 AM   PLATFORM NO: CGDB Address:93 SET CONFIGURATION COMMAND IS SUCCESSFUL
8:14:26 AM   PLATFORM NO: CGDB Address:92 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:14:26 AM   PLATFORM NO: CGDB Address:92 SET CONFIGURATION COMMAND IS SUCCESSFUL
8:14:26 AM   PLATFORM NO: CGDB Address:91 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:14:26 AM   PLATFORM NO: CGDB Address:91 SET CONFIGURATION COMMAND IS SUCCESSFUL
8:14:34 AM   PLATFORM NO: CGDB Address:90 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
8:14:42 AM   PLATFORM NO: CGDB Address:89 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
8:14:50 AM   PLATFORM NO: CGDB Address:88 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
8:14:50 AM   PLATFORM NO: CGDB Address:87 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:14:50 AM   PLATFORM NO: CGDB Address:87 SET CONFIGURATION COMMAND IS SUCCESSFUL
8:14:51 AM   PLATFORM NO: CGDB Address:86 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:14:51 AM   PLATFORM NO: CGDB Address:86 SET CONFIGURATION COMMAND IS SUCCESSFUL
8:14:51 AM   PLATFORM NO: CGDB Address:85 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:14:51 AM   PLATFORM NO: CGDB Address:85 SET CONFIGURATION COMMAND IS SUCCESSFUL
8:14:51 AM   PLATFORM NO: CGDB Address:84 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:14:51 AM   PLATFORM NO: CGDB Address:84 SET CONFIGURATION COMMAND IS SUCCESSFUL
8:14:51 AM   PLATFORM NO: CGDB Address:83 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:14:51 AM   PLATFORM NO: CGDB Address:83 SET CONFIGURATION COMMAND IS SUCCESSFUL
8:14:51 AM   PLATFORM NO: CGDB Address:82 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:14:51 AM   PLATFORM NO: CGDB Address:82 SET CONFIGURATION COMMAND IS SUCCESSFUL
8:14:51 AM   PLATFORM NO: CGDB Address:81 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:14:51 AM   PLATFORM NO: CGDB Address:81 SET CONFIGURATION COMMAND IS SUCCESSFUL
8:14:51 AM   PLATFORM NO: CGDB Address:80 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:14:51 AM   PLATFORM NO: CGDB Address:80 SET CONFIGURATION COMMAND IS SUCCESSFUL
8:14:51 AM   PLATFORM NO: CGDB Address:79 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:14:51 AM   PLATFORM NO: CGDB Address:79 SET CONFIGURATION COMMAND IS SUCCESSFUL
8:14:52 AM   PLATFORM NO: CGDB Address:78 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:14:52 AM   PLATFORM NO: CGDB Address:78 SET CONFIGURATION COMMAND IS SUCCESSFUL
8:14:52 AM   PLATFORM NO: CGDB Address:77 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:14:52 AM   PLATFORM NO: CGDB Address:77 SET CONFIGURATION COMMAND IS SUCCESSFUL
8:14:52 AM   PLATFORM NO: CGDB Address:76 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:14:52 AM   PLATFORM NO: CGDB Address:76 SET CONFIGURATION COMMAND IS SUCCESSFUL
8:14:52 AM   PLATFORM NO: CGDB Address:75 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:14:52 AM   PLATFORM NO: CGDB Address:75 SET CONFIGURATION COMMAND IS SUCCESSFUL
8:14:52 AM   PLATFORM NO: CGDB Address:74 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:14:52 AM   PLATFORM NO: CGDB Address:74 SET CONFIGURATION COMMAND IS SUCCESSFUL
8:14:52 AM   PLATFORM NO: CGDB Address:73 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:14:52 AM   PLATFORM NO: CGDB Address:73 SET CONFIGURATION COMMAND IS SUCCESSFUL
8:14:52 AM   PLATFORM NO: CGDB Address:72 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:14:52 AM   PLATFORM NO: CGDB Address:72 SET CONFIGURATION COMMAND IS SUCCESSFUL
8:14:52 AM   PLATFORM NO: CGDB Address:71 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:14:52 AM   PLATFORM NO: CGDB Address:71 SET CONFIGURATION COMMAND IS SUCCESSFUL
8:14:53 AM   PLATFORM NO: CGDB Address:70 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:14:53 AM   PLATFORM NO: CGDB Address:70 SET CONFIGURATION COMMAND IS SUCCESSFUL
8:14:53 AM   PLATFORM NO: CGDB Address:69 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:14:53 AM   PLATFORM NO: CGDB Address:69 SET CONFIGURATION COMMAND IS SUCCESSFUL
8:15:01 AM   PLATFORM NO: CGDB Address:68 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
8:15:09 AM   PLATFORM NO: CGDB Address:67 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
8:15:17 AM   PLATFORM NO: CGDB Address:66 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
8:15:25 AM   PLATFORM NO: CGDB Address:65 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
8:15:33 AM   PLATFORM NO: CGDB Address:64 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
8:16:23 AM    MLDB Address:53 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
8:16:23 AM    MLDB Address:50LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:16:23 AM    MLDB Address:50LINK CHECK COMMAND IS SUCCESSFUL
8:18:23 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:18:23 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND IS SUCCESSFUL
8:18:23 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:18:23 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND IS SUCCESSFUL
8:20:23 AM   PLATFORM NO: AGDB Address:33LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:20:23 AM   PLATFORM NO: AGDB Address:33LINK CHECK COMMAND IS SUCCESSFUL
8:20:23 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:20:23 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND IS SUCCESSFUL
8:20:24 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:20:24 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND IS SUCCESSFUL
8:22:24 AM   PLATFORM NO: CGDB Address:112LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:22:24 AM   PLATFORM NO: CGDB Address:112LINK CHECK COMMAND IS SUCCESSFUL
8:22:24 AM   PLATFORM NO: CGDB Address:111LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:22:24 AM   PLATFORM NO: CGDB Address:111LINK CHECK COMMAND IS SUCCESSFUL
8:22:24 AM   PLATFORM NO: CGDB Address:110LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:22:24 AM   PLATFORM NO: CGDB Address:110LINK CHECK COMMAND IS SUCCESSFUL
8:22:24 AM   PLATFORM NO: CGDB Address:109LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:22:24 AM   PLATFORM NO: CGDB Address:109LINK CHECK COMMAND IS SUCCESSFUL
8:22:24 AM   PLATFORM NO: CGDB Address:108LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:22:24 AM   PLATFORM NO: CGDB Address:108LINK CHECK COMMAND IS SUCCESSFUL
8:22:24 AM   PLATFORM NO: CGDB Address:107LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:22:24 AM   PLATFORM NO: CGDB Address:107LINK CHECK COMMAND IS SUCCESSFUL
8:22:24 AM   PLATFORM NO: CGDB Address:106LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:22:24 AM   PLATFORM NO: CGDB Address:106LINK CHECK COMMAND IS SUCCESSFUL
8:22:25 AM   PLATFORM NO: CGDB Address:105LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:22:25 AM   PLATFORM NO: CGDB Address:105LINK CHECK COMMAND IS SUCCESSFUL
8:22:25 AM   PLATFORM NO: CGDB Address:104LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:22:25 AM   PLATFORM NO: CGDB Address:104LINK CHECK COMMAND IS SUCCESSFUL
8:22:25 AM   PLATFORM NO: CGDB Address:103LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:22:25 AM   PLATFORM NO: CGDB Address:103LINK CHECK COMMAND IS SUCCESSFUL
8:22:25 AM   PLATFORM NO: CGDB Address:102LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:22:25 AM   PLATFORM NO: CGDB Address:102LINK CHECK COMMAND IS SUCCESSFUL
8:22:25 AM   PLATFORM NO: CGDB Address:101LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:22:25 AM   PLATFORM NO: CGDB Address:101LINK CHECK COMMAND IS SUCCESSFUL
8:22:25 AM   PLATFORM NO: CGDB Address:100LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:22:25 AM   PLATFORM NO: CGDB Address:100LINK CHECK COMMAND IS SUCCESSFUL
8:22:25 AM   PLATFORM NO: CGDB Address:99LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:22:25 AM   PLATFORM NO: CGDB Address:99LINK CHECK COMMAND IS SUCCESSFUL
8:22:25 AM   PLATFORM NO: CGDB Address:97LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:22:25 AM   PLATFORM NO: CGDB Address:97LINK CHECK COMMAND IS SUCCESSFUL
8:22:26 AM   PLATFORM NO: CGDB Address:96LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:22:26 AM   PLATFORM NO: CGDB Address:96LINK CHECK COMMAND IS SUCCESSFUL
8:22:26 AM   PLATFORM NO: CGDB Address:95LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:22:26 AM   PLATFORM NO: CGDB Address:95LINK CHECK COMMAND IS SUCCESSFUL
8:22:26 AM   PLATFORM NO: CGDB Address:94LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:22:26 AM   PLATFORM NO: CGDB Address:94LINK CHECK COMMAND IS SUCCESSFUL
8:22:26 AM   PLATFORM NO: CGDB Address:93LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:22:26 AM   PLATFORM NO: CGDB Address:93LINK CHECK COMMAND IS SUCCESSFUL
8:22:26 AM   PLATFORM NO: CGDB Address:92LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:22:26 AM   PLATFORM NO: CGDB Address:92LINK CHECK COMMAND IS SUCCESSFUL
8:22:26 AM   PLATFORM NO: CGDB Address:91LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:22:26 AM   PLATFORM NO: CGDB Address:91LINK CHECK COMMAND IS SUCCESSFUL
8:22:34 AM   PLATFORM NO: CGDB Address:90 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
8:22:42 AM   PLATFORM NO: CGDB Address:89 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
8:22:50 AM   PLATFORM NO: CGDB Address:88 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
8:22:50 AM   PLATFORM NO: CGDB Address:87LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:22:50 AM   PLATFORM NO: CGDB Address:87LINK CHECK COMMAND IS SUCCESSFUL
8:22:50 AM   PLATFORM NO: CGDB Address:86LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:22:50 AM   PLATFORM NO: CGDB Address:86LINK CHECK COMMAND IS SUCCESSFUL
8:22:51 AM   PLATFORM NO: CGDB Address:85LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:22:51 AM   PLATFORM NO: CGDB Address:85LINK CHECK COMMAND IS SUCCESSFUL
8:22:51 AM   PLATFORM NO: CGDB Address:84LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:22:51 AM   PLATFORM NO: CGDB Address:84LINK CHECK COMMAND IS SUCCESSFUL
8:22:51 AM   PLATFORM NO: CGDB Address:83LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:22:51 AM   PLATFORM NO: CGDB Address:83LINK CHECK COMMAND IS SUCCESSFUL
8:22:59 AM   PLATFORM NO: CGDB Address:82 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
8:22:59 AM   PLATFORM NO: CGDB Address:81LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:22:59 AM   PLATFORM NO: CGDB Address:81LINK CHECK COMMAND IS SUCCESSFUL
8:22:59 AM   PLATFORM NO: CGDB Address:80LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:22:59 AM   PLATFORM NO: CGDB Address:80LINK CHECK COMMAND IS SUCCESSFUL
8:22:59 AM   PLATFORM NO: CGDB Address:79LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:22:59 AM   PLATFORM NO: CGDB Address:79LINK CHECK COMMAND IS SUCCESSFUL
8:22:59 AM   PLATFORM NO: CGDB Address:78LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:22:59 AM   PLATFORM NO: CGDB Address:78LINK CHECK COMMAND IS SUCCESSFUL
8:22:59 AM   PLATFORM NO: CGDB Address:77LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:22:59 AM   PLATFORM NO: CGDB Address:77LINK CHECK COMMAND IS SUCCESSFUL
8:23:00 AM   PLATFORM NO: CGDB Address:76LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:23:00 AM   PLATFORM NO: CGDB Address:76LINK CHECK COMMAND IS SUCCESSFUL
8:23:00 AM   PLATFORM NO: CGDB Address:75LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:23:00 AM   PLATFORM NO: CGDB Address:75LINK CHECK COMMAND IS SUCCESSFUL
8:23:00 AM   PLATFORM NO: CGDB Address:74LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:23:00 AM   PLATFORM NO: CGDB Address:74LINK CHECK COMMAND IS SUCCESSFUL
8:23:00 AM   PLATFORM NO: CGDB Address:73LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:23:00 AM   PLATFORM NO: CGDB Address:73LINK CHECK COMMAND IS SUCCESSFUL
8:23:00 AM   PLATFORM NO: CGDB Address:72LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:23:00 AM   PLATFORM NO: CGDB Address:72LINK CHECK COMMAND IS SUCCESSFUL
8:23:00 AM   PLATFORM NO: CGDB Address:71LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:23:00 AM   PLATFORM NO: CGDB Address:71LINK CHECK COMMAND IS SUCCESSFUL
8:23:00 AM   PLATFORM NO: CGDB Address:70LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:23:00 AM   PLATFORM NO: CGDB Address:70LINK CHECK COMMAND IS SUCCESSFUL
8:23:00 AM   PLATFORM NO: CGDB Address:69LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:23:00 AM   PLATFORM NO: CGDB Address:69LINK CHECK COMMAND IS SUCCESSFUL
8:23:08 AM   PLATFORM NO: CGDB Address:68 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
8:23:16 AM   PLATFORM NO: CGDB Address:67 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
8:23:24 AM   PLATFORM NO: CGDB Address:66 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
8:23:32 AM   PLATFORM NO: CGDB Address:65 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
8:23:40 AM   PLATFORM NO: CGDB Address:64 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
8:25:48 AM    MLDB Address:53 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
8:25:48 AM    MLDB Address:50LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:25:48 AM    MLDB Address:50LINK CHECK COMMAND IS SUCCESSFUL
8:27:49 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:27:49 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND IS SUCCESSFUL
8:27:49 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:27:49 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND IS SUCCESSFUL
8:29:21 AM    MLDB Address:53 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
8:29:31 AM    MLDB Address:53 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
8:30:03 AM    MLDB Address:53 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
8:30:17 AM    MLDB Address:53 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
8:31:49 AM   PLATFORM NO: AGDB Address:33LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:31:49 AM   PLATFORM NO: AGDB Address:33LINK CHECK COMMAND IS SUCCESSFUL
8:31:49 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:31:49 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND IS SUCCESSFUL
8:31:49 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:31:49 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND IS SUCCESSFUL
8:33:34 AM    MLDB Address:53 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
8:33:54 AM    MLDB Address:50 DATA PACKET STATUS: Packet Received and proceed successfully 
8:33:54 AM    MLDB Address:50 DATA PACKET IS SUCCESSFUL
8:33:54 AM    MLDB Address:50 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:33:54 AM    MLDB Address:50 SET CONFIGURATION COMMAND IS SUCCESSFUL
8:33:56 AM    MLDB Address:50 DATA PACKET STATUS: Packet Received and proceed successfully 
8:33:56 AM    MLDB Address:50 DATA PACKET IS SUCCESSFUL
8:33:58 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET STATUS: Packet Received and proceed successfully 
8:33:58 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET IS SUCCESSFUL
8:34:06 AM    MLDB Address:53 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
8:34:16 AM    MLDB Address:53 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
8:34:40 AM    MLDB Address:53 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
8:35:49 AM   PLATFORM NO: CGDB Address:112LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:35:49 AM   PLATFORM NO: CGDB Address:112LINK CHECK COMMAND IS SUCCESSFUL
8:35:49 AM   PLATFORM NO: CGDB Address:111LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:35:49 AM   PLATFORM NO: CGDB Address:111LINK CHECK COMMAND IS SUCCESSFUL
8:35:49 AM   PLATFORM NO: CGDB Address:110LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:35:49 AM   PLATFORM NO: CGDB Address:110LINK CHECK COMMAND IS SUCCESSFUL
8:35:50 AM   PLATFORM NO: CGDB Address:109LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:35:50 AM   PLATFORM NO: CGDB Address:109LINK CHECK COMMAND IS SUCCESSFUL
8:35:50 AM   PLATFORM NO: CGDB Address:108LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:35:50 AM   PLATFORM NO: CGDB Address:108LINK CHECK COMMAND IS SUCCESSFUL
8:35:50 AM   PLATFORM NO: CGDB Address:107LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:35:50 AM   PLATFORM NO: CGDB Address:107LINK CHECK COMMAND IS SUCCESSFUL
8:35:50 AM   PLATFORM NO: CGDB Address:106LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:35:50 AM   PLATFORM NO: CGDB Address:106LINK CHECK COMMAND IS SUCCESSFUL
8:35:50 AM   PLATFORM NO: CGDB Address:105LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:35:50 AM   PLATFORM NO: CGDB Address:105LINK CHECK COMMAND IS SUCCESSFUL
8:35:50 AM   PLATFORM NO: CGDB Address:104LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:35:50 AM   PLATFORM NO: CGDB Address:104LINK CHECK COMMAND IS SUCCESSFUL
8:35:50 AM   PLATFORM NO: CGDB Address:103LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:35:50 AM   PLATFORM NO: CGDB Address:103LINK CHECK COMMAND IS SUCCESSFUL
8:35:50 AM   PLATFORM NO: CGDB Address:102LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:35:50 AM   PLATFORM NO: CGDB Address:102LINK CHECK COMMAND IS SUCCESSFUL
8:35:51 AM   PLATFORM NO: CGDB Address:101LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:35:51 AM   PLATFORM NO: CGDB Address:101LINK CHECK COMMAND IS SUCCESSFUL
8:35:51 AM   PLATFORM NO: CGDB Address:100LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:35:51 AM   PLATFORM NO: CGDB Address:100LINK CHECK COMMAND IS SUCCESSFUL
8:35:51 AM   PLATFORM NO: CGDB Address:99LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:35:51 AM   PLATFORM NO: CGDB Address:99LINK CHECK COMMAND IS SUCCESSFUL
8:35:51 AM   PLATFORM NO: CGDB Address:97LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:35:51 AM   PLATFORM NO: CGDB Address:97LINK CHECK COMMAND IS SUCCESSFUL
8:35:51 AM   PLATFORM NO: CGDB Address:96LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:35:51 AM   PLATFORM NO: CGDB Address:96LINK CHECK COMMAND IS SUCCESSFUL
8:35:51 AM   PLATFORM NO: CGDB Address:95LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:35:51 AM   PLATFORM NO: CGDB Address:95LINK CHECK COMMAND IS SUCCESSFUL
8:35:51 AM   PLATFORM NO: CGDB Address:94LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:35:51 AM   PLATFORM NO: CGDB Address:94LINK CHECK COMMAND IS SUCCESSFUL
8:35:51 AM   PLATFORM NO: CGDB Address:93LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:35:51 AM   PLATFORM NO: CGDB Address:93LINK CHECK COMMAND IS SUCCESSFUL
8:35:51 AM   PLATFORM NO: CGDB Address:92LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:35:51 AM   PLATFORM NO: CGDB Address:92LINK CHECK COMMAND IS SUCCESSFUL
8:35:52 AM   PLATFORM NO: CGDB Address:91LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:35:52 AM   PLATFORM NO: CGDB Address:91LINK CHECK COMMAND IS SUCCESSFUL
8:36:00 AM   PLATFORM NO: CGDB Address:90 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
8:36:08 AM   PLATFORM NO: CGDB Address:89 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
8:36:17 AM    MLDB Address:53 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
8:36:32 AM    MLDB Address:53 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
8:36:42 AM    MLDB Address:53 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
8:36:54 AM    MLDB Address:53 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
8:38:16 AM    MLDB Address:53 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
8:38:16 AM    MLDB Address:50LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:38:16 AM    MLDB Address:50LINK CHECK COMMAND IS SUCCESSFUL
8:38:22 AM    MLDB Address:50 DATA PACKET STATUS: Packet Received and proceed successfully 
8:38:22 AM    MLDB Address:50 DATA PACKET IS SUCCESSFUL
8:38:22 AM   DELETE DATA PACKET SEND: AGDB: 33 PLATFORM NO:1 TRAIN NO: 57594 TRAINSTATUS:ARRIVING ON ARRIVAL TIME:09:00 DEPARTURE TIME:09:05
8:38:22 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET STATUS: Packet Received and proceed successfully 
8:38:22 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET IS SUCCESSFUL
8:38:22 AM    MLDB Address:50 DATA PACKET STATUS: Packet Received and proceed successfully 
8:38:22 AM    MLDB Address:50 DATA PACKET IS SUCCESSFUL
8:38:22 AM   DELETE DATA PACKET SEND: AGDB: 33 PLATFORM NO:1 TRAIN NO: 57502 TRAINSTATUS:ARRIVING ON ARRIVAL TIME:09:00 DEPARTURE TIME:09:05
8:38:22 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET STATUS: Packet Received and proceed successfully 
8:38:22 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET IS SUCCESSFUL
8:38:22 AM    MLDB Address:50 DATA PACKET STATUS: Packet Received and proceed successfully 
8:38:22 AM    MLDB Address:50 DATA PACKET IS SUCCESSFUL
8:38:23 AM   DELETE DATA PACKET SEND: AGDB: 33 PLATFORM NO:1 TRAIN NO: 57561 TRAINSTATUS:ARRIVING ON ARRIVAL TIME:09:35 DEPARTURE TIME:09:40
8:38:23 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET STATUS: Packet Received and proceed successfully 
8:38:23 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET IS SUCCESSFUL
8:38:23 AM    MLDB Address:50 DATA PACKET STATUS: Packet Received and proceed successfully 
8:38:23 AM    MLDB Address:50 DATA PACKET IS SUCCESSFUL
8:38:23 AM   DELETE DATA PACKET SEND: AGDB: 33 PLATFORM NO:1 TRAIN NO: 17641 TRAINSTATUS:ARRIVING ON ARRIVAL TIME:10:40 DEPARTURE TIME:10:45
8:38:23 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET STATUS: Packet Received and proceed successfully 
8:38:23 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET IS SUCCESSFUL
8:38:33 AM    MLDB Address:53 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
8:38:35 AM    MLDB Address:50 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:38:35 AM    MLDB Address:50 SET CONFIGURATION COMMAND IS SUCCESSFUL
8:38:37 AM    MLDB Address:50 DATA PACKET STATUS: Packet Received and proceed successfully 
8:38:37 AM    MLDB Address:50 DATA PACKET IS SUCCESSFUL
8:38:39 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET STATUS: Packet Received and proceed successfully 
8:38:39 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET IS SUCCESSFUL
8:39:46 AM    MLDB Address:53 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
8:39:52 AM    MLDB Address:50 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:39:52 AM    MLDB Address:50 SET CONFIGURATION COMMAND IS SUCCESSFUL
8:39:54 AM    MLDB Address:50 DATA PACKET STATUS: Packet Received and proceed successfully 
8:39:54 AM    MLDB Address:50 DATA PACKET IS SUCCESSFUL
8:39:57 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET STATUS: Packet Received and proceed successfully 
8:39:57 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET IS SUCCESSFUL
8:40:05 AM    MLDB Address:53 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
8:40:47 AM    MLDB Address:50 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:40:47 AM    MLDB Address:50 SET CONFIGURATION COMMAND IS SUCCESSFUL
8:40:49 AM    MLDB Address:50 DATA PACKET STATUS: Packet Received and proceed successfully 
8:40:49 AM    MLDB Address:50 DATA PACKET IS SUCCESSFUL
8:40:51 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET STATUS: Packet Received and proceed successfully 
8:40:51 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET IS SUCCESSFUL
8:41:00 AM    MLDB Address:53 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
8:43:50 AM    MLDB Address:50 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:43:50 AM    MLDB Address:50 SET CONFIGURATION COMMAND IS SUCCESSFUL
8:43:52 AM    MLDB Address:50 DATA PACKET STATUS: Packet Received and proceed successfully 
8:43:52 AM    MLDB Address:50 DATA PACKET IS SUCCESSFUL
8:43:54 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET STATUS: Packet Received and proceed successfully 
8:43:54 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET IS SUCCESSFUL
8:44:02 AM    MLDB Address:53 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
8:44:24 AM    MLDB Address:50 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:44:24 AM    MLDB Address:50 SET CONFIGURATION COMMAND IS SUCCESSFUL
8:44:26 AM    MLDB Address:50 DATA PACKET STATUS: Packet Received and proceed successfully 
8:44:26 AM    MLDB Address:50 DATA PACKET IS SUCCESSFUL
8:44:28 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET STATUS: Packet Received and proceed successfully 
8:44:28 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET IS SUCCESSFUL
8:44:38 AM    MLDB Address:53 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
8:46:16 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:46:16 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND IS SUCCESSFUL
8:46:16 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:46:16 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND IS SUCCESSFUL
8:46:17 AM   MDCH Address:239 SOFT RESET COMMAND IS SUCCESSFUL
8:46:21 AM   MDCH Address:239 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:46:21 AM   MDCH Address:239 SET CONFIGURATION COMMAND IS SUCCESSFUL
8:46:22 AM   MDCH Address:239 GET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:46:22 AM   MDCH Address:239 GET CONFIGURATION COMMAND IS SUCCESSFUL
8:46:23 AM   MDCH Address:239LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:46:23 AM   MDCH Address:239LINK CHECK COMMAND IS SUCCESSFUL
8:47:13 AM   Network Configuration Saved
8:47:33 AM   MDCH Address:239 SOFT RESET COMMAND IS SUCCESSFUL
8:47:37 AM   MDCH Address:239 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:47:37 AM   MDCH Address:239 SET CONFIGURATION COMMAND IS SUCCESSFUL
8:47:38 AM   MDCH Address:239 GET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:47:38 AM   MDCH Address:239 GET CONFIGURATION COMMAND IS SUCCESSFUL
8:47:39 AM   MDCH Address:239LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:47:39 AM   MDCH Address:239LINK CHECK COMMAND IS SUCCESSFUL
8:47:44 AM    MLDB Address:58 SOFT RESET COMMAND IS SUCCESSFUL
8:47:57 AM    MLDB Address:58 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
8:47:58 AM    MLDB Address:58 SOFT RESET COMMAND IS SUCCESSFUL
8:48:16 AM   PLATFORM NO: AGDB Address:33LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:48:16 AM   PLATFORM NO: AGDB Address:33LINK CHECK COMMAND IS SUCCESSFUL
8:48:16 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:48:16 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND IS SUCCESSFUL
8:48:16 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:48:16 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND IS SUCCESSFUL
8:48:19 AM    MLDB Address:58 SOFT RESET COMMAND IS SUCCESSFUL
8:48:35 AM    MLDB Address:58 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
8:48:50 AM    MLDB Address:58 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
8:48:59 AM    MLDB Address:58 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
8:50:05 AM    MLDB Address:58 SOFT RESET COMMAND IS SUCCESSFUL
8:50:06 AM    MLDB Address:58 SOFT RESET COMMAND IS SUCCESSFUL
8:50:07 AM    MLDB Address:58 SOFT RESET COMMAND IS SUCCESSFUL
8:50:08 AM    MLDB Address:58 SOFT RESET COMMAND IS SUCCESSFUL
8:50:08 AM    MLDB Address:58 SOFT RESET COMMAND IS SUCCESSFUL
8:50:09 AM    MLDB Address:58 SOFT RESET COMMAND IS SUCCESSFUL
8:50:09 AM    MLDB Address:58 SOFT RESET COMMAND IS SUCCESSFUL
8:50:10 AM    MLDB Address:58 SOFT RESET COMMAND IS SUCCESSFUL
8:50:10 AM    MLDB Address:58 SOFT RESET COMMAND IS SUCCESSFUL
8:50:11 AM    MLDB Address:58 SOFT RESET COMMAND IS SUCCESSFUL
8:50:12 AM    MLDB Address:58 SOFT RESET COMMAND IS SUCCESSFUL
8:50:15 AM    MLDB Address:58 SOFT RESET COMMAND IS SUCCESSFUL
8:50:16 AM   PLATFORM NO: CGDB Address:112LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:50:16 AM   PLATFORM NO: CGDB Address:112LINK CHECK COMMAND IS SUCCESSFUL
8:50:17 AM   PLATFORM NO: CGDB Address:111LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:50:17 AM   PLATFORM NO: CGDB Address:111LINK CHECK COMMAND IS SUCCESSFUL
8:50:17 AM   PLATFORM NO: CGDB Address:110LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:50:17 AM   PLATFORM NO: CGDB Address:110LINK CHECK COMMAND IS SUCCESSFUL
8:50:17 AM   PLATFORM NO: CGDB Address:109LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:50:17 AM   PLATFORM NO: CGDB Address:109LINK CHECK COMMAND IS SUCCESSFUL
8:50:17 AM   PLATFORM NO: CGDB Address:108LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:50:17 AM   PLATFORM NO: CGDB Address:108LINK CHECK COMMAND IS SUCCESSFUL
8:50:17 AM   PLATFORM NO: CGDB Address:107LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:50:17 AM   PLATFORM NO: CGDB Address:107LINK CHECK COMMAND IS SUCCESSFUL
8:50:17 AM   PLATFORM NO: CGDB Address:106LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:50:17 AM   PLATFORM NO: CGDB Address:106LINK CHECK COMMAND IS SUCCESSFUL
8:50:17 AM   PLATFORM NO: CGDB Address:105LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:50:17 AM   PLATFORM NO: CGDB Address:105LINK CHECK COMMAND IS SUCCESSFUL
8:50:17 AM   PLATFORM NO: CGDB Address:104LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:50:17 AM   PLATFORM NO: CGDB Address:104LINK CHECK COMMAND IS SUCCESSFUL
8:50:18 AM   PLATFORM NO: CGDB Address:103LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:50:18 AM   PLATFORM NO: CGDB Address:103LINK CHECK COMMAND IS SUCCESSFUL
8:50:18 AM   PLATFORM NO: CGDB Address:102LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:50:18 AM   PLATFORM NO: CGDB Address:102LINK CHECK COMMAND IS SUCCESSFUL
8:50:18 AM   PLATFORM NO: CGDB Address:101LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:50:18 AM   PLATFORM NO: CGDB Address:101LINK CHECK COMMAND IS SUCCESSFUL
8:50:18 AM   PLATFORM NO: CGDB Address:100LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:50:18 AM   PLATFORM NO: CGDB Address:100LINK CHECK COMMAND IS SUCCESSFUL
8:50:18 AM   PLATFORM NO: CGDB Address:99LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:50:18 AM   PLATFORM NO: CGDB Address:99LINK CHECK COMMAND IS SUCCESSFUL
8:50:18 AM   PLATFORM NO: CGDB Address:97LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:50:18 AM   PLATFORM NO: CGDB Address:97LINK CHECK COMMAND IS SUCCESSFUL
8:50:18 AM   PLATFORM NO: CGDB Address:96LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:50:18 AM   PLATFORM NO: CGDB Address:96LINK CHECK COMMAND IS SUCCESSFUL
8:50:18 AM   PLATFORM NO: CGDB Address:95LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:50:18 AM   PLATFORM NO: CGDB Address:95LINK CHECK COMMAND IS SUCCESSFUL
8:50:19 AM   PLATFORM NO: CGDB Address:94LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:50:19 AM   PLATFORM NO: CGDB Address:94LINK CHECK COMMAND IS SUCCESSFUL
8:50:19 AM   PLATFORM NO: CGDB Address:93LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:50:19 AM   PLATFORM NO: CGDB Address:93LINK CHECK COMMAND IS SUCCESSFUL
8:50:19 AM   PLATFORM NO: CGDB Address:92LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:50:19 AM   PLATFORM NO: CGDB Address:92LINK CHECK COMMAND IS SUCCESSFUL
8:50:19 AM   PLATFORM NO: CGDB Address:91LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:50:19 AM   PLATFORM NO: CGDB Address:91LINK CHECK COMMAND IS SUCCESSFUL
8:50:27 AM   PLATFORM NO: CGDB Address:90 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
8:50:35 AM   PLATFORM NO: CGDB Address:89 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
8:50:43 AM   PLATFORM NO: CGDB Address:88 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
8:50:43 AM   PLATFORM NO: CGDB Address:87LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:50:43 AM   PLATFORM NO: CGDB Address:87LINK CHECK COMMAND IS SUCCESSFUL
8:50:43 AM   PLATFORM NO: CGDB Address:86LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:50:43 AM   PLATFORM NO: CGDB Address:86LINK CHECK COMMAND IS SUCCESSFUL
8:50:43 AM   PLATFORM NO: CGDB Address:85LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:50:43 AM   PLATFORM NO: CGDB Address:85LINK CHECK COMMAND IS SUCCESSFUL
8:50:43 AM   PLATFORM NO: CGDB Address:84LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:50:43 AM   PLATFORM NO: CGDB Address:84LINK CHECK COMMAND IS SUCCESSFUL
8:50:44 AM   PLATFORM NO: CGDB Address:83LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:50:44 AM   PLATFORM NO: CGDB Address:83LINK CHECK COMMAND IS SUCCESSFUL
8:50:52 AM   PLATFORM NO: CGDB Address:82 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
8:50:52 AM   PLATFORM NO: CGDB Address:81LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:50:52 AM   PLATFORM NO: CGDB Address:81LINK CHECK COMMAND IS SUCCESSFUL
8:50:52 AM   PLATFORM NO: CGDB Address:80LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:50:52 AM   PLATFORM NO: CGDB Address:80LINK CHECK COMMAND IS SUCCESSFUL
8:50:54 AM   Network Configuration Saved
8:51:08 AM   MDCH Address:239 SOFT RESET COMMAND IS SUCCESSFUL
8:51:12 AM   MDCH Address:239 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:51:12 AM   MDCH Address:239 SET CONFIGURATION COMMAND IS SUCCESSFUL
8:51:15 AM   MDCH Address:239 GET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:51:15 AM   MDCH Address:239 GET CONFIGURATION COMMAND IS SUCCESSFUL
8:51:16 AM   MDCH Address:239LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:51:16 AM   MDCH Address:239LINK CHECK COMMAND IS SUCCESSFUL
8:51:22 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:51:34 AM    MLDB Address:56 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
8:51:37 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:51:37 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:51:37 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:51:37 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:51:52 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:51:53 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:51:53 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:51:53 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:51:53 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:51:54 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:51:54 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:51:54 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:52:09 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:52:09 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:52:09 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:52:18 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:52:18 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:52:18 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:52:19 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:52:30 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:52:31 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:52:32 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:52:33 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:52:35 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:52:36 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:52:36 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:52:37 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:52:38 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:52:38 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:52:39 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:52:40 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:52:40 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:52:41 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:52:41 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:52:42 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:52:42 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:52:43 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:52:43 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:52:43 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:52:44 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:52:45 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:52:45 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:52:45 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:52:45 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:52:46 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:52:46 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:52:49 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:52:49 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:52:58 AM    MLDB Address:56 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
8:54:41 AM   Network Configuration Saved
8:54:51 AM   MDCH Address:239 SOFT RESET COMMAND IS SUCCESSFUL
8:55:00 AM    MLDB Address:56 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
8:55:00 AM    MLDB Address:50LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:55:00 AM    MLDB Address:50LINK CHECK COMMAND IS SUCCESSFUL
8:55:00 AM   MDCH Address:239 SOFT RESET COMMAND IS SUCCESSFUL
8:55:04 AM   MDCH Address:239 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:55:04 AM   MDCH Address:239 SET CONFIGURATION COMMAND IS SUCCESSFUL
8:55:05 AM   MDCH Address:239 GET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
8:55:05 AM   MDCH Address:239 GET CONFIGURATION COMMAND IS SUCCESSFUL
8:55:06 AM   MDCH Address:239LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:55:06 AM   MDCH Address:239LINK CHECK COMMAND IS SUCCESSFUL
8:55:12 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:55:23 AM    MLDB Address:56 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
8:55:23 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:55:24 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:55:24 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:55:24 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:55:25 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:55:25 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:55:25 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:55:25 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:55:25 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:55:26 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:55:26 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:55:26 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:55:26 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:55:47 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:55:54 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:56:06 AM    MLDB Address:56 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
8:56:07 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:56:07 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:56:08 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:56:08 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:56:08 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:56:08 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:56:08 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:56:09 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:56:09 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:56:09 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:56:10 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:56:10 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:56:10 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:56:10 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:56:10 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:56:11 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:56:11 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:56:14 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:56:15 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:56:16 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:56:16 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:56:17 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:56:19 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:56:20 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:56:20 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:56:21 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:56:22 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:56:23 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:56:24 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:56:35 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:56:36 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:56:38 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:56:38 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:56:38 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:56:38 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:57:00 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:57:00 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND IS SUCCESSFUL
8:57:00 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:57:00 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND IS SUCCESSFUL
8:59:00 AM   PLATFORM NO: AGDB Address:33LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:59:00 AM   PLATFORM NO: AGDB Address:33LINK CHECK COMMAND IS SUCCESSFUL
8:59:00 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:59:00 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND IS SUCCESSFUL
8:59:00 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
8:59:00 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND IS SUCCESSFUL
8:59:43 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:59:45 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:59:47 AM    MLDB Address:56 SOFT RESET COMMAND IS SUCCESSFUL
8:59:59 AM    MLDB Address:56 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
9:00:07 AM    MLDB Address:56 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
9:00:16 AM    MLDB Address:50 DATA PACKET STATUS: Packet Received and proceed successfully 
9:00:16 AM    MLDB Address:50 DATA PACKET IS SUCCESSFUL
9:00:16 AM   DELETE DATA PACKET SEND: AGDB: 33 PLATFORM NO:1 TRAIN NO: 57594 TRAINSTATUS:ON PLATFORM ARRIVAL TIME:09:00 DEPARTURE TIME:09:05
9:00:16 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET STATUS: Packet Received and proceed successfully 
9:00:16 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET IS SUCCESSFUL
9:00:25 AM    MLDB Address:56 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
9:00:27 AM    MLDB Address:50 DATA PACKET STATUS: Packet Received and proceed successfully 
9:00:27 AM    MLDB Address:50 DATA PACKET IS SUCCESSFUL
9:00:27 AM    MLDB Address:50 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
9:00:27 AM    MLDB Address:50 SET CONFIGURATION COMMAND IS SUCCESSFUL
9:00:29 AM    MLDB Address:50 DATA PACKET STATUS: Packet Received and proceed successfully 
9:00:29 AM    MLDB Address:50 DATA PACKET IS SUCCESSFUL
9:00:31 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET STATUS: Packet Received and proceed successfully 
9:00:31 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET IS SUCCESSFUL
9:00:31 AM    MLDB Address:50 DATA PACKET STATUS: Packet Received and proceed successfully 
9:00:31 AM    MLDB Address:50 DATA PACKET IS SUCCESSFUL
9:00:31 AM   DELETE DATA PACKET SEND: AGDB: 33 PLATFORM NO:1 TRAIN NO: 57561 TRAINSTATUS:ARRIVING ON ARRIVAL TIME:09:35 DEPARTURE TIME:09:40
9:00:31 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET STATUS: Packet Received and proceed successfully 
9:00:31 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET IS SUCCESSFUL
9:00:32 AM    MLDB Address:50 DATA PACKET STATUS: Packet Received and proceed successfully 
9:00:32 AM    MLDB Address:50 DATA PACKET IS SUCCESSFUL
9:00:32 AM   DELETE DATA PACKET SEND: AGDB: 33 PLATFORM NO:1 TRAIN NO: 17641 TRAINSTATUS:ARRIVING ON ARRIVAL TIME:10:40 DEPARTURE TIME:10:45
9:00:32 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET STATUS: Packet Received and proceed successfully 
9:00:32 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET IS SUCCESSFUL
9:00:34 AM    MLDB Address:50 DATA PACKET STATUS: Packet Received and proceed successfully 
9:00:34 AM    MLDB Address:50 DATA PACKET IS SUCCESSFUL
9:00:34 AM   DELETE DATA PACKET SEND: AGDB: 33 PLATFORM NO:1 TRAIN NO: 17057 TRAINSTATUS:ARRIVING ON ARRIVAL TIME:11:00 DEPARTURE TIME:11:05
9:00:34 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET STATUS: Packet Received and proceed successfully 
9:00:34 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET IS SUCCESSFUL
9:00:54 AM    MLDB Address:50 DATA PACKET STATUS: Packet Received and proceed successfully 
9:00:54 AM    MLDB Address:50 DATA PACKET IS SUCCESSFUL
9:00:54 AM   DELETE DATA PACKET SEND: AGDB: 33 PLATFORM NO:1 TRAIN NO: 57502 TRAINSTATUS:ON PLATFORM ARRIVAL TIME:09:00 DEPARTURE TIME:09:05
9:00:54 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET STATUS: Packet Received and proceed successfully 
9:00:54 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET IS SUCCESSFUL
9:01:01 AM   PLATFORM NO: CGDB Address:112LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:01:01 AM   PLATFORM NO: CGDB Address:112LINK CHECK COMMAND IS SUCCESSFUL
9:01:01 AM   PLATFORM NO: CGDB Address:111LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:01:01 AM   PLATFORM NO: CGDB Address:111LINK CHECK COMMAND IS SUCCESSFUL
9:01:01 AM   PLATFORM NO: CGDB Address:110LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:01:01 AM   PLATFORM NO: CGDB Address:110LINK CHECK COMMAND IS SUCCESSFUL
9:01:01 AM   PLATFORM NO: CGDB Address:109LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:01:01 AM   PLATFORM NO: CGDB Address:109LINK CHECK COMMAND IS SUCCESSFUL
9:01:01 AM   PLATFORM NO: CGDB Address:108LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:01:01 AM   PLATFORM NO: CGDB Address:108LINK CHECK COMMAND IS SUCCESSFUL
9:01:01 AM   PLATFORM NO: CGDB Address:107LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:01:01 AM   PLATFORM NO: CGDB Address:107LINK CHECK COMMAND IS SUCCESSFUL
9:01:01 AM   PLATFORM NO: CGDB Address:106LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:01:01 AM   PLATFORM NO: CGDB Address:106LINK CHECK COMMAND IS SUCCESSFUL
9:01:01 AM   PLATFORM NO: CGDB Address:105LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:01:01 AM   PLATFORM NO: CGDB Address:105LINK CHECK COMMAND IS SUCCESSFUL
9:01:02 AM   PLATFORM NO: CGDB Address:104LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:01:02 AM   PLATFORM NO: CGDB Address:104LINK CHECK COMMAND IS SUCCESSFUL
9:01:02 AM   PLATFORM NO: CGDB Address:103LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:01:02 AM   PLATFORM NO: CGDB Address:103LINK CHECK COMMAND IS SUCCESSFUL
9:01:02 AM   PLATFORM NO: CGDB Address:102LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:01:02 AM   PLATFORM NO: CGDB Address:102LINK CHECK COMMAND IS SUCCESSFUL
9:01:02 AM   PLATFORM NO: CGDB Address:101LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:01:02 AM   PLATFORM NO: CGDB Address:101LINK CHECK COMMAND IS SUCCESSFUL
9:01:02 AM   PLATFORM NO: CGDB Address:100LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:01:02 AM   PLATFORM NO: CGDB Address:100LINK CHECK COMMAND IS SUCCESSFUL
9:01:02 AM   PLATFORM NO: CGDB Address:99LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:01:02 AM   PLATFORM NO: CGDB Address:99LINK CHECK COMMAND IS SUCCESSFUL
9:01:02 AM   PLATFORM NO: CGDB Address:97LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:01:02 AM   PLATFORM NO: CGDB Address:97LINK CHECK COMMAND IS SUCCESSFUL
9:01:02 AM   PLATFORM NO: CGDB Address:96LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:01:02 AM   PLATFORM NO: CGDB Address:96LINK CHECK COMMAND IS SUCCESSFUL
9:01:03 AM   PLATFORM NO: CGDB Address:95LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:01:03 AM   PLATFORM NO: CGDB Address:95LINK CHECK COMMAND IS SUCCESSFUL
9:01:03 AM   PLATFORM NO: CGDB Address:94LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:01:03 AM   PLATFORM NO: CGDB Address:94LINK CHECK COMMAND IS SUCCESSFUL
9:01:03 AM   PLATFORM NO: CGDB Address:93LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:01:03 AM   PLATFORM NO: CGDB Address:93LINK CHECK COMMAND IS SUCCESSFUL
9:01:03 AM   PLATFORM NO: CGDB Address:92LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:01:03 AM   PLATFORM NO: CGDB Address:92LINK CHECK COMMAND IS SUCCESSFUL
9:01:03 AM   PLATFORM NO: CGDB Address:91LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:01:03 AM   PLATFORM NO: CGDB Address:91LINK CHECK COMMAND IS SUCCESSFUL
9:01:11 AM   PLATFORM NO: CGDB Address:90 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
9:01:19 AM   PLATFORM NO: CGDB Address:89 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
9:01:27 AM   PLATFORM NO: CGDB Address:88 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
9:01:27 AM   PLATFORM NO: CGDB Address:87LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:01:27 AM   PLATFORM NO: CGDB Address:87LINK CHECK COMMAND IS SUCCESSFUL
9:01:27 AM   PLATFORM NO: CGDB Address:86LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:01:27 AM   PLATFORM NO: CGDB Address:86LINK CHECK COMMAND IS SUCCESSFUL
9:01:27 AM   PLATFORM NO: CGDB Address:85LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:01:27 AM   PLATFORM NO: CGDB Address:85LINK CHECK COMMAND IS SUCCESSFUL
9:01:28 AM   PLATFORM NO: CGDB Address:84LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:01:28 AM   PLATFORM NO: CGDB Address:84LINK CHECK COMMAND IS SUCCESSFUL
9:01:28 AM   PLATFORM NO: CGDB Address:83LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:01:28 AM   PLATFORM NO: CGDB Address:83LINK CHECK COMMAND IS SUCCESSFUL
9:01:36 AM   PLATFORM NO: CGDB Address:82 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
9:01:44 AM   PLATFORM NO: CGDB Address:81 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
9:01:44 AM   PLATFORM NO: CGDB Address:80LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:01:44 AM   PLATFORM NO: CGDB Address:80LINK CHECK COMMAND IS SUCCESSFUL
9:01:44 AM   PLATFORM NO: CGDB Address:79LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:01:44 AM   PLATFORM NO: CGDB Address:79LINK CHECK COMMAND IS SUCCESSFUL
9:01:44 AM   PLATFORM NO: CGDB Address:78LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:01:44 AM   PLATFORM NO: CGDB Address:78LINK CHECK COMMAND IS SUCCESSFUL
9:01:44 AM   PLATFORM NO: CGDB Address:77LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:01:44 AM   PLATFORM NO: CGDB Address:77LINK CHECK COMMAND IS SUCCESSFUL
9:01:44 AM   PLATFORM NO: CGDB Address:76LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:01:44 AM   PLATFORM NO: CGDB Address:76LINK CHECK COMMAND IS SUCCESSFUL
9:01:44 AM   PLATFORM NO: CGDB Address:75LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:01:44 AM   PLATFORM NO: CGDB Address:75LINK CHECK COMMAND IS SUCCESSFUL
9:01:45 AM   PLATFORM NO: CGDB Address:74LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:01:45 AM   PLATFORM NO: CGDB Address:74LINK CHECK COMMAND IS SUCCESSFUL
9:01:45 AM   PLATFORM NO: CGDB Address:73LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:01:45 AM   PLATFORM NO: CGDB Address:73LINK CHECK COMMAND IS SUCCESSFUL
9:01:45 AM   PLATFORM NO: CGDB Address:72LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:01:45 AM   PLATFORM NO: CGDB Address:72LINK CHECK COMMAND IS SUCCESSFUL
9:01:45 AM   PLATFORM NO: CGDB Address:71LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:01:45 AM   PLATFORM NO: CGDB Address:71LINK CHECK COMMAND IS SUCCESSFUL
9:01:45 AM   PLATFORM NO: CGDB Address:70LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:01:45 AM   PLATFORM NO: CGDB Address:70LINK CHECK COMMAND IS SUCCESSFUL
9:01:45 AM   PLATFORM NO: CGDB Address:69LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:01:45 AM   PLATFORM NO: CGDB Address:69LINK CHECK COMMAND IS SUCCESSFUL
9:01:53 AM   PLATFORM NO: CGDB Address:68 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
9:02:01 AM   PLATFORM NO: CGDB Address:67 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
9:02:09 AM   PLATFORM NO: CGDB Address:66 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
9:02:17 AM   PLATFORM NO: CGDB Address:65 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
9:02:25 AM   PLATFORM NO: CGDB Address:64 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
9:04:33 AM    MLDB Address:56 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
9:04:33 AM    MLDB Address:50LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:04:33 AM    MLDB Address:50LINK CHECK COMMAND IS SUCCESSFUL
9:05:39 AM    MLDB Address:56 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
9:05:42 AM    MLDB Address:50 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
9:05:42 AM    MLDB Address:50 SET CONFIGURATION COMMAND IS SUCCESSFUL
9:05:44 AM    MLDB Address:50 DATA PACKET STATUS: Packet Received and proceed successfully 
9:05:44 AM    MLDB Address:50 DATA PACKET IS SUCCESSFUL
9:05:46 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET STATUS: Packet Received and proceed successfully 
9:05:46 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET IS SUCCESSFUL
9:05:54 AM    MLDB Address:56 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
9:05:57 AM    MLDB Address:50 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
9:05:57 AM    MLDB Address:50 SET CONFIGURATION COMMAND IS SUCCESSFUL
9:05:59 AM    MLDB Address:50 DATA PACKET STATUS: Packet Received and proceed successfully 
9:05:59 AM    MLDB Address:50 DATA PACKET IS SUCCESSFUL
9:06:01 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET STATUS: Packet Received and proceed successfully 
9:06:01 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET IS SUCCESSFUL
9:06:11 AM    MLDB Address:56 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
9:06:13 AM    MLDB Address:50 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
9:06:13 AM    MLDB Address:50 SET CONFIGURATION COMMAND IS SUCCESSFUL
9:06:15 AM    MLDB Address:50 DATA PACKET STATUS: Packet Received and proceed successfully 
9:06:15 AM    MLDB Address:50 DATA PACKET IS SUCCESSFUL
9:06:17 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET STATUS: Packet Received and proceed successfully 
9:06:17 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET IS SUCCESSFUL
9:06:26 AM    MLDB Address:56 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
9:06:31 AM    MLDB Address:50 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
9:06:31 AM    MLDB Address:50 SET CONFIGURATION COMMAND IS SUCCESSFUL
9:06:33 AM    MLDB Address:50 DATA PACKET STATUS: Packet Received and proceed successfully 
9:06:33 AM    MLDB Address:50 DATA PACKET IS SUCCESSFUL
9:06:35 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET STATUS: Packet Received and proceed successfully 
9:06:35 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET IS SUCCESSFUL
9:07:29 AM    MLDB Address:56 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
9:07:31 AM    MLDB Address:50 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
9:07:31 AM    MLDB Address:50 SET CONFIGURATION COMMAND IS SUCCESSFUL
9:07:33 AM    MLDB Address:50 DATA PACKET STATUS: Packet Received and proceed successfully 
9:07:33 AM    MLDB Address:50 DATA PACKET IS SUCCESSFUL
9:07:36 AM   PLATFORM NO:3 PDB Address:1 DATA PACKET STATUS: Packet Received and proceed successfully 
9:07:36 AM   PLATFORM NO:3 PDB Address:1 DATA PACKET IS SUCCESSFUL
9:07:40 AM   PLATFORM NO:1 AGDB Address:36 DATA PACKET STATUS: Packet Received and proceed successfully 
9:07:40 AM   PLATFORM NO:1 AGDB Address:36 DATA PACKET IS SUCCESSFUL
9:07:40 AM    MLDB Address:56 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
9:07:40 AM    MLDB Address:56 SET CONFIGURATION RESPONSE PACKET: CHECKSUM FAILED
9:07:48 AM    MLDB Address:56 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
9:08:19 AM    MLDB Address:50 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
9:08:19 AM    MLDB Address:50 SET CONFIGURATION COMMAND IS SUCCESSFUL
9:08:21 AM    MLDB Address:50 DATA PACKET STATUS: Packet Received and proceed successfully 
9:08:21 AM    MLDB Address:50 DATA PACKET IS SUCCESSFUL
9:08:23 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET STATUS: Packet Received and proceed successfully 
9:08:23 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET IS SUCCESSFUL
9:08:31 AM    MLDB Address:56 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
9:08:41 AM    MLDB Address:56 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
9:08:54 AM    MLDB Address:56 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
9:09:06 AM    MLDB Address:56 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
9:10:33 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:10:33 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND IS SUCCESSFUL
9:10:33 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:10:33 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND IS SUCCESSFUL
9:12:34 AM   PLATFORM NO: AGDB Address:33LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:12:34 AM   PLATFORM NO: AGDB Address:33LINK CHECK COMMAND IS SUCCESSFUL
9:12:34 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:12:34 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND IS SUCCESSFUL
9:12:34 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:12:34 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND IS SUCCESSFUL
9:13:12 AM    MLDB Address:56 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
9:13:12 AM    MLDB Address:50 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
9:13:12 AM    MLDB Address:50 SET CONFIGURATION COMMAND IS SUCCESSFUL
9:13:12 AM   PLATFORM NO: PDB Address:162 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
9:13:12 AM   PLATFORM NO: PDB Address:162 SET CONFIGURATION COMMAND IS SUCCESSFUL
9:13:12 AM   PLATFORM NO: PDB Address:162 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
9:13:12 AM   PLATFORM NO: PDB Address:162 SET CONFIGURATION COMMAND IS SUCCESSFUL
9:13:12 AM   PLATFORM NO: AGDB Address:33 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
9:13:12 AM   PLATFORM NO: AGDB Address:33 SET CONFIGURATION COMMAND IS SUCCESSFUL
9:13:12 AM   PLATFORM NO: AGDB Address:36 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
9:13:12 AM   PLATFORM NO: AGDB Address:36 SET CONFIGURATION COMMAND IS SUCCESSFUL
9:13:12 AM   PLATFORM NO: AGDB Address:36 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
9:13:12 AM   PLATFORM NO: AGDB Address:36 SET CONFIGURATION COMMAND IS SUCCESSFUL
9:13:12 AM   PLATFORM NO: CGDB Address:112 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
9:13:12 AM   PLATFORM NO: CGDB Address:112 SET CONFIGURATION COMMAND IS SUCCESSFUL
9:13:12 AM   PLATFORM NO: CGDB Address:111 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
9:13:12 AM   PLATFORM NO: CGDB Address:111 SET CONFIGURATION COMMAND IS SUCCESSFUL
9:13:13 AM   PLATFORM NO: CGDB Address:110 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
9:13:13 AM   PLATFORM NO: CGDB Address:110 SET CONFIGURATION COMMAND IS SUCCESSFUL
9:13:13 AM   PLATFORM NO: CGDB Address:109 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
9:13:13 AM   PLATFORM NO: CGDB Address:109 SET CONFIGURATION COMMAND IS SUCCESSFUL
9:13:13 AM   PLATFORM NO: CGDB Address:108 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
9:13:13 AM   PLATFORM NO: CGDB Address:108 SET CONFIGURATION COMMAND IS SUCCESSFUL
9:13:13 AM   PLATFORM NO: CGDB Address:107 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
9:13:13 AM   PLATFORM NO: CGDB Address:107 SET CONFIGURATION COMMAND IS SUCCESSFUL
9:13:13 AM   PLATFORM NO: CGDB Address:106 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
9:13:13 AM   PLATFORM NO: CGDB Address:106 SET CONFIGURATION COMMAND IS SUCCESSFUL
9:13:13 AM   PLATFORM NO: CGDB Address:105 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
9:13:13 AM   PLATFORM NO: CGDB Address:105 SET CONFIGURATION COMMAND IS SUCCESSFUL
9:13:13 AM   PLATFORM NO: CGDB Address:104 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
9:13:13 AM   PLATFORM NO: CGDB Address:104 SET CONFIGURATION COMMAND IS SUCCESSFUL
9:13:13 AM   PLATFORM NO: CGDB Address:103 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
9:13:13 AM   PLATFORM NO: CGDB Address:103 SET CONFIGURATION COMMAND IS SUCCESSFUL
9:13:14 AM   PLATFORM NO: CGDB Address:102 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
9:13:14 AM   PLATFORM NO: CGDB Address:102 SET CONFIGURATION COMMAND IS SUCCESSFUL
9:13:14 AM   PLATFORM NO: CGDB Address:101 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
9:13:14 AM   PLATFORM NO: CGDB Address:101 SET CONFIGURATION COMMAND IS SUCCESSFUL
9:13:14 AM   PLATFORM NO: CGDB Address:100 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
9:13:14 AM   PLATFORM NO: CGDB Address:100 SET CONFIGURATION COMMAND IS SUCCESSFUL
9:13:14 AM   PLATFORM NO: CGDB Address:99 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
9:13:14 AM   PLATFORM NO: CGDB Address:99 SET CONFIGURATION COMMAND IS SUCCESSFUL
9:13:14 AM   PLATFORM NO: CGDB Address:97 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
9:13:14 AM   PLATFORM NO: CGDB Address:97 SET CONFIGURATION COMMAND IS SUCCESSFUL
9:13:14 AM   PLATFORM NO: CGDB Address:96 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
9:13:14 AM   PLATFORM NO: CGDB Address:96 SET CONFIGURATION COMMAND IS SUCCESSFUL
9:13:14 AM   PLATFORM NO: CGDB Address:95 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
9:13:14 AM   PLATFORM NO: CGDB Address:95 SET CONFIGURATION COMMAND IS SUCCESSFUL
9:13:14 AM   PLATFORM NO: CGDB Address:94 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
9:13:14 AM   PLATFORM NO: CGDB Address:94 SET CONFIGURATION COMMAND IS SUCCESSFUL
9:13:15 AM   PLATFORM NO: CGDB Address:93 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
9:13:15 AM   PLATFORM NO: CGDB Address:93 SET CONFIGURATION COMMAND IS SUCCESSFUL
9:13:15 AM   PLATFORM NO: CGDB Address:92 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
9:13:15 AM   PLATFORM NO: CGDB Address:92 SET CONFIGURATION COMMAND IS SUCCESSFUL
9:13:15 AM   PLATFORM NO: CGDB Address:91 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
9:13:15 AM   PLATFORM NO: CGDB Address:91 SET CONFIGURATION COMMAND IS SUCCESSFUL
9:13:23 AM   PLATFORM NO: CGDB Address:90 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
9:13:31 AM   PLATFORM NO: CGDB Address:89 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
9:13:39 AM   PLATFORM NO: CGDB Address:88 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
9:13:39 AM   PLATFORM NO: CGDB Address:87 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
9:13:39 AM   PLATFORM NO: CGDB Address:87 SET CONFIGURATION COMMAND IS SUCCESSFUL
9:13:39 AM   PLATFORM NO: CGDB Address:86 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
9:13:39 AM   PLATFORM NO: CGDB Address:86 SET CONFIGURATION COMMAND IS SUCCESSFUL
9:13:39 AM   PLATFORM NO: CGDB Address:85 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
9:13:39 AM   PLATFORM NO: CGDB Address:85 SET CONFIGURATION COMMAND IS SUCCESSFUL
9:13:39 AM   PLATFORM NO: CGDB Address:84 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
9:13:39 AM   PLATFORM NO: CGDB Address:84 SET CONFIGURATION COMMAND IS SUCCESSFUL
9:13:40 AM   PLATFORM NO: CGDB Address:83 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
9:13:40 AM   PLATFORM NO: CGDB Address:83 SET CONFIGURATION COMMAND IS SUCCESSFUL
9:13:40 AM   PLATFORM NO: CGDB Address:82 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
9:13:40 AM   PLATFORM NO: CGDB Address:82 SET CONFIGURATION COMMAND IS SUCCESSFUL
9:13:40 AM   PLATFORM NO: CGDB Address:81 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
9:13:40 AM   PLATFORM NO: CGDB Address:81 SET CONFIGURATION COMMAND IS SUCCESSFUL
9:13:40 AM   PLATFORM NO: CGDB Address:80 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
9:13:40 AM   PLATFORM NO: CGDB Address:80 SET CONFIGURATION COMMAND IS SUCCESSFUL
9:13:40 AM   PLATFORM NO: CGDB Address:79 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
9:13:40 AM   PLATFORM NO: CGDB Address:79 SET CONFIGURATION COMMAND IS SUCCESSFUL
9:13:40 AM   PLATFORM NO: CGDB Address:78 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
9:13:40 AM   PLATFORM NO: CGDB Address:78 SET CONFIGURATION COMMAND IS SUCCESSFUL
9:13:40 AM   PLATFORM NO: CGDB Address:77 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
9:13:40 AM   PLATFORM NO: CGDB Address:77 SET CONFIGURATION COMMAND IS SUCCESSFUL
9:13:40 AM   PLATFORM NO: CGDB Address:76 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
9:13:40 AM   PLATFORM NO: CGDB Address:76 SET CONFIGURATION COMMAND IS SUCCESSFUL
9:13:41 AM   PLATFORM NO: CGDB Address:75 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
9:13:41 AM   PLATFORM NO: CGDB Address:75 SET CONFIGURATION COMMAND IS SUCCESSFUL
9:13:41 AM   PLATFORM NO: CGDB Address:74 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
9:13:41 AM   PLATFORM NO: CGDB Address:74 SET CONFIGURATION COMMAND IS SUCCESSFUL
9:13:41 AM   PLATFORM NO: CGDB Address:73 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
9:13:41 AM   PLATFORM NO: CGDB Address:73 SET CONFIGURATION COMMAND IS SUCCESSFUL
9:13:41 AM   PLATFORM NO: CGDB Address:72 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
9:13:41 AM   PLATFORM NO: CGDB Address:72 SET CONFIGURATION COMMAND IS SUCCESSFUL
9:13:41 AM   PLATFORM NO: CGDB Address:71 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
9:13:41 AM   PLATFORM NO: CGDB Address:71 SET CONFIGURATION COMMAND IS SUCCESSFUL
9:13:41 AM   PLATFORM NO: CGDB Address:70 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
9:13:41 AM   PLATFORM NO: CGDB Address:70 SET CONFIGURATION COMMAND IS SUCCESSFUL
9:13:41 AM   PLATFORM NO: CGDB Address:69 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
9:13:41 AM   PLATFORM NO: CGDB Address:69 SET CONFIGURATION COMMAND IS SUCCESSFUL
9:13:49 AM   PLATFORM NO: CGDB Address:68 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
9:13:57 AM   PLATFORM NO: CGDB Address:67 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
9:14:05 AM   PLATFORM NO: CGDB Address:66 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
9:14:13 AM   PLATFORM NO: CGDB Address:65 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
9:14:21 AM   PLATFORM NO: CGDB Address:64 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
9:14:34 AM   PLATFORM NO: CGDB Address:112LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:14:34 AM   PLATFORM NO: CGDB Address:112LINK CHECK COMMAND IS SUCCESSFUL
9:14:34 AM   PLATFORM NO: CGDB Address:111LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:14:34 AM   PLATFORM NO: CGDB Address:111LINK CHECK COMMAND IS SUCCESSFUL
9:14:34 AM   PLATFORM NO: CGDB Address:110LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:14:34 AM   PLATFORM NO: CGDB Address:110LINK CHECK COMMAND IS SUCCESSFUL
9:14:34 AM   PLATFORM NO: CGDB Address:109LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:14:34 AM   PLATFORM NO: CGDB Address:109LINK CHECK COMMAND IS SUCCESSFUL
9:14:34 AM   PLATFORM NO: CGDB Address:108LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:14:34 AM   PLATFORM NO: CGDB Address:108LINK CHECK COMMAND IS SUCCESSFUL
9:14:35 AM   PLATFORM NO: CGDB Address:107LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:14:35 AM   PLATFORM NO: CGDB Address:107LINK CHECK COMMAND IS SUCCESSFUL
9:14:35 AM   PLATFORM NO: CGDB Address:106LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:14:35 AM   PLATFORM NO: CGDB Address:106LINK CHECK COMMAND IS SUCCESSFUL
9:14:35 AM   PLATFORM NO: CGDB Address:105LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:14:35 AM   PLATFORM NO: CGDB Address:105LINK CHECK COMMAND IS SUCCESSFUL
9:14:35 AM   PLATFORM NO: CGDB Address:104LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:14:35 AM   PLATFORM NO: CGDB Address:104LINK CHECK COMMAND IS SUCCESSFUL
9:14:35 AM   PLATFORM NO: CGDB Address:103LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:14:35 AM   PLATFORM NO: CGDB Address:103LINK CHECK COMMAND IS SUCCESSFUL
9:14:35 AM   PLATFORM NO: CGDB Address:102LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:14:35 AM   PLATFORM NO: CGDB Address:102LINK CHECK COMMAND IS SUCCESSFUL
9:14:35 AM   PLATFORM NO: CGDB Address:101LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:14:35 AM   PLATFORM NO: CGDB Address:101LINK CHECK COMMAND IS SUCCESSFUL
9:14:35 AM   PLATFORM NO: CGDB Address:100LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:14:35 AM   PLATFORM NO: CGDB Address:100LINK CHECK COMMAND IS SUCCESSFUL
9:14:35 AM   PLATFORM NO: CGDB Address:99LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:14:36 AM   PLATFORM NO: CGDB Address:99LINK CHECK COMMAND IS SUCCESSFUL
9:14:36 AM   PLATFORM NO: CGDB Address:97LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:14:36 AM   PLATFORM NO: CGDB Address:97LINK CHECK COMMAND IS SUCCESSFUL
9:14:36 AM   PLATFORM NO: CGDB Address:96LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:14:36 AM   PLATFORM NO: CGDB Address:96LINK CHECK COMMAND IS SUCCESSFUL
9:14:36 AM   PLATFORM NO: CGDB Address:95LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:14:36 AM   PLATFORM NO: CGDB Address:95LINK CHECK COMMAND IS SUCCESSFUL
9:14:36 AM   PLATFORM NO: CGDB Address:94LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:14:36 AM   PLATFORM NO: CGDB Address:94LINK CHECK COMMAND IS SUCCESSFUL
9:14:36 AM   PLATFORM NO: CGDB Address:93LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:14:36 AM   PLATFORM NO: CGDB Address:93LINK CHECK COMMAND IS SUCCESSFUL
9:14:36 AM   PLATFORM NO: CGDB Address:92LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:14:36 AM   PLATFORM NO: CGDB Address:92LINK CHECK COMMAND IS SUCCESSFUL
9:14:36 AM   PLATFORM NO: CGDB Address:91LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:14:36 AM   PLATFORM NO: CGDB Address:91LINK CHECK COMMAND IS SUCCESSFUL
9:14:44 AM   PLATFORM NO: CGDB Address:90 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
9:14:52 AM   PLATFORM NO: CGDB Address:89 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
9:15:00 AM   PLATFORM NO: CGDB Address:88 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
9:15:00 AM   PLATFORM NO: CGDB Address:87LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:15:00 AM   PLATFORM NO: CGDB Address:87LINK CHECK COMMAND IS SUCCESSFUL
9:15:01 AM   PLATFORM NO: CGDB Address:86LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:15:01 AM   PLATFORM NO: CGDB Address:86LINK CHECK COMMAND IS SUCCESSFUL
9:15:01 AM   PLATFORM NO: CGDB Address:85LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:15:01 AM   PLATFORM NO: CGDB Address:85LINK CHECK COMMAND IS SUCCESSFUL
9:15:01 AM   PLATFORM NO: CGDB Address:84LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:15:01 AM   PLATFORM NO: CGDB Address:84LINK CHECK COMMAND IS SUCCESSFUL
9:15:01 AM   PLATFORM NO: CGDB Address:83LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:15:01 AM   PLATFORM NO: CGDB Address:83LINK CHECK COMMAND IS SUCCESSFUL
9:15:09 AM   PLATFORM NO: CGDB Address:82 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
9:15:09 AM   PLATFORM NO: CGDB Address:81LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:15:09 AM   PLATFORM NO: CGDB Address:81LINK CHECK COMMAND IS SUCCESSFUL
9:15:09 AM   PLATFORM NO: CGDB Address:80LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:15:09 AM   PLATFORM NO: CGDB Address:80LINK CHECK COMMAND IS SUCCESSFUL
9:15:09 AM   PLATFORM NO: CGDB Address:79LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:15:09 AM   PLATFORM NO: CGDB Address:79LINK CHECK COMMAND IS SUCCESSFUL
9:15:09 AM   PLATFORM NO: CGDB Address:78LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:15:09 AM   PLATFORM NO: CGDB Address:78LINK CHECK COMMAND IS SUCCESSFUL
9:15:10 AM   PLATFORM NO: CGDB Address:77LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:15:10 AM   PLATFORM NO: CGDB Address:77LINK CHECK COMMAND IS SUCCESSFUL
9:15:10 AM   PLATFORM NO: CGDB Address:76LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:15:10 AM   PLATFORM NO: CGDB Address:76LINK CHECK COMMAND IS SUCCESSFUL
9:15:10 AM   PLATFORM NO: CGDB Address:75LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:15:10 AM   PLATFORM NO: CGDB Address:75LINK CHECK COMMAND IS SUCCESSFUL
9:15:10 AM   PLATFORM NO: CGDB Address:74LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:15:10 AM   PLATFORM NO: CGDB Address:74LINK CHECK COMMAND IS SUCCESSFUL
9:15:10 AM   PLATFORM NO: CGDB Address:73LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:15:10 AM   PLATFORM NO: CGDB Address:73LINK CHECK COMMAND IS SUCCESSFUL
9:15:10 AM   PLATFORM NO: CGDB Address:72LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:15:10 AM   PLATFORM NO: CGDB Address:72LINK CHECK COMMAND IS SUCCESSFUL
9:15:10 AM   PLATFORM NO: CGDB Address:71LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:15:10 AM   PLATFORM NO: CGDB Address:71LINK CHECK COMMAND IS SUCCESSFUL
9:15:10 AM   PLATFORM NO: CGDB Address:70LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:15:10 AM   PLATFORM NO: CGDB Address:70LINK CHECK COMMAND IS SUCCESSFUL
9:15:11 AM   PLATFORM NO: CGDB Address:69LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:15:11 AM   PLATFORM NO: CGDB Address:69LINK CHECK COMMAND IS SUCCESSFUL
9:15:19 AM   PLATFORM NO: CGDB Address:68 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
9:15:27 AM   PLATFORM NO: CGDB Address:67 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
9:15:35 AM   PLATFORM NO: CGDB Address:66 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
9:15:43 AM   PLATFORM NO: CGDB Address:65 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
9:15:51 AM   PLATFORM NO: CGDB Address:64 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
9:17:59 AM    MLDB Address:56 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
9:17:59 AM    MLDB Address:50LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:17:59 AM    MLDB Address:50LINK CHECK COMMAND IS SUCCESSFUL
9:19:59 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:19:59 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND IS SUCCESSFUL
9:19:59 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:19:59 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND IS SUCCESSFUL
9:21:59 AM   PLATFORM NO: AGDB Address:33LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:21:59 AM   PLATFORM NO: AGDB Address:33LINK CHECK COMMAND IS SUCCESSFUL
9:21:59 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:21:59 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND IS SUCCESSFUL
9:21:59 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:21:59 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND IS SUCCESSFUL
9:23:59 AM   PLATFORM NO: CGDB Address:112LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:23:59 AM   PLATFORM NO: CGDB Address:112LINK CHECK COMMAND IS SUCCESSFUL
9:23:59 AM   PLATFORM NO: CGDB Address:111LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:23:59 AM   PLATFORM NO: CGDB Address:111LINK CHECK COMMAND IS SUCCESSFUL
9:24:00 AM   PLATFORM NO: CGDB Address:110LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:24:00 AM   PLATFORM NO: CGDB Address:110LINK CHECK COMMAND IS SUCCESSFUL
9:24:00 AM   PLATFORM NO: CGDB Address:109LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:24:00 AM   PLATFORM NO: CGDB Address:109LINK CHECK COMMAND IS SUCCESSFUL
9:24:00 AM   PLATFORM NO: CGDB Address:108LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:24:00 AM   PLATFORM NO: CGDB Address:108LINK CHECK COMMAND IS SUCCESSFUL
9:24:00 AM   PLATFORM NO: CGDB Address:107LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:24:00 AM   PLATFORM NO: CGDB Address:107LINK CHECK COMMAND IS SUCCESSFUL
9:24:00 AM   PLATFORM NO: CGDB Address:106LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:24:00 AM   PLATFORM NO: CGDB Address:106LINK CHECK COMMAND IS SUCCESSFUL
9:24:00 AM   PLATFORM NO: CGDB Address:105LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:24:00 AM   PLATFORM NO: CGDB Address:105LINK CHECK COMMAND IS SUCCESSFUL
9:24:00 AM   PLATFORM NO: CGDB Address:104LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:24:00 AM   PLATFORM NO: CGDB Address:104LINK CHECK COMMAND IS SUCCESSFUL
9:24:00 AM   PLATFORM NO: CGDB Address:103LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:24:00 AM   PLATFORM NO: CGDB Address:103LINK CHECK COMMAND IS SUCCESSFUL
9:24:01 AM   PLATFORM NO: CGDB Address:102LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:24:01 AM   PLATFORM NO: CGDB Address:102LINK CHECK COMMAND IS SUCCESSFUL
9:24:01 AM   PLATFORM NO: CGDB Address:101LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:24:01 AM   PLATFORM NO: CGDB Address:101LINK CHECK COMMAND IS SUCCESSFUL
9:24:01 AM   PLATFORM NO: CGDB Address:100LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:24:01 AM   PLATFORM NO: CGDB Address:100LINK CHECK COMMAND IS SUCCESSFUL
9:24:01 AM   PLATFORM NO: CGDB Address:99LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:24:01 AM   PLATFORM NO: CGDB Address:99LINK CHECK COMMAND IS SUCCESSFUL
9:24:01 AM   PLATFORM NO: CGDB Address:97LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:24:01 AM   PLATFORM NO: CGDB Address:97LINK CHECK COMMAND IS SUCCESSFUL
9:24:01 AM   PLATFORM NO: CGDB Address:96LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:24:01 AM   PLATFORM NO: CGDB Address:96LINK CHECK COMMAND IS SUCCESSFUL
9:24:01 AM   PLATFORM NO: CGDB Address:95LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:24:01 AM   PLATFORM NO: CGDB Address:95LINK CHECK COMMAND IS SUCCESSFUL
9:24:01 AM   PLATFORM NO: CGDB Address:94LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:24:01 AM   PLATFORM NO: CGDB Address:94LINK CHECK COMMAND IS SUCCESSFUL
9:24:02 AM   PLATFORM NO: CGDB Address:93LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:24:02 AM   PLATFORM NO: CGDB Address:93LINK CHECK COMMAND IS SUCCESSFUL
9:24:02 AM   PLATFORM NO: CGDB Address:92LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:24:02 AM   PLATFORM NO: CGDB Address:92LINK CHECK COMMAND IS SUCCESSFUL
9:24:02 AM   PLATFORM NO: CGDB Address:91LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:24:02 AM   PLATFORM NO: CGDB Address:91LINK CHECK COMMAND IS SUCCESSFUL
9:24:10 AM   PLATFORM NO: CGDB Address:90 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
9:24:18 AM   PLATFORM NO: CGDB Address:89 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
9:24:26 AM   PLATFORM NO: CGDB Address:88 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
9:24:26 AM   PLATFORM NO: CGDB Address:87LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:24:26 AM   PLATFORM NO: CGDB Address:87LINK CHECK COMMAND IS SUCCESSFUL
9:24:26 AM   PLATFORM NO: CGDB Address:86LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:24:26 AM   PLATFORM NO: CGDB Address:86LINK CHECK COMMAND IS SUCCESSFUL
9:24:26 AM   PLATFORM NO: CGDB Address:85LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:24:26 AM   PLATFORM NO: CGDB Address:85LINK CHECK COMMAND IS SUCCESSFUL
9:24:26 AM   PLATFORM NO: CGDB Address:84LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:24:26 AM   PLATFORM NO: CGDB Address:84LINK CHECK COMMAND IS SUCCESSFUL
9:24:26 AM   PLATFORM NO: CGDB Address:83LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:24:26 AM   PLATFORM NO: CGDB Address:83LINK CHECK COMMAND IS SUCCESSFUL
9:24:34 AM   PLATFORM NO: CGDB Address:82 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
9:24:35 AM   PLATFORM NO: CGDB Address:81LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:24:35 AM   PLATFORM NO: CGDB Address:81LINK CHECK COMMAND IS SUCCESSFUL
9:24:35 AM   PLATFORM NO: CGDB Address:80LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:24:35 AM   PLATFORM NO: CGDB Address:80LINK CHECK COMMAND IS SUCCESSFUL
9:24:35 AM   PLATFORM NO: CGDB Address:79LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:24:35 AM   PLATFORM NO: CGDB Address:79LINK CHECK COMMAND IS SUCCESSFUL
9:24:35 AM   PLATFORM NO: CGDB Address:78LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:24:35 AM   PLATFORM NO: CGDB Address:78LINK CHECK COMMAND IS SUCCESSFUL
9:24:35 AM   PLATFORM NO: CGDB Address:77LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:24:35 AM   PLATFORM NO: CGDB Address:77LINK CHECK COMMAND IS SUCCESSFUL
9:24:35 AM   PLATFORM NO: CGDB Address:76LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:24:35 AM   PLATFORM NO: CGDB Address:76LINK CHECK COMMAND IS SUCCESSFUL
9:24:35 AM   PLATFORM NO: CGDB Address:75LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:24:35 AM   PLATFORM NO: CGDB Address:75LINK CHECK COMMAND IS SUCCESSFUL
9:24:35 AM   PLATFORM NO: CGDB Address:74LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:24:35 AM   PLATFORM NO: CGDB Address:74LINK CHECK COMMAND IS SUCCESSFUL
9:24:36 AM   PLATFORM NO: CGDB Address:73LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:24:36 AM   PLATFORM NO: CGDB Address:73LINK CHECK COMMAND IS SUCCESSFUL
9:24:36 AM   PLATFORM NO: CGDB Address:72LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:24:36 AM   PLATFORM NO: CGDB Address:72LINK CHECK COMMAND IS SUCCESSFUL
9:24:36 AM   PLATFORM NO: CGDB Address:71LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:24:36 AM   PLATFORM NO: CGDB Address:71LINK CHECK COMMAND IS SUCCESSFUL
9:24:36 AM   PLATFORM NO: CGDB Address:70LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:24:36 AM   PLATFORM NO: CGDB Address:70LINK CHECK COMMAND IS SUCCESSFUL
9:24:36 AM   PLATFORM NO: CGDB Address:69LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:24:36 AM   PLATFORM NO: CGDB Address:69LINK CHECK COMMAND IS SUCCESSFUL
9:24:44 AM   PLATFORM NO: CGDB Address:68 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
9:24:52 AM   PLATFORM NO: CGDB Address:67 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
9:25:00 AM   PLATFORM NO: CGDB Address:66 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
9:25:08 AM   PLATFORM NO: CGDB Address:65 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
9:25:16 AM   PLATFORM NO: CGDB Address:64 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
9:27:24 AM    MLDB Address:56 LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST
9:27:24 AM    MLDB Address:50LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:27:24 AM    MLDB Address:50LINK CHECK COMMAND IS SUCCESSFUL
9:29:24 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:29:24 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND IS SUCCESSFUL
9:29:24 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:29:24 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND IS SUCCESSFUL
9:31:24 AM   PLATFORM NO: AGDB Address:33LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:31:24 AM   PLATFORM NO: AGDB Address:33LINK CHECK COMMAND IS SUCCESSFUL
9:31:25 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:31:25 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND IS SUCCESSFUL
9:31:25 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:31:25 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND IS SUCCESSFUL
9:33:25 AM   PLATFORM NO: CGDB Address:112LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:33:25 AM   PLATFORM NO: CGDB Address:112LINK CHECK COMMAND IS SUCCESSFUL
9:33:25 AM   PLATFORM NO: CGDB Address:111LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:33:25 AM   PLATFORM NO: CGDB Address:111LINK CHECK COMMAND IS SUCCESSFUL
9:33:25 AM   PLATFORM NO: CGDB Address:110LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:33:25 AM   PLATFORM NO: CGDB Address:110LINK CHECK COMMAND IS SUCCESSFUL
9:33:25 AM   PLATFORM NO: CGDB Address:109LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:33:25 AM   PLATFORM NO: CGDB Address:109LINK CHECK COMMAND IS SUCCESSFUL
9:33:25 AM   PLATFORM NO: CGDB Address:108LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:33:25 AM   PLATFORM NO: CGDB Address:108LINK CHECK COMMAND IS SUCCESSFUL
9:33:25 AM   PLATFORM NO: CGDB Address:107LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:33:25 AM   PLATFORM NO: CGDB Address:107LINK CHECK COMMAND IS SUCCESSFUL
9:33:26 AM   PLATFORM NO: CGDB Address:106LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:33:26 AM   PLATFORM NO: CGDB Address:106LINK CHECK COMMAND IS SUCCESSFUL
9:33:26 AM   PLATFORM NO: CGDB Address:105LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:33:26 AM   PLATFORM NO: CGDB Address:105LINK CHECK COMMAND IS SUCCESSFUL
9:33:26 AM   PLATFORM NO: CGDB Address:104LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:33:26 AM   PLATFORM NO: CGDB Address:104LINK CHECK COMMAND IS SUCCESSFUL
9:33:26 AM   PLATFORM NO: CGDB Address:103LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:33:26 AM   PLATFORM NO: CGDB Address:103LINK CHECK COMMAND IS SUCCESSFUL
9:33:26 AM   PLATFORM NO: CGDB Address:102LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:33:26 AM   PLATFORM NO: CGDB Address:102LINK CHECK COMMAND IS SUCCESSFUL
9:33:26 AM   PLATFORM NO: CGDB Address:101LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:33:26 AM   PLATFORM NO: CGDB Address:101LINK CHECK COMMAND IS SUCCESSFUL
9:33:26 AM   PLATFORM NO: CGDB Address:100LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:33:26 AM   PLATFORM NO: CGDB Address:100LINK CHECK COMMAND IS SUCCESSFUL
9:33:26 AM   PLATFORM NO: CGDB Address:99LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:33:26 AM   PLATFORM NO: CGDB Address:99LINK CHECK COMMAND IS SUCCESSFUL
9:33:27 AM   PLATFORM NO: CGDB Address:97LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:33:27 AM   PLATFORM NO: CGDB Address:97LINK CHECK COMMAND IS SUCCESSFUL
9:33:27 AM   PLATFORM NO: CGDB Address:96LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:33:27 AM   PLATFORM NO: CGDB Address:96LINK CHECK COMMAND IS SUCCESSFUL
9:33:27 AM   PLATFORM NO: CGDB Address:95LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:33:27 AM   PLATFORM NO: CGDB Address:95LINK CHECK COMMAND IS SUCCESSFUL
9:33:27 AM   PLATFORM NO: CGDB Address:94LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:33:27 AM   PLATFORM NO: CGDB Address:94LINK CHECK COMMAND IS SUCCESSFUL
9:33:27 AM   PLATFORM NO: CGDB Address:93LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:33:27 AM   PLATFORM NO: CGDB Address:93LINK CHECK COMMAND IS SUCCESSFUL
9:33:27 AM   PLATFORM NO: CGDB Address:92LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:33:27 AM   PLATFORM NO: CGDB Address:92LINK CHECK COMMAND IS SUCCESSFUL
9:33:27 AM   PLATFORM NO: CGDB Address:91LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:33:27 AM   PLATFORM NO: CGDB Address:91LINK CHECK COMMAND IS SUCCESSFUL
9:33:35 AM   PLATFORM NO: CGDB Address:90 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
9:33:43 AM   PLATFORM NO: CGDB Address:89 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
9:33:51 AM   PLATFORM NO: CGDB Address:88 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
9:33:51 AM   PLATFORM NO: CGDB Address:87LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:33:51 AM   PLATFORM NO: CGDB Address:87LINK CHECK COMMAND IS SUCCESSFUL
9:33:52 AM   PLATFORM NO: CGDB Address:86LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:33:52 AM   PLATFORM NO: CGDB Address:86LINK CHECK COMMAND IS SUCCESSFUL
9:33:52 AM   PLATFORM NO: CGDB Address:85LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:33:52 AM   PLATFORM NO: CGDB Address:85LINK CHECK COMMAND IS SUCCESSFUL
9:33:52 AM   PLATFORM NO: CGDB Address:84LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:33:52 AM   PLATFORM NO: CGDB Address:84LINK CHECK COMMAND IS SUCCESSFUL
9:33:52 AM   PLATFORM NO: CGDB Address:83LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:33:52 AM   PLATFORM NO: CGDB Address:83LINK CHECK COMMAND IS SUCCESSFUL
9:34:00 AM   PLATFORM NO: CGDB Address:82 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
9:34:00 AM   PLATFORM NO: CGDB Address:81LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:34:00 AM   PLATFORM NO: CGDB Address:81LINK CHECK COMMAND IS SUCCESSFUL
9:34:00 AM   PLATFORM NO: CGDB Address:80LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:34:00 AM   PLATFORM NO: CGDB Address:80LINK CHECK COMMAND IS SUCCESSFUL
9:34:00 AM   PLATFORM NO: CGDB Address:79LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:34:00 AM   PLATFORM NO: CGDB Address:79LINK CHECK COMMAND IS SUCCESSFUL
9:34:00 AM   PLATFORM NO: CGDB Address:78LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:34:00 AM   PLATFORM NO: CGDB Address:78LINK CHECK COMMAND IS SUCCESSFUL
9:34:01 AM   PLATFORM NO: CGDB Address:77LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:34:01 AM   PLATFORM NO: CGDB Address:77LINK CHECK COMMAND IS SUCCESSFUL
9:34:01 AM   PLATFORM NO: CGDB Address:76LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:34:01 AM   PLATFORM NO: CGDB Address:76LINK CHECK COMMAND IS SUCCESSFUL
9:34:01 AM   PLATFORM NO: CGDB Address:75LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:34:01 AM   PLATFORM NO: CGDB Address:75LINK CHECK COMMAND IS SUCCESSFUL
9:34:01 AM   PLATFORM NO: CGDB Address:74LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:34:01 AM   PLATFORM NO: CGDB Address:74LINK CHECK COMMAND IS SUCCESSFUL
9:34:01 AM   PLATFORM NO: CGDB Address:73LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:34:01 AM   PLATFORM NO: CGDB Address:73LINK CHECK COMMAND IS SUCCESSFUL
9:34:01 AM   PLATFORM NO: CGDB Address:72LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:34:01 AM   PLATFORM NO: CGDB Address:72LINK CHECK COMMAND IS SUCCESSFUL
9:34:01 AM   PLATFORM NO: CGDB Address:71LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:34:01 AM   PLATFORM NO: CGDB Address:71LINK CHECK COMMAND IS SUCCESSFUL
9:34:01 AM   PLATFORM NO: CGDB Address:70LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:34:01 AM   PLATFORM NO: CGDB Address:70LINK CHECK COMMAND IS SUCCESSFUL
9:34:01 AM   PLATFORM NO: CGDB Address:69LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:34:01 AM   PLATFORM NO: CGDB Address:69LINK CHECK COMMAND IS SUCCESSFUL
9:34:10 AM   PLATFORM NO: CGDB Address:68 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
9:34:18 AM   PLATFORM NO: CGDB Address:67 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
9:34:26 AM   PLATFORM NO: CGDB Address:66 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
9:34:34 AM   PLATFORM NO: CGDB Address:65 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
9:34:42 AM   PLATFORM NO: CGDB Address:64 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
9:35:41 AM   Network Configuration Saved
9:36:42 AM    MLDB Address:50LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:36:42 AM    MLDB Address:50LINK CHECK COMMAND IS SUCCESSFUL
9:36:51 AM    MLDB Address:50 DATA PACKET STATUS: Packet Received and proceed successfully 
9:36:51 AM    MLDB Address:50 DATA PACKET IS SUCCESSFUL
9:36:51 AM   PLATFORM NO:3 PDB Address:1 DATA PACKET STATUS: Packet Received and proceed successfully 
9:36:51 AM   PLATFORM NO:3 PDB Address:1 DATA PACKET IS SUCCESSFUL
9:36:52 AM   DELETE DATA PACKET SEND: AGDB: 36 PLATFORM NO:3 TRAIN NO: 57594 TRAINSTATUS:ON PLATFORM ARRIVAL TIME:08:40 DEPARTURE TIME:08:45
9:36:52 AM   PLATFORM NO:3 AGDB Address:36 DATA PACKET STATUS: Packet Received and proceed successfully 
9:36:52 AM   PLATFORM NO:3 AGDB Address:36 DATA PACKET IS SUCCESSFUL
9:36:52 AM    MLDB Address:50 DATA PACKET STATUS: Packet Received and proceed successfully 
9:36:52 AM    MLDB Address:50 DATA PACKET IS SUCCESSFUL
9:36:52 AM   DELETE DATA PACKET SEND: AGDB: 33 PLATFORM NO:1 TRAIN NO: 57502 TRAINSTATUS:READY TO LEAVE ARRIVAL TIME:09:00 DEPARTURE TIME:09:05
9:36:52 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET STATUS: Packet Received and proceed successfully 
9:36:52 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET IS SUCCESSFUL
9:38:42 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:38:42 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND IS SUCCESSFUL
9:38:42 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:38:42 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND IS SUCCESSFUL
9:40:42 AM   PLATFORM NO: AGDB Address:33LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:40:42 AM   PLATFORM NO: AGDB Address:33LINK CHECK COMMAND IS SUCCESSFUL
9:40:42 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:40:42 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND IS SUCCESSFUL
9:40:42 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:40:42 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND IS SUCCESSFUL
9:42:22 AM    MLDB Address:50 DATA PACKET STATUS: Packet Received and proceed successfully 
9:42:22 AM    MLDB Address:50 DATA PACKET IS SUCCESSFUL
9:42:22 AM    MLDB Address:50 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
9:42:22 AM    MLDB Address:50 SET CONFIGURATION COMMAND IS SUCCESSFUL
9:42:24 AM    MLDB Address:50 DATA PACKET STATUS: Packet Received and proceed successfully 
9:42:24 AM    MLDB Address:50 DATA PACKET IS SUCCESSFUL
9:42:26 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET STATUS: Packet Received and proceed successfully 
9:42:26 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET IS SUCCESSFUL
9:42:26 AM    MLDB Address:50 DATA PACKET STATUS: Packet Received and proceed successfully 
9:42:26 AM    MLDB Address:50 DATA PACKET IS SUCCESSFUL
9:42:27 AM    MLDB Address:50 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
9:42:27 AM    MLDB Address:50 SET CONFIGURATION COMMAND IS SUCCESSFUL
9:42:28 AM    MLDB Address:50 DATA PACKET STATUS: Packet Received and proceed successfully 
9:42:28 AM    MLDB Address:50 DATA PACKET IS SUCCESSFUL
9:42:31 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET STATUS: Packet Received and proceed successfully 
9:42:31 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET IS SUCCESSFUL
9:42:42 AM   PLATFORM NO: CGDB Address:112LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:42:42 AM   PLATFORM NO: CGDB Address:112LINK CHECK COMMAND IS SUCCESSFUL
9:42:42 AM   PLATFORM NO: CGDB Address:111LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:42:42 AM   PLATFORM NO: CGDB Address:111LINK CHECK COMMAND IS SUCCESSFUL
9:42:43 AM   PLATFORM NO: CGDB Address:110LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:42:43 AM   PLATFORM NO: CGDB Address:110LINK CHECK COMMAND IS SUCCESSFUL
9:42:43 AM   PLATFORM NO: CGDB Address:109LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:42:43 AM   PLATFORM NO: CGDB Address:109LINK CHECK COMMAND IS SUCCESSFUL
9:42:43 AM   PLATFORM NO: CGDB Address:108LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:42:43 AM   PLATFORM NO: CGDB Address:108LINK CHECK COMMAND IS SUCCESSFUL
9:42:43 AM   PLATFORM NO: CGDB Address:107LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:42:43 AM   PLATFORM NO: CGDB Address:107LINK CHECK COMMAND IS SUCCESSFUL
9:42:43 AM   PLATFORM NO: CGDB Address:106LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:42:43 AM   PLATFORM NO: CGDB Address:106LINK CHECK COMMAND IS SUCCESSFUL
9:42:43 AM   PLATFORM NO: CGDB Address:105LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:42:43 AM   PLATFORM NO: CGDB Address:105LINK CHECK COMMAND IS SUCCESSFUL
9:42:43 AM   PLATFORM NO: CGDB Address:104LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:42:43 AM   PLATFORM NO: CGDB Address:104LINK CHECK COMMAND IS SUCCESSFUL
9:42:43 AM   PLATFORM NO: CGDB Address:103LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:42:43 AM   PLATFORM NO: CGDB Address:103LINK CHECK COMMAND IS SUCCESSFUL
9:42:43 AM   PLATFORM NO: CGDB Address:102LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:42:43 AM   PLATFORM NO: CGDB Address:102LINK CHECK COMMAND IS SUCCESSFUL
9:42:44 AM   PLATFORM NO: CGDB Address:101LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:42:44 AM   PLATFORM NO: CGDB Address:101LINK CHECK COMMAND IS SUCCESSFUL
9:42:44 AM   PLATFORM NO: CGDB Address:100LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:42:44 AM   PLATFORM NO: CGDB Address:100LINK CHECK COMMAND IS SUCCESSFUL
9:42:44 AM   PLATFORM NO: CGDB Address:99LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:42:44 AM   PLATFORM NO: CGDB Address:99LINK CHECK COMMAND IS SUCCESSFUL
9:42:44 AM   PLATFORM NO: CGDB Address:97LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:42:44 AM   PLATFORM NO: CGDB Address:97LINK CHECK COMMAND IS SUCCESSFUL
9:42:44 AM   PLATFORM NO: CGDB Address:96LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:42:44 AM   PLATFORM NO: CGDB Address:96LINK CHECK COMMAND IS SUCCESSFUL
9:42:44 AM   PLATFORM NO: CGDB Address:95LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:42:44 AM   PLATFORM NO: CGDB Address:95LINK CHECK COMMAND IS SUCCESSFUL
9:42:44 AM   PLATFORM NO: CGDB Address:94LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:42:44 AM   PLATFORM NO: CGDB Address:94LINK CHECK COMMAND IS SUCCESSFUL
9:42:44 AM   PLATFORM NO: CGDB Address:93LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:42:44 AM   PLATFORM NO: CGDB Address:93LINK CHECK COMMAND IS SUCCESSFUL
9:42:45 AM   PLATFORM NO: CGDB Address:92LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:42:45 AM   PLATFORM NO: CGDB Address:92LINK CHECK COMMAND IS SUCCESSFUL
9:42:45 AM   PLATFORM NO: CGDB Address:91LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:42:45 AM   PLATFORM NO: CGDB Address:91LINK CHECK COMMAND IS SUCCESSFUL
9:42:53 AM   PLATFORM NO: CGDB Address:90 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
9:42:59 AM    MLDB Address:50 DATA PACKET STATUS: Packet Received and proceed successfully 
9:42:59 AM    MLDB Address:50 DATA PACKET IS SUCCESSFUL
9:42:59 AM    MLDB Address:50 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
9:42:59 AM    MLDB Address:50 SET CONFIGURATION COMMAND IS SUCCESSFUL
9:43:01 AM    MLDB Address:50 DATA PACKET STATUS: Packet Received and proceed successfully 
9:43:01 AM    MLDB Address:50 DATA PACKET IS SUCCESSFUL
9:43:03 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET STATUS: Packet Received and proceed successfully 
9:43:03 AM   PLATFORM NO:1 AGDB Address:33 DATA PACKET IS SUCCESSFUL
9:44:53 AM    MLDB Address:50LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:44:53 AM    MLDB Address:50LINK CHECK COMMAND IS SUCCESSFUL
9:46:53 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:46:53 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND IS SUCCESSFUL
9:46:53 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:46:53 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND IS SUCCESSFUL
9:48:53 AM   PLATFORM NO: AGDB Address:33LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:48:53 AM   PLATFORM NO: AGDB Address:33LINK CHECK COMMAND IS SUCCESSFUL
9:48:53 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:48:53 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND IS SUCCESSFUL
9:48:53 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:48:53 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND IS SUCCESSFUL
9:50:53 AM   PLATFORM NO: CGDB Address:112LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:50:53 AM   PLATFORM NO: CGDB Address:112LINK CHECK COMMAND IS SUCCESSFUL
9:50:54 AM   PLATFORM NO: CGDB Address:111LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:50:54 AM   PLATFORM NO: CGDB Address:111LINK CHECK COMMAND IS SUCCESSFUL
9:50:54 AM   PLATFORM NO: CGDB Address:110LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:50:54 AM   PLATFORM NO: CGDB Address:110LINK CHECK COMMAND IS SUCCESSFUL
9:50:54 AM   PLATFORM NO: CGDB Address:109LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:50:54 AM   PLATFORM NO: CGDB Address:109LINK CHECK COMMAND IS SUCCESSFUL
9:50:54 AM   PLATFORM NO: CGDB Address:108LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:50:54 AM   PLATFORM NO: CGDB Address:108LINK CHECK COMMAND IS SUCCESSFUL
9:50:54 AM   PLATFORM NO: CGDB Address:107LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:50:54 AM   PLATFORM NO: CGDB Address:107LINK CHECK COMMAND IS SUCCESSFUL
9:50:54 AM   PLATFORM NO: CGDB Address:106LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:50:54 AM   PLATFORM NO: CGDB Address:106LINK CHECK COMMAND IS SUCCESSFUL
9:50:54 AM   PLATFORM NO: CGDB Address:105LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:50:54 AM   PLATFORM NO: CGDB Address:105LINK CHECK COMMAND IS SUCCESSFUL
9:50:54 AM   PLATFORM NO: CGDB Address:104LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:50:54 AM   PLATFORM NO: CGDB Address:104LINK CHECK COMMAND IS SUCCESSFUL
9:50:55 AM   PLATFORM NO: CGDB Address:103LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:50:55 AM   PLATFORM NO: CGDB Address:103LINK CHECK COMMAND IS SUCCESSFUL
9:50:55 AM   PLATFORM NO: CGDB Address:102LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:50:55 AM   PLATFORM NO: CGDB Address:102LINK CHECK COMMAND IS SUCCESSFUL
9:50:55 AM   PLATFORM NO: CGDB Address:101LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:50:55 AM   PLATFORM NO: CGDB Address:101LINK CHECK COMMAND IS SUCCESSFUL
9:50:55 AM   PLATFORM NO: CGDB Address:100LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:50:55 AM   PLATFORM NO: CGDB Address:100LINK CHECK COMMAND IS SUCCESSFUL
9:50:55 AM   PLATFORM NO: CGDB Address:99LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:50:55 AM   PLATFORM NO: CGDB Address:99LINK CHECK COMMAND IS SUCCESSFUL
9:50:55 AM   PLATFORM NO: CGDB Address:97LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:50:55 AM   PLATFORM NO: CGDB Address:97LINK CHECK COMMAND IS SUCCESSFUL
9:50:55 AM   PLATFORM NO: CGDB Address:96LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:50:55 AM   PLATFORM NO: CGDB Address:96LINK CHECK COMMAND IS SUCCESSFUL
9:50:55 AM   PLATFORM NO: CGDB Address:95LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:50:55 AM   PLATFORM NO: CGDB Address:95LINK CHECK COMMAND IS SUCCESSFUL
9:50:56 AM   PLATFORM NO: CGDB Address:94LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:50:56 AM   PLATFORM NO: CGDB Address:94LINK CHECK COMMAND IS SUCCESSFUL
9:50:56 AM   PLATFORM NO: CGDB Address:93LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:50:56 AM   PLATFORM NO: CGDB Address:93LINK CHECK COMMAND IS SUCCESSFUL
9:50:56 AM   PLATFORM NO: CGDB Address:92LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:50:56 AM   PLATFORM NO: CGDB Address:92LINK CHECK COMMAND IS SUCCESSFUL
9:50:56 AM   PLATFORM NO: CGDB Address:91LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:50:56 AM   PLATFORM NO: CGDB Address:91LINK CHECK COMMAND IS SUCCESSFUL
9:51:04 AM   PLATFORM NO: CGDB Address:90 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
9:51:12 AM   PLATFORM NO: CGDB Address:89 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
9:51:20 AM   PLATFORM NO: CGDB Address:88 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
9:51:20 AM   PLATFORM NO: CGDB Address:87LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:51:20 AM   PLATFORM NO: CGDB Address:87LINK CHECK COMMAND IS SUCCESSFUL
9:51:20 AM   PLATFORM NO: CGDB Address:86LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:51:20 AM   PLATFORM NO: CGDB Address:86LINK CHECK COMMAND IS SUCCESSFUL
9:51:20 AM   PLATFORM NO: CGDB Address:85LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:51:20 AM   PLATFORM NO: CGDB Address:85LINK CHECK COMMAND IS SUCCESSFUL
9:51:20 AM   PLATFORM NO: CGDB Address:84LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:51:20 AM   PLATFORM NO: CGDB Address:84LINK CHECK COMMAND IS SUCCESSFUL
9:51:21 AM   PLATFORM NO: CGDB Address:83LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:51:21 AM   PLATFORM NO: CGDB Address:83LINK CHECK COMMAND IS SUCCESSFUL
9:51:29 AM   PLATFORM NO: CGDB Address:82 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
9:51:29 AM   PLATFORM NO: CGDB Address:81LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:51:29 AM   PLATFORM NO: CGDB Address:81LINK CHECK COMMAND IS SUCCESSFUL
9:51:29 AM   PLATFORM NO: CGDB Address:80LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:51:29 AM   PLATFORM NO: CGDB Address:80LINK CHECK COMMAND IS SUCCESSFUL
9:51:29 AM   PLATFORM NO: CGDB Address:79LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:51:29 AM   PLATFORM NO: CGDB Address:79LINK CHECK COMMAND IS SUCCESSFUL
9:51:29 AM   PLATFORM NO: CGDB Address:78LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:51:29 AM   PLATFORM NO: CGDB Address:78LINK CHECK COMMAND IS SUCCESSFUL
9:51:29 AM   PLATFORM NO: CGDB Address:77LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:51:29 AM   PLATFORM NO: CGDB Address:77LINK CHECK COMMAND IS SUCCESSFUL
9:51:29 AM   PLATFORM NO: CGDB Address:76LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:51:29 AM   PLATFORM NO: CGDB Address:76LINK CHECK COMMAND IS SUCCESSFUL
9:51:29 AM   PLATFORM NO: CGDB Address:75LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:51:29 AM   PLATFORM NO: CGDB Address:75LINK CHECK COMMAND IS SUCCESSFUL
9:51:30 AM   PLATFORM NO: CGDB Address:74LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:51:30 AM   PLATFORM NO: CGDB Address:74LINK CHECK COMMAND IS SUCCESSFUL
9:51:30 AM   PLATFORM NO: CGDB Address:73LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:51:30 AM   PLATFORM NO: CGDB Address:73LINK CHECK COMMAND IS SUCCESSFUL
9:51:30 AM   PLATFORM NO: CGDB Address:72LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:51:30 AM   PLATFORM NO: CGDB Address:72LINK CHECK COMMAND IS SUCCESSFUL
9:51:30 AM   PLATFORM NO: CGDB Address:71LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:51:30 AM   PLATFORM NO: CGDB Address:71LINK CHECK COMMAND IS SUCCESSFUL
9:51:30 AM   PLATFORM NO: CGDB Address:70LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:51:30 AM   PLATFORM NO: CGDB Address:70LINK CHECK COMMAND IS SUCCESSFUL
9:51:30 AM   PLATFORM NO: CGDB Address:69LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:51:30 AM   PLATFORM NO: CGDB Address:69LINK CHECK COMMAND IS SUCCESSFUL
9:51:38 AM   PLATFORM NO: CGDB Address:68 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
9:51:46 AM   PLATFORM NO: CGDB Address:67 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
9:51:54 AM   PLATFORM NO: CGDB Address:66 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
9:52:02 AM   PLATFORM NO: CGDB Address:65 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
9:52:10 AM   PLATFORM NO: CGDB Address:64 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
9:54:10 AM    MLDB Address:50LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:54:10 AM    MLDB Address:50LINK CHECK COMMAND IS SUCCESSFUL
9:56:10 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:56:10 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND IS SUCCESSFUL
9:56:10 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:56:10 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND IS SUCCESSFUL
9:58:11 AM   PLATFORM NO: AGDB Address:33LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:58:11 AM   PLATFORM NO: AGDB Address:33LINK CHECK COMMAND IS SUCCESSFUL
9:58:11 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:58:11 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND IS SUCCESSFUL
9:58:11 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
9:58:11 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND IS SUCCESSFUL
10:00:11 AM   PLATFORM NO: CGDB Address:112LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:00:11 AM   PLATFORM NO: CGDB Address:112LINK CHECK COMMAND IS SUCCESSFUL
10:00:11 AM   PLATFORM NO: CGDB Address:111LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:00:11 AM   PLATFORM NO: CGDB Address:111LINK CHECK COMMAND IS SUCCESSFUL
10:00:11 AM   PLATFORM NO: CGDB Address:110LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:00:11 AM   PLATFORM NO: CGDB Address:110LINK CHECK COMMAND IS SUCCESSFUL
10:00:11 AM   PLATFORM NO: CGDB Address:109LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:00:11 AM   PLATFORM NO: CGDB Address:109LINK CHECK COMMAND IS SUCCESSFUL
10:00:11 AM   PLATFORM NO: CGDB Address:108LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:00:11 AM   PLATFORM NO: CGDB Address:108LINK CHECK COMMAND IS SUCCESSFUL
10:00:12 AM   PLATFORM NO: CGDB Address:107LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:00:12 AM   PLATFORM NO: CGDB Address:107LINK CHECK COMMAND IS SUCCESSFUL
10:00:12 AM   PLATFORM NO: CGDB Address:106LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:00:12 AM   PLATFORM NO: CGDB Address:106LINK CHECK COMMAND IS SUCCESSFUL
10:00:12 AM   PLATFORM NO: CGDB Address:105LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:00:12 AM   PLATFORM NO: CGDB Address:105LINK CHECK COMMAND IS SUCCESSFUL
10:00:12 AM   PLATFORM NO: CGDB Address:104LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:00:12 AM   PLATFORM NO: CGDB Address:104LINK CHECK COMMAND IS SUCCESSFUL
10:00:12 AM   PLATFORM NO: CGDB Address:103LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:00:12 AM   PLATFORM NO: CGDB Address:103LINK CHECK COMMAND IS SUCCESSFUL
10:00:12 AM   PLATFORM NO: CGDB Address:102LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:00:12 AM   PLATFORM NO: CGDB Address:102LINK CHECK COMMAND IS SUCCESSFUL
10:00:12 AM   PLATFORM NO: CGDB Address:101LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:00:12 AM   PLATFORM NO: CGDB Address:101LINK CHECK COMMAND IS SUCCESSFUL
10:00:12 AM   PLATFORM NO: CGDB Address:100LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:00:12 AM   PLATFORM NO: CGDB Address:100LINK CHECK COMMAND IS SUCCESSFUL
10:00:12 AM   PLATFORM NO: CGDB Address:99LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:00:13 AM   PLATFORM NO: CGDB Address:99LINK CHECK COMMAND IS SUCCESSFUL
10:00:13 AM   PLATFORM NO: CGDB Address:97LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:00:13 AM   PLATFORM NO: CGDB Address:97LINK CHECK COMMAND IS SUCCESSFUL
10:00:13 AM   PLATFORM NO: CGDB Address:96LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:00:13 AM   PLATFORM NO: CGDB Address:96LINK CHECK COMMAND IS SUCCESSFUL
10:00:13 AM   PLATFORM NO: CGDB Address:95LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:00:13 AM   PLATFORM NO: CGDB Address:95LINK CHECK COMMAND IS SUCCESSFUL
10:00:13 AM   PLATFORM NO: CGDB Address:94LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:00:13 AM   PLATFORM NO: CGDB Address:94LINK CHECK COMMAND IS SUCCESSFUL
10:00:13 AM   PLATFORM NO: CGDB Address:93LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:00:13 AM   PLATFORM NO: CGDB Address:93LINK CHECK COMMAND IS SUCCESSFUL
10:00:13 AM   PLATFORM NO: CGDB Address:92LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:00:13 AM   PLATFORM NO: CGDB Address:92LINK CHECK COMMAND IS SUCCESSFUL
10:00:13 AM   PLATFORM NO: CGDB Address:91LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:00:13 AM   PLATFORM NO: CGDB Address:91LINK CHECK COMMAND IS SUCCESSFUL
10:00:21 AM   PLATFORM NO: CGDB Address:90 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
10:00:29 AM   PLATFORM NO: CGDB Address:89 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
10:00:37 AM   PLATFORM NO: CGDB Address:88 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
10:00:37 AM   PLATFORM NO: CGDB Address:87LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:00:37 AM   PLATFORM NO: CGDB Address:87LINK CHECK COMMAND IS SUCCESSFUL
10:00:38 AM   PLATFORM NO: CGDB Address:86LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:00:38 AM   PLATFORM NO: CGDB Address:86LINK CHECK COMMAND IS SUCCESSFUL
10:00:38 AM   PLATFORM NO: CGDB Address:85LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:00:38 AM   PLATFORM NO: CGDB Address:85LINK CHECK COMMAND IS SUCCESSFUL
10:00:38 AM   PLATFORM NO: CGDB Address:84LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:00:38 AM   PLATFORM NO: CGDB Address:84LINK CHECK COMMAND IS SUCCESSFUL
10:00:38 AM   PLATFORM NO: CGDB Address:83LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:00:38 AM   PLATFORM NO: CGDB Address:83LINK CHECK COMMAND IS SUCCESSFUL
10:00:46 AM   PLATFORM NO: CGDB Address:82 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
10:00:46 AM   PLATFORM NO: CGDB Address:81LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:00:46 AM   PLATFORM NO: CGDB Address:81LINK CHECK COMMAND IS SUCCESSFUL
10:00:46 AM   PLATFORM NO: CGDB Address:80LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:00:46 AM   PLATFORM NO: CGDB Address:80LINK CHECK COMMAND IS SUCCESSFUL
10:00:46 AM   PLATFORM NO: CGDB Address:79LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:00:46 AM   PLATFORM NO: CGDB Address:79LINK CHECK COMMAND IS SUCCESSFUL
10:00:46 AM   PLATFORM NO: CGDB Address:78LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:00:46 AM   PLATFORM NO: CGDB Address:78LINK CHECK COMMAND IS SUCCESSFUL
10:00:47 AM   PLATFORM NO: CGDB Address:77LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:00:47 AM   PLATFORM NO: CGDB Address:77LINK CHECK COMMAND IS SUCCESSFUL
10:00:47 AM   PLATFORM NO: CGDB Address:76LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:00:47 AM   PLATFORM NO: CGDB Address:76LINK CHECK COMMAND IS SUCCESSFUL
10:00:47 AM   PLATFORM NO: CGDB Address:75LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:00:47 AM   PLATFORM NO: CGDB Address:75LINK CHECK COMMAND IS SUCCESSFUL
10:00:47 AM   PLATFORM NO: CGDB Address:74LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:00:47 AM   PLATFORM NO: CGDB Address:74LINK CHECK COMMAND IS SUCCESSFUL
10:00:47 AM   PLATFORM NO: CGDB Address:73LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:00:47 AM   PLATFORM NO: CGDB Address:73LINK CHECK COMMAND IS SUCCESSFUL
10:00:47 AM   PLATFORM NO: CGDB Address:72LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:00:47 AM   PLATFORM NO: CGDB Address:72LINK CHECK COMMAND IS SUCCESSFUL
10:00:47 AM   PLATFORM NO: CGDB Address:71LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:00:47 AM   PLATFORM NO: CGDB Address:71LINK CHECK COMMAND IS SUCCESSFUL
10:00:47 AM   PLATFORM NO: CGDB Address:70LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:00:47 AM   PLATFORM NO: CGDB Address:70LINK CHECK COMMAND IS SUCCESSFUL
10:00:48 AM   PLATFORM NO: CGDB Address:69LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:00:48 AM   PLATFORM NO: CGDB Address:69LINK CHECK COMMAND IS SUCCESSFUL
10:00:56 AM   PLATFORM NO: CGDB Address:68 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
10:01:04 AM   PLATFORM NO: CGDB Address:67 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
10:01:12 AM   PLATFORM NO: CGDB Address:66 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
10:01:20 AM   PLATFORM NO: CGDB Address:65 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
10:01:28 AM   PLATFORM NO: CGDB Address:64 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
10:03:28 AM    MLDB Address:50LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:03:28 AM    MLDB Address:50LINK CHECK COMMAND IS SUCCESSFUL
10:05:28 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:05:28 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND IS SUCCESSFUL
10:05:28 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:05:28 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND IS SUCCESSFUL
10:07:28 AM   PLATFORM NO: AGDB Address:33LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:07:28 AM   PLATFORM NO: AGDB Address:33LINK CHECK COMMAND IS SUCCESSFUL
10:07:28 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:07:28 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND IS SUCCESSFUL
10:07:28 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:07:28 AM   PLATFORM NO: AGDB Address:36LINK CHECK COMMAND IS SUCCESSFUL
10:09:28 AM   PLATFORM NO: CGDB Address:112LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:09:28 AM   PLATFORM NO: CGDB Address:112LINK CHECK COMMAND IS SUCCESSFUL
10:09:28 AM   PLATFORM NO: CGDB Address:111LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:09:28 AM   PLATFORM NO: CGDB Address:111LINK CHECK COMMAND IS SUCCESSFUL
10:09:29 AM   PLATFORM NO: CGDB Address:110LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:09:29 AM   PLATFORM NO: CGDB Address:110LINK CHECK COMMAND IS SUCCESSFUL
10:09:29 AM   PLATFORM NO: CGDB Address:109LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:09:29 AM   PLATFORM NO: CGDB Address:109LINK CHECK COMMAND IS SUCCESSFUL
10:09:29 AM   PLATFORM NO: CGDB Address:108LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:09:29 AM   PLATFORM NO: CGDB Address:108LINK CHECK COMMAND IS SUCCESSFUL
10:09:29 AM   PLATFORM NO: CGDB Address:107LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:09:29 AM   PLATFORM NO: CGDB Address:107LINK CHECK COMMAND IS SUCCESSFUL
10:09:29 AM   PLATFORM NO: CGDB Address:106LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:09:29 AM   PLATFORM NO: CGDB Address:106LINK CHECK COMMAND IS SUCCESSFUL
10:09:29 AM   PLATFORM NO: CGDB Address:105LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:09:29 AM   PLATFORM NO: CGDB Address:105LINK CHECK COMMAND IS SUCCESSFUL
10:09:29 AM   PLATFORM NO: CGDB Address:104LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:09:29 AM   PLATFORM NO: CGDB Address:104LINK CHECK COMMAND IS SUCCESSFUL
10:09:29 AM   PLATFORM NO: CGDB Address:103LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:09:29 AM   PLATFORM NO: CGDB Address:103LINK CHECK COMMAND IS SUCCESSFUL
10:09:30 AM   PLATFORM NO: CGDB Address:102LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:09:30 AM   PLATFORM NO: CGDB Address:102LINK CHECK COMMAND IS SUCCESSFUL
10:09:30 AM   PLATFORM NO: CGDB Address:101LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:09:30 AM   PLATFORM NO: CGDB Address:101LINK CHECK COMMAND IS SUCCESSFUL
10:09:30 AM   PLATFORM NO: CGDB Address:100LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:09:30 AM   PLATFORM NO: CGDB Address:100LINK CHECK COMMAND IS SUCCESSFUL
10:09:30 AM   PLATFORM NO: CGDB Address:99LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:09:30 AM   PLATFORM NO: CGDB Address:99LINK CHECK COMMAND IS SUCCESSFUL
10:09:30 AM   PLATFORM NO: CGDB Address:97LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:09:30 AM   PLATFORM NO: CGDB Address:97LINK CHECK COMMAND IS SUCCESSFUL
10:09:30 AM   PLATFORM NO: CGDB Address:96LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:09:30 AM   PLATFORM NO: CGDB Address:96LINK CHECK COMMAND IS SUCCESSFUL
10:09:30 AM   PLATFORM NO: CGDB Address:95LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:09:30 AM   PLATFORM NO: CGDB Address:95LINK CHECK COMMAND IS SUCCESSFUL
10:09:30 AM   PLATFORM NO: CGDB Address:94LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:09:30 AM   PLATFORM NO: CGDB Address:94LINK CHECK COMMAND IS SUCCESSFUL
10:09:31 AM   PLATFORM NO: CGDB Address:93LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:09:31 AM   PLATFORM NO: CGDB Address:93LINK CHECK COMMAND IS SUCCESSFUL
10:09:31 AM   PLATFORM NO: CGDB Address:92LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:09:31 AM   PLATFORM NO: CGDB Address:92LINK CHECK COMMAND IS SUCCESSFUL
10:09:31 AM   PLATFORM NO: CGDB Address:91LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:09:31 AM   PLATFORM NO: CGDB Address:91LINK CHECK COMMAND IS SUCCESSFUL
10:09:39 AM   PLATFORM NO: CGDB Address:90 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
10:09:47 AM   PLATFORM NO: CGDB Address:89 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
10:09:55 AM   PLATFORM NO: CGDB Address:88 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
10:09:55 AM   PLATFORM NO: CGDB Address:87LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:09:55 AM   PLATFORM NO: CGDB Address:87LINK CHECK COMMAND IS SUCCESSFUL
10:09:55 AM   PLATFORM NO: CGDB Address:86LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:09:55 AM   PLATFORM NO: CGDB Address:86LINK CHECK COMMAND IS SUCCESSFUL
10:09:55 AM   PLATFORM NO: CGDB Address:85LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:09:55 AM   PLATFORM NO: CGDB Address:85LINK CHECK COMMAND IS SUCCESSFUL
10:09:55 AM   PLATFORM NO: CGDB Address:84LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:09:55 AM   PLATFORM NO: CGDB Address:84LINK CHECK COMMAND IS SUCCESSFUL
10:09:55 AM   PLATFORM NO: CGDB Address:83LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:09:55 AM   PLATFORM NO: CGDB Address:83LINK CHECK COMMAND IS SUCCESSFUL
10:10:03 AM   PLATFORM NO: CGDB Address:82 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
10:10:04 AM   PLATFORM NO: CGDB Address:81LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:10:04 AM   PLATFORM NO: CGDB Address:81LINK CHECK COMMAND IS SUCCESSFUL
10:10:04 AM   PLATFORM NO: CGDB Address:80LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:10:04 AM   PLATFORM NO: CGDB Address:80LINK CHECK COMMAND IS SUCCESSFUL
10:10:04 AM   PLATFORM NO: CGDB Address:79LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:10:04 AM   PLATFORM NO: CGDB Address:79LINK CHECK COMMAND IS SUCCESSFUL
10:10:04 AM   PLATFORM NO: CGDB Address:78LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:10:04 AM   PLATFORM NO: CGDB Address:78LINK CHECK COMMAND IS SUCCESSFUL
10:10:04 AM   PLATFORM NO: CGDB Address:77LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:10:04 AM   PLATFORM NO: CGDB Address:77LINK CHECK COMMAND IS SUCCESSFUL
10:10:04 AM   PLATFORM NO: CGDB Address:76LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:10:04 AM   PLATFORM NO: CGDB Address:76LINK CHECK COMMAND IS SUCCESSFUL
10:10:04 AM   PLATFORM NO: CGDB Address:75LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:10:04 AM   PLATFORM NO: CGDB Address:75LINK CHECK COMMAND IS SUCCESSFUL
10:10:04 AM   PLATFORM NO: CGDB Address:74LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:10:04 AM   PLATFORM NO: CGDB Address:74LINK CHECK COMMAND IS SUCCESSFUL
10:10:05 AM   PLATFORM NO: CGDB Address:73LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:10:05 AM   PLATFORM NO: CGDB Address:73LINK CHECK COMMAND IS SUCCESSFUL
10:10:05 AM   PLATFORM NO: CGDB Address:72LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:10:05 AM   PLATFORM NO: CGDB Address:72LINK CHECK COMMAND IS SUCCESSFUL
10:10:05 AM   PLATFORM NO: CGDB Address:71LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:10:05 AM   PLATFORM NO: CGDB Address:71LINK CHECK COMMAND IS SUCCESSFUL
10:10:05 AM   PLATFORM NO: CGDB Address:70LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:10:05 AM   PLATFORM NO: CGDB Address:70LINK CHECK COMMAND IS SUCCESSFUL
10:10:05 AM   PLATFORM NO: CGDB Address:69LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:10:05 AM   PLATFORM NO: CGDB Address:69LINK CHECK COMMAND IS SUCCESSFUL
10:10:13 AM   PLATFORM NO: CGDB Address:68 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
10:10:21 AM   PLATFORM NO: CGDB Address:67 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
10:10:29 AM   PLATFORM NO: CGDB Address:66 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
10:10:37 AM   PLATFORM NO: CGDB Address:65 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
10:10:45 AM   PLATFORM NO: CGDB Address:64 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
10:12:45 AM    MLDB Address:50LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:12:45 AM    MLDB Address:50LINK CHECK COMMAND IS SUCCESSFUL
10:13:04 AM    MLDB Address:50 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
10:13:04 AM    MLDB Address:50 SET CONFIGURATION COMMAND IS SUCCESSFUL
10:13:04 AM   PLATFORM NO: PDB Address:162 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
10:13:04 AM   PLATFORM NO: PDB Address:162 SET CONFIGURATION COMMAND IS SUCCESSFUL
10:13:04 AM   PLATFORM NO: PDB Address:162 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
10:13:04 AM   PLATFORM NO: PDB Address:162 SET CONFIGURATION COMMAND IS SUCCESSFUL
10:13:04 AM   PLATFORM NO: AGDB Address:33 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
10:13:04 AM   PLATFORM NO: AGDB Address:33 SET CONFIGURATION COMMAND IS SUCCESSFUL
10:13:04 AM   PLATFORM NO: AGDB Address:36 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
10:13:04 AM   PLATFORM NO: AGDB Address:36 SET CONFIGURATION COMMAND IS SUCCESSFUL
10:13:04 AM   PLATFORM NO: AGDB Address:36 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
10:13:04 AM   PLATFORM NO: AGDB Address:36 SET CONFIGURATION COMMAND IS SUCCESSFUL
10:13:04 AM   PLATFORM NO: CGDB Address:112 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
10:13:04 AM   PLATFORM NO: CGDB Address:112 SET CONFIGURATION COMMAND IS SUCCESSFUL
10:13:04 AM   PLATFORM NO: CGDB Address:111 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
10:13:04 AM   PLATFORM NO: CGDB Address:111 SET CONFIGURATION COMMAND IS SUCCESSFUL
10:13:05 AM   PLATFORM NO: CGDB Address:110 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
10:13:05 AM   PLATFORM NO: CGDB Address:110 SET CONFIGURATION COMMAND IS SUCCESSFUL
10:13:05 AM   PLATFORM NO: CGDB Address:109 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
10:13:05 AM   PLATFORM NO: CGDB Address:109 SET CONFIGURATION COMMAND IS SUCCESSFUL
10:13:05 AM   PLATFORM NO: CGDB Address:108 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
10:13:05 AM   PLATFORM NO: CGDB Address:108 SET CONFIGURATION COMMAND IS SUCCESSFUL
10:13:05 AM   PLATFORM NO: CGDB Address:107 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
10:13:05 AM   PLATFORM NO: CGDB Address:107 SET CONFIGURATION COMMAND IS SUCCESSFUL
10:13:05 AM   PLATFORM NO: CGDB Address:106 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
10:13:05 AM   PLATFORM NO: CGDB Address:106 SET CONFIGURATION COMMAND IS SUCCESSFUL
10:13:05 AM   PLATFORM NO: CGDB Address:105 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
10:13:05 AM   PLATFORM NO: CGDB Address:105 SET CONFIGURATION COMMAND IS SUCCESSFUL
10:13:05 AM   PLATFORM NO: CGDB Address:104 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
10:13:05 AM   PLATFORM NO: CGDB Address:104 SET CONFIGURATION COMMAND IS SUCCESSFUL
10:13:05 AM   PLATFORM NO: CGDB Address:103 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
10:13:05 AM   PLATFORM NO: CGDB Address:103 SET CONFIGURATION COMMAND IS SUCCESSFUL
10:13:06 AM   PLATFORM NO: CGDB Address:102 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
10:13:06 AM   PLATFORM NO: CGDB Address:102 SET CONFIGURATION COMMAND IS SUCCESSFUL
10:13:06 AM   PLATFORM NO: CGDB Address:101 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
10:13:06 AM   PLATFORM NO: CGDB Address:101 SET CONFIGURATION COMMAND IS SUCCESSFUL
10:13:06 AM   PLATFORM NO: CGDB Address:100 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
10:13:06 AM   PLATFORM NO: CGDB Address:100 SET CONFIGURATION COMMAND IS SUCCESSFUL
10:13:06 AM   PLATFORM NO: CGDB Address:99 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
10:13:06 AM   PLATFORM NO: CGDB Address:99 SET CONFIGURATION COMMAND IS SUCCESSFUL
10:13:06 AM   PLATFORM NO: CGDB Address:97 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
10:13:06 AM   PLATFORM NO: CGDB Address:97 SET CONFIGURATION COMMAND IS SUCCESSFUL
10:13:06 AM   PLATFORM NO: CGDB Address:96 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
10:13:06 AM   PLATFORM NO: CGDB Address:96 SET CONFIGURATION COMMAND IS SUCCESSFUL
10:13:06 AM   PLATFORM NO: CGDB Address:95 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
10:13:06 AM   PLATFORM NO: CGDB Address:95 SET CONFIGURATION COMMAND IS SUCCESSFUL
10:13:06 AM   PLATFORM NO: CGDB Address:94 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
10:13:06 AM   PLATFORM NO: CGDB Address:94 SET CONFIGURATION COMMAND IS SUCCESSFUL
10:13:07 AM   PLATFORM NO: CGDB Address:93 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
10:13:07 AM   PLATFORM NO: CGDB Address:93 SET CONFIGURATION COMMAND IS SUCCESSFUL
10:13:07 AM   PLATFORM NO: CGDB Address:92 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
10:13:07 AM   PLATFORM NO: CGDB Address:92 SET CONFIGURATION COMMAND IS SUCCESSFUL
10:13:07 AM   PLATFORM NO: CGDB Address:91 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
10:13:07 AM   PLATFORM NO: CGDB Address:91 SET CONFIGURATION COMMAND IS SUCCESSFUL
10:13:15 AM   PLATFORM NO: CGDB Address:90 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
10:13:23 AM   PLATFORM NO: CGDB Address:89 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
10:13:31 AM   PLATFORM NO: CGDB Address:88 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
10:13:31 AM   PLATFORM NO: CGDB Address:87 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
10:13:31 AM   PLATFORM NO: CGDB Address:87 SET CONFIGURATION COMMAND IS SUCCESSFUL
10:13:31 AM   PLATFORM NO: CGDB Address:86 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
10:13:31 AM   PLATFORM NO: CGDB Address:86 SET CONFIGURATION COMMAND IS SUCCESSFUL
10:13:31 AM   PLATFORM NO: CGDB Address:85 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
10:13:31 AM   PLATFORM NO: CGDB Address:85 SET CONFIGURATION COMMAND IS SUCCESSFUL
10:13:31 AM   PLATFORM NO: CGDB Address:84 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
10:13:31 AM   PLATFORM NO: CGDB Address:84 SET CONFIGURATION COMMAND IS SUCCESSFUL
10:13:32 AM   PLATFORM NO: CGDB Address:83 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
10:13:32 AM   PLATFORM NO: CGDB Address:83 SET CONFIGURATION COMMAND IS SUCCESSFUL
10:13:32 AM   PLATFORM NO: CGDB Address:82 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
10:13:32 AM   PLATFORM NO: CGDB Address:82 SET CONFIGURATION COMMAND IS SUCCESSFUL
10:13:32 AM   PLATFORM NO: CGDB Address:81 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
10:13:32 AM   PLATFORM NO: CGDB Address:81 SET CONFIGURATION COMMAND IS SUCCESSFUL
10:13:32 AM   PLATFORM NO: CGDB Address:80 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
10:13:32 AM   PLATFORM NO: CGDB Address:80 SET CONFIGURATION COMMAND IS SUCCESSFUL
10:13:32 AM   PLATFORM NO: CGDB Address:79 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
10:13:32 AM   PLATFORM NO: CGDB Address:79 SET CONFIGURATION COMMAND IS SUCCESSFUL
10:13:32 AM   PLATFORM NO: CGDB Address:78 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
10:13:32 AM   PLATFORM NO: CGDB Address:78 SET CONFIGURATION COMMAND IS SUCCESSFUL
10:13:32 AM   PLATFORM NO: CGDB Address:77 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
10:13:32 AM   PLATFORM NO: CGDB Address:77 SET CONFIGURATION COMMAND IS SUCCESSFUL
10:13:32 AM   PLATFORM NO: CGDB Address:76 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
10:13:32 AM   PLATFORM NO: CGDB Address:76 SET CONFIGURATION COMMAND IS SUCCESSFUL
10:13:33 AM   PLATFORM NO: CGDB Address:75 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
10:13:33 AM   PLATFORM NO: CGDB Address:75 SET CONFIGURATION COMMAND IS SUCCESSFUL
10:13:33 AM   PLATFORM NO: CGDB Address:74 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
10:13:33 AM   PLATFORM NO: CGDB Address:74 SET CONFIGURATION COMMAND IS SUCCESSFUL
10:13:33 AM   PLATFORM NO: CGDB Address:73 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
10:13:33 AM   PLATFORM NO: CGDB Address:73 SET CONFIGURATION COMMAND IS SUCCESSFUL
10:13:33 AM   PLATFORM NO: CGDB Address:72 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
10:13:33 AM   PLATFORM NO: CGDB Address:72 SET CONFIGURATION COMMAND IS SUCCESSFUL
10:13:33 AM   PLATFORM NO: CGDB Address:71 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
10:13:33 AM   PLATFORM NO: CGDB Address:71 SET CONFIGURATION COMMAND IS SUCCESSFUL
10:13:33 AM   PLATFORM NO: CGDB Address:70 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
10:13:33 AM   PLATFORM NO: CGDB Address:70 SET CONFIGURATION COMMAND IS SUCCESSFUL
10:13:33 AM   PLATFORM NO: CGDB Address:69 SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully
10:13:33 AM   PLATFORM NO: CGDB Address:69 SET CONFIGURATION COMMAND IS SUCCESSFUL
10:13:41 AM   PLATFORM NO: CGDB Address:68 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
10:13:49 AM   PLATFORM NO: CGDB Address:67 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
10:13:57 AM   PLATFORM NO: CGDB Address:66 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
10:14:05 AM   PLATFORM NO: CGDB Address:65 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
10:14:13 AM   PLATFORM NO: CGDB Address:64 LINK FAILURE or ADDRESSED CGDB DOESn't EXIST
10:14:45 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:14:45 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND IS SUCCESSFUL
10:14:45 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully 
10:14:45 AM   PLATFORM NO: PDB Address:162LINK CHECK COMMAND IS SUCCESSFUL
