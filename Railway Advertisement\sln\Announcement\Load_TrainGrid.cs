﻿using System;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Windows.Forms;

namespace Announcement
{
	// Token: 0x02000007 RID: 7
	public partial class Load_TrainGrid : Form
	{
		// Token: 0x06000030 RID: 48 RVA: 0x00008438 File Offset: 0x00006638
		public Load_TrainGrid()
		{
			this.InitializeComponent();
		}

		// Token: 0x06000031 RID: 49 RVA: 0x0000845C File Offset: 0x0000665C
		private void Btn_Load_Click(object sender, EventArgs e)
		{
			DataTable dataTable = new DataTable();
			DataTable dataTable2 = new DataTable();
			dataTable = new DataTable();
			string text = this.Cmb_FrmTime.Value.ToString("HH:mm");
			string text2 = this.Cmb_ToTime.Value.ToString("HH:mm");
			string str = DateTime.Today.DayOfWeek.ToString().Substring(0, 3);
			string text3 = "Chk_" + str;
			DateTime t = Convert.ToDateTime(text);
			DateTime t2 = Convert.ToDateTime(text2);
			bool flag = t > t2;
			if (flag)
			{
				DataTable dataTable3 = new DataTable();
				dataTable2 = this.DB.Read_Database(string.Concat(new string[]
				{
					"Select Train_No,Train_NameEng,Train_AD,Sch_AT,Sch_DT,Sch_PF From Train_Data WHERE (All_Days = 'True' OR ",
					text3,
					" = 'True') AND (Sch_DT BETWEEN '",
					text,
					"' AND '23:59') ORDER BY Sch_DT"
				}));
				dataTable2.PrimaryKey = new DataColumn[]
				{
					dataTable2.Columns["Train_No"]
				};
				dataTable2.Merge(this.DB.Read_Database(string.Concat(new string[]
				{
					"Select Train_No,Train_NameEng,Train_AD,Sch_AT,Sch_DT,Sch_PF From Train_Data WHERE (All_Days = 'True' OR ",
					text3,
					" = 'True') AND (Sch_AT BETWEEN '",
					text,
					"' AND '23:59') ORDER BY Sch_AT"
				})));
				str = DateTime.Today.AddDays(1.0).DayOfWeek.ToString().Substring(0, 3);
				text3 = "Chk_" + str;
				dataTable3 = this.DB.Read_Database(string.Concat(new string[]
				{
					"Select Train_No,Train_NameEng,Train_AD,Sch_AT,Sch_DT,Sch_PF From Train_Data WHERE (All_Days = 'True' OR ",
					text3,
					" = 'True') AND (Sch_DT BETWEEN '00:01' AND '",
					text2,
					"') ORDER BY Sch_DT"
				}));
				dataTable3.PrimaryKey = new DataColumn[]
				{
					dataTable3.Columns["Train_No"]
				};
				dataTable3.Merge(this.DB.Read_Database(string.Concat(new string[]
				{
					"Select Train_No,Train_NameEng,Train_AD,Sch_AT,Sch_DT,Sch_PF From Train_Data WHERE (All_Days = 'True' OR ",
					text3,
					" = 'True') AND (Sch_AT BETWEEN '00:01' AND '",
					text2,
					"') ORDER BY Sch_AT"
				})));
				dataTable2.Merge(dataTable3);
			}
			else
			{
				dataTable2 = this.DB.Read_Database(string.Concat(new string[]
				{
					"Select Train_No,Train_NameEng,Train_AD,Sch_AT,Sch_DT,Sch_PF From Train_Data WHERE (All_Days = 'True' OR ",
					text3,
					" = 'True') AND (Sch_DT BETWEEN '",
					text,
					"' AND '",
					text2,
					"') ORDER BY Sch_DT"
				}));
				dataTable2.PrimaryKey = new DataColumn[]
				{
					dataTable2.Columns["Train_No"]
				};
				dataTable2.Merge(this.DB.Read_Database(string.Concat(new string[]
				{
					"Select Train_No,Train_NameEng,Train_AD,Sch_AT,Sch_DT,Sch_PF From Train_Data WHERE (All_Days = 'True' OR ",
					text3,
					" = 'True') AND (Sch_AT BETWEEN '",
					text,
					"' AND '",
					text2,
					"') ORDER BY Sch_AT"
				})));
			}
			dataTable2.Columns.Add("Train_Status");
			dataTable2.Columns.Add("Late");
			dataTable2.Columns.Add("Exp_AT");
			dataTable2.Columns.Add("Exp_DT");
			dataTable2.Columns.Add("AN");
			dataTable2.Columns.Add("Div_City");
			dataTable2.Columns.Add("Del_Train");
			for (int i = 0; i < dataTable2.Rows.Count; i++)
			{
				dataTable2.Rows[i]["Train_Status"] = "RUNNING RIGHT TIME";
				dataTable2.Rows[i]["Exp_AT"] = dataTable2.Rows[i]["Sch_AT"];
				dataTable2.Rows[i]["Exp_DT"] = dataTable2.Rows[i]["Sch_DT"];
				bool flag2 = dataTable2.Rows[i]["Train_AD"].ToString() == "Both";
				if (flag2)
				{
					dataTable2.Rows[i]["Train_AD"] = "A";
				}
			}
			foreach (object obj in dataTable2.Rows)
			{
				DataRow dataRow = (DataRow)obj;
				string str2 = dataRow["Train_No"].ToString();
				DataRow[] array = Main.Online_TrainsGV.Select("Train_No = '" + str2 + "'");
				bool flag3 = array.Length == 0;
				if (flag3)
				{
					bool flag4 = array.Length == 0;
					if (flag4)
					{
						Main.Online_TrainsGV.ImportRow(dataRow);
					}
				}
			}
			Main.Flag_Update_DGVOT = true;
			base.Close();
		}

		// Token: 0x06000032 RID: 50 RVA: 0x0000895C File Offset: 0x00006B5C
		private void Load_TrainGrid_Load(object sender, EventArgs e)
		{
			this.Cmb_FrmTime.Value = DateTime.Now;
			this.Cmb_ToTime.Value = DateTime.Now.AddHours(2.0);
		}

		// Token: 0x0400003F RID: 63
		private Class_Database DB = new Class_Database();
	}
}
