using System;
using System.Windows.Forms;
using IPIS.Forms.User;
using IPIS.Forms.Announcement;
using IPIS.Repositories;
using IPIS.Services;
using IPIS.Utils;
using static IPIS.Utils.BatchLoggerExtensions;
using IPIS.Models;

namespace IPIS
{
    internal static class Program
    {
        /// <summary>
        ///  The main entry point for the application.
        /// </summary>
        [STAThread]
        static void Main()
        {
            using var bootLogger = CreateSystemBootLogger();

            try
            {
                bootLogger.LogStep("Initializing application configuration");
                ApplicationConfiguration.Initialize();

                bootLogger.LogStep("Initializing database and creating tables");
                Database.EnsureDatabaseInitialized();

                bootLogger.LogStep("Initializing language manager");
                var languageService = new LanguageService(new SQLiteLanguageRepository());
                LanguageManager.Initialize(languageService);

                bootLogger.LogStep("System startup completed successfully");

                // Log individual system startup for compatibility
                Logger.LogSystemStartup();

                // Show login form
                using (var loginForm = new LoginForm())
                {
                    if (loginForm.ShowDialog() == DialogResult.OK)
                    {
                        // If login is successful, show main form
                        Application.Run(new MainForm());
                    }
                }

                // Log system shutdown
                Logger.LogSystemShutdown();
            }
            catch (Exception ex)
            {
                // Log critical error
                Logger.LogCritical(LogCategory.System, "Application startup failed",
                                 ex.Message, "Program.Main", ex);

                MessageBox.Show($"A critical error occurred during application startup: {ex.Message}",
                              "Critical Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}