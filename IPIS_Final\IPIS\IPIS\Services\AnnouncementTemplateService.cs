using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using IPIS.Models;
using IPIS.Repositories.Interfaces;

namespace IPIS.Services
{
    public class AnnouncementTemplateService
    {
        private readonly IAnnouncementTemplateRepository _templateRepository;

        public AnnouncementTemplateService(IAnnouncementTemplateRepository templateRepository)
        {
            _templateRepository = templateRepository;
        }

        public async Task<IEnumerable<AnnouncementTemplate>> GetAllTemplatesAsync()
        {
            return await _templateRepository.GetAllTemplatesAsync();
        }

        public async Task<IEnumerable<AnnouncementTemplate>> GetActiveTemplatesAsync()
        {
            return await _templateRepository.GetActiveTemplatesAsync();
        }

        public async Task<IEnumerable<AnnouncementTemplate>> GetActiveTemplatesByArrivalDepartureAsync(string arrivalDeparture)
        {
            if (string.IsNullOrWhiteSpace(arrivalDeparture))
                throw new ArgumentException("Arrival/Departure value is required.");

            if (arrivalDeparture != "A" && arrivalDeparture != "D")
                throw new ArgumentException("Arrival/Departure value must be 'A' or 'D'.");

            return await _templateRepository.GetActiveTemplatesByArrivalDepartureAsync(arrivalDeparture);
        }

        public async Task<AnnouncementTemplate> GetTemplateByIdAsync(int id)
        {
            return await _templateRepository.GetTemplateByIdAsync(id);
        }

        public async Task<AnnouncementTemplate> GetTemplateByNameAsync(string name)
        {
            return await _templateRepository.GetTemplateByNameAsync(name);
        }

        public async Task<int> AddTemplateAsync(AnnouncementTemplate template)
        {
            // Validate template
            if (string.IsNullOrWhiteSpace(template.Name))
                throw new ArgumentException("Template name is required.");

            // Check if template with same name already exists
            var existingTemplate = await _templateRepository.GetTemplateByNameAsync(template.Name);
            if (existingTemplate != null)
                throw new InvalidOperationException($"Template with name '{template.Name}' already exists.");

            return await _templateRepository.AddTemplateAsync(template);
        }

        public async Task<bool> UpdateTemplateAsync(AnnouncementTemplate template)
        {
            // Validate template
            if (string.IsNullOrWhiteSpace(template.Name))
                throw new ArgumentException("Template name is required.");

            // Check if template with same name already exists (excluding current template)
            var existingTemplate = await _templateRepository.GetTemplateByNameAsync(template.Name);
            if (existingTemplate != null && existingTemplate.Id != template.Id)
                throw new InvalidOperationException($"Template with name '{template.Name}' already exists.");

            return await _templateRepository.UpdateTemplateAsync(template);
        }

        public async Task<bool> DeleteTemplateAsync(int id)
        {
            var template = await _templateRepository.GetTemplateByIdAsync(id);
            if (template == null)
                throw new ArgumentException("Template not found.");

            return await _templateRepository.DeleteTemplateAsync(id);
        }

        public async Task<bool> ToggleTemplateStatusAsync(int id)
        {
            var template = await _templateRepository.GetTemplateByIdAsync(id);
            if (template == null)
                throw new ArgumentException("Template not found.");

            return await _templateRepository.ToggleTemplateStatusAsync(id);
        }
    }
} 