using System;
using System.Threading.Tasks;
using IPIS.Utils;
using IPIS.Repositories;
using IPIS.Services;
using IPIS.Models;

namespace IPIS.Test
{
    public class DatabaseTest
    {
        public static async Task TestDatabaseStructure()
        {
            try
            {
                Console.WriteLine("Testing database structure...");
                
                // Initialize database
                Database.InitializeDatabase();
                Console.WriteLine("Database initialized successfully.");
                
                // Test language service
                var languageService = new LanguageService(new SQLiteLanguageRepository());
                
                // Add a test language
                var testLanguage = new Language
                {
                    Name = "Test Language",
                    Code = "TL",
                    NativeName = "Test Language Native",
                    WaveFolderPath = "C:\\Test\\Wave",
                    IsActive = true,
                    IsDefault = false
                };
                
                var languageId = await languageService.AddLanguageAsync(testLanguage);
                Console.WriteLine($"Test language added with ID: {languageId}");
                
                // Test station language service
                var stationLanguageService = new StationLanguageService(
                    new SQLiteStationLanguageConfigRepository(),
                    new SQLiteStationAnnouncementConfigRepository(),
                    new SQLiteLanguageRepository());
                
                // Test adding station language config
                var success = await stationLanguageService.AddStationLanguageAsync("Test Station", "TL", true, "C:\\Test\\Wave\\test.wav");
                Console.WriteLine($"Station language config added: {success}");
                
                // Test getting station languages
                var stationLanguages = await stationLanguageService.GetStationLanguagesAsync("Test Station");
                Console.WriteLine($"Found {stationLanguages.Count} language configurations for Test Station");
                
                Console.WriteLine("Database structure test completed successfully!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error testing database structure: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }
    }
} 