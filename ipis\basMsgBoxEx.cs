// Decompiled with JetBrains decompiler
// Type: ipis.basMsgBoxEx
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using Microsoft.VisualBasic.Compatibility.VB6;
using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Windows.Forms;

namespace ipis
{

[StandardModule]
internal sealed class basMsgBoxEx
{
  private const int NV_CLOSEMSGBOX = 20480 /*0x5000*/;
  private const int NV_MOVEMSGBOX = 20481;
  private const short HWND_TOPMOST = -1;
  private const short SWP_NOSIZE = 1;
  private static string mTitle;
  private static int mX;
  private static int mY_Renamed;
  private static int mPause;
  private static int mHandle;

  [DebuggerNonUserCode]
  static basMsgBoxEx()
  {
  }

  [DllImport("user32", EntryPoint = "MessageBoxA", CharSet = CharSet.Ansi, SetLastError = true)]
  private static extern int MessageBox(
    int hwnd,
    [MarshalAs(UnmanagedType.VBByRefStr)] ref string lpText,
    [MarshalAs(UnmanagedType.VBByRefStr)] ref string lpCaption,
    int wType);

  [DllImport("user32", EntryPoint = "FindWindowA", CharSet = CharSet.Ansi, SetLastError = true)]
  private static extern int FindWindow([MarshalAs(UnmanagedType.VBByRefStr)] ref string lpClassName, [MarshalAs(UnmanagedType.VBByRefStr)] ref string lpWindowName);

  [DllImport("user32", CharSet = CharSet.Ansi, SetLastError = true)]
  private static extern int SetWindowPos(
    int hwnd,
    int hWndInsertAfter,
    int x,
    int y,
    int cx,
    int cy,
    int wFlags);

  [DllImport("user32", CharSet = CharSet.Ansi, SetLastError = true)]
  private static extern int SetForegroundWindow(int hwnd);

  [DllImport("user32", CharSet = CharSet.Ansi, SetLastError = true)]
  private static extern int SetTimer(int hwnd, int nIDEvent, int uElapse, int lpTimerFunc);

  [DllImport("user32", CharSet = CharSet.Ansi, SetLastError = true)]
  private static extern int KillTimer(int hwnd, int nIDEvent);

  [DllImport("user32", CharSet = CharSet.Ansi, SetLastError = true)]
  private static extern int GetWindowRect(int hwnd, ref basMsgBoxEx.RECT lpRect);

  public static short MsgBoxMove(
    int hwnd,
    string inPrompt,
    string inTitle,
    int inButtons,
    int inX,
    int inY)
  {
    basMsgBoxEx.mTitle = inTitle;
    basMsgBoxEx.mX = inX;
    basMsgBoxEx.mY_Renamed = inY;
    basMsgBoxEx.time_proc timeProc = new basMsgBoxEx.time_proc(basMsgBoxEx.NewTimerProc);
    basMsgBoxEx.SetTimer(hwnd, 20481, 0, timeProc(hwnd, 20481, 20481, inY));
    return checked ((short) basMsgBoxEx.MessageBox(hwnd, ref inPrompt, ref inTitle, inButtons));
  }

  public static short MsgBoxPause(
    int hwnd,
    string inPrompt,
    string inTitle,
    int inButtons,
    short inPause)
  {
    basMsgBoxEx.mTitle = inTitle;
    basMsgBoxEx.mPause = checked ((int) inPause * 1000);
    return checked ((short) basMsgBoxEx.MessageBox(hwnd, ref inPrompt, ref inTitle, inButtons));
  }

  public static int NewTimerProc(int hwnd, int Msg, int wparam, int lparam)
  {
    basMsgBoxEx.KillTimer(hwnd, wparam);
    switch (wparam)
    {
      case 20480 /*0x5000*/:
        string lpClassName1 = "#32770";
        basMsgBoxEx.mHandle = basMsgBoxEx.FindWindow(ref lpClassName1, ref basMsgBoxEx.mTitle);
        if (basMsgBoxEx.mHandle != 0)
        {
          basMsgBoxEx.SetForegroundWindow(basMsgBoxEx.mHandle);
          SendKeys.Send("{enter}");
          break;
        }
        break;
      case 20481:
        string lpClassName2 = "#32770";
        basMsgBoxEx.mHandle = basMsgBoxEx.FindWindow(ref lpClassName2, ref basMsgBoxEx.mTitle);
        if (basMsgBoxEx.mHandle != 0)
        {
          float num1 = (float) Support.PixelsToTwipsX((double) Screen.PrimaryScreen.Bounds.Width) / Support.TwipsPerPixelX();
          float num2 = (float) Support.PixelsToTwipsY((double) Screen.PrimaryScreen.Bounds.Height) / Support.TwipsPerPixelY();
          basMsgBoxEx.RECT lpRect = new basMsgBoxEx.RECT();
          basMsgBoxEx.GetWindowRect(basMsgBoxEx.mHandle, ref lpRect);
          if ((double) basMsgBoxEx.mX > (double) num1 - (double) checked (lpRect.Right_Renamed - lpRect.Left_Renamed) - 1.0)
            basMsgBoxEx.mX = checked ((int) Math.Round(unchecked ((double) num1 - (double) checked (lpRect.Right_Renamed - lpRect.Left_Renamed) - 1.0)));
          if ((double) basMsgBoxEx.mY_Renamed > (double) num2 - (double) checked (lpRect.Bottom - lpRect.Top) - 1.0)
            basMsgBoxEx.mY_Renamed = checked ((int) Math.Round(unchecked ((double) num2 - (double) checked (lpRect.Bottom - lpRect.Top) - 1.0)));
          if (basMsgBoxEx.mX < 1)
          {
            basMsgBoxEx.mX = 1;
            if (basMsgBoxEx.mY_Renamed < 1)
              basMsgBoxEx.mY_Renamed = 1;
          }
          basMsgBoxEx.SetWindowPos(basMsgBoxEx.mHandle, -1, 0, 0, 0, 0, 1);
        }
        break;
    }
    int num = 0;
    return num;
  }

  private struct RECT
  {
    public int Left_Renamed;
    public int Top;
    public int Right_Renamed;
    public int Bottom;
  }

  public delegate int time_proc(int hwnd, int Msg, int wparam, int lparam);
}

}