using System;
using System.IO;
using System.Runtime.InteropServices;

namespace IPIS.Utils
{
    public static class WaveFileDuration
    {
        [StructLayout(LayoutKind.Sequential)]
        private struct WAVEFORMATEX
        {
            public ushort wFormatTag;
            public ushort nChannels;
            public uint nSamplesPerSec;
            public uint nAvgBytesPerSec;
            public ushort nBlockAlign;
            public ushort wBitsPerSample;
            public ushort cbSize;
        }

        [StructLayout(LayoutKind.Sequential)]
        private struct RIFFHEADER
        {
            [MarshalAs(UnmanagedType.ByValArray, SizeConst = 4)]
            public byte[] riff;
            public uint fileSize;
            [MarshalAs(UnmanagedType.ByValArray, SizeConst = 4)]
            public byte[] wave;
        }

        [StructLayout(LayoutKind.Sequential)]
        private struct CHUNKHEADER
        {
            [MarshalAs(UnmanagedType.ByValArray, SizeConst = 4)]
            public byte[] chunkId;
            public uint chunkSize;
        }

        /// <summary>
        /// Gets the duration of a wave file in seconds
        /// </summary>
        /// <param name="filePath">Path to the wave file</param>
        /// <returns>Duration in seconds, or 0 if unable to read</returns>
        public static double GetDuration(string filePath)
        {
            try
            {
                if (string.IsNullOrEmpty(filePath) || !File.Exists(filePath))
                    return 0;

                using (var stream = new FileStream(filePath, FileMode.Open, FileAccess.Read))
                using (var reader = new BinaryReader(stream))
                {
                    // Read RIFF header
                    var riff = reader.ReadBytes(4);
                    var fileSize = reader.ReadUInt32();
                    var wave = reader.ReadBytes(4);

                    if (System.Text.Encoding.ASCII.GetString(riff) != "RIFF" ||
                        System.Text.Encoding.ASCII.GetString(wave) != "WAVE")
                        return 0;

                    // Find format and data chunks
                    uint dataSize = 0;
                    uint sampleRate = 0;
                    ushort channels = 0;
                    ushort bitsPerSample = 0;

                    while (stream.Position < stream.Length - 8)
                    {
                        var chunkId = reader.ReadBytes(4);
                        var chunkSize = reader.ReadUInt32();
                        var chunkName = System.Text.Encoding.ASCII.GetString(chunkId);

                        if (chunkName == "fmt ")
                        {
                            var formatTag = reader.ReadUInt16();
                            channels = reader.ReadUInt16();
                            sampleRate = reader.ReadUInt32();
                            var avgBytesPerSec = reader.ReadUInt32();
                            var blockAlign = reader.ReadUInt16();
                            bitsPerSample = reader.ReadUInt16();

                            if (chunkSize > 16)
                                reader.ReadBytes((int)(chunkSize - 16));
                        }
                        else if (chunkName == "data")
                        {
                            dataSize = chunkSize;
                            break;
                        }
                        else
                        {
                            reader.ReadBytes((int)chunkSize);
                        }
                    }

                    if (dataSize > 0 && sampleRate > 0 && channels > 0 && bitsPerSample > 0)
                    {
                        uint bytesPerSample = (uint)(bitsPerSample / 8);
                        uint bytesPerSecond = sampleRate * channels * bytesPerSample;
                        return (double)dataSize / bytesPerSecond;
                    }

                    return 0;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error reading wave file duration: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// Gets the duration of a wave file and formats it as MM:SS
        /// </summary>
        /// <param name="filePath">Path to the wave file</param>
        /// <returns>Formatted duration string (MM:SS) or "00:00" if unable to read</returns>
        public static string GetFormattedDuration(string filePath)
        {
            double duration = GetDuration(filePath);
            if (duration <= 0) return "00:00";

            int minutes = (int)(duration / 60);
            int seconds = (int)(duration % 60);
            return $"{minutes:D2}:{seconds:D2}";
        }

        /// <summary>
        /// Gets the total duration of multiple wave files
        /// </summary>
        /// <param name="filePaths">Array of wave file paths</param>
        /// <returns>Total duration in seconds</returns>
        public static double GetTotalDuration(string[] filePaths)
        {
            if (filePaths == null || filePaths.Length == 0) return 0;

            double totalDuration = 0;
            foreach (string filePath in filePaths)
            {
                if (!string.IsNullOrEmpty(filePath))
                    totalDuration += GetDuration(filePath);
            }
            return totalDuration;
        }

        /// <summary>
        /// Gets the total duration of multiple wave files and formats it as MM:SS
        /// </summary>
        /// <param name="filePaths">Array of wave file paths</param>
        /// <returns>Formatted total duration string (MM:SS)</returns>
        public static string GetFormattedTotalDuration(string[] filePaths)
        {
            double totalDuration = GetTotalDuration(filePaths);
            if (totalDuration <= 0) return "00:00";

            int minutes = (int)(totalDuration / 60);
            int seconds = (int)(totalDuration % 60);
            return $"{minutes:D2}:{seconds:D2}";
        }
    }
} 