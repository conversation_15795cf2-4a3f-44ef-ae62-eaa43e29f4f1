// Decompiled with JetBrains decompiler
// Type: ipis.frmCom
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

namespace ipis
{

[DesignerGenerated]
public class frmCom : Form
{
  private static List<WeakReference> __ENCList = new List<WeakReference>();
  private IContainer components;
  [AccessedThroughProperty("btnExit")]
  private Button _btnExit;
  [AccessedThroughProperty("cmbBaud")]
  private ComboBox _cmbBaud;
  [AccessedThroughProperty("Label2")]
  private Label _Label2;
  [AccessedThroughProperty("cmbCom")]
  private ComboBox _cmbCom;
  [AccessedThroughProperty("Label1")]
  private Label _Label1;
  [AccessedThroughProperty("btnSave")]
  private Button _btnSave;
  private string com_port;
  private int baud_rate;
  public static int COMValue = 1;
  private RS232 frm;

  public frmCom()
  {
    this.Load += new EventHandler(this.frmCOM_Load);
    frmCom.__ENCAddToList((object) this);
    this.com_port = "COM1";
    this.baud_rate = 57600;
    this.frm = new RS232();
    this.InitializeComponent();
  }

  [DebuggerNonUserCode]
  private static void __ENCAddToList(object value)
  {
    lock (frmCom.__ENCList)
    {
      if (frmCom.__ENCList.Count == frmCom.__ENCList.Capacity)
      {
        int index1 = 0;
        int num = checked (frmCom.__ENCList.Count - 1);
        int index2 = 0;
        while (index2 <= num)
        {
          if (frmCom.__ENCList[index2].IsAlive)
          {
            if (index2 != index1)
              frmCom.__ENCList[index1] = frmCom.__ENCList[index2];
            checked { ++index1; }
          }
          checked { ++index2; }
        }
        frmCom.__ENCList.RemoveRange(index1, checked (frmCom.__ENCList.Count - index1));
        frmCom.__ENCList.Capacity = frmCom.__ENCList.Count;
      }
      frmCom.__ENCList.Add(new WeakReference(RuntimeHelpers.GetObjectValue(value)));
    }
  }

  [DebuggerNonUserCode]
  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  [DebuggerStepThrough]
  private void InitializeComponent()
  {
    this.btnExit = new Button();
    this.cmbBaud = new ComboBox();
    this.Label2 = new Label();
    this.cmbCom = new ComboBox();
    this.Label1 = new Label();
    this.btnSave = new Button();
    this.SuspendLayout();
    this.btnExit.BackColor = SystemColors.ButtonFace;
    this.btnExit.DialogResult = DialogResult.Cancel;
    this.btnExit.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnExit1 = this.btnExit;
    Point point1 = new Point(160 /*0xA0*/, 136);
    Point point2 = point1;
    btnExit1.Location = point2;
    this.btnExit.Name = "btnExit";
    Button btnExit2 = this.btnExit;
    Size size1 = new Size(60, 25);
    Size size2 = size1;
    btnExit2.Size = size2;
    this.btnExit.TabIndex = 4;
    this.btnExit.Text = "Exit";
    this.btnExit.UseVisualStyleBackColor = false;
    this.cmbBaud.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.cmbBaud.FormattingEnabled = true;
    this.cmbBaud.Items.AddRange(new object[8]
    {
      (object) "1200",
      (object) "2400",
      (object) "4800",
      (object) "9600",
      (object) "19200",
      (object) "38400",
      (object) "57600",
      (object) "115200"
    });
    ComboBox cmbBaud1 = this.cmbBaud;
    point1 = new Point(148, 80 /*0x50*/);
    Point point3 = point1;
    cmbBaud1.Location = point3;
    this.cmbBaud.Name = "cmbBaud";
    ComboBox cmbBaud2 = this.cmbBaud;
    size1 = new Size(85, 24);
    Size size3 = size1;
    cmbBaud2.Size = size3;
    this.cmbBaud.TabIndex = 2;
    this.Label2.AutoSize = true;
    this.Label2.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label label2_1 = this.Label2;
    point1 = new Point(37, 80 /*0x50*/);
    Point point4 = point1;
    label2_1.Location = point4;
    this.Label2.Name = "Label2";
    Label label2_2 = this.Label2;
    size1 = new Size(71, 16 /*0x10*/);
    Size size4 = size1;
    label2_2.Size = size4;
    this.Label2.TabIndex = 16 /*0x10*/;
    this.Label2.Text = "Baudrate";
    this.cmbCom.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.cmbCom.FormattingEnabled = true;
    this.cmbCom.Items.AddRange(new object[10]
    {
      (object) "COM1",
      (object) "COM2",
      (object) "COM3",
      (object) "COM4",
      (object) "COM5",
      (object) "COM6",
      (object) "COM7",
      (object) "COM8",
      (object) "COM9",
      (object) "COM10"
    });
    ComboBox cmbCom1 = this.cmbCom;
    point1 = new Point(148, 27);
    Point point5 = point1;
    cmbCom1.Location = point5;
    this.cmbCom.Name = "cmbCom";
    ComboBox cmbCom2 = this.cmbCom;
    size1 = new Size(85, 24);
    Size size5 = size1;
    cmbCom2.Size = size5;
    this.cmbCom.TabIndex = 1;
    this.Label1.AutoSize = true;
    this.Label1.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label label1_1 = this.Label1;
    point1 = new Point(37, 30);
    Point point6 = point1;
    label1_1.Location = point6;
    this.Label1.Name = "Label1";
    Label label1_2 = this.Label1;
    size1 = new Size(69, 16 /*0x10*/);
    Size size6 = size1;
    label1_2.Size = size6;
    this.Label1.TabIndex = 14;
    this.Label1.Text = "COMPort";
    this.btnSave.BackColor = SystemColors.ButtonFace;
    this.btnSave.DialogResult = DialogResult.Cancel;
    this.btnSave.Enabled = false;
    this.btnSave.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnSave1 = this.btnSave;
    point1 = new Point(65, 136);
    Point point7 = point1;
    btnSave1.Location = point7;
    this.btnSave.Name = "btnSave";
    Button btnSave2 = this.btnSave;
    size1 = new Size(60, 25);
    Size size7 = size1;
    btnSave2.Size = size7;
    this.btnSave.TabIndex = 3;
    this.btnSave.Text = "Save";
    this.btnSave.UseVisualStyleBackColor = false;
    this.AcceptButton = (IButtonControl) this.btnSave;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.BackColor = Color.PowderBlue;
    this.CancelButton = (IButtonControl) this.btnExit;
    size1 = new Size(278, 189);
    this.ClientSize = size1;
    this.Controls.Add((Control) this.btnExit);
    this.Controls.Add((Control) this.cmbBaud);
    this.Controls.Add((Control) this.Label2);
    this.Controls.Add((Control) this.cmbCom);
    this.Controls.Add((Control) this.Label1);
    this.Controls.Add((Control) this.btnSave);
    this.FormBorderStyle = FormBorderStyle.FixedToolWindow;
    this.Name = "frmCom";
    this.Text = "COM Port";
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  internal virtual Button btnExit
  {
    [DebuggerNonUserCode] get { return this._btnExit; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnExit_Click);
      if (this._btnExit != null)
        this._btnExit.Click -= eventHandler;
      this._btnExit = value;
      if (this._btnExit == null)
        return;
      this._btnExit.Click += eventHandler;
    }
  }

  internal virtual ComboBox cmbBaud
  {
    [DebuggerNonUserCode] get { return this._cmbBaud; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbBaud = value; }
  }

  internal virtual Label Label2
  {
    [DebuggerNonUserCode] get { return this._Label2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Label2 = value; }
  }

  internal virtual ComboBox cmbCom
  {
    [DebuggerNonUserCode] get { return this._cmbCom; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbCom = value; }
  }

  internal virtual Label Label1
  {
    [DebuggerNonUserCode] get { return this._Label1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Label1 = value; }
  }

  internal virtual Button btnSave
  {
    [DebuggerNonUserCode] get { return this._btnSave; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnSave_Click);
      if (this._btnSave != null)
        this._btnSave.Click -= eventHandler;
      this._btnSave = value;
      if (this._btnSave == null)
        return;
      this._btnSave.Click += eventHandler;
    }
  }

  private void btnSave_Click(object sender, EventArgs e)
  {
    string text = this.cmbCom.Text;
    int integer = Conversions.ToInteger(this.cmbBaud.Text);
    int Dtb = 8;
    bool result = false;
    try
    {
      network_db_read.set_com_baud_values(this.cmbCom.Text, Conversions.ToInteger(this.cmbBaud.Text), ref result);
      if (this.frm.Serial_Open(Strings.Trim(text), integer, RS232.enumParity.None, Dtb, RS232.enumStopBits.One) == 1)
      {
        int num1 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Port Configuration Successful", "Msg Box", 0, 0, 0);
      }
      else
      {
        int num2 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Can't open the {Strings.Trim(this.cmbCom.Text)}Port", "Msg Box", 0, 0, 0);
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void btnExit_Click(object sender, EventArgs e)
{
  this.Close();
}

  private void frmCOM_Load(object sender, EventArgs e)
  {
    this.btnSave.Enabled = true;
    network_db_read.get_com_baud_values(ref this.com_port, ref this.baud_rate);
    this.cmbCom.Text = this.com_port;
    this.cmbBaud.Text = Conversions.ToString(this.baud_rate);
  }
}

}