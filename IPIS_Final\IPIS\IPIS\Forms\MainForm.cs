using System;
using System.Windows.Forms;
using IPIS.Forms.Announcement;
using IPIS.Forms.Train;
using IPIS.Forms.Coach;
using IPIS.Forms.User;
using IPIS.Forms.Settings;
using IPIS.Forms.Advertising;
using IPIS.Forms.Station;
using IPIS.Forms.Logs;
using IPIS.Utils;
using IPIS.Models;

namespace IPIS
{
    /// <summary>
    /// Main application form with role-based menu system
    /// Provides access to all system features based on user permissions and roles
    ///
    /// Role-based access:
    /// - Administrator: Full access to all features
    /// - Supervisor: User management, reports, logs, and operational features
    /// - Operator: Operational features like advertising, train data
    /// - User: Basic features based on specific permissions
    ///
    /// Permission-based features:
    /// - "Add Advertising": Access to advertising management
    /// - "Train Data Entry": Access to train and train type management
    /// - "Station Details": Access to station management
    /// - "Add Station Code": Access to station code management
    /// - "Reports": Access to system reports
    /// - "Add User": Access to user management (with role restrictions)
    /// </summary>
    public partial class MainForm : Form
    {
        private ToolStripStatusLabel userInfoLabel;
        private ToolStripStatusLabel sessionInfoLabel;

        public MainForm()
        {
            InitializeComponent();
            CheckAuthentication();
        }

        private void CheckAuthentication()
        {
            if (!SessionManager.IsLoggedIn)
            {
                MessageBox.Show("You must be logged in to access the system.", "Authentication Required",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                Application.Exit();
                return;
            }

            // Update user info in status bar
            UpdateUserInfo();
        }

        private void UpdateUserInfo()
        {
            if (SessionManager.CurrentUser != null)
            {
                userInfoLabel.Text = $"User: {SessionManager.CurrentUser.Username} ({SessionManager.CurrentUser.Role})";
                sessionInfoLabel.Text = $"Session: {SessionManager.SessionDuration:hh\\:mm\\:ss}";
            }
        }

        private void InitializeComponent()
        {
            this.IsMdiContainer = true;
            this.WindowState = FormWindowState.Maximized;
            this.Text = "IPIS - Integrated Passenger Information System";

            // Create menu strip
            MenuStrip menuStrip = new MenuStrip();
            this.MainMenuStrip = menuStrip;
            this.Controls.Add(menuStrip);

            // Create status strip
            StatusStrip statusStrip = new StatusStrip();
            this.Controls.Add(statusStrip);

            // Add status labels
            userInfoLabel = new ToolStripStatusLabel();
            sessionInfoLabel = new ToolStripStatusLabel();
            statusStrip.Items.AddRange(new ToolStripItem[] { userInfoLabel, sessionInfoLabel });

            // Build menus based on user roles and permissions
            BuildMenus(menuStrip);

            // Show AnnouncementBoardForm by default
            OpenForm(new AnnouncementBoardForm());

            // Start timer to update session duration
            System.Windows.Forms.Timer sessionTimer = new System.Windows.Forms.Timer
            {
                Interval = 1000 // Update every second
            };
            sessionTimer.Tick += (s, e) => UpdateUserInfo();
            sessionTimer.Start();
        }

        private void BuildMenus(MenuStrip menuStrip)
        {
            var menuItems = new System.Collections.Generic.List<ToolStripItem>();

            // File Menu - Always visible
            ToolStripMenuItem fileMenu = CreateFileMenu();
            menuItems.Add(fileMenu);

            // Dashboard Menu - Always visible
            ToolStripMenuItem dashboardMenu = CreateDashboardMenu();
            menuItems.Add(dashboardMenu);

            // Train and Station Menu - Based on permissions
            ToolStripMenuItem trainMenu = CreateTrainStationMenu();
            if (trainMenu != null && trainMenu.DropDownItems.Count > 0)
            {
                menuItems.Add(trainMenu);
            }

            // Announcement Menu - Based on permissions (currently commented out)
            // ToolStripMenuItem announcementMenu = CreateAnnouncementMenu();
            // if (announcementMenu != null && announcementMenu.DropDownItems.Count > 0)
            // {
            //     menuItems.Add(announcementMenu);
            // }

            // Advertising Menu - Based on permissions
            ToolStripMenuItem advertisingMenu = CreateAdvertisingMenu();
            if (advertisingMenu != null && advertisingMenu.DropDownItems.Count > 0)
            {
                menuItems.Add(advertisingMenu);
            }

            // User Management Menu - Based on role
            ToolStripMenuItem userMenu = CreateUserManagementMenu();
            if (userMenu != null)
            {
                menuItems.Add(userMenu);
            }

            // Reports Menu - Based on permissions
            ToolStripMenuItem reportsMenu = CreateReportsMenu();
            if (reportsMenu != null && reportsMenu.DropDownItems.Count > 0)
            {
                menuItems.Add(reportsMenu);
            }

            // Logs Menu - Based on role
            ToolStripMenuItem logsMenu = CreateLogsMenu();
            if (logsMenu != null)
            {
                menuItems.Add(logsMenu);
            }

            // System Menu - Based on role
            ToolStripMenuItem systemMenu = CreateSystemMenu();
            if (systemMenu != null)
            {
                menuItems.Add(systemMenu);
            }

            // Add all menus to the menu strip
            menuStrip.Items.AddRange(menuItems.ToArray());
        }

        private ToolStripMenuItem CreateFileMenu()
        {
            ToolStripMenuItem fileMenu = new ToolStripMenuItem("File");
            fileMenu.DropDownItems.AddRange(new ToolStripItem[] {
                new ToolStripMenuItem("Logout", null, Logout_Click),
                new ToolStripSeparator(),
                new ToolStripMenuItem("Exit", null, (s, e) => Application.Exit())
            });
            return fileMenu;
        }

        private ToolStripMenuItem CreateDashboardMenu()
        {
            return new ToolStripMenuItem("Dashboard", null, (s, e) => OpenForm(new AnnouncementBoardForm()));
        }

        private ToolStripMenuItem CreateTrainStationMenu()
        {
            ToolStripMenuItem trainMenu = new ToolStripMenuItem("Trains & Station");
            var menuItems = new System.Collections.Generic.List<ToolStripItem>();

            // Train Data Entry - Check permission
            if (SessionManager.HasPermission("Train Data Entry") || SessionManager.IsAdministrator())
            {
                menuItems.Add(new ToolStripMenuItem("Add Train Details", null, (s, e) => OpenForm(new AddTrainDetailsForm())));
                menuItems.Add(new ToolStripMenuItem("Add Train Type", null, (s, e) => OpenForm(new IPIS.Forms.Train.AddTrainTypeForm())));
            }

            // Load Data - Admin only
            if (SessionManager.IsAdministrator())
            {
                menuItems.Add(new ToolStripMenuItem("Load Data", null, (s, e) => OpenForm(new IPIS.Forms.Train.LoadDataForm())));
            }

            // Station Details - Check permission
            if (SessionManager.HasPermission("Station Details") || SessionManager.IsAdministrator())
            {
                if (menuItems.Count > 0) menuItems.Add(new ToolStripSeparator());
                menuItems.Add(new ToolStripMenuItem("Add Station Details", null, (s, e) => OpenForm(new IPIS.Forms.Station.AddStationDetailsForm())));
            }

            // Add Station Code - Check permission
            if (SessionManager.HasPermission("Add Station Code") || SessionManager.IsAdministrator())
            {
                menuItems.Add(new ToolStripMenuItem("Current Station", null, (s, e) => OpenForm(new IPIS.Forms.Station.CurrentStationForm())));
            }

            trainMenu.DropDownItems.AddRange(menuItems.ToArray());
            return trainMenu;
        }

        private ToolStripMenuItem CreateAdvertisingMenu()
        {
            // Check if user has advertising permissions
            if (!SessionManager.HasPermission("Add Advertising") && !SessionManager.IsAdministrator() && !SessionManager.IsOperator())
            {
                return null;
            }

            ToolStripMenuItem advertisingMenu = new ToolStripMenuItem("Advertising");
            advertisingMenu.DropDownItems.AddRange(new ToolStripItem[] {
                new ToolStripMenuItem("Dashboard", null, (s, e) => OpenForm(new AdvertisingDashboardForm())),
                new ToolStripMenuItem("Add Advertising", null, (s, e) => OpenForm(new AdvertisingForm()))
            });
            return advertisingMenu;
        }

        private ToolStripMenuItem CreateUserManagementMenu()
        {
            // Only administrators and supervisors can access user management
            if (!SessionManager.IsAdministrator() && !SessionManager.IsSupervisor())
            {
                return null;
            }

            ToolStripMenuItem userMenu = new ToolStripMenuItem("User Management");
            var menuItems = new System.Collections.Generic.List<ToolStripItem>();

            // User Management - Available to both Admin and Supervisor
            menuItems.Add(new ToolStripMenuItem("User Management", null, (s, e) => OpenForm(new UserManagementForm())));

            // Role Management - Only for Administrators
            if (SessionManager.IsAdministrator())
            {
                menuItems.Add(new ToolStripMenuItem("Role Management", null, (s, e) => OpenForm(new RoleManagementForm())));
            }

            userMenu.DropDownItems.AddRange(menuItems.ToArray());
            return userMenu;
        }

        private ToolStripMenuItem CreateReportsMenu()
        {
            // Check if user has reports permission
            if (!SessionManager.HasPermission("Reports") && !SessionManager.IsAdministrator())
            {
                return null;
            }

            ToolStripMenuItem reportsMenu = new ToolStripMenuItem("Reports");
            var menuItems = new System.Collections.Generic.List<ToolStripItem>();

            // Add report menu items here when report forms are available
            // For now, just add a placeholder
            menuItems.Add(new ToolStripMenuItem("System Reports", null, (s, e) =>
                MessageBox.Show("Reports functionality will be available soon.", "Reports",
                    MessageBoxButtons.OK, MessageBoxIcon.Information)));

            reportsMenu.DropDownItems.AddRange(menuItems.ToArray());
            return reportsMenu;
        }

        private ToolStripMenuItem CreateLogsMenu()
        {
            // Only administrators and supervisors can access logs
            if (!SessionManager.IsAdministrator() && !SessionManager.IsSupervisor())
            {
                return null;
            }

            ToolStripMenuItem logsMenu = new ToolStripMenuItem("Logs");
            logsMenu.DropDownItems.AddRange(new ToolStripItem[] {
                new ToolStripMenuItem("View Logs", null, (s, e) => OpenForm(new LogsForm()))
            });
            return logsMenu;
        }

        private ToolStripMenuItem CreateSystemMenu()
        {
            // Only administrators can access system settings
            if (!SessionManager.IsAdministrator())
            {
                return null;
            }

            return new ToolStripMenuItem("System Settings", null, (s, e) => OpenForm(new SystemSettingsForm()));
        }

        // Commented out announcement menu for future use
        // private ToolStripMenuItem CreateAnnouncementMenu()
        // {
        //     ToolStripMenuItem announcementMenu = new ToolStripMenuItem("Announcement");
        //     var menuItems = new System.Collections.Generic.List<ToolStripItem>();
        //
        //     // Add announcement menu items based on permissions
        //     menuItems.Add(new ToolStripMenuItem("Manual Announcement", null, (s, e) => OpenForm(new ManualAnnouncementForm())));
        //     menuItems.Add(new ToolStripMenuItem("Auto Announcement", null, (s, e) => OpenForm(new AutoAnnouncementForm())));
        //
        //     announcementMenu.DropDownItems.AddRange(menuItems.ToArray());
        //     return announcementMenu;
        // }

        private void Logout_Click(object sender, EventArgs e)
        {
            var result = MessageBox.Show("Are you sure you want to logout?", "Confirm Logout",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                SessionManager.Logout();
                Application.Restart();
            }
        }

        private void OpenForm(Form form)
        {
            try
            {
                string formName = form.GetType().Name;

                // Check permissions before opening forms
                if (!CheckFormPermissions(form))
                {
                    LogMenuAccess(formName, false);
                    ShowPermissionDeniedMessage(formName.Replace("Form", ""));
                    form.Dispose(); // Clean up the form since we're not using it
                    return;
                }

                // Log successful access
                LogMenuAccess(formName, true);

                // Close any existing instance of the same form type
                foreach (Form existingForm in this.MdiChildren)
                {
                    if (existingForm.GetType() == form.GetType())
                    {
                        existingForm.Close();
                        break;
                    }
                }

                // Configure and show the form
                form.MdiParent = this;
                form.WindowState = FormWindowState.Maximized;
                form.Show();
                form.BringToFront();
            }
            catch (Exception ex)
            {
                // Log the error
                Logger.LogError(LogCategory.System, "Error opening form",
                    $"Failed to open {form.GetType().Name}: {ex.Message}",
                    "MainForm.OpenForm", ex);

                // Show user-friendly error message
                MessageBox.Show($"An error occurred while opening the form.\n\nError: {ex.Message}",
                    "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);

                // Clean up the form
                form?.Dispose();
            }
        }

        private bool CheckFormPermissions(Form form)
        {
            // Check specific form permissions based on user role and permissions

            // User Management Forms
            if (form is UserManagementForm)
            {
                return SessionManager.IsAdministrator() || SessionManager.IsSupervisor();
            }

            if (form is RoleManagementForm)
            {
                return SessionManager.IsAdministrator(); // Only administrators can manage roles
            }

            // System Settings
            if (form is SystemSettingsForm)
            {
                return SessionManager.IsAdministrator();
            }

            // Advertising Forms
            if (form is AdvertisingForm || form is AdvertisingDashboardForm)
            {
                return SessionManager.HasPermission("Add Advertising") ||
                       SessionManager.IsAdministrator() ||
                       SessionManager.IsOperator();
            }

            // Train Forms (excluding LoadDataForm)
            if (form is AddTrainDetailsForm || form is IPIS.Forms.Train.AddTrainTypeForm)
            {
                return SessionManager.HasPermission("Train Data Entry") || SessionManager.IsAdministrator();
            }

            // Load Data Form - Admin only
            if (form is IPIS.Forms.Train.LoadDataForm)
            {
                return SessionManager.IsAdministrator();
            }

            if (form is IPIS.Forms.Station.AddStationDetailsForm)
            {
                return SessionManager.HasPermission("Station Details") || SessionManager.IsAdministrator();
            }

            if (form is IPIS.Forms.Station.CurrentStationForm)
            {
                return SessionManager.HasPermission("Add Station Code") || SessionManager.IsAdministrator();
            }

            // Logs Form
            if (form is LogsForm)
            {
                return SessionManager.IsAdministrator() || SessionManager.IsSupervisor();
            }

            // Coach Forms
            if (form is IPIS.Forms.Coach.CoachGuidanceForm)
            {
                return SessionManager.HasPermission("Train Data Entry") || SessionManager.IsAdministrator();
            }

            // Announcement Forms (for future use)
            // if (form is ManualAnnouncementForm || form is AutoAnnouncementForm)
            // {
            //     return SessionManager.HasPermission("Announcements") || SessionManager.IsAdministrator();
            // }

            // Default forms that are generally accessible (like AnnouncementBoardForm)
            if (form is AnnouncementBoardForm)
            {
                return true; // Dashboard is accessible to all logged-in users
            }

            // Default deny for unknown forms - be restrictive for security
            return SessionManager.IsAdministrator();
        }

        /// <summary>
        /// Refreshes the menu structure based on current user permissions
        /// Call this method if user permissions change during the session
        /// </summary>
        public void RefreshMenus()
        {
            if (this.MainMenuStrip != null)
            {
                this.MainMenuStrip.Items.Clear();
                BuildMenus(this.MainMenuStrip);
                UpdateUserInfo();
            }
        }

        /// <summary>
        /// Shows a permission denied message with consistent formatting
        /// </summary>
        private void ShowPermissionDeniedMessage(string feature = "this feature")
        {
            MessageBox.Show($"You don't have permission to access {feature}.\n\nContact your administrator if you need access.",
                "Access Denied", MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }

        /// <summary>
        /// Logs menu access attempts for security auditing
        /// </summary>
        private void LogMenuAccess(string menuItem, bool accessGranted)
        {
            try
            {
                string action = accessGranted
                    ? $"Access Menu: {menuItem}"
                    : $"Access Denied: {menuItem}";

                Logger.LogUserActivity(action, LogCategory.Security, "Menu", menuItem);
            }
            catch (Exception ex)
            {
                // Don't let logging errors affect the application
                System.Diagnostics.Debug.WriteLine($"Menu access logging error: {ex.Message}");
            }
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            if (e.CloseReason == CloseReason.UserClosing)
            {
                var result = MessageBox.Show("Are you sure you want to exit the application?", "Confirm Exit",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.No)
                {
                    e.Cancel = true;
                    return;
                }
            }

            SessionManager.Logout();
            base.OnFormClosing(e);
        }
    }
}