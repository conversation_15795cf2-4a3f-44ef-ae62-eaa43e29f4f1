﻿using System;
using System.Data;
using System.IO;
using System.Threading;
using System.Windows.Forms;

namespace Announcement
{
	// Token: 0x02000009 RID: 9
	internal class Misc_Functions : Exception
	{
		// Token: 0x0600003D RID: 61 RVA: 0x00009950 File Offset: 0x00007B50
		public void Write_Log(string Device, DataTable DTable, string Remark)
		{
			try
			{
				string text = Application.StartupPath + "\\Data\\Logs\\" + Device + "\\";
				string[] files = Directory.GetFiles(text);
				foreach (string path in files)
				{
					DateTime creationTime = File.GetCreationTime(path);
					bool flag = creationTime < DateTime.Today.AddDays(Main.LogDays);
					if (flag)
					{
						File.Delete(path);
					}
				}
				string path2 = text + DateTime.Now.ToString("dd-MM-yyyy") + ".txt";
				bool flag2 = !Directory.Exists(text);
				if (flag2)
				{
					Directory.CreateDirectory(text);
				}
				bool flag3 = File.Exists(path2);
				FileStream fileStream;
				if (flag3)
				{
					fileStream = new FileStream(path2, FileMode.Append, FileAccess.Write, FileShare.None);
				}
				else
				{
					fileStream = new FileStream(path2, FileMode.Create, FileAccess.Write, FileShare.None);
				}
				StreamWriter streamWriter = new StreamWriter(fileStream);
				if (!(Device == "USER"))
				{
					if (!(Device == "ERROR"))
					{
						if (Device == "ANN")
						{
							bool flag4 = DTable != null;
							if (flag4)
							{
								string[] array2 = new string[18];
								array2[0] = DateTime.Now.ToString("MMMM dd yyyy,HH:mm:ss,");
								int num = 1;
								object obj = DTable.Rows[0]["Train_No"];
								array2[num] = ((obj != null) ? obj.ToString() : null);
								array2[2] = "-";
								int num2 = 3;
								object obj2 = DTable.Rows[0]["Train_NameEng"];
								array2[num2] = ((obj2 != null) ? obj2.ToString() : null);
								array2[4] = "(";
								int num3 = 5;
								object obj3 = DTable.Rows[0]["Train_AD"];
								array2[num3] = ((obj3 != null) ? obj3.ToString() : null);
								array2[6] = ") ";
								int num4 = 7;
								object obj4 = DTable.Rows[0]["Train_Status"];
								array2[num4] = ((obj4 != null) ? obj4.ToString() : null);
								array2[8] = "  EAT:";
								int num5 = 9;
								object obj5 = DTable.Rows[0]["Exp_AT"];
								array2[num5] = ((obj5 != null) ? obj5.ToString() : null);
								array2[10] = " EDT:";
								int num6 = 11;
								object obj6 = DTable.Rows[0]["Exp_DT"];
								array2[num6] = ((obj6 != null) ? obj6.ToString() : null);
								array2[12] = " Late:";
								int num7 = 13;
								object obj7 = DTable.Rows[0]["Late"];
								array2[num7] = ((obj7 != null) ? obj7.ToString() : null);
								array2[14] = " PF:";
								int num8 = 15;
								object obj8 = DTable.Rows[0]["Sch_PF"];
								array2[num8] = ((obj8 != null) ? obj8.ToString() : null);
								array2[16] = "-";
								int num9 = 17;
								object obj9 = DTable.Rows[0]["Div_City"];
								array2[num9] = ((obj9 != null) ? obj9.ToString() : null);
								string value = string.Concat(array2);
								streamWriter.WriteLine(value);
								for (int j = 1; j < DTable.Rows.Count; j++)
								{
									string[] array3 = new string[18];
									array3[0] = " , ,";
									int num10 = 1;
									object obj10 = DTable.Rows[j]["Train_No"];
									array3[num10] = ((obj10 != null) ? obj10.ToString() : null);
									array3[2] = "-";
									int num11 = 3;
									object obj11 = DTable.Rows[j]["Train_NameEng"];
									array3[num11] = ((obj11 != null) ? obj11.ToString() : null);
									array3[4] = "(";
									int num12 = 5;
									object obj12 = DTable.Rows[j]["Train_AD"];
									array3[num12] = ((obj12 != null) ? obj12.ToString() : null);
									array3[6] = ")";
									int num13 = 7;
									object obj13 = DTable.Rows[j]["Train_Status"];
									array3[num13] = ((obj13 != null) ? obj13.ToString() : null);
									array3[8] = "  EAT:";
									int num14 = 9;
									object obj14 = DTable.Rows[j]["Exp_AT"];
									array3[num14] = ((obj14 != null) ? obj14.ToString() : null);
									array3[10] = " EDT:";
									int num15 = 11;
									object obj15 = DTable.Rows[j]["Exp_DT"];
									array3[num15] = ((obj15 != null) ? obj15.ToString() : null);
									array3[12] = " Late:";
									int num16 = 13;
									object obj16 = DTable.Rows[j]["Late"];
									array3[num16] = ((obj16 != null) ? obj16.ToString() : null);
									array3[14] = " PF:";
									int num17 = 15;
									object obj17 = DTable.Rows[j]["Sch_PF"];
									array3[num17] = ((obj17 != null) ? obj17.ToString() : null);
									array3[16] = "-";
									int num18 = 17;
									object obj18 = DTable.Rows[j]["Div_City"];
									array3[num18] = ((obj18 != null) ? obj18.ToString() : null);
									value = string.Concat(array3);
									streamWriter.WriteLine(value);
								}
							}
							else
							{
								string value = DateTime.Now.ToString("MMMM dd yyyy,HH:mm:ss,") + Remark;
								streamWriter.WriteLine(value);
							}
						}
					}
					else
					{
						string value = DateTime.Now.ToString("MMMM dd yyyy,HH:mm:ss,") + Remark;
						streamWriter.WriteLine(value);
					}
				}
				else
				{
					string value = DateTime.Now.ToString("MMMM dd yyyy,HH:mm:ss,") + Remark;
					streamWriter.WriteLine(value);
				}
				streamWriter.Flush();
				streamWriter.Close();
				fileStream.Close();
			}
			catch (Exception ex)
			{
				string text2 = ex.Message.ToString();
			}
		}

		// Token: 0x0600003E RID: 62 RVA: 0x00009EE8 File Offset: 0x000080E8
		internal void Load_Language()
		{
			throw new NotImplementedException();
		}

		// Token: 0x0600003F RID: 63 RVA: 0x00009EF0 File Offset: 0x000080F0
		internal void Load_PF()
		{
			Class_Database class_Database = new Class_Database();
			DataTable dataTable = new DataTable();
			dataTable = class_Database.Read_Database("Select * From Station_Details");
			bool flag = dataTable.Rows.Count > 0;
			if (flag)
			{
				string s = dataTable.Rows[0]["Avilable_PF"].ToString();
				Main.Avl_PF = int.Parse(s);
				Main.PF_Names = new string[Main.Avl_PF + 1];
				int i;
				for (i = 0; i < Main.Avl_PF; i++)
				{
					bool flag2 = (string)dataTable.Rows[0]["P" + (i + 1).ToString()] != null;
					if (flag2)
					{
						Main.PF_Names[i] = dataTable.Rows[0]["P" + (i + 1).ToString()].ToString();
					}
				}
				Main.PF_Names[i] = "  ";
			}
		}

		// Token: 0x06000040 RID: 64 RVA: 0x0000A004 File Offset: 0x00008204
		internal void Load_City()
		{
			Class_Database class_Database = new Class_Database();
			DataTable dataTable = new DataTable();
			dataTable = class_Database.Read_Database("Select Stn_Code From Station_Code ");
			bool flag = dataTable.Rows.Count > 0;
			if (flag)
			{
				Main.City_Names = new string[dataTable.Rows.Count];
				for (int i = 0; i < dataTable.Rows.Count; i++)
				{
					bool flag2 = (string)dataTable.Rows[i][0] != "";
					if (flag2)
					{
						Main.City_Names[i] = dataTable.Rows[i][0].ToString();
					}
				}
			}
		}

		// Token: 0x04000053 RID: 83
		public static Thread FTP_Thread;
	}
}
