using System;
using System.IO.Ports; // For Parity and StopBits enums
using System.Threading.Tasks; // For Task

namespace ipis_V2_jules.Services.DisplayBoard.Hardware.Communication // Adjusted namespace
{
    public interface ICommunicationService : IDisposable
    {
        bool IsPortOpen { get; }
        string PortName { get; }
        int BaudRate { get; }
        Parity Parity { get; }
        int DataBits { get; }
        StopBits StopBits { get; }

        bool OpenPort(); // Parameters will be passed in constructor
        void ClosePort();
        Task WriteDataAsync(byte[] data);
        Task<byte[]> ReadDataAsync(int bytesToRead, int timeoutMs); // Added timeoutMs
    }
}
