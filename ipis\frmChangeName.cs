// Decompiled with JetBrains decompiler
// Type: ipis.frmChangeName
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using ipis.My;
using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

namespace ipis
{

[DesignerGenerated]
public class frmChangeName : Form
{
  private static List<WeakReference> __ENCList = new List<WeakReference>();
  private IContainer components;
  [AccessedThroughProperty("txtNewName")]
  private TextBox _txtNewName;
  [AccessedThroughProperty("Label1")]
  private Label _Label1;
  [AccessedThroughProperty("btnOk")]
  private Button _btnOk;
  [AccessedThroughProperty("btnExit")]
  private Button _btnExit;

  [DebuggerNonUserCode]
  static frmChangeName()
  {
  }

  [DebuggerNonUserCode]
  public frmChangeName()
  {
    frmChangeName.__ENCAddToList((object) this);
    this.InitializeComponent();
  }

  [DebuggerNonUserCode]
  private static void __ENCAddToList(object value)
  {
    lock (frmChangeName.__ENCList)
    {
      if (frmChangeName.__ENCList.Count == frmChangeName.__ENCList.Capacity)
      {
        int index1 = 0;
        int num = checked (frmChangeName.__ENCList.Count - 1);
        int index2 = 0;
        while (index2 <= num)
        {
          if (frmChangeName.__ENCList[index2].IsAlive)
          {
            if (index2 != index1)
              frmChangeName.__ENCList[index1] = frmChangeName.__ENCList[index2];
            checked { ++index1; }
          }
          checked { ++index2; }
        }
        frmChangeName.__ENCList.RemoveRange(index1, checked (frmChangeName.__ENCList.Count - index1));
        frmChangeName.__ENCList.Capacity = frmChangeName.__ENCList.Count;
      }
      frmChangeName.__ENCList.Add(new WeakReference(RuntimeHelpers.GetObjectValue(value)));
    }
  }

  [DebuggerNonUserCode]
  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  [DebuggerStepThrough]
  private void InitializeComponent()
  {
    this.txtNewName = new TextBox();
    this.Label1 = new Label();
    this.btnOk = new Button();
    this.btnExit = new Button();
    this.SuspendLayout();
    this.txtNewName.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtNewName1 = this.txtNewName;
    Point point1 = new Point(168, 38);
    Point point2 = point1;
    txtNewName1.Location = point2;
    this.txtNewName.Name = "txtNewName";
    TextBox txtNewName2 = this.txtNewName;
    Size size1 = new Size(100, 22);
    Size size2 = size1;
    txtNewName2.Size = size2;
    this.txtNewName.TabIndex = 1;
    this.Label1.AutoSize = true;
    this.Label1.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label label1_1 = this.Label1;
    point1 = new Point(41, 41);
    Point point3 = point1;
    label1_1.Location = point3;
    this.Label1.Name = "Label1";
    Label label1_2 = this.Label1;
    size1 = new Size(83, 16 /*0x10*/);
    Size size3 = size1;
    label1_2.Size = size3;
    this.Label1.TabIndex = 2;
    this.Label1.Text = "New Name";
    this.btnOk.BackColor = Color.SeaShell;
    this.btnOk.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnOk1 = this.btnOk;
    point1 = new Point(49, 100);
    Point point4 = point1;
    btnOk1.Location = point4;
    this.btnOk.Name = "btnOk";
    Button btnOk2 = this.btnOk;
    size1 = new Size(123, 30);
    Size size4 = size1;
    btnOk2.Size = size4;
    this.btnOk.TabIndex = 2;
    this.btnOk.Text = "Change Name";
    this.btnOk.UseVisualStyleBackColor = false;
    this.btnExit.BackColor = Color.SeaShell;
    this.btnExit.DialogResult = DialogResult.Cancel;
    this.btnExit.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnExit1 = this.btnExit;
    point1 = new Point(204, 100);
    Point point5 = point1;
    btnExit1.Location = point5;
    this.btnExit.Name = "btnExit";
    Button btnExit2 = this.btnExit;
    size1 = new Size(64 /*0x40*/, 30);
    Size size5 = size1;
    btnExit2.Size = size5;
    this.btnExit.TabIndex = 3;
    this.btnExit.Text = "Exit";
    this.btnExit.UseVisualStyleBackColor = false;
    this.AcceptButton = (IButtonControl) this.btnOk;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.BackColor = Color.PowderBlue;
    this.CancelButton = (IButtonControl) this.btnExit;
    size1 = new Size(308, 159);
    this.ClientSize = size1;
    this.Controls.Add((Control) this.btnExit);
    this.Controls.Add((Control) this.btnOk);
    this.Controls.Add((Control) this.txtNewName);
    this.Controls.Add((Control) this.Label1);
    this.FormBorderStyle = FormBorderStyle.FixedToolWindow;
    this.Name = "frmChangeName";
    this.Text = "Change Name";
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  internal virtual TextBox txtNewName
  {
    [DebuggerNonUserCode] get { return this._txtNewName; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtNewName = value;
    }
  }

  internal virtual Label Label1
  {
    [DebuggerNonUserCode] get { return this._Label1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Label1 = value; }
  }

  internal virtual Button btnOk
  {
    [DebuggerNonUserCode] get { return this._btnOk; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnOk_Click);
      if (this._btnOk != null)
        this._btnOk.Click -= eventHandler;
      this._btnOk = value;
      if (this._btnOk == null)
        return;
      this._btnOk.Click += eventHandler;
    }
  }

  internal virtual Button btnExit
  {
    [DebuggerNonUserCode] get { return this._btnExit; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnExit_Click);
      if (this._btnExit != null)
        this._btnExit.Click -= eventHandler;
      this._btnExit = value;
      if (this._btnExit == null)
        return;
      this._btnExit.Click += eventHandler;
    }
  }

  private void btnOk_Click(object sender, EventArgs e)
  {
    bool result = false;
    try
    {
      int index1 = 0;
      while (index1 < (int) frmMainFormIPIS.user_cnt.cnt)
      {
        if (Operators.CompareString(this.txtNewName.Text, frmMainFormIPIS.user_details[index1].user_name, false) == 0)
        {
          int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Name Already Exists, Please Select another name", "Msg Box", 0, 0, 0);
          return;
        }
        checked { ++index1; }
      }
      int index2 = 0;
      while (index2 < (int) frmMainFormIPIS.user_cnt.cnt)
      {
        if (Operators.CompareString(frmMainFormIPIS.user_details[index2].user_name, frmChangeUserDetails.user_name, false) == 0)
        {
          network_db_read.set_user_name(this.txtNewName.Text, frmChangeUserDetails.user_name, ref result);
          if (result)
          {
            int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "name changed successfully", "Msg Box", 0, 0, 0);
            frmMainFormIPIS.user_details[index2].user_name = this.txtNewName.Text;
            MyProject.Forms.frmChangeUserDetails.cmbUsers.Text = this.txtNewName.Text;
            this.txtNewName.Text = string.Empty;
            this.Close();
            return;
          }
        }
        checked { ++index2; }
      }
      int num1 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "No name Exists, Please retype the name", "Msg Box", 0, 0, 0);
      this.txtNewName.Text = string.Empty;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    this.Close();
  }

  private void btnExit_Click(object sender, EventArgs e)
  {
    this.txtNewName.Text = string.Empty;
    this.Close();
  }
}

}