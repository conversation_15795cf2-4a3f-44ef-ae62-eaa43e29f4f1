﻿// Decompiled with JetBrains decompiler
// Type: ipis.My.MyComputer
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using Microsoft.VisualBasic.Devices;
using System.CodeDom.Compiler;
using System.ComponentModel;
using System.Diagnostics;

namespace ipis.My
{

[GeneratedCode("MyTemplate", "********")]
[EditorBrowsable(EditorBrowsableState.Never)]
internal class MyComputer : Computer
{
  [DebuggerHidden]
  [EditorBrowsable(EditorBrowsableState.Never)]
  public MyComputer()
  {
  }
}
}
