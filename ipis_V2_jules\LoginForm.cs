using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using ipis_V2_jules.Data; // For DatabaseHelper

namespace ipis_V2_jules
{
    public partial class LoginForm : Form
    {
        private Label lblUsername;
        private TextBox txtUsername;
        private Label lblPassword;
        private TextBox txtPassword;
        private Button btnLogin;
        private Label lblStatus;

        private DatabaseHelper _dbHelper;

        public LoginForm()
        {
            InitializeComponent();
            // Initialize DatabaseHelper - assuming the database file is in the application's root directory
            // For a real application, consider placing the DB in a user data folder.
            _dbHelper = new DatabaseHelper("ipis_v2.sqlite");

            // Initialize the database (creates schema if not exists)
            // This is good for development, but for production, you might handle this differently.
            try
            {
                _dbHelper.InitializeDatabase();
            }
            catch (Exception ex)
            {
                lblStatus.Text = $"Database initialization error: {ex.Message}";
                // Potentially disable login button if <PERSON> is not ready
                btnLogin.Enabled = false;
            }
        }

        private void btnLogin_Click(object sender, EventArgs e)
        {
            string username = txtUsername.Text.Trim();
            string password = txtPassword.Text; // Password will not be trimmed

            if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
            {
                lblStatus.Text = "Username and password cannot be empty.";
                return;
            }

            try
            {
                string query = "SELECT Username, PasswordHash FROM Users WHERE Username = @Username";
                var parameters = new Dictionary<string, object>
                {
                    { "@Username", username }
                };

                var result = _dbHelper.ExecuteQuery(query, parameters);

                if (result.Count > 0)
                {
                    // User found
                    var userRecord = result[0]; // Get the first (and should be only) user
                    string storedPasswordHash = userRecord["PasswordHash"]?.ToString();

                    if (storedPasswordHash != null && storedPasswordHash.StartsWith("NEEDS_RESET_"))
                    {
                        lblStatus.ForeColor = Color.OrangeRed;
                        lblStatus.Text = "Password needs reset. Please contact admin.";
                    }
                    else
                    {
                        // For this task, we are not comparing actual password hashes.
                        // If a user is found and password is not a placeholder, consider it a success.
                        lblStatus.ForeColor = Color.Green;
                        lblStatus.Text = "Login successful (placeholder - password not actually checked)";

                        // Optionally, open the main form and close this one
                        // Form1 mainForm = new Form1();
                        // mainForm.Show();
                        // this.Hide(); // Or this.Close(); if LoginForm is not needed anymore
                    }
                }
                else
                {
                    // No user found
                    lblStatus.ForeColor = Color.Red;
                    lblStatus.Text = "Invalid username.";
                }
            }
            catch (Exception ex)
            {
                lblStatus.ForeColor = Color.Red;
                lblStatus.Text = $"Login error: {ex.Message}";
            }
        }
    }
}
