using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using IPIS.Models;
using IPIS.Services;

namespace IPIS.Utils
{
    public static class LanguageManager
    {
        private static LanguageService _languageService;
        private static List<Language> _cachedLanguages;
        private static DateTime _lastCacheUpdate = DateTime.MinValue;
        private static readonly TimeSpan _cacheExpiration = TimeSpan.FromMinutes(5);

        public static void Initialize(LanguageService languageService)
        {
            _languageService = languageService;
        }

        public static async Task<List<Language>> GetActiveLanguagesAsync()
        {
            await RefreshCacheIfNeededAsync();
            return _cachedLanguages?.Where(l => l.IsActive).ToList() ?? new List<Language>();
        }

        public static async Task<List<Language>> GetAllLanguagesAsync()
        {
            await RefreshCacheIfNeededAsync();
            return _cachedLanguages ?? new List<Language>();
        }

        public static async Task<Language> GetDefaultLanguageAsync()
        {
            await RefreshCacheIfNeededAsync();
            return _cachedLanguages?.FirstOrDefault(l => l.IsDefault) ?? _cachedLanguages?.FirstOrDefault();
        }

        public static async Task<Language> GetLanguageByCodeAsync(string code)
        {
            await RefreshCacheIfNeededAsync();
            return _cachedLanguages?.FirstOrDefault(l => l.Code.Equals(code, StringComparison.OrdinalIgnoreCase));
        }

        public static async Task<Language> GetLanguageByIdAsync(int id)
        {
            await RefreshCacheIfNeededAsync();
            return _cachedLanguages?.FirstOrDefault(l => l.Id == id);
        }

        public static async Task<bool> IsLanguageActiveAsync(int languageId)
        {
            var language = await GetLanguageByIdAsync(languageId);
            return language?.IsActive ?? false;
        }

        public static async Task<bool> IsLanguageActiveAsync(string languageCode)
        {
            var language = await GetLanguageByCodeAsync(languageCode);
            return language?.IsActive ?? false;
        }

        public static async Task<string> GetWaveFolderPathAsync(int languageId)
        {
            var language = await GetLanguageByIdAsync(languageId);
            return language?.WaveFolderPath ?? string.Empty;
        }

        public static async Task<string> GetWaveFolderPathAsync(string languageCode)
        {
            var language = await GetLanguageByCodeAsync(languageCode);
            return language?.WaveFolderPath ?? string.Empty;
        }

        public static async Task<bool> ValidateWaveFolderAsync(int languageId)
        {
            var waveFolder = await GetWaveFolderPathAsync(languageId);
            return !string.IsNullOrEmpty(waveFolder) && Directory.Exists(waveFolder);
        }

        public static async Task<bool> ValidateWaveFolderAsync(string languageCode)
        {
            var waveFolder = await GetWaveFolderPathAsync(languageCode);
            return !string.IsNullOrEmpty(waveFolder) && Directory.Exists(waveFolder);
        }

        public static async Task<List<string>> GetAvailableWaveFilesAsync(int languageId)
        {
            var waveFolder = await GetWaveFolderPathAsync(languageId);
            if (string.IsNullOrEmpty(waveFolder) || !Directory.Exists(waveFolder))
                return new List<string>();

            try
            {
                return Directory.GetFiles(waveFolder, "*.wav", SearchOption.AllDirectories)
                    .Select(Path.GetFileNameWithoutExtension)
                    .ToList();
            }
            catch
            {
                return new List<string>();
            }
        }

        public static async Task<List<string>> GetAvailableWaveFilesAsync(string languageCode)
        {
            var waveFolder = await GetWaveFolderPathAsync(languageCode);
            if (string.IsNullOrEmpty(waveFolder) || !Directory.Exists(waveFolder))
                return new List<string>();

            try
            {
                return Directory.GetFiles(waveFolder, "*.wav", SearchOption.AllDirectories)
                    .Select(Path.GetFileNameWithoutExtension)
                    .ToList();
            }
            catch
            {
                return new List<string>();
            }
        }

        public static async Task<string> GetFullWaveFilePathAsync(int languageId, string fileName)
        {
            var waveFolder = await GetWaveFolderPathAsync(languageId);
            if (string.IsNullOrEmpty(waveFolder))
                return string.Empty;

            var filePath = Path.Combine(waveFolder, fileName);
            return File.Exists(filePath) ? filePath : string.Empty;
        }

        public static async Task<string> GetFullWaveFilePathAsync(string languageCode, string fileName)
        {
            var waveFolder = await GetWaveFolderPathAsync(languageCode);
            if (string.IsNullOrEmpty(waveFolder))
                return string.Empty;

            var filePath = Path.Combine(waveFolder, fileName);
            return File.Exists(filePath) ? filePath : string.Empty;
        }

        public static async Task<List<Language>> GetLanguagesForAnnouncementAsync()
        {
            var activeLanguages = await GetActiveLanguagesAsync();
            var validLanguages = new List<Language>();

            foreach (var language in activeLanguages)
            {
                if (await ValidateWaveFolderAsync(language.Id))
                {
                    validLanguages.Add(language);
                }
            }

            return validLanguages;
        }

        public static async Task<Dictionary<string, List<string>>> GetLanguageWaveFilesMapAsync()
        {
            var result = new Dictionary<string, List<string>>();
            var activeLanguages = await GetActiveLanguagesAsync();

            foreach (var language in activeLanguages)
            {
                var waveFiles = await GetAvailableWaveFilesAsync(language.Id);
                result[language.Code] = waveFiles;
            }

            return result;
        }

        public static void ClearCache()
        {
            _cachedLanguages = null;
            _lastCacheUpdate = DateTime.MinValue;
        }

        private static async Task RefreshCacheIfNeededAsync()
        {
            if (_languageService == null)
                throw new InvalidOperationException("LanguageManager not initialized. Call Initialize() first.");

            if (_cachedLanguages == null || DateTime.Now - _lastCacheUpdate > _cacheExpiration)
            {
                _cachedLanguages = await _languageService.GetAllLanguagesAsync();
                _lastCacheUpdate = DateTime.Now;
            }
        }

        public static async Task<bool> HasValidWaveFilesAsync(int languageId)
        {
            var waveFiles = await GetAvailableWaveFilesAsync(languageId);
            return waveFiles.Count > 0;
        }

        public static async Task<bool> HasValidWaveFilesAsync(string languageCode)
        {
            var waveFiles = await GetAvailableWaveFilesAsync(languageCode);
            return waveFiles.Count > 0;
        }

        public static async Task<string> GetLanguageDisplayNameAsync(int languageId)
        {
            var language = await GetLanguageByIdAsync(languageId);
            if (language == null)
                return string.Empty;

            return string.IsNullOrEmpty(language.NativeName) ? language.Name : $"{language.Name} ({language.NativeName})";
        }

        public static async Task<string> GetLanguageDisplayNameAsync(string languageCode)
        {
            var language = await GetLanguageByCodeAsync(languageCode);
            if (language == null)
                return string.Empty;

            return string.IsNullOrEmpty(language.NativeName) ? language.Name : $"{language.Name} ({language.NativeName})";
        }
    }
} 