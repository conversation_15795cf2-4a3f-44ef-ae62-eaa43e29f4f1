using System;
using System.Collections.Generic;
using System.Diagnostics;
using IPIS.Models;

namespace IPIS.Utils
{
    /// <summary>
    /// Helper class for managing batch logging operations during system boot and data loading
    /// </summary>
    public class BatchLogger : IDisposable
    {
        private readonly List<string> _operations;
        private readonly List<string> _errors;
        private readonly Stopwatch _stopwatch;
        private readonly string _operationType;
        private readonly LogCategory _category;
        private int _successCount;
        private int _totalCount;
        private bool _disposed;

        public BatchLogger(string operationType, LogCategory category)
        {
            _operationType = operationType;
            _category = category;
            _operations = new List<string>();
            _errors = new List<string>();
            _stopwatch = Stopwatch.StartNew();
            _successCount = 0;
            _totalCount = 0;
        }

        /// <summary>
        /// Add a successful operation to the batch
        /// </summary>
        public void LogSuccess(string operation)
        {
            _operations.Add($"✓ {operation}");
            _successCount++;
            _totalCount++;
        }

        /// <summary>
        /// Add a failed operation to the batch
        /// </summary>
        public void LogFailure(string operation, string error)
        {
            _operations.Add($"✗ {operation}");
            _errors.Add($"{operation}: {error}");
            _totalCount++;
        }

        /// <summary>
        /// Add an informational step to the batch
        /// </summary>
        public void LogStep(string step)
        {
            _operations.Add($"• {step}");
        }

        /// <summary>
        /// Increment counters without adding detailed operation logs (for large datasets)
        /// </summary>
        public void IncrementSuccess()
        {
            _successCount++;
            _totalCount++;
        }

        /// <summary>
        /// Increment success with operation details
        /// </summary>
        public void IncrementSuccess(string operationDetail)
        {
            _operations.Add($"✓ {operationDetail}");
            _successCount++;
            _totalCount++;
        }

        /// <summary>
        /// Increment counters and log error without adding detailed operation logs (for large datasets)
        /// </summary>
        public void IncrementFailure(string error)
        {
            _errors.Add(error);
            _totalCount++;
        }

        /// <summary>
        /// Increment failure with operation details
        /// </summary>
        public void IncrementFailure(string operationDetail, string error)
        {
            _operations.Add($"✗ {operationDetail}");
            _errors.Add($"{operationDetail}: {error}");
            _totalCount++;
        }

        /// <summary>
        /// Get current statistics
        /// </summary>
        public (int Total, int Success, int Failures, TimeSpan Duration) GetStats()
        {
            return (_totalCount, _successCount, _totalCount - _successCount, _stopwatch.Elapsed);
        }

        /// <summary>
        /// Manually complete the batch logging (called automatically on dispose)
        /// </summary>
        public void Complete()
        {
            if (_disposed) return;

            _stopwatch.Stop();
            var failureCount = _totalCount - _successCount;

            if (_category == LogCategory.System && _operationType.Contains("Boot", StringComparison.OrdinalIgnoreCase))
            {
                Logger.LogSystemBootSummary(_operations, _stopwatch.Elapsed);
            }
            else if (_totalCount > 0)
            {
                Logger.LogDataLoadingSummary(_operationType, _totalCount, _successCount, failureCount, _stopwatch.Elapsed, _errors.Count > 0 ? _errors : null);
            }
            else
            {
                // For operations without counts (like system initialization steps)
                var summary = $"{_operationType} completed in {_stopwatch.Elapsed.TotalSeconds:F2} seconds";
                Logger.LogBatchOperation(_category, _operationType, summary, _operations);
            }

            _disposed = true;
        }

        public void Dispose()
        {
            Complete();
        }
    }

    /// <summary>
    /// Static helper methods for common batch logging scenarios
    /// </summary>
    public static class BatchLoggerExtensions
    {
        /// <summary>
        /// Create a batch logger for system boot operations
        /// </summary>
        public static BatchLogger CreateSystemBootLogger()
        {
            return new BatchLogger("System Boot", LogCategory.System);
        }

        /// <summary>
        /// Create a batch logger for database initialization
        /// </summary>
        public static BatchLogger CreateDatabaseInitLogger()
        {
            return new BatchLogger("Database Initialization", LogCategory.Database);
        }

        /// <summary>
        /// Create a batch logger for train data loading
        /// </summary>
        public static BatchLogger CreateTrainDataLoader()
        {
            return new BatchLogger("Train Data Loading", LogCategory.TrainManagement);
        }

        /// <summary>
        /// Create a batch logger for station data loading
        /// </summary>
        public static BatchLogger CreateStationDataLoader()
        {
            return new BatchLogger("Station Data Loading", LogCategory.StationManagement);
        }
    }
}
