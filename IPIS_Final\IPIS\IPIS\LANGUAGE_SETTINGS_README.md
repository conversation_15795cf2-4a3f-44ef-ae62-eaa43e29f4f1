# Language Settings Feature

## Overview

The Language Settings feature allows you to configure and manage multiple languages for the IPIS announcement system. This feature enables you to:

- Add, edit, and delete languages
- Set default languages
- Configure wave file folders for each language
- Manage language activation status
- Use languages in announcement configurations

## Features

### 1. Language Management
- **Add Language**: Create new language entries with name, code, native name, and wave folder path
- **Edit Language**: Modify existing language settings (except language code)
- **Delete Language**: Remove languages (cannot delete default language)
- **Set Default**: Designate a language as the system default
- **Toggle Status**: Activate/deactivate languages
- **Refresh**: Reload language data from database

### 2. Language Properties
- **Name**: Display name of the language (e.g., "English", "Hindi")
- **Code**: Unique language code (e.g., "EN", "HI")
- **Native Name**: Name in the native script (e.g., "हिंदी" for Hindi)
- **Wave Folder Path**: Path to the folder containing wave files for this language
- **Is Active**: Whether the language is available for use
- **Is Default**: Whether this is the default language for the system

### 3. Wave File Integration
- Automatic validation of wave folder paths
- Support for nested folder structures
- File existence validation
- Integration with existing WAVE folder structure

## Database Structure

The language settings are stored in a SQLite database table with the following structure:

```sql
CREATE TABLE Languages (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    Name TEXT NOT NULL,
    Code TEXT UNIQUE NOT NULL,
    NativeName TEXT,
    IsActive INTEGER DEFAULT 1,
    IsDefault INTEGER DEFAULT 0,
    WaveFolderPath TEXT,
    CreatedAt TEXT NOT NULL,
    UpdatedAt TEXT
);
```

## Default Languages

The system automatically creates two default languages on first run:

1. **English (EN)**
   - Name: English
   - Code: EN
   - Native Name: English
   - Wave Folder: ENGLISH
   - Is Default: true

2. **Hindi (HI)**
   - Name: Hindi
   - Code: HI
   - Native Name: हिंदी
   - Wave Folder: HINDI
   - Is Default: false

## Usage in Announcement Configurations

### 1. Getting Available Languages

```csharp
// Get all active languages with valid wave files
var availableLanguages = await AnnouncementLanguageHelper.GetAvailableLanguagesForAnnouncementAsync();

// Get language options for UI controls
var languageOptions = await AnnouncementLanguageHelper.GetLanguageOptionsAsync();
```

### 2. Language Selection in Forms

```csharp
// Populate ComboBox with language options
comboBoxLanguages.DataSource = await AnnouncementLanguageHelper.GetLanguageOptionsAsync();
comboBoxLanguages.DisplayMember = "DisplayName";
comboBoxLanguages.ValueMember = "Id";
```

### 3. Wave File Access

```csharp
// Get wave files for a specific language
var waveFiles = await AnnouncementLanguageHelper.GetWaveFilesForLanguageAsync(languageId);

// Get full path to a specific wave file
var filePath = await AnnouncementLanguageHelper.GetWaveFilePathAsync(languageId, "STD1");
```

### 4. Language Validation

```csharp
// Check if a language can be used for announcements
bool canUse = await AnnouncementLanguageHelper.CanUseLanguageForAnnouncementAsync(languageId);

// Get language display information
string info = await AnnouncementLanguageHelper.GetLanguageDisplayInfoAsync(languageId);
```

### 5. Default Language Handling

```csharp
// Get the default language
var defaultLanguage = await AnnouncementLanguageHelper.GetDefaultAnnouncementLanguageAsync();

// Suggest the best available language
var bestLanguage = await AnnouncementLanguageHelper.SuggestBestLanguageAsync();
```

## Integration Examples

### Example 1: Announcement Form with Language Selection

```csharp
public partial class AnnouncementForm : Form
{
    private async void LoadLanguages()
    {
        try
        {
            var languageOptions = await AnnouncementLanguageHelper.GetLanguageOptionsAsync();
            comboBoxLanguage.DataSource = languageOptions;
            comboBoxLanguage.DisplayMember = "DisplayName";
            comboBoxLanguage.ValueMember = "Id";
            
            // Set default language
            var defaultLanguage = await AnnouncementLanguageHelper.GetDefaultAnnouncementLanguageAsync();
            if (defaultLanguage != null)
            {
                comboBoxLanguage.SelectedValue = defaultLanguage.Id;
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error loading languages: {ex.Message}");
        }
    }
    
    private async void comboBoxLanguage_SelectedIndexChanged(object sender, EventArgs e)
    {
        if (comboBoxLanguage.SelectedValue != null)
        {
            int languageId = (int)comboBoxLanguage.SelectedValue;
            var waveFiles = await AnnouncementLanguageHelper.GetWaveFilesForLanguageAsync(languageId);
            
            // Populate wave file list
            listBoxWaveFiles.DataSource = waveFiles;
        }
    }
}
```

### Example 2: Multi-language Announcement Configuration

```csharp
public class MultiLanguageAnnouncement
{
    public int LanguageId { get; set; }
    public string Message { get; set; }
    public List<string> WaveFiles { get; set; }
    
    public async Task<bool> Validate()
    {
        return await AnnouncementLanguageHelper.CanUseLanguageForAnnouncementAsync(LanguageId);
    }
    
    public async Task<List<string>> GetAvailableWaveFiles()
    {
        return await AnnouncementLanguageHelper.GetWaveFilesForLanguageAsync(LanguageId);
    }
}
```

### Example 3: Language Statistics and Reporting

```csharp
public async Task<string> GenerateLanguageReport()
{
    var stats = await AnnouncementLanguageHelper.GetLanguageStatisticsAsync();
    var summary = await AnnouncementLanguageHelper.GetLanguageSummaryAsync();
    
    return $@"
Language Statistics Report:
==========================
Total Languages: {stats.TotalLanguages}
Active Languages: {stats.ActiveLanguages}
Languages with Wave Files: {stats.LanguagesWithWaveFiles}
Default Language: {stats.DefaultLanguageName}

Summary:
========
Available for Announcements: {summary.TotalLanguages}
Default Language: {summary.DefaultLanguage}
Languages with Valid Wave Files: {summary.LanguagesWithWaveFiles}
";
}
```

## File Structure

```
IPIS/
├── Models/
│   └── Language.cs                    # Language model
├── Repositories/
│   ├── Interfaces/
│   │   └── ILanguageRepository.cs     # Language repository interface
│   └── SQLiteLanguageRepository.cs    # SQLite implementation
├── Services/
│   └── LanguageService.cs             # Language business logic
├── Utils/
│   ├── LanguageManager.cs             # Language management utilities
│   └── AnnouncementLanguageHelper.cs  # Announcement-specific helpers
└── Forms/
    └── Settings/
        ├── SystemSettingsForm.cs      # Main settings form with language tab
        └── LanguageForm.cs            # Add/Edit language form
```

## Best Practices

1. **Always validate languages before use**: Check if the language is active and has valid wave files
2. **Use the default language as fallback**: When no specific language is selected, use the system default
3. **Cache language data**: Use the LanguageManager for efficient language data access
4. **Handle missing wave files gracefully**: Provide fallback options when wave files are not available
5. **Validate wave folder paths**: Ensure wave folders exist and contain valid files
6. **Use async/await**: All language operations are asynchronous for better performance

## Error Handling

The system includes comprehensive error handling for:

- Invalid language codes
- Duplicate language entries
- Missing wave folders
- Database connection issues
- File system access problems

## Future Enhancements

Potential future improvements include:

1. **Language-specific settings**: Per-language configuration options
2. **Wave file management**: Built-in wave file organization tools
3. **Language templates**: Pre-configured language setups
4. **Import/Export**: Language configuration backup and restore
5. **Multi-language announcements**: Support for simultaneous multi-language announcements
6. **Language-specific formatting**: Custom formatting rules per language

## Support

For issues or questions regarding the Language Settings feature, please refer to the main project documentation or contact the development team. 