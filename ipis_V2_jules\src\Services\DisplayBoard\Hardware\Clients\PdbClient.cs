using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ipis_V2_jules.DisplayFormatters; // For PdbDataFormatter, FormattedDisplayData
using ipis_V2_jules.Services.DisplayBoard.Hardware.Protocols; // For DisplayPacketBuilder
using ipis_V2_jules.Services.DisplayBoard.Hardware.Communication; // For ICommunicationService
using ipis_V2_jules.Models; // For DisplayBoardConfig
using ipis_V2_jules.ApiClients; // For TrainDataErail

// Ensure PdbDataFormatter is in the correct namespace.
// Assuming PdbDataFormatter is in ipis_V2_jules.Services.DisplayBoard.DisplayFormatters (from Turn 51-53)

namespace ipis_V2_jules.Services.DisplayBoard.Hardware.Clients
{
    public class PdbClient : IBoardClient
    {
        private readonly ICommunicationService _communicationService;
        private readonly PdbDataFormatter _dataFormatter; // Specific formatter
        private readonly DisplayBoardConfig _boardConfig;

        public BoardStatus Status { get; private set; }

        // Placeholder command codes for PDB. These might be same as MLDB or different.
        // For this iteration, assuming they might use similar line-based commands.
        private const byte CMD_PDB_DISPLAY_LINE_1 = 0x51; // Placeholder, e.g. 'Q'
        private const byte CMD_PDB_DISPLAY_LINE_2 = 0x52; // Placeholder, e.g. 'R'
        private const byte CMD_PDB_DISPLAY_LINE_3 = 0x53; // Placeholder, e.g. 'S'


        public PdbClient(ICommunicationService communicationService,
                         PdbDataFormatter dataFormatter,
                         DisplayBoardConfig boardConfig)
        {
            _communicationService = communicationService ?? throw new ArgumentNullException(nameof(communicationService));
            _dataFormatter = dataFormatter ?? throw new ArgumentNullException(nameof(dataFormatter));
            _boardConfig = boardConfig ?? throw new ArgumentNullException(nameof(boardConfig));
            Status = new BoardStatus { IsLinkOk = false, StatusMessage = "Initialized", FirmwareVersion = "N/A" };
        }

        private Dictionary<string, string> GetBoardConfigAsDictionary()
        {
            return _boardConfig.ToDictionary();
        }

        private async Task<bool> SendSingleLineDataAsync(byte[] lineBitmapData, byte lineCommandCode)
        {
            if (lineBitmapData == null || lineBitmapData.Length == 0)
            {
                Console.WriteLine($"PDB Client ({_boardConfig.BoardName}): No bitmap data for line command {lineCommandCode}. Sending empty payload if protocol requires.");
                lineBitmapData = Array.Empty<byte>();
            }

            // Using BuildMldbBitmapDisplayPacket as PDBs are also bitmap based and might share packet structure.
            // If PDB has a distinct packet structure, a new builder method would be needed.
            byte[] packet = DisplayPacketBuilder.BuildMldbBitmapDisplayPacket(
                _boardConfig.BoardId,
                lineCommandCode,
                lineBitmapData
            );

            try
            {
                await _communicationService.WriteDataAsync(packet);
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"PDB Client ({_boardConfig.BoardName}) Error: Failed to send line data (Cmd:{lineCommandCode}). Exception: {ex.Message}");
                Status = new BoardStatus { IsLinkOk = false, StatusMessage = $"Send line (Cmd:{lineCommandCode}) failed: {ex.Message}", FirmwareVersion = Status.FirmwareVersion };
                return false;
            }
        }

        public async Task<bool> SendMessageAsync(FormattedDisplayData data, byte boardAddress, byte subAddress, byte serialNo)
        {
            if (boardAddress != _boardConfig.BoardId)
            {
                Console.WriteLine($"PDB Client ({_boardConfig.BoardName}) Error: Mismatched boardAddress ({boardAddress}). Expected {_boardConfig.BoardId}.");
                Status = new BoardStatus { IsLinkOk = false, StatusMessage = "Config error: Board ID mismatch.", FirmwareVersion = Status.FirmwareVersion };
                return false;
            }

            bool overallSuccess = true;
            if (data.Line1 != null)
            {
                Console.WriteLine($"PDB Client ({_boardConfig.BoardName}): Sending Line 1 data.");
                if (!await SendSingleLineDataAsync(data.Line1, CMD_PDB_DISPLAY_LINE_1)) overallSuccess = false;
            }
            if (overallSuccess && data.Line2 != null)
            {
                Console.WriteLine($"PDB Client ({_boardConfig.BoardName}): Sending Line 2 data.");
                if (!await SendSingleLineDataAsync(data.Line2, CMD_PDB_DISPLAY_LINE_2)) overallSuccess = false;
            }
            if (overallSuccess && data.Line3 != null)
            {
                Console.WriteLine($"PDB Client ({_boardConfig.BoardName}): Sending Line 3 data.");
                if (!await SendSingleLineDataAsync(data.Line3, CMD_PDB_DISPLAY_LINE_3)) overallSuccess = false;
            }

            if (overallSuccess)
            {
                Status = new BoardStatus { IsLinkOk = true, StatusMessage = "Message sent successfully.", FirmwareVersion = Status.FirmwareVersion };
            }
            return overallSuccess;
        }

        public async Task<bool> SendMessageAsync(string message)
        {
            if (string.IsNullOrEmpty(message)) return false;
            Console.WriteLine($"PDB Client ({_boardConfig.BoardName}): Formatting and sending message: \"{message.Substring(0, Math.Min(message.Length, 20))}...\"");
            FormattedDisplayData formattedData = _dataFormatter.FormatMessage(message, GetBoardConfigAsDictionary());
            return await SendMessageAsync(formattedData, _boardConfig.BoardId, 0, 0);
        }

        public async Task<bool> UpdateTrainDisplayAsync(TrainDataErail trainData, Dictionary<string, string> platformInfo)
        {
            if (trainData == null) return false;
            Console.WriteLine($"PDB Client ({_boardConfig.BoardName}): Formatting and sending train data for {trainData.TrainNo}");
            FormattedDisplayData formattedData = _dataFormatter.FormatTrainData(trainData, platformInfo, GetBoardConfigAsDictionary());
            return await SendMessageAsync(formattedData, _boardConfig.BoardId, 0, 0);
        }

        public async Task<bool> ClearDisplayAsync() => await ClearDisplayAsync(_boardConfig.BoardId, 0, 0);

        public async Task<bool> ClearDisplayAsync(byte boardAddress, byte subAddress, byte serialNo)
        {
            if (boardAddress != _boardConfig.BoardId)
            {
                 Console.WriteLine($"PDB Client ({_boardConfig.BoardName}) Error: Mismatched boardAddress ({boardAddress}) for clear.");
                 Status = new BoardStatus { IsLinkOk = false, StatusMessage = "Config error: Board ID mismatch for clear.", FirmwareVersion = Status.FirmwareVersion };
                 return false;
            }
            Console.WriteLine($"PDB Client ({_boardConfig.BoardName}): Clearing display.");
            // Assuming PDBs can use the same clear command structure as MLDBs.
            // If not, a specific BuildPdbClearScreenPacket would be needed.
            byte[] packet = DisplayPacketBuilder.BuildMldbClearScreenPacket(_boardConfig.BoardId);
            try
            {
                await _communicationService.WriteDataAsync(packet);
                Status = new BoardStatus { IsLinkOk = true, StatusMessage = "Display cleared.", FirmwareVersion = Status.FirmwareVersion };
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"PDB Client ({_boardConfig.BoardName}) Error: Failed to clear display. Exception: {ex.Message}");
                Status = new BoardStatus { IsLinkOk = false, StatusMessage = $"Clear failed: {ex.Message}", FirmwareVersion = Status.FirmwareVersion };
                return false;
            }
        }

        public async Task<BoardStatus> CheckLinkAsync() => await CheckLinkAsync(_boardConfig.BoardId, 0, 0);

        public async Task<BoardStatus> CheckLinkAsync(byte boardAddress, byte subAddress, byte serialNo)
        {
            if (boardAddress != _boardConfig.BoardId)
            {
                 Console.WriteLine($"PDB Client ({_boardConfig.BoardName}) Error: Mismatched boardAddress ({boardAddress}) for link check.");
                 Status = new BoardStatus { IsLinkOk = false, StatusMessage = "Config error: Board ID mismatch for link check.", FirmwareVersion = Status.FirmwareVersion };
                 return Status;
            }
            Console.WriteLine($"PDB Client ({_boardConfig.BoardName}): Checking link.");
             // Using generic BuildLinkCheckPacket. If PDB needs a specific one, it should be added.
            byte[] packet = DisplayPacketBuilder.BuildLinkCheckPacket(_boardConfig.BoardId);
            try
            {
                await _communicationService.WriteDataAsync(packet);
                // TODO: Implement response reading.
                Status = new BoardStatus { IsLinkOk = true, StatusMessage = "Link check sent (response check not implemented).", FirmwareVersion = Status.FirmwareVersion };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"PDB Client ({_boardConfig.BoardName}) Error: Failed to send link check. Exception: {ex.Message}");
                Status = new BoardStatus { IsLinkOk = false, StatusMessage = $"Link check send failed: {ex.Message}", FirmwareVersion = Status.FirmwareVersion };
            }
            return Status;
        }

        // Stub implementations
        public async Task<bool> SetConfigurationAsync(byte boardAddress, byte subAddress, byte serialNo, byte[] configData)
        {
            Console.WriteLine($"PDB Client ({_boardConfig.BoardName}): SetConfigurationAsync - NOT IMPLEMENTED.");
            await Task.CompletedTask;
            Status = new BoardStatus { IsLinkOk = Status.IsLinkOk, StatusMessage = "SetConfiguration not implemented.", FirmwareVersion = Status.FirmwareVersion };
            return false;
        }

        public async Task<byte[]> GetConfigurationAsync(byte boardAddress, byte subAddress, byte serialNo)
        {
            Console.WriteLine($"PDB Client ({_boardConfig.BoardName}): GetConfigurationAsync - NOT IMPLEMENTED.");
            await Task.CompletedTask;
            Status = new BoardStatus { IsLinkOk = Status.IsLinkOk, StatusMessage = "GetConfiguration not implemented.", FirmwareVersion = Status.FirmwareVersion };
            return Array.Empty<byte>();
        }

        public async Task<bool> ResetBoardAsync(byte boardAddress, byte subAddress, byte serialNo)
        {
            Console.WriteLine($"PDB Client ({_boardConfig.BoardName}): ResetBoardAsync - NOT IMPLEMENTED.");
            await Task.CompletedTask;
            Status = new BoardStatus { IsLinkOk = Status.IsLinkOk, StatusMessage = "ResetBoard not implemented.", FirmwareVersion = Status.FirmwareVersion };
            return false;
        }
    }
}
