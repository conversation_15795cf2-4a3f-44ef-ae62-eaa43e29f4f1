using System.Collections.Generic;
using ipis_V2_jules.ApiClients; // For TrainDataErail
// Assuming BoardConfig and PlatformDisplayInfo might be defined in a Models directory later.
// For now, using Dictionary<string, string> as placeholders for their structure.
// using ipis_V2_jules.Models;

namespace ipis_V2_jules.DisplayFormatters
{
    public interface IDisplayDataFormatter
    {
        /// <summary>
        /// Formats train information for display on a specific board type.
        /// </summary>
        /// <param name="trainInfo">The train data retrieved from an API (e.g., ErailApiClient.TrainDataErail).</param>
        /// <param name="platformInfo">Information about the platform (e.g., Platform No, expected arrival/departure for this train at this station).</param>
        /// <param name="boardConfig">Configuration specific to the target display board (e.g., resolution, lines, character set).</param>
        /// <returns>FormattedDisplayData object containing byte arrays for display lines and any additional header bytes.</returns>
        FormattedDisplayData FormatTrainData(TrainDataErail trainInfo, Dictionary<string, string> platformInfo, Dictionary<string, string> boardConfig);

        /// <summary>
        /// Formats a generic message for display on a specific board type.
        /// </summary>
        /// <param name="message">The text message to display.</param>
        /// <param name="boardConfig">Configuration specific to the target display board.</param>
        /// <returns>FormattedDisplayData object containing byte arrays for display lines and any additional header bytes.</returns>
        FormattedDisplayData FormatMessage(string message, Dictionary<string, string> boardConfig);

        // Potentially add other methods for specific data types or display modes
        // e.g., FormattedDisplayData FormatCoachComposition(CoachCompositionInfo coachInfo, Dictionary<string, string> boardConfig);
    }
}
