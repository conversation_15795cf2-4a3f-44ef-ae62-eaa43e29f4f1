// Decompiled with JetBrains decompiler
// Type: ipis.frmTrainDetails
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using ipis.My;
using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

namespace ipis
{

[DesignerGenerated]
public class frmTrainDetails : Form
{
  private static List<WeakReference> __ENCList = new List<WeakReference>();
  private IContainer components;
  [AccessedThroughProperty("datTo")]
  private DateTimePicker _datTo;
  [AccessedThroughProperty("btnRemove")]
  private Button _btnRemove;
  [AccessedThroughProperty("Label2")]
  private Label _Label2;
  [AccessedThroughProperty("datFrom")]
  private DateTimePicker _datFrom;
  [AccessedThroughProperty("btnAdd")]
  private Button _btnAdd;
  [AccessedThroughProperty("lstboxSpecificDates")]
  private ListBox _lstboxSpecificDates;
  [AccessedThroughProperty("Label1")]
  private Label _Label1;
  [AccessedThroughProperty("radSpecificDates")]
  private RadioButton _radSpecificDates;
  [AccessedThroughProperty("radPeriod")]
  private RadioButton _radPeriod;
  [AccessedThroughProperty("datSpecificDate")]
  private DateTimePicker _datSpecificDate;
  [AccessedThroughProperty("radSpecificDaysWeek")]
  private RadioButton _radSpecificDaysWeek;
  [AccessedThroughProperty("radDaily")]
  private RadioButton _radDaily;
  [AccessedThroughProperty("btnExit")]
  private Button _btnExit;
  [AccessedThroughProperty("btnOk")]
  private Button _btnOk;
  [AccessedThroughProperty("chklstWeekDays")]
  private CheckedListBox _chklstWeekDays;
  public frmTrainConfig myCaller;
  private byte found;
  public static string[] Weekdaysarray = new string[8];
  public static DateTime[] ftdate = new DateTime[3];
  public static string[] specificdate = new string[11];
  public static int ldaily = 0;
  public static int lspecificdays = 0;
  public static int lperiod = 0;
  public static int lspecificdates = 0;
  public static DateTime[] spdate = new DateTime[11];
  private int i;

  public frmTrainDetails()
  {
    this.Load += new EventHandler(this.TrainTypeConfig_Load);
    frmTrainDetails.__ENCAddToList((object) this);
    this.found = (byte) 0;
    this.InitializeComponent();
  }

  [DebuggerNonUserCode]
  private static void __ENCAddToList(object value)
  {
    lock (frmTrainDetails.__ENCList)
    {
      if (frmTrainDetails.__ENCList.Count == frmTrainDetails.__ENCList.Capacity)
      {
        int index1 = 0;
        int num = checked (frmTrainDetails.__ENCList.Count - 1);
        int index2 = 0;
        while (index2 <= num)
        {
          if (frmTrainDetails.__ENCList[index2].IsAlive)
          {
            if (index2 != index1)
              frmTrainDetails.__ENCList[index1] = frmTrainDetails.__ENCList[index2];
            checked { ++index1; }
          }
          checked { ++index2; }
        }
        frmTrainDetails.__ENCList.RemoveRange(index1, checked (frmTrainDetails.__ENCList.Count - index1));
        frmTrainDetails.__ENCList.Capacity = frmTrainDetails.__ENCList.Count;
      }
      frmTrainDetails.__ENCList.Add(new WeakReference(RuntimeHelpers.GetObjectValue(value)));
    }
  }

  [DebuggerNonUserCode]
  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  [DebuggerStepThrough]
  private void InitializeComponent()
  {
    this.datTo = new DateTimePicker();
    this.btnRemove = new Button();
    this.Label2 = new Label();
    this.datFrom = new DateTimePicker();
    this.btnAdd = new Button();
    this.lstboxSpecificDates = new ListBox();
    this.Label1 = new Label();
    this.radSpecificDates = new RadioButton();
    this.radPeriod = new RadioButton();
    this.datSpecificDate = new DateTimePicker();
    this.radSpecificDaysWeek = new RadioButton();
    this.radDaily = new RadioButton();
    this.btnExit = new Button();
    this.btnOk = new Button();
    this.chklstWeekDays = new CheckedListBox();
    this.SuspendLayout();
    this.datTo.CustomFormat = "dd-MM-yyyy";
    this.datTo.Enabled = false;
    this.datTo.Format = DateTimePickerFormat.Custom;
    DateTimePicker datTo1 = this.datTo;
    Point point1 = new Point(701, 28);
    Point point2 = point1;
    datTo1.Location = point2;
    DateTimePicker datTo2 = this.datTo;
    Padding padding1 = new Padding(4);
    Padding padding2 = padding1;
    datTo2.Margin = padding2;
    this.datTo.Name = "datTo";
    DateTimePicker datTo3 = this.datTo;
    Size size1 = new Size(129, 22);
    Size size2 = size1;
    datTo3.Size = size2;
    this.datTo.TabIndex = 7;
    this.btnRemove.BackColor = SystemColors.ButtonFace;
    this.btnRemove.Enabled = false;
    this.btnRemove.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnRemove1 = this.btnRemove;
    point1 = new Point(464, 340);
    Point point3 = point1;
    btnRemove1.Location = point3;
    Button btnRemove2 = this.btnRemove;
    padding1 = new Padding(4);
    Padding padding3 = padding1;
    btnRemove2.Margin = padding3;
    this.btnRemove.Name = "btnRemove";
    Button btnRemove3 = this.btnRemove;
    size1 = new Size(75, 31 /*0x1F*/);
    Size size3 = size1;
    btnRemove3.Size = size3;
    this.btnRemove.TabIndex = 11;
    this.btnRemove.Text = "Remove";
    this.btnRemove.UseVisualStyleBackColor = false;
    this.Label2.AutoSize = true;
    this.Label2.Enabled = false;
    Label label2_1 = this.Label2;
    point1 = new Point(662, 33);
    Point point4 = point1;
    label2_1.Location = point4;
    Label label2_2 = this.Label2;
    padding1 = new Padding(4, 0, 4, 0);
    Padding padding4 = padding1;
    label2_2.Margin = padding4;
    this.Label2.Name = "Label2";
    Label label2_3 = this.Label2;
    size1 = new Size(27, 16 /*0x10*/);
    Size size4 = size1;
    label2_3.Size = size4;
    this.Label2.TabIndex = 31 /*0x1F*/;
    this.Label2.Text = "To";
    this.datFrom.CustomFormat = "dd-MM-yyyy";
    this.datFrom.Enabled = false;
    this.datFrom.Format = DateTimePickerFormat.Custom;
    DateTimePicker datFrom1 = this.datFrom;
    point1 = new Point(498, 30);
    Point point5 = point1;
    datFrom1.Location = point5;
    DateTimePicker datFrom2 = this.datFrom;
    padding1 = new Padding(4);
    Padding padding5 = padding1;
    datFrom2.Margin = padding5;
    this.datFrom.Name = "datFrom";
    DateTimePicker datFrom3 = this.datFrom;
    size1 = new Size((int) sbyte.MaxValue, 22);
    Size size5 = size1;
    datFrom3.Size = size5;
    this.datFrom.TabIndex = 6;
    this.btnAdd.BackColor = SystemColors.ButtonFace;
    this.btnAdd.Enabled = false;
    this.btnAdd.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnAdd1 = this.btnAdd;
    point1 = new Point(391, 340);
    Point point6 = point1;
    btnAdd1.Location = point6;
    Button btnAdd2 = this.btnAdd;
    padding1 = new Padding(4);
    Padding padding6 = padding1;
    btnAdd2.Margin = padding6;
    this.btnAdd.Name = "btnAdd";
    Button btnAdd3 = this.btnAdd;
    size1 = new Size(57, 31 /*0x1F*/);
    Size size6 = size1;
    btnAdd3.Size = size6;
    this.btnAdd.TabIndex = 10;
    this.btnAdd.Text = "Add";
    this.btnAdd.UseVisualStyleBackColor = false;
    this.lstboxSpecificDates.Enabled = false;
    this.lstboxSpecificDates.FormattingEnabled = true;
    this.lstboxSpecificDates.ItemHeight = 16 /*0x10*/;
    ListBox lstboxSpecificDates1 = this.lstboxSpecificDates;
    point1 = new Point(341, 150);
    Point point7 = point1;
    lstboxSpecificDates1.Location = point7;
    ListBox lstboxSpecificDates2 = this.lstboxSpecificDates;
    padding1 = new Padding(4);
    Padding padding7 = padding1;
    lstboxSpecificDates2.Margin = padding7;
    this.lstboxSpecificDates.Name = "lstboxSpecificDates";
    ListBox lstboxSpecificDates3 = this.lstboxSpecificDates;
    size1 = new Size(178, 116);
    Size size7 = size1;
    lstboxSpecificDates3.Size = size7;
    this.lstboxSpecificDates.TabIndex = 9;
    this.Label1.AutoSize = true;
    this.Label1.Enabled = false;
    Label label1_1 = this.Label1;
    point1 = new Point(444, 34);
    Point point8 = point1;
    label1_1.Location = point8;
    Label label1_2 = this.Label1;
    padding1 = new Padding(4, 0, 4, 0);
    Padding padding8 = padding1;
    label1_2.Margin = padding8;
    this.Label1.Name = "Label1";
    Label label1_3 = this.Label1;
    size1 = new Size(43, 16 /*0x10*/);
    Size size8 = size1;
    label1_3.Size = size8;
    this.Label1.TabIndex = 30;
    this.Label1.Text = "From";
    this.radSpecificDates.AutoSize = true;
    this.radSpecificDates.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    RadioButton radSpecificDates1 = this.radSpecificDates;
    point1 = new Point(341, 95);
    Point point9 = point1;
    radSpecificDates1.Location = point9;
    RadioButton radSpecificDates2 = this.radSpecificDates;
    padding1 = new Padding(4);
    Padding padding9 = padding1;
    radSpecificDates2.Margin = padding9;
    this.radSpecificDates.Name = "radSpecificDates";
    RadioButton radSpecificDates3 = this.radSpecificDates;
    size1 = new Size((int) sbyte.MaxValue, 20);
    Size size9 = size1;
    radSpecificDates3.Size = size9;
    this.radSpecificDates.TabIndex = 4;
    this.radSpecificDates.TabStop = true;
    this.radSpecificDates.Text = "Specific Dates";
    this.radSpecificDates.UseVisualStyleBackColor = true;
    this.radPeriod.AutoSize = true;
    RadioButton radPeriod1 = this.radPeriod;
    point1 = new Point(341, 33);
    Point point10 = point1;
    radPeriod1.Location = point10;
    RadioButton radPeriod2 = this.radPeriod;
    padding1 = new Padding(4);
    Padding padding10 = padding1;
    radPeriod2.Margin = padding10;
    this.radPeriod.Name = "radPeriod";
    RadioButton radPeriod3 = this.radPeriod;
    size1 = new Size(72, 20);
    Size size10 = size1;
    radPeriod3.Size = size10;
    this.radPeriod.TabIndex = 3;
    this.radPeriod.TabStop = true;
    this.radPeriod.Text = "Period";
    this.radPeriod.UseVisualStyleBackColor = true;
    this.datSpecificDate.CustomFormat = "dd-MM-yyyy";
    this.datSpecificDate.Format = DateTimePickerFormat.Custom;
    DateTimePicker datSpecificDate1 = this.datSpecificDate;
    point1 = new Point(527, 150);
    Point point11 = point1;
    datSpecificDate1.Location = point11;
    DateTimePicker datSpecificDate2 = this.datSpecificDate;
    padding1 = new Padding(4);
    Padding padding11 = padding1;
    datSpecificDate2.Margin = padding11;
    this.datSpecificDate.Name = "datSpecificDate";
    DateTimePicker datSpecificDate3 = this.datSpecificDate;
    size1 = new Size(122, 22);
    Size size11 = size1;
    datSpecificDate3.Size = size11;
    this.datSpecificDate.TabIndex = 8;
    this.datSpecificDate.Visible = false;
    this.radSpecificDaysWeek.AutoSize = true;
    this.radSpecificDaysWeek.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    RadioButton specificDaysWeek1 = this.radSpecificDaysWeek;
    point1 = new Point(60, 95);
    Point point12 = point1;
    specificDaysWeek1.Location = point12;
    RadioButton specificDaysWeek2 = this.radSpecificDaysWeek;
    padding1 = new Padding(4);
    Padding padding12 = padding1;
    specificDaysWeek2.Margin = padding12;
    this.radSpecificDaysWeek.Name = "radSpecificDaysWeek";
    RadioButton specificDaysWeek3 = this.radSpecificDaysWeek;
    size1 = new Size(180, 20);
    Size size12 = size1;
    specificDaysWeek3.Size = size12;
    this.radSpecificDaysWeek.TabIndex = 2;
    this.radSpecificDaysWeek.Text = "Specific days in Week";
    this.radSpecificDaysWeek.UseVisualStyleBackColor = true;
    this.radDaily.AutoSize = true;
    this.radDaily.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    RadioButton radDaily1 = this.radDaily;
    point1 = new Point(62, 37);
    Point point13 = point1;
    radDaily1.Location = point13;
    RadioButton radDaily2 = this.radDaily;
    padding1 = new Padding(4);
    Padding padding13 = padding1;
    radDaily2.Margin = padding13;
    this.radDaily.Name = "radDaily";
    RadioButton radDaily3 = this.radDaily;
    size1 = new Size(62, 20);
    Size size13 = size1;
    radDaily3.Size = size13;
    this.radDaily.TabIndex = 1;
    this.radDaily.Text = "Daily";
    this.radDaily.UseVisualStyleBackColor = true;
    this.btnExit.BackColor = Color.SeaShell;
    this.btnExit.DialogResult = DialogResult.Cancel;
    this.btnExit.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnExit1 = this.btnExit;
    point1 = new Point(458, 450);
    Point point14 = point1;
    btnExit1.Location = point14;
    Button btnExit2 = this.btnExit;
    padding1 = new Padding(4);
    Padding padding14 = padding1;
    btnExit2.Margin = padding14;
    this.btnExit.Name = "btnExit";
    Button btnExit3 = this.btnExit;
    size1 = new Size(61, 31 /*0x1F*/);
    Size size14 = size1;
    btnExit3.Size = size14;
    this.btnExit.TabIndex = 13;
    this.btnExit.Text = "Exit";
    this.btnExit.UseVisualStyleBackColor = false;
    this.btnOk.BackColor = Color.SeaShell;
    this.btnOk.DialogResult = DialogResult.OK;
    this.btnOk.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnOk1 = this.btnOk;
    point1 = new Point(359, 450);
    Point point15 = point1;
    btnOk1.Location = point15;
    Button btnOk2 = this.btnOk;
    padding1 = new Padding(4);
    Padding padding15 = padding1;
    btnOk2.Margin = padding15;
    this.btnOk.Name = "btnOk";
    Button btnOk3 = this.btnOk;
    size1 = new Size(66, 31 /*0x1F*/);
    Size size15 = size1;
    btnOk3.Size = size15;
    this.btnOk.TabIndex = 12;
    this.btnOk.Text = "Ok";
    this.btnOk.UseVisualStyleBackColor = false;
    this.chklstWeekDays.CheckOnClick = true;
    this.chklstWeekDays.Enabled = false;
    this.chklstWeekDays.ForeColor = SystemColors.InfoText;
    this.chklstWeekDays.FormattingEnabled = true;
    this.chklstWeekDays.HorizontalExtent = 1;
    this.chklstWeekDays.Items.AddRange(new object[7]
    {
      (object) "Sunday",
      (object) "Monday",
      (object) "Tuesday",
      (object) "Wednesday",
      (object) "Thursday",
      (object) "Friday",
      (object) "Saturday"
    });
    CheckedListBox chklstWeekDays1 = this.chklstWeekDays;
    point1 = new Point(62, 150);
    Point point16 = point1;
    chklstWeekDays1.Location = point16;
    CheckedListBox chklstWeekDays2 = this.chklstWeekDays;
    padding1 = new Padding(4);
    Padding padding16 = padding1;
    chklstWeekDays2.Margin = padding16;
    this.chklstWeekDays.Name = "chklstWeekDays";
    CheckedListBox chklstWeekDays3 = this.chklstWeekDays;
    size1 = new Size(178, 123);
    Size size16 = size1;
    chklstWeekDays3.Size = size16;
    this.chklstWeekDays.TabIndex = 5;
    this.chklstWeekDays.Tag = (object) "WeekDays";
    this.chklstWeekDays.ThreeDCheckBoxes = true;
    this.AcceptButton = (IButtonControl) this.btnOk;
    this.AutoScaleDimensions = new SizeF(9f, 16f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.BackColor = Color.PowderBlue;
    this.CancelButton = (IButtonControl) this.btnExit;
    size1 = new Size(846, 511 /*0x01FF*/);
    this.ClientSize = size1;
    this.Controls.Add((Control) this.datTo);
    this.Controls.Add((Control) this.btnRemove);
    this.Controls.Add((Control) this.Label2);
    this.Controls.Add((Control) this.datFrom);
    this.Controls.Add((Control) this.btnAdd);
    this.Controls.Add((Control) this.lstboxSpecificDates);
    this.Controls.Add((Control) this.Label1);
    this.Controls.Add((Control) this.radSpecificDates);
    this.Controls.Add((Control) this.radPeriod);
    this.Controls.Add((Control) this.datSpecificDate);
    this.Controls.Add((Control) this.radSpecificDaysWeek);
    this.Controls.Add((Control) this.radDaily);
    this.Controls.Add((Control) this.btnExit);
    this.Controls.Add((Control) this.btnOk);
    this.Controls.Add((Control) this.chklstWeekDays);
    this.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.FormBorderStyle = FormBorderStyle.FixedToolWindow;
    padding1 = new Padding(4);
    this.Margin = padding1;
    this.Name = "frmTrainDetails";
    this.Text = "frmTrainDetails";
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  internal virtual DateTimePicker datTo
  {
    [DebuggerNonUserCode] get { return this._datTo; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.datTo_ValueChanged);
      if (this._datTo != null)
        this._datTo.ValueChanged -= eventHandler;
      this._datTo = value;
      if (this._datTo == null)
        return;
      this._datTo.ValueChanged += eventHandler;
    }
  }

  internal virtual Button btnRemove
  {
    [DebuggerNonUserCode] get { return this._btnRemove; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnRemove_Click);
      if (this._btnRemove != null)
        this._btnRemove.Click -= eventHandler;
      this._btnRemove = value;
      if (this._btnRemove == null)
        return;
      this._btnRemove.Click += eventHandler;
    }
  }

  internal virtual Label Label2
  {
    [DebuggerNonUserCode] get { return this._Label2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Label2 = value; }
  }

  internal virtual DateTimePicker datFrom
  {
    [DebuggerNonUserCode] get { return this._datFrom; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._datFrom = value; }
  }

  internal virtual Button btnAdd
  {
    [DebuggerNonUserCode] get { return this._btnAdd; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnAdd_Click);
      if (this._btnAdd != null)
        this._btnAdd.Click -= eventHandler;
      this._btnAdd = value;
      if (this._btnAdd == null)
        return;
      this._btnAdd.Click += eventHandler;
    }
  }

  public virtual ListBox lstboxSpecificDates
  {
    [DebuggerNonUserCode] get { return this._lstboxSpecificDates; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lstboxSpecificDates = value;
    }
  }

  internal virtual Label Label1
  {
    [DebuggerNonUserCode] get { return this._Label1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Label1 = value; }
  }

  internal virtual RadioButton radSpecificDates
  {
    [DebuggerNonUserCode] get { return this._radSpecificDates; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.radSpecificDates_CheckedChanged);
      if (this._radSpecificDates != null)
        this._radSpecificDates.CheckedChanged -= eventHandler;
      this._radSpecificDates = value;
      if (this._radSpecificDates == null)
        return;
      this._radSpecificDates.CheckedChanged += eventHandler;
    }
  }

  internal virtual RadioButton radPeriod
  {
    [DebuggerNonUserCode] get { return this._radPeriod; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.radPeriod_CheckedChanged);
      if (this._radPeriod != null)
        this._radPeriod.CheckedChanged -= eventHandler;
      this._radPeriod = value;
      if (this._radPeriod == null)
        return;
      this._radPeriod.CheckedChanged += eventHandler;
    }
  }

  internal virtual DateTimePicker datSpecificDate
  {
    [DebuggerNonUserCode] get { return this._datSpecificDate; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler1 = new EventHandler(this.datSpecificDate_ValueChanged);
      EventHandler eventHandler2 = new EventHandler(this.datSpecificDate_LostFocus);
      if (this._datSpecificDate != null)
      {
        this._datSpecificDate.ValueChanged -= eventHandler1;
        this._datSpecificDate.LostFocus -= eventHandler2;
      }
      this._datSpecificDate = value;
      if (this._datSpecificDate == null)
        return;
      this._datSpecificDate.ValueChanged += eventHandler1;
      this._datSpecificDate.LostFocus += eventHandler2;
    }
  }

  internal virtual RadioButton radSpecificDaysWeek
  {
    [DebuggerNonUserCode] get { return this._radSpecificDaysWeek; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.radSpecificDaysWeek_CheckedChanged_1);
      if (this._radSpecificDaysWeek != null)
        this._radSpecificDaysWeek.CheckedChanged -= eventHandler;
      this._radSpecificDaysWeek = value;
      if (this._radSpecificDaysWeek == null)
        return;
      this._radSpecificDaysWeek.CheckedChanged += eventHandler;
    }
  }

  internal virtual RadioButton radDaily
  {
    [DebuggerNonUserCode] get { return this._radDaily; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._radDaily = value; }
  }

  internal virtual Button btnExit
  {
    [DebuggerNonUserCode] get { return this._btnExit; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnExit_Click);
      if (this._btnExit != null)
        this._btnExit.Click -= eventHandler;
      this._btnExit = value;
      if (this._btnExit == null)
        return;
      this._btnExit.Click += eventHandler;
    }
  }

  internal virtual Button btnOk
  {
    [DebuggerNonUserCode] get { return this._btnOk; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnOk_Click);
      if (this._btnOk != null)
        this._btnOk.Click -= eventHandler;
      this._btnOk = value;
      if (this._btnOk == null)
        return;
      this._btnOk.Click += eventHandler;
    }
  }

  internal virtual CheckedListBox chklstWeekDays
  {
    [DebuggerNonUserCode] get { return this._chklstWeekDays; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._chklstWeekDays = value;
    }
  }

  private void btnAdd_Click(object sender, EventArgs e)
  {
    if (this.lstboxSpecificDates.Items.Count > 10)
    {
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, " Only 10 Special trains dates can be added\r\nRemove any entered date and then add the new date", "Msg Box", 0, 0, 0);
      this.datSpecificDate.Visible = true;
    }
    else
      this.datSpecificDate.Visible = true;
  }

  public int dailydays { get { return frmTrainDetails.ldaily; } }

  public int specificdays { get { return frmTrainDetails.lspecificdays; } }

  public string Sunday { get { return Strings.Trim(frmTrainDetails.Weekdaysarray[0]); } }

  public string Monday { get { return Strings.Trim(frmTrainDetails.Weekdaysarray[1]); } }

  public string Tuesday { get { return Strings.Trim(frmTrainDetails.Weekdaysarray[2]); } }

  public string Wednesday { get { return Strings.Trim(frmTrainDetails.Weekdaysarray[3]); } }

  public string Thursday { get { return Strings.Trim(frmTrainDetails.Weekdaysarray[4]); } }

  public string Friday { get { return Strings.Trim(frmTrainDetails.Weekdaysarray[5]); } }

  public string Saturday { get { return Strings.Trim(frmTrainDetails.Weekdaysarray[6]); } }

  public int perioddates { get { return frmTrainDetails.lperiod; } }

  public DateTime fdate { get { return frmTrainDetails.ftdate[0]; } }

  public DateTime tdate { get { return frmTrainDetails.ftdate[1]; } }

  public int specificdates { get { return frmTrainDetails.lspecificdates; } }

  public DateTime spdate1 { get { return frmTrainDetails.spdate[0]; } }

  public DateTime spdate2 { get { return frmTrainDetails.spdate[1]; } }

  public DateTime spdate3 { get { return frmTrainDetails.spdate[2]; } }

  public DateTime spdate4 { get { return frmTrainDetails.spdate[3]; } }

  public DateTime spdate5 { get { return frmTrainDetails.spdate[4]; } }

  public DateTime spdate6 { get { return frmTrainDetails.spdate[5]; } }

  public DateTime spdate7 { get { return frmTrainDetails.spdate[6]; } }

  public DateTime spdate8 { get { return frmTrainDetails.spdate[7]; } }

  public DateTime spdate9 { get { return frmTrainDetails.spdate[8]; } }

  public DateTime spdate10 { get { return frmTrainDetails.spdate[9]; } }

  public void btnOk_Click(object sender, EventArgs e)
  {
    frmTrainConfig.train_details_enter = true;
    try
    {
      int index1 = 0;
      int index2 = 0;
      int[] numArray = new int[11];
      string[] strArray = new string[11];
      frmTrainDetails.ldaily = 0;
      frmTrainDetails.lspecificdays = 0;
      frmTrainDetails.lperiod = 0;
      frmTrainDetails.lspecificdates = 0;
      int count1 = this.chklstWeekDays.Items.Count;
      int num1 = 0;
      if (index2 <= count1)
      {
        try
        {
          foreach (object checkedIndex in this.chklstWeekDays.CheckedIndices)
          {
            int integer = Conversions.ToInteger(checkedIndex);
            numArray[index1] = integer;
            strArray[index1] = this.chklstWeekDays.CheckedItems[index2].ToString();
            checked { ++index1; }
            checked { ++index2; }
          }
        }
        finally
        {
          IEnumerator enumerator = null;
          if (enumerator is IDisposable)
            (enumerator as IDisposable).Dispose();
        }
        numArray[index1] = 0;
        num1 = checked (index1 + 1);
      }
      int index3 = 0;
      int num2 = 0;
      int num3 = 0;
      string str1;
      if (num2 == numArray[index3] & num2 == 0)
      {
        str1 = strArray[index3];
        checked { ++index3; }
      }
      else
        str1 = DBNull.Value.ToString();
      int num4 = checked (num2 + 1);
      string str2;
      if (num4 == numArray[index3] & num4 == 1)
      {
        str2 = strArray[index3];
        checked { ++index3; }
      }
      else
        str2 = DBNull.Value.ToString();
      int num5 = checked (num4 + 1);
      string str3;
      if (num5 == numArray[index3] & num5 == 2)
      {
        str3 = strArray[index3];
        checked { ++index3; }
      }
      else
        str3 = DBNull.Value.ToString();
      int num6 = checked (num5 + 1);
      string str4;
      if (num6 == numArray[index3] & num6 == 3)
      {
        str4 = strArray[index3];
        checked { ++index3; }
      }
      else
        str4 = DBNull.Value.ToString();
      int num7 = checked (num6 + 1);
      string str5;
      if (num7 == numArray[index3] & num7 == 4)
      {
        str5 = strArray[index3];
        checked { ++index3; }
      }
      else
        str5 = DBNull.Value.ToString();
      int num8 = checked (num7 + 1);
      string str6;
      if (num8 == numArray[index3] & num8 == 5)
      {
        str6 = strArray[index3];
        checked { ++index3; }
      }
      else
        str6 = DBNull.Value.ToString();
      int num9 = checked (num8 + 1);
      string str7;
      if (num9 == numArray[index3] & num9 == 6)
      {
        str7 = strArray[index3];
        num1 = checked (index3 + 1);
      }
      else
        str7 = DBNull.Value.ToString();
      num3 = 0;
      if (this.radDaily.Checked)
      {
        this.chklstWeekDays.ClearSelected();
        frmTrainDetails.Weekdaysarray[0] = DBNull.Value.ToString();
        frmTrainDetails.Weekdaysarray[1] = DBNull.Value.ToString();
        frmTrainDetails.Weekdaysarray[2] = DBNull.Value.ToString();
        frmTrainDetails.Weekdaysarray[3] = DBNull.Value.ToString();
        frmTrainDetails.Weekdaysarray[4] = DBNull.Value.ToString();
        frmTrainDetails.Weekdaysarray[5] = DBNull.Value.ToString();
        frmTrainDetails.Weekdaysarray[6] = DBNull.Value.ToString();
        frmTrainDetails.ldaily = 1;
        frmTrainDetails.lspecificdays = 0;
      }
      else if (this.radSpecificDaysWeek.Checked)
      {
        frmTrainDetails.lspecificdays = 1;
        frmTrainDetails.ldaily = 0;
        frmTrainDetails.Weekdaysarray[0] = str1;
        frmTrainDetails.Weekdaysarray[1] = str2;
        frmTrainDetails.Weekdaysarray[2] = str3;
        frmTrainDetails.Weekdaysarray[3] = str4;
        frmTrainDetails.Weekdaysarray[4] = str5;
        frmTrainDetails.Weekdaysarray[5] = str6;
        frmTrainDetails.Weekdaysarray[6] = str7;
      }
      if (this.radPeriod.Checked)
      {
        frmTrainDetails.lperiod = 1;
        frmTrainDetails.ftdate[0] = this.datFrom.Value;
        frmTrainDetails.ftdate[1] = this.datTo.Value;
      }
      else
      {
        frmTrainDetails.ftdate[0] = DateTime.MinValue;
        frmTrainDetails.ftdate[1] = DateTime.MinValue;
        frmTrainDetails.lperiod = 0;
      }
      if (DateTime.Compare(frmTrainDetails.ftdate[0], DateTime.MinValue) == 0)
        frmTrainDetails.ftdate[0] = DateTime.MinValue;
      if (DateTime.Compare(frmTrainDetails.ftdate[1], DateTime.MinValue) == 0)
        frmTrainDetails.ftdate[1] = DateTime.MinValue;
      int index4 = 0;
      num1 = 0;
      if (this.radSpecificDates.Checked)
      {
        frmTrainDetails.lspecificdates = 1;
        int count2 = this.lstboxSpecificDates.Items.Count;
        if (count2 < 10)
        {
          while (index4 < count2)
          {
            frmTrainDetails.spdate[index4] = Conversions.ToDate(this.lstboxSpecificDates.Items[index4]);
            frmTrainDetails.specificdate[index4] = frmTrainDetails.spdate[index4].ToString("dd/MM/yyyy");
            checked { ++index4; }
          }
          while (index4 <= 10)
          {
            frmTrainDetails.specificdate[index4] = Conversions.ToString(DateTime.MinValue);
            checked { ++index4; }
          }
        }
      }
      else
      {
        frmTrainDetails.lspecificdates = 0;
        int index5 = 0;
        while (index5 <= 10)
        {
          frmTrainDetails.specificdate[index5] = Conversions.ToString(DateTime.MinValue);
          checked { ++index5; }
        }
      }
      MyProject.Forms.frmTrainConfig.BringToFront();
      this.Close();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void radSpecificDaysWeek_CheckedChanged_1(object sender, EventArgs e)
  {
    try
    {
      if (this.radSpecificDaysWeek.Checked)
      {
        this.chklstWeekDays.Enabled = true;
      }
      else
      {
        this.chklstWeekDays.Enabled = false;
        int index = 0;
        do
        {
          this.chklstWeekDays.SetItemChecked(index, false);
          checked { ++index; }
        }
        while (index <= 6);
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void btnRemove_Click(object sender, EventArgs e)
  {
    Conversions.ToDate(this.lstboxSpecificDates.SelectedItem);
    this.lstboxSpecificDates.Items.Remove(RuntimeHelpers.GetObjectValue(this.lstboxSpecificDates.SelectedItem));
  }

  private void TrainTypeConfig_Load(object sender, EventArgs e)
{
  this.datSpecificDate.Hide();
}

  private void radSpecificDates_CheckedChanged(object sender, EventArgs e)
  {
    if (this.radSpecificDates.Checked)
    {
      this.lstboxSpecificDates.Enabled = true;
      this.btnAdd.Enabled = true;
      this.btnRemove.Enabled = true;
    }
    else if (!this.radSpecificDates.Checked)
    {
      this.lstboxSpecificDates.Enabled = false;
      this.btnAdd.Enabled = false;
      this.btnRemove.Enabled = false;
      this.lstboxSpecificDates.Items.Clear();
    }
  }

  private void datSpecificDate_LostFocus(object sender, EventArgs e)
{
  this.datSpecificDate.Hide();
}

  private void datSpecificDate_ValueChanged(object sender, EventArgs e)
  {
    bool flag = false;
    if (this.lstboxSpecificDates.Items.Count < 10)
    {
      this.i = 0;
      while (this.i < this.lstboxSpecificDates.Items.Count)
      {
        if (Operators.ConditionalCompareObjectEqual((object) this.datSpecificDate.Value.ToShortDateString(), this.lstboxSpecificDates.Items[this.i], false))
        {
          flag = true;
          int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Date already Added", "Msg Box", 0, 0, 0);
          return;
        }
        checked { ++this.i; }
      }
      this.lstboxSpecificDates.Items.Add((object) this.datSpecificDate.Value.ToShortDateString());
    }
    else
    {
      int num1 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Only 10 special train dates can be added", "Msg Box", 0, 0, 0);
    }
  }

  private void btnExit_Click(object sender, EventArgs e)
  {
    frmTrainConfig.train_details_enter = false;
    this.Close();
  }

  private void radPeriod_CheckedChanged(object sender, EventArgs e)
  {
    if (this.radPeriod.Checked)
    {
      this.datFrom.Enabled = true;
      this.datTo.Enabled = true;
    }
    else if (!this.radPeriod.Checked)
    {
      this.datFrom.Enabled = false;
      this.datTo.Enabled = false;
    }
  }

  private void datTo_ValueChanged(object sender, EventArgs e)
  {
    if (DateTime.Compare(this.datFrom.Value, this.datTo.Value) <= 0)
      return;
    this.datTo.Text = Conversions.ToString(this.datFrom.Value);
    checked { ++this.found; }
    if (this.found >= (byte) 2)
    {
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Select the date greater than from date", "Msg Box", 0, 0, 0);
      this.found = (byte) 0;
    }
  }
}

}