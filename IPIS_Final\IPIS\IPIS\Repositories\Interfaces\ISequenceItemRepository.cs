using System.Collections.Generic;
using System.Threading.Tasks;
using IPIS.Models;

namespace IPIS.Repositories.Interfaces
{
    public interface ISequenceItemRepository
    {
        Task<IEnumerable<SequenceItem>> GetItemsBySequenceAsync(int sequenceId);
        Task<IEnumerable<SequenceItem>> GetItemsBySequenceOrderedAsync(int sequenceId);
        Task<SequenceItem> GetItemByIdAsync(int id);
        Task<int> AddItemAsync(SequenceItem item);
        Task<bool> UpdateItemAsync(SequenceItem item);
        Task<bool> DeleteItemAsync(int id);
        Task<bool> ReorderItemsAsync(int sequenceId, List<int> itemIds);
        Task<bool> MoveItemUpAsync(int itemId);
        Task<bool> MoveItemDownAsync(int itemId);
    }
} 