// Decompiled with JetBrains decompiler
// Type: ipis.frmNetworkMDCH
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using ipis.My;
using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Data.OleDb;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

namespace ipis
{

[DesignerGenerated]
public class frmNetworkMDCH : Form
{
  private static List<WeakReference> __ENCList = new List<WeakReference>();
  private IContainer components;
  [AccessedThroughProperty("btnExit")]
  private Button _btnExit;
  [AccessedThroughProperty("GroupBox1")]
  private GroupBox _GroupBox1;
  [AccessedThroughProperty("btnP16S4")]
  private Button _btnP16S4;
  [AccessedThroughProperty("btnP15S4")]
  private Button _btnP15S4;
  [AccessedThroughProperty("btnP14S4")]
  private Button _btnP14S4;
  [AccessedThroughProperty("btnP13S4")]
  private Button _btnP13S4;
  [AccessedThroughProperty("btnP12S4")]
  private Button _btnP12S4;
  [AccessedThroughProperty("btnP11S4")]
  private Button _btnP11S4;
  [AccessedThroughProperty("btnP10S4")]
  private Button _btnP10S4;
  [AccessedThroughProperty("btnP9S4")]
  private Button _btnP9S4;
  [AccessedThroughProperty("btnP8S4")]
  private Button _btnP8S4;
  [AccessedThroughProperty("btnP7S4")]
  private Button _btnP7S4;
  [AccessedThroughProperty("btnP6S4")]
  private Button _btnP6S4;
  [AccessedThroughProperty("btnP5S4")]
  private Button _btnP5S4;
  [AccessedThroughProperty("btnP4S4")]
  private Button _btnP4S4;
  [AccessedThroughProperty("btnP3S4")]
  private Button _btnP3S4;
  [AccessedThroughProperty("btnP2S4")]
  private Button _btnP2S4;
  [AccessedThroughProperty("btnP1S4")]
  private Button _btnP1S4;
  [AccessedThroughProperty("cmbP16S4")]
  private ComboBox _cmbP16S4;
  [AccessedThroughProperty("cmbP15S4")]
  private ComboBox _cmbP15S4;
  [AccessedThroughProperty("cmbP14S4")]
  private ComboBox _cmbP14S4;
  [AccessedThroughProperty("cmbP13S4")]
  private ComboBox _cmbP13S4;
  [AccessedThroughProperty("cmbP12S4")]
  private ComboBox _cmbP12S4;
  [AccessedThroughProperty("cmbP11S4")]
  private ComboBox _cmbP11S4;
  [AccessedThroughProperty("cmbP10S4")]
  private ComboBox _cmbP10S4;
  [AccessedThroughProperty("cmbP9S4")]
  private ComboBox _cmbP9S4;
  [AccessedThroughProperty("cmbP8S4")]
  private ComboBox _cmbP8S4;
  [AccessedThroughProperty("cmbP7S4")]
  private ComboBox _cmbP7S4;
  [AccessedThroughProperty("cmbP6S4")]
  private ComboBox _cmbP6S4;
  [AccessedThroughProperty("cmbP5S4")]
  private ComboBox _cmbP5S4;
  [AccessedThroughProperty("cmbP4S4")]
  private ComboBox _cmbP4S4;
  [AccessedThroughProperty("cmbP3S4")]
  private ComboBox _cmbP3S4;
  [AccessedThroughProperty("cmbP2S4")]
  private ComboBox _cmbP2S4;
  [AccessedThroughProperty("cmbP1S4")]
  private ComboBox _cmbP1S4;
  [AccessedThroughProperty("btnP16S3")]
  private Button _btnP16S3;
  [AccessedThroughProperty("btnP15S3")]
  private Button _btnP15S3;
  [AccessedThroughProperty("btnP14S3")]
  private Button _btnP14S3;
  [AccessedThroughProperty("btnP13S3")]
  private Button _btnP13S3;
  [AccessedThroughProperty("btnP12S3")]
  private Button _btnP12S3;
  [AccessedThroughProperty("btnP11S3")]
  private Button _btnP11S3;
  [AccessedThroughProperty("btnP10S3")]
  private Button _btnP10S3;
  [AccessedThroughProperty("btnP9S3")]
  private Button _btnP9S3;
  [AccessedThroughProperty("btnP8S3")]
  private Button _btnP8S3;
  [AccessedThroughProperty("btnP7S3")]
  private Button _btnP7S3;
  [AccessedThroughProperty("btnP6S3")]
  private Button _btnP6S3;
  [AccessedThroughProperty("btnP5S3")]
  private Button _btnP5S3;
  [AccessedThroughProperty("btnP4S3")]
  private Button _btnP4S3;
  [AccessedThroughProperty("btnP3S3")]
  private Button _btnP3S3;
  [AccessedThroughProperty("btnP2S3")]
  private Button _btnP2S3;
  [AccessedThroughProperty("btnP1S3")]
  private Button _btnP1S3;
  [AccessedThroughProperty("cmbP16S3")]
  private ComboBox _cmbP16S3;
  [AccessedThroughProperty("cmbP15S3")]
  private ComboBox _cmbP15S3;
  [AccessedThroughProperty("cmbP14S3")]
  private ComboBox _cmbP14S3;
  [AccessedThroughProperty("cmbP13S3")]
  private ComboBox _cmbP13S3;
  [AccessedThroughProperty("cmbP12S3")]
  private ComboBox _cmbP12S3;
  [AccessedThroughProperty("cmbP11S3")]
  private ComboBox _cmbP11S3;
  [AccessedThroughProperty("cmbP10S3")]
  private ComboBox _cmbP10S3;
  [AccessedThroughProperty("cmbP9S3")]
  private ComboBox _cmbP9S3;
  [AccessedThroughProperty("cmbP8S3")]
  private ComboBox _cmbP8S3;
  [AccessedThroughProperty("cmbP7S3")]
  private ComboBox _cmbP7S3;
  [AccessedThroughProperty("cmbP6S3")]
  private ComboBox _cmbP6S3;
  [AccessedThroughProperty("cmbP5S3")]
  private ComboBox _cmbP5S3;
  [AccessedThroughProperty("cmbP4S3")]
  private ComboBox _cmbP4S3;
  [AccessedThroughProperty("cmbP3S3")]
  private ComboBox _cmbP3S3;
  [AccessedThroughProperty("cmbP2S3")]
  private ComboBox _cmbP2S3;
  [AccessedThroughProperty("cmbP1S3")]
  private ComboBox _cmbP1S3;
  [AccessedThroughProperty("btnP16S2")]
  private Button _btnP16S2;
  [AccessedThroughProperty("btnP15S2")]
  private Button _btnP15S2;
  [AccessedThroughProperty("btnP14S2")]
  private Button _btnP14S2;
  [AccessedThroughProperty("btnP13S2")]
  private Button _btnP13S2;
  [AccessedThroughProperty("btnP12S2")]
  private Button _btnP12S2;
  [AccessedThroughProperty("btnP11S2")]
  private Button _btnP11S2;
  [AccessedThroughProperty("btnP10S2")]
  private Button _btnP10S2;
  [AccessedThroughProperty("btnP9S2")]
  private Button _btnP9S2;
  [AccessedThroughProperty("btnP8S2")]
  private Button _btnP8S2;
  [AccessedThroughProperty("btnP7S2")]
  private Button _btnP7S2;
  [AccessedThroughProperty("btnP6S2")]
  private Button _btnP6S2;
  [AccessedThroughProperty("btnP5S2")]
  private Button _btnP5S2;
  [AccessedThroughProperty("btnP4S2")]
  private Button _btnP4S2;
  [AccessedThroughProperty("btnP3S2")]
  private Button _btnP3S2;
  [AccessedThroughProperty("btnP2S2")]
  private Button _btnP2S2;
  [AccessedThroughProperty("btnP1S2")]
  private Button _btnP1S2;
  [AccessedThroughProperty("cmbP16S2")]
  private ComboBox _cmbP16S2;
  [AccessedThroughProperty("cmbP15S2")]
  private ComboBox _cmbP15S2;
  [AccessedThroughProperty("cmbP14S2")]
  private ComboBox _cmbP14S2;
  [AccessedThroughProperty("cmbP13S2")]
  private ComboBox _cmbP13S2;
  [AccessedThroughProperty("cmbP12S2")]
  private ComboBox _cmbP12S2;
  [AccessedThroughProperty("cmbP11S2")]
  private ComboBox _cmbP11S2;
  [AccessedThroughProperty("cmbP10S2")]
  private ComboBox _cmbP10S2;
  [AccessedThroughProperty("cmbP9S2")]
  private ComboBox _cmbP9S2;
  [AccessedThroughProperty("cmbP8S2")]
  private ComboBox _cmbP8S2;
  [AccessedThroughProperty("cmbP7S2")]
  private ComboBox _cmbP7S2;
  [AccessedThroughProperty("cmbP6S2")]
  private ComboBox _cmbP6S2;
  [AccessedThroughProperty("cmbP5S2")]
  private ComboBox _cmbP5S2;
  [AccessedThroughProperty("cmbP4S2")]
  private ComboBox _cmbP4S2;
  [AccessedThroughProperty("cmbP3S2")]
  private ComboBox _cmbP3S2;
  [AccessedThroughProperty("cmbP2S2")]
  private ComboBox _cmbP2S2;
  [AccessedThroughProperty("cmbP1S2")]
  private ComboBox _cmbP1S2;
  [AccessedThroughProperty("btnP16S1")]
  private Button _btnP16S1;
  [AccessedThroughProperty("btnP15S1")]
  private Button _btnP15S1;
  [AccessedThroughProperty("btnP14S1")]
  private Button _btnP14S1;
  [AccessedThroughProperty("btnP13S1")]
  private Button _btnP13S1;
  [AccessedThroughProperty("btnP12S1")]
  private Button _btnP12S1;
  [AccessedThroughProperty("btnP11S1")]
  private Button _btnP11S1;
  [AccessedThroughProperty("btnP10S1")]
  private Button _btnP10S1;
  [AccessedThroughProperty("btnP9S1")]
  private Button _btnP9S1;
  [AccessedThroughProperty("btnP8S1")]
  private Button _btnP8S1;
  [AccessedThroughProperty("btnP7S1")]
  private Button _btnP7S1;
  [AccessedThroughProperty("btnP6S1")]
  private Button _btnP6S1;
  [AccessedThroughProperty("btnP5S1")]
  private Button _btnP5S1;
  [AccessedThroughProperty("btnP4S1")]
  private Button _btnP4S1;
  [AccessedThroughProperty("btnP3S1")]
  private Button _btnP3S1;
  [AccessedThroughProperty("btnP2S1")]
  private Button _btnP2S1;
  [AccessedThroughProperty("btnP1S1")]
  private Button _btnP1S1;
  [AccessedThroughProperty("cmbP16S1")]
  private ComboBox _cmbP16S1;
  [AccessedThroughProperty("cmbP15S1")]
  private ComboBox _cmbP15S1;
  [AccessedThroughProperty("cmbP14S1")]
  private ComboBox _cmbP14S1;
  [AccessedThroughProperty("cmbP13S1")]
  private ComboBox _cmbP13S1;
  [AccessedThroughProperty("cmbP12S1")]
  private ComboBox _cmbP12S1;
  [AccessedThroughProperty("cmbP11S1")]
  private ComboBox _cmbP11S1;
  [AccessedThroughProperty("cmbP10S1")]
  private ComboBox _cmbP10S1;
  [AccessedThroughProperty("cmbP9S1")]
  private ComboBox _cmbP9S1;
  [AccessedThroughProperty("cmbP8S1")]
  private ComboBox _cmbP8S1;
  [AccessedThroughProperty("cmbP7S1")]
  private ComboBox _cmbP7S1;
  [AccessedThroughProperty("cmbP6S1")]
  private ComboBox _cmbP6S1;
  [AccessedThroughProperty("cmbP5S1")]
  private ComboBox _cmbP5S1;
  [AccessedThroughProperty("cmbP4S1")]
  private ComboBox _cmbP4S1;
  [AccessedThroughProperty("cmbP3S1")]
  private ComboBox _cmbP3S1;
  [AccessedThroughProperty("cmbP2S1")]
  private ComboBox _cmbP2S1;
  [AccessedThroughProperty("cmbP1S1")]
  private ComboBox _cmbP1S1;
  [AccessedThroughProperty("chkPort16")]
  private CheckBox _chkPort16;
  [AccessedThroughProperty("chkPort15")]
  private CheckBox _chkPort15;
  [AccessedThroughProperty("chkPort14")]
  private CheckBox _chkPort14;
  [AccessedThroughProperty("chkPort13")]
  private CheckBox _chkPort13;
  [AccessedThroughProperty("chkPort12")]
  private CheckBox _chkPort12;
  [AccessedThroughProperty("chkPort11")]
  private CheckBox _chkPort11;
  [AccessedThroughProperty("chkPort10")]
  private CheckBox _chkPort10;
  [AccessedThroughProperty("chkPort9")]
  private CheckBox _chkPort9;
  [AccessedThroughProperty("chkPort8")]
  private CheckBox _chkPort8;
  [AccessedThroughProperty("chkPort7")]
  private CheckBox _chkPort7;
  [AccessedThroughProperty("chkPort6")]
  private CheckBox _chkPort6;
  [AccessedThroughProperty("chkPort5")]
  private CheckBox _chkPort5;
  [AccessedThroughProperty("chkPort4")]
  private CheckBox _chkPort4;
  [AccessedThroughProperty("chkPort3")]
  private CheckBox _chkPort3;
  [AccessedThroughProperty("chkPort2")]
  private CheckBox _chkPort2;
  [AccessedThroughProperty("chkPort1")]
  private CheckBox _chkPort1;
  [AccessedThroughProperty("txtCcuName")]
  private TextBox _txtCcuName;
  [AccessedThroughProperty("txtCcuAddress")]
  private TextBox _txtCcuAddress;
  [AccessedThroughProperty("lblCcuName")]
  private Label _lblCcuName;
  [AccessedThroughProperty("lblCcuAddress")]
  private Label _lblCcuAddress;
  [AccessedThroughProperty("txtMdchName")]
  private TextBox _txtMdchName;
  [AccessedThroughProperty("txtMdchAddress")]
  private TextBox _txtMdchAddress;
  [AccessedThroughProperty("lblMdchName")]
  private Label _lblMdchName;
  [AccessedThroughProperty("lblMdchAddress")]
  private Label _lblMdchAddress;
  [AccessedThroughProperty("btnNew")]
  private Button _btnNew;
  [AccessedThroughProperty("btnSave")]
  private Button _btnSave;
  [AccessedThroughProperty("btnDelete")]
  private Button _btnDelete;
  [AccessedThroughProperty("btnEdit")]
  private Button _btnEdit;
  [AccessedThroughProperty("event_pdch")]
  private frmNetworkPDCH _event_pdch;
  [AccessedThroughProperty("event_agdb")]
  private frmNetworkAGDB _event_agdb;
  [AccessedThroughProperty("event_mldb")]
  private frmNetworkMLDB _event_mldb;
  [AccessedThroughProperty("event_pdb")]
  private frmNetworkPDB _event_pdb;
  public static bool hub_type = false;
  public static byte mdch_port_num;
  public static byte mdch_system_num;

  [DebuggerNonUserCode]
  public frmNetworkMDCH()
  {
    frmNetworkMDCH.__ENCAddToList((object) this);
    this.InitializeComponent();
  }

  [DebuggerNonUserCode]
  private static void __ENCAddToList(object value)
  {
    lock (frmNetworkMDCH.__ENCList)
    {
      if (frmNetworkMDCH.__ENCList.Count == frmNetworkMDCH.__ENCList.Capacity)
      {
        int index1 = 0;
        int num = checked (frmNetworkMDCH.__ENCList.Count - 1);
        int index2 = 0;
        while (index2 <= num)
        {
          if (frmNetworkMDCH.__ENCList[index2].IsAlive)
          {
            if (index2 != index1)
              frmNetworkMDCH.__ENCList[index1] = frmNetworkMDCH.__ENCList[index2];
            checked { ++index1; }
          }
          checked { ++index2; }
        }
        frmNetworkMDCH.__ENCList.RemoveRange(index1, checked (frmNetworkMDCH.__ENCList.Count - index1));
        frmNetworkMDCH.__ENCList.Capacity = frmNetworkMDCH.__ENCList.Count;
      }
      frmNetworkMDCH.__ENCList.Add(new WeakReference(RuntimeHelpers.GetObjectValue(value)));
    }
  }

  [DebuggerNonUserCode]
  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  [DebuggerStepThrough]
  private void InitializeComponent()
  {
    this.btnExit = new Button();
    this.GroupBox1 = new GroupBox();
    this.btnP16S4 = new Button();
    this.btnP15S4 = new Button();
    this.btnP14S4 = new Button();
    this.btnP13S4 = new Button();
    this.btnP12S4 = new Button();
    this.btnP11S4 = new Button();
    this.btnP10S4 = new Button();
    this.btnP9S4 = new Button();
    this.btnP8S4 = new Button();
    this.btnP7S4 = new Button();
    this.btnP6S4 = new Button();
    this.btnP5S4 = new Button();
    this.btnP4S4 = new Button();
    this.btnP3S4 = new Button();
    this.btnP2S4 = new Button();
    this.btnP1S4 = new Button();
    this.cmbP16S4 = new ComboBox();
    this.cmbP15S4 = new ComboBox();
    this.cmbP14S4 = new ComboBox();
    this.cmbP13S4 = new ComboBox();
    this.cmbP12S4 = new ComboBox();
    this.cmbP11S4 = new ComboBox();
    this.cmbP10S4 = new ComboBox();
    this.cmbP9S4 = new ComboBox();
    this.cmbP8S4 = new ComboBox();
    this.cmbP7S4 = new ComboBox();
    this.cmbP6S4 = new ComboBox();
    this.cmbP5S4 = new ComboBox();
    this.cmbP4S4 = new ComboBox();
    this.cmbP3S4 = new ComboBox();
    this.cmbP2S4 = new ComboBox();
    this.cmbP1S4 = new ComboBox();
    this.btnP16S3 = new Button();
    this.btnP15S3 = new Button();
    this.btnP14S3 = new Button();
    this.btnP13S3 = new Button();
    this.btnP12S3 = new Button();
    this.btnP11S3 = new Button();
    this.btnP10S3 = new Button();
    this.btnP9S3 = new Button();
    this.btnP8S3 = new Button();
    this.btnP7S3 = new Button();
    this.btnP6S3 = new Button();
    this.btnP5S3 = new Button();
    this.btnP4S3 = new Button();
    this.btnP3S3 = new Button();
    this.btnP2S3 = new Button();
    this.btnP1S3 = new Button();
    this.cmbP16S3 = new ComboBox();
    this.cmbP15S3 = new ComboBox();
    this.cmbP14S3 = new ComboBox();
    this.cmbP13S3 = new ComboBox();
    this.cmbP12S3 = new ComboBox();
    this.cmbP11S3 = new ComboBox();
    this.cmbP10S3 = new ComboBox();
    this.cmbP9S3 = new ComboBox();
    this.cmbP8S3 = new ComboBox();
    this.cmbP7S3 = new ComboBox();
    this.cmbP6S3 = new ComboBox();
    this.cmbP5S3 = new ComboBox();
    this.cmbP4S3 = new ComboBox();
    this.cmbP3S3 = new ComboBox();
    this.cmbP2S3 = new ComboBox();
    this.cmbP1S3 = new ComboBox();
    this.btnP16S2 = new Button();
    this.btnP15S2 = new Button();
    this.btnP14S2 = new Button();
    this.btnP13S2 = new Button();
    this.btnP12S2 = new Button();
    this.btnP11S2 = new Button();
    this.btnP10S2 = new Button();
    this.btnP9S2 = new Button();
    this.btnP8S2 = new Button();
    this.btnP7S2 = new Button();
    this.btnP6S2 = new Button();
    this.btnP5S2 = new Button();
    this.btnP4S2 = new Button();
    this.btnP3S2 = new Button();
    this.btnP2S2 = new Button();
    this.btnP1S2 = new Button();
    this.cmbP16S2 = new ComboBox();
    this.cmbP15S2 = new ComboBox();
    this.cmbP14S2 = new ComboBox();
    this.cmbP13S2 = new ComboBox();
    this.cmbP12S2 = new ComboBox();
    this.cmbP11S2 = new ComboBox();
    this.cmbP10S2 = new ComboBox();
    this.cmbP9S2 = new ComboBox();
    this.cmbP8S2 = new ComboBox();
    this.cmbP7S2 = new ComboBox();
    this.cmbP6S2 = new ComboBox();
    this.cmbP5S2 = new ComboBox();
    this.cmbP4S2 = new ComboBox();
    this.cmbP3S2 = new ComboBox();
    this.cmbP2S2 = new ComboBox();
    this.cmbP1S2 = new ComboBox();
    this.btnP16S1 = new Button();
    this.btnP15S1 = new Button();
    this.btnP14S1 = new Button();
    this.btnP13S1 = new Button();
    this.btnP12S1 = new Button();
    this.btnP11S1 = new Button();
    this.btnP10S1 = new Button();
    this.btnP9S1 = new Button();
    this.btnP8S1 = new Button();
    this.btnP7S1 = new Button();
    this.btnP6S1 = new Button();
    this.btnP5S1 = new Button();
    this.btnP4S1 = new Button();
    this.btnP3S1 = new Button();
    this.btnP2S1 = new Button();
    this.btnP1S1 = new Button();
    this.cmbP16S1 = new ComboBox();
    this.cmbP15S1 = new ComboBox();
    this.cmbP14S1 = new ComboBox();
    this.cmbP13S1 = new ComboBox();
    this.cmbP12S1 = new ComboBox();
    this.cmbP11S1 = new ComboBox();
    this.cmbP10S1 = new ComboBox();
    this.cmbP9S1 = new ComboBox();
    this.cmbP8S1 = new ComboBox();
    this.cmbP7S1 = new ComboBox();
    this.cmbP6S1 = new ComboBox();
    this.cmbP5S1 = new ComboBox();
    this.cmbP4S1 = new ComboBox();
    this.cmbP3S1 = new ComboBox();
    this.cmbP2S1 = new ComboBox();
    this.cmbP1S1 = new ComboBox();
    this.chkPort16 = new CheckBox();
    this.chkPort15 = new CheckBox();
    this.chkPort14 = new CheckBox();
    this.chkPort13 = new CheckBox();
    this.chkPort12 = new CheckBox();
    this.chkPort11 = new CheckBox();
    this.chkPort10 = new CheckBox();
    this.chkPort9 = new CheckBox();
    this.chkPort8 = new CheckBox();
    this.chkPort7 = new CheckBox();
    this.chkPort6 = new CheckBox();
    this.chkPort5 = new CheckBox();
    this.chkPort4 = new CheckBox();
    this.chkPort3 = new CheckBox();
    this.chkPort2 = new CheckBox();
    this.chkPort1 = new CheckBox();
    this.txtCcuName = new TextBox();
    this.txtCcuAddress = new TextBox();
    this.lblCcuName = new Label();
    this.lblCcuAddress = new Label();
    this.txtMdchName = new TextBox();
    this.txtMdchAddress = new TextBox();
    this.lblMdchName = new Label();
    this.lblMdchAddress = new Label();
    this.btnNew = new Button();
    this.btnSave = new Button();
    this.btnDelete = new Button();
    this.btnEdit = new Button();
    this.GroupBox1.SuspendLayout();
    this.SuspendLayout();
    this.btnExit.BackColor = SystemColors.ButtonFace;
    this.btnExit.DialogResult = DialogResult.Cancel;
    this.btnExit.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnExit1 = this.btnExit;
    Point point1 = new Point(637, 582);
    Point point2 = point1;
    btnExit1.Location = point2;
    this.btnExit.Name = "btnExit";
    Button btnExit2 = this.btnExit;
    Size size1 = new Size(48 /*0x30*/, 23);
    Size size2 = size1;
    btnExit2.Size = size2;
    this.btnExit.TabIndex = 152;
    this.btnExit.Text = "Exit";
    this.btnExit.UseVisualStyleBackColor = false;
    this.GroupBox1.BackColor = SystemColors.ButtonHighlight;
    this.GroupBox1.Controls.Add((Control) this.btnP16S4);
    this.GroupBox1.Controls.Add((Control) this.btnP15S4);
    this.GroupBox1.Controls.Add((Control) this.btnP14S4);
    this.GroupBox1.Controls.Add((Control) this.btnP13S4);
    this.GroupBox1.Controls.Add((Control) this.btnP12S4);
    this.GroupBox1.Controls.Add((Control) this.btnP11S4);
    this.GroupBox1.Controls.Add((Control) this.btnP10S4);
    this.GroupBox1.Controls.Add((Control) this.btnP9S4);
    this.GroupBox1.Controls.Add((Control) this.btnP8S4);
    this.GroupBox1.Controls.Add((Control) this.btnP7S4);
    this.GroupBox1.Controls.Add((Control) this.btnP6S4);
    this.GroupBox1.Controls.Add((Control) this.btnP5S4);
    this.GroupBox1.Controls.Add((Control) this.btnP4S4);
    this.GroupBox1.Controls.Add((Control) this.btnP3S4);
    this.GroupBox1.Controls.Add((Control) this.btnP2S4);
    this.GroupBox1.Controls.Add((Control) this.btnP1S4);
    this.GroupBox1.Controls.Add((Control) this.cmbP16S4);
    this.GroupBox1.Controls.Add((Control) this.cmbP15S4);
    this.GroupBox1.Controls.Add((Control) this.cmbP14S4);
    this.GroupBox1.Controls.Add((Control) this.cmbP13S4);
    this.GroupBox1.Controls.Add((Control) this.cmbP12S4);
    this.GroupBox1.Controls.Add((Control) this.cmbP11S4);
    this.GroupBox1.Controls.Add((Control) this.cmbP10S4);
    this.GroupBox1.Controls.Add((Control) this.cmbP9S4);
    this.GroupBox1.Controls.Add((Control) this.cmbP8S4);
    this.GroupBox1.Controls.Add((Control) this.cmbP7S4);
    this.GroupBox1.Controls.Add((Control) this.cmbP6S4);
    this.GroupBox1.Controls.Add((Control) this.cmbP5S4);
    this.GroupBox1.Controls.Add((Control) this.cmbP4S4);
    this.GroupBox1.Controls.Add((Control) this.cmbP3S4);
    this.GroupBox1.Controls.Add((Control) this.cmbP2S4);
    this.GroupBox1.Controls.Add((Control) this.cmbP1S4);
    this.GroupBox1.Controls.Add((Control) this.btnP16S3);
    this.GroupBox1.Controls.Add((Control) this.btnP15S3);
    this.GroupBox1.Controls.Add((Control) this.btnP14S3);
    this.GroupBox1.Controls.Add((Control) this.btnP13S3);
    this.GroupBox1.Controls.Add((Control) this.btnP12S3);
    this.GroupBox1.Controls.Add((Control) this.btnP11S3);
    this.GroupBox1.Controls.Add((Control) this.btnP10S3);
    this.GroupBox1.Controls.Add((Control) this.btnP9S3);
    this.GroupBox1.Controls.Add((Control) this.btnP8S3);
    this.GroupBox1.Controls.Add((Control) this.btnP7S3);
    this.GroupBox1.Controls.Add((Control) this.btnP6S3);
    this.GroupBox1.Controls.Add((Control) this.btnP5S3);
    this.GroupBox1.Controls.Add((Control) this.btnP4S3);
    this.GroupBox1.Controls.Add((Control) this.btnP3S3);
    this.GroupBox1.Controls.Add((Control) this.btnP2S3);
    this.GroupBox1.Controls.Add((Control) this.btnP1S3);
    this.GroupBox1.Controls.Add((Control) this.cmbP16S3);
    this.GroupBox1.Controls.Add((Control) this.cmbP15S3);
    this.GroupBox1.Controls.Add((Control) this.cmbP14S3);
    this.GroupBox1.Controls.Add((Control) this.cmbP13S3);
    this.GroupBox1.Controls.Add((Control) this.cmbP12S3);
    this.GroupBox1.Controls.Add((Control) this.cmbP11S3);
    this.GroupBox1.Controls.Add((Control) this.cmbP10S3);
    this.GroupBox1.Controls.Add((Control) this.cmbP9S3);
    this.GroupBox1.Controls.Add((Control) this.cmbP8S3);
    this.GroupBox1.Controls.Add((Control) this.cmbP7S3);
    this.GroupBox1.Controls.Add((Control) this.cmbP6S3);
    this.GroupBox1.Controls.Add((Control) this.cmbP5S3);
    this.GroupBox1.Controls.Add((Control) this.cmbP4S3);
    this.GroupBox1.Controls.Add((Control) this.cmbP3S3);
    this.GroupBox1.Controls.Add((Control) this.cmbP2S3);
    this.GroupBox1.Controls.Add((Control) this.cmbP1S3);
    this.GroupBox1.Controls.Add((Control) this.btnP16S2);
    this.GroupBox1.Controls.Add((Control) this.btnP15S2);
    this.GroupBox1.Controls.Add((Control) this.btnP14S2);
    this.GroupBox1.Controls.Add((Control) this.btnP13S2);
    this.GroupBox1.Controls.Add((Control) this.btnP12S2);
    this.GroupBox1.Controls.Add((Control) this.btnP11S2);
    this.GroupBox1.Controls.Add((Control) this.btnP10S2);
    this.GroupBox1.Controls.Add((Control) this.btnP9S2);
    this.GroupBox1.Controls.Add((Control) this.btnP8S2);
    this.GroupBox1.Controls.Add((Control) this.btnP7S2);
    this.GroupBox1.Controls.Add((Control) this.btnP6S2);
    this.GroupBox1.Controls.Add((Control) this.btnP5S2);
    this.GroupBox1.Controls.Add((Control) this.btnP4S2);
    this.GroupBox1.Controls.Add((Control) this.btnP3S2);
    this.GroupBox1.Controls.Add((Control) this.btnP2S2);
    this.GroupBox1.Controls.Add((Control) this.btnP1S2);
    this.GroupBox1.Controls.Add((Control) this.cmbP16S2);
    this.GroupBox1.Controls.Add((Control) this.cmbP15S2);
    this.GroupBox1.Controls.Add((Control) this.cmbP14S2);
    this.GroupBox1.Controls.Add((Control) this.cmbP13S2);
    this.GroupBox1.Controls.Add((Control) this.cmbP12S2);
    this.GroupBox1.Controls.Add((Control) this.cmbP11S2);
    this.GroupBox1.Controls.Add((Control) this.cmbP10S2);
    this.GroupBox1.Controls.Add((Control) this.cmbP9S2);
    this.GroupBox1.Controls.Add((Control) this.cmbP8S2);
    this.GroupBox1.Controls.Add((Control) this.cmbP7S2);
    this.GroupBox1.Controls.Add((Control) this.cmbP6S2);
    this.GroupBox1.Controls.Add((Control) this.cmbP5S2);
    this.GroupBox1.Controls.Add((Control) this.cmbP4S2);
    this.GroupBox1.Controls.Add((Control) this.cmbP3S2);
    this.GroupBox1.Controls.Add((Control) this.cmbP2S2);
    this.GroupBox1.Controls.Add((Control) this.cmbP1S2);
    this.GroupBox1.Controls.Add((Control) this.btnP16S1);
    this.GroupBox1.Controls.Add((Control) this.btnP15S1);
    this.GroupBox1.Controls.Add((Control) this.btnP14S1);
    this.GroupBox1.Controls.Add((Control) this.btnP13S1);
    this.GroupBox1.Controls.Add((Control) this.btnP12S1);
    this.GroupBox1.Controls.Add((Control) this.btnP11S1);
    this.GroupBox1.Controls.Add((Control) this.btnP10S1);
    this.GroupBox1.Controls.Add((Control) this.btnP9S1);
    this.GroupBox1.Controls.Add((Control) this.btnP8S1);
    this.GroupBox1.Controls.Add((Control) this.btnP7S1);
    this.GroupBox1.Controls.Add((Control) this.btnP6S1);
    this.GroupBox1.Controls.Add((Control) this.btnP5S1);
    this.GroupBox1.Controls.Add((Control) this.btnP4S1);
    this.GroupBox1.Controls.Add((Control) this.btnP3S1);
    this.GroupBox1.Controls.Add((Control) this.btnP2S1);
    this.GroupBox1.Controls.Add((Control) this.btnP1S1);
    this.GroupBox1.Controls.Add((Control) this.cmbP16S1);
    this.GroupBox1.Controls.Add((Control) this.cmbP15S1);
    this.GroupBox1.Controls.Add((Control) this.cmbP14S1);
    this.GroupBox1.Controls.Add((Control) this.cmbP13S1);
    this.GroupBox1.Controls.Add((Control) this.cmbP12S1);
    this.GroupBox1.Controls.Add((Control) this.cmbP11S1);
    this.GroupBox1.Controls.Add((Control) this.cmbP10S1);
    this.GroupBox1.Controls.Add((Control) this.cmbP9S1);
    this.GroupBox1.Controls.Add((Control) this.cmbP8S1);
    this.GroupBox1.Controls.Add((Control) this.cmbP7S1);
    this.GroupBox1.Controls.Add((Control) this.cmbP6S1);
    this.GroupBox1.Controls.Add((Control) this.cmbP5S1);
    this.GroupBox1.Controls.Add((Control) this.cmbP4S1);
    this.GroupBox1.Controls.Add((Control) this.cmbP3S1);
    this.GroupBox1.Controls.Add((Control) this.cmbP2S1);
    this.GroupBox1.Controls.Add((Control) this.cmbP1S1);
    this.GroupBox1.Controls.Add((Control) this.chkPort16);
    this.GroupBox1.Controls.Add((Control) this.chkPort15);
    this.GroupBox1.Controls.Add((Control) this.chkPort14);
    this.GroupBox1.Controls.Add((Control) this.chkPort13);
    this.GroupBox1.Controls.Add((Control) this.chkPort12);
    this.GroupBox1.Controls.Add((Control) this.chkPort11);
    this.GroupBox1.Controls.Add((Control) this.chkPort10);
    this.GroupBox1.Controls.Add((Control) this.chkPort9);
    this.GroupBox1.Controls.Add((Control) this.chkPort8);
    this.GroupBox1.Controls.Add((Control) this.chkPort7);
    this.GroupBox1.Controls.Add((Control) this.chkPort6);
    this.GroupBox1.Controls.Add((Control) this.chkPort5);
    this.GroupBox1.Controls.Add((Control) this.chkPort4);
    this.GroupBox1.Controls.Add((Control) this.chkPort3);
    this.GroupBox1.Controls.Add((Control) this.chkPort2);
    this.GroupBox1.Controls.Add((Control) this.chkPort1);
    this.GroupBox1.Enabled = false;
    this.GroupBox1.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    GroupBox groupBox1_1 = this.GroupBox1;
    point1 = new Point(27, 92);
    Point point3 = point1;
    groupBox1_1.Location = point3;
    this.GroupBox1.Name = "GroupBox1";
    GroupBox groupBox1_2 = this.GroupBox1;
    size1 = new Size(783, 468);
    Size size3 = size1;
    groupBox1_2.Size = size3;
    this.GroupBox1.TabIndex = 150;
    this.GroupBox1.TabStop = false;
    this.GroupBox1.Text = "Port";
    this.btnP16S4.BackColor = Color.LightSalmon;
    Button btnP16S4_1 = this.btnP16S4;
    point1 = new Point(734, 424);
    Point point4 = point1;
    btnP16S4_1.Location = point4;
    this.btnP16S4.Name = "btnP16S4";
    Button btnP16S4_2 = this.btnP16S4;
    size1 = new Size(20, 17);
    Size size4 = size1;
    btnP16S4_2.Size = size4;
    this.btnP16S4.TabIndex = 147;
    this.btnP16S4.Text = " ";
    this.btnP16S4.UseVisualStyleBackColor = false;
    this.btnP16S4.Visible = false;
    this.btnP15S4.BackColor = Color.LightSalmon;
    Button btnP15S4_1 = this.btnP15S4;
    point1 = new Point(734, 397);
    Point point5 = point1;
    btnP15S4_1.Location = point5;
    this.btnP15S4.Name = "btnP15S4";
    Button btnP15S4_2 = this.btnP15S4;
    size1 = new Size(19, 17);
    Size size5 = size1;
    btnP15S4_2.Size = size5;
    this.btnP15S4.TabIndex = 139;
    this.btnP15S4.Text = " ";
    this.btnP15S4.UseVisualStyleBackColor = false;
    this.btnP15S4.Visible = false;
    this.btnP14S4.BackColor = Color.LightSalmon;
    Button btnP14S4_1 = this.btnP14S4;
    point1 = new Point(733, 372);
    Point point6 = point1;
    btnP14S4_1.Location = point6;
    this.btnP14S4.Name = "btnP14S4";
    Button btnP14S4_2 = this.btnP14S4;
    size1 = new Size(20, 17);
    Size size6 = size1;
    btnP14S4_2.Size = size6;
    this.btnP14S4.TabIndex = 131;
    this.btnP14S4.Text = " ";
    this.btnP14S4.UseVisualStyleBackColor = false;
    this.btnP14S4.Visible = false;
    this.btnP13S4.BackColor = Color.LightSalmon;
    Button btnP13S4_1 = this.btnP13S4;
    point1 = new Point(734, 343);
    Point point7 = point1;
    btnP13S4_1.Location = point7;
    this.btnP13S4.Name = "btnP13S4";
    Button btnP13S4_2 = this.btnP13S4;
    size1 = new Size(19, 17);
    Size size7 = size1;
    btnP13S4_2.Size = size7;
    this.btnP13S4.TabIndex = 123;
    this.btnP13S4.Text = " ";
    this.btnP13S4.UseVisualStyleBackColor = false;
    this.btnP13S4.Visible = false;
    this.btnP12S4.BackColor = Color.LightSalmon;
    Button btnP12S4_1 = this.btnP12S4;
    point1 = new Point(734, 314);
    Point point8 = point1;
    btnP12S4_1.Location = point8;
    this.btnP12S4.Name = "btnP12S4";
    Button btnP12S4_2 = this.btnP12S4;
    size1 = new Size(19, 19);
    Size size8 = size1;
    btnP12S4_2.Size = size8;
    this.btnP12S4.TabIndex = 115;
    this.btnP12S4.Text = " ";
    this.btnP12S4.UseVisualStyleBackColor = false;
    this.btnP12S4.Visible = false;
    this.btnP11S4.BackColor = Color.LightSalmon;
    Button btnP11S4_1 = this.btnP11S4;
    point1 = new Point(734, 289);
    Point point9 = point1;
    btnP11S4_1.Location = point9;
    this.btnP11S4.Name = "btnP11S4";
    Button btnP11S4_2 = this.btnP11S4;
    size1 = new Size(19, 17);
    Size size9 = size1;
    btnP11S4_2.Size = size9;
    this.btnP11S4.TabIndex = 108;
    this.btnP11S4.Text = " ";
    this.btnP11S4.UseVisualStyleBackColor = false;
    this.btnP11S4.Visible = false;
    this.btnP10S4.BackColor = Color.LightSalmon;
    Button btnP10S4_1 = this.btnP10S4;
    point1 = new Point(734, 262);
    Point point10 = point1;
    btnP10S4_1.Location = point10;
    this.btnP10S4.Name = "btnP10S4";
    Button btnP10S4_2 = this.btnP10S4;
    size1 = new Size(19, 17);
    Size size10 = size1;
    btnP10S4_2.Size = size10;
    this.btnP10S4.TabIndex = 100;
    this.btnP10S4.Text = " ";
    this.btnP10S4.UseVisualStyleBackColor = false;
    this.btnP10S4.Visible = false;
    this.btnP9S4.BackColor = Color.LightSalmon;
    Button btnP9S4_1 = this.btnP9S4;
    point1 = new Point(734, 235);
    Point point11 = point1;
    btnP9S4_1.Location = point11;
    this.btnP9S4.Name = "btnP9S4";
    Button btnP9S4_2 = this.btnP9S4;
    size1 = new Size(19, 17);
    Size size11 = size1;
    btnP9S4_2.Size = size11;
    this.btnP9S4.TabIndex = 92;
    this.btnP9S4.Text = " ";
    this.btnP9S4.UseVisualStyleBackColor = false;
    this.btnP9S4.Visible = false;
    this.btnP8S4.BackColor = Color.LightSalmon;
    Button btnP8S4_1 = this.btnP8S4;
    point1 = new Point(734, 210);
    Point point12 = point1;
    btnP8S4_1.Location = point12;
    this.btnP8S4.Name = "btnP8S4";
    Button btnP8S4_2 = this.btnP8S4;
    size1 = new Size(17, 18);
    Size size12 = size1;
    btnP8S4_2.Size = size12;
    this.btnP8S4.TabIndex = 84;
    this.btnP8S4.Text = " ";
    this.btnP8S4.UseVisualStyleBackColor = false;
    this.btnP8S4.Visible = false;
    this.btnP7S4.BackColor = Color.LightSalmon;
    Button btnP7S4_1 = this.btnP7S4;
    point1 = new Point(734, 181);
    Point point13 = point1;
    btnP7S4_1.Location = point13;
    this.btnP7S4.Name = "btnP7S4";
    Button btnP7S4_2 = this.btnP7S4;
    size1 = new Size(17, 17);
    Size size13 = size1;
    btnP7S4_2.Size = size13;
    this.btnP7S4.TabIndex = 76;
    this.btnP7S4.Text = " ";
    this.btnP7S4.UseVisualStyleBackColor = false;
    this.btnP7S4.Visible = false;
    this.btnP6S4.BackColor = Color.LightSalmon;
    Button btnP6S4_1 = this.btnP6S4;
    point1 = new Point(734, 152);
    Point point14 = point1;
    btnP6S4_1.Location = point14;
    this.btnP6S4.Name = "btnP6S4";
    Button btnP6S4_2 = this.btnP6S4;
    size1 = new Size(17, 17);
    Size size14 = size1;
    btnP6S4_2.Size = size14;
    this.btnP6S4.TabIndex = 68;
    this.btnP6S4.Text = " ";
    this.btnP6S4.UseVisualStyleBackColor = false;
    this.btnP6S4.Visible = false;
    this.btnP5S4.BackColor = Color.LightSalmon;
    Button btnP5S4_1 = this.btnP5S4;
    point1 = new Point(734, (int) sbyte.MaxValue);
    Point point15 = point1;
    btnP5S4_1.Location = point15;
    this.btnP5S4.Name = "btnP5S4";
    Button btnP5S4_2 = this.btnP5S4;
    size1 = new Size(17, 17);
    Size size15 = size1;
    btnP5S4_2.Size = size15;
    this.btnP5S4.TabIndex = 60;
    this.btnP5S4.Text = " ";
    this.btnP5S4.UseVisualStyleBackColor = false;
    this.btnP5S4.Visible = false;
    this.btnP4S4.BackColor = Color.LightSalmon;
    Button btnP4S4_1 = this.btnP4S4;
    point1 = new Point(734, 100);
    Point point16 = point1;
    btnP4S4_1.Location = point16;
    this.btnP4S4.Name = "btnP4S4";
    Button btnP4S4_2 = this.btnP4S4;
    size1 = new Size(17, 19);
    Size size16 = size1;
    btnP4S4_2.Size = size16;
    this.btnP4S4.TabIndex = 52;
    this.btnP4S4.Text = " ";
    this.btnP4S4.UseVisualStyleBackColor = false;
    this.btnP4S4.Visible = false;
    this.btnP3S4.BackColor = Color.LightSalmon;
    Button btnP3S4_1 = this.btnP3S4;
    point1 = new Point(734, 73);
    Point point17 = point1;
    btnP3S4_1.Location = point17;
    this.btnP3S4.Name = "btnP3S4";
    Button btnP3S4_2 = this.btnP3S4;
    size1 = new Size(17, 19);
    Size size17 = size1;
    btnP3S4_2.Size = size17;
    this.btnP3S4.TabIndex = 44;
    this.btnP3S4.Text = " ";
    this.btnP3S4.UseVisualStyleBackColor = false;
    this.btnP3S4.Visible = false;
    this.btnP2S4.BackColor = Color.LightSalmon;
    Button btnP2S4_1 = this.btnP2S4;
    point1 = new Point(734, 44);
    Point point18 = point1;
    btnP2S4_1.Location = point18;
    this.btnP2S4.Name = "btnP2S4";
    Button btnP2S4_2 = this.btnP2S4;
    size1 = new Size(17, 19);
    Size size18 = size1;
    btnP2S4_2.Size = size18;
    this.btnP2S4.TabIndex = 36;
    this.btnP2S4.Text = " ";
    this.btnP2S4.UseVisualStyleBackColor = false;
    this.btnP2S4.Visible = false;
    this.btnP1S4.BackColor = Color.LightSalmon;
    Button btnP1S4_1 = this.btnP1S4;
    point1 = new Point(734, 17);
    Point point19 = point1;
    btnP1S4_1.Location = point19;
    this.btnP1S4.Name = "btnP1S4";
    Button btnP1S4_2 = this.btnP1S4;
    size1 = new Size(17, 19);
    Size size19 = size1;
    btnP1S4_2.Size = size19;
    this.btnP1S4.TabIndex = 28;
    this.btnP1S4.UseVisualStyleBackColor = false;
    this.btnP1S4.Visible = false;
    this.cmbP16S4.BackColor = SystemColors.Control;
    this.cmbP16S4.FormattingEnabled = true;
    this.cmbP16S4.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP16S4_1 = this.cmbP16S4;
    point1 = new Point(635, 420);
    Point point20 = point1;
    cmbP16S4_1.Location = point20;
    this.cmbP16S4.Name = "cmbP16S4";
    ComboBox cmbP16S4_2 = this.cmbP16S4;
    size1 = new Size(91, 21);
    Size size20 = size1;
    cmbP16S4_2.Size = size20;
    this.cmbP16S4.TabIndex = 146;
    this.cmbP16S4.Visible = false;
    this.cmbP15S4.BackColor = SystemColors.Control;
    this.cmbP15S4.FormattingEnabled = true;
    this.cmbP15S4.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP15S4_1 = this.cmbP15S4;
    point1 = new Point(635, 393);
    Point point21 = point1;
    cmbP15S4_1.Location = point21;
    this.cmbP15S4.Name = "cmbP15S4";
    ComboBox cmbP15S4_2 = this.cmbP15S4;
    size1 = new Size(91, 21);
    Size size21 = size1;
    cmbP15S4_2.Size = size21;
    this.cmbP15S4.TabIndex = 138;
    this.cmbP15S4.Visible = false;
    this.cmbP14S4.BackColor = SystemColors.Control;
    this.cmbP14S4.FormattingEnabled = true;
    this.cmbP14S4.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP14S4_1 = this.cmbP14S4;
    point1 = new Point(635, 366);
    Point point22 = point1;
    cmbP14S4_1.Location = point22;
    this.cmbP14S4.Name = "cmbP14S4";
    ComboBox cmbP14S4_2 = this.cmbP14S4;
    size1 = new Size(91, 21);
    Size size22 = size1;
    cmbP14S4_2.Size = size22;
    this.cmbP14S4.TabIndex = 130;
    this.cmbP14S4.Visible = false;
    this.cmbP13S4.BackColor = SystemColors.Control;
    this.cmbP13S4.FormattingEnabled = true;
    this.cmbP13S4.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP13S4_1 = this.cmbP13S4;
    point1 = new Point(635, 339);
    Point point23 = point1;
    cmbP13S4_1.Location = point23;
    this.cmbP13S4.Name = "cmbP13S4";
    ComboBox cmbP13S4_2 = this.cmbP13S4;
    size1 = new Size(91, 21);
    Size size23 = size1;
    cmbP13S4_2.Size = size23;
    this.cmbP13S4.TabIndex = 122;
    this.cmbP13S4.Visible = false;
    this.cmbP12S4.BackColor = SystemColors.Control;
    this.cmbP12S4.FormattingEnabled = true;
    this.cmbP12S4.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP12S4_1 = this.cmbP12S4;
    point1 = new Point(635, 312);
    Point point24 = point1;
    cmbP12S4_1.Location = point24;
    this.cmbP12S4.Name = "cmbP12S4";
    ComboBox cmbP12S4_2 = this.cmbP12S4;
    size1 = new Size(91, 21);
    Size size24 = size1;
    cmbP12S4_2.Size = size24;
    this.cmbP12S4.TabIndex = 114;
    this.cmbP12S4.Visible = false;
    this.cmbP11S4.BackColor = SystemColors.Control;
    this.cmbP11S4.FormattingEnabled = true;
    this.cmbP11S4.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP11S4_1 = this.cmbP11S4;
    point1 = new Point(635, 285);
    Point point25 = point1;
    cmbP11S4_1.Location = point25;
    this.cmbP11S4.Name = "cmbP11S4";
    ComboBox cmbP11S4_2 = this.cmbP11S4;
    size1 = new Size(91, 21);
    Size size25 = size1;
    cmbP11S4_2.Size = size25;
    this.cmbP11S4.TabIndex = 107;
    this.cmbP11S4.Visible = false;
    this.cmbP10S4.BackColor = SystemColors.Control;
    this.cmbP10S4.FormattingEnabled = true;
    this.cmbP10S4.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP10S4_1 = this.cmbP10S4;
    point1 = new Point(635, 258);
    Point point26 = point1;
    cmbP10S4_1.Location = point26;
    this.cmbP10S4.Name = "cmbP10S4";
    ComboBox cmbP10S4_2 = this.cmbP10S4;
    size1 = new Size(91, 21);
    Size size26 = size1;
    cmbP10S4_2.Size = size26;
    this.cmbP10S4.TabIndex = 99;
    this.cmbP10S4.Visible = false;
    this.cmbP9S4.BackColor = SystemColors.Control;
    this.cmbP9S4.FormattingEnabled = true;
    this.cmbP9S4.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP9S4_1 = this.cmbP9S4;
    point1 = new Point(635, 231);
    Point point27 = point1;
    cmbP9S4_1.Location = point27;
    this.cmbP9S4.Name = "cmbP9S4";
    ComboBox cmbP9S4_2 = this.cmbP9S4;
    size1 = new Size(91, 21);
    Size size27 = size1;
    cmbP9S4_2.Size = size27;
    this.cmbP9S4.TabIndex = 91;
    this.cmbP9S4.Visible = false;
    this.cmbP8S4.BackColor = SystemColors.Control;
    this.cmbP8S4.FormattingEnabled = true;
    this.cmbP8S4.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP8S4_1 = this.cmbP8S4;
    point1 = new Point(635, 204);
    Point point28 = point1;
    cmbP8S4_1.Location = point28;
    this.cmbP8S4.Name = "cmbP8S4";
    ComboBox cmbP8S4_2 = this.cmbP8S4;
    size1 = new Size(91, 21);
    Size size28 = size1;
    cmbP8S4_2.Size = size28;
    this.cmbP8S4.TabIndex = 83;
    this.cmbP8S4.Visible = false;
    this.cmbP7S4.BackColor = SystemColors.Control;
    this.cmbP7S4.FormattingEnabled = true;
    this.cmbP7S4.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP7S4_1 = this.cmbP7S4;
    point1 = new Point(635, 177);
    Point point29 = point1;
    cmbP7S4_1.Location = point29;
    this.cmbP7S4.Name = "cmbP7S4";
    ComboBox cmbP7S4_2 = this.cmbP7S4;
    size1 = new Size(91, 21);
    Size size29 = size1;
    cmbP7S4_2.Size = size29;
    this.cmbP7S4.TabIndex = 75;
    this.cmbP7S4.Visible = false;
    this.cmbP6S4.BackColor = SystemColors.Control;
    this.cmbP6S4.FormattingEnabled = true;
    this.cmbP6S4.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP6S4_1 = this.cmbP6S4;
    point1 = new Point(635, 150);
    Point point30 = point1;
    cmbP6S4_1.Location = point30;
    this.cmbP6S4.Name = "cmbP6S4";
    ComboBox cmbP6S4_2 = this.cmbP6S4;
    size1 = new Size(91, 21);
    Size size30 = size1;
    cmbP6S4_2.Size = size30;
    this.cmbP6S4.TabIndex = 67;
    this.cmbP6S4.Visible = false;
    this.cmbP5S4.BackColor = SystemColors.Control;
    this.cmbP5S4.FormattingEnabled = true;
    this.cmbP5S4.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP5S4_1 = this.cmbP5S4;
    point1 = new Point(635, 123);
    Point point31 = point1;
    cmbP5S4_1.Location = point31;
    this.cmbP5S4.Name = "cmbP5S4";
    ComboBox cmbP5S4_2 = this.cmbP5S4;
    size1 = new Size(91, 21);
    Size size31 = size1;
    cmbP5S4_2.Size = size31;
    this.cmbP5S4.TabIndex = 59;
    this.cmbP5S4.Visible = false;
    this.cmbP4S4.BackColor = SystemColors.Control;
    this.cmbP4S4.FormattingEnabled = true;
    this.cmbP4S4.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP4S4_1 = this.cmbP4S4;
    point1 = new Point(635, 96 /*0x60*/);
    Point point32 = point1;
    cmbP4S4_1.Location = point32;
    this.cmbP4S4.Name = "cmbP4S4";
    ComboBox cmbP4S4_2 = this.cmbP4S4;
    size1 = new Size(91, 21);
    Size size32 = size1;
    cmbP4S4_2.Size = size32;
    this.cmbP4S4.TabIndex = 51;
    this.cmbP4S4.Visible = false;
    this.cmbP3S4.BackColor = SystemColors.Control;
    this.cmbP3S4.FormattingEnabled = true;
    this.cmbP3S4.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP3S4_1 = this.cmbP3S4;
    point1 = new Point(635, 69);
    Point point33 = point1;
    cmbP3S4_1.Location = point33;
    this.cmbP3S4.Name = "cmbP3S4";
    ComboBox cmbP3S4_2 = this.cmbP3S4;
    size1 = new Size(91, 21);
    Size size33 = size1;
    cmbP3S4_2.Size = size33;
    this.cmbP3S4.TabIndex = 43;
    this.cmbP3S4.Visible = false;
    this.cmbP2S4.BackColor = SystemColors.Control;
    this.cmbP2S4.FormattingEnabled = true;
    this.cmbP2S4.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP2S4_1 = this.cmbP2S4;
    point1 = new Point(635, 42);
    Point point34 = point1;
    cmbP2S4_1.Location = point34;
    this.cmbP2S4.Name = "cmbP2S4";
    ComboBox cmbP2S4_2 = this.cmbP2S4;
    size1 = new Size(91, 21);
    Size size34 = size1;
    cmbP2S4_2.Size = size34;
    this.cmbP2S4.TabIndex = 35;
    this.cmbP2S4.Visible = false;
    this.cmbP1S4.BackColor = SystemColors.Control;
    this.cmbP1S4.FormattingEnabled = true;
    this.cmbP1S4.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP1S4_1 = this.cmbP1S4;
    point1 = new Point(635, 15);
    Point point35 = point1;
    cmbP1S4_1.Location = point35;
    this.cmbP1S4.Name = "cmbP1S4";
    ComboBox cmbP1S4_2 = this.cmbP1S4;
    size1 = new Size(91, 21);
    Size size35 = size1;
    cmbP1S4_2.Size = size35;
    this.cmbP1S4.TabIndex = 27;
    this.cmbP1S4.Visible = false;
    this.btnP16S3.BackColor = Color.LightSalmon;
    Button btnP16S3_1 = this.btnP16S3;
    point1 = new Point(567, 422);
    Point point36 = point1;
    btnP16S3_1.Location = point36;
    this.btnP16S3.Name = "btnP16S3";
    Button btnP16S3_2 = this.btnP16S3;
    size1 = new Size(20, 17);
    Size size36 = size1;
    btnP16S3_2.Size = size36;
    this.btnP16S3.TabIndex = 145;
    this.btnP16S3.Text = " ";
    this.btnP16S3.UseVisualStyleBackColor = false;
    this.btnP16S3.Visible = false;
    this.btnP15S3.BackColor = Color.LightSalmon;
    Button btnP15S3_1 = this.btnP15S3;
    point1 = new Point(567, 395);
    Point point37 = point1;
    btnP15S3_1.Location = point37;
    this.btnP15S3.Name = "btnP15S3";
    Button btnP15S3_2 = this.btnP15S3;
    size1 = new Size(19, 17);
    Size size37 = size1;
    btnP15S3_2.Size = size37;
    this.btnP15S3.TabIndex = 137;
    this.btnP15S3.Text = " ";
    this.btnP15S3.UseVisualStyleBackColor = false;
    this.btnP15S3.Visible = false;
    this.btnP14S3.BackColor = Color.LightSalmon;
    Button btnP14S3_1 = this.btnP14S3;
    point1 = new Point(566, 370);
    Point point38 = point1;
    btnP14S3_1.Location = point38;
    this.btnP14S3.Name = "btnP14S3";
    Button btnP14S3_2 = this.btnP14S3;
    size1 = new Size(20, 17);
    Size size38 = size1;
    btnP14S3_2.Size = size38;
    this.btnP14S3.TabIndex = 129;
    this.btnP14S3.Text = " ";
    this.btnP14S3.UseVisualStyleBackColor = false;
    this.btnP14S3.Visible = false;
    this.btnP13S3.BackColor = Color.LightSalmon;
    Button btnP13S3_1 = this.btnP13S3;
    point1 = new Point(567, 341);
    Point point39 = point1;
    btnP13S3_1.Location = point39;
    this.btnP13S3.Name = "btnP13S3";
    Button btnP13S3_2 = this.btnP13S3;
    size1 = new Size(19, 17);
    Size size39 = size1;
    btnP13S3_2.Size = size39;
    this.btnP13S3.TabIndex = 121;
    this.btnP13S3.Text = " ";
    this.btnP13S3.UseVisualStyleBackColor = false;
    this.btnP13S3.Visible = false;
    this.btnP12S3.BackColor = Color.LightSalmon;
    Button btnP12S3_1 = this.btnP12S3;
    point1 = new Point(567, 312);
    Point point40 = point1;
    btnP12S3_1.Location = point40;
    this.btnP12S3.Name = "btnP12S3";
    Button btnP12S3_2 = this.btnP12S3;
    size1 = new Size(19, 19);
    Size size40 = size1;
    btnP12S3_2.Size = size40;
    this.btnP12S3.TabIndex = 113;
    this.btnP12S3.Text = " ";
    this.btnP12S3.UseVisualStyleBackColor = false;
    this.btnP12S3.Visible = false;
    this.btnP11S3.BackColor = Color.LightSalmon;
    Button btnP11S3_1 = this.btnP11S3;
    point1 = new Point(567, 287);
    Point point41 = point1;
    btnP11S3_1.Location = point41;
    this.btnP11S3.Name = "btnP11S3";
    Button btnP11S3_2 = this.btnP11S3;
    size1 = new Size(19, 17);
    Size size41 = size1;
    btnP11S3_2.Size = size41;
    this.btnP11S3.TabIndex = 106;
    this.btnP11S3.Text = " ";
    this.btnP11S3.UseVisualStyleBackColor = false;
    this.btnP11S3.Visible = false;
    this.btnP10S3.BackColor = Color.LightSalmon;
    Button btnP10S3_1 = this.btnP10S3;
    point1 = new Point(567, 260);
    Point point42 = point1;
    btnP10S3_1.Location = point42;
    this.btnP10S3.Name = "btnP10S3";
    Button btnP10S3_2 = this.btnP10S3;
    size1 = new Size(19, 17);
    Size size42 = size1;
    btnP10S3_2.Size = size42;
    this.btnP10S3.TabIndex = 98;
    this.btnP10S3.Text = " ";
    this.btnP10S3.UseVisualStyleBackColor = false;
    this.btnP10S3.Visible = false;
    this.btnP9S3.BackColor = Color.LightSalmon;
    Button btnP9S3_1 = this.btnP9S3;
    point1 = new Point(567, 233);
    Point point43 = point1;
    btnP9S3_1.Location = point43;
    this.btnP9S3.Name = "btnP9S3";
    Button btnP9S3_2 = this.btnP9S3;
    size1 = new Size(19, 17);
    Size size43 = size1;
    btnP9S3_2.Size = size43;
    this.btnP9S3.TabIndex = 90;
    this.btnP9S3.Text = " ";
    this.btnP9S3.UseVisualStyleBackColor = false;
    this.btnP9S3.Visible = false;
    this.btnP8S3.BackColor = Color.LightSalmon;
    Button btnP8S3_1 = this.btnP8S3;
    point1 = new Point(567, 208 /*0xD0*/);
    Point point44 = point1;
    btnP8S3_1.Location = point44;
    this.btnP8S3.Name = "btnP8S3";
    Button btnP8S3_2 = this.btnP8S3;
    size1 = new Size(17, 18);
    Size size44 = size1;
    btnP8S3_2.Size = size44;
    this.btnP8S3.TabIndex = 82;
    this.btnP8S3.Text = " ";
    this.btnP8S3.UseVisualStyleBackColor = false;
    this.btnP8S3.Visible = false;
    this.btnP7S3.BackColor = Color.LightSalmon;
    Button btnP7S3_1 = this.btnP7S3;
    point1 = new Point(567, 179);
    Point point45 = point1;
    btnP7S3_1.Location = point45;
    this.btnP7S3.Name = "btnP7S3";
    Button btnP7S3_2 = this.btnP7S3;
    size1 = new Size(17, 17);
    Size size45 = size1;
    btnP7S3_2.Size = size45;
    this.btnP7S3.TabIndex = 74;
    this.btnP7S3.Text = " ";
    this.btnP7S3.UseVisualStyleBackColor = false;
    this.btnP7S3.Visible = false;
    this.btnP6S3.BackColor = Color.LightSalmon;
    Button btnP6S3_1 = this.btnP6S3;
    point1 = new Point(567, 150);
    Point point46 = point1;
    btnP6S3_1.Location = point46;
    this.btnP6S3.Name = "btnP6S3";
    Button btnP6S3_2 = this.btnP6S3;
    size1 = new Size(17, 17);
    Size size46 = size1;
    btnP6S3_2.Size = size46;
    this.btnP6S3.TabIndex = 66;
    this.btnP6S3.Text = " ";
    this.btnP6S3.UseVisualStyleBackColor = false;
    this.btnP6S3.Visible = false;
    this.btnP5S3.BackColor = Color.LightSalmon;
    Button btnP5S3_1 = this.btnP5S3;
    point1 = new Point(567, 125);
    Point point47 = point1;
    btnP5S3_1.Location = point47;
    this.btnP5S3.Name = "btnP5S3";
    Button btnP5S3_2 = this.btnP5S3;
    size1 = new Size(17, 17);
    Size size47 = size1;
    btnP5S3_2.Size = size47;
    this.btnP5S3.TabIndex = 58;
    this.btnP5S3.Text = " ";
    this.btnP5S3.UseVisualStyleBackColor = false;
    this.btnP5S3.Visible = false;
    this.btnP4S3.BackColor = Color.LightSalmon;
    Button btnP4S3_1 = this.btnP4S3;
    point1 = new Point(567, 98);
    Point point48 = point1;
    btnP4S3_1.Location = point48;
    this.btnP4S3.Name = "btnP4S3";
    Button btnP4S3_2 = this.btnP4S3;
    size1 = new Size(17, 19);
    Size size48 = size1;
    btnP4S3_2.Size = size48;
    this.btnP4S3.TabIndex = 50;
    this.btnP4S3.Text = " ";
    this.btnP4S3.UseVisualStyleBackColor = false;
    this.btnP4S3.Visible = false;
    this.btnP3S3.BackColor = Color.LightSalmon;
    Button btnP3S3_1 = this.btnP3S3;
    point1 = new Point(567, 71);
    Point point49 = point1;
    btnP3S3_1.Location = point49;
    this.btnP3S3.Name = "btnP3S3";
    Button btnP3S3_2 = this.btnP3S3;
    size1 = new Size(17, 19);
    Size size49 = size1;
    btnP3S3_2.Size = size49;
    this.btnP3S3.TabIndex = 42;
    this.btnP3S3.Text = " ";
    this.btnP3S3.UseVisualStyleBackColor = false;
    this.btnP3S3.Visible = false;
    this.btnP2S3.BackColor = Color.LightSalmon;
    Button btnP2S3_1 = this.btnP2S3;
    point1 = new Point(567, 42);
    Point point50 = point1;
    btnP2S3_1.Location = point50;
    this.btnP2S3.Name = "btnP2S3";
    Button btnP2S3_2 = this.btnP2S3;
    size1 = new Size(17, 19);
    Size size50 = size1;
    btnP2S3_2.Size = size50;
    this.btnP2S3.TabIndex = 34;
    this.btnP2S3.Text = " ";
    this.btnP2S3.UseVisualStyleBackColor = false;
    this.btnP2S3.Visible = false;
    this.btnP1S3.BackColor = Color.LightSalmon;
    Button btnP1S3_1 = this.btnP1S3;
    point1 = new Point(567, 15);
    Point point51 = point1;
    btnP1S3_1.Location = point51;
    this.btnP1S3.Name = "btnP1S3";
    Button btnP1S3_2 = this.btnP1S3;
    size1 = new Size(17, 19);
    Size size51 = size1;
    btnP1S3_2.Size = size51;
    this.btnP1S3.TabIndex = 26;
    this.btnP1S3.UseVisualStyleBackColor = false;
    this.btnP1S3.Visible = false;
    this.cmbP16S3.BackColor = SystemColors.Control;
    this.cmbP16S3.FormattingEnabled = true;
    this.cmbP16S3.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP16S3_1 = this.cmbP16S3;
    point1 = new Point(468, 418);
    Point point52 = point1;
    cmbP16S3_1.Location = point52;
    this.cmbP16S3.Name = "cmbP16S3";
    ComboBox cmbP16S3_2 = this.cmbP16S3;
    size1 = new Size(91, 21);
    Size size52 = size1;
    cmbP16S3_2.Size = size52;
    this.cmbP16S3.TabIndex = 144 /*0x90*/;
    this.cmbP16S3.Visible = false;
    this.cmbP15S3.BackColor = SystemColors.Control;
    this.cmbP15S3.FormattingEnabled = true;
    this.cmbP15S3.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP15S3_1 = this.cmbP15S3;
    point1 = new Point(468, 391);
    Point point53 = point1;
    cmbP15S3_1.Location = point53;
    this.cmbP15S3.Name = "cmbP15S3";
    ComboBox cmbP15S3_2 = this.cmbP15S3;
    size1 = new Size(91, 21);
    Size size53 = size1;
    cmbP15S3_2.Size = size53;
    this.cmbP15S3.TabIndex = 136;
    this.cmbP15S3.Visible = false;
    this.cmbP14S3.BackColor = SystemColors.Control;
    this.cmbP14S3.FormattingEnabled = true;
    this.cmbP14S3.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP14S3_1 = this.cmbP14S3;
    point1 = new Point(468, 364);
    Point point54 = point1;
    cmbP14S3_1.Location = point54;
    this.cmbP14S3.Name = "cmbP14S3";
    ComboBox cmbP14S3_2 = this.cmbP14S3;
    size1 = new Size(91, 21);
    Size size54 = size1;
    cmbP14S3_2.Size = size54;
    this.cmbP14S3.TabIndex = 128 /*0x80*/;
    this.cmbP14S3.Visible = false;
    this.cmbP13S3.BackColor = SystemColors.Control;
    this.cmbP13S3.FormattingEnabled = true;
    this.cmbP13S3.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP13S3_1 = this.cmbP13S3;
    point1 = new Point(468, 337);
    Point point55 = point1;
    cmbP13S3_1.Location = point55;
    this.cmbP13S3.Name = "cmbP13S3";
    ComboBox cmbP13S3_2 = this.cmbP13S3;
    size1 = new Size(91, 21);
    Size size55 = size1;
    cmbP13S3_2.Size = size55;
    this.cmbP13S3.TabIndex = 120;
    this.cmbP13S3.Visible = false;
    this.cmbP12S3.BackColor = SystemColors.Control;
    this.cmbP12S3.FormattingEnabled = true;
    this.cmbP12S3.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP12S3_1 = this.cmbP12S3;
    point1 = new Point(468, 310);
    Point point56 = point1;
    cmbP12S3_1.Location = point56;
    this.cmbP12S3.Name = "cmbP12S3";
    ComboBox cmbP12S3_2 = this.cmbP12S3;
    size1 = new Size(91, 21);
    Size size56 = size1;
    cmbP12S3_2.Size = size56;
    this.cmbP12S3.TabIndex = 112 /*0x70*/;
    this.cmbP12S3.Visible = false;
    this.cmbP11S3.BackColor = SystemColors.Control;
    this.cmbP11S3.FormattingEnabled = true;
    this.cmbP11S3.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP11S3_1 = this.cmbP11S3;
    point1 = new Point(468, 283);
    Point point57 = point1;
    cmbP11S3_1.Location = point57;
    this.cmbP11S3.Name = "cmbP11S3";
    ComboBox cmbP11S3_2 = this.cmbP11S3;
    size1 = new Size(91, 21);
    Size size57 = size1;
    cmbP11S3_2.Size = size57;
    this.cmbP11S3.TabIndex = 105;
    this.cmbP11S3.Visible = false;
    this.cmbP10S3.BackColor = SystemColors.Control;
    this.cmbP10S3.FormattingEnabled = true;
    this.cmbP10S3.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP10S3_1 = this.cmbP10S3;
    point1 = new Point(468, 256 /*0x0100*/);
    Point point58 = point1;
    cmbP10S3_1.Location = point58;
    this.cmbP10S3.Name = "cmbP10S3";
    ComboBox cmbP10S3_2 = this.cmbP10S3;
    size1 = new Size(91, 21);
    Size size58 = size1;
    cmbP10S3_2.Size = size58;
    this.cmbP10S3.TabIndex = 97;
    this.cmbP10S3.Visible = false;
    this.cmbP9S3.BackColor = SystemColors.Control;
    this.cmbP9S3.FormattingEnabled = true;
    this.cmbP9S3.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP9S3_1 = this.cmbP9S3;
    point1 = new Point(468, 229);
    Point point59 = point1;
    cmbP9S3_1.Location = point59;
    this.cmbP9S3.Name = "cmbP9S3";
    ComboBox cmbP9S3_2 = this.cmbP9S3;
    size1 = new Size(91, 21);
    Size size59 = size1;
    cmbP9S3_2.Size = size59;
    this.cmbP9S3.TabIndex = 89;
    this.cmbP9S3.Visible = false;
    this.cmbP8S3.BackColor = SystemColors.Control;
    this.cmbP8S3.FormattingEnabled = true;
    this.cmbP8S3.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP8S3_1 = this.cmbP8S3;
    point1 = new Point(468, 202);
    Point point60 = point1;
    cmbP8S3_1.Location = point60;
    this.cmbP8S3.Name = "cmbP8S3";
    ComboBox cmbP8S3_2 = this.cmbP8S3;
    size1 = new Size(91, 21);
    Size size60 = size1;
    cmbP8S3_2.Size = size60;
    this.cmbP8S3.TabIndex = 81;
    this.cmbP8S3.Visible = false;
    this.cmbP7S3.BackColor = SystemColors.Control;
    this.cmbP7S3.FormattingEnabled = true;
    this.cmbP7S3.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP7S3_1 = this.cmbP7S3;
    point1 = new Point(468, 175);
    Point point61 = point1;
    cmbP7S3_1.Location = point61;
    this.cmbP7S3.Name = "cmbP7S3";
    ComboBox cmbP7S3_2 = this.cmbP7S3;
    size1 = new Size(91, 21);
    Size size61 = size1;
    cmbP7S3_2.Size = size61;
    this.cmbP7S3.TabIndex = 73;
    this.cmbP7S3.Visible = false;
    this.cmbP6S3.BackColor = SystemColors.Control;
    this.cmbP6S3.FormattingEnabled = true;
    this.cmbP6S3.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP6S3_1 = this.cmbP6S3;
    point1 = new Point(468, 148);
    Point point62 = point1;
    cmbP6S3_1.Location = point62;
    this.cmbP6S3.Name = "cmbP6S3";
    ComboBox cmbP6S3_2 = this.cmbP6S3;
    size1 = new Size(91, 21);
    Size size62 = size1;
    cmbP6S3_2.Size = size62;
    this.cmbP6S3.TabIndex = 65;
    this.cmbP6S3.Visible = false;
    this.cmbP5S3.BackColor = SystemColors.Control;
    this.cmbP5S3.FormattingEnabled = true;
    this.cmbP5S3.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP5S3_1 = this.cmbP5S3;
    point1 = new Point(468, 121);
    Point point63 = point1;
    cmbP5S3_1.Location = point63;
    this.cmbP5S3.Name = "cmbP5S3";
    ComboBox cmbP5S3_2 = this.cmbP5S3;
    size1 = new Size(91, 21);
    Size size63 = size1;
    cmbP5S3_2.Size = size63;
    this.cmbP5S3.TabIndex = 57;
    this.cmbP5S3.Visible = false;
    this.cmbP4S3.BackColor = SystemColors.Control;
    this.cmbP4S3.FormattingEnabled = true;
    this.cmbP4S3.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP4S3_1 = this.cmbP4S3;
    point1 = new Point(468, 94);
    Point point64 = point1;
    cmbP4S3_1.Location = point64;
    this.cmbP4S3.Name = "cmbP4S3";
    ComboBox cmbP4S3_2 = this.cmbP4S3;
    size1 = new Size(91, 21);
    Size size64 = size1;
    cmbP4S3_2.Size = size64;
    this.cmbP4S3.TabIndex = 49;
    this.cmbP4S3.Visible = false;
    this.cmbP3S3.BackColor = SystemColors.Control;
    this.cmbP3S3.FormattingEnabled = true;
    this.cmbP3S3.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP3S3_1 = this.cmbP3S3;
    point1 = new Point(468, 67);
    Point point65 = point1;
    cmbP3S3_1.Location = point65;
    this.cmbP3S3.Name = "cmbP3S3";
    ComboBox cmbP3S3_2 = this.cmbP3S3;
    size1 = new Size(91, 21);
    Size size65 = size1;
    cmbP3S3_2.Size = size65;
    this.cmbP3S3.TabIndex = 41;
    this.cmbP3S3.Visible = false;
    this.cmbP2S3.BackColor = SystemColors.Control;
    this.cmbP2S3.FormattingEnabled = true;
    this.cmbP2S3.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP2S3_1 = this.cmbP2S3;
    point1 = new Point(468, 40);
    Point point66 = point1;
    cmbP2S3_1.Location = point66;
    this.cmbP2S3.Name = "cmbP2S3";
    ComboBox cmbP2S3_2 = this.cmbP2S3;
    size1 = new Size(91, 21);
    Size size66 = size1;
    cmbP2S3_2.Size = size66;
    this.cmbP2S3.TabIndex = 33;
    this.cmbP2S3.Visible = false;
    this.cmbP1S3.BackColor = SystemColors.Control;
    this.cmbP1S3.FormattingEnabled = true;
    this.cmbP1S3.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP1S3_1 = this.cmbP1S3;
    point1 = new Point(468, 13);
    Point point67 = point1;
    cmbP1S3_1.Location = point67;
    this.cmbP1S3.Name = "cmbP1S3";
    ComboBox cmbP1S3_2 = this.cmbP1S3;
    size1 = new Size(91, 21);
    Size size67 = size1;
    cmbP1S3_2.Size = size67;
    this.cmbP1S3.TabIndex = 25;
    this.cmbP1S3.Visible = false;
    this.btnP16S2.BackColor = Color.LightSalmon;
    Button btnP16S2_1 = this.btnP16S2;
    point1 = new Point(405, 421);
    Point point68 = point1;
    btnP16S2_1.Location = point68;
    this.btnP16S2.Name = "btnP16S2";
    Button btnP16S2_2 = this.btnP16S2;
    size1 = new Size(20, 17);
    Size size68 = size1;
    btnP16S2_2.Size = size68;
    this.btnP16S2.TabIndex = 143;
    this.btnP16S2.Text = " ";
    this.btnP16S2.UseVisualStyleBackColor = false;
    this.btnP16S2.Visible = false;
    this.btnP15S2.BackColor = Color.LightSalmon;
    Button btnP15S2_1 = this.btnP15S2;
    point1 = new Point(405, 394);
    Point point69 = point1;
    btnP15S2_1.Location = point69;
    this.btnP15S2.Name = "btnP15S2";
    Button btnP15S2_2 = this.btnP15S2;
    size1 = new Size(19, 17);
    Size size69 = size1;
    btnP15S2_2.Size = size69;
    this.btnP15S2.TabIndex = 135;
    this.btnP15S2.Text = " ";
    this.btnP15S2.UseVisualStyleBackColor = false;
    this.btnP15S2.Visible = false;
    this.btnP14S2.BackColor = Color.LightSalmon;
    Button btnP14S2_1 = this.btnP14S2;
    point1 = new Point(404, 369);
    Point point70 = point1;
    btnP14S2_1.Location = point70;
    this.btnP14S2.Name = "btnP14S2";
    Button btnP14S2_2 = this.btnP14S2;
    size1 = new Size(20, 17);
    Size size70 = size1;
    btnP14S2_2.Size = size70;
    this.btnP14S2.TabIndex = (int) sbyte.MaxValue;
    this.btnP14S2.Text = " ";
    this.btnP14S2.UseVisualStyleBackColor = false;
    this.btnP14S2.Visible = false;
    this.btnP13S2.BackColor = Color.LightSalmon;
    Button btnP13S2_1 = this.btnP13S2;
    point1 = new Point(405, 340);
    Point point71 = point1;
    btnP13S2_1.Location = point71;
    this.btnP13S2.Name = "btnP13S2";
    Button btnP13S2_2 = this.btnP13S2;
    size1 = new Size(19, 17);
    Size size71 = size1;
    btnP13S2_2.Size = size71;
    this.btnP13S2.TabIndex = 119;
    this.btnP13S2.Text = " ";
    this.btnP13S2.UseVisualStyleBackColor = false;
    this.btnP13S2.Visible = false;
    this.btnP12S2.BackColor = Color.LightSalmon;
    Button btnP12S2_1 = this.btnP12S2;
    point1 = new Point(405, 311);
    Point point72 = point1;
    btnP12S2_1.Location = point72;
    this.btnP12S2.Name = "btnP12S2";
    Button btnP12S2_2 = this.btnP12S2;
    size1 = new Size(19, 19);
    Size size72 = size1;
    btnP12S2_2.Size = size72;
    this.btnP12S2.TabIndex = 111;
    this.btnP12S2.Text = " ";
    this.btnP12S2.UseVisualStyleBackColor = false;
    this.btnP12S2.Visible = false;
    this.btnP11S2.BackColor = Color.LightSalmon;
    Button btnP11S2_1 = this.btnP11S2;
    point1 = new Point(405, 286);
    Point point73 = point1;
    btnP11S2_1.Location = point73;
    this.btnP11S2.Name = "btnP11S2";
    Button btnP11S2_2 = this.btnP11S2;
    size1 = new Size(19, 17);
    Size size73 = size1;
    btnP11S2_2.Size = size73;
    this.btnP11S2.TabIndex = 104;
    this.btnP11S2.Text = " ";
    this.btnP11S2.UseVisualStyleBackColor = false;
    this.btnP11S2.Visible = false;
    this.btnP10S2.BackColor = Color.LightSalmon;
    Button btnP10S2_1 = this.btnP10S2;
    point1 = new Point(405, 259);
    Point point74 = point1;
    btnP10S2_1.Location = point74;
    this.btnP10S2.Name = "btnP10S2";
    Button btnP10S2_2 = this.btnP10S2;
    size1 = new Size(19, 17);
    Size size74 = size1;
    btnP10S2_2.Size = size74;
    this.btnP10S2.TabIndex = 96 /*0x60*/;
    this.btnP10S2.Text = " ";
    this.btnP10S2.UseVisualStyleBackColor = false;
    this.btnP10S2.Visible = false;
    this.btnP9S2.BackColor = Color.LightSalmon;
    Button btnP9S2_1 = this.btnP9S2;
    point1 = new Point(405, 232);
    Point point75 = point1;
    btnP9S2_1.Location = point75;
    this.btnP9S2.Name = "btnP9S2";
    Button btnP9S2_2 = this.btnP9S2;
    size1 = new Size(19, 17);
    Size size75 = size1;
    btnP9S2_2.Size = size75;
    this.btnP9S2.TabIndex = 88;
    this.btnP9S2.Text = " ";
    this.btnP9S2.UseVisualStyleBackColor = false;
    this.btnP9S2.Visible = false;
    this.btnP8S2.BackColor = Color.LightSalmon;
    Button btnP8S2_1 = this.btnP8S2;
    point1 = new Point(405, 207);
    Point point76 = point1;
    btnP8S2_1.Location = point76;
    this.btnP8S2.Name = "btnP8S2";
    Button btnP8S2_2 = this.btnP8S2;
    size1 = new Size(17, 18);
    Size size76 = size1;
    btnP8S2_2.Size = size76;
    this.btnP8S2.TabIndex = 80 /*0x50*/;
    this.btnP8S2.Text = " ";
    this.btnP8S2.UseVisualStyleBackColor = false;
    this.btnP8S2.Visible = false;
    this.btnP7S2.BackColor = Color.LightSalmon;
    Button btnP7S2_1 = this.btnP7S2;
    point1 = new Point(405, 178);
    Point point77 = point1;
    btnP7S2_1.Location = point77;
    this.btnP7S2.Name = "btnP7S2";
    Button btnP7S2_2 = this.btnP7S2;
    size1 = new Size(17, 17);
    Size size77 = size1;
    btnP7S2_2.Size = size77;
    this.btnP7S2.TabIndex = 72;
    this.btnP7S2.Text = " ";
    this.btnP7S2.UseVisualStyleBackColor = false;
    this.btnP7S2.Visible = false;
    this.btnP6S2.BackColor = Color.LightSalmon;
    Button btnP6S2_1 = this.btnP6S2;
    point1 = new Point(405, 149);
    Point point78 = point1;
    btnP6S2_1.Location = point78;
    this.btnP6S2.Name = "btnP6S2";
    Button btnP6S2_2 = this.btnP6S2;
    size1 = new Size(17, 17);
    Size size78 = size1;
    btnP6S2_2.Size = size78;
    this.btnP6S2.TabIndex = 64 /*0x40*/;
    this.btnP6S2.Text = " ";
    this.btnP6S2.UseVisualStyleBackColor = false;
    this.btnP6S2.Visible = false;
    this.btnP5S2.BackColor = Color.LightSalmon;
    Button btnP5S2_1 = this.btnP5S2;
    point1 = new Point(405, 124);
    Point point79 = point1;
    btnP5S2_1.Location = point79;
    this.btnP5S2.Name = "btnP5S2";
    Button btnP5S2_2 = this.btnP5S2;
    size1 = new Size(17, 17);
    Size size79 = size1;
    btnP5S2_2.Size = size79;
    this.btnP5S2.TabIndex = 56;
    this.btnP5S2.Text = " ";
    this.btnP5S2.UseVisualStyleBackColor = false;
    this.btnP5S2.Visible = false;
    this.btnP4S2.BackColor = Color.LightSalmon;
    Button btnP4S2_1 = this.btnP4S2;
    point1 = new Point(405, 97);
    Point point80 = point1;
    btnP4S2_1.Location = point80;
    this.btnP4S2.Name = "btnP4S2";
    Button btnP4S2_2 = this.btnP4S2;
    size1 = new Size(17, 19);
    Size size80 = size1;
    btnP4S2_2.Size = size80;
    this.btnP4S2.TabIndex = 48 /*0x30*/;
    this.btnP4S2.Text = " ";
    this.btnP4S2.UseVisualStyleBackColor = false;
    this.btnP4S2.Visible = false;
    this.btnP3S2.BackColor = Color.LightSalmon;
    Button btnP3S2_1 = this.btnP3S2;
    point1 = new Point(405, 70);
    Point point81 = point1;
    btnP3S2_1.Location = point81;
    this.btnP3S2.Name = "btnP3S2";
    Button btnP3S2_2 = this.btnP3S2;
    size1 = new Size(17, 19);
    Size size81 = size1;
    btnP3S2_2.Size = size81;
    this.btnP3S2.TabIndex = 40;
    this.btnP3S2.Text = " ";
    this.btnP3S2.UseVisualStyleBackColor = false;
    this.btnP3S2.Visible = false;
    this.btnP2S2.BackColor = Color.LightSalmon;
    Button btnP2S2_1 = this.btnP2S2;
    point1 = new Point(405, 41);
    Point point82 = point1;
    btnP2S2_1.Location = point82;
    this.btnP2S2.Name = "btnP2S2";
    Button btnP2S2_2 = this.btnP2S2;
    size1 = new Size(17, 19);
    Size size82 = size1;
    btnP2S2_2.Size = size82;
    this.btnP2S2.TabIndex = 32 /*0x20*/;
    this.btnP2S2.Text = " ";
    this.btnP2S2.UseVisualStyleBackColor = false;
    this.btnP2S2.Visible = false;
    this.btnP1S2.BackColor = Color.LightSalmon;
    Button btnP1S2_1 = this.btnP1S2;
    point1 = new Point(405, 14);
    Point point83 = point1;
    btnP1S2_1.Location = point83;
    this.btnP1S2.Name = "btnP1S2";
    Button btnP1S2_2 = this.btnP1S2;
    size1 = new Size(17, 19);
    Size size83 = size1;
    btnP1S2_2.Size = size83;
    this.btnP1S2.TabIndex = 24;
    this.btnP1S2.UseVisualStyleBackColor = false;
    this.btnP1S2.Visible = false;
    this.cmbP16S2.BackColor = SystemColors.Control;
    this.cmbP16S2.FormattingEnabled = true;
    this.cmbP16S2.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP16S2_1 = this.cmbP16S2;
    point1 = new Point(306, 418);
    Point point84 = point1;
    cmbP16S2_1.Location = point84;
    this.cmbP16S2.Name = "cmbP16S2";
    ComboBox cmbP16S2_2 = this.cmbP16S2;
    size1 = new Size(91, 21);
    Size size84 = size1;
    cmbP16S2_2.Size = size84;
    this.cmbP16S2.TabIndex = 142;
    this.cmbP16S2.Visible = false;
    this.cmbP15S2.BackColor = SystemColors.Control;
    this.cmbP15S2.FormattingEnabled = true;
    this.cmbP15S2.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP15S2_1 = this.cmbP15S2;
    point1 = new Point(306, 391);
    Point point85 = point1;
    cmbP15S2_1.Location = point85;
    this.cmbP15S2.Name = "cmbP15S2";
    ComboBox cmbP15S2_2 = this.cmbP15S2;
    size1 = new Size(91, 21);
    Size size85 = size1;
    cmbP15S2_2.Size = size85;
    this.cmbP15S2.TabIndex = 134;
    this.cmbP15S2.Visible = false;
    this.cmbP14S2.BackColor = SystemColors.Control;
    this.cmbP14S2.FormattingEnabled = true;
    this.cmbP14S2.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP14S2_1 = this.cmbP14S2;
    point1 = new Point(306, 364);
    Point point86 = point1;
    cmbP14S2_1.Location = point86;
    this.cmbP14S2.Name = "cmbP14S2";
    ComboBox cmbP14S2_2 = this.cmbP14S2;
    size1 = new Size(91, 21);
    Size size86 = size1;
    cmbP14S2_2.Size = size86;
    this.cmbP14S2.TabIndex = 126;
    this.cmbP14S2.Visible = false;
    this.cmbP13S2.BackColor = SystemColors.Control;
    this.cmbP13S2.FormattingEnabled = true;
    this.cmbP13S2.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP13S2_1 = this.cmbP13S2;
    point1 = new Point(306, 337);
    Point point87 = point1;
    cmbP13S2_1.Location = point87;
    this.cmbP13S2.Name = "cmbP13S2";
    ComboBox cmbP13S2_2 = this.cmbP13S2;
    size1 = new Size(91, 21);
    Size size87 = size1;
    cmbP13S2_2.Size = size87;
    this.cmbP13S2.TabIndex = 118;
    this.cmbP13S2.Visible = false;
    this.cmbP12S2.BackColor = SystemColors.Control;
    this.cmbP12S2.FormattingEnabled = true;
    this.cmbP12S2.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP12S2_1 = this.cmbP12S2;
    point1 = new Point(306, 310);
    Point point88 = point1;
    cmbP12S2_1.Location = point88;
    this.cmbP12S2.Name = "cmbP12S2";
    ComboBox cmbP12S2_2 = this.cmbP12S2;
    size1 = new Size(91, 21);
    Size size88 = size1;
    cmbP12S2_2.Size = size88;
    this.cmbP12S2.TabIndex = 106;
    this.cmbP12S2.Visible = false;
    this.cmbP11S2.BackColor = SystemColors.Control;
    this.cmbP11S2.FormattingEnabled = true;
    this.cmbP11S2.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP11S2_1 = this.cmbP11S2;
    point1 = new Point(306, 283);
    Point point89 = point1;
    cmbP11S2_1.Location = point89;
    this.cmbP11S2.Name = "cmbP11S2";
    ComboBox cmbP11S2_2 = this.cmbP11S2;
    size1 = new Size(91, 21);
    Size size89 = size1;
    cmbP11S2_2.Size = size89;
    this.cmbP11S2.TabIndex = 103;
    this.cmbP11S2.Visible = false;
    this.cmbP10S2.BackColor = SystemColors.Control;
    this.cmbP10S2.FormattingEnabled = true;
    this.cmbP10S2.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP10S2_1 = this.cmbP10S2;
    point1 = new Point(306, 256 /*0x0100*/);
    Point point90 = point1;
    cmbP10S2_1.Location = point90;
    this.cmbP10S2.Name = "cmbP10S2";
    ComboBox cmbP10S2_2 = this.cmbP10S2;
    size1 = new Size(91, 21);
    Size size90 = size1;
    cmbP10S2_2.Size = size90;
    this.cmbP10S2.TabIndex = 95;
    this.cmbP10S2.Visible = false;
    this.cmbP9S2.BackColor = SystemColors.Control;
    this.cmbP9S2.FormattingEnabled = true;
    this.cmbP9S2.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP9S2_1 = this.cmbP9S2;
    point1 = new Point(306, 229);
    Point point91 = point1;
    cmbP9S2_1.Location = point91;
    this.cmbP9S2.Name = "cmbP9S2";
    ComboBox cmbP9S2_2 = this.cmbP9S2;
    size1 = new Size(91, 21);
    Size size91 = size1;
    cmbP9S2_2.Size = size91;
    this.cmbP9S2.TabIndex = 87;
    this.cmbP9S2.Visible = false;
    this.cmbP8S2.BackColor = SystemColors.Control;
    this.cmbP8S2.FormattingEnabled = true;
    this.cmbP8S2.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP8S2_1 = this.cmbP8S2;
    point1 = new Point(306, 202);
    Point point92 = point1;
    cmbP8S2_1.Location = point92;
    this.cmbP8S2.Name = "cmbP8S2";
    ComboBox cmbP8S2_2 = this.cmbP8S2;
    size1 = new Size(91, 21);
    Size size92 = size1;
    cmbP8S2_2.Size = size92;
    this.cmbP8S2.TabIndex = 79;
    this.cmbP8S2.Visible = false;
    this.cmbP7S2.BackColor = SystemColors.Control;
    this.cmbP7S2.FormattingEnabled = true;
    this.cmbP7S2.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP7S2_1 = this.cmbP7S2;
    point1 = new Point(306, 175);
    Point point93 = point1;
    cmbP7S2_1.Location = point93;
    this.cmbP7S2.Name = "cmbP7S2";
    ComboBox cmbP7S2_2 = this.cmbP7S2;
    size1 = new Size(91, 21);
    Size size93 = size1;
    cmbP7S2_2.Size = size93;
    this.cmbP7S2.TabIndex = 71;
    this.cmbP7S2.Visible = false;
    this.cmbP6S2.BackColor = SystemColors.Control;
    this.cmbP6S2.FormattingEnabled = true;
    this.cmbP6S2.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP6S2_1 = this.cmbP6S2;
    point1 = new Point(306, 148);
    Point point94 = point1;
    cmbP6S2_1.Location = point94;
    this.cmbP6S2.Name = "cmbP6S2";
    ComboBox cmbP6S2_2 = this.cmbP6S2;
    size1 = new Size(91, 21);
    Size size94 = size1;
    cmbP6S2_2.Size = size94;
    this.cmbP6S2.TabIndex = 63 /*0x3F*/;
    this.cmbP6S2.Visible = false;
    this.cmbP5S2.BackColor = SystemColors.Control;
    this.cmbP5S2.FormattingEnabled = true;
    this.cmbP5S2.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP5S2_1 = this.cmbP5S2;
    point1 = new Point(306, 121);
    Point point95 = point1;
    cmbP5S2_1.Location = point95;
    this.cmbP5S2.Name = "cmbP5S2";
    ComboBox cmbP5S2_2 = this.cmbP5S2;
    size1 = new Size(91, 21);
    Size size95 = size1;
    cmbP5S2_2.Size = size95;
    this.cmbP5S2.TabIndex = 55;
    this.cmbP5S2.Visible = false;
    this.cmbP4S2.BackColor = SystemColors.Control;
    this.cmbP4S2.FormattingEnabled = true;
    this.cmbP4S2.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP4S2_1 = this.cmbP4S2;
    point1 = new Point(306, 94);
    Point point96 = point1;
    cmbP4S2_1.Location = point96;
    this.cmbP4S2.Name = "cmbP4S2";
    ComboBox cmbP4S2_2 = this.cmbP4S2;
    size1 = new Size(91, 21);
    Size size96 = size1;
    cmbP4S2_2.Size = size96;
    this.cmbP4S2.TabIndex = 47;
    this.cmbP4S2.Visible = false;
    this.cmbP3S2.BackColor = SystemColors.Control;
    this.cmbP3S2.FormattingEnabled = true;
    this.cmbP3S2.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP3S2_1 = this.cmbP3S2;
    point1 = new Point(306, 67);
    Point point97 = point1;
    cmbP3S2_1.Location = point97;
    this.cmbP3S2.Name = "cmbP3S2";
    ComboBox cmbP3S2_2 = this.cmbP3S2;
    size1 = new Size(91, 21);
    Size size97 = size1;
    cmbP3S2_2.Size = size97;
    this.cmbP3S2.TabIndex = 39;
    this.cmbP3S2.Visible = false;
    this.cmbP2S2.BackColor = SystemColors.Control;
    this.cmbP2S2.FormattingEnabled = true;
    this.cmbP2S2.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP2S2_1 = this.cmbP2S2;
    point1 = new Point(306, 40);
    Point point98 = point1;
    cmbP2S2_1.Location = point98;
    this.cmbP2S2.Name = "cmbP2S2";
    ComboBox cmbP2S2_2 = this.cmbP2S2;
    size1 = new Size(91, 21);
    Size size98 = size1;
    cmbP2S2_2.Size = size98;
    this.cmbP2S2.TabIndex = 31 /*0x1F*/;
    this.cmbP2S2.Visible = false;
    this.cmbP1S2.BackColor = SystemColors.Control;
    this.cmbP1S2.FormattingEnabled = true;
    this.cmbP1S2.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP1S2_1 = this.cmbP1S2;
    point1 = new Point(306, 13);
    Point point99 = point1;
    cmbP1S2_1.Location = point99;
    this.cmbP1S2.Name = "cmbP1S2";
    ComboBox cmbP1S2_2 = this.cmbP1S2;
    size1 = new Size(91, 21);
    Size size99 = size1;
    cmbP1S2_2.Size = size99;
    this.cmbP1S2.TabIndex = 23;
    this.cmbP1S2.Visible = false;
    this.btnP16S1.BackColor = Color.LightSalmon;
    Button btnP16S1_1 = this.btnP16S1;
    point1 = new Point(232, 422);
    Point point100 = point1;
    btnP16S1_1.Location = point100;
    this.btnP16S1.Name = "btnP16S1";
    Button btnP16S1_2 = this.btnP16S1;
    size1 = new Size(20, 17);
    Size size100 = size1;
    btnP16S1_2.Size = size100;
    this.btnP16S1.TabIndex = 141;
    this.btnP16S1.Text = " ";
    this.btnP16S1.UseVisualStyleBackColor = false;
    this.btnP16S1.Visible = false;
    this.btnP15S1.BackColor = Color.LightSalmon;
    Button btnP15S1_1 = this.btnP15S1;
    point1 = new Point(232, 395);
    Point point101 = point1;
    btnP15S1_1.Location = point101;
    this.btnP15S1.Name = "btnP15S1";
    Button btnP15S1_2 = this.btnP15S1;
    size1 = new Size(19, 17);
    Size size101 = size1;
    btnP15S1_2.Size = size101;
    this.btnP15S1.TabIndex = 133;
    this.btnP15S1.Text = " ";
    this.btnP15S1.UseVisualStyleBackColor = false;
    this.btnP15S1.Visible = false;
    this.btnP14S1.BackColor = Color.LightSalmon;
    Button btnP14S1_1 = this.btnP14S1;
    point1 = new Point(231, 370);
    Point point102 = point1;
    btnP14S1_1.Location = point102;
    this.btnP14S1.Name = "btnP14S1";
    Button btnP14S1_2 = this.btnP14S1;
    size1 = new Size(20, 17);
    Size size102 = size1;
    btnP14S1_2.Size = size102;
    this.btnP14S1.TabIndex = 125;
    this.btnP14S1.Text = " ";
    this.btnP14S1.UseVisualStyleBackColor = false;
    this.btnP14S1.Visible = false;
    this.btnP13S1.BackColor = Color.LightSalmon;
    Button btnP13S1_1 = this.btnP13S1;
    point1 = new Point(232, 341);
    Point point103 = point1;
    btnP13S1_1.Location = point103;
    this.btnP13S1.Name = "btnP13S1";
    Button btnP13S1_2 = this.btnP13S1;
    size1 = new Size(19, 17);
    Size size103 = size1;
    btnP13S1_2.Size = size103;
    this.btnP13S1.TabIndex = 117;
    this.btnP13S1.Text = " ";
    this.btnP13S1.UseVisualStyleBackColor = false;
    this.btnP13S1.Visible = false;
    this.btnP12S1.BackColor = Color.LightSalmon;
    Button btnP12S1_1 = this.btnP12S1;
    point1 = new Point(232, 312);
    Point point104 = point1;
    btnP12S1_1.Location = point104;
    this.btnP12S1.Name = "btnP12S1";
    Button btnP12S1_2 = this.btnP12S1;
    size1 = new Size(19, 19);
    Size size104 = size1;
    btnP12S1_2.Size = size104;
    this.btnP12S1.TabIndex = 110;
    this.btnP12S1.Text = " ";
    this.btnP12S1.UseVisualStyleBackColor = false;
    this.btnP12S1.Visible = false;
    this.btnP11S1.BackColor = Color.LightSalmon;
    Button btnP11S1_1 = this.btnP11S1;
    point1 = new Point(232, 287);
    Point point105 = point1;
    btnP11S1_1.Location = point105;
    this.btnP11S1.Name = "btnP11S1";
    Button btnP11S1_2 = this.btnP11S1;
    size1 = new Size(19, 17);
    Size size105 = size1;
    btnP11S1_2.Size = size105;
    this.btnP11S1.TabIndex = 102;
    this.btnP11S1.Text = " ";
    this.btnP11S1.UseVisualStyleBackColor = false;
    this.btnP11S1.Visible = false;
    this.btnP10S1.BackColor = Color.LightSalmon;
    Button btnP10S1_1 = this.btnP10S1;
    point1 = new Point(232, 260);
    Point point106 = point1;
    btnP10S1_1.Location = point106;
    this.btnP10S1.Name = "btnP10S1";
    Button btnP10S1_2 = this.btnP10S1;
    size1 = new Size(19, 17);
    Size size106 = size1;
    btnP10S1_2.Size = size106;
    this.btnP10S1.TabIndex = 94;
    this.btnP10S1.Text = " ";
    this.btnP10S1.UseVisualStyleBackColor = false;
    this.btnP10S1.Visible = false;
    this.btnP9S1.BackColor = Color.LightSalmon;
    Button btnP9S1_1 = this.btnP9S1;
    point1 = new Point(232, 233);
    Point point107 = point1;
    btnP9S1_1.Location = point107;
    this.btnP9S1.Name = "btnP9S1";
    Button btnP9S1_2 = this.btnP9S1;
    size1 = new Size(19, 17);
    Size size107 = size1;
    btnP9S1_2.Size = size107;
    this.btnP9S1.TabIndex = 86;
    this.btnP9S1.Text = " ";
    this.btnP9S1.UseVisualStyleBackColor = false;
    this.btnP9S1.Visible = false;
    this.btnP8S1.BackColor = Color.LightSalmon;
    Button btnP8S1_1 = this.btnP8S1;
    point1 = new Point(232, 208 /*0xD0*/);
    Point point108 = point1;
    btnP8S1_1.Location = point108;
    this.btnP8S1.Name = "btnP8S1";
    Button btnP8S1_2 = this.btnP8S1;
    size1 = new Size(17, 18);
    Size size108 = size1;
    btnP8S1_2.Size = size108;
    this.btnP8S1.TabIndex = 78;
    this.btnP8S1.Text = " ";
    this.btnP8S1.UseVisualStyleBackColor = false;
    this.btnP8S1.Visible = false;
    this.btnP7S1.BackColor = Color.LightSalmon;
    Button btnP7S1_1 = this.btnP7S1;
    point1 = new Point(232, 179);
    Point point109 = point1;
    btnP7S1_1.Location = point109;
    this.btnP7S1.Name = "btnP7S1";
    Button btnP7S1_2 = this.btnP7S1;
    size1 = new Size(17, 17);
    Size size109 = size1;
    btnP7S1_2.Size = size109;
    this.btnP7S1.TabIndex = 70;
    this.btnP7S1.Text = " ";
    this.btnP7S1.UseVisualStyleBackColor = false;
    this.btnP7S1.Visible = false;
    this.btnP6S1.BackColor = Color.LightSalmon;
    Button btnP6S1_1 = this.btnP6S1;
    point1 = new Point(232, 150);
    Point point110 = point1;
    btnP6S1_1.Location = point110;
    this.btnP6S1.Name = "btnP6S1";
    Button btnP6S1_2 = this.btnP6S1;
    size1 = new Size(17, 17);
    Size size110 = size1;
    btnP6S1_2.Size = size110;
    this.btnP6S1.TabIndex = 62;
    this.btnP6S1.Text = " ";
    this.btnP6S1.UseVisualStyleBackColor = false;
    this.btnP6S1.Visible = false;
    this.btnP5S1.BackColor = Color.LightSalmon;
    Button btnP5S1_1 = this.btnP5S1;
    point1 = new Point(232, 125);
    Point point111 = point1;
    btnP5S1_1.Location = point111;
    this.btnP5S1.Name = "btnP5S1";
    Button btnP5S1_2 = this.btnP5S1;
    size1 = new Size(17, 17);
    Size size111 = size1;
    btnP5S1_2.Size = size111;
    this.btnP5S1.TabIndex = 54;
    this.btnP5S1.Text = " ";
    this.btnP5S1.UseVisualStyleBackColor = false;
    this.btnP5S1.Visible = false;
    this.btnP4S1.BackColor = Color.LightSalmon;
    Button btnP4S1_1 = this.btnP4S1;
    point1 = new Point(232, 98);
    Point point112 = point1;
    btnP4S1_1.Location = point112;
    this.btnP4S1.Name = "btnP4S1";
    Button btnP4S1_2 = this.btnP4S1;
    size1 = new Size(17, 19);
    Size size112 = size1;
    btnP4S1_2.Size = size112;
    this.btnP4S1.TabIndex = 46;
    this.btnP4S1.Text = " ";
    this.btnP4S1.UseVisualStyleBackColor = false;
    this.btnP4S1.Visible = false;
    this.btnP3S1.BackColor = Color.LightSalmon;
    Button btnP3S1_1 = this.btnP3S1;
    point1 = new Point(232, 71);
    Point point113 = point1;
    btnP3S1_1.Location = point113;
    this.btnP3S1.Name = "btnP3S1";
    Button btnP3S1_2 = this.btnP3S1;
    size1 = new Size(17, 19);
    Size size113 = size1;
    btnP3S1_2.Size = size113;
    this.btnP3S1.TabIndex = 38;
    this.btnP3S1.Text = " ";
    this.btnP3S1.UseVisualStyleBackColor = false;
    this.btnP3S1.Visible = false;
    this.btnP2S1.BackColor = Color.LightSalmon;
    Button btnP2S1_1 = this.btnP2S1;
    point1 = new Point(232, 42);
    Point point114 = point1;
    btnP2S1_1.Location = point114;
    this.btnP2S1.Name = "btnP2S1";
    Button btnP2S1_2 = this.btnP2S1;
    size1 = new Size(17, 19);
    Size size114 = size1;
    btnP2S1_2.Size = size114;
    this.btnP2S1.TabIndex = 30;
    this.btnP2S1.Text = " ";
    this.btnP2S1.UseVisualStyleBackColor = false;
    this.btnP2S1.Visible = false;
    this.btnP1S1.BackColor = Color.LightSalmon;
    Button btnP1S1_1 = this.btnP1S1;
    point1 = new Point(232, 15);
    Point point115 = point1;
    btnP1S1_1.Location = point115;
    this.btnP1S1.Name = "btnP1S1";
    Button btnP1S1_2 = this.btnP1S1;
    size1 = new Size(17, 19);
    Size size115 = size1;
    btnP1S1_2.Size = size115;
    this.btnP1S1.TabIndex = 22;
    this.btnP1S1.UseVisualStyleBackColor = false;
    this.btnP1S1.Visible = false;
    this.cmbP16S1.BackColor = SystemColors.ButtonFace;
    this.cmbP16S1.FormattingEnabled = true;
    this.cmbP16S1.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP16S1_1 = this.cmbP16S1;
    point1 = new Point(133, 418);
    Point point116 = point1;
    cmbP16S1_1.Location = point116;
    this.cmbP16S1.Name = "cmbP16S1";
    ComboBox cmbP16S1_2 = this.cmbP16S1;
    size1 = new Size(91, 21);
    Size size116 = size1;
    cmbP16S1_2.Size = size116;
    this.cmbP16S1.TabIndex = 140;
    this.cmbP16S1.Visible = false;
    this.cmbP15S1.BackColor = SystemColors.ButtonFace;
    this.cmbP15S1.FormattingEnabled = true;
    this.cmbP15S1.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP15S1_1 = this.cmbP15S1;
    point1 = new Point(132, 389);
    Point point117 = point1;
    cmbP15S1_1.Location = point117;
    this.cmbP15S1.Name = "cmbP15S1";
    ComboBox cmbP15S1_2 = this.cmbP15S1;
    size1 = new Size(91, 21);
    Size size117 = size1;
    cmbP15S1_2.Size = size117;
    this.cmbP15S1.TabIndex = 132;
    this.cmbP15S1.Visible = false;
    this.cmbP14S1.BackColor = SystemColors.ButtonFace;
    this.cmbP14S1.FormattingEnabled = true;
    this.cmbP14S1.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP14S1_1 = this.cmbP14S1;
    point1 = new Point(133, 364);
    Point point118 = point1;
    cmbP14S1_1.Location = point118;
    this.cmbP14S1.Name = "cmbP14S1";
    ComboBox cmbP14S1_2 = this.cmbP14S1;
    size1 = new Size(91, 21);
    Size size118 = size1;
    cmbP14S1_2.Size = size118;
    this.cmbP14S1.TabIndex = 124;
    this.cmbP14S1.Visible = false;
    this.cmbP13S1.BackColor = SystemColors.ButtonFace;
    this.cmbP13S1.FormattingEnabled = true;
    this.cmbP13S1.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP13S1_1 = this.cmbP13S1;
    point1 = new Point(133, 337);
    Point point119 = point1;
    cmbP13S1_1.Location = point119;
    this.cmbP13S1.Name = "cmbP13S1";
    ComboBox cmbP13S1_2 = this.cmbP13S1;
    size1 = new Size(91, 21);
    Size size119 = size1;
    cmbP13S1_2.Size = size119;
    this.cmbP13S1.TabIndex = 116;
    this.cmbP13S1.Visible = false;
    this.cmbP12S1.BackColor = SystemColors.ButtonFace;
    this.cmbP12S1.FormattingEnabled = true;
    this.cmbP12S1.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP12S1_1 = this.cmbP12S1;
    point1 = new Point(133, 310);
    Point point120 = point1;
    cmbP12S1_1.Location = point120;
    this.cmbP12S1.Name = "cmbP12S1";
    ComboBox cmbP12S1_2 = this.cmbP12S1;
    size1 = new Size(91, 21);
    Size size120 = size1;
    cmbP12S1_2.Size = size120;
    this.cmbP12S1.TabIndex = 109;
    this.cmbP12S1.Visible = false;
    this.cmbP11S1.BackColor = SystemColors.ButtonFace;
    this.cmbP11S1.FormattingEnabled = true;
    this.cmbP11S1.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP11S1_1 = this.cmbP11S1;
    point1 = new Point(133, 283);
    Point point121 = point1;
    cmbP11S1_1.Location = point121;
    this.cmbP11S1.Name = "cmbP11S1";
    ComboBox cmbP11S1_2 = this.cmbP11S1;
    size1 = new Size(91, 21);
    Size size121 = size1;
    cmbP11S1_2.Size = size121;
    this.cmbP11S1.TabIndex = 101;
    this.cmbP11S1.Visible = false;
    this.cmbP10S1.BackColor = SystemColors.ButtonFace;
    this.cmbP10S1.FormattingEnabled = true;
    this.cmbP10S1.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP10S1_1 = this.cmbP10S1;
    point1 = new Point(133, 256 /*0x0100*/);
    Point point122 = point1;
    cmbP10S1_1.Location = point122;
    this.cmbP10S1.Name = "cmbP10S1";
    ComboBox cmbP10S1_2 = this.cmbP10S1;
    size1 = new Size(91, 21);
    Size size122 = size1;
    cmbP10S1_2.Size = size122;
    this.cmbP10S1.TabIndex = 93;
    this.cmbP10S1.Visible = false;
    this.cmbP9S1.BackColor = SystemColors.ButtonFace;
    this.cmbP9S1.FormattingEnabled = true;
    this.cmbP9S1.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP9S1_1 = this.cmbP9S1;
    point1 = new Point(133, 229);
    Point point123 = point1;
    cmbP9S1_1.Location = point123;
    this.cmbP9S1.Name = "cmbP9S1";
    ComboBox cmbP9S1_2 = this.cmbP9S1;
    size1 = new Size(91, 21);
    Size size123 = size1;
    cmbP9S1_2.Size = size123;
    this.cmbP9S1.TabIndex = 85;
    this.cmbP9S1.Visible = false;
    this.cmbP8S1.BackColor = SystemColors.ButtonFace;
    this.cmbP8S1.FormattingEnabled = true;
    this.cmbP8S1.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP8S1_1 = this.cmbP8S1;
    point1 = new Point(133, 202);
    Point point124 = point1;
    cmbP8S1_1.Location = point124;
    this.cmbP8S1.Name = "cmbP8S1";
    ComboBox cmbP8S1_2 = this.cmbP8S1;
    size1 = new Size(91, 21);
    Size size124 = size1;
    cmbP8S1_2.Size = size124;
    this.cmbP8S1.TabIndex = 77;
    this.cmbP8S1.Visible = false;
    this.cmbP7S1.BackColor = SystemColors.ButtonFace;
    this.cmbP7S1.FormattingEnabled = true;
    this.cmbP7S1.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP7S1_1 = this.cmbP7S1;
    point1 = new Point(133, 175);
    Point point125 = point1;
    cmbP7S1_1.Location = point125;
    this.cmbP7S1.Name = "cmbP7S1";
    ComboBox cmbP7S1_2 = this.cmbP7S1;
    size1 = new Size(91, 21);
    Size size125 = size1;
    cmbP7S1_2.Size = size125;
    this.cmbP7S1.TabIndex = 69;
    this.cmbP7S1.Visible = false;
    this.cmbP6S1.BackColor = SystemColors.ButtonFace;
    this.cmbP6S1.FormattingEnabled = true;
    this.cmbP6S1.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP6S1_1 = this.cmbP6S1;
    point1 = new Point(133, 148);
    Point point126 = point1;
    cmbP6S1_1.Location = point126;
    this.cmbP6S1.Name = "cmbP6S1";
    ComboBox cmbP6S1_2 = this.cmbP6S1;
    size1 = new Size(91, 21);
    Size size126 = size1;
    cmbP6S1_2.Size = size126;
    this.cmbP6S1.TabIndex = 610;
    this.cmbP6S1.Visible = false;
    this.cmbP5S1.BackColor = SystemColors.ButtonFace;
    this.cmbP5S1.FormattingEnabled = true;
    this.cmbP5S1.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP5S1_1 = this.cmbP5S1;
    point1 = new Point(133, 121);
    Point point127 = point1;
    cmbP5S1_1.Location = point127;
    this.cmbP5S1.Name = "cmbP5S1";
    ComboBox cmbP5S1_2 = this.cmbP5S1;
    size1 = new Size(91, 21);
    Size size127 = size1;
    cmbP5S1_2.Size = size127;
    this.cmbP5S1.TabIndex = 53;
    this.cmbP5S1.Visible = false;
    this.cmbP4S1.BackColor = SystemColors.ButtonFace;
    this.cmbP4S1.FormattingEnabled = true;
    this.cmbP4S1.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP4S1_1 = this.cmbP4S1;
    point1 = new Point(133, 94);
    Point point128 = point1;
    cmbP4S1_1.Location = point128;
    this.cmbP4S1.Name = "cmbP4S1";
    ComboBox cmbP4S1_2 = this.cmbP4S1;
    size1 = new Size(91, 21);
    Size size128 = size1;
    cmbP4S1_2.Size = size128;
    this.cmbP4S1.TabIndex = 45;
    this.cmbP4S1.Visible = false;
    this.cmbP3S1.BackColor = SystemColors.ButtonFace;
    this.cmbP3S1.FormattingEnabled = true;
    this.cmbP3S1.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP3S1_1 = this.cmbP3S1;
    point1 = new Point(133, 67);
    Point point129 = point1;
    cmbP3S1_1.Location = point129;
    this.cmbP3S1.Name = "cmbP3S1";
    ComboBox cmbP3S1_2 = this.cmbP3S1;
    size1 = new Size(91, 21);
    Size size129 = size1;
    cmbP3S1_2.Size = size129;
    this.cmbP3S1.TabIndex = 37;
    this.cmbP3S1.Visible = false;
    this.cmbP2S1.BackColor = SystemColors.ButtonFace;
    this.cmbP2S1.FormattingEnabled = true;
    this.cmbP2S1.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP2S1_1 = this.cmbP2S1;
    point1 = new Point(133, 40);
    Point point130 = point1;
    cmbP2S1_1.Location = point130;
    this.cmbP2S1.Name = "cmbP2S1";
    ComboBox cmbP2S1_2 = this.cmbP2S1;
    size1 = new Size(91, 21);
    Size size130 = size1;
    cmbP2S1_2.Size = size130;
    this.cmbP2S1.TabIndex = 29;
    this.cmbP2S1.Visible = false;
    this.cmbP1S1.BackColor = SystemColors.ButtonFace;
    this.cmbP1S1.FormattingEnabled = true;
    this.cmbP1S1.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "MLDB",
      (object) "PDCH",
      (object) "None"
    });
    ComboBox cmbP1S1_1 = this.cmbP1S1;
    point1 = new Point(133, 13);
    Point point131 = point1;
    cmbP1S1_1.Location = point131;
    this.cmbP1S1.Name = "cmbP1S1";
    ComboBox cmbP1S1_2 = this.cmbP1S1;
    size1 = new Size(91, 21);
    Size size131 = size1;
    cmbP1S1_2.Size = size131;
    this.cmbP1S1.TabIndex = 21;
    this.cmbP1S1.Visible = false;
    this.chkPort16.AutoSize = true;
    this.chkPort16.BackColor = SystemColors.ButtonHighlight;
    this.chkPort16.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    CheckBox chkPort16_1 = this.chkPort16;
    point1 = new Point(57, 420);
    Point point132 = point1;
    chkPort16_1.Location = point132;
    this.chkPort16.Name = "chkPort16";
    CheckBox chkPort16_2 = this.chkPort16;
    size1 = new Size(63 /*0x3F*/, 17);
    Size size132 = size1;
    chkPort16_2.Size = size132;
    this.chkPort16.TabIndex = 20;
    this.chkPort16.Text = "Port16";
    this.chkPort16.UseVisualStyleBackColor = false;
    this.chkPort15.AutoSize = true;
    this.chkPort15.BackColor = SystemColors.ButtonHighlight;
    this.chkPort15.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    CheckBox chkPort15_1 = this.chkPort15;
    point1 = new Point(57, 393);
    Point point133 = point1;
    chkPort15_1.Location = point133;
    this.chkPort15.Name = "chkPort15";
    CheckBox chkPort15_2 = this.chkPort15;
    size1 = new Size(63 /*0x3F*/, 17);
    Size size133 = size1;
    chkPort15_2.Size = size133;
    this.chkPort15.TabIndex = 19;
    this.chkPort15.Text = "Port15";
    this.chkPort15.UseVisualStyleBackColor = false;
    this.chkPort14.AutoSize = true;
    this.chkPort14.BackColor = SystemColors.ButtonHighlight;
    this.chkPort14.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    CheckBox chkPort14_1 = this.chkPort14;
    point1 = new Point(57, 366);
    Point point134 = point1;
    chkPort14_1.Location = point134;
    this.chkPort14.Name = "chkPort14";
    CheckBox chkPort14_2 = this.chkPort14;
    size1 = new Size(63 /*0x3F*/, 17);
    Size size134 = size1;
    chkPort14_2.Size = size134;
    this.chkPort14.TabIndex = 18;
    this.chkPort14.Text = "Port14";
    this.chkPort14.UseVisualStyleBackColor = false;
    this.chkPort13.AutoSize = true;
    this.chkPort13.BackColor = SystemColors.ButtonHighlight;
    this.chkPort13.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    CheckBox chkPort13_1 = this.chkPort13;
    point1 = new Point(57, 339);
    Point point135 = point1;
    chkPort13_1.Location = point135;
    this.chkPort13.Name = "chkPort13";
    CheckBox chkPort13_2 = this.chkPort13;
    size1 = new Size(63 /*0x3F*/, 17);
    Size size135 = size1;
    chkPort13_2.Size = size135;
    this.chkPort13.TabIndex = 17;
    this.chkPort13.Text = "Port13";
    this.chkPort13.UseVisualStyleBackColor = false;
    this.chkPort12.AutoSize = true;
    this.chkPort12.BackColor = SystemColors.ButtonHighlight;
    this.chkPort12.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    CheckBox chkPort12_1 = this.chkPort12;
    point1 = new Point(57, 312);
    Point point136 = point1;
    chkPort12_1.Location = point136;
    this.chkPort12.Name = "chkPort12";
    CheckBox chkPort12_2 = this.chkPort12;
    size1 = new Size(63 /*0x3F*/, 17);
    Size size136 = size1;
    chkPort12_2.Size = size136;
    this.chkPort12.TabIndex = 16 /*0x10*/;
    this.chkPort12.Text = "Port12";
    this.chkPort12.UseVisualStyleBackColor = false;
    this.chkPort11.AutoSize = true;
    this.chkPort11.BackColor = SystemColors.ButtonHighlight;
    this.chkPort11.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    CheckBox chkPort11_1 = this.chkPort11;
    point1 = new Point(57, 285);
    Point point137 = point1;
    chkPort11_1.Location = point137;
    this.chkPort11.Name = "chkPort11";
    CheckBox chkPort11_2 = this.chkPort11;
    size1 = new Size(63 /*0x3F*/, 17);
    Size size137 = size1;
    chkPort11_2.Size = size137;
    this.chkPort11.TabIndex = 15;
    this.chkPort11.Text = "Port11";
    this.chkPort11.UseVisualStyleBackColor = false;
    this.chkPort10.AutoSize = true;
    this.chkPort10.BackColor = SystemColors.ButtonHighlight;
    this.chkPort10.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    CheckBox chkPort10_1 = this.chkPort10;
    point1 = new Point(57, 258);
    Point point138 = point1;
    chkPort10_1.Location = point138;
    this.chkPort10.Name = "chkPort10";
    CheckBox chkPort10_2 = this.chkPort10;
    size1 = new Size(63 /*0x3F*/, 17);
    Size size138 = size1;
    chkPort10_2.Size = size138;
    this.chkPort10.TabIndex = 14;
    this.chkPort10.Text = "Port10";
    this.chkPort10.UseVisualStyleBackColor = false;
    this.chkPort9.AutoSize = true;
    this.chkPort9.BackColor = SystemColors.ButtonHighlight;
    this.chkPort9.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    CheckBox chkPort9_1 = this.chkPort9;
    point1 = new Point(57, 231);
    Point point139 = point1;
    chkPort9_1.Location = point139;
    this.chkPort9.Name = "chkPort9";
    CheckBox chkPort9_2 = this.chkPort9;
    size1 = new Size(56, 17);
    Size size139 = size1;
    chkPort9_2.Size = size139;
    this.chkPort9.TabIndex = 13;
    this.chkPort9.Text = "Port9";
    this.chkPort9.UseVisualStyleBackColor = false;
    this.chkPort8.AutoSize = true;
    this.chkPort8.BackColor = SystemColors.ButtonHighlight;
    this.chkPort8.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    CheckBox chkPort8_1 = this.chkPort8;
    point1 = new Point(57, 204);
    Point point140 = point1;
    chkPort8_1.Location = point140;
    this.chkPort8.Name = "chkPort8";
    CheckBox chkPort8_2 = this.chkPort8;
    size1 = new Size(56, 17);
    Size size140 = size1;
    chkPort8_2.Size = size140;
    this.chkPort8.TabIndex = 12;
    this.chkPort8.Text = "Port8";
    this.chkPort8.UseVisualStyleBackColor = false;
    this.chkPort7.AutoSize = true;
    this.chkPort7.BackColor = SystemColors.ButtonHighlight;
    this.chkPort7.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    CheckBox chkPort7_1 = this.chkPort7;
    point1 = new Point(57, 177);
    Point point141 = point1;
    chkPort7_1.Location = point141;
    this.chkPort7.Name = "chkPort7";
    CheckBox chkPort7_2 = this.chkPort7;
    size1 = new Size(56, 17);
    Size size141 = size1;
    chkPort7_2.Size = size141;
    this.chkPort7.TabIndex = 11;
    this.chkPort7.Text = "Port7";
    this.chkPort7.UseVisualStyleBackColor = false;
    this.chkPort6.AutoSize = true;
    this.chkPort6.BackColor = SystemColors.ButtonHighlight;
    this.chkPort6.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    CheckBox chkPort6_1 = this.chkPort6;
    point1 = new Point(57, 150);
    Point point142 = point1;
    chkPort6_1.Location = point142;
    this.chkPort6.Name = "chkPort6";
    CheckBox chkPort6_2 = this.chkPort6;
    size1 = new Size(56, 17);
    Size size142 = size1;
    chkPort6_2.Size = size142;
    this.chkPort6.TabIndex = 10;
    this.chkPort6.Text = "Port6";
    this.chkPort6.UseVisualStyleBackColor = false;
    this.chkPort5.AutoSize = true;
    this.chkPort5.BackColor = SystemColors.ButtonHighlight;
    this.chkPort5.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    CheckBox chkPort5_1 = this.chkPort5;
    point1 = new Point(57, 123);
    Point point143 = point1;
    chkPort5_1.Location = point143;
    this.chkPort5.Name = "chkPort5";
    CheckBox chkPort5_2 = this.chkPort5;
    size1 = new Size(56, 17);
    Size size143 = size1;
    chkPort5_2.Size = size143;
    this.chkPort5.TabIndex = 9;
    this.chkPort5.Text = "Port5";
    this.chkPort5.UseVisualStyleBackColor = false;
    this.chkPort4.AutoSize = true;
    this.chkPort4.BackColor = SystemColors.ButtonHighlight;
    this.chkPort4.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    CheckBox chkPort4_1 = this.chkPort4;
    point1 = new Point(57, 96 /*0x60*/);
    Point point144 = point1;
    chkPort4_1.Location = point144;
    this.chkPort4.Name = "chkPort4";
    CheckBox chkPort4_2 = this.chkPort4;
    size1 = new Size(56, 17);
    Size size144 = size1;
    chkPort4_2.Size = size144;
    this.chkPort4.TabIndex = 8;
    this.chkPort4.Text = "Port4";
    this.chkPort4.UseVisualStyleBackColor = false;
    this.chkPort3.AutoSize = true;
    this.chkPort3.BackColor = SystemColors.ButtonHighlight;
    this.chkPort3.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    CheckBox chkPort3_1 = this.chkPort3;
    point1 = new Point(57, 69);
    Point point145 = point1;
    chkPort3_1.Location = point145;
    this.chkPort3.Name = "chkPort3";
    CheckBox chkPort3_2 = this.chkPort3;
    size1 = new Size(56, 17);
    Size size145 = size1;
    chkPort3_2.Size = size145;
    this.chkPort3.TabIndex = 7;
    this.chkPort3.Text = "Port3";
    this.chkPort3.UseVisualStyleBackColor = false;
    this.chkPort2.AutoSize = true;
    this.chkPort2.BackColor = SystemColors.ButtonHighlight;
    this.chkPort2.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    CheckBox chkPort2_1 = this.chkPort2;
    point1 = new Point(57, 44);
    Point point146 = point1;
    chkPort2_1.Location = point146;
    this.chkPort2.Name = "chkPort2";
    CheckBox chkPort2_2 = this.chkPort2;
    size1 = new Size(56, 17);
    Size size146 = size1;
    chkPort2_2.Size = size146;
    this.chkPort2.TabIndex = 6;
    this.chkPort2.Text = "Port2";
    this.chkPort2.UseVisualStyleBackColor = false;
    this.chkPort1.AutoSize = true;
    this.chkPort1.BackColor = SystemColors.ButtonHighlight;
    this.chkPort1.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    CheckBox chkPort1_1 = this.chkPort1;
    point1 = new Point(57, 17);
    Point point147 = point1;
    chkPort1_1.Location = point147;
    this.chkPort1.Name = "chkPort1";
    CheckBox chkPort1_2 = this.chkPort1;
    size1 = new Size(56, 17);
    Size size147 = size1;
    chkPort1_2.Size = size147;
    this.chkPort1.TabIndex = 5;
    this.chkPort1.Text = "Port1";
    this.chkPort1.UseVisualStyleBackColor = false;
    this.txtCcuName.Enabled = false;
    this.txtCcuName.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtCcuName1 = this.txtCcuName;
    point1 = new Point(276, 6);
    Point point148 = point1;
    txtCcuName1.Location = point148;
    this.txtCcuName.MaxLength = 15;
    this.txtCcuName.Name = "txtCcuName";
    TextBox txtCcuName2 = this.txtCcuName;
    size1 = new Size(116, 22);
    Size size148 = size1;
    txtCcuName2.Size = size148;
    this.txtCcuName.TabIndex = 1;
    this.txtCcuAddress.Enabled = false;
    this.txtCcuAddress.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtCcuAddress1 = this.txtCcuAddress;
    point1 = new Point(276, 46);
    Point point149 = point1;
    txtCcuAddress1.Location = point149;
    this.txtCcuAddress.MaxLength = 3;
    this.txtCcuAddress.Name = "txtCcuAddress";
    TextBox txtCcuAddress2 = this.txtCcuAddress;
    size1 = new Size(116, 22);
    Size size149 = size1;
    txtCcuAddress2.Size = size149;
    this.txtCcuAddress.TabIndex = 2;
    this.lblCcuName.AutoSize = true;
    this.lblCcuName.Enabled = false;
    this.lblCcuName.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblCcuName1 = this.lblCcuName;
    point1 = new Point(165, 9);
    Point point150 = point1;
    lblCcuName1.Location = point150;
    this.lblCcuName.Name = "lblCcuName";
    Label lblCcuName2 = this.lblCcuName;
    size1 = new Size(84, 16 /*0x10*/);
    Size size150 = size1;
    lblCcuName2.Size = size150;
    this.lblCcuName.TabIndex = 160 /*0xA0*/;
    this.lblCcuName.Text = "CCU Name";
    this.lblCcuAddress.AutoSize = true;
    this.lblCcuAddress.Enabled = false;
    this.lblCcuAddress.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblCcuAddress1 = this.lblCcuAddress;
    point1 = new Point(148, 49);
    Point point151 = point1;
    lblCcuAddress1.Location = point151;
    this.lblCcuAddress.Name = "lblCcuAddress";
    Label lblCcuAddress2 = this.lblCcuAddress;
    size1 = new Size(101, 16 /*0x10*/);
    Size size151 = size1;
    lblCcuAddress2.Size = size151;
    this.lblCcuAddress.TabIndex = 159;
    this.lblCcuAddress.Text = "CCU Address";
    this.txtMdchName.Enabled = false;
    this.txtMdchName.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtMdchName1 = this.txtMdchName;
    point1 = new Point(637, 9);
    Point point152 = point1;
    txtMdchName1.Location = point152;
    this.txtMdchName.MaxLength = 15;
    this.txtMdchName.Name = "txtMdchName";
    TextBox txtMdchName2 = this.txtMdchName;
    size1 = new Size(116, 22);
    Size size152 = size1;
    txtMdchName2.Size = size152;
    this.txtMdchName.TabIndex = 3;
    this.txtMdchAddress.Enabled = false;
    this.txtMdchAddress.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtMdchAddress1 = this.txtMdchAddress;
    point1 = new Point(637, 49);
    Point point153 = point1;
    txtMdchAddress1.Location = point153;
    this.txtMdchAddress.MaxLength = 3;
    this.txtMdchAddress.Name = "txtMdchAddress";
    TextBox txtMdchAddress2 = this.txtMdchAddress;
    size1 = new Size(116, 22);
    Size size153 = size1;
    txtMdchAddress2.Size = size153;
    this.txtMdchAddress.TabIndex = 4;
    this.lblMdchName.AutoSize = true;
    this.lblMdchName.Enabled = false;
    this.lblMdchName.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblMdchName1 = this.lblMdchName;
    point1 = new Point(489, 15);
    Point point154 = point1;
    lblMdchName1.Location = point154;
    this.lblMdchName.Name = "lblMdchName";
    Label lblMdchName2 = this.lblMdchName;
    size1 = new Size(97, 16 /*0x10*/);
    Size size154 = size1;
    lblMdchName2.Size = size154;
    this.lblMdchName.TabIndex = 158;
    this.lblMdchName.Text = "MDCH Name";
    this.lblMdchAddress.AutoSize = true;
    this.lblMdchAddress.Enabled = false;
    this.lblMdchAddress.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblMdchAddress1 = this.lblMdchAddress;
    point1 = new Point(472, 49);
    Point point155 = point1;
    lblMdchAddress1.Location = point155;
    this.lblMdchAddress.Name = "lblMdchAddress";
    Label lblMdchAddress2 = this.lblMdchAddress;
    size1 = new Size(114, 16 /*0x10*/);
    Size size155 = size1;
    lblMdchAddress2.Size = size155;
    this.lblMdchAddress.TabIndex = 157;
    this.lblMdchAddress.Text = "MDCH Address";
    this.btnNew.BackColor = SystemColors.ButtonFace;
    this.btnNew.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnNew1 = this.btnNew;
    point1 = new Point(160 /*0xA0*/, 582);
    Point point156 = point1;
    btnNew1.Location = point156;
    this.btnNew.Name = "btnNew";
    Button btnNew2 = this.btnNew;
    size1 = new Size(48 /*0x30*/, 23);
    Size size156 = size1;
    btnNew2.Size = size156;
    this.btnNew.TabIndex = 148;
    this.btnNew.Text = "New";
    this.btnNew.UseVisualStyleBackColor = false;
    this.btnSave.BackColor = SystemColors.ButtonFace;
    this.btnSave.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnSave1 = this.btnSave;
    point1 = new Point(388, 582);
    Point point157 = point1;
    btnSave1.Location = point157;
    this.btnSave.Name = "btnSave";
    Button btnSave2 = this.btnSave;
    size1 = new Size(52, 23);
    Size size157 = size1;
    btnSave2.Size = size157;
    this.btnSave.TabIndex = 150;
    this.btnSave.Text = "Save";
    this.btnSave.UseVisualStyleBackColor = false;
    this.btnDelete.BackColor = SystemColors.ButtonFace;
    this.btnDelete.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnDelete1 = this.btnDelete;
    point1 = new Point(508, 582);
    Point point158 = point1;
    btnDelete1.Location = point158;
    this.btnDelete.Name = "btnDelete";
    Button btnDelete2 = this.btnDelete;
    size1 = new Size(62, 23);
    Size size158 = size1;
    btnDelete2.Size = size158;
    this.btnDelete.TabIndex = 151;
    this.btnDelete.Text = "Delete";
    this.btnDelete.UseVisualStyleBackColor = false;
    this.btnEdit.BackColor = SystemColors.ButtonFace;
    this.btnEdit.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnEdit1 = this.btnEdit;
    point1 = new Point(276, 582);
    Point point159 = point1;
    btnEdit1.Location = point159;
    this.btnEdit.Name = "btnEdit";
    Button btnEdit2 = this.btnEdit;
    size1 = new Size(48 /*0x30*/, 23);
    Size size159 = size1;
    btnEdit2.Size = size159;
    this.btnEdit.TabIndex = 149;
    this.btnEdit.Text = "Edit";
    this.btnEdit.UseVisualStyleBackColor = false;
    this.AcceptButton = (IButtonControl) this.btnEdit;
    this.AutoScaleDimensions = new SizeF(7f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.BackColor = Color.PowderBlue;
    this.CancelButton = (IButtonControl) this.btnExit;
    size1 = new Size(842, 617);
    this.ClientSize = size1;
    this.Controls.Add((Control) this.btnEdit);
    this.Controls.Add((Control) this.btnDelete);
    this.Controls.Add((Control) this.btnSave);
    this.Controls.Add((Control) this.btnNew);
    this.Controls.Add((Control) this.txtCcuName);
    this.Controls.Add((Control) this.txtCcuAddress);
    this.Controls.Add((Control) this.lblCcuName);
    this.Controls.Add((Control) this.lblCcuAddress);
    this.Controls.Add((Control) this.txtMdchName);
    this.Controls.Add((Control) this.txtMdchAddress);
    this.Controls.Add((Control) this.lblMdchName);
    this.Controls.Add((Control) this.lblMdchAddress);
    this.Controls.Add((Control) this.btnExit);
    this.Controls.Add((Control) this.GroupBox1);
    this.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.FormBorderStyle = FormBorderStyle.FixedToolWindow;
    this.Name = "frmNetworkMDCH";
    this.Text = "MDCH";
    this.GroupBox1.ResumeLayout(false);
    this.GroupBox1.PerformLayout();
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  internal virtual Button btnExit
  {
    [DebuggerNonUserCode] get { return this._btnExit; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnExit_Click);
      if (this._btnExit != null)
        this._btnExit.Click -= eventHandler;
      this._btnExit = value;
      if (this._btnExit == null)
        return;
      this._btnExit.Click += eventHandler;
    }
  }

  internal virtual GroupBox GroupBox1
  {
    [DebuggerNonUserCode] get { return this._GroupBox1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._GroupBox1 = value;
    }
  }

  internal virtual Button btnP16S4
  {
    [DebuggerNonUserCode] get { return this._btnP16S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP16S4_Click);
      if (this._btnP16S4 != null)
        this._btnP16S4.Click -= eventHandler;
      this._btnP16S4 = value;
      if (this._btnP16S4 == null)
        return;
      this._btnP16S4.Click += eventHandler;
    }
  }

  internal virtual Button btnP15S4
  {
    [DebuggerNonUserCode] get { return this._btnP15S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP15S4_Click);
      if (this._btnP15S4 != null)
        this._btnP15S4.Click -= eventHandler;
      this._btnP15S4 = value;
      if (this._btnP15S4 == null)
        return;
      this._btnP15S4.Click += eventHandler;
    }
  }

  internal virtual Button btnP14S4
  {
    [DebuggerNonUserCode] get { return this._btnP14S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP14S4_Click);
      if (this._btnP14S4 != null)
        this._btnP14S4.Click -= eventHandler;
      this._btnP14S4 = value;
      if (this._btnP14S4 == null)
        return;
      this._btnP14S4.Click += eventHandler;
    }
  }

  internal virtual Button btnP13S4
  {
    [DebuggerNonUserCode] get { return this._btnP13S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP13S4_Click);
      if (this._btnP13S4 != null)
        this._btnP13S4.Click -= eventHandler;
      this._btnP13S4 = value;
      if (this._btnP13S4 == null)
        return;
      this._btnP13S4.Click += eventHandler;
    }
  }

  internal virtual Button btnP12S4
  {
    [DebuggerNonUserCode] get { return this._btnP12S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP12S4_Click);
      if (this._btnP12S4 != null)
        this._btnP12S4.Click -= eventHandler;
      this._btnP12S4 = value;
      if (this._btnP12S4 == null)
        return;
      this._btnP12S4.Click += eventHandler;
    }
  }

  internal virtual Button btnP11S4
  {
    [DebuggerNonUserCode] get { return this._btnP11S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP11S4_Click);
      if (this._btnP11S4 != null)
        this._btnP11S4.Click -= eventHandler;
      this._btnP11S4 = value;
      if (this._btnP11S4 == null)
        return;
      this._btnP11S4.Click += eventHandler;
    }
  }

  internal virtual Button btnP10S4
  {
    [DebuggerNonUserCode] get { return this._btnP10S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP10S4_Click);
      if (this._btnP10S4 != null)
        this._btnP10S4.Click -= eventHandler;
      this._btnP10S4 = value;
      if (this._btnP10S4 == null)
        return;
      this._btnP10S4.Click += eventHandler;
    }
  }

  internal virtual Button btnP9S4
  {
    [DebuggerNonUserCode] get { return this._btnP9S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP9S4_Click);
      if (this._btnP9S4 != null)
        this._btnP9S4.Click -= eventHandler;
      this._btnP9S4 = value;
      if (this._btnP9S4 == null)
        return;
      this._btnP9S4.Click += eventHandler;
    }
  }

  internal virtual Button btnP8S4
  {
    [DebuggerNonUserCode] get { return this._btnP8S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP8S4_Click);
      if (this._btnP8S4 != null)
        this._btnP8S4.Click -= eventHandler;
      this._btnP8S4 = value;
      if (this._btnP8S4 == null)
        return;
      this._btnP8S4.Click += eventHandler;
    }
  }

  internal virtual Button btnP7S4
  {
    [DebuggerNonUserCode] get { return this._btnP7S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP7S4_Click);
      if (this._btnP7S4 != null)
        this._btnP7S4.Click -= eventHandler;
      this._btnP7S4 = value;
      if (this._btnP7S4 == null)
        return;
      this._btnP7S4.Click += eventHandler;
    }
  }

  internal virtual Button btnP6S4
  {
    [DebuggerNonUserCode] get { return this._btnP6S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP6S4_Click);
      if (this._btnP6S4 != null)
        this._btnP6S4.Click -= eventHandler;
      this._btnP6S4 = value;
      if (this._btnP6S4 == null)
        return;
      this._btnP6S4.Click += eventHandler;
    }
  }

  internal virtual Button btnP5S4
  {
    [DebuggerNonUserCode] get { return this._btnP5S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP5S4_Click);
      if (this._btnP5S4 != null)
        this._btnP5S4.Click -= eventHandler;
      this._btnP5S4 = value;
      if (this._btnP5S4 == null)
        return;
      this._btnP5S4.Click += eventHandler;
    }
  }

  internal virtual Button btnP4S4
  {
    [DebuggerNonUserCode] get { return this._btnP4S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP4S4_Click);
      if (this._btnP4S4 != null)
        this._btnP4S4.Click -= eventHandler;
      this._btnP4S4 = value;
      if (this._btnP4S4 == null)
        return;
      this._btnP4S4.Click += eventHandler;
    }
  }

  internal virtual Button btnP3S4
  {
    [DebuggerNonUserCode] get { return this._btnP3S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP3S4_Click);
      if (this._btnP3S4 != null)
        this._btnP3S4.Click -= eventHandler;
      this._btnP3S4 = value;
      if (this._btnP3S4 == null)
        return;
      this._btnP3S4.Click += eventHandler;
    }
  }

  internal virtual Button btnP2S4
  {
    [DebuggerNonUserCode] get { return this._btnP2S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP2S4_Click);
      if (this._btnP2S4 != null)
        this._btnP2S4.Click -= eventHandler;
      this._btnP2S4 = value;
      if (this._btnP2S4 == null)
        return;
      this._btnP2S4.Click += eventHandler;
    }
  }

  internal virtual Button btnP1S4
  {
    [DebuggerNonUserCode] get { return this._btnP1S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP1S4_Click);
      if (this._btnP1S4 != null)
        this._btnP1S4.Click -= eventHandler;
      this._btnP1S4 = value;
      if (this._btnP1S4 == null)
        return;
      this._btnP1S4.Click += eventHandler;
    }
  }

  internal virtual ComboBox cmbP16S4
  {
    [DebuggerNonUserCode] get { return this._cmbP16S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP16S4 = value; }
  }

  internal virtual ComboBox cmbP15S4
  {
    [DebuggerNonUserCode] get { return this._cmbP15S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP15S4 = value; }
  }

  internal virtual ComboBox cmbP14S4
  {
    [DebuggerNonUserCode] get { return this._cmbP14S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP14S4 = value; }
  }

  internal virtual ComboBox cmbP13S4
  {
    [DebuggerNonUserCode] get { return this._cmbP13S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP13S4 = value; }
  }

  internal virtual ComboBox cmbP12S4
  {
    [DebuggerNonUserCode] get { return this._cmbP12S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP12S4 = value; }
  }

  internal virtual ComboBox cmbP11S4
  {
    [DebuggerNonUserCode] get { return this._cmbP11S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP11S4 = value; }
  }

  internal virtual ComboBox cmbP10S4
  {
    [DebuggerNonUserCode] get { return this._cmbP10S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP10S4 = value; }
  }

  internal virtual ComboBox cmbP9S4
  {
    [DebuggerNonUserCode] get { return this._cmbP9S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP9S4 = value; }
  }

  internal virtual ComboBox cmbP8S4
  {
    [DebuggerNonUserCode] get { return this._cmbP8S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP8S4 = value; }
  }

  internal virtual ComboBox cmbP7S4
  {
    [DebuggerNonUserCode] get { return this._cmbP7S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP7S4 = value; }
  }

  internal virtual ComboBox cmbP6S4
  {
    [DebuggerNonUserCode] get { return this._cmbP6S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP6S4 = value; }
  }

  internal virtual ComboBox cmbP5S4
  {
    [DebuggerNonUserCode] get { return this._cmbP5S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP5S4 = value; }
  }

  internal virtual ComboBox cmbP4S4
  {
    [DebuggerNonUserCode] get { return this._cmbP4S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP4S4 = value; }
  }

  internal virtual ComboBox cmbP3S4
  {
    [DebuggerNonUserCode] get { return this._cmbP3S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP3S4 = value; }
  }

  internal virtual ComboBox cmbP2S4
  {
    [DebuggerNonUserCode] get { return this._cmbP2S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP2S4 = value; }
  }

  internal virtual ComboBox cmbP1S4
  {
    [DebuggerNonUserCode] get { return this._cmbP1S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP1S4 = value; }
  }

  internal virtual Button btnP16S3
  {
    [DebuggerNonUserCode] get { return this._btnP16S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP16S3_Click);
      if (this._btnP16S3 != null)
        this._btnP16S3.Click -= eventHandler;
      this._btnP16S3 = value;
      if (this._btnP16S3 == null)
        return;
      this._btnP16S3.Click += eventHandler;
    }
  }

  internal virtual Button btnP15S3
  {
    [DebuggerNonUserCode] get { return this._btnP15S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP15S3_Click);
      if (this._btnP15S3 != null)
        this._btnP15S3.Click -= eventHandler;
      this._btnP15S3 = value;
      if (this._btnP15S3 == null)
        return;
      this._btnP15S3.Click += eventHandler;
    }
  }

  internal virtual Button btnP14S3
  {
    [DebuggerNonUserCode] get { return this._btnP14S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP14S3_Click);
      if (this._btnP14S3 != null)
        this._btnP14S3.Click -= eventHandler;
      this._btnP14S3 = value;
      if (this._btnP14S3 == null)
        return;
      this._btnP14S3.Click += eventHandler;
    }
  }

  internal virtual Button btnP13S3
  {
    [DebuggerNonUserCode] get { return this._btnP13S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP13S3_Click);
      if (this._btnP13S3 != null)
        this._btnP13S3.Click -= eventHandler;
      this._btnP13S3 = value;
      if (this._btnP13S3 == null)
        return;
      this._btnP13S3.Click += eventHandler;
    }
  }

  internal virtual Button btnP12S3
  {
    [DebuggerNonUserCode] get { return this._btnP12S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP12S3_Click);
      if (this._btnP12S3 != null)
        this._btnP12S3.Click -= eventHandler;
      this._btnP12S3 = value;
      if (this._btnP12S3 == null)
        return;
      this._btnP12S3.Click += eventHandler;
    }
  }

  internal virtual Button btnP11S3
  {
    [DebuggerNonUserCode] get { return this._btnP11S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP11S3_Click);
      if (this._btnP11S3 != null)
        this._btnP11S3.Click -= eventHandler;
      this._btnP11S3 = value;
      if (this._btnP11S3 == null)
        return;
      this._btnP11S3.Click += eventHandler;
    }
  }

  internal virtual Button btnP10S3
  {
    [DebuggerNonUserCode] get { return this._btnP10S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP10S3_Click);
      if (this._btnP10S3 != null)
        this._btnP10S3.Click -= eventHandler;
      this._btnP10S3 = value;
      if (this._btnP10S3 == null)
        return;
      this._btnP10S3.Click += eventHandler;
    }
  }

  internal virtual Button btnP9S3
  {
    [DebuggerNonUserCode] get { return this._btnP9S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP9S3_Click);
      if (this._btnP9S3 != null)
        this._btnP9S3.Click -= eventHandler;
      this._btnP9S3 = value;
      if (this._btnP9S3 == null)
        return;
      this._btnP9S3.Click += eventHandler;
    }
  }

  internal virtual Button btnP8S3
  {
    [DebuggerNonUserCode] get { return this._btnP8S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP8S3_Click);
      if (this._btnP8S3 != null)
        this._btnP8S3.Click -= eventHandler;
      this._btnP8S3 = value;
      if (this._btnP8S3 == null)
        return;
      this._btnP8S3.Click += eventHandler;
    }
  }

  internal virtual Button btnP7S3
  {
    [DebuggerNonUserCode] get { return this._btnP7S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP7S3_Click);
      if (this._btnP7S3 != null)
        this._btnP7S3.Click -= eventHandler;
      this._btnP7S3 = value;
      if (this._btnP7S3 == null)
        return;
      this._btnP7S3.Click += eventHandler;
    }
  }

  internal virtual Button btnP6S3
  {
    [DebuggerNonUserCode] get { return this._btnP6S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP6S3_Click);
      if (this._btnP6S3 != null)
        this._btnP6S3.Click -= eventHandler;
      this._btnP6S3 = value;
      if (this._btnP6S3 == null)
        return;
      this._btnP6S3.Click += eventHandler;
    }
  }

  internal virtual Button btnP5S3
  {
    [DebuggerNonUserCode] get { return this._btnP5S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP5S3_Click);
      if (this._btnP5S3 != null)
        this._btnP5S3.Click -= eventHandler;
      this._btnP5S3 = value;
      if (this._btnP5S3 == null)
        return;
      this._btnP5S3.Click += eventHandler;
    }
  }

  internal virtual Button btnP4S3
  {
    [DebuggerNonUserCode] get { return this._btnP4S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP4S3_Click);
      if (this._btnP4S3 != null)
        this._btnP4S3.Click -= eventHandler;
      this._btnP4S3 = value;
      if (this._btnP4S3 == null)
        return;
      this._btnP4S3.Click += eventHandler;
    }
  }

  internal virtual Button btnP3S3
  {
    [DebuggerNonUserCode] get { return this._btnP3S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP3S3_Click);
      if (this._btnP3S3 != null)
        this._btnP3S3.Click -= eventHandler;
      this._btnP3S3 = value;
      if (this._btnP3S3 == null)
        return;
      this._btnP3S3.Click += eventHandler;
    }
  }

  internal virtual Button btnP2S3
  {
    [DebuggerNonUserCode] get { return this._btnP2S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP2S3_Click);
      if (this._btnP2S3 != null)
        this._btnP2S3.Click -= eventHandler;
      this._btnP2S3 = value;
      if (this._btnP2S3 == null)
        return;
      this._btnP2S3.Click += eventHandler;
    }
  }

  internal virtual Button btnP1S3
  {
    [DebuggerNonUserCode] get { return this._btnP1S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP1S3_Click);
      if (this._btnP1S3 != null)
        this._btnP1S3.Click -= eventHandler;
      this._btnP1S3 = value;
      if (this._btnP1S3 == null)
        return;
      this._btnP1S3.Click += eventHandler;
    }
  }

  internal virtual ComboBox cmbP16S3
  {
    [DebuggerNonUserCode] get { return this._cmbP16S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP16S3 = value; }
  }

  internal virtual ComboBox cmbP15S3
  {
    [DebuggerNonUserCode] get { return this._cmbP15S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP15S3 = value; }
  }

  internal virtual ComboBox cmbP14S3
  {
    [DebuggerNonUserCode] get { return this._cmbP14S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP14S3 = value; }
  }

  internal virtual ComboBox cmbP13S3
  {
    [DebuggerNonUserCode] get { return this._cmbP13S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP13S3 = value; }
  }

  internal virtual ComboBox cmbP12S3
  {
    [DebuggerNonUserCode] get { return this._cmbP12S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP12S3 = value; }
  }

  internal virtual ComboBox cmbP11S3
  {
    [DebuggerNonUserCode] get { return this._cmbP11S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP11S3 = value; }
  }

  internal virtual ComboBox cmbP10S3
  {
    [DebuggerNonUserCode] get { return this._cmbP10S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP10S3 = value; }
  }

  internal virtual ComboBox cmbP9S3
  {
    [DebuggerNonUserCode] get { return this._cmbP9S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP9S3 = value; }
  }

  internal virtual ComboBox cmbP8S3
  {
    [DebuggerNonUserCode] get { return this._cmbP8S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP8S3 = value; }
  }

  internal virtual ComboBox cmbP7S3
  {
    [DebuggerNonUserCode] get { return this._cmbP7S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP7S3 = value; }
  }

  internal virtual ComboBox cmbP6S3
  {
    [DebuggerNonUserCode] get { return this._cmbP6S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP6S3 = value; }
  }

  internal virtual ComboBox cmbP5S3
  {
    [DebuggerNonUserCode] get { return this._cmbP5S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP5S3 = value; }
  }

  internal virtual ComboBox cmbP4S3
  {
    [DebuggerNonUserCode] get { return this._cmbP4S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP4S3 = value; }
  }

  internal virtual ComboBox cmbP3S3
  {
    [DebuggerNonUserCode] get { return this._cmbP3S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP3S3 = value; }
  }

  internal virtual ComboBox cmbP2S3
  {
    [DebuggerNonUserCode] get { return this._cmbP2S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP2S3 = value; }
  }

  internal virtual ComboBox cmbP1S3
  {
    [DebuggerNonUserCode] get { return this._cmbP1S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP1S3 = value; }
  }

  internal virtual Button btnP16S2
  {
    [DebuggerNonUserCode] get { return this._btnP16S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP16S2_Click);
      if (this._btnP16S2 != null)
        this._btnP16S2.Click -= eventHandler;
      this._btnP16S2 = value;
      if (this._btnP16S2 == null)
        return;
      this._btnP16S2.Click += eventHandler;
    }
  }

  internal virtual Button btnP15S2
  {
    [DebuggerNonUserCode] get { return this._btnP15S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP15S2_Click);
      if (this._btnP15S2 != null)
        this._btnP15S2.Click -= eventHandler;
      this._btnP15S2 = value;
      if (this._btnP15S2 == null)
        return;
      this._btnP15S2.Click += eventHandler;
    }
  }

  internal virtual Button btnP14S2
  {
    [DebuggerNonUserCode] get { return this._btnP14S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP14S2_Click);
      if (this._btnP14S2 != null)
        this._btnP14S2.Click -= eventHandler;
      this._btnP14S2 = value;
      if (this._btnP14S2 == null)
        return;
      this._btnP14S2.Click += eventHandler;
    }
  }

  internal virtual Button btnP13S2
  {
    [DebuggerNonUserCode] get { return this._btnP13S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP13S2_Click);
      if (this._btnP13S2 != null)
        this._btnP13S2.Click -= eventHandler;
      this._btnP13S2 = value;
      if (this._btnP13S2 == null)
        return;
      this._btnP13S2.Click += eventHandler;
    }
  }

  internal virtual Button btnP12S2
  {
    [DebuggerNonUserCode] get { return this._btnP12S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP12S2_Click);
      if (this._btnP12S2 != null)
        this._btnP12S2.Click -= eventHandler;
      this._btnP12S2 = value;
      if (this._btnP12S2 == null)
        return;
      this._btnP12S2.Click += eventHandler;
    }
  }

  internal virtual Button btnP11S2
  {
    [DebuggerNonUserCode] get { return this._btnP11S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP11S2_Click);
      if (this._btnP11S2 != null)
        this._btnP11S2.Click -= eventHandler;
      this._btnP11S2 = value;
      if (this._btnP11S2 == null)
        return;
      this._btnP11S2.Click += eventHandler;
    }
  }

  internal virtual Button btnP10S2
  {
    [DebuggerNonUserCode] get { return this._btnP10S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP10S2_Click);
      if (this._btnP10S2 != null)
        this._btnP10S2.Click -= eventHandler;
      this._btnP10S2 = value;
      if (this._btnP10S2 == null)
        return;
      this._btnP10S2.Click += eventHandler;
    }
  }

  internal virtual Button btnP9S2
  {
    [DebuggerNonUserCode] get { return this._btnP9S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP9S2_Click);
      if (this._btnP9S2 != null)
        this._btnP9S2.Click -= eventHandler;
      this._btnP9S2 = value;
      if (this._btnP9S2 == null)
        return;
      this._btnP9S2.Click += eventHandler;
    }
  }

  internal virtual Button btnP8S2
  {
    [DebuggerNonUserCode] get { return this._btnP8S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP8S2_Click);
      if (this._btnP8S2 != null)
        this._btnP8S2.Click -= eventHandler;
      this._btnP8S2 = value;
      if (this._btnP8S2 == null)
        return;
      this._btnP8S2.Click += eventHandler;
    }
  }

  internal virtual Button btnP7S2
  {
    [DebuggerNonUserCode] get { return this._btnP7S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP7S2_Click);
      if (this._btnP7S2 != null)
        this._btnP7S2.Click -= eventHandler;
      this._btnP7S2 = value;
      if (this._btnP7S2 == null)
        return;
      this._btnP7S2.Click += eventHandler;
    }
  }

  internal virtual Button btnP6S2
  {
    [DebuggerNonUserCode] get { return this._btnP6S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP6S2_Click);
      if (this._btnP6S2 != null)
        this._btnP6S2.Click -= eventHandler;
      this._btnP6S2 = value;
      if (this._btnP6S2 == null)
        return;
      this._btnP6S2.Click += eventHandler;
    }
  }

  internal virtual Button btnP5S2
  {
    [DebuggerNonUserCode] get { return this._btnP5S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP5S2_Click);
      if (this._btnP5S2 != null)
        this._btnP5S2.Click -= eventHandler;
      this._btnP5S2 = value;
      if (this._btnP5S2 == null)
        return;
      this._btnP5S2.Click += eventHandler;
    }
  }

  internal virtual Button btnP4S2
  {
    [DebuggerNonUserCode] get { return this._btnP4S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP4S2_Click);
      if (this._btnP4S2 != null)
        this._btnP4S2.Click -= eventHandler;
      this._btnP4S2 = value;
      if (this._btnP4S2 == null)
        return;
      this._btnP4S2.Click += eventHandler;
    }
  }

  internal virtual Button btnP3S2
  {
    [DebuggerNonUserCode] get { return this._btnP3S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP3S2_Click);
      if (this._btnP3S2 != null)
        this._btnP3S2.Click -= eventHandler;
      this._btnP3S2 = value;
      if (this._btnP3S2 == null)
        return;
      this._btnP3S2.Click += eventHandler;
    }
  }

  internal virtual Button btnP2S2
  {
    [DebuggerNonUserCode] get { return this._btnP2S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP2S2_Click);
      if (this._btnP2S2 != null)
        this._btnP2S2.Click -= eventHandler;
      this._btnP2S2 = value;
      if (this._btnP2S2 == null)
        return;
      this._btnP2S2.Click += eventHandler;
    }
  }

  internal virtual Button btnP1S2
  {
    [DebuggerNonUserCode] get { return this._btnP1S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP1S2_Click);
      if (this._btnP1S2 != null)
        this._btnP1S2.Click -= eventHandler;
      this._btnP1S2 = value;
      if (this._btnP1S2 == null)
        return;
      this._btnP1S2.Click += eventHandler;
    }
  }

  internal virtual ComboBox cmbP16S2
  {
    [DebuggerNonUserCode] get { return this._cmbP16S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP16S2 = value; }
  }

  internal virtual ComboBox cmbP15S2
  {
    [DebuggerNonUserCode] get { return this._cmbP15S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP15S2 = value; }
  }

  internal virtual ComboBox cmbP14S2
  {
    [DebuggerNonUserCode] get { return this._cmbP14S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP14S2 = value; }
  }

  internal virtual ComboBox cmbP13S2
  {
    [DebuggerNonUserCode] get { return this._cmbP13S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP13S2 = value; }
  }

  internal virtual ComboBox cmbP12S2
  {
    [DebuggerNonUserCode] get { return this._cmbP12S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP12S2 = value; }
  }

  internal virtual ComboBox cmbP11S2
  {
    [DebuggerNonUserCode] get { return this._cmbP11S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP11S2 = value; }
  }

  internal virtual ComboBox cmbP10S2
  {
    [DebuggerNonUserCode] get { return this._cmbP10S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP10S2 = value; }
  }

  internal virtual ComboBox cmbP9S2
  {
    [DebuggerNonUserCode] get { return this._cmbP9S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP9S2 = value; }
  }

  internal virtual ComboBox cmbP8S2
  {
    [DebuggerNonUserCode] get { return this._cmbP8S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP8S2 = value; }
  }

  internal virtual ComboBox cmbP7S2
  {
    [DebuggerNonUserCode] get { return this._cmbP7S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP7S2 = value; }
  }

  internal virtual ComboBox cmbP6S2
  {
    [DebuggerNonUserCode] get { return this._cmbP6S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP6S2 = value; }
  }

  internal virtual ComboBox cmbP5S2
  {
    [DebuggerNonUserCode] get { return this._cmbP5S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP5S2 = value; }
  }

  internal virtual ComboBox cmbP4S2
  {
    [DebuggerNonUserCode] get { return this._cmbP4S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP4S2 = value; }
  }

  internal virtual ComboBox cmbP3S2
  {
    [DebuggerNonUserCode] get { return this._cmbP3S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP3S2 = value; }
  }

  internal virtual ComboBox cmbP2S2
  {
    [DebuggerNonUserCode] get { return this._cmbP2S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP2S2 = value; }
  }

  internal virtual ComboBox cmbP1S2
  {
    [DebuggerNonUserCode] get { return this._cmbP1S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP1S2 = value; }
  }

  internal virtual Button btnP16S1
  {
    [DebuggerNonUserCode] get { return this._btnP16S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP16S1_Click);
      if (this._btnP16S1 != null)
        this._btnP16S1.Click -= eventHandler;
      this._btnP16S1 = value;
      if (this._btnP16S1 == null)
        return;
      this._btnP16S1.Click += eventHandler;
    }
  }

  internal virtual Button btnP15S1
  {
    [DebuggerNonUserCode] get { return this._btnP15S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP15S1_Click);
      if (this._btnP15S1 != null)
        this._btnP15S1.Click -= eventHandler;
      this._btnP15S1 = value;
      if (this._btnP15S1 == null)
        return;
      this._btnP15S1.Click += eventHandler;
    }
  }

  internal virtual Button btnP14S1
  {
    [DebuggerNonUserCode] get { return this._btnP14S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP14S1_Click);
      if (this._btnP14S1 != null)
        this._btnP14S1.Click -= eventHandler;
      this._btnP14S1 = value;
      if (this._btnP14S1 == null)
        return;
      this._btnP14S1.Click += eventHandler;
    }
  }

  internal virtual Button btnP13S1
  {
    [DebuggerNonUserCode] get { return this._btnP13S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP13S1_Click);
      if (this._btnP13S1 != null)
        this._btnP13S1.Click -= eventHandler;
      this._btnP13S1 = value;
      if (this._btnP13S1 == null)
        return;
      this._btnP13S1.Click += eventHandler;
    }
  }

  internal virtual Button btnP12S1
  {
    [DebuggerNonUserCode] get { return this._btnP12S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP12S1_Click);
      if (this._btnP12S1 != null)
        this._btnP12S1.Click -= eventHandler;
      this._btnP12S1 = value;
      if (this._btnP12S1 == null)
        return;
      this._btnP12S1.Click += eventHandler;
    }
  }

  internal virtual Button btnP11S1
  {
    [DebuggerNonUserCode] get { return this._btnP11S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP11S1_Click);
      if (this._btnP11S1 != null)
        this._btnP11S1.Click -= eventHandler;
      this._btnP11S1 = value;
      if (this._btnP11S1 == null)
        return;
      this._btnP11S1.Click += eventHandler;
    }
  }

  internal virtual Button btnP10S1
  {
    [DebuggerNonUserCode] get { return this._btnP10S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP10S1_Click);
      if (this._btnP10S1 != null)
        this._btnP10S1.Click -= eventHandler;
      this._btnP10S1 = value;
      if (this._btnP10S1 == null)
        return;
      this._btnP10S1.Click += eventHandler;
    }
  }

  internal virtual Button btnP9S1
  {
    [DebuggerNonUserCode] get { return this._btnP9S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP9S1_Click);
      if (this._btnP9S1 != null)
        this._btnP9S1.Click -= eventHandler;
      this._btnP9S1 = value;
      if (this._btnP9S1 == null)
        return;
      this._btnP9S1.Click += eventHandler;
    }
  }

  internal virtual Button btnP8S1
  {
    [DebuggerNonUserCode] get { return this._btnP8S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP8S1_Click);
      if (this._btnP8S1 != null)
        this._btnP8S1.Click -= eventHandler;
      this._btnP8S1 = value;
      if (this._btnP8S1 == null)
        return;
      this._btnP8S1.Click += eventHandler;
    }
  }

  internal virtual Button btnP7S1
  {
    [DebuggerNonUserCode] get { return this._btnP7S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP7S1_Click);
      if (this._btnP7S1 != null)
        this._btnP7S1.Click -= eventHandler;
      this._btnP7S1 = value;
      if (this._btnP7S1 == null)
        return;
      this._btnP7S1.Click += eventHandler;
    }
  }

  internal virtual Button btnP6S1
  {
    [DebuggerNonUserCode] get { return this._btnP6S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP6S1_Click);
      if (this._btnP6S1 != null)
        this._btnP6S1.Click -= eventHandler;
      this._btnP6S1 = value;
      if (this._btnP6S1 == null)
        return;
      this._btnP6S1.Click += eventHandler;
    }
  }

  internal virtual Button btnP5S1
  {
    [DebuggerNonUserCode] get { return this._btnP5S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP5S1_Click);
      if (this._btnP5S1 != null)
        this._btnP5S1.Click -= eventHandler;
      this._btnP5S1 = value;
      if (this._btnP5S1 == null)
        return;
      this._btnP5S1.Click += eventHandler;
    }
  }

  internal virtual Button btnP4S1
  {
    [DebuggerNonUserCode] get { return this._btnP4S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP4S1_Click);
      if (this._btnP4S1 != null)
        this._btnP4S1.Click -= eventHandler;
      this._btnP4S1 = value;
      if (this._btnP4S1 == null)
        return;
      this._btnP4S1.Click += eventHandler;
    }
  }

  internal virtual Button btnP3S1
  {
    [DebuggerNonUserCode] get { return this._btnP3S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP3S1_Click);
      if (this._btnP3S1 != null)
        this._btnP3S1.Click -= eventHandler;
      this._btnP3S1 = value;
      if (this._btnP3S1 == null)
        return;
      this._btnP3S1.Click += eventHandler;
    }
  }

  internal virtual Button btnP2S1
  {
    [DebuggerNonUserCode] get { return this._btnP2S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP2S1_Click);
      if (this._btnP2S1 != null)
        this._btnP2S1.Click -= eventHandler;
      this._btnP2S1 = value;
      if (this._btnP2S1 == null)
        return;
      this._btnP2S1.Click += eventHandler;
    }
  }

  internal virtual Button btnP1S1
  {
    [DebuggerNonUserCode] get { return this._btnP1S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP1S1_Click);
      if (this._btnP1S1 != null)
        this._btnP1S1.Click -= eventHandler;
      this._btnP1S1 = value;
      if (this._btnP1S1 == null)
        return;
      this._btnP1S1.Click += eventHandler;
    }
  }

  internal virtual ComboBox cmbP16S1
  {
    [DebuggerNonUserCode] get { return this._cmbP16S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP16S1 = value; }
  }

  internal virtual ComboBox cmbP15S1
  {
    [DebuggerNonUserCode] get { return this._cmbP15S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP15S1 = value; }
  }

  internal virtual ComboBox cmbP14S1
  {
    [DebuggerNonUserCode] get { return this._cmbP14S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP14S1 = value; }
  }

  internal virtual ComboBox cmbP13S1
  {
    [DebuggerNonUserCode] get { return this._cmbP13S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP13S1 = value; }
  }

  internal virtual ComboBox cmbP12S1
  {
    [DebuggerNonUserCode] get { return this._cmbP12S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP12S1 = value; }
  }

  internal virtual ComboBox cmbP11S1
  {
    [DebuggerNonUserCode] get { return this._cmbP11S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP11S1 = value; }
  }

  internal virtual ComboBox cmbP10S1
  {
    [DebuggerNonUserCode] get { return this._cmbP10S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP10S1 = value; }
  }

  internal virtual ComboBox cmbP9S1
  {
    [DebuggerNonUserCode] get { return this._cmbP9S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP9S1 = value; }
  }

  internal virtual ComboBox cmbP8S1
  {
    [DebuggerNonUserCode] get { return this._cmbP8S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP8S1 = value; }
  }

  internal virtual ComboBox cmbP7S1
  {
    [DebuggerNonUserCode] get { return this._cmbP7S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP7S1 = value; }
  }

  internal virtual ComboBox cmbP6S1
  {
    [DebuggerNonUserCode] get { return this._cmbP6S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP6S1 = value; }
  }

  internal virtual ComboBox cmbP5S1
  {
    [DebuggerNonUserCode] get { return this._cmbP5S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP5S1 = value; }
  }

  internal virtual ComboBox cmbP4S1
  {
    [DebuggerNonUserCode] get { return this._cmbP4S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP4S1 = value; }
  }

  internal virtual ComboBox cmbP3S1
  {
    [DebuggerNonUserCode] get { return this._cmbP3S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP3S1 = value; }
  }

  internal virtual ComboBox cmbP2S1
  {
    [DebuggerNonUserCode] get { return this._cmbP2S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP2S1 = value; }
  }

  internal virtual ComboBox cmbP1S1
  {
    [DebuggerNonUserCode] get { return this._cmbP1S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP1S1 = value; }
  }

  internal virtual CheckBox chkPort16
  {
    [DebuggerNonUserCode] get { return this._chkPort16; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.chkPort16_CheckedChanged);
      if (this._chkPort16 != null)
        this._chkPort16.CheckedChanged -= eventHandler;
      this._chkPort16 = value;
      if (this._chkPort16 == null)
        return;
      this._chkPort16.CheckedChanged += eventHandler;
    }
  }

  internal virtual CheckBox chkPort15
  {
    [DebuggerNonUserCode] get { return this._chkPort15; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.chkPort15_CheckedChanged);
      if (this._chkPort15 != null)
        this._chkPort15.CheckedChanged -= eventHandler;
      this._chkPort15 = value;
      if (this._chkPort15 == null)
        return;
      this._chkPort15.CheckedChanged += eventHandler;
    }
  }

  internal virtual CheckBox chkPort14
  {
    [DebuggerNonUserCode] get { return this._chkPort14; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.chkPort14_CheckedChanged);
      if (this._chkPort14 != null)
        this._chkPort14.CheckedChanged -= eventHandler;
      this._chkPort14 = value;
      if (this._chkPort14 == null)
        return;
      this._chkPort14.CheckedChanged += eventHandler;
    }
  }

  internal virtual CheckBox chkPort13
  {
    [DebuggerNonUserCode] get { return this._chkPort13; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.chkPort13_CheckedChanged);
      if (this._chkPort13 != null)
        this._chkPort13.CheckedChanged -= eventHandler;
      this._chkPort13 = value;
      if (this._chkPort13 == null)
        return;
      this._chkPort13.CheckedChanged += eventHandler;
    }
  }

  internal virtual CheckBox chkPort12
  {
    [DebuggerNonUserCode] get { return this._chkPort12; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.chkPort12_CheckedChanged);
      if (this._chkPort12 != null)
        this._chkPort12.CheckedChanged -= eventHandler;
      this._chkPort12 = value;
      if (this._chkPort12 == null)
        return;
      this._chkPort12.CheckedChanged += eventHandler;
    }
  }

  internal virtual CheckBox chkPort11
  {
    [DebuggerNonUserCode] get { return this._chkPort11; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.chkPort11_CheckedChanged);
      if (this._chkPort11 != null)
        this._chkPort11.CheckedChanged -= eventHandler;
      this._chkPort11 = value;
      if (this._chkPort11 == null)
        return;
      this._chkPort11.CheckedChanged += eventHandler;
    }
  }

  internal virtual CheckBox chkPort10
  {
    [DebuggerNonUserCode] get { return this._chkPort10; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.chkPort10_CheckedChanged);
      if (this._chkPort10 != null)
        this._chkPort10.CheckedChanged -= eventHandler;
      this._chkPort10 = value;
      if (this._chkPort10 == null)
        return;
      this._chkPort10.CheckedChanged += eventHandler;
    }
  }

  internal virtual CheckBox chkPort9
  {
    [DebuggerNonUserCode] get { return this._chkPort9; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.chkPort9_CheckedChanged);
      if (this._chkPort9 != null)
        this._chkPort9.CheckedChanged -= eventHandler;
      this._chkPort9 = value;
      if (this._chkPort9 == null)
        return;
      this._chkPort9.CheckedChanged += eventHandler;
    }
  }

  internal virtual CheckBox chkPort8
  {
    [DebuggerNonUserCode] get { return this._chkPort8; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.chkPort8_CheckedChanged);
      if (this._chkPort8 != null)
        this._chkPort8.CheckedChanged -= eventHandler;
      this._chkPort8 = value;
      if (this._chkPort8 == null)
        return;
      this._chkPort8.CheckedChanged += eventHandler;
    }
  }

  internal virtual CheckBox chkPort7
  {
    [DebuggerNonUserCode] get { return this._chkPort7; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.chkPort7_CheckedChanged);
      if (this._chkPort7 != null)
        this._chkPort7.CheckedChanged -= eventHandler;
      this._chkPort7 = value;
      if (this._chkPort7 == null)
        return;
      this._chkPort7.CheckedChanged += eventHandler;
    }
  }

  internal virtual CheckBox chkPort6
  {
    [DebuggerNonUserCode] get { return this._chkPort6; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.chkPort6_CheckedChanged);
      if (this._chkPort6 != null)
        this._chkPort6.CheckedChanged -= eventHandler;
      this._chkPort6 = value;
      if (this._chkPort6 == null)
        return;
      this._chkPort6.CheckedChanged += eventHandler;
    }
  }

  internal virtual CheckBox chkPort5
  {
    [DebuggerNonUserCode] get { return this._chkPort5; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.chkPort5_CheckedChanged);
      if (this._chkPort5 != null)
        this._chkPort5.CheckedChanged -= eventHandler;
      this._chkPort5 = value;
      if (this._chkPort5 == null)
        return;
      this._chkPort5.CheckedChanged += eventHandler;
    }
  }

  internal virtual CheckBox chkPort4
  {
    [DebuggerNonUserCode] get { return this._chkPort4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.chkPort4_CheckedChanged);
      if (this._chkPort4 != null)
        this._chkPort4.CheckedChanged -= eventHandler;
      this._chkPort4 = value;
      if (this._chkPort4 == null)
        return;
      this._chkPort4.CheckedChanged += eventHandler;
    }
  }

  internal virtual CheckBox chkPort3
  {
    [DebuggerNonUserCode] get { return this._chkPort3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.chkPort3_CheckedChanged);
      if (this._chkPort3 != null)
        this._chkPort3.CheckedChanged -= eventHandler;
      this._chkPort3 = value;
      if (this._chkPort3 == null)
        return;
      this._chkPort3.CheckedChanged += eventHandler;
    }
  }

  internal virtual CheckBox chkPort2
  {
    [DebuggerNonUserCode] get { return this._chkPort2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.chkPort2_CheckedChanged);
      if (this._chkPort2 != null)
        this._chkPort2.CheckedChanged -= eventHandler;
      this._chkPort2 = value;
      if (this._chkPort2 == null)
        return;
      this._chkPort2.CheckedChanged += eventHandler;
    }
  }

  internal virtual CheckBox chkPort1
  {
    [DebuggerNonUserCode] get { return this._chkPort1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.chkPort1_CheckedChanged);
      if (this._chkPort1 != null)
        this._chkPort1.CheckedChanged -= eventHandler;
      this._chkPort1 = value;
      if (this._chkPort1 == null)
        return;
      this._chkPort1.CheckedChanged += eventHandler;
    }
  }

  internal virtual TextBox txtCcuName
  {
    [DebuggerNonUserCode] get { return this._txtCcuName; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtCcuName = value;
    }
  }

  internal virtual TextBox txtCcuAddress
  {
    [DebuggerNonUserCode] get { return this._txtCcuAddress; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtCcuAddress = value;
    }
  }

  internal virtual Label lblCcuName
  {
    [DebuggerNonUserCode] get { return this._lblCcuName; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblCcuName = value;
    }
  }

  internal virtual Label lblCcuAddress
  {
    [DebuggerNonUserCode] get { return this._lblCcuAddress; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblCcuAddress = value;
    }
  }

  internal virtual TextBox txtMdchName
  {
    [DebuggerNonUserCode] get { return this._txtMdchName; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtMdchName = value;
    }
  }

  internal virtual TextBox txtMdchAddress
  {
    [DebuggerNonUserCode] get { return this._txtMdchAddress; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtMdchAddress = value;
    }
  }

  internal virtual Label lblMdchName
  {
    [DebuggerNonUserCode] get { return this._lblMdchName; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblMdchName = value;
    }
  }

  internal virtual Label lblMdchAddress
  {
    [DebuggerNonUserCode] get { return this._lblMdchAddress; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblMdchAddress = value;
    }
  }

  internal virtual Button btnNew
  {
    [DebuggerNonUserCode] get { return this._btnNew; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnAdd_Click);
      if (this._btnNew != null)
        this._btnNew.Click -= eventHandler;
      this._btnNew = value;
      if (this._btnNew == null)
        return;
      this._btnNew.Click += eventHandler;
    }
  }

  internal virtual Button btnSave
  {
    [DebuggerNonUserCode] get { return this._btnSave; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnSave_Click);
      if (this._btnSave != null)
        this._btnSave.Click -= eventHandler;
      this._btnSave = value;
      if (this._btnSave == null)
        return;
      this._btnSave.Click += eventHandler;
    }
  }

  internal virtual Button btnDelete
  {
    [DebuggerNonUserCode] get { return this._btnDelete; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnDelete_Click);
      if (this._btnDelete != null)
        this._btnDelete.Click -= eventHandler;
      this._btnDelete = value;
      if (this._btnDelete == null)
        return;
      this._btnDelete.Click += eventHandler;
    }
  }

  internal virtual Button btnEdit
  {
    [DebuggerNonUserCode] get { return this._btnEdit; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnEdit_Click);
      if (this._btnEdit != null)
        this._btnEdit.Click -= eventHandler;
      this._btnEdit = value;
      if (this._btnEdit == null)
        return;
      this._btnEdit.Click += eventHandler;
    }
  }

  protected virtual frmNetworkPDCH event_pdch
  {
    [DebuggerNonUserCode] get { return this._event_pdch; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._event_pdch = value;
    }
  }

  protected virtual frmNetworkAGDB event_agdb
  {
    [DebuggerNonUserCode] get { return this._event_agdb; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._event_agdb = value;
    }
  }

  protected virtual frmNetworkMLDB event_mldb
  {
    [DebuggerNonUserCode] get { return this._event_mldb; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._event_mldb = value;
    }
  }

  protected virtual frmNetworkPDB event_pdb
  {
    [DebuggerNonUserCode] get { return this._event_pdb; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._event_pdb = value;
    }
  }

  private void btnExit_Click(object sender, EventArgs e)
  {
    frmMainFormIPIS.mdch_db_struc_init();
    // TODO: Implement missing methods in taddb_msg class
    // taddb_msg.mldb_dis_brd_init();
    // taddb_msg.pdb_dis_brd_init();
    // taddb_msg.agdb_dis_brd_init();
    cgdb_dis.cgdb_dis_brd_init();
    frmMainFormIPIS.hub_init();
    lock (this)
    {
      network_db_read.get_nw_database();
      network_db_read.get_mldb_dis_brd_info();
      network_db_read.get_pdb_dis_brd_info();
      network_db_read.get_agdb_dis_brd_info();
      network_db_read.get_cgdb_dis_brd_info();
      network_db_read.get_mdch_port_info();
      network_db_read.get_pdch_port_info();
    }
    this.Close();
  }

  private void btnEdit_Click(object sender, EventArgs e)
  {
    int index = 0;
    while (index < frmMainFormIPIS.online_train_cnt)
    {
      if (frmMainFormIPIS.online_train_data[index].display_checked)
      {
        int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "First Uncheck the TADDB in all rows", "Msg Box", 0, 0, 0);
        return;
      }
      checked { ++index; }
    }
    ComboBox[] cmb_system_type = new ComboBox[4];
    try
    {
      lock (this)
      {
        network_db_read.get_nw_database();
        network_db_read.get_mldb_dis_brd_info();
        network_db_read.get_pdb_dis_brd_info();
        network_db_read.get_agdb_dis_brd_info();
        network_db_read.get_cgdb_dis_brd_info();
        network_db_read.get_mdch_port_info();
        network_db_read.get_pdch_port_info();
      }
      this.GroupBox1.Enabled = true;
      this.lblCcuAddress.Enabled = true;
      this.lblCcuName.Enabled = true;
      this.lblMdchAddress.Enabled = true;
      this.lblMdchName.Enabled = true;
      this.txtCcuAddress.Enabled = true;
      this.txtCcuName.Enabled = true;
      this.txtMdchAddress.Enabled = true;
      this.txtMdchName.Enabled = true;
      this.btnNew.Enabled = false;
      this.btnDelete.Enabled = false;
      this.btnSave.Enabled = true;
      this.txtMdchName.Text = frmMainFormIPIS.mdch_db.mdch_name;
      this.txtMdchAddress.Text = frmMainFormIPIS.mdch_db.mdch_addr != (byte) 0 ? Conversions.ToString(frmMainFormIPIS.mdch_db.mdch_addr) : " ";
      this.txtCcuName.Text = frmMainFormIPIS.ccu_name;
      this.txtCcuAddress.Text = frmMainFormIPIS.ccu_addr != (byte) 0 ? Conversions.ToString(frmMainFormIPIS.ccu_addr) : " ";
      int i1 = 0;
      cmb_system_type[0] = this.cmbP1S1;
      cmb_system_type[1] = this.cmbP1S2;
      cmb_system_type[2] = this.cmbP1S3;
      cmb_system_type[3] = this.cmbP1S4;
      this.mdch_chkport_check(this.chkPort1, i1, cmb_system_type);
      int i2 = checked (i1 + 1);
      cmb_system_type[0] = this.cmbP2S1;
      cmb_system_type[1] = this.cmbP2S2;
      cmb_system_type[2] = this.cmbP2S3;
      cmb_system_type[3] = this.cmbP2S4;
      this.mdch_chkport_check(this.chkPort2, i2, cmb_system_type);
      int i3 = checked (i2 + 1);
      cmb_system_type[0] = this.cmbP3S1;
      cmb_system_type[1] = this.cmbP3S2;
      cmb_system_type[2] = this.cmbP3S3;
      cmb_system_type[3] = this.cmbP3S4;
      this.mdch_chkport_check(this.chkPort3, i3, cmb_system_type);
      int i4 = checked (i3 + 1);
      cmb_system_type[0] = this.cmbP4S1;
      cmb_system_type[1] = this.cmbP4S2;
      cmb_system_type[2] = this.cmbP4S3;
      cmb_system_type[3] = this.cmbP4S4;
      this.mdch_chkport_check(this.chkPort4, i4, cmb_system_type);
      int i5 = checked (i4 + 1);
      cmb_system_type[0] = this.cmbP5S1;
      cmb_system_type[1] = this.cmbP5S2;
      cmb_system_type[2] = this.cmbP5S3;
      cmb_system_type[3] = this.cmbP5S4;
      this.mdch_chkport_check(this.chkPort5, i5, cmb_system_type);
      int i6 = checked (i5 + 1);
      cmb_system_type[0] = this.cmbP6S1;
      cmb_system_type[1] = this.cmbP6S2;
      cmb_system_type[2] = this.cmbP6S3;
      cmb_system_type[3] = this.cmbP6S4;
      this.mdch_chkport_check(this.chkPort6, i6, cmb_system_type);
      int i7 = checked (i6 + 1);
      cmb_system_type[0] = this.cmbP7S1;
      cmb_system_type[1] = this.cmbP7S2;
      cmb_system_type[2] = this.cmbP7S3;
      cmb_system_type[3] = this.cmbP7S4;
      this.mdch_chkport_check(this.chkPort7, i7, cmb_system_type);
      int i8 = checked (i7 + 1);
      cmb_system_type[0] = this.cmbP8S1;
      cmb_system_type[1] = this.cmbP8S2;
      cmb_system_type[2] = this.cmbP8S3;
      cmb_system_type[3] = this.cmbP8S4;
      this.mdch_chkport_check(this.chkPort8, i8, cmb_system_type);
      int i9 = checked (i8 + 1);
      cmb_system_type[0] = this.cmbP9S1;
      cmb_system_type[1] = this.cmbP9S2;
      cmb_system_type[2] = this.cmbP9S3;
      cmb_system_type[3] = this.cmbP9S4;
      this.mdch_chkport_check(this.chkPort9, i9, cmb_system_type);
      int i10 = checked (i9 + 1);
      cmb_system_type[0] = this.cmbP10S1;
      cmb_system_type[1] = this.cmbP10S2;
      cmb_system_type[2] = this.cmbP10S3;
      cmb_system_type[3] = this.cmbP10S4;
      this.mdch_chkport_check(this.chkPort10, i10, cmb_system_type);
      int i11 = checked (i10 + 1);
      cmb_system_type[0] = this.cmbP11S1;
      cmb_system_type[1] = this.cmbP11S2;
      cmb_system_type[2] = this.cmbP11S3;
      cmb_system_type[3] = this.cmbP11S4;
      this.mdch_chkport_check(this.chkPort11, i11, cmb_system_type);
      int i12 = checked (i11 + 1);
      cmb_system_type[0] = this.cmbP12S1;
      cmb_system_type[1] = this.cmbP12S2;
      cmb_system_type[2] = this.cmbP12S3;
      cmb_system_type[3] = this.cmbP12S4;
      this.mdch_chkport_check(this.chkPort12, i12, cmb_system_type);
      int i13 = checked (i12 + 1);
      cmb_system_type[0] = this.cmbP13S1;
      cmb_system_type[1] = this.cmbP13S2;
      cmb_system_type[2] = this.cmbP13S3;
      cmb_system_type[3] = this.cmbP13S4;
      this.mdch_chkport_check(this.chkPort13, i13, cmb_system_type);
      int i14 = checked (i13 + 1);
      cmb_system_type[0] = this.cmbP14S1;
      cmb_system_type[1] = this.cmbP14S2;
      cmb_system_type[2] = this.cmbP14S3;
      cmb_system_type[3] = this.cmbP14S4;
      this.mdch_chkport_check(this.chkPort14, i14, cmb_system_type);
      int i15 = checked (i14 + 1);
      cmb_system_type[0] = this.cmbP15S1;
      cmb_system_type[1] = this.cmbP15S2;
      cmb_system_type[2] = this.cmbP15S3;
      cmb_system_type[3] = this.cmbP15S4;
      this.mdch_chkport_check(this.chkPort15, i15, cmb_system_type);
      int i16 = checked (i15 + 1);
      cmb_system_type[0] = this.cmbP16S1;
      cmb_system_type[1] = this.cmbP16S2;
      cmb_system_type[2] = this.cmbP16S3;
      cmb_system_type[3] = this.cmbP16S4;
      this.mdch_chkport_check(this.chkPort16, i16, cmb_system_type);
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void btnSave_Click(object sender, EventArgs e)
  {
    try
    {
      if (basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Save Network Configuration(Y/N)", "Msg Box", 4, 0, 0) == (short) 7)
      {
        lock (this)
        {
          network_db_read.get_nw_database();
          network_db_read.get_mldb_dis_brd_info();
          network_db_read.get_pdb_dis_brd_info();
          network_db_read.get_agdb_dis_brd_info();
          network_db_read.get_cgdb_dis_brd_info();
          network_db_read.get_mdch_port_info();
          network_db_read.get_pdch_port_info();
          return;
        }
      }
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(this.txtCcuName.Text, "", false) == 0)
      {
        int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Please Enter the CCU Name", "Msg Box", 0, 0, 0);
        return;
      }
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(this.txtCcuAddress.Text, "", false) == 0)
      {
        int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Please Enter the CCU Address", "Msg Box", 0, 0, 0);
        return;
      }
      if (Conversions.ToDouble(this.txtCcuAddress.Text) > 254.0 & Conversions.ToDouble(this.txtCcuAddress.Text) < 253.0)
      {
        int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Please Enter the CCU Address either 253 or 254", "Msg Box", 0, 0, 0);
        return;
      }
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(this.txtMdchName.Text, "", false) == 0)
      {
        int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Please Enter the MDCH Name", "Msg Box", 0, 0, 0);
        return;
      }
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(this.txtMdchAddress.Text, "", false) == 0)
      {
        int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Please Enter the MDCH Address", "Msg Box", 0, 0, 0);
        return;
      }
      if (Conversions.ToDouble(this.txtMdchAddress.Text) > 239.0 & Conversions.ToDouble(this.txtMdchAddress.Text) < 1.0)
      {
        int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Please Enter the MDCH Address in the range of 1 to 239", "Msg Box", 0, 0, 0);
        return;
      }
      frmMainFormIPIS.ccu_name = this.txtCcuName.Text;
      frmMainFormIPIS.ccu_addr = Conversions.ToByte(this.txtCcuAddress.Text);
      frmMainFormIPIS.mdch_db.mdch_name = this.txtMdchName.Text;
      frmMainFormIPIS.mdch_db.mdch_addr = Conversions.ToByte(this.txtMdchAddress.Text);
      this.mdch_update_no_systems();
      lock (this)
        network_db_read.set_nw_database();
      Log_file.Log(string.Format("Network Configuration Saved"));
      int num1 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Network Configuration Saved", "Msg Box", 0, 0, 0);
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    this.GroupBox1.Enabled = false;
    this.lblCcuAddress.Enabled = false;
    this.lblCcuName.Enabled = false;
    this.lblMdchAddress.Enabled = false;
    this.lblMdchName.Enabled = false;
    this.txtCcuAddress.Enabled = false;
    this.txtCcuName.Enabled = false;
    this.txtMdchAddress.Enabled = false;
    this.txtMdchName.Enabled = false;
    this.btnEdit.Enabled = true;
    this.btnDelete.Enabled = true;
    this.btnSave.Enabled = false;
    this.BringToFront();
    Strings.Format((object) DateTime.Now.Date, "dd-MM-yyyy");
    try
    {
      string str = "Z:\\Database\\network.mdb";
      string sourceFileName = "C:\\IPIS\\Database\\network.mdb";
      if (!File.Exists(str))
        File.Create(str);
      bool overwrite = true;
      MyProject.Computer.FileSystem.CopyFile(sourceFileName, str, overwrite);
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
  }

  private void btnDelete_Click(object sender, EventArgs e)
  {
    OleDbCommand oleDbCommand = new OleDbCommand();
    int index = 0;
    while (index < frmMainFormIPIS.online_train_cnt)
    {
      if (frmMainFormIPIS.online_train_data[index].display_checked)
      {
        int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "First Uncheck the TADDB in all rows", "Msg Box", 0, 0, 0);
        return;
      }
      checked { ++index; }
    }
    try
    {
      if (basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "COnform Delete (Y/N) ", "Msg Box", 4, 0, 0) == (short) 6)
      {
        if (connection_Database.con2.State == ConnectionState.Closed)
          connection_Database.con2.Open();
        this.btnEdit.Enabled = false;
        this.btnNew.Enabled = false;
        this.btnSave.Enabled = false;
        new OleDbCommand("delete * from tbl_CCUMDCH", connection_Database.con2).ExecuteNonQuery();
        new OleDbCommand("delete * from tbl_MDCH", connection_Database.con2).ExecuteNonQuery();
        new OleDbCommand("delete * from tbl_PDCH", connection_Database.con2).ExecuteNonQuery();
        if (connection_Database.con2.State == ConnectionState.Open)
          connection_Database.con2.Close();
        Log_file.Log(string.Format("Deleted Network Configuration"));
        int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Deleted Network Configuration", "Msg Box", 0, 0, 0);
        this.GroupBox1.Enabled = false;
        this.lblCcuAddress.Enabled = false;
        this.lblCcuName.Enabled = false;
        this.lblMdchAddress.Enabled = false;
        this.lblMdchName.Enabled = false;
        this.txtCcuAddress.Enabled = false;
        this.txtCcuName.Enabled = false;
        this.txtMdchAddress.Enabled = false;
        this.txtMdchName.Enabled = false;
        frmMainFormIPIS.ccu_name = string.Empty;
        frmMainFormIPIS.ccu_addr = (byte) 0;
        frmMainFormIPIS.mdch_db_struc_init();
        // TODO: Implement missing methods in taddb_msg class
        // taddb_msg.mldb_dis_brd_init();
        // taddb_msg.pdb_dis_brd_init();
        // taddb_msg.agdb_dis_brd_init();
        cgdb_dis.cgdb_dis_brd_init();
        frmMainFormIPIS.hub_init();
        lock (this)
        {
          network_db_read.get_nw_database();
          network_db_read.get_mldb_dis_brd_info();
          network_db_read.get_pdb_dis_brd_info();
          network_db_read.get_agdb_dis_brd_info();
          network_db_read.get_cgdb_dis_brd_info();
          network_db_read.get_mdch_port_info();
          network_db_read.get_pdch_port_info();
        }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    this.btnEdit.Enabled = true;
    this.btnDelete.Enabled = true;
    this.btnNew.Enabled = true;
    this.btnSave.Enabled = false;
  }

  private void btnAdd_Click(object sender, EventArgs e)
  {
    OleDbCommand oleDbCommand = new OleDbCommand();
    int index = 0;
    while (index < frmMainFormIPIS.online_train_cnt)
    {
      if (frmMainFormIPIS.online_train_data[index].display_checked)
      {
        int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "First Uncheck the TADDB in all rows", "Msg Box", 0, 0, 0);
        return;
      }
      checked { ++index; }
    }
    int num1 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Please deleted existing database first\r\nIf already deleted procced with new database by pressing Yes", "Msg Box", 0, 0, 0);
    if (basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Confirm DELETE (Y/N) ", "Msg Box", 4, 0, 0) == (short) 7)
    {
      this.GroupBox1.Enabled = false;
      this.lblCcuAddress.Enabled = false;
      this.lblCcuName.Enabled = false;
      this.lblMdchAddress.Enabled = false;
      this.lblMdchName.Enabled = false;
      this.txtCcuAddress.Enabled = false;
      this.txtCcuName.Enabled = false;
      this.txtMdchAddress.Enabled = false;
      this.txtMdchName.Enabled = false;
      this.btnEdit.Enabled = true;
      this.btnDelete.Enabled = true;
      this.btnSave.Enabled = false;
    }
    else
    {
      this.GroupBox1.Enabled = true;
      this.lblCcuAddress.Enabled = true;
      this.lblCcuName.Enabled = true;
      this.lblMdchAddress.Enabled = true;
      this.lblMdchName.Enabled = true;
      this.txtCcuAddress.Enabled = true;
      this.txtCcuName.Enabled = true;
      this.txtMdchAddress.Enabled = true;
      this.txtMdchName.Enabled = true;
      this.btnEdit.Enabled = false;
      this.btnDelete.Enabled = false;
      this.btnSave.Enabled = true;
      frmMainFormIPIS.mdch_db_struc_init();
      frmMainFormIPIS.hub_init();
      cgdb_dis.cgdb_dis_brd_init();
      // TODO: Implement missing methods in taddb_msg class
      // taddb_msg.pdb_dis_brd_init();
      // taddb_msg.agdb_dis_brd_init();
      // taddb_msg.mldb_dis_brd_init();
    }
  }

  private void mdch_count_no_of_systems(CheckBox chkport, byte port_no, ComboBox[] cmbps)
  {
    byte index = 0;
    try
    {
      if (!chkport.Checked)
        return;
      frmMainFormIPIS.mdch_db.mdch_port[(int) port_no].no_of_systems = (byte) 0;
      while (index < (byte) 4)
      {
        if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(cmbps[(int) index].Text, "PDCH", false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(cmbps[(int) index].Text, "AGDB", false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(cmbps[(int) index].Text, "MLDB", false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(cmbps[(int) index].Text, "PDB", false) == 0)
          checked { ++frmMainFormIPIS.mdch_db.mdch_port[(int) port_no].no_of_systems; }
        checked { ++index; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void mdch_update_no_systems()
  {
    byte port_no1 = 0;
    ComboBox[] cmbps = new ComboBox[4];
    try
    {
      cmbps[0] = this.cmbP1S1;
      cmbps[1] = this.cmbP1S2;
      cmbps[2] = this.cmbP1S3;
      cmbps[3] = this.cmbP1S4;
      this.mdch_count_no_of_systems(this.chkPort1, port_no1, cmbps);
      byte port_no2 = checked ((byte) ((int) port_no1 + 1));
      cmbps[0] = this.cmbP2S1;
      cmbps[1] = this.cmbP2S2;
      cmbps[2] = this.cmbP2S3;
      cmbps[3] = this.cmbP2S4;
      this.mdch_count_no_of_systems(this.chkPort2, port_no2, cmbps);
      byte port_no3 = checked ((byte) ((int) port_no2 + 1));
      cmbps[0] = this.cmbP3S1;
      cmbps[1] = this.cmbP3S2;
      cmbps[2] = this.cmbP3S3;
      cmbps[3] = this.cmbP3S4;
      this.mdch_count_no_of_systems(this.chkPort3, port_no3, cmbps);
      byte port_no4 = checked ((byte) ((int) port_no3 + 1));
      cmbps[0] = this.cmbP4S1;
      cmbps[1] = this.cmbP4S2;
      cmbps[2] = this.cmbP4S3;
      cmbps[3] = this.cmbP4S4;
      this.mdch_count_no_of_systems(this.chkPort4, port_no4, cmbps);
      byte port_no5 = checked ((byte) ((int) port_no4 + 1));
      cmbps[0] = this.cmbP5S1;
      cmbps[1] = this.cmbP5S2;
      cmbps[2] = this.cmbP5S3;
      cmbps[3] = this.cmbP5S4;
      this.mdch_count_no_of_systems(this.chkPort5, port_no5, cmbps);
      byte port_no6 = checked ((byte) ((int) port_no5 + 1));
      cmbps[0] = this.cmbP6S1;
      cmbps[1] = this.cmbP6S2;
      cmbps[2] = this.cmbP6S3;
      cmbps[3] = this.cmbP6S4;
      this.mdch_count_no_of_systems(this.chkPort6, port_no6, cmbps);
      byte port_no7 = checked ((byte) ((int) port_no6 + 1));
      cmbps[0] = this.cmbP7S1;
      cmbps[1] = this.cmbP7S2;
      cmbps[2] = this.cmbP7S3;
      cmbps[3] = this.cmbP7S4;
      this.mdch_count_no_of_systems(this.chkPort7, port_no7, cmbps);
      byte port_no8 = checked ((byte) ((int) port_no7 + 1));
      cmbps[0] = this.cmbP8S1;
      cmbps[1] = this.cmbP8S2;
      cmbps[2] = this.cmbP8S3;
      cmbps[3] = this.cmbP8S4;
      this.mdch_count_no_of_systems(this.chkPort8, port_no8, cmbps);
      byte port_no9 = checked ((byte) ((int) port_no8 + 1));
      cmbps[0] = this.cmbP9S1;
      cmbps[1] = this.cmbP9S2;
      cmbps[2] = this.cmbP9S3;
      cmbps[3] = this.cmbP9S4;
      this.mdch_count_no_of_systems(this.chkPort9, port_no9, cmbps);
      byte port_no10 = checked ((byte) ((int) port_no9 + 1));
      cmbps[0] = this.cmbP10S1;
      cmbps[1] = this.cmbP10S2;
      cmbps[2] = this.cmbP10S3;
      cmbps[3] = this.cmbP10S4;
      this.mdch_count_no_of_systems(this.chkPort10, port_no10, cmbps);
      byte port_no11 = checked ((byte) ((int) port_no10 + 1));
      cmbps[0] = this.cmbP11S1;
      cmbps[1] = this.cmbP11S2;
      cmbps[2] = this.cmbP11S3;
      cmbps[3] = this.cmbP11S4;
      this.mdch_count_no_of_systems(this.chkPort11, port_no11, cmbps);
      byte port_no12 = checked ((byte) ((int) port_no11 + 1));
      cmbps[0] = this.cmbP12S1;
      cmbps[1] = this.cmbP12S2;
      cmbps[2] = this.cmbP12S3;
      cmbps[3] = this.cmbP12S4;
      this.mdch_count_no_of_systems(this.chkPort12, port_no12, cmbps);
      byte port_no13 = checked ((byte) ((int) port_no12 + 1));
      cmbps[0] = this.cmbP13S1;
      cmbps[1] = this.cmbP13S2;
      cmbps[2] = this.cmbP13S3;
      cmbps[3] = this.cmbP13S4;
      this.mdch_count_no_of_systems(this.chkPort13, port_no13, cmbps);
      byte port_no14 = checked ((byte) ((int) port_no13 + 1));
      cmbps[0] = this.cmbP14S1;
      cmbps[1] = this.cmbP14S2;
      cmbps[2] = this.cmbP14S3;
      cmbps[3] = this.cmbP14S4;
      this.mdch_count_no_of_systems(this.chkPort14, port_no14, cmbps);
      byte port_no15 = checked ((byte) ((int) port_no14 + 1));
      cmbps[0] = this.cmbP15S1;
      cmbps[1] = this.cmbP15S2;
      cmbps[2] = this.cmbP15S3;
      cmbps[3] = this.cmbP15S4;
      this.mdch_count_no_of_systems(this.chkPort15, port_no15, cmbps);
      byte port_no16 = checked ((byte) ((int) port_no15 + 1));
      cmbps[0] = this.cmbP16S1;
      cmbps[1] = this.cmbP16S2;
      cmbps[2] = this.cmbP16S3;
      cmbps[3] = this.cmbP16S4;
      this.mdch_count_no_of_systems(this.chkPort16, port_no16, cmbps);
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void mdch_chkport_check(CheckBox chkport, int i, ComboBox[] cmb_system_type)
  {
    try
    {
      if (frmMainFormIPIS.mdch_db.port_status[i])
      {
        chkport.Checked = true;
        int index = 0;
        while (index < 4)
        {
          if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.mdch_db.mdch_port[i].system_type[index], "PDCH", false) == 0)
            cmb_system_type[index].Text = "PDCH";
          else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.mdch_db.mdch_port[i].system_type[index], "AGDB", false) == 0)
            cmb_system_type[index].Text = "AGDB";
          else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.mdch_db.mdch_port[i].system_type[index], "MLDB", false) == 0)
            cmb_system_type[index].Text = "MLDB";
          else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.mdch_db.mdch_port[i].system_type[index], "PDB", false) == 0)
            cmb_system_type[index].Text = "PDB";
          checked { ++index; }
        }
      }
      else
        chkport.Checked = false;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void chkPort1_CheckedChanged(object sender, EventArgs e)
  {
    if (this.chkPort1.Checked)
    {
      frmNetworkMDCH.mdch_port_num = (byte) 0;
      frmMainFormIPIS.mdch_db.port_status[(int) frmNetworkMDCH.mdch_port_num] = true;
      this.cmbP1S1.Visible = true;
      this.cmbP1S2.Visible = true;
      this.cmbP1S3.Visible = true;
      this.cmbP1S4.Visible = true;
      this.btnP1S1.Visible = true;
      this.btnP1S2.Visible = true;
      this.btnP1S3.Visible = true;
      this.btnP1S4.Visible = true;
    }
    else
    {
      if (this.chkPort1.Checked)
        return;
      frmMainFormIPIS.mdch_db.port_status[0] = false;
      this.cmbP1S1.Visible = false;
      this.cmbP1S2.Visible = false;
      this.cmbP1S3.Visible = false;
      this.cmbP1S4.Visible = false;
      this.btnP1S1.Visible = false;
      this.btnP1S2.Visible = false;
      this.btnP1S3.Visible = false;
      this.btnP1S4.Visible = false;
    }
  }

  private void chkPort2_CheckedChanged(object sender, EventArgs e)
  {
    if (this.chkPort2.Checked)
    {
      frmMainFormIPIS.mdch_db.port_status[1] = true;
      frmNetworkMDCH.mdch_port_num = (byte) 1;
      frmMainFormIPIS.mdch_db.port_status[(int) frmNetworkMDCH.mdch_port_num] = true;
      this.cmbP2S1.Visible = true;
      this.cmbP2S2.Visible = true;
      this.cmbP2S3.Visible = true;
      this.cmbP2S4.Visible = true;
      this.btnP2S1.Visible = true;
      this.btnP2S2.Visible = true;
      this.btnP2S3.Visible = true;
      this.btnP2S4.Visible = true;
    }
    else
    {
      if (this.chkPort2.Checked)
        return;
      frmMainFormIPIS.mdch_db.port_status[1] = false;
      this.cmbP2S1.Visible = false;
      this.cmbP2S2.Visible = false;
      this.cmbP2S3.Visible = false;
      this.cmbP2S4.Visible = false;
      this.btnP2S1.Visible = false;
      this.btnP2S2.Visible = false;
      this.btnP2S3.Visible = false;
      this.btnP2S4.Visible = false;
    }
  }

  private void chkPort3_CheckedChanged(object sender, EventArgs e)
  {
    if (this.chkPort3.Checked)
    {
      frmNetworkMDCH.mdch_port_num = (byte) 2;
      frmMainFormIPIS.mdch_db.port_status[(int) frmNetworkMDCH.mdch_port_num] = true;
      this.cmbP3S1.Visible = true;
      this.cmbP3S2.Visible = true;
      this.cmbP3S3.Visible = true;
      this.cmbP3S4.Visible = true;
      this.btnP3S1.Visible = true;
      this.btnP3S2.Visible = true;
      this.btnP3S3.Visible = true;
      this.btnP3S4.Visible = true;
    }
    else
    {
      if (this.chkPort3.Checked)
        return;
      frmMainFormIPIS.mdch_db.port_status[2] = false;
      this.cmbP3S1.Visible = false;
      this.cmbP3S2.Visible = false;
      this.cmbP3S3.Visible = false;
      this.cmbP3S4.Visible = false;
      this.btnP3S1.Visible = false;
      this.btnP3S2.Visible = false;
      this.btnP3S3.Visible = false;
      this.btnP3S4.Visible = false;
    }
  }

  private void chkPort4_CheckedChanged(object sender, EventArgs e)
  {
    if (this.chkPort4.Checked)
    {
      frmMainFormIPIS.mdch_db.port_status[3] = true;
      this.cmbP4S1.Visible = true;
      this.cmbP4S2.Visible = true;
      this.cmbP4S3.Visible = true;
      this.cmbP4S4.Visible = true;
      this.btnP4S1.Visible = true;
      this.btnP4S2.Visible = true;
      this.btnP4S3.Visible = true;
      this.btnP4S4.Visible = true;
    }
    else
    {
      if (this.chkPort4.Checked)
        return;
      frmMainFormIPIS.mdch_db.port_status[3] = false;
      this.cmbP4S1.Visible = false;
      this.cmbP4S2.Visible = false;
      this.cmbP4S3.Visible = false;
      this.cmbP4S4.Visible = false;
      this.btnP4S1.Visible = false;
      this.btnP4S2.Visible = false;
      this.btnP4S3.Visible = false;
      this.btnP4S4.Visible = false;
    }
  }

  private void chkPort5_CheckedChanged(object sender, EventArgs e)
  {
    if (this.chkPort5.Checked)
    {
      frmMainFormIPIS.mdch_db.port_status[4] = true;
      this.cmbP5S1.Visible = true;
      this.cmbP5S2.Visible = true;
      this.cmbP5S3.Visible = true;
      this.cmbP5S4.Visible = true;
      this.btnP5S1.Visible = true;
      this.btnP5S2.Visible = true;
      this.btnP5S3.Visible = true;
      this.btnP5S4.Visible = true;
    }
    else
    {
      if (this.chkPort5.Checked)
        return;
      frmMainFormIPIS.mdch_db.port_status[4] = false;
      this.cmbP5S1.Visible = false;
      this.cmbP5S2.Visible = false;
      this.cmbP5S3.Visible = false;
      this.cmbP5S4.Visible = false;
      this.btnP5S1.Visible = false;
      this.btnP5S2.Visible = false;
      this.btnP5S3.Visible = false;
      this.btnP5S4.Visible = false;
    }
  }

  private void chkPort6_CheckedChanged(object sender, EventArgs e)
  {
    if (this.chkPort6.Checked)
    {
      frmMainFormIPIS.mdch_db.port_status[5] = true;
      this.cmbP6S1.Visible = true;
      this.cmbP6S2.Visible = true;
      this.cmbP6S3.Visible = true;
      this.cmbP6S4.Visible = true;
      this.btnP6S1.Visible = true;
      this.btnP6S2.Visible = true;
      this.btnP6S3.Visible = true;
      this.btnP6S4.Visible = true;
    }
    else
    {
      if (this.chkPort6.Checked)
        return;
      frmMainFormIPIS.mdch_db.port_status[5] = false;
      this.cmbP6S1.Visible = false;
      this.cmbP6S2.Visible = false;
      this.cmbP6S3.Visible = false;
      this.cmbP6S4.Visible = false;
      this.btnP6S1.Visible = false;
      this.btnP6S2.Visible = false;
      this.btnP6S3.Visible = false;
      this.btnP6S4.Visible = false;
    }
  }

  private void chkPort7_CheckedChanged(object sender, EventArgs e)
  {
    if (this.chkPort7.Checked)
    {
      frmMainFormIPIS.mdch_db.port_status[6] = true;
      this.cmbP7S1.Visible = true;
      this.cmbP7S2.Visible = true;
      this.cmbP7S3.Visible = true;
      this.cmbP7S4.Visible = true;
      this.btnP7S1.Visible = true;
      this.btnP7S2.Visible = true;
      this.btnP7S3.Visible = true;
      this.btnP7S4.Visible = true;
    }
    else
    {
      if (this.chkPort7.Checked)
        return;
      frmMainFormIPIS.mdch_db.port_status[6] = false;
      this.cmbP7S1.Visible = false;
      this.cmbP7S2.Visible = false;
      this.cmbP7S3.Visible = false;
      this.cmbP7S4.Visible = false;
      this.btnP7S1.Visible = false;
      this.btnP7S2.Visible = false;
      this.btnP7S3.Visible = false;
      this.btnP7S4.Visible = false;
    }
  }

  private void chkPort8_CheckedChanged(object sender, EventArgs e)
  {
    if (this.chkPort8.Checked)
    {
      frmMainFormIPIS.mdch_db.port_status[7] = true;
      this.cmbP8S1.Visible = true;
      this.cmbP8S2.Visible = true;
      this.cmbP8S3.Visible = true;
      this.cmbP8S4.Visible = true;
      this.btnP8S1.Visible = true;
      this.btnP8S2.Visible = true;
      this.btnP8S3.Visible = true;
      this.btnP8S4.Visible = true;
    }
    else
    {
      if (this.chkPort8.Checked)
        return;
      frmMainFormIPIS.mdch_db.port_status[7] = false;
      this.cmbP8S1.Visible = false;
      this.cmbP8S2.Visible = false;
      this.cmbP8S3.Visible = false;
      this.cmbP8S4.Visible = false;
      this.btnP8S1.Visible = false;
      this.btnP8S2.Visible = false;
      this.btnP8S3.Visible = false;
      this.btnP8S4.Visible = false;
    }
  }

  private void chkPort9_CheckedChanged(object sender, EventArgs e)
  {
    if (this.chkPort9.Checked)
    {
      frmMainFormIPIS.mdch_db.port_status[8] = true;
      this.cmbP9S1.Visible = true;
      this.cmbP9S2.Visible = true;
      this.cmbP9S3.Visible = true;
      this.cmbP9S4.Visible = true;
      this.btnP9S1.Visible = true;
      this.btnP9S2.Visible = true;
      this.btnP9S3.Visible = true;
      this.btnP9S4.Visible = true;
    }
    else
    {
      if (this.chkPort9.Checked)
        return;
      frmMainFormIPIS.mdch_db.port_status[8] = false;
      this.cmbP9S1.Visible = false;
      this.cmbP9S2.Visible = false;
      this.cmbP9S3.Visible = false;
      this.cmbP9S4.Visible = false;
      this.btnP9S1.Visible = false;
      this.btnP9S2.Visible = false;
      this.btnP9S3.Visible = false;
      this.btnP9S4.Visible = false;
    }
  }

  private void chkPort10_CheckedChanged(object sender, EventArgs e)
  {
    if (this.chkPort10.Checked)
    {
      frmMainFormIPIS.mdch_db.port_status[9] = true;
      this.cmbP10S1.Visible = true;
      this.cmbP10S2.Visible = true;
      this.cmbP10S3.Visible = true;
      this.cmbP10S4.Visible = true;
      this.btnP10S1.Visible = true;
      this.btnP10S2.Visible = true;
      this.btnP10S3.Visible = true;
      this.btnP10S4.Visible = true;
    }
    else
    {
      if (this.chkPort10.Checked)
        return;
      frmMainFormIPIS.mdch_db.port_status[9] = false;
      this.cmbP10S1.Visible = false;
      this.cmbP10S2.Visible = false;
      this.cmbP10S3.Visible = false;
      this.cmbP10S4.Visible = false;
      this.btnP10S1.Visible = false;
      this.btnP10S2.Visible = false;
      this.btnP10S3.Visible = false;
      this.btnP10S4.Visible = false;
    }
  }

  private void chkPort11_CheckedChanged(object sender, EventArgs e)
  {
    if (this.chkPort11.Checked)
    {
      frmMainFormIPIS.mdch_db.port_status[10] = true;
      this.cmbP11S1.Visible = true;
      this.cmbP11S2.Visible = true;
      this.cmbP11S3.Visible = true;
      this.cmbP11S4.Visible = true;
      this.btnP11S1.Visible = true;
      this.btnP11S2.Visible = true;
      this.btnP11S3.Visible = true;
      this.btnP11S4.Visible = true;
    }
    else
    {
      if (this.chkPort11.Checked)
        return;
      frmMainFormIPIS.mdch_db.port_status[10] = false;
      this.cmbP11S1.Visible = false;
      this.cmbP11S2.Visible = false;
      this.cmbP11S3.Visible = false;
      this.cmbP11S4.Visible = false;
      this.btnP11S1.Visible = false;
      this.btnP11S2.Visible = false;
      this.btnP11S3.Visible = false;
      this.btnP11S4.Visible = false;
    }
  }

  private void chkPort12_CheckedChanged(object sender, EventArgs e)
  {
    if (this.chkPort12.Checked)
    {
      frmMainFormIPIS.mdch_db.port_status[11] = true;
      this.cmbP12S1.Visible = true;
      this.cmbP12S2.Visible = true;
      this.cmbP12S3.Visible = true;
      this.cmbP12S4.Visible = true;
      this.btnP12S1.Visible = true;
      this.btnP12S2.Visible = true;
      this.btnP12S3.Visible = true;
      this.btnP12S4.Visible = true;
    }
    else
    {
      if (this.chkPort12.Checked)
        return;
      frmMainFormIPIS.mdch_db.port_status[11] = false;
      this.cmbP12S1.Visible = false;
      this.cmbP12S2.Visible = false;
      this.cmbP12S3.Visible = false;
      this.cmbP12S4.Visible = false;
      this.btnP12S1.Visible = false;
      this.btnP12S2.Visible = false;
      this.btnP12S3.Visible = false;
      this.btnP12S4.Visible = false;
    }
  }

  private void chkPort13_CheckedChanged(object sender, EventArgs e)
  {
    if (this.chkPort13.Checked)
    {
      frmMainFormIPIS.mdch_db.port_status[12] = true;
      this.cmbP13S1.Visible = true;
      this.cmbP13S2.Visible = true;
      this.cmbP13S3.Visible = true;
      this.cmbP13S4.Visible = true;
      this.btnP13S1.Visible = true;
      this.btnP13S2.Visible = true;
      this.btnP13S3.Visible = true;
      this.btnP13S4.Visible = true;
    }
    else
    {
      if (this.chkPort13.Checked)
        return;
      frmMainFormIPIS.mdch_db.port_status[12] = false;
      this.cmbP13S1.Visible = false;
      this.cmbP13S2.Visible = false;
      this.cmbP13S3.Visible = false;
      this.cmbP13S4.Visible = false;
      this.btnP13S1.Visible = false;
      this.btnP13S2.Visible = false;
      this.btnP13S3.Visible = false;
      this.btnP13S4.Visible = false;
    }
  }

  private void chkPort14_CheckedChanged(object sender, EventArgs e)
  {
    if (this.chkPort14.Checked)
    {
      frmMainFormIPIS.mdch_db.port_status[13] = true;
      this.cmbP14S1.Visible = true;
      this.cmbP14S2.Visible = true;
      this.cmbP14S3.Visible = true;
      this.cmbP14S4.Visible = true;
      this.btnP14S1.Visible = true;
      this.btnP14S2.Visible = true;
      this.btnP14S3.Visible = true;
      this.btnP14S4.Visible = true;
    }
    else
    {
      if (this.chkPort14.Checked)
        return;
      frmMainFormIPIS.mdch_db.port_status[13] = false;
      this.cmbP14S1.Visible = false;
      this.cmbP14S2.Visible = false;
      this.cmbP14S3.Visible = false;
      this.cmbP14S4.Visible = false;
      this.btnP14S1.Visible = false;
      this.btnP14S2.Visible = false;
      this.btnP14S3.Visible = false;
      this.btnP14S4.Visible = false;
    }
  }

  private void chkPort15_CheckedChanged(object sender, EventArgs e)
  {
    if (this.chkPort15.Checked)
    {
      frmMainFormIPIS.mdch_db.port_status[14] = true;
      this.cmbP15S1.Visible = true;
      this.cmbP15S2.Visible = true;
      this.cmbP15S3.Visible = true;
      this.cmbP15S4.Visible = true;
      this.btnP15S1.Visible = true;
      this.btnP15S2.Visible = true;
      this.btnP15S3.Visible = true;
      this.btnP15S4.Visible = true;
    }
    else
    {
      if (this.chkPort15.Checked)
        return;
      frmMainFormIPIS.mdch_db.port_status[14] = false;
      this.cmbP15S1.Visible = false;
      this.cmbP15S2.Visible = false;
      this.cmbP15S3.Visible = false;
      this.cmbP15S4.Visible = false;
      this.btnP15S1.Visible = false;
      this.btnP15S2.Visible = false;
      this.btnP15S3.Visible = false;
      this.btnP15S4.Visible = false;
    }
  }

  private void chkPort16_CheckedChanged(object sender, EventArgs e)
  {
    if (this.chkPort16.Checked)
    {
      frmMainFormIPIS.mdch_db.port_status[15] = true;
      this.cmbP16S1.Visible = true;
      this.cmbP16S2.Visible = true;
      this.cmbP16S3.Visible = true;
      this.cmbP16S4.Visible = true;
      this.btnP16S1.Visible = true;
      this.btnP16S2.Visible = true;
      this.btnP16S3.Visible = true;
      this.btnP16S4.Visible = true;
    }
    else
    {
      if (this.chkPort16.Checked)
        return;
      frmMainFormIPIS.mdch_db.port_status[15] = false;
      this.cmbP16S1.Visible = false;
      this.cmbP16S2.Visible = false;
      this.cmbP16S3.Visible = false;
      this.cmbP16S4.Visible = false;
      this.btnP16S1.Visible = false;
      this.btnP16S2.Visible = false;
      this.btnP16S3.Visible = false;
      this.btnP16S4.Visible = false;
    }
  }

  private void pdch_chkport_check(
    CheckBox chkport,
    int i,
    int j,
    int k,
    ComboBox[] cmb_system_type)
  {
    try
    {
      if (frmMainFormIPIS.mdch_db.mdch_port[i].pdch[j].port_status[k])
      {
        chkport.Checked = true;
        int index = 0;
        while (index < 8)
        {
          if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.mdch_db.mdch_port[i].pdch[j].pdch_port[k].dis_board[index].dis_board_type, "AGDB", false) == 0)
            cmb_system_type[index].Text = "AGDB";
          else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.mdch_db.mdch_port[i].pdch[j].pdch_port[k].dis_board[index].dis_board_type, "PDB", false) == 0)
            cmb_system_type[index].Text = "PDB";
          else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.mdch_db.mdch_port[i].pdch[j].pdch_port[k].dis_board[index].dis_board_type, "CGDB", false) == 0)
            cmb_system_type[index].Text = "CGDB";
          else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.mdch_db.mdch_port[i].pdch[j].pdch_port[k].dis_board[index].dis_board_type, "None", false) == 0)
            cmb_system_type[index].Text = "None";
          checked { ++index; }
        }
      }
      else
        chkport.Checked = false;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void pdch_hub_data(int i, int j)
  {
    ComboBox[] cmb_system_type = new ComboBox[8];
    try
    {
      MyProject.Forms.frmNetworkPDCH.txtPdchName.Text = frmMainFormIPIS.mdch_db.mdch_port[i].pdch[j].pdch_name;
      MyProject.Forms.frmNetworkPDCH.txtPdchAddress.Text = Conversions.ToString(frmMainFormIPIS.mdch_db.mdch_port[i].pdch[j].pdch_addr);
      MyProject.Forms.frmNetworkPDCH.cmbPdchPfno.Text = frmMainFormIPIS.mdch_db.mdch_port[i].pdch[j].platform_no;
      if (frmMainFormIPIS.mdch_db.mdch_port[i].pdch[j].shared_platform)
      {
        MyProject.Forms.frmNetworkPDCH.chkPDCHSharedPfNo.Checked = true;
        MyProject.Forms.frmNetworkPDCH.cmbPdchSharedPfno.Text = frmMainFormIPIS.mdch_db.mdch_port[i].pdch[j].shared_platform_no;
      }
      else
      {
        MyProject.Forms.frmNetworkPDCH.chkPDCHSharedPfNo.Checked = false;
        MyProject.Forms.frmNetworkPDCH.cmbPdchSharedPfno.Text = string.Empty;
      }
      int k1 = 0;
      cmb_system_type[0] = MyProject.Forms.frmNetworkPDCH.cmbP1S1;
      cmb_system_type[1] = MyProject.Forms.frmNetworkPDCH.cmbP1S2;
      cmb_system_type[2] = MyProject.Forms.frmNetworkPDCH.cmbP1S3;
      cmb_system_type[3] = MyProject.Forms.frmNetworkPDCH.cmbP1S4;
      cmb_system_type[4] = MyProject.Forms.frmNetworkPDCH.cmbP1S5;
      cmb_system_type[5] = MyProject.Forms.frmNetworkPDCH.cmbP1S6;
      cmb_system_type[6] = MyProject.Forms.frmNetworkPDCH.cmbP1S7;
      cmb_system_type[7] = MyProject.Forms.frmNetworkPDCH.cmbP1S8;
      this.pdch_chkport_check(MyProject.Forms.frmNetworkPDCH.chkPort1, i, j, k1, cmb_system_type);
      int k2 = 1;
      cmb_system_type[0] = MyProject.Forms.frmNetworkPDCH.cmbP2S1;
      cmb_system_type[1] = MyProject.Forms.frmNetworkPDCH.cmbP2S2;
      cmb_system_type[2] = MyProject.Forms.frmNetworkPDCH.cmbP2S3;
      cmb_system_type[3] = MyProject.Forms.frmNetworkPDCH.cmbP2S4;
      cmb_system_type[4] = MyProject.Forms.frmNetworkPDCH.cmbP2S5;
      cmb_system_type[5] = MyProject.Forms.frmNetworkPDCH.cmbP2S6;
      cmb_system_type[6] = MyProject.Forms.frmNetworkPDCH.cmbP2S7;
      cmb_system_type[7] = MyProject.Forms.frmNetworkPDCH.cmbP2S8;
      this.pdch_chkport_check(MyProject.Forms.frmNetworkPDCH.chkPort2, i, j, k2, cmb_system_type);
      int k3 = 2;
      cmb_system_type[0] = MyProject.Forms.frmNetworkPDCH.cmbP3S1;
      cmb_system_type[1] = MyProject.Forms.frmNetworkPDCH.cmbP3S2;
      cmb_system_type[2] = MyProject.Forms.frmNetworkPDCH.cmbP3S3;
      cmb_system_type[3] = MyProject.Forms.frmNetworkPDCH.cmbP3S4;
      cmb_system_type[4] = MyProject.Forms.frmNetworkPDCH.cmbP3S5;
      cmb_system_type[5] = MyProject.Forms.frmNetworkPDCH.cmbP3S6;
      cmb_system_type[6] = MyProject.Forms.frmNetworkPDCH.cmbP3S7;
      cmb_system_type[7] = MyProject.Forms.frmNetworkPDCH.cmbP3S8;
      this.pdch_chkport_check(MyProject.Forms.frmNetworkPDCH.chkPort3, i, j, k3, cmb_system_type);
      int k4 = 3;
      cmb_system_type[0] = MyProject.Forms.frmNetworkPDCH.cmbP4S1;
      cmb_system_type[1] = MyProject.Forms.frmNetworkPDCH.cmbP4S2;
      cmb_system_type[2] = MyProject.Forms.frmNetworkPDCH.cmbP4S3;
      cmb_system_type[3] = MyProject.Forms.frmNetworkPDCH.cmbP4S4;
      cmb_system_type[4] = MyProject.Forms.frmNetworkPDCH.cmbP4S5;
      cmb_system_type[5] = MyProject.Forms.frmNetworkPDCH.cmbP4S6;
      cmb_system_type[6] = MyProject.Forms.frmNetworkPDCH.cmbP4S7;
      cmb_system_type[7] = MyProject.Forms.frmNetworkPDCH.cmbP4S8;
      this.pdch_chkport_check(MyProject.Forms.frmNetworkPDCH.chkPort4, i, j, k4, cmb_system_type);
      int k5 = 4;
      cmb_system_type[0] = MyProject.Forms.frmNetworkPDCH.cmbP5S1;
      cmb_system_type[1] = MyProject.Forms.frmNetworkPDCH.cmbP5S2;
      cmb_system_type[2] = MyProject.Forms.frmNetworkPDCH.cmbP5S3;
      cmb_system_type[3] = MyProject.Forms.frmNetworkPDCH.cmbP5S4;
      cmb_system_type[4] = MyProject.Forms.frmNetworkPDCH.cmbP5S5;
      cmb_system_type[5] = MyProject.Forms.frmNetworkPDCH.cmbP5S6;
      cmb_system_type[6] = MyProject.Forms.frmNetworkPDCH.cmbP5S7;
      cmb_system_type[7] = MyProject.Forms.frmNetworkPDCH.cmbP5S8;
      this.pdch_chkport_check(MyProject.Forms.frmNetworkPDCH.chkPort5, i, j, k5, cmb_system_type);
      int k6 = 5;
      cmb_system_type[0] = MyProject.Forms.frmNetworkPDCH.cmbP6S1;
      cmb_system_type[1] = MyProject.Forms.frmNetworkPDCH.cmbP6S2;
      cmb_system_type[2] = MyProject.Forms.frmNetworkPDCH.cmbP6S3;
      cmb_system_type[3] = MyProject.Forms.frmNetworkPDCH.cmbP6S4;
      cmb_system_type[4] = MyProject.Forms.frmNetworkPDCH.cmbP6S5;
      cmb_system_type[5] = MyProject.Forms.frmNetworkPDCH.cmbP6S6;
      cmb_system_type[6] = MyProject.Forms.frmNetworkPDCH.cmbP6S7;
      cmb_system_type[7] = MyProject.Forms.frmNetworkPDCH.cmbP6S8;
      this.pdch_chkport_check(MyProject.Forms.frmNetworkPDCH.chkPort6, i, j, k6, cmb_system_type);
      int k7 = 6;
      cmb_system_type[0] = MyProject.Forms.frmNetworkPDCH.cmbP7S1;
      cmb_system_type[1] = MyProject.Forms.frmNetworkPDCH.cmbP7S2;
      cmb_system_type[2] = MyProject.Forms.frmNetworkPDCH.cmbP7S3;
      cmb_system_type[3] = MyProject.Forms.frmNetworkPDCH.cmbP7S4;
      cmb_system_type[4] = MyProject.Forms.frmNetworkPDCH.cmbP7S5;
      cmb_system_type[5] = MyProject.Forms.frmNetworkPDCH.cmbP7S6;
      cmb_system_type[6] = MyProject.Forms.frmNetworkPDCH.cmbP7S7;
      cmb_system_type[7] = MyProject.Forms.frmNetworkPDCH.cmbP7S8;
      this.pdch_chkport_check(MyProject.Forms.frmNetworkPDCH.chkPort7, i, j, k7, cmb_system_type);
      int k8 = 7;
      cmb_system_type[0] = MyProject.Forms.frmNetworkPDCH.cmbP8S1;
      cmb_system_type[1] = MyProject.Forms.frmNetworkPDCH.cmbP8S2;
      cmb_system_type[2] = MyProject.Forms.frmNetworkPDCH.cmbP8S3;
      cmb_system_type[3] = MyProject.Forms.frmNetworkPDCH.cmbP8S4;
      cmb_system_type[4] = MyProject.Forms.frmNetworkPDCH.cmbP8S5;
      cmb_system_type[5] = MyProject.Forms.frmNetworkPDCH.cmbP8S6;
      cmb_system_type[6] = MyProject.Forms.frmNetworkPDCH.cmbP8S7;
      cmb_system_type[7] = MyProject.Forms.frmNetworkPDCH.cmbP8S8;
      this.pdch_chkport_check(MyProject.Forms.frmNetworkPDCH.chkPort8, i, j, k8, cmb_system_type);
      int k9 = 8;
      cmb_system_type[0] = MyProject.Forms.frmNetworkPDCH.cmbP9S1;
      cmb_system_type[1] = MyProject.Forms.frmNetworkPDCH.cmbP9S2;
      cmb_system_type[2] = MyProject.Forms.frmNetworkPDCH.cmbP9S3;
      cmb_system_type[3] = MyProject.Forms.frmNetworkPDCH.cmbP9S4;
      cmb_system_type[4] = MyProject.Forms.frmNetworkPDCH.cmbP9S5;
      cmb_system_type[5] = MyProject.Forms.frmNetworkPDCH.cmbP9S6;
      cmb_system_type[6] = MyProject.Forms.frmNetworkPDCH.cmbP9S7;
      cmb_system_type[7] = MyProject.Forms.frmNetworkPDCH.cmbP9S8;
      this.pdch_chkport_check(MyProject.Forms.frmNetworkPDCH.chkPort9, i, j, k9, cmb_system_type);
      int k10 = 9;
      cmb_system_type[0] = MyProject.Forms.frmNetworkPDCH.cmbP10S1;
      cmb_system_type[1] = MyProject.Forms.frmNetworkPDCH.cmbP10S2;
      cmb_system_type[2] = MyProject.Forms.frmNetworkPDCH.cmbP10S3;
      cmb_system_type[3] = MyProject.Forms.frmNetworkPDCH.cmbP10S4;
      cmb_system_type[4] = MyProject.Forms.frmNetworkPDCH.cmbP10S5;
      cmb_system_type[5] = MyProject.Forms.frmNetworkPDCH.cmbP10S6;
      cmb_system_type[6] = MyProject.Forms.frmNetworkPDCH.cmbP10S7;
      cmb_system_type[7] = MyProject.Forms.frmNetworkPDCH.cmbP10S8;
      this.pdch_chkport_check(MyProject.Forms.frmNetworkPDCH.chkPort10, i, j, k10, cmb_system_type);
      int k11 = 10;
      cmb_system_type[0] = MyProject.Forms.frmNetworkPDCH.cmbP11S1;
      cmb_system_type[1] = MyProject.Forms.frmNetworkPDCH.cmbP11S2;
      cmb_system_type[2] = MyProject.Forms.frmNetworkPDCH.cmbP11S3;
      cmb_system_type[3] = MyProject.Forms.frmNetworkPDCH.cmbP11S4;
      cmb_system_type[4] = MyProject.Forms.frmNetworkPDCH.cmbP11S5;
      cmb_system_type[5] = MyProject.Forms.frmNetworkPDCH.cmbP11S6;
      cmb_system_type[6] = MyProject.Forms.frmNetworkPDCH.cmbP11S7;
      cmb_system_type[7] = MyProject.Forms.frmNetworkPDCH.cmbP11S8;
      this.pdch_chkport_check(MyProject.Forms.frmNetworkPDCH.chkPort11, i, j, k11, cmb_system_type);
      int k12 = 11;
      cmb_system_type[0] = MyProject.Forms.frmNetworkPDCH.cmbP12S1;
      cmb_system_type[1] = MyProject.Forms.frmNetworkPDCH.cmbP12S2;
      cmb_system_type[2] = MyProject.Forms.frmNetworkPDCH.cmbP12S3;
      cmb_system_type[3] = MyProject.Forms.frmNetworkPDCH.cmbP12S4;
      cmb_system_type[4] = MyProject.Forms.frmNetworkPDCH.cmbP12S5;
      cmb_system_type[5] = MyProject.Forms.frmNetworkPDCH.cmbP12S6;
      cmb_system_type[6] = MyProject.Forms.frmNetworkPDCH.cmbP12S7;
      cmb_system_type[7] = MyProject.Forms.frmNetworkPDCH.cmbP12S8;
      this.pdch_chkport_check(MyProject.Forms.frmNetworkPDCH.chkPort12, i, j, k12, cmb_system_type);
      int k13 = 12;
      cmb_system_type[0] = MyProject.Forms.frmNetworkPDCH.cmbP13S1;
      cmb_system_type[1] = MyProject.Forms.frmNetworkPDCH.cmbP13S2;
      cmb_system_type[2] = MyProject.Forms.frmNetworkPDCH.cmbP13S3;
      cmb_system_type[3] = MyProject.Forms.frmNetworkPDCH.cmbP13S4;
      cmb_system_type[4] = MyProject.Forms.frmNetworkPDCH.cmbP13S5;
      cmb_system_type[5] = MyProject.Forms.frmNetworkPDCH.cmbP13S6;
      cmb_system_type[6] = MyProject.Forms.frmNetworkPDCH.cmbP13S7;
      cmb_system_type[7] = MyProject.Forms.frmNetworkPDCH.cmbP13S8;
      this.pdch_chkport_check(MyProject.Forms.frmNetworkPDCH.chkPort13, i, j, k13, cmb_system_type);
      int k14 = 13;
      cmb_system_type[0] = MyProject.Forms.frmNetworkPDCH.cmbP14S1;
      cmb_system_type[1] = MyProject.Forms.frmNetworkPDCH.cmbP14S2;
      cmb_system_type[2] = MyProject.Forms.frmNetworkPDCH.cmbP14S3;
      cmb_system_type[3] = MyProject.Forms.frmNetworkPDCH.cmbP14S4;
      cmb_system_type[4] = MyProject.Forms.frmNetworkPDCH.cmbP14S5;
      cmb_system_type[5] = MyProject.Forms.frmNetworkPDCH.cmbP14S6;
      cmb_system_type[6] = MyProject.Forms.frmNetworkPDCH.cmbP14S7;
      cmb_system_type[7] = MyProject.Forms.frmNetworkPDCH.cmbP14S8;
      this.pdch_chkport_check(MyProject.Forms.frmNetworkPDCH.chkPort14, i, j, k14, cmb_system_type);
      int k15 = 14;
      cmb_system_type[0] = MyProject.Forms.frmNetworkPDCH.cmbP15S1;
      cmb_system_type[1] = MyProject.Forms.frmNetworkPDCH.cmbP15S2;
      cmb_system_type[2] = MyProject.Forms.frmNetworkPDCH.cmbP15S3;
      cmb_system_type[3] = MyProject.Forms.frmNetworkPDCH.cmbP15S4;
      cmb_system_type[4] = MyProject.Forms.frmNetworkPDCH.cmbP15S5;
      cmb_system_type[5] = MyProject.Forms.frmNetworkPDCH.cmbP15S6;
      cmb_system_type[6] = MyProject.Forms.frmNetworkPDCH.cmbP15S7;
      cmb_system_type[7] = MyProject.Forms.frmNetworkPDCH.cmbP15S8;
      this.pdch_chkport_check(MyProject.Forms.frmNetworkPDCH.chkPort15, i, j, k15, cmb_system_type);
      int k16 = 15;
      cmb_system_type[0] = MyProject.Forms.frmNetworkPDCH.cmbP16S1;
      cmb_system_type[1] = MyProject.Forms.frmNetworkPDCH.cmbP16S2;
      cmb_system_type[2] = MyProject.Forms.frmNetworkPDCH.cmbP16S3;
      cmb_system_type[3] = MyProject.Forms.frmNetworkPDCH.cmbP16S4;
      cmb_system_type[4] = MyProject.Forms.frmNetworkPDCH.cmbP16S5;
      cmb_system_type[5] = MyProject.Forms.frmNetworkPDCH.cmbP16S6;
      cmb_system_type[6] = MyProject.Forms.frmNetworkPDCH.cmbP16S7;
      cmb_system_type[7] = MyProject.Forms.frmNetworkPDCH.cmbP16S8;
      this.pdch_chkport_check(MyProject.Forms.frmNetworkPDCH.chkPort16, i, j, k16, cmb_system_type);
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(MyProject.Forms.frmNetworkPDCH.txtPdchAddress.Text, "0", false) == 0)
        MyProject.Forms.frmNetworkPDCH.txtPdchAddress.Text = string.Empty;
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(MyProject.Forms.frmNetworkPDCH.cmbPdchPfno.Text, "0", false) == 0)
        MyProject.Forms.frmNetworkPDCH.cmbPdchPfno.Text = string.Empty;
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(MyProject.Forms.frmNetworkPDCH.cmbPdchSharedPfno.Text, "0", false) == 0)
        MyProject.Forms.frmNetworkPDCH.cmbPdchSharedPfno.Text = string.Empty;
      MyProject.Forms.frmNetworkPDCH.Show();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void agdb_display_board_data(int i, int j)
  {
    try
    {
      MyProject.Forms.frmNetworkAGDB.txtAgdbName.Text = frmMainFormIPIS.mdch_db.mdch_port[i].dis_board[j].dis_board_name;
      MyProject.Forms.frmNetworkAGDB.txtAgdbAddress.Text = Conversions.ToString(frmMainFormIPIS.mdch_db.mdch_port[i].dis_board[j].dis_board_addr);
      MyProject.Forms.frmNetworkAGDB.chkAgdbAllPfno.Visible = true;
      MyProject.Forms.frmNetworkAGDB.lblAllPfno.Visible = true;
      if (!frmMainFormIPIS.mdch_db.mdch_port[i].dis_board[j].all_platfroms)
      {
        MyProject.Forms.frmNetworkAGDB.cmbAgdbPfno.Text = frmMainFormIPIS.mdch_db.mdch_port[i].dis_board[j].platform_no;
        MyProject.Forms.frmNetworkAGDB.chkAgdbAllPfno.Checked = false;
      }
      else
      {
        MyProject.Forms.frmNetworkAGDB.cmbAgdbPfno.Text = string.Empty;
        MyProject.Forms.frmNetworkAGDB.chkAgdbAllPfno.Checked = true;
      }
      if (frmMainFormIPIS.mdch_db.mdch_port[i].dis_board[j].shared_platform)
      {
        MyProject.Forms.frmNetworkAGDB.chkAGDBSharedPfNo.Checked = true;
        MyProject.Forms.frmNetworkAGDB.cmbAgdbSharedPfno.Text = frmMainFormIPIS.mdch_db.mdch_port[i].dis_board[j].shared_platform_no;
      }
      else
      {
        MyProject.Forms.frmNetworkAGDB.chkAGDBSharedPfNo.Checked = false;
        MyProject.Forms.frmNetworkAGDB.cmbAgdbSharedPfno.Text = string.Empty;
      }
      MyProject.Forms.frmNetworkAGDB.txtAgdbMsgSwDly.Text = Conversions.ToString(frmMainFormIPIS.mdch_db.mdch_port[i].dis_board[j].switching_time);
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(MyProject.Forms.frmNetworkAGDB.txtAgdbAddress.Text, "0", false) == 0)
        MyProject.Forms.frmNetworkAGDB.txtAgdbAddress.Text = string.Empty;
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(MyProject.Forms.frmNetworkAGDB.cmbAgdbPfno.Text, "0", false) == 0)
        MyProject.Forms.frmNetworkAGDB.cmbAgdbPfno.Text = string.Empty;
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(MyProject.Forms.frmNetworkAGDB.cmbAgdbSharedPfno.Text, "0", false) == 0)
        MyProject.Forms.frmNetworkAGDB.cmbAgdbSharedPfno.Text = string.Empty;
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(MyProject.Forms.frmNetworkAGDB.txtAgdbMsgSwDly.Text, "0", false) == 0)
        MyProject.Forms.frmNetworkAGDB.txtAgdbMsgSwDly.Text = string.Empty;
      MyProject.Forms.frmNetworkAGDB.numSign.Value = new Decimal((int) frmMainFormIPIS.mdch_db.mdch_port[i].dis_board[j].agdb_sign);
      MyProject.Forms.frmNetworkAGDB.Show();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void pdb_display_board_data(int i, int j)
  {
    try
    {
      MyProject.Forms.frmNetworkPDB.txtPdbName.Text = frmMainFormIPIS.mdch_db.mdch_port[i].dis_board[j].dis_board_name;
      MyProject.Forms.frmNetworkPDB.txtPdbAddress.Text = Conversions.ToString(frmMainFormIPIS.mdch_db.mdch_port[i].dis_board[j].dis_board_addr);
      MyProject.Forms.frmNetworkPDB.cmbPdbPdno.Text = frmMainFormIPIS.mdch_db.mdch_port[i].dis_board[j].platform_no;
      if (frmMainFormIPIS.mdch_db.mdch_port[i].dis_board[j].shared_platform)
      {
        MyProject.Forms.frmNetworkPDB.chkPDBSharedPfNo.Checked = true;
        MyProject.Forms.frmNetworkPDB.cmbPdbSharedPlatformNo.Text = frmMainFormIPIS.mdch_db.mdch_port[i].dis_board[j].shared_platform_no;
      }
      else
      {
        MyProject.Forms.frmNetworkPDB.chkPDBSharedPfNo.Checked = false;
        MyProject.Forms.frmNetworkPDB.cmbPdbSharedPlatformNo.Text = string.Empty;
      }
      MyProject.Forms.frmNetworkPDB.txtPdbMsgSwDly.Text = Conversions.ToString(frmMainFormIPIS.mdch_db.mdch_port[i].dis_board[j].switching_time);
      MyProject.Forms.frmNetworkPDB.txtMultiCastAddress.Text = Conversions.ToString(frmMainFormIPIS.mdch_db.mdch_port[i].dis_board[j].multicast_addr);
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(MyProject.Forms.frmNetworkPDB.txtPdbAddress.Text, "0", false) == 0)
        MyProject.Forms.frmNetworkPDB.txtPdbAddress.Text = string.Empty;
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(MyProject.Forms.frmNetworkPDB.cmbPdbPdno.Text, "0", false) == 0)
        MyProject.Forms.frmNetworkPDB.cmbPdbPdno.Text = string.Empty;
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(MyProject.Forms.frmNetworkPDB.cmbPdbSharedPlatformNo.Text, "0", false) == 0)
        MyProject.Forms.frmNetworkPDB.cmbPdbSharedPlatformNo.Text = string.Empty;
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(MyProject.Forms.frmNetworkPDB.txtPdbMsgSwDly.Text, "0", false) == 0)
        MyProject.Forms.frmNetworkPDB.txtPdbMsgSwDly.Text = string.Empty;
      MyProject.Forms.frmNetworkPDB.Show();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void mldb_display_board_data(int i, int j)
  {
    try
    {
      MyProject.Forms.frmNetworkMLDB.txtMldbName.Text = frmMainFormIPIS.mdch_db.mdch_port[i].dis_board[j].dis_board_name;
      MyProject.Forms.frmNetworkMLDB.txtMldbAddress.Text = Conversions.ToString(frmMainFormIPIS.mdch_db.mdch_port[i].dis_board[j].dis_board_addr);
      MyProject.Forms.frmNetworkMLDB.numMldbNoLines.Value = new Decimal((int) frmMainFormIPIS.mdch_db.mdch_port[i].dis_board[j].mldb_no_of_lines);
      MyProject.Forms.frmNetworkMLDB.cmbMldbType.Text = frmMainFormIPIS.mdch_db.mdch_port[i].dis_board[j].mldb_type;
      MyProject.Forms.frmNetworkMLDB.txtMldbMsgSwDly.Text = Conversions.ToString(frmMainFormIPIS.mdch_db.mdch_port[i].dis_board[j].switching_time);
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(MyProject.Forms.frmNetworkMLDB.txtMldbAddress.Text, "0", false) == 0)
        MyProject.Forms.frmNetworkMLDB.txtMldbAddress.Text = string.Empty;
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(MyProject.Forms.frmNetworkMLDB.txtMldbMsgSwDly.Text, "0", false) == 0)
        MyProject.Forms.frmNetworkMLDB.txtMldbMsgSwDly.Text = string.Empty;
      MyProject.Forms.frmNetworkMLDB.Show();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void port_system_selected(ComboBox cmbps, int i, int j)
  {
    try
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(cmbps.Text, "PDCH", false) == 0)
      {
        this.pdch_hub_data(i, j);
        frmNetworkMDCH.hub_type = false;
      }
      else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(cmbps.Text, "AGDB", false) == 0)
      {
        frmNetworkMDCH.hub_type = true;
        this.agdb_display_board_data(i, j);
      }
      else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(cmbps.Text, "PDB", false) == 0)
      {
        this.pdb_display_board_data(i, j);
        frmNetworkMDCH.hub_type = true;
      }
      else
      {
        if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(cmbps.Text, "MLDB", false) != 0)
          return;
        this.mldb_display_board_data(i, j);
        frmNetworkMDCH.hub_type = true;
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void btnP1S1_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 0;
    frmNetworkMDCH.mdch_system_num = (byte) 0;
    this.port_system_selected(this.cmbP1S1, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP1S2_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 0;
    frmNetworkMDCH.mdch_system_num = (byte) 1;
    this.port_system_selected(this.cmbP1S2, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP1S3_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 0;
    frmNetworkMDCH.mdch_system_num = (byte) 2;
    this.port_system_selected(this.cmbP1S3, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP1S4_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 0;
    frmNetworkMDCH.mdch_system_num = (byte) 3;
    this.port_system_selected(this.cmbP1S4, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP2S1_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 1;
    frmNetworkMDCH.mdch_system_num = (byte) 0;
    this.port_system_selected(this.cmbP2S1, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP2S2_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 1;
    frmNetworkMDCH.mdch_system_num = (byte) 1;
    this.port_system_selected(this.cmbP2S2, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP2S3_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 1;
    frmNetworkMDCH.mdch_system_num = (byte) 2;
    this.port_system_selected(this.cmbP2S3, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP2S4_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 1;
    frmNetworkMDCH.mdch_system_num = (byte) 3;
    this.port_system_selected(this.cmbP2S4, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP3S1_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 2;
    frmNetworkMDCH.mdch_system_num = (byte) 0;
    this.port_system_selected(this.cmbP3S1, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP3S2_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 2;
    frmNetworkMDCH.mdch_system_num = (byte) 1;
    this.port_system_selected(this.cmbP3S2, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP3S3_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 2;
    frmNetworkMDCH.mdch_system_num = (byte) 2;
    this.port_system_selected(this.cmbP3S3, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP3S4_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 2;
    frmNetworkMDCH.mdch_system_num = (byte) 3;
    this.port_system_selected(this.cmbP3S4, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP4S1_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 3;
    frmNetworkMDCH.mdch_system_num = (byte) 0;
    this.port_system_selected(this.cmbP4S1, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP4S2_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 3;
    frmNetworkMDCH.mdch_system_num = (byte) 1;
    this.port_system_selected(this.cmbP4S2, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP4S3_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 3;
    frmNetworkMDCH.mdch_system_num = (byte) 2;
    this.port_system_selected(this.cmbP4S3, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP4S4_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 3;
    frmNetworkMDCH.mdch_system_num = (byte) 3;
    this.port_system_selected(this.cmbP4S4, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP5S1_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 4;
    frmNetworkMDCH.mdch_system_num = (byte) 0;
    this.port_system_selected(this.cmbP5S1, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP5S2_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 4;
    frmNetworkMDCH.mdch_system_num = (byte) 1;
    this.port_system_selected(this.cmbP5S2, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP5S3_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 4;
    frmNetworkMDCH.mdch_system_num = (byte) 2;
    this.port_system_selected(this.cmbP5S3, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP5S4_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 4;
    frmNetworkMDCH.mdch_system_num = (byte) 3;
    this.port_system_selected(this.cmbP5S4, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP6S1_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 5;
    frmNetworkMDCH.mdch_system_num = (byte) 0;
    this.port_system_selected(this.cmbP6S1, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP6S2_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 5;
    frmNetworkMDCH.mdch_system_num = (byte) 1;
    this.port_system_selected(this.cmbP6S2, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP6S3_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 5;
    frmNetworkMDCH.mdch_system_num = (byte) 2;
    this.port_system_selected(this.cmbP6S3, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP6S4_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 5;
    frmNetworkMDCH.mdch_system_num = (byte) 3;
    this.port_system_selected(this.cmbP6S4, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP7S1_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 6;
    frmNetworkMDCH.mdch_system_num = (byte) 0;
    this.port_system_selected(this.cmbP7S1, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP7S2_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 6;
    frmNetworkMDCH.mdch_system_num = (byte) 1;
    this.port_system_selected(this.cmbP7S2, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP7S3_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 6;
    frmNetworkMDCH.mdch_system_num = (byte) 2;
    this.port_system_selected(this.cmbP7S3, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP7S4_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 6;
    frmNetworkMDCH.mdch_system_num = (byte) 3;
    this.port_system_selected(this.cmbP7S4, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP8S1_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 7;
    frmNetworkMDCH.mdch_system_num = (byte) 0;
    this.port_system_selected(this.cmbP8S1, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP8S2_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 7;
    frmNetworkMDCH.mdch_system_num = (byte) 1;
    this.port_system_selected(this.cmbP8S2, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP8S3_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 7;
    frmNetworkMDCH.mdch_system_num = (byte) 2;
    this.port_system_selected(this.cmbP8S3, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP8S4_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 7;
    frmNetworkMDCH.mdch_system_num = (byte) 3;
    this.port_system_selected(this.cmbP8S4, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP9S1_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 8;
    frmNetworkMDCH.mdch_system_num = (byte) 0;
    this.port_system_selected(this.cmbP9S1, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP9S2_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 8;
    frmNetworkMDCH.mdch_system_num = (byte) 1;
    this.port_system_selected(this.cmbP9S2, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP9S3_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 8;
    frmNetworkMDCH.mdch_system_num = (byte) 2;
    this.port_system_selected(this.cmbP9S3, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP9S4_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 8;
    frmNetworkMDCH.mdch_system_num = (byte) 3;
    this.port_system_selected(this.cmbP9S4, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP10S1_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 9;
    frmNetworkMDCH.mdch_system_num = (byte) 0;
    this.port_system_selected(this.cmbP10S1, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP10S2_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 9;
    frmNetworkMDCH.mdch_system_num = (byte) 1;
    this.port_system_selected(this.cmbP10S2, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP10S3_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 9;
    frmNetworkMDCH.mdch_system_num = (byte) 2;
    this.port_system_selected(this.cmbP10S3, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP10S4_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 9;
    frmNetworkMDCH.mdch_system_num = (byte) 3;
    this.port_system_selected(this.cmbP10S4, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP11S1_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 10;
    frmNetworkMDCH.mdch_system_num = (byte) 0;
    this.port_system_selected(this.cmbP11S1, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP11S2_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 10;
    frmNetworkMDCH.mdch_system_num = (byte) 1;
    this.port_system_selected(this.cmbP11S2, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP11S3_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 10;
    frmNetworkMDCH.mdch_system_num = (byte) 2;
    this.port_system_selected(this.cmbP11S3, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP11S4_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 10;
    frmNetworkMDCH.mdch_system_num = (byte) 3;
    this.port_system_selected(this.cmbP11S4, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP12S1_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 11;
    frmNetworkMDCH.mdch_system_num = (byte) 0;
    this.port_system_selected(this.cmbP12S1, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP12S2_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 11;
    frmNetworkMDCH.mdch_system_num = (byte) 1;
    this.port_system_selected(this.cmbP12S2, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP12S3_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 11;
    frmNetworkMDCH.mdch_system_num = (byte) 2;
    this.port_system_selected(this.cmbP12S3, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP12S4_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 11;
    frmNetworkMDCH.mdch_system_num = (byte) 3;
    this.port_system_selected(this.cmbP12S4, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP13S1_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 12;
    frmNetworkMDCH.mdch_system_num = (byte) 0;
    this.port_system_selected(this.cmbP13S1, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP13S2_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 12;
    frmNetworkMDCH.mdch_system_num = (byte) 1;
    this.port_system_selected(this.cmbP13S2, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP13S3_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 12;
    frmNetworkMDCH.mdch_system_num = (byte) 2;
    this.port_system_selected(this.cmbP13S3, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP13S4_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 12;
    frmNetworkMDCH.mdch_system_num = (byte) 3;
    this.port_system_selected(this.cmbP13S3, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP14S1_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 13;
    frmNetworkMDCH.mdch_system_num = (byte) 0;
    this.port_system_selected(this.cmbP14S1, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP14S2_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 13;
    frmNetworkMDCH.mdch_system_num = (byte) 1;
    this.port_system_selected(this.cmbP14S2, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP14S3_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 13;
    frmNetworkMDCH.mdch_system_num = (byte) 2;
    this.port_system_selected(this.cmbP14S3, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP14S4_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 13;
    frmNetworkMDCH.mdch_system_num = (byte) 3;
    this.port_system_selected(this.cmbP14S4, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP15S1_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 14;
    frmNetworkMDCH.mdch_system_num = (byte) 0;
    this.port_system_selected(this.cmbP15S1, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP15S2_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 14;
    frmNetworkMDCH.mdch_system_num = (byte) 1;
    this.port_system_selected(this.cmbP15S2, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP15S3_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 14;
    frmNetworkMDCH.mdch_system_num = (byte) 2;
    this.port_system_selected(this.cmbP15S3, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP15S4_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 14;
    frmNetworkMDCH.mdch_system_num = (byte) 3;
    this.port_system_selected(this.cmbP15S4, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP16S1_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 15;
    frmNetworkMDCH.mdch_system_num = (byte) 0;
    this.port_system_selected(this.cmbP16S1, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP16S2_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 15;
    frmNetworkMDCH.mdch_system_num = (byte) 1;
    this.port_system_selected(this.cmbP16S2, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP16S3_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 15;
    frmNetworkMDCH.mdch_system_num = (byte) 2;
    this.port_system_selected(this.cmbP16S3, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }

  private void btnP16S4_Click(object sender, EventArgs e)
  {
    frmNetworkMDCH.mdch_port_num = (byte) 15;
    frmNetworkMDCH.mdch_system_num = (byte) 3;
    this.port_system_selected(this.cmbP16S4, (int) frmNetworkMDCH.mdch_port_num, (int) frmNetworkMDCH.mdch_system_num);
  }
}

}