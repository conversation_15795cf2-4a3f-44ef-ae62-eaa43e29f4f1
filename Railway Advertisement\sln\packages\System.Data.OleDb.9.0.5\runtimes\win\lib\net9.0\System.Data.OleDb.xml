<?xml version="1.0"?>
<doc>
    <assembly>
        <name>System.Data.OleDb</name>
    </assembly>
    <members>
        <member name="M:System.Data.Common.DbConnectionOptions.CreateConnectionStringValidKeyRegex">
            <remarks>
            Pattern:<br/>
            <code>^(?![;\\s])[^\\p{Cc}]+(?&lt;!\\s)$</code><br/>
            Explanation:<br/>
            <code>
            ○ Match if at the beginning of the string.<br/>
            ○ Zero-width negative lookahead.<br/>
                ○ Match a character in the set [;\s].<br/>
            ○ Match a character in the set [^\p{Cc}] greedily at least once.<br/>
            ○ Zero-width negative lookbehind.<br/>
                ○ Match a whitespace character right-to-left.<br/>
            ○ Match if at the end of the string or if before an ending newline.<br/>
            </code>
            </remarks>
        </member>
        <member name="M:System.Data.Common.DbConnectionOptions.CreateConnectionStringQuoteValueRegex">
            <remarks>
            Pattern:<br/>
            <code>^[^"'=;\\s\\p{Cc}]*$</code><br/>
            Explanation:<br/>
            <code>
            ○ Match if at the beginning of the string.<br/>
            ○ Match a character in the set [^"';=\s\p{Cc}] atomically any number of times.<br/>
            ○ Match if at the end of the string or if before an ending newline.<br/>
            </code>
            </remarks>
        </member>
        <member name="M:System.Data.Common.DbConnectionOptions.CreateConnectionStringQuoteOdbcValueRegex">
            <remarks>
            Pattern:<br/>
            <code>^\\{([^\\}\0]|\\}\\})*\\}$</code><br/>
            Options:<br/>
            <code>RegexOptions.ExplicitCapture</code><br/>
            Explanation:<br/>
            <code>
            ○ Match if at the beginning of the string.<br/>
            ○ Match '{'.<br/>
            ○ Loop greedily any number of times.<br/>
                ○ Match with 2 alternative expressions.<br/>
                    ○ Match a character in the set [^\0}].<br/>
                    ○ Match the string "}}".<br/>
            ○ Match '}'.<br/>
            ○ Match if at the end of the string or if before an ending newline.<br/>
            </code>
            </remarks>
        </member>
        <member name="T:System.Data.OleDb.PROPVARIANT">
             <summary>
             Managed view of unmanaged PROPVARIANT type
             </summary>
             <remarks>
             PROPVARIANT can represent many different things.  We are only interested in strings
             for this version but the full range of values is listed her for completeness.
            
             typedef unsigned short VARTYPE;
             typedef unsigned short WORD;
             typedef struct PROPVARIANT {
             VARTYPE vt;  WORD wReserved1;  WORD wReserved2;  WORD wReserved3;
             union {
                 CHAR cVal;
                 UCHAR bVal;
                 SHORT iVal;
                 USHORT uiVal;
                 LONG lVal;
                 INT intVal;
                 ULONG ulVal;
                 UINT uintVal;
                 LARGE_INTEGER hVal;
                 ULARGE_INTEGER uhVal;
                 FLOAT fltVal;    DOUBLE dblVal;    CY cyVal;    DATE date;
                 BSTR bstrVal;    VARIANT_BOOL boolVal;    SCODE scode;
                 FILETIME filetime;    LPSTR pszVal;    LPWSTR pwszVal;
                 CLSID* puuid;    CLIPDATA* pclipdata;    BLOB blob;
                 IStream* pStream;    IStorage* pStorage;    IUnknown* punkVal;
                 IDispatch* pdispVal;    LPSAFEARRAY parray;    CAC cac;
                 CAUB caub;    CAI cai;    CAUI caui;    CAL cal;    CAUL caul;
                 CAH cah;    CAUH cauh;    CAFLT caflt;    CADBL cadbl;
                 CACY cacy;    CADATE cadate;    CABSTR cabstr;
                 CABOOL cabool;    CASCODE cascode;    CALPSTR calpstr;
                 CALPWSTR calpwstr;    CAFILETIME cafiletime;    CACLSID cauuid;
                 CACLIPDATA caclipdata;    CAPROPVARIANT capropvar;
                 CHAR* pcVal;    UCHAR* pbVal;    SHORT* piVal;    USHORT* puiVal;
                 LONG* plVal;    ULONG* pulVal;    INT* pintVal;    UINT* puintVal;
                 FLOAT* pfltVal;    DOUBLE* pdblVal;    VARIANT_BOOL* pboolVal;
                 DECIMAL* pdecVal;    SCODE* pscode;    CY* pcyVal;
                 PROPVARIANT* pvarVal;
             };
             } PROPVARIANT;
             </remarks>
        </member>
        <member name="F:System.Data.OleDb.PROPVARIANT.vt">
            <summary>
            Variant type
            </summary>
        </member>
        <member name="F:System.Data.OleDb.PROPVARIANT.wReserved1">
            <summary>
            unused
            </summary>
        </member>
        <member name="F:System.Data.OleDb.PROPVARIANT.wReserved2">
            <summary>
            unused
            </summary>
        </member>
        <member name="F:System.Data.OleDb.PROPVARIANT.wReserved3">
            <summary>
            unused
            </summary>
        </member>
        <member name="F:System.Data.OleDb.PROPVARIANT.union">
            <summary>
            union where the actual variant value lives
            </summary>
        </member>
        <member name="T:System.Data.OleDb.VARTYPE">
            <summary>
            enumeration for all legal types of a PROPVARIANT
            </summary>
            <remarks>add definitions as needed</remarks>
        </member>
        <member name="F:System.Data.OleDb.VARTYPE.VT_BSTR">
            <summary>
            BSTR
            </summary>
        </member>
        <member name="F:System.Data.OleDb.VARTYPE.VT_LPSTR">
            <summary>
            LPSTR
            </summary>
        </member>
        <member name="F:System.Data.OleDb.VARTYPE.VT_FILETIME">
            <summary>
            FILETIME
            </summary>
        </member>
        <member name="T:System.Data.OleDb.PropVariantUnion">
            <summary>
            Union portion of PROPVARIANT
            </summary>
            <remarks>
            All fields (or their placeholders) are declared even if
            they are not used. This is to make sure that the size of
            the union matches the size of the union in
            the actual unmanaged PROPVARIANT structure
            for all architectures (32-bit/64-bit).
            Points to note:
            - All pointer type fields are declared as IntPtr.
            - CAxxx type fields (like CAC, CAUB, etc.) are all of same
                structural layout, hence not all declared individually
                since they are not used. A placeholder CArray
                is used to represent all of them to account for the
                size of these types. CArray is defined later.
            - Rest of the fields are declared with corresponding
                managed equivalent types.
            </remarks>
        </member>
        <member name="F:System.Data.OleDb.PropVariantUnion.cVal">
            <summary>
            CHAR
            </summary>
        </member>
        <member name="F:System.Data.OleDb.PropVariantUnion.bVal">
            <summary>
            UCHAR
            </summary>
        </member>
        <member name="F:System.Data.OleDb.PropVariantUnion.iVal">
            <summary>
            SHORT
            </summary>
        </member>
        <member name="F:System.Data.OleDb.PropVariantUnion.uiVal">
            <summary>
            USHORT
            </summary>
        </member>
        <member name="F:System.Data.OleDb.PropVariantUnion.lVal">
            <summary>
            LONG
            </summary>
        </member>
        <member name="F:System.Data.OleDb.PropVariantUnion.ulVal">
            <summary>
            ULONG
            </summary>
        </member>
        <member name="F:System.Data.OleDb.PropVariantUnion.intVal">
            <summary>
            INT
            </summary>
        </member>
        <member name="F:System.Data.OleDb.PropVariantUnion.uintVal">
            <summary>
            UINT
            </summary>
        </member>
        <member name="F:System.Data.OleDb.PropVariantUnion.hVal">
            <summary>
            LARGE_INTEGER
            </summary>
        </member>
        <member name="F:System.Data.OleDb.PropVariantUnion.uhVal">
            <summary>
            ULARGE_INTEGER
            </summary>
        </member>
        <member name="F:System.Data.OleDb.PropVariantUnion.fltVal">
            <summary>
            FLOAT
            </summary>
        </member>
        <member name="F:System.Data.OleDb.PropVariantUnion.dblVal">
            <summary>
            DOUBLE
            </summary>
        </member>
        <member name="F:System.Data.OleDb.PropVariantUnion.boolVal">
            <summary>
            VARIANT_BOOL
            </summary>
        </member>
        <member name="F:System.Data.OleDb.PropVariantUnion.scode">
            <summary>
            SCODE
            </summary>
        </member>
        <member name="F:System.Data.OleDb.PropVariantUnion.cyVal">
            <summary>
            CY
            </summary>
        </member>
        <member name="F:System.Data.OleDb.PropVariantUnion.date">
            <summary>
            DATE
            </summary>
        </member>
        <member name="F:System.Data.OleDb.PropVariantUnion.filetime">
            <summary>
            FILETIME
            </summary>
        </member>
        <member name="F:System.Data.OleDb.PropVariantUnion.puuid">
            <summary>
            CLSID*
            </summary>
        </member>
        <member name="F:System.Data.OleDb.PropVariantUnion.pclipdata">
            <summary>
            CLIPDATA*
            </summary>
        </member>
        <member name="F:System.Data.OleDb.PropVariantUnion.bstrVal">
            <summary>
            BSTR
            </summary>
        </member>
        <member name="F:System.Data.OleDb.PropVariantUnion.bstrblobVal">
            <summary>
            BSTRBLOB
            </summary>
        </member>
        <member name="F:System.Data.OleDb.PropVariantUnion.blob">
            <summary>
            BLOB
            </summary>
        </member>
        <member name="F:System.Data.OleDb.PropVariantUnion.pszVal">
            <summary>
            LPSTR
            </summary>
        </member>
        <member name="F:System.Data.OleDb.PropVariantUnion.pwszVal">
            <summary>
            LPWSTR
            </summary>
        </member>
        <member name="F:System.Data.OleDb.PropVariantUnion.punkVal">
            <summary>
            IUnknown*
            </summary>
        </member>
        <member name="F:System.Data.OleDb.PropVariantUnion.pdispVal">
            <summary>
            IDispatch*
            </summary>
        </member>
        <member name="F:System.Data.OleDb.PropVariantUnion.pStream">
            <summary>
            IStream*
            </summary>
        </member>
        <member name="F:System.Data.OleDb.PropVariantUnion.pStorage">
            <summary>
            IStorage*
            </summary>
        </member>
        <member name="F:System.Data.OleDb.PropVariantUnion.pVersionedStream">
            <summary>
            LPVERSIONEDSTREAM
            </summary>
        </member>
        <member name="F:System.Data.OleDb.PropVariantUnion.parray">
            <summary>
            LPSAFEARRAY
            </summary>
        </member>
        <member name="F:System.Data.OleDb.PropVariantUnion.cArray">
            <summary>
            Placeholder for
            CAC, CAUB, CAI, CAUI, CAL, CAUL, CAH, CAUH; CAFLT,
            CADBL, CABOOL, CASCODE, CACY, CADATE, CAFILETIME,
            CACLSID, CACLIPDATA, CABSTR, CABSTRBLOB,
            CALPSTR, CALPWSTR, CAPROPVARIANT
            </summary>
        </member>
        <member name="F:System.Data.OleDb.PropVariantUnion.pcVal">
            <summary>
            CHAR*
            </summary>
        </member>
        <member name="F:System.Data.OleDb.PropVariantUnion.pbVal">
            <summary>
            UCHAR*
            </summary>
        </member>
        <member name="F:System.Data.OleDb.PropVariantUnion.piVal">
            <summary>
            SHORT*
            </summary>
        </member>
        <member name="F:System.Data.OleDb.PropVariantUnion.puiVal">
            <summary>
            USHORT*
            </summary>
        </member>
        <member name="F:System.Data.OleDb.PropVariantUnion.plVal">
            <summary>
            LONG*
            </summary>
        </member>
        <member name="F:System.Data.OleDb.PropVariantUnion.pulVal">
            <summary>
            ULONG*
            </summary>
        </member>
        <member name="F:System.Data.OleDb.PropVariantUnion.pintVal">
            <summary>
            INT*
            </summary>
        </member>
        <member name="F:System.Data.OleDb.PropVariantUnion.puintVal">
            <summary>
            UINT*
            </summary>
        </member>
        <member name="F:System.Data.OleDb.PropVariantUnion.pfltVal">
            <summary>
            FLOAT*
            </summary>
        </member>
        <member name="F:System.Data.OleDb.PropVariantUnion.pdblVal">
            <summary>
            DOUBLE*
            </summary>
        </member>
        <member name="F:System.Data.OleDb.PropVariantUnion.pboolVal">
            <summary>
            VARIANT_BOOL*
            </summary>
        </member>
        <member name="F:System.Data.OleDb.PropVariantUnion.pdecVal">
            <summary>
            DECIMAL*
            </summary>
        </member>
        <member name="F:System.Data.OleDb.PropVariantUnion.pscode">
            <summary>
            SCODE*
            </summary>
        </member>
        <member name="F:System.Data.OleDb.PropVariantUnion.pcyVal">
            <summary>
            CY*
            </summary>
        </member>
        <member name="F:System.Data.OleDb.PropVariantUnion.pdate">
            <summary>
            DATE*
            </summary>
        </member>
        <member name="F:System.Data.OleDb.PropVariantUnion.pbstrVal">
            <summary>
            BSTR*
            </summary>
        </member>
        <member name="F:System.Data.OleDb.PropVariantUnion.ppunkVal">
            <summary>
            IUnknown**
            </summary>
        </member>
        <member name="F:System.Data.OleDb.PropVariantUnion.ppdispVal">
            <summary>
            IDispatch**
            </summary>
        </member>
        <member name="F:System.Data.OleDb.PropVariantUnion.pparray">
            <summary>
            LPSAFEARRAY*
            </summary>
        </member>
        <member name="F:System.Data.OleDb.PropVariantUnion.pvarVal">
            <summary>
            PROPVARIANT*
            </summary>
        </member>
        <member name="T:System.Data.OleDb.CY">
            <summary>
            CY, used in PropVariantUnion.
            </summary>
        </member>
        <member name="T:System.Data.OleDb.BSTRBLOB">
            <summary>
            BSTRBLOB, used in PropVariantUnion.
            </summary>
        </member>
        <member name="T:System.Data.OleDb.BLOB">
            <summary>
            BLOB, used in PropVariantUnion.
            </summary>
        </member>
        <member name="T:System.Data.OleDb.CArray">
            <summary>
            CArray, used in PropVariantUnion.
            </summary>
        </member>
        <member name="T:System.Data.OleDb.OleDbComWrappers">
             <summary>
             The ComWrappers implementation for System.Data.OleDb's COM interop usages.
            
             Supports IErrorInfo COM interface.
             </summary>
        </member>
        <member name="P:System.Data.ProviderBase.DbConnectionInternal.UnbindOnTransactionCompletion">
            <summary>
            Get boolean that specifies whether an enlisted transaction can be unbound from
            the connection when that transaction completes.
            </summary>
            <value>
            True if the enlisted transaction can be unbound on transaction completion; otherwise false.
            </value>
        </member>
        <member name="M:System.Data.ProviderBase.DbConnectionInternal.DoomThisConnection">
            <devdoc>Ensure that this connection cannot be put back into the pool.</devdoc>
        </member>
        <member name="M:System.Data.ProviderBase.DbConnectionInternal.TryOpenConnection(System.Data.Common.DbConnection,System.Data.ProviderBase.DbConnectionFactory,System.Threading.Tasks.TaskCompletionSource{System.Data.ProviderBase.DbConnectionInternal},System.Data.Common.DbConnectionOptions)">
            <devdoc>The default implementation is for the open connection objects, and
            it simply throws.  Our private closed-state connection objects
            override this and do the correct thing.</devdoc>
        </member>
        <member name="M:System.Data.ProviderBase.DbConnectionInternal.IsConnectionAlive(System.Boolean)">
            <summary>
            When overridden in a derived class, will check if the underlying connection is still actually alive
            </summary>
            <param name="throwOnException">If true an exception will be thrown if the connection is dead instead of returning true\false
            (this allows the caller to have the real reason that the connection is not alive (e.g. network error, etc))</param>
            <returns>True if the connection is still alive, otherwise false (If not overridden, then always true)</returns>
        </member>
        <member name="M:System.Data.ProviderBase.DbConnectionPool.ReplaceConnection(System.Data.Common.DbConnection,System.Data.Common.DbConnectionOptions,System.Data.ProviderBase.DbConnectionInternal)">
            <summary>
            Creates a new connection to replace an existing connection
            </summary>
            <param name="owningObject">Outer connection that currently owns <paramref name="oldConnection"/></param>
            <param name="userOptions">Options used to create the new connection</param>
            <param name="oldConnection">Inner connection that will be replaced</param>
            <returns>A new inner connection that is attached to the <paramref name="owningObject"/></returns>
        </member>
        <member name="P:System.SR.ADP_CollectionIndexInt32">
            <summary>Invalid index {0} for this {1} with Count={2}.</summary>
        </member>
        <member name="P:System.SR.ADP_CollectionIndexString">
            <summary>An {0} with {1} '{2}' is not contained by this {3}.</summary>
        </member>
        <member name="P:System.SR.ADP_CollectionInvalidType">
            <summary>The {0} only accepts non-null {1} type objects, not {2} objects.</summary>
        </member>
        <member name="P:System.SR.ADP_CollectionIsNotParent">
            <summary>The {0} is already contained by another {1}.</summary>
        </member>
        <member name="P:System.SR.ADP_CollectionNullValue">
            <summary>The {0} only accepts non-null {1} type objects.</summary>
        </member>
        <member name="P:System.SR.ADP_CollectionRemoveInvalidObject">
            <summary>Attempted to remove an {0} that is not contained by this {1}.</summary>
        </member>
        <member name="P:System.SR.ADP_ConnectionStateMsg_Closed">
            <summary>The connection's current state is closed.</summary>
        </member>
        <member name="P:System.SR.ADP_ConnectionStateMsg_Connecting">
            <summary>The connection's current state is connecting.</summary>
        </member>
        <member name="P:System.SR.ADP_ConnectionStateMsg_Open">
            <summary>The connection's current state is open.</summary>
        </member>
        <member name="P:System.SR.ADP_ConnectionStateMsg_OpenExecuting">
            <summary>The connection's current state is executing.</summary>
        </member>
        <member name="P:System.SR.ADP_ConnectionStateMsg_OpenFetching">
            <summary>The connection's current state is fetching.</summary>
        </member>
        <member name="P:System.SR.ADP_ConnectionStateMsg">
            <summary>The connection's current state: {0}.</summary>
        </member>
        <member name="P:System.SR.ADP_ConnectionStringSyntax">
            <summary>Format of the initialization string does not conform to specification starting at index {0}.</summary>
        </member>
        <member name="P:System.SR.ADP_DataReaderClosed">
            <summary>Invalid attempt to call {0} when reader is closed.</summary>
        </member>
        <member name="P:System.SR.ADP_InvalidEnumerationValue">
            <summary>The {0} enumeration value, {1}, is invalid.</summary>
        </member>
        <member name="P:System.SR.SqlConvert_ConvertFailed">
            <summary>Cannot convert object of type '{0}' to object of type '{1}'.</summary>
        </member>
        <member name="P:System.SR.ADP_InvalidConnectionOptionValue">
            <summary>Invalid value for key '{0}'.</summary>
        </member>
        <member name="P:System.SR.ADP_KeywordNotSupported">
            <summary>Keyword not supported: '{0}'.</summary>
        </member>
        <member name="P:System.SR.ADP_InternalProviderError">
            <summary>Internal data provider error {0}.</summary>
        </member>
        <member name="P:System.SR.ADP_InvalidMultipartName">
            <summary>{0} "{1}".</summary>
        </member>
        <member name="P:System.SR.ADP_InvalidMultipartNameQuoteUsage">
            <summary>{0} "{1}", incorrect usage of quotes.</summary>
        </member>
        <member name="P:System.SR.ADP_InvalidMultipartNameToManyParts">
            <summary>{0} "{1}", the current limit of "{2}" is insufficient.</summary>
        </member>
        <member name="P:System.SR.OLEDB_OLEDBCommandText">
            <summary>OleDbCommandBuilder.DeriveParameters failed because the OleDbCommandBuilder.CommandText property value is an invalid multipart name</summary>
        </member>
        <member name="P:System.SR.ADP_InvalidSourceBufferIndex">
            <summary>Invalid source buffer (size of {0}) offset: {1}</summary>
        </member>
        <member name="P:System.SR.ADP_InvalidDestinationBufferIndex">
            <summary>Invalid destination buffer (size of {0}) offset: {1}</summary>
        </member>
        <member name="P:System.SR.OleDb_SchemaRowsetsNotSupported">
            <summary>'{0}' interface is not supported by the '{1}' provider.  GetOleDbSchemaTable is unavailable with the current provider.</summary>
        </member>
        <member name="P:System.SR.OleDb_NoErrorInformation2">
            <summary>'{0}' failed with no error message available, result code: {1}.</summary>
        </member>
        <member name="P:System.SR.OleDb_NoErrorInformation">
            <summary>No error message available, result code: {0}.</summary>
        </member>
        <member name="P:System.SR.OleDb_MDACNotAvailable">
            <summary>The data providers require Microsoft Data Access Components (MDAC).  Please install Microsoft Data Access Components (MDAC) version 2.6 or later.</summary>
        </member>
        <member name="P:System.SR.OleDb_MSDASQLNotSupported">
            <summary>The data provider for OLEDB (System.Data.OleDb) does not support the Microsoft OLEDB Provider for ODBC Drivers (MSDASQL). Use the data provider for ODBC (System.Data.Odbc).</summary>
        </member>
        <member name="P:System.SR.OleDb_PossiblePromptNotUserInteractive">
            <summary>The data provider for OLEDB will not allow the OLEDB Provider to prompt the user in a non-interactive environment.</summary>
        </member>
        <member name="P:System.SR.OleDb_ProviderUnavailable">
            <summary>The '{0}' provider is not registered on the local machine.</summary>
        </member>
        <member name="P:System.SR.OleDb_CommandTextNotSupported">
            <summary>The ICommandText interface is not supported by the '{0}' provider. Use CommandType.TableDirect instead.</summary>
        </member>
        <member name="P:System.SR.OleDb_TransactionsNotSupported">
            <summary>The ITransactionLocal interface is not supported by the '{0}' provider.  Local transactions are unavailable with the current provider.</summary>
        </member>
        <member name="P:System.SR.OleDb_AsynchronousNotSupported">
            <summary>'Asynchronous Processing' is not a supported feature of the Data OLEDB Provider (System.Data.OleDb).</summary>
        </member>
        <member name="P:System.SR.OleDb_NoProviderSpecified">
            <summary>An OLEDB Provider was not specified in the ConnectionString.  An example would be, 'Provider=SQLOLEDB;'.</summary>
        </member>
        <member name="P:System.SR.OleDb_InvalidProviderSpecified">
            <summary>The OLEDB Provider specified in the ConnectionString is too long.</summary>
        </member>
        <member name="P:System.SR.OleDb_InvalidRestrictionsDbInfoKeywords">
            <summary>No restrictions are expected for the DbInfoKeywords OleDbSchemaGuid.</summary>
        </member>
        <member name="P:System.SR.OleDb_InvalidRestrictionsDbInfoLiteral">
            <summary>No restrictions are expected for the DbInfoLiterals OleDbSchemaGuid.</summary>
        </member>
        <member name="P:System.SR.OleDb_InvalidRestrictionsSchemaGuids">
            <summary>No restrictions are expected for the schema guid OleDbSchemaGuid.</summary>
        </member>
        <member name="P:System.SR.OleDb_NotSupportedSchemaTable">
            <summary>The {0} OleDbSchemaGuid is not a supported schema by the '{1}' provider.</summary>
        </member>
        <member name="P:System.SR.OleDb_CommandParameterBadAccessor">
            <summary>Command parameter[{0}] '{1}' is invalid.</summary>
        </member>
        <member name="P:System.SR.OleDb_CommandParameterCantConvertValue">
            <summary>Command parameter[{0}] '{1}' data value could not be converted for reasons other than sign mismatch or data overflow.</summary>
        </member>
        <member name="P:System.SR.OleDb_CommandParameterSignMismatch">
            <summary>Conversion failed for command parameter[{0}] '{1}' because the data value was signed and the type used by the provider was unsigned.</summary>
        </member>
        <member name="P:System.SR.OleDb_CommandParameterDataOverflow">
            <summary>Conversion failed for command parameter[{0}] '{1}' because the data value overflowed the type used by the provider.</summary>
        </member>
        <member name="P:System.SR.OleDb_CommandParameterUnavailable">
            <summary>Provider encountered an error while sending command parameter[{0}] '{1}' value and stopped processing.</summary>
        </member>
        <member name="P:System.SR.OleDb_CommandParameterDefault">
            <summary>Parameter[{0}] '{1}' has no default value.</summary>
        </member>
        <member name="P:System.SR.OleDb_CommandParameterError">
            <summary>Error occurred with parameter[{0}]: {1}.</summary>
        </member>
        <member name="P:System.SR.OleDb_BadStatus_ParamAcc">
            <summary>System.Data.OleDb.OleDbDataAdapter internal error: invalid parameter accessor: {0} {1}.</summary>
        </member>
        <member name="P:System.SR.OleDb_UninitializedParameters">
            <summary>Parameter[{0}]: the OleDbType property is uninitialized: OleDbType.{1}.</summary>
        </member>
        <member name="P:System.SR.OleDb_NoProviderSupportForParameters">
            <summary>The ICommandWithParameters interface is not supported by the '{0}' provider.  Command parameters are unsupported with the current provider.</summary>
        </member>
        <member name="P:System.SR.OleDb_NoProviderSupportForSProcResetParameters">
            <summary>Retrieving procedure parameter information is not supported by the '{0}' provider.</summary>
        </member>
        <member name="P:System.SR.OleDb_Fill_NotADODB">
            <summary>Object is not an ADODB.RecordSet or an ADODB.Record.</summary>
        </member>
        <member name="P:System.SR.OleDb_Fill_EmptyRecordSet">
            <summary>Unable to retrieve the '{0}' interface from the ADODB.RecordSet object.</summary>
        </member>
        <member name="P:System.SR.OleDb_Fill_EmptyRecord">
            <summary>Unable to retrieve the IRow interface from the ADODB.Record object.</summary>
        </member>
        <member name="P:System.SR.OleDb_ISourcesRowsetNotSupported">
            <summary>Type does not support the OLEDB interface ISourcesRowset</summary>
        </member>
        <member name="P:System.SR.OleDb_IDBInfoNotSupported">
            <summary>Cannot construct the ReservedWords schema collection because the provider does not support IDBInfo.</summary>
        </member>
        <member name="P:System.SR.OleDb_PropertyNotSupported">
            <summary>The property's value was not set because the provider did not support the '{0}' property, or the consumer attempted to get or set values of properties not in the Initialization property group and the data source object is uninitialized.</summary>
        </member>
        <member name="P:System.SR.OleDb_PropertyBadValue">
            <summary>Failed to initialize the '{0}' property for one of the following reasons:
              The value data type was not the data type of the property or was not null. For example, the property was DBPROP_MEMORYUSAGE, which has a data type of Int32, and the data type was I ...</summary>
        </member>
        <member name="P:System.SR.OleDb_PropertyBadOption">
            <summary>The value of Options was invalid.</summary>
        </member>
        <member name="P:System.SR.OleDb_PropertyBadColumn">
            <summary>The ColumnID element was invalid.</summary>
        </member>
        <member name="P:System.SR.OleDb_PropertyNotAllSettable">
            <summary>A '{0}' property was specified to be applied to all columns but could not be applied to one or more of them.</summary>
        </member>
        <member name="P:System.SR.OleDb_PropertyNotSettable">
            <summary>The '{0}' property was read-only, or the consumer attempted to set values of properties in the Initialization property group after the data source object was initialized. Consumers can set the value of a read-only property to its current value. This status ...</summary>
        </member>
        <member name="P:System.SR.OleDb_PropertyNotSet">
            <summary>The optional '{0}' property's value was not set to the specified value and setting the property to the specified value was not possible.</summary>
        </member>
        <member name="P:System.SR.OleDb_PropertyConflicting">
            <summary>The '{0}'property's value was not set because doing so would have conflicted with an existing property.</summary>
        </member>
        <member name="P:System.SR.OleDb_PropertyNotAvailable">
            <summary>(Reserved).</summary>
        </member>
        <member name="P:System.SR.OleDb_PropertyStatusUnknown">
            <summary>The provider returned an unknown DBPROPSTATUS_ value '{0}'.</summary>
        </member>
        <member name="P:System.SR.OleDb_BadAccessor">
            <summary>Accessor validation was deferred and was performed while the method returned data. The binding was invalid for this column or parameter.</summary>
        </member>
        <member name="P:System.SR.OleDb_BadStatusRowAccessor">
            <summary>OleDbDataAdapter internal error: invalid row set accessor: Ordinal={0} Status={1}.</summary>
        </member>
        <member name="P:System.SR.OleDb_CantConvertValue">
            <summary>The data value could not be converted for reasons other than sign mismatch or data overflow. For example, the data was corrupted in the data store but the row was still retrievable.</summary>
        </member>
        <member name="P:System.SR.OleDb_CantCreate">
            <summary>The provider could not allocate memory in which to return {0} data.</summary>
        </member>
        <member name="P:System.SR.OleDb_DataOverflow">
            <summary>Conversion failed because the {0} data value overflowed the type specified for the {0} value part in the consumer's buffer.</summary>
        </member>
        <member name="P:System.SR.OleDb_GVtUnknown">
            <summary>OleDbDataAdapter internal error: [get] Unknown OLEDB data type: 0x{0} ({1}).</summary>
        </member>
        <member name="P:System.SR.OleDb_SignMismatch">
            <summary>Conversion failed because the {0} data value was signed and the type specified for the {0} value part in the consumer's buffer was unsigned.</summary>
        </member>
        <member name="P:System.SR.OleDb_SVtUnknown">
            <summary>OleDbDataAdapter internal error: [set] Unknown OLEDB data type: 0x{0} ({1}).</summary>
        </member>
        <member name="P:System.SR.OleDb_Unavailable">
            <summary>The provider could not determine the {0} value. For example, the row was just created, the default for the {0} column was not available, and the consumer had not yet set a new {0} value.</summary>
        </member>
        <member name="P:System.SR.OleDb_UnexpectedStatusValue">
            <summary>OLEDB Provider returned an unexpected status value of {0}.</summary>
        </member>
        <member name="P:System.SR.OleDb_ThreadApartmentState">
            <summary>The OleDbDataReader.Read must be used from the same thread on which is was created if that thread's ApartmentState was not ApartmentState.MTA.</summary>
        </member>
        <member name="P:System.SR.OleDb_NoErrorMessage">
            <summary>Unspecified error: {0}</summary>
        </member>
        <member name="P:System.SR.OleDb_FailedGetDescription">
            <summary>IErrorInfo.GetDescription failed with {0}.</summary>
        </member>
        <member name="P:System.SR.OleDb_FailedGetSource">
            <summary>IErrorInfo.GetSource failed with {0}.</summary>
        </member>
        <member name="P:System.SR.OleDb_DBBindingGetVector">
            <summary>DBTYPE_VECTOR data is not supported by the Data OLEDB Provider (System.Data.OleDb).</summary>
        </member>
        <member name="P:System.SR.SQL_InvalidDataLength">
            <summary>Data length '{0}' is less than 0.</summary>
        </member>
        <member name="P:System.SR.PlatformNotSupported_OleDb">
            <summary>System.Data.OleDb is not supported on this platform.</summary>
        </member>
        <member name="P:System.SR.PlatformNotSupported_GetIDispatchForObject">
            <summary>Marshal.GetIDispatchForObject API not available on this plaTform</summary>
        </member>
        <member name="P:System.SR.ADP_EmptyString">
            <summary>Expecting non-empty string for '{0}' parameter.</summary>
        </member>
        <member name="P:System.SR.ADP_UdlFileError">
            <summary>Unable to load the UDL file.</summary>
        </member>
        <member name="P:System.SR.ADP_InvalidUDL">
            <summary>Invalid UDL file.</summary>
        </member>
        <member name="P:System.SR.ADP_InvalidDataDirectory">
            <summary>The DataDirectory substitute is not a string.</summary>
        </member>
        <member name="P:System.SR.ADP_InvalidKey">
            <summary>Invalid keyword, contain one or more of 'no characters', 'control characters', 'leading or trailing whitespace' or 'leading semicolons'.</summary>
        </member>
        <member name="P:System.SR.ADP_InvalidValue">
            <summary>The value contains embedded nulls (\\u0000).</summary>
        </member>
        <member name="P:System.SR.ADP_NoConnectionString">
            <summary>The ConnectionString property has not been initialized.</summary>
        </member>
        <member name="P:System.SR.OleDb_ConfigUnableToLoadXmlMetaDataFile">
            <summary>Unable to load the XML file specified in configuration setting '{0}'.</summary>
        </member>
        <member name="P:System.SR.OleDb_ConfigWrongNumberOfValues">
            <summary>The '{0}' configuration setting has the wrong number of values.</summary>
        </member>
        <member name="P:System.SR.ADP_PooledOpenTimeout">
            <summary>Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.</summary>
        </member>
        <member name="P:System.SR.ADP_NonPooledOpenTimeout">
            <summary>Timeout attempting to open the connection.  The time period elapsed prior to attempting to open the connection has been exceeded.</summary>
        </member>
        <member name="P:System.SR.ADP_TransactionConnectionMismatch">
            <summary>The transaction is either not associated with the current connection or has been completed.</summary>
        </member>
        <member name="P:System.SR.ADP_TransactionRequired">
            <summary>{0} requires the command to have a transaction when the connection assigned to the command is in a pending local transaction.  The Transaction property of the command has not been initialized.</summary>
        </member>
        <member name="P:System.SR.ADP_CommandTextRequired">
            <summary>{0}: CommandText property has not been initialized</summary>
        </member>
        <member name="P:System.SR.ADP_ConnectionRequired">
            <summary>{0}: Connection property has not been initialized.</summary>
        </member>
        <member name="P:System.SR.ADP_OpenConnectionRequired">
            <summary>{0} requires an open and available Connection. {1}</summary>
        </member>
        <member name="P:System.SR.ADP_NoStoredProcedureExists">
            <summary>The stored procedure '{0}' doesn't exist.</summary>
        </member>
        <member name="P:System.SR.ADP_OpenReaderExists">
            <summary>There is already an open DataReader associated with this Command which must be closed first.</summary>
        </member>
        <member name="P:System.SR.ADP_TransactionCompleted">
            <summary>The transaction assigned to this command must be the most nested pending local transaction.</summary>
        </member>
        <member name="P:System.SR.ADP_NonSeqByteAccess">
            <summary>Invalid {2} attempt at dataIndex '{0}'.  With CommandBehavior.SequentialAccess, you may only read from dataIndex '{1}' or greater.</summary>
        </member>
        <member name="P:System.SR.ADP_NumericToDecimalOverflow">
            <summary>The numerical value is too large to fit into a 96 bit decimal.</summary>
        </member>
        <member name="P:System.SR.ADP_NonSequentialColumnAccess">
            <summary>Invalid attempt to read from column ordinal '{0}'.  With CommandBehavior.SequentialAccess, you may only read from column ordinal '{1}' or greater.</summary>
        </member>
        <member name="P:System.SR.ADP_FillRequiresSourceTableName">
            <summary>Fill: expected a non-empty string for the SourceTable name.</summary>
        </member>
        <member name="P:System.SR.ADP_InvalidCommandTimeout">
            <summary>Invalid CommandTimeout value {0}; the value must be &gt;= 0.</summary>
        </member>
        <member name="P:System.SR.ADP_DeriveParametersNotSupported">
            <summary>{0} DeriveParameters only supports CommandType.StoredProcedure, not CommandType.{1}.</summary>
        </member>
        <member name="P:System.SR.ADP_UninitializedParameterSize">
            <summary>{1}[{0}]: the Size property has an invalid size of 0.</summary>
        </member>
        <member name="P:System.SR.ADP_PrepareParameterType">
            <summary>{0}.Prepare method requires all parameters to have an explicitly set type.</summary>
        </member>
        <member name="P:System.SR.ADP_PrepareParameterSize">
            <summary>{0}.Prepare method requires all variable length parameters to have an explicitly set non-zero Size.</summary>
        </member>
        <member name="P:System.SR.ADP_PrepareParameterScale">
            <summary>{0}.Prepare method requires parameters of type '{1}' have an explicitly set Precision and Scale.</summary>
        </member>
        <member name="P:System.SR.ADP_ClosedConnectionError">
            <summary>Invalid operation. The connection is closed.</summary>
        </member>
        <member name="P:System.SR.ADP_ConnectionAlreadyOpen">
            <summary>The connection was not closed. {0}</summary>
        </member>
        <member name="P:System.SR.ADP_TransactionPresent">
            <summary>Connection currently has transaction enlisted.  Finish current transaction and retry.</summary>
        </member>
        <member name="P:System.SR.ADP_LocalTransactionPresent">
            <summary>Cannot enlist in the transaction because a local transaction is in progress on the connection.  Finish local transaction and retry.</summary>
        </member>
        <member name="P:System.SR.ADP_OpenConnectionPropertySet">
            <summary>Not allowed to change the '{0}' property. {1}</summary>
        </member>
        <member name="P:System.SR.ADP_EmptyDatabaseName">
            <summary>Database cannot be null, the empty string, or string of only whitespace.</summary>
        </member>
        <member name="P:System.SR.ADP_InternalConnectionError">
            <summary>Internal DbConnection Error: {0}</summary>
        </member>
        <member name="P:System.SR.ADP_InvalidConnectTimeoutValue">
            <summary>Invalid 'Connect Timeout' value which must be an integer &gt;= 0.</summary>
        </member>
        <member name="P:System.SR.ADP_DataReaderNoData">
            <summary>No data exists for the row/column.</summary>
        </member>
        <member name="P:System.SR.ADP_InvalidDataType">
            <summary>The parameter data type of {0} is invalid.</summary>
        </member>
        <member name="P:System.SR.ADP_DbTypeNotSupported">
            <summary>No mapping exists from DbType {0} to a known {1}.</summary>
        </member>
        <member name="P:System.SR.ADP_UnknownDataTypeCode">
            <summary>Unable to handle an unknown TypeCode {0} returned by Type {1}.</summary>
        </member>
        <member name="P:System.SR.ADP_InvalidOffsetValue">
            <summary>Invalid parameter Offset value '{0}'. The value must be greater than or equal to 0.</summary>
        </member>
        <member name="P:System.SR.ADP_InvalidSizeValue">
            <summary>Invalid parameter Size value '{0}'. The value must be greater than or equal to 0.</summary>
        </member>
        <member name="P:System.SR.ADP_ParameterConversionFailed">
            <summary>Failed to convert parameter value from a {0} to a {1}.</summary>
        </member>
        <member name="P:System.SR.ADP_ParallelTransactionsNotSupported">
            <summary>{0} does not support parallel transactions.</summary>
        </member>
        <member name="P:System.SR.ADP_TransactionZombied">
            <summary>This {0} has completed; it is no longer usable.</summary>
        </member>
        <member name="P:System.SR.MDF_AmbiguousCollectionName">
            <summary>The collection name '{0}' matches at least two collections with the same name but with different case, but does not match any of them exactly.</summary>
        </member>
        <member name="P:System.SR.MDF_CollectionNameISNotUnique">
            <summary>There are multiple collections named '{0}'.</summary>
        </member>
        <member name="P:System.SR.MDF_DataTableDoesNotExist">
            <summary>The collection '{0}' is missing from the metadata XML.</summary>
        </member>
        <member name="P:System.SR.MDF_IncorrectNumberOfDataSourceInformationRows">
            <summary>The DataSourceInformation table must contain exactly one row.</summary>
        </member>
        <member name="P:System.SR.MDF_InvalidRestrictionValue">
            <summary>'{2}' is not a valid value for the '{1}' restriction of the '{0}' schema collection.</summary>
        </member>
        <member name="P:System.SR.MDF_InvalidXml">
            <summary>The metadata XML is invalid.</summary>
        </member>
        <member name="P:System.SR.MDF_InvalidXmlMissingColumn">
            <summary>The metadata XML is invalid. The {0} collection must contain a {1} column and it must be a string column.</summary>
        </member>
        <member name="P:System.SR.MDF_InvalidXmlInvalidValue">
            <summary>The metadata XML is invalid. The {1} column of the {0} collection must contain a non-empty string.</summary>
        </member>
        <member name="P:System.SR.MDF_MissingDataSourceInformationColumn">
            <summary>One of the required DataSourceInformation tables columns is missing.</summary>
        </member>
        <member name="P:System.SR.MDF_MissingRestrictionColumn">
            <summary>One or more of the required columns of the restrictions collection is missing.</summary>
        </member>
        <member name="P:System.SR.MDF_MissingRestrictionRow">
            <summary>A restriction exists for which there is no matching row in the restrictions collection.</summary>
        </member>
        <member name="P:System.SR.MDF_NoColumns">
            <summary>The schema table contains no columns.</summary>
        </member>
        <member name="P:System.SR.MDF_QueryFailed">
            <summary>Unable to build the '{0}' collection because execution of the SQL query failed. See the inner exception for details.</summary>
        </member>
        <member name="P:System.SR.MDF_TooManyRestrictions">
            <summary>More restrictions were provided than the requested schema ('{0}') supports.</summary>
        </member>
        <member name="P:System.SR.MDF_UnableToBuildCollection">
            <summary>Unable to build schema collection '{0}';</summary>
        </member>
        <member name="P:System.SR.MDF_UndefinedCollection">
            <summary>The requested collection ({0}) is not defined.</summary>
        </member>
        <member name="P:System.SR.MDF_UndefinedPopulationMechanism">
            <summary>The population mechanism '{0}' is not defined.</summary>
        </member>
        <member name="P:System.SR.MDF_UnsupportedVersion">
            <summary>The requested collection ({0}) is not supported by this version of the provider.</summary>
        </member>
        <member name="P:System.SR.ADP_QuotePrefixNotSet">
            <summary>{0} requires open connection when the quote prefix has not been set.</summary>
        </member>
        <member name="P:System.SR.Odbc_MDACWrongVersion">
            <summary>The Odbc Data Provider requires Microsoft Data Access Components (MDAC) version 2.6 or later. Version {0} was found currently installed.</summary>
        </member>
        <member name="P:System.SR.OleDb_MDACWrongVersion">
            <summary>The OLEDB Data Provider requires Microsoft Data Access Components (MDAC) version 2.6 or later. Version {0} was found currently installed.</summary>
        </member>
        <member name="T:System.Text.RegularExpressions.Generated.CreateConnectionStringValidKeyRegex_0">
            <summary>Custom <see cref="T:System.Text.RegularExpressions.Regex"/>-derived type for the CreateConnectionStringValidKeyRegex method.</summary>
        </member>
        <member name="F:System.Text.RegularExpressions.Generated.CreateConnectionStringValidKeyRegex_0.Instance">
            <summary>Cached, thread-safe singleton instance.</summary>
        </member>
        <member name="M:System.Text.RegularExpressions.Generated.CreateConnectionStringValidKeyRegex_0.#ctor">
            <summary>Initializes the instance.</summary>
        </member>
        <member name="T:System.Text.RegularExpressions.Generated.CreateConnectionStringValidKeyRegex_0.RunnerFactory">
            <summary>Provides a factory for creating <see cref="T:System.Text.RegularExpressions.RegexRunner"/> instances to be used by methods on <see cref="T:System.Text.RegularExpressions.Regex"/>.</summary>
        </member>
        <member name="M:System.Text.RegularExpressions.Generated.CreateConnectionStringValidKeyRegex_0.RunnerFactory.CreateInstance">
            <summary>Creates an instance of a <see cref="T:System.Text.RegularExpressions.RegexRunner"/> used by methods on <see cref="T:System.Text.RegularExpressions.Regex"/>.</summary>
        </member>
        <member name="T:System.Text.RegularExpressions.Generated.CreateConnectionStringValidKeyRegex_0.RunnerFactory.Runner">
            <summary>Provides the runner that contains the custom logic implementing the specified regular expression.</summary>
        </member>
        <member name="M:System.Text.RegularExpressions.Generated.CreateConnectionStringValidKeyRegex_0.RunnerFactory.Runner.Scan(System.ReadOnlySpan{System.Char})">
            <summary>Scan the <paramref name="inputSpan"/> starting from base.runtextstart for the next match.</summary>
            <param name="inputSpan">The text being scanned by the regular expression.</param>
        </member>
        <member name="M:System.Text.RegularExpressions.Generated.CreateConnectionStringValidKeyRegex_0.RunnerFactory.Runner.TryFindNextPossibleStartingPosition(System.ReadOnlySpan{System.Char})">
            <summary>Search <paramref name="inputSpan"/> starting from base.runtextpos for the next location a match could possibly start.</summary>
            <param name="inputSpan">The text being scanned by the regular expression.</param>
            <returns>true if a possible match was found; false if no more matches are possible.</returns>
        </member>
        <member name="M:System.Text.RegularExpressions.Generated.CreateConnectionStringValidKeyRegex_0.RunnerFactory.Runner.TryMatchAtCurrentPosition(System.ReadOnlySpan{System.Char})">
            <summary>Determine whether <paramref name="inputSpan"/> at base.runtextpos is a match for the regular expression.</summary>
            <param name="inputSpan">The text being scanned by the regular expression.</param>
            <returns>true if the regular expression matches at the current position; otherwise, false.</returns>
        </member>
        <member name="T:System.Text.RegularExpressions.Generated.CreateConnectionStringQuoteValueRegex_1">
            <summary>Custom <see cref="T:System.Text.RegularExpressions.Regex"/>-derived type for the CreateConnectionStringQuoteValueRegex method.</summary>
        </member>
        <member name="F:System.Text.RegularExpressions.Generated.CreateConnectionStringQuoteValueRegex_1.Instance">
            <summary>Cached, thread-safe singleton instance.</summary>
        </member>
        <member name="M:System.Text.RegularExpressions.Generated.CreateConnectionStringQuoteValueRegex_1.#ctor">
            <summary>Initializes the instance.</summary>
        </member>
        <member name="T:System.Text.RegularExpressions.Generated.CreateConnectionStringQuoteValueRegex_1.RunnerFactory">
            <summary>Provides a factory for creating <see cref="T:System.Text.RegularExpressions.RegexRunner"/> instances to be used by methods on <see cref="T:System.Text.RegularExpressions.Regex"/>.</summary>
        </member>
        <member name="M:System.Text.RegularExpressions.Generated.CreateConnectionStringQuoteValueRegex_1.RunnerFactory.CreateInstance">
            <summary>Creates an instance of a <see cref="T:System.Text.RegularExpressions.RegexRunner"/> used by methods on <see cref="T:System.Text.RegularExpressions.Regex"/>.</summary>
        </member>
        <member name="T:System.Text.RegularExpressions.Generated.CreateConnectionStringQuoteValueRegex_1.RunnerFactory.Runner">
            <summary>Provides the runner that contains the custom logic implementing the specified regular expression.</summary>
        </member>
        <member name="M:System.Text.RegularExpressions.Generated.CreateConnectionStringQuoteValueRegex_1.RunnerFactory.Runner.Scan(System.ReadOnlySpan{System.Char})">
            <summary>Scan the <paramref name="inputSpan"/> starting from base.runtextstart for the next match.</summary>
            <param name="inputSpan">The text being scanned by the regular expression.</param>
        </member>
        <member name="M:System.Text.RegularExpressions.Generated.CreateConnectionStringQuoteValueRegex_1.RunnerFactory.Runner.TryFindNextPossibleStartingPosition(System.ReadOnlySpan{System.Char})">
            <summary>Search <paramref name="inputSpan"/> starting from base.runtextpos for the next location a match could possibly start.</summary>
            <param name="inputSpan">The text being scanned by the regular expression.</param>
            <returns>true if a possible match was found; false if no more matches are possible.</returns>
        </member>
        <member name="M:System.Text.RegularExpressions.Generated.CreateConnectionStringQuoteValueRegex_1.RunnerFactory.Runner.TryMatchAtCurrentPosition(System.ReadOnlySpan{System.Char})">
            <summary>Determine whether <paramref name="inputSpan"/> at base.runtextpos is a match for the regular expression.</summary>
            <param name="inputSpan">The text being scanned by the regular expression.</param>
            <returns>true if the regular expression matches at the current position; otherwise, false.</returns>
        </member>
        <member name="T:System.Text.RegularExpressions.Generated.CreateConnectionStringQuoteOdbcValueRegex_2">
            <summary>Custom <see cref="T:System.Text.RegularExpressions.Regex"/>-derived type for the CreateConnectionStringQuoteOdbcValueRegex method.</summary>
        </member>
        <member name="F:System.Text.RegularExpressions.Generated.CreateConnectionStringQuoteOdbcValueRegex_2.Instance">
            <summary>Cached, thread-safe singleton instance.</summary>
        </member>
        <member name="M:System.Text.RegularExpressions.Generated.CreateConnectionStringQuoteOdbcValueRegex_2.#ctor">
            <summary>Initializes the instance.</summary>
        </member>
        <member name="T:System.Text.RegularExpressions.Generated.CreateConnectionStringQuoteOdbcValueRegex_2.RunnerFactory">
            <summary>Provides a factory for creating <see cref="T:System.Text.RegularExpressions.RegexRunner"/> instances to be used by methods on <see cref="T:System.Text.RegularExpressions.Regex"/>.</summary>
        </member>
        <member name="M:System.Text.RegularExpressions.Generated.CreateConnectionStringQuoteOdbcValueRegex_2.RunnerFactory.CreateInstance">
            <summary>Creates an instance of a <see cref="T:System.Text.RegularExpressions.RegexRunner"/> used by methods on <see cref="T:System.Text.RegularExpressions.Regex"/>.</summary>
        </member>
        <member name="T:System.Text.RegularExpressions.Generated.CreateConnectionStringQuoteOdbcValueRegex_2.RunnerFactory.Runner">
            <summary>Provides the runner that contains the custom logic implementing the specified regular expression.</summary>
        </member>
        <member name="M:System.Text.RegularExpressions.Generated.CreateConnectionStringQuoteOdbcValueRegex_2.RunnerFactory.Runner.Scan(System.ReadOnlySpan{System.Char})">
            <summary>Scan the <paramref name="inputSpan"/> starting from base.runtextstart for the next match.</summary>
            <param name="inputSpan">The text being scanned by the regular expression.</param>
        </member>
        <member name="M:System.Text.RegularExpressions.Generated.CreateConnectionStringQuoteOdbcValueRegex_2.RunnerFactory.Runner.TryFindNextPossibleStartingPosition(System.ReadOnlySpan{System.Char})">
            <summary>Search <paramref name="inputSpan"/> starting from base.runtextpos for the next location a match could possibly start.</summary>
            <param name="inputSpan">The text being scanned by the regular expression.</param>
            <returns>true if a possible match was found; false if no more matches are possible.</returns>
        </member>
        <member name="M:System.Text.RegularExpressions.Generated.CreateConnectionStringQuoteOdbcValueRegex_2.RunnerFactory.Runner.TryMatchAtCurrentPosition(System.ReadOnlySpan{System.Char})">
            <summary>Determine whether <paramref name="inputSpan"/> at base.runtextpos is a match for the regular expression.</summary>
            <param name="inputSpan">The text being scanned by the regular expression.</param>
            <returns>true if the regular expression matches at the current position; otherwise, false.</returns>
        </member>
        <member name="T:System.Text.RegularExpressions.Generated.Utilities">
            <summary>Helper methods used by generated <see cref="T:System.Text.RegularExpressions.Regex"/>-derived implementations.</summary>
        </member>
        <member name="F:System.Text.RegularExpressions.Generated.Utilities.s_defaultTimeout">
            <summary>Default timeout value set in <see cref="T:System.AppContext"/>, or <see cref="F:System.Text.RegularExpressions.Regex.InfiniteMatchTimeout"/> if none was set.</summary>
        </member>
        <member name="F:System.Text.RegularExpressions.Generated.Utilities.s_hasTimeout">
            <summary>Whether <see cref="F:System.Text.RegularExpressions.Generated.Utilities.s_defaultTimeout"/> is non-infinite.</summary>
        </member>
        <member name="M:System.Text.RegularExpressions.Generated.Utilities.StackPush(System.Int32[]@,System.Int32@,System.Int32)">
            <summary>Pushes 1 value onto the backtracking stack.</summary>
        </member>
        <member name="M:System.Text.RegularExpressions.Generated.Utilities.StackPush(System.Int32[]@,System.Int32@,System.Int32,System.Int32)">
            <summary>Pushes 2 values onto the backtracking stack.</summary>
        </member>
    </members>
</doc>
