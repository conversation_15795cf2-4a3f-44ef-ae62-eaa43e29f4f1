﻿namespace Announcement
{
	// Token: 0x0200000F RID: 15
	public partial class User : global::System.Windows.Forms.Form
	{
		// Token: 0x06000094 RID: 148 RVA: 0x00016C70 File Offset: 0x00014E70
		protected override void Dispose(bool disposing)
		{
			bool flag = disposing && this.components != null;
			if (flag)
			{
				this.components.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06000095 RID: 149 RVA: 0x00016CA8 File Offset: 0x00014EA8
		private void InitializeComponent()
		{
			global::System.ComponentModel.ComponentResourceManager componentResourceManager = new global::System.ComponentModel.ComponentResourceManager(typeof(global::Announcement.User));
			this.label2 = new global::System.Windows.Forms.Label();
			this.label3 = new global::System.Windows.Forms.Label();
			this.label4 = new global::System.Windows.Forms.Label();
			this.GB_Sup = new global::System.Windows.Forms.GroupBox();
			this.CHH_Reports = new global::System.Windows.Forms.CheckBox();
			this.CHH_AUser = new global::System.Windows.Forms.CheckBox();
			this.CHH_SD = new global::System.Windows.Forms.CheckBox();
			this.CHH_ASCode = new global::System.Windows.Forms.CheckBox();
			this.CHH_TDEntry = new global::System.Windows.Forms.CheckBox();
			this.CHH_Adve = new global::System.Windows.Forms.CheckBox();
			this.TB_Pass = new global::System.Windows.Forms.TextBox();
			this.TB_CPass = new global::System.Windows.Forms.TextBox();
			this.pictureBox1 = new global::System.Windows.Forms.PictureBox();
			this.BTN_Exit = new global::System.Windows.Forms.Button();
			this.BTN_Save = new global::System.Windows.Forms.Button();
			this.BTN_Add = new global::System.Windows.Forms.Button();
			this.BTN_Del = new global::System.Windows.Forms.Button();
			this.label1 = new global::System.Windows.Forms.Label();
			this.TB_PassHint = new global::System.Windows.Forms.TextBox();
			this.label5 = new global::System.Windows.Forms.Label();
			this.Cmb_User = new global::System.Windows.Forms.ComboBox();
			this.BTN_Edit = new global::System.Windows.Forms.Button();
			this.TB_UName = new global::System.Windows.Forms.TextBox();
			this.Dev_Font = new global::System.Windows.Forms.FontDialog();
			this.Lbl_UserType = new global::System.Windows.Forms.Label();
			this.Cmb_UserType = new global::System.Windows.Forms.ComboBox();
			this.GB_Sup.SuspendLayout();
			((global::System.ComponentModel.ISupportInitialize)this.pictureBox1).BeginInit();
			base.SuspendLayout();
			this.label2.AutoSize = true;
			this.label2.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 11.25f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 0);
			this.label2.ForeColor = global::System.Drawing.Color.FromArgb(0, 0, 192);
			this.label2.Location = new global::System.Drawing.Point(182, 39);
			this.label2.Name = "label2";
			this.label2.Size = new global::System.Drawing.Size(84, 18);
			this.label2.TabIndex = 1;
			this.label2.Text = "User Name";
			this.label3.AutoSize = true;
			this.label3.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 11.25f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 0);
			this.label3.ForeColor = global::System.Drawing.Color.FromArgb(0, 0, 192);
			this.label3.Location = new global::System.Drawing.Point(182, 68);
			this.label3.Name = "label3";
			this.label3.Size = new global::System.Drawing.Size(75, 18);
			this.label3.TabIndex = 2;
			this.label3.Text = "Password";
			this.label4.AutoSize = true;
			this.label4.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 11.25f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 0);
			this.label4.ForeColor = global::System.Drawing.Color.FromArgb(0, 0, 192);
			this.label4.Location = new global::System.Drawing.Point(182, 96);
			this.label4.Name = "label4";
			this.label4.Size = new global::System.Drawing.Size(132, 18);
			this.label4.TabIndex = 3;
			this.label4.Text = "Confirm Password";
			this.GB_Sup.Controls.Add(this.CHH_Reports);
			this.GB_Sup.Controls.Add(this.CHH_AUser);
			this.GB_Sup.Controls.Add(this.CHH_SD);
			this.GB_Sup.Controls.Add(this.CHH_ASCode);
			this.GB_Sup.Controls.Add(this.CHH_TDEntry);
			this.GB_Sup.Controls.Add(this.CHH_Adve);
			this.GB_Sup.Enabled = false;
			this.GB_Sup.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 11.25f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 0);
			this.GB_Sup.ForeColor = global::System.Drawing.Color.Red;
			this.GB_Sup.Location = new global::System.Drawing.Point(12, 182);
			this.GB_Sup.Name = "GB_Sup";
			this.GB_Sup.Size = new global::System.Drawing.Size(471, 118);
			this.GB_Sup.TabIndex = 5;
			this.GB_Sup.TabStop = false;
			this.GB_Sup.Text = "Select User Permission";
			this.CHH_Reports.AutoSize = true;
			this.CHH_Reports.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 11.25f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 0);
			this.CHH_Reports.ForeColor = global::System.Drawing.Color.Black;
			this.CHH_Reports.Location = new global::System.Drawing.Point(295, 53);
			this.CHH_Reports.Name = "CHH_Reports";
			this.CHH_Reports.Size = new global::System.Drawing.Size(80, 22);
			this.CHH_Reports.TabIndex = 34;
			this.CHH_Reports.Text = "Reports";
			this.CHH_Reports.UseVisualStyleBackColor = true;
			this.CHH_AUser.AutoSize = true;
			this.CHH_AUser.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 11.25f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 0);
			this.CHH_AUser.ForeColor = global::System.Drawing.Color.Black;
			this.CHH_AUser.Location = new global::System.Drawing.Point(295, 25);
			this.CHH_AUser.Name = "CHH_AUser";
			this.CHH_AUser.Size = new global::System.Drawing.Size(88, 22);
			this.CHH_AUser.TabIndex = 33;
			this.CHH_AUser.Text = "Add User";
			this.CHH_AUser.UseVisualStyleBackColor = true;
			this.CHH_SD.AutoSize = true;
			this.CHH_SD.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 11.25f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 0);
			this.CHH_SD.ForeColor = global::System.Drawing.Color.Black;
			this.CHH_SD.Location = new global::System.Drawing.Point(295, 83);
			this.CHH_SD.Name = "CHH_SD";
			this.CHH_SD.Size = new global::System.Drawing.Size(122, 22);
			this.CHH_SD.TabIndex = 32;
			this.CHH_SD.Text = "Station Details";
			this.CHH_SD.UseVisualStyleBackColor = true;
			this.CHH_ASCode.AutoSize = true;
			this.CHH_ASCode.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 11.25f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 0);
			this.CHH_ASCode.ForeColor = global::System.Drawing.Color.Black;
			this.CHH_ASCode.Location = new global::System.Drawing.Point(40, 83);
			this.CHH_ASCode.Name = "CHH_ASCode";
			this.CHH_ASCode.Size = new global::System.Drawing.Size(142, 22);
			this.CHH_ASCode.TabIndex = 31;
			this.CHH_ASCode.Text = "Add Station Code";
			this.CHH_ASCode.UseVisualStyleBackColor = true;
			this.CHH_TDEntry.AutoSize = true;
			this.CHH_TDEntry.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 11.25f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 0);
			this.CHH_TDEntry.ForeColor = global::System.Drawing.Color.Black;
			this.CHH_TDEntry.Location = new global::System.Drawing.Point(40, 53);
			this.CHH_TDEntry.Name = "CHH_TDEntry";
			this.CHH_TDEntry.Size = new global::System.Drawing.Size(133, 22);
			this.CHH_TDEntry.TabIndex = 30;
			this.CHH_TDEntry.Text = "Train Data Entry";
			this.CHH_TDEntry.UseVisualStyleBackColor = true;
			this.CHH_Adve.AutoSize = true;
			this.CHH_Adve.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 11.25f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 0);
			this.CHH_Adve.ForeColor = global::System.Drawing.Color.Black;
			this.CHH_Adve.Location = new global::System.Drawing.Point(40, 25);
			this.CHH_Adve.Name = "CHH_Adve";
			this.CHH_Adve.Size = new global::System.Drawing.Size(127, 22);
			this.CHH_Adve.TabIndex = 29;
			this.CHH_Adve.Text = "Add Advertising";
			this.CHH_Adve.UseVisualStyleBackColor = true;
			this.TB_Pass.Enabled = false;
			this.TB_Pass.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 11.25f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 0);
			this.TB_Pass.Location = new global::System.Drawing.Point(318, 65);
			this.TB_Pass.MaxLength = 10;
			this.TB_Pass.Name = "TB_Pass";
			this.TB_Pass.Size = new global::System.Drawing.Size(165, 24);
			this.TB_Pass.TabIndex = 2;
			this.TB_Pass.UseSystemPasswordChar = true;
			this.TB_CPass.Enabled = false;
			this.TB_CPass.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 11.25f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 0);
			this.TB_CPass.Location = new global::System.Drawing.Point(318, 93);
			this.TB_CPass.MaxLength = 10;
			this.TB_CPass.Name = "TB_CPass";
			this.TB_CPass.Size = new global::System.Drawing.Size(165, 24);
			this.TB_CPass.TabIndex = 9;
			this.TB_CPass.UseSystemPasswordChar = true;
			this.pictureBox1.BackgroundImage = (global::System.Drawing.Image)componentResourceManager.GetObject("pictureBox1.BackgroundImage");
			this.pictureBox1.Location = new global::System.Drawing.Point(11, 16);
			this.pictureBox1.Name = "pictureBox1";
			this.pictureBox1.Size = new global::System.Drawing.Size(128, 147);
			this.pictureBox1.TabIndex = 10;
			this.pictureBox1.TabStop = false;
			this.pictureBox1.Click += new global::System.EventHandler(this.pictureBox1_Click);
			this.BTN_Exit.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 12f, global::System.Drawing.FontStyle.Bold, global::System.Drawing.GraphicsUnit.Point, 0);
			this.BTN_Exit.ForeColor = global::System.Drawing.Color.FromArgb(0, 0, 192);
			this.BTN_Exit.Image = (global::System.Drawing.Image)componentResourceManager.GetObject("BTN_Exit.Image");
			this.BTN_Exit.ImageAlign = global::System.Drawing.ContentAlignment.MiddleLeft;
			this.BTN_Exit.Location = new global::System.Drawing.Point(389, 306);
			this.BTN_Exit.Name = "BTN_Exit";
			this.BTN_Exit.Size = new global::System.Drawing.Size(94, 46);
			this.BTN_Exit.TabIndex = 51;
			this.BTN_Exit.Text = "EXIT";
			this.BTN_Exit.TextAlign = global::System.Drawing.ContentAlignment.MiddleRight;
			this.BTN_Exit.UseVisualStyleBackColor = true;
			this.BTN_Exit.Click += new global::System.EventHandler(this.button2_Click);
			this.BTN_Save.Enabled = false;
			this.BTN_Save.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 12f, global::System.Drawing.FontStyle.Bold, global::System.Drawing.GraphicsUnit.Point, 0);
			this.BTN_Save.ForeColor = global::System.Drawing.Color.FromArgb(0, 192, 0);
			this.BTN_Save.Image = (global::System.Drawing.Image)componentResourceManager.GetObject("BTN_Save.Image");
			this.BTN_Save.ImageAlign = global::System.Drawing.ContentAlignment.MiddleLeft;
			this.BTN_Save.Location = new global::System.Drawing.Point(200, 306);
			this.BTN_Save.Name = "BTN_Save";
			this.BTN_Save.Size = new global::System.Drawing.Size(94, 46);
			this.BTN_Save.TabIndex = 49;
			this.BTN_Save.Text = "SAVE";
			this.BTN_Save.TextAlign = global::System.Drawing.ContentAlignment.MiddleRight;
			this.BTN_Save.UseVisualStyleBackColor = true;
			this.BTN_Save.Click += new global::System.EventHandler(this.BTN_Save_Click);
			this.BTN_Add.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 12f, global::System.Drawing.FontStyle.Bold, global::System.Drawing.GraphicsUnit.Point, 0);
			this.BTN_Add.ForeColor = global::System.Drawing.Color.FromArgb(0, 0, 192);
			this.BTN_Add.Image = (global::System.Drawing.Image)componentResourceManager.GetObject("BTN_Add.Image");
			this.BTN_Add.ImageAlign = global::System.Drawing.ContentAlignment.MiddleLeft;
			this.BTN_Add.Location = new global::System.Drawing.Point(11, 306);
			this.BTN_Add.Name = "BTN_Add";
			this.BTN_Add.Size = new global::System.Drawing.Size(94, 46);
			this.BTN_Add.TabIndex = 47;
			this.BTN_Add.Text = "ADD";
			this.BTN_Add.TextAlign = global::System.Drawing.ContentAlignment.MiddleRight;
			this.BTN_Add.UseVisualStyleBackColor = true;
			this.BTN_Add.Click += new global::System.EventHandler(this.BTN_Add_Click);
			this.BTN_Del.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 12f, global::System.Drawing.FontStyle.Bold, global::System.Drawing.GraphicsUnit.Point, 0);
			this.BTN_Del.ForeColor = global::System.Drawing.Color.Red;
			this.BTN_Del.Image = (global::System.Drawing.Image)componentResourceManager.GetObject("BTN_Del.Image");
			this.BTN_Del.ImageAlign = global::System.Drawing.ContentAlignment.MiddleLeft;
			this.BTN_Del.Location = new global::System.Drawing.Point(295, 306);
			this.BTN_Del.Name = "BTN_Del";
			this.BTN_Del.Size = new global::System.Drawing.Size(94, 46);
			this.BTN_Del.TabIndex = 52;
			this.BTN_Del.Text = "DEL";
			this.BTN_Del.TextAlign = global::System.Drawing.ContentAlignment.MiddleRight;
			this.BTN_Del.UseVisualStyleBackColor = true;
			this.BTN_Del.Click += new global::System.EventHandler(this.BTN_Del_Click);
			this.label1.AutoSize = true;
			this.label1.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 11.25f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 0);
			this.label1.ForeColor = global::System.Drawing.Color.FromArgb(0, 0, 192);
			this.label1.Location = new global::System.Drawing.Point(183, 123);
			this.label1.Name = "label1";
			this.label1.Size = new global::System.Drawing.Size(105, 18);
			this.label1.TabIndex = 54;
			this.label1.Text = "Password Hint";
			this.label1.Click += new global::System.EventHandler(this.label1_Click);
			this.TB_PassHint.Enabled = false;
			this.TB_PassHint.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 11.25f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 0);
			this.TB_PassHint.Location = new global::System.Drawing.Point(318, 122);
			this.TB_PassHint.MaxLength = 10;
			this.TB_PassHint.Name = "TB_PassHint";
			this.TB_PassHint.Size = new global::System.Drawing.Size(165, 24);
			this.TB_PassHint.TabIndex = 55;
			this.TB_PassHint.TextChanged += new global::System.EventHandler(this.TB_PassHint_TextChanged);
			this.label5.AutoSize = true;
			this.label5.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 11.25f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 0);
			this.label5.ForeColor = global::System.Drawing.Color.FromArgb(0, 0, 192);
			this.label5.Location = new global::System.Drawing.Point(181, 9);
			this.label5.Name = "label5";
			this.label5.Size = new global::System.Drawing.Size(76, 18);
			this.label5.TabIndex = 56;
			this.label5.Text = "User Type";
			this.Cmb_User.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 11.25f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 0);
			this.Cmb_User.FormattingEnabled = true;
			this.Cmb_User.Location = new global::System.Drawing.Point(318, 7);
			this.Cmb_User.Name = "Cmb_User";
			this.Cmb_User.Size = new global::System.Drawing.Size(165, 26);
			this.Cmb_User.TabIndex = 57;
			this.Cmb_User.Text = "New";
			this.Cmb_User.SelectedIndexChanged += new global::System.EventHandler(this.Cmb_User_SelectedIndexChanged);
			this.BTN_Edit.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 12f, global::System.Drawing.FontStyle.Bold, global::System.Drawing.GraphicsUnit.Point, 0);
			this.BTN_Edit.ForeColor = global::System.Drawing.Color.FromArgb(255, 128, 0);
			this.BTN_Edit.Image = (global::System.Drawing.Image)componentResourceManager.GetObject("BTN_Edit.Image");
			this.BTN_Edit.ImageAlign = global::System.Drawing.ContentAlignment.MiddleLeft;
			this.BTN_Edit.Location = new global::System.Drawing.Point(105, 306);
			this.BTN_Edit.Name = "BTN_Edit";
			this.BTN_Edit.Size = new global::System.Drawing.Size(94, 46);
			this.BTN_Edit.TabIndex = 58;
			this.BTN_Edit.Text = "EDIT";
			this.BTN_Edit.TextAlign = global::System.Drawing.ContentAlignment.MiddleRight;
			this.BTN_Edit.UseVisualStyleBackColor = true;
			this.BTN_Edit.Click += new global::System.EventHandler(this.BTN_Edit_Click);
			this.TB_UName.Enabled = false;
			this.TB_UName.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 11.25f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 0);
			this.TB_UName.Location = new global::System.Drawing.Point(318, 37);
			this.TB_UName.MaxLength = 10;
			this.TB_UName.Name = "TB_UName";
			this.TB_UName.Size = new global::System.Drawing.Size(165, 24);
			this.TB_UName.TabIndex = 59;
			this.Dev_Font.Apply += new global::System.EventHandler(this.Dev_Font_Apply);
			this.Lbl_UserType.AutoSize = true;
			this.Lbl_UserType.Enabled = false;
			this.Lbl_UserType.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 11.25f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 0);
			this.Lbl_UserType.ForeColor = global::System.Drawing.Color.FromArgb(0, 0, 192);
			this.Lbl_UserType.Location = new global::System.Drawing.Point(180, 152);
			this.Lbl_UserType.Name = "Lbl_UserType";
			this.Lbl_UserType.Size = new global::System.Drawing.Size(121, 18);
			this.Lbl_UserType.TabIndex = 61;
			this.Lbl_UserType.Text = "Select User Type";
			this.Lbl_UserType.Click += new global::System.EventHandler(this.Lbl_UserType_Click);
			this.Cmb_UserType.Enabled = false;
			this.Cmb_UserType.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 11.25f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 0);
			this.Cmb_UserType.FormattingEnabled = true;
			this.Cmb_UserType.Items.AddRange(new object[]
			{
				"Administrator",
				"Operator",
				"Supervisor",
				"User"
			});
			this.Cmb_UserType.Location = new global::System.Drawing.Point(318, 151);
			this.Cmb_UserType.Name = "Cmb_UserType";
			this.Cmb_UserType.Size = new global::System.Drawing.Size(165, 26);
			this.Cmb_UserType.Sorted = true;
			this.Cmb_UserType.TabIndex = 60;
			this.Cmb_UserType.SelectedIndexChanged += new global::System.EventHandler(this.Cmb_UserType_SelectedIndexChanged);
			base.AutoScaleDimensions = new global::System.Drawing.SizeF(6f, 13f);
			base.AutoScaleMode = global::System.Windows.Forms.AutoScaleMode.Font;
			this.BackColor = global::System.Drawing.Color.FromArgb(255, 192, 128);
			base.ClientSize = new global::System.Drawing.Size(495, 363);
			base.ControlBox = false;
			base.Controls.Add(this.Lbl_UserType);
			base.Controls.Add(this.Cmb_UserType);
			base.Controls.Add(this.TB_UName);
			base.Controls.Add(this.BTN_Edit);
			base.Controls.Add(this.Cmb_User);
			base.Controls.Add(this.label5);
			base.Controls.Add(this.label1);
			base.Controls.Add(this.TB_PassHint);
			base.Controls.Add(this.BTN_Del);
			base.Controls.Add(this.BTN_Add);
			base.Controls.Add(this.BTN_Exit);
			base.Controls.Add(this.BTN_Save);
			base.Controls.Add(this.pictureBox1);
			base.Controls.Add(this.TB_CPass);
			base.Controls.Add(this.TB_Pass);
			base.Controls.Add(this.GB_Sup);
			base.Controls.Add(this.label4);
			base.Controls.Add(this.label3);
			base.Controls.Add(this.label2);
			base.Name = "User";
			base.StartPosition = global::System.Windows.Forms.FormStartPosition.CenterScreen;
			this.Text = "User";
			base.Load += new global::System.EventHandler(this.User_Load);
			this.GB_Sup.ResumeLayout(false);
			this.GB_Sup.PerformLayout();
			((global::System.ComponentModel.ISupportInitialize)this.pictureBox1).EndInit();
			base.ResumeLayout(false);
			base.PerformLayout();
		}

		// Token: 0x040000EF RID: 239
		private global::System.ComponentModel.IContainer components = null;

		// Token: 0x040000F0 RID: 240
		private global::System.Windows.Forms.Label label2;

		// Token: 0x040000F1 RID: 241
		private global::System.Windows.Forms.Label label3;

		// Token: 0x040000F2 RID: 242
		private global::System.Windows.Forms.Label label4;

		// Token: 0x040000F3 RID: 243
		private global::System.Windows.Forms.GroupBox GB_Sup;

		// Token: 0x040000F4 RID: 244
		private global::System.Windows.Forms.CheckBox CHH_SD;

		// Token: 0x040000F5 RID: 245
		private global::System.Windows.Forms.CheckBox CHH_ASCode;

		// Token: 0x040000F6 RID: 246
		private global::System.Windows.Forms.CheckBox CHH_TDEntry;

		// Token: 0x040000F7 RID: 247
		private global::System.Windows.Forms.CheckBox CHH_Adve;

		// Token: 0x040000F8 RID: 248
		private global::System.Windows.Forms.CheckBox CHH_AUser;

		// Token: 0x040000F9 RID: 249
		private global::System.Windows.Forms.TextBox TB_Pass;

		// Token: 0x040000FA RID: 250
		private global::System.Windows.Forms.TextBox TB_CPass;

		// Token: 0x040000FB RID: 251
		private global::System.Windows.Forms.PictureBox pictureBox1;

		// Token: 0x040000FC RID: 252
		private global::System.Windows.Forms.Button BTN_Exit;

		// Token: 0x040000FD RID: 253
		private global::System.Windows.Forms.Button BTN_Save;

		// Token: 0x040000FE RID: 254
		private global::System.Windows.Forms.Button BTN_Add;

		// Token: 0x040000FF RID: 255
		private global::System.Windows.Forms.CheckBox CHH_Reports;

		// Token: 0x04000100 RID: 256
		private global::System.Windows.Forms.Button BTN_Del;

		// Token: 0x04000101 RID: 257
		private global::System.Windows.Forms.Label label1;

		// Token: 0x04000102 RID: 258
		private global::System.Windows.Forms.TextBox TB_PassHint;

		// Token: 0x04000103 RID: 259
		private global::System.Windows.Forms.Label label5;

		// Token: 0x04000104 RID: 260
		private global::System.Windows.Forms.ComboBox Cmb_User;

		// Token: 0x04000105 RID: 261
		private global::System.Windows.Forms.Button BTN_Edit;

		// Token: 0x04000106 RID: 262
		private global::System.Windows.Forms.TextBox TB_UName;

		// Token: 0x04000107 RID: 263
		private global::System.Windows.Forms.FontDialog Dev_Font;

		// Token: 0x04000108 RID: 264
		private global::System.Windows.Forms.Label Lbl_UserType;

		// Token: 0x04000109 RID: 265
		private global::System.Windows.Forms.ComboBox Cmb_UserType;
	}
}
