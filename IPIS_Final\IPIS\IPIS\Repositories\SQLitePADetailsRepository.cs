using System.Data;
using System.Data.SQLite;
using IPIS.Repositories.Interfaces;
using IPIS.Utils;

namespace IPIS.Repositories
{
    public class SQLitePADetailsRepository : IPADetailsRepository
    {
        private readonly string connectionString;

        public SQLitePADetailsRepository()
        {
            connectionString = Database.ConnectionString;
            InitializeDatabase();
        }

        private void InitializeDatabase()
        {
            int maxRetries = 3;
            int retryDelayMs = 1000; // 1 second

            for (int attempt = 1; attempt <= maxRetries; attempt++)
            {
                try
                {
                    using (var connection = new SQLiteConnection(connectionString))
                    {
                        connection.Open();
                        using (var command = new SQLiteCommand(connection))
                        {
                            // Create PA_Details table if it doesn't exist
                            command.CommandText = @"
                                CREATE TABLE IF NOT EXISTS PA_Details (
                                    Part_Type TEXT NOT NULL,
                                    Part_Value TEXT NOT NULL,
                                    Language TEXT NOT NULL,
                                    FilePath TEXT NOT NULL,
                                    PRIMARY KEY (Part_Type, Part_Value, Language)
                                )";
                            command.ExecuteNonQuery();

                            // Check if table is empty, and if so, insert default data
                            command.CommandText = "SELECT COUNT(*) FROM PA_Details";
                            int count = Convert.ToInt32(command.ExecuteScalar());
                            
                            if (count == 0)
                            {
                                // Insert some default data only if table is empty
                                InsertDefaultData(command);
                            }
                        }
                    }
                    
                    // If we get here, initialization was successful
                    return;
                }
                catch (Exception ex)
                {
                    if (attempt == maxRetries)
                    {
                        // Log the error but don't throw - this allows the application to continue
                        Console.WriteLine($"Warning: Failed to initialize PA_Details database after {maxRetries} attempts: {ex.Message}");
                        // Don't throw the exception to prevent application startup failure
                        return;
                    }
                    else
                    {
                        // Wait before retrying
                        Console.WriteLine($"Database initialization attempt {attempt} failed, retrying in {retryDelayMs}ms: {ex.Message}");
                        System.Threading.Thread.Sleep(retryDelayMs);
                        retryDelayMs *= 2; // Exponential backoff
                    }
                }
            }
        }

        private void InsertDefaultData(SQLiteCommand command)
        {
            // Insert default keywords
            InsertKeyword(command, "Keyword", "EFROM", "ENGLISH", "\\WAVE\\SPL\\EFROM.wav");
            InsertKeyword(command, "Keyword", "ETO", "ENGLISH", "\\WAVE\\SPL\\ETO.wav");
            InsertKeyword(command, "Keyword", "PF", "ENGLISH", "\\WAVE\\SPL\\PF.wav");

            // Insert default status messages
            InsertKeyword(command, "Status", "IS ARRIVING ON", "ENGLISH", "\\WAVE\\SPL\\IS_ARRIVING_ON.wav");
            InsertKeyword(command, "Status", "HAS ARRIVED ON", "ENGLISH", "\\WAVE\\SPL\\HAS_ARRIVED_ON.wav");
            InsertKeyword(command, "Status", "WILL ARRIVE SHORTLY", "ENGLISH", "\\WAVE\\SPL\\WILL_ARRIVE_SHORTLY.wav");
            InsertKeyword(command, "Status", "RUNNING RIGHT TIME", "ENGLISH", "\\WAVE\\SPL\\RUNNING_RIGHT_TIME.wav");
            InsertKeyword(command, "Status", "RUNNING LATE", "ENGLISH", "\\WAVE\\SPL\\RUNNING_LATE.wav");
            InsertKeyword(command, "Status", "INDEFINITE LATE", "ENGLISH", "\\WAVE\\SPL\\INDEFINITE_LATE.wav");
            InsertKeyword(command, "Status", "CANCELLED", "ENGLISH", "\\WAVE\\SPL\\CANCELLED.wav");
            InsertKeyword(command, "Status", "PLATFORM CHANGED", "ENGLISH", "\\WAVE\\SPL\\PLATFORM_CHANGED.wav");
            InsertKeyword(command, "Status", "TERMINATED", "ENGLISH", "\\WAVE\\SPL\\TERMINATED.wav");

            // Insert numbers for train numbers and platforms
            for (int i = 0; i <= 9; i++)
            {
                InsertKeyword(command, "Train_No", i.ToString(), "ENGLISH", $"\\WAVE\\SPL\\{i}.wav");
                InsertKeyword(command, "Platform", i.ToString(), "ENGLISH", $"\\WAVE\\SPL\\{i}.wav");
            }
        }

        private void InsertKeyword(SQLiteCommand command, string partType, string partValue, string language, string filePath)
        {
            command.CommandText = @"
                INSERT OR IGNORE INTO PA_Details (Part_Type, Part_Value, Language, FilePath)
                VALUES (@PartType, @PartValue, @Language, @FilePath)";

            command.Parameters.Clear();
            command.Parameters.AddWithValue("@PartType", partType);
            command.Parameters.AddWithValue("@PartValue", partValue);
            command.Parameters.AddWithValue("@Language", language);
            command.Parameters.AddWithValue("@FilePath", filePath);
            command.ExecuteNonQuery();
        }

        public DataTable GetPADetails(string partType, string partValue, string language)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = @"
                    SELECT * FROM PA_Details 
                    WHERE Part_Type = @PartType 
                    AND Part_Value = @PartValue 
                    AND Language = @Language";
                
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@PartType", partType);
                    command.Parameters.AddWithValue("@PartValue", partValue);
                    command.Parameters.AddWithValue("@Language", language);

                    using (var adapter = new SQLiteDataAdapter(command))
                    {
                        var dataTable = new DataTable();
                        adapter.Fill(dataTable);
                        return dataTable;
                    }
                }
            }
        }

        public void AddPADetails(string partType, string partValue, string language, string filePath)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = @"
                    INSERT INTO PA_Details (Part_Type, Part_Value, Language, FilePath)
                    VALUES (@PartType, @PartValue, @Language, @FilePath)";

                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@PartType", partType);
                    command.Parameters.AddWithValue("@PartValue", partValue);
                    command.Parameters.AddWithValue("@Language", language);
                    command.Parameters.AddWithValue("@FilePath", filePath);
                    command.ExecuteNonQuery();
                }
            }
        }

        public void UpdatePADetails(string partType, string partValue, string language, string filePath)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = @"
                    UPDATE PA_Details 
                    SET FilePath = @FilePath
                    WHERE Part_Type = @PartType 
                    AND Part_Value = @PartValue 
                    AND Language = @Language";

                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@PartType", partType);
                    command.Parameters.AddWithValue("@PartValue", partValue);
                    command.Parameters.AddWithValue("@Language", language);
                    command.Parameters.AddWithValue("@FilePath", filePath);
                    command.ExecuteNonQuery();
                }
            }
        }

        public void DeletePADetails(string partType, string partValue, string language)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = @"
                    DELETE FROM PA_Details 
                    WHERE Part_Type = @PartType 
                    AND Part_Value = @PartValue 
                    AND Language = @Language";

                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@PartType", partType);
                    command.Parameters.AddWithValue("@PartValue", partValue);
                    command.Parameters.AddWithValue("@Language", language);
                    command.ExecuteNonQuery();
                }
            }
        }
    }
} 