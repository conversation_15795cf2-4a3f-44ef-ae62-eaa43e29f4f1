// Decompiled with JetBrains decompiler
// Type: ipis.frmAddNewUser
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using ipis.My;
using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

namespace ipis
{

[DesignerGenerated]
public class frmAddNewUser : Form
{
  private static List<WeakReference> __ENCList = new List<WeakReference>();
  private IContainer components;
  [AccessedThroughProperty("btnOk")]
  private Button _btnOk;
  [AccessedThroughProperty("btnExit")]
  private Button _btnExit;
  [AccessedThroughProperty("lblName")]
  private Label _lblName;
  [AccessedThroughProperty("lblPwd")]
  private Label _lblPwd;
  [AccessedThroughProperty("txtName")]
  private TextBox _txtName;
  [AccessedThroughProperty("txtPwd")]
  private TextBox _txtPwd;
  [AccessedThroughProperty("txtUid")]
  private TextBox _txtUid;
  [AccessedThroughProperty("lblUid")]
  private Label _lblUid;
  [AccessedThroughProperty("lblGroup")]
  private Label _lblGroup;
  [AccessedThroughProperty("cmbGroup")]
  private ComboBox _cmbGroup;

  [DebuggerNonUserCode]
  static frmAddNewUser()
  {
  }

  [DebuggerNonUserCode]
  public frmAddNewUser()
  {
    frmAddNewUser.__ENCAddToList((object) this);
    this.InitializeComponent();
  }

  [DebuggerNonUserCode]
  private static void __ENCAddToList(object value)
  {
    lock (frmAddNewUser.__ENCList)
    {
      if (frmAddNewUser.__ENCList.Count == frmAddNewUser.__ENCList.Capacity)
      {
        int index1 = 0;
        int num = checked (frmAddNewUser.__ENCList.Count - 1);
        int index2 = 0;
        while (index2 <= num)
        {
          if (frmAddNewUser.__ENCList[index2].IsAlive)
          {
            if (index2 != index1)
              frmAddNewUser.__ENCList[index1] = frmAddNewUser.__ENCList[index2];
            checked { ++index1; }
          }
          checked { ++index2; }
        }
        frmAddNewUser.__ENCList.RemoveRange(index1, checked (frmAddNewUser.__ENCList.Count - index1));
        frmAddNewUser.__ENCList.Capacity = frmAddNewUser.__ENCList.Count;
      }
      frmAddNewUser.__ENCList.Add(new WeakReference(RuntimeHelpers.GetObjectValue(value)));
    }
  }

  [DebuggerNonUserCode]
  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  [DebuggerStepThrough]
  private void InitializeComponent()
  {
    this.btnOk = new Button();
    this.btnExit = new Button();
    this.lblName = new Label();
    this.lblPwd = new Label();
    this.txtName = new TextBox();
    this.txtPwd = new TextBox();
    this.txtUid = new TextBox();
    this.lblUid = new Label();
    this.lblGroup = new Label();
    this.cmbGroup = new ComboBox();
    this.SuspendLayout();
    this.btnOk.BackColor = Color.SeaShell;
    this.btnOk.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnOk1 = this.btnOk;
    Point point1 = new Point(87, 203);
    Point point2 = point1;
    btnOk1.Location = point2;
    this.btnOk.Name = "btnOk";
    Button btnOk2 = this.btnOk;
    Size size1 = new Size(53, 23);
    Size size2 = size1;
    btnOk2.Size = size2;
    this.btnOk.TabIndex = 5;
    this.btnOk.Text = "Ok";
    this.btnOk.UseVisualStyleBackColor = false;
    this.btnExit.BackColor = Color.SeaShell;
    this.btnExit.DialogResult = DialogResult.Cancel;
    this.btnExit.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnExit1 = this.btnExit;
    point1 = new Point(162, 203);
    Point point3 = point1;
    btnExit1.Location = point3;
    this.btnExit.Name = "btnExit";
    Button btnExit2 = this.btnExit;
    size1 = new Size(54, 23);
    Size size3 = size1;
    btnExit2.Size = size3;
    this.btnExit.TabIndex = 6;
    this.btnExit.Text = "Exit";
    this.btnExit.UseVisualStyleBackColor = false;
    this.lblName.AutoSize = true;
    this.lblName.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblName1 = this.lblName;
    point1 = new Point(48 /*0x30*/, 67);
    Point point4 = point1;
    lblName1.Location = point4;
    this.lblName.Name = "lblName";
    Label lblName2 = this.lblName;
    size1 = new Size(49, 16 /*0x10*/);
    Size size4 = size1;
    lblName2.Size = size4;
    this.lblName.TabIndex = 2;
    this.lblName.Text = "Name";
    this.lblPwd.AutoSize = true;
    this.lblPwd.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblPwd1 = this.lblPwd;
    point1 = new Point(21, 106);
    Point point5 = point1;
    lblPwd1.Location = point5;
    this.lblPwd.Name = "lblPwd";
    Label lblPwd2 = this.lblPwd;
    size1 = new Size(76, 16 /*0x10*/);
    Size size5 = size1;
    lblPwd2.Size = size5;
    this.lblPwd.TabIndex = 3;
    this.lblPwd.Text = "Password";
    this.txtName.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtName1 = this.txtName;
    point1 = new Point(129, 64 /*0x40*/);
    Point point6 = point1;
    txtName1.Location = point6;
    this.txtName.MaxLength = 15;
    this.txtName.Name = "txtName";
    TextBox txtName2 = this.txtName;
    size1 = new Size(148, 22);
    Size size6 = size1;
    txtName2.Size = size6;
    this.txtName.TabIndex = 2;
    this.txtPwd.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtPwd1 = this.txtPwd;
    point1 = new Point(129, 103);
    Point point7 = point1;
    txtPwd1.Location = point7;
    this.txtPwd.MaxLength = 15;
    this.txtPwd.Name = "txtPwd";
    this.txtPwd.PasswordChar = '*';
    TextBox txtPwd2 = this.txtPwd;
    size1 = new Size(148, 22);
    Size size7 = size1;
    txtPwd2.Size = size7;
    this.txtPwd.TabIndex = 3;
    this.txtPwd.UseSystemPasswordChar = true;
    this.txtUid.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtUid1 = this.txtUid;
    point1 = new Point(129, 23);
    Point point8 = point1;
    txtUid1.Location = point8;
    this.txtUid.MaxLength = 15;
    this.txtUid.Name = "txtUid";
    TextBox txtUid2 = this.txtUid;
    size1 = new Size(87, 22);
    Size size8 = size1;
    txtUid2.Size = size8;
    this.txtUid.TabIndex = 1;
    this.lblUid.AutoSize = true;
    this.lblUid.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblUid1 = this.lblUid;
    point1 = new Point(37, 26);
    Point point9 = point1;
    lblUid1.Location = point9;
    this.lblUid.Name = "lblUid";
    Label lblUid2 = this.lblUid;
    size1 = new Size(60, 16 /*0x10*/);
    Size size9 = size1;
    lblUid2.Size = size9;
    this.lblUid.TabIndex = 6;
    this.lblUid.Text = "User ID";
    this.lblGroup.AutoSize = true;
    this.lblGroup.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblGroup1 = this.lblGroup;
    point1 = new Point(47, 141);
    Point point10 = point1;
    lblGroup1.Location = point10;
    this.lblGroup.Name = "lblGroup";
    Label lblGroup2 = this.lblGroup;
    size1 = new Size(50, 16 /*0x10*/);
    Size size10 = size1;
    lblGroup2.Size = size10;
    this.lblGroup.TabIndex = 7;
    this.lblGroup.Text = "Group";
    this.cmbGroup.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.cmbGroup.FormattingEnabled = true;
    this.cmbGroup.Items.AddRange(new object[2]
    {
      (object) "Admin",
      (object) "Normal"
    });
    ComboBox cmbGroup1 = this.cmbGroup;
    point1 = new Point(129, 138);
    Point point11 = point1;
    cmbGroup1.Location = point11;
    this.cmbGroup.Name = "cmbGroup";
    ComboBox cmbGroup2 = this.cmbGroup;
    size1 = new Size(121, 24);
    Size size11 = size1;
    cmbGroup2.Size = size11;
    this.cmbGroup.TabIndex = 4;
    this.AcceptButton = (IButtonControl) this.btnOk;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.BackColor = Color.PowderBlue;
    this.CancelButton = (IButtonControl) this.btnExit;
    size1 = new Size(336, 238);
    this.ClientSize = size1;
    this.Controls.Add((Control) this.cmbGroup);
    this.Controls.Add((Control) this.lblGroup);
    this.Controls.Add((Control) this.txtUid);
    this.Controls.Add((Control) this.lblUid);
    this.Controls.Add((Control) this.txtPwd);
    this.Controls.Add((Control) this.txtName);
    this.Controls.Add((Control) this.lblPwd);
    this.Controls.Add((Control) this.lblName);
    this.Controls.Add((Control) this.btnExit);
    this.Controls.Add((Control) this.btnOk);
    this.FormBorderStyle = FormBorderStyle.FixedToolWindow;
    this.Name = "frmAddNewUser";
    this.Text = "New User";
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  internal virtual Button btnOk
  {
    [DebuggerNonUserCode] get { return this._btnOk; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnOk_Click);
      if (this._btnOk != null)
        this._btnOk.Click -= eventHandler;
      this._btnOk = value;
      if (this._btnOk == null)
        return;
      this._btnOk.Click += eventHandler;
    }
  }

  internal virtual Button btnExit
  {
    [DebuggerNonUserCode] get { return this._btnExit; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnExit_Click);
      if (this._btnExit != null)
        this._btnExit.Click -= eventHandler;
      this._btnExit = value;
      if (this._btnExit == null)
        return;
      this._btnExit.Click += eventHandler;
    }
  }

  internal virtual Label lblName
  {
    [DebuggerNonUserCode] get { return this._lblName; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._lblName = value; }
  }

  internal virtual Label lblPwd
  {
    [DebuggerNonUserCode] get { return this._lblPwd; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._lblPwd = value; }
  }

  internal virtual TextBox txtName
  {
    [DebuggerNonUserCode] get { return this._txtName; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._txtName = value; }
  }

  internal virtual TextBox txtPwd
  {
    [DebuggerNonUserCode] get { return this._txtPwd; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._txtPwd = value; }
  }

  internal virtual TextBox txtUid
  {
    [DebuggerNonUserCode] get { return this._txtUid; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._txtUid = value; }
  }

  internal virtual Label lblUid
  {
    [DebuggerNonUserCode] get { return this._lblUid; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._lblUid = value; }
  }

  internal virtual Label lblGroup
  {
    [DebuggerNonUserCode] get { return this._lblGroup; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._lblGroup = value; }
  }

  internal virtual ComboBox cmbGroup
  {
    [DebuggerNonUserCode] get { return this._cmbGroup; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbGroup = value; }
  }

  private void btnOk_Click(object sender, EventArgs e)
  {
    int index = 0;
    while (index < (int) frmMainFormIPIS.user_cnt.cnt)
    {
      if (Operators.CompareString(this.txtName.Text, frmMainFormIPIS.user_details[index].user_name, false) == 0)
      {
        int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, " User Name Already Exists, Please Select another name", "Msg Box", 0, 0, 0);
        this.txtName.Text = string.Empty;
        return;
      }
      checked { ++index; }
    }
    network_db_read.set_new_user(this.txtUid.Text, this.txtName.Text, this.txtPwd.Text, this.txtPwd.Text.Length, this.cmbGroup.Text);
    this.Close();
    try
    {
      string str = "Z:\\Database\\logindb.mdb";
      string sourceFileName = "C:\\IPIS\\Database\\logindb.mdb";
      if (!File.Exists(str))
        File.Create(str);
      bool overwrite = true;
      MyProject.Computer.FileSystem.CopyFile(sourceFileName, str, overwrite);
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
  }

  private void btnExit_Click(object sender, EventArgs e)
{
  this.Close();
}
}

}