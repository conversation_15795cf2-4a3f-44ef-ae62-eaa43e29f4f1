﻿using System;
using System.Data;
using System.Windows.Forms;

namespace Announcement
{
	// Token: 0x0200000A RID: 10
	internal class Online_Trains
	{
		// Token: 0x06000042 RID: 66 RVA: 0x0000A0C4 File Offset: 0x000082C4
		public DataTable Load_Trains()
		{
			Class_Database class_Database = new Class_Database();
			DataTable dataTable = new DataTable();
			dataTable = new DataTable();
			dataTable = class_Database.Read_Database("Select Train_No,Train_NameEng,Train_AD,Train_Status,Sch_AT,Sch_DT,Late,Exp_AT,Exp_DT,Sch_PF,AN,Div_City From Online_Trains ORDER by Exp_DT");
			bool flag = dataTable.Rows.Count == 0;
			if (flag)
			{
				dataTable = new DataTable();
				string str = DateTime.Today.DayOfWeek.ToString().Substring(0, 3);
				string text = "Chk_" + str;
				int hour = DateTime.Now.Hour;
				int minute = DateTime.Now.Minute;
				int num = Main.AutoLoad_Interval / 60;
				int num2 = Main.AutoLoad_Interval % 60;
				int num3 = hour + num;
				int num4 = minute + num2;
				bool flag2 = num4 > 60;
				if (flag2)
				{
					num3++;
					num4 -= 60;
				}
				bool flag3 = num3 > 23;
				if (flag3)
				{
					num3 = 23;
					num4 = 59;
				}
				string text2 = hour.ToString("00:") + minute.ToString("00");
				string text3 = num3.ToString("00:") + num4.ToString("00");
				dataTable = class_Database.Read_Database(string.Concat(new string[]
				{
					"Select Train_No,Train_NameEng,Train_AD,Sch_AT,Sch_DT,Sch_PF From Train_Data WHERE (All_Days = 'True' OR ",
					text,
					" = 'True') AND (Sch_DT BETWEEN '",
					text2,
					"' AND '",
					text3,
					"') ORDER BY Sch_DT"
				}));
				dataTable.Columns.Add("Sl_No");
				dataTable.Columns.Add("Train_Status");
				dataTable.Columns.Add("Late");
				dataTable.Columns.Add("Exp_AT");
				dataTable.Columns.Add("Exp_DT");
				dataTable.Columns.Add("AN");
				dataTable.Columns.Add("Div_City");
				dataTable.Columns.Add("Del_Train");
				for (int i = 0; i < dataTable.Rows.Count; i++)
				{
					dataTable.Rows[i]["Train_Status"] = "RUNNING RIGHT TIME";
					dataTable.Rows[i]["Exp_AT"] = dataTable.Rows[i]["Sch_AT"];
					dataTable.Rows[i]["Exp_DT"] = dataTable.Rows[i]["Sch_DT"];
				}
			}
			else
			{
				dataTable.Columns.Add("Sl_No");
				dataTable.Columns.Add("Del_Train");
				for (int j = 0; j < dataTable.Rows.Count; j++)
				{
					bool flag4 = dataTable.Rows[j]["AN"].ToString() != "True";
					if (flag4)
					{
						dataTable.Rows[j]["AN"] = "False";
					}
					bool flag5 = dataTable.Rows[j]["Train_AD"].ToString() == "Both";
					if (flag5)
					{
						dataTable.Rows[j]["Train_AD"] = "A";
					}
				}
			}
			Main.Online_TrainsGV = new DataTable();
			dataTable.Columns["Sl_No"].SetOrdinal(0);
			dataTable.Columns["Train_No"].SetOrdinal(1);
			dataTable.Columns["Train_NameEng"].SetOrdinal(2);
			dataTable.Columns["Train_AD"].SetOrdinal(3);
			dataTable.Columns["Train_Status"].SetOrdinal(4);
			dataTable.Columns["Sch_AT"].SetOrdinal(5);
			dataTable.Columns["Sch_DT"].SetOrdinal(6);
			dataTable.Columns["Late"].SetOrdinal(7);
			dataTable.Columns["Exp_AT"].SetOrdinal(8);
			dataTable.Columns["Exp_DT"].SetOrdinal(9);
			dataTable.Columns["Sch_PF"].SetOrdinal(10);
			dataTable.Columns["AN"].SetOrdinal(11);
			dataTable.Columns["Div_City"].SetOrdinal(12);
			dataTable.Columns["Del_Train"].SetOrdinal(13);
			Main.Online_TrainsGV = dataTable;
			return Main.Online_TrainsGV;
		}

		// Token: 0x06000043 RID: 67 RVA: 0x0000A5A4 File Offset: 0x000087A4
		public void AutoLoad_Trains()
		{
			Class_Database class_Database = new Class_Database();
			DataTable dataTable = new DataTable();
			DataTable dataTable2 = new DataTable();
			dataTable2 = new DataTable();
			int hour = DateTime.Now.Hour;
			int minute = DateTime.Now.Minute;
			int num = Main.AutoLoad_Interval / 60;
			int num2 = Main.AutoLoad_Interval % 60;
			bool flag_AutoLoad = Main.Flag_AutoLoad;
			if (flag_AutoLoad)
			{
				int num3 = hour + num;
				int num4 = minute + num2;
				bool flag = num4 > 59;
				if (flag)
				{
					num3++;
					num4 -= 60;
				}
				bool flag2 = num3 > 23;
				if (flag2)
				{
					num3 -= 24;
				}
				string text = hour.ToString("00:") + minute.ToString("00");
				string text2 = num3.ToString("00:") + num4.ToString("00");
				string str = DateTime.Today.DayOfWeek.ToString().Substring(0, 3);
				string text3 = "Chk_" + str;
				DateTime t = Convert.ToDateTime(text);
				DateTime t2 = Convert.ToDateTime(text2);
				bool flag3 = t > t2;
				if (flag3)
				{
					DataTable dataTable3 = new DataTable();
					dataTable2 = class_Database.Read_Database(string.Concat(new string[]
					{
						"Select Train_No,Train_NameEng,Train_AD,Sch_AT,Sch_DT,Sch_PF From Train_Data WHERE (All_Days = 'True' OR ",
						text3,
						" = 'True') AND (Sch_DT BETWEEN '",
						text,
						"' AND '23:59') ORDER BY Sch_DT"
					}));
					dataTable2.PrimaryKey = new DataColumn[]
					{
						dataTable2.Columns["Train_No"]
					};
					dataTable2.Merge(class_Database.Read_Database(string.Concat(new string[]
					{
						"Select Train_No,Train_NameEng,Train_AD,Sch_AT,Sch_DT,Sch_PF From Train_Data WHERE (All_Days = 'True' OR ",
						text3,
						" = 'True') AND (Sch_AT BETWEEN '",
						text,
						"' AND '23:59') ORDER BY Sch_AT"
					})));
					str = DateTime.Today.AddDays(1.0).DayOfWeek.ToString().Substring(0, 3);
					text3 = "Chk_" + str;
					dataTable3 = class_Database.Read_Database(string.Concat(new string[]
					{
						"Select Train_No,Train_NameEng,Train_AD,Sch_AT,Sch_DT,Sch_PF From Train_Data WHERE (All_Days = 'True' OR ",
						text3,
						" = 'True') AND (Sch_DT BETWEEN '00:01' AND '",
						text2,
						"') ORDER BY Sch_DT"
					}));
					dataTable3.PrimaryKey = new DataColumn[]
					{
						dataTable3.Columns["Train_No"]
					};
					dataTable3.Merge(class_Database.Read_Database(string.Concat(new string[]
					{
						"Select Train_No,Train_NameEng,Train_AD,Sch_AT,Sch_DT,Sch_PF From Train_Data WHERE (All_Days = 'True' OR ",
						text3,
						" = 'True') AND (Sch_AT BETWEEN '00:01' AND '",
						text2,
						"') ORDER BY Sch_AT"
					})));
					dataTable2.Merge(dataTable3);
				}
				else
				{
					dataTable2 = class_Database.Read_Database(string.Concat(new string[]
					{
						"Select Train_No,Train_NameEng,Train_AD,Sch_AT,Sch_DT,Sch_PF From Train_Data WHERE (All_Days = 'True' OR ",
						text3,
						" = 'True') AND (Sch_DT BETWEEN '",
						text,
						"' AND '",
						text2,
						"') ORDER BY Sch_DT"
					}));
					dataTable2.PrimaryKey = new DataColumn[]
					{
						dataTable2.Columns["Train_No"]
					};
					dataTable2.Merge(class_Database.Read_Database(string.Concat(new string[]
					{
						"Select Train_No,Train_NameEng,Train_AD,Sch_AT,Sch_DT,Sch_PF From Train_Data WHERE (All_Days = 'True' OR ",
						text3,
						" = 'True') AND (Sch_AT BETWEEN '",
						text,
						"' AND '",
						text2,
						"') ORDER BY Sch_AT"
					})));
				}
				dataTable2.Columns.Add("Sl_No");
				dataTable2.Columns.Add("Train_Status");
				dataTable2.Columns.Add("Late");
				dataTable2.Columns.Add("Exp_AT");
				dataTable2.Columns.Add("Exp_DT");
				dataTable2.Columns.Add("AN");
				dataTable2.Columns.Add("Div_City");
				dataTable2.Columns.Add("Del_Train");
				for (int i = 0; i < dataTable2.Rows.Count; i++)
				{
					dataTable2.Rows[i]["Train_Status"] = "RUNNING RIGHT TIME";
					dataTable2.Rows[i]["Exp_AT"] = dataTable2.Rows[i]["Sch_AT"];
					dataTable2.Rows[i]["Exp_DT"] = dataTable2.Rows[i]["Sch_DT"];
					bool flag4 = dataTable2.Rows[i]["Train_AD"].ToString() == "Both";
					if (flag4)
					{
						dataTable2.Rows[i]["Train_AD"] = "A";
					}
				}
				dataTable2.Columns["Sl_No"].SetOrdinal(0);
				dataTable2.Columns["Train_No"].SetOrdinal(1);
				dataTable2.Columns["Train_NameEng"].SetOrdinal(2);
				dataTable2.Columns["Train_AD"].SetOrdinal(3);
				dataTable2.Columns["Train_Status"].SetOrdinal(4);
				dataTable2.Columns["Sch_AT"].SetOrdinal(5);
				dataTable2.Columns["Sch_DT"].SetOrdinal(6);
				dataTable2.Columns["Late"].SetOrdinal(7);
				dataTable2.Columns["Exp_AT"].SetOrdinal(8);
				dataTable2.Columns["Exp_DT"].SetOrdinal(9);
				dataTable2.Columns["Sch_PF"].SetOrdinal(10);
				dataTable2.Columns["AN"].SetOrdinal(11);
				dataTable2.Columns["Div_City"].SetOrdinal(12);
				dataTable2.Columns["Del_Train"].SetOrdinal(13);
				foreach (object obj in dataTable2.Rows)
				{
					DataRow dataRow = (DataRow)obj;
					string str2 = dataRow["Train_No"].ToString();
					DataRow[] array = Main.Online_TrainsGV.Select("Train_No = '" + str2 + "'");
					bool flag5 = array.Length == 0;
					if (flag5)
					{
						DataTable dataTable4 = class_Database.Read_Database("SELECT Train_No FROM Train_Data WHERE Merged_Train = '" + str2 + "'");
						bool flag6 = dataTable4.Rows.Count > 0;
						if (flag6)
						{
							string str3 = dataTable4.Rows[0][0].ToString();
							array = Main.Online_TrainsGV.Select("Train_No = '" + str3 + "'");
						}
						bool flag7 = array.Length == 0;
						if (flag7)
						{
							Main.Online_TrainsGV.ImportRow(dataRow);
						}
					}
				}
			}
		}

		// Token: 0x06000044 RID: 68 RVA: 0x0000ACDC File Offset: 0x00008EDC
		public void Load_NewTrain()
		{
			Class_Database class_Database = new Class_Database();
			DataTable dataTable = new DataTable();
			bool flag = Main.SelectedTrain == "";
			if (flag)
			{
				TrainData trainData = new TrainData();
				trainData.ShowDialog();
			}
			DataRow[] array = Main.Online_TrainsGV.Select("Train_No = '" + Main.SelectedTrain + "'");
			Main.Flag_TrainExsist = false;
			bool flag2 = array.Length != 0;
			if (flag2)
			{
				MessageBox.Show("Train Already Exsist in grid");
				Main.Flag_TrainExsist = true;
			}
			else
			{
				DataTable dataTable2 = class_Database.Read_Database("SELECT Train_No FROM Train_Details WHERE Merged_Train = '" + Main.SelectedTrain + "'");
				bool flag3 = dataTable2.Rows.Count > 0;
				if (flag3)
				{
					string text = dataTable2.Rows[0][0].ToString();
					array = Main.Online_TrainsGV.Select("Train_No = '" + text + "'");
					bool flag4 = array.Length != 0;
					if (flag4)
					{
						MessageBox.Show(string.Concat(new string[]
						{
							"Merged Train  ",
							Main.SelectedTrain,
							" Or ",
							text,
							" Already Exsist in grid"
						}));
						Main.Flag_TrainExsist = true;
						return;
					}
				}
				dataTable = class_Database.Read_Database("Select Train_No,Train_NameEng,Train_AD,Sch_AT,Sch_DT,Sch_PF From Train_Data WHERE Train_No = '" + Main.SelectedTrain + "'");
				bool flag5 = dataTable.Rows.Count > 0;
				if (flag5)
				{
					dataTable.Columns.Add("Sl_No");
					dataTable.Columns.Add("Train_Status");
					dataTable.Columns.Add("Late");
					dataTable.Columns.Add("Exp_AT");
					dataTable.Columns.Add("Exp_DT");
					dataTable.Columns.Add("AN");
					dataTable.Columns.Add("Div_City");
					dataTable.Columns.Add("Del_Train");
					dataTable.Rows[0]["Exp_AT"] = dataTable.Rows[0]["Sch_AT"];
					dataTable.Rows[0]["Exp_DT"] = dataTable.Rows[0]["Sch_DT"];
					dataTable.Rows[0]["Late"] = "";
					Main.Online_TrainsGV.Rows.Add(new object[]
					{
						Main.Online_TrainsGV.Rows.Count + 1,
						Main.SelectedTrain,
						dataTable.Rows[0]["Train_NameEng"],
						dataTable.Rows[0]["Train_AD"],
						"RUNNING RIGHT TIME",
						dataTable.Rows[0]["Sch_AT"],
						dataTable.Rows[0]["Sch_DT"],
						"",
						dataTable.Rows[0]["Exp_AT"],
						dataTable.Rows[0]["Exp_DT"],
						dataTable.Rows[0]["Sch_PF"],
						"False",
						dataTable.Rows[0]["Div_City"],
						dataTable.Rows[0]["Del_Train"]
					});
				}
				else
				{
					bool flag6 = !Main.Flag_LoadCancel;
					if (flag6)
					{
						MessageBox.Show("The Selected Train " + Main.SelectedTrain + " Does not Exsist");
					}
					Main.Flag_LoadCancel = false;
				}
				Main.Flag_TrainAdded = true;
				Main.Flag_Update_DGVOT = true;
			}
		}
	}
}
