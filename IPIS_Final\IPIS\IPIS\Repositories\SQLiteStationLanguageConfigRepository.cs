using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SQLite;
using System.Threading.Tasks;
using IPIS.Models;
using IPIS.Repositories.Interfaces;
using IPIS.Utils;

namespace IPIS.Repositories
{
    public class SQLiteStationLanguageConfigRepository : IStationLanguageConfigRepository
    {
        private readonly string _connectionString;

        public SQLiteStationLanguageConfigRepository()
        {
            _connectionString = Database.ConnectionString;
        }

        public async Task<List<StationLanguageConfig>> GetByStationNameAsync(string stationName)
        {
            var configs = new List<StationLanguageConfig>();
            
            using (var connection = new SQLiteConnection(_connectionString))
            {
                await connection.OpenAsync();
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = @"
                        SELECT slc.*, l.Name as LanguageName, l.NativeName as LanguageNativeName
                        FROM Station_Language_Config slc
                        LEFT JOIN Languages l ON slc.Language_Code = l.Code
                        WHERE slc.Station_Name = @StationName
                        ORDER BY l.Name";
                    
                    command.Parameters.AddWithValue("@StationName", stationName);
                    
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            configs.Add(MapReaderToStationLanguageConfig(reader));
                        }
                    }
                }
            }
            
            return configs;
        }

        public async Task<StationLanguageConfig> GetByStationAndLanguageAsync(string stationName, string languageCode)
        {
            using (var connection = new SQLiteConnection(_connectionString))
            {
                await connection.OpenAsync();
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = @"
                        SELECT slc.*, l.Name as LanguageName, l.NativeName as LanguageNativeName
                        FROM Station_Language_Config slc
                        LEFT JOIN Languages l ON slc.Language_Code = l.Code
                        WHERE slc.Station_Name = @StationName AND slc.Language_Code = @LanguageCode";
                    
                    command.Parameters.AddWithValue("@StationName", stationName);
                    command.Parameters.AddWithValue("@LanguageCode", languageCode);
                    
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        if (await reader.ReadAsync())
                        {
                            return MapReaderToStationLanguageConfig(reader);
                        }
                    }
                }
            }
            
            return null;
        }

        public async Task<List<StationLanguageConfig>> GetEnabledByStationAsync(string stationName)
        {
            var configs = new List<StationLanguageConfig>();
            
            using (var connection = new SQLiteConnection(_connectionString))
            {
                await connection.OpenAsync();
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = @"
                        SELECT slc.*, l.Name as LanguageName, l.NativeName as LanguageNativeName
                        FROM Station_Language_Config slc
                        LEFT JOIN Languages l ON slc.Language_Code = l.Code
                        WHERE slc.Station_Name = @StationName AND slc.Is_Enabled = 1
                        ORDER BY l.Name";
                    
                    command.Parameters.AddWithValue("@StationName", stationName);
                    
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            configs.Add(MapReaderToStationLanguageConfig(reader));
                        }
                    }
                }
            }
            
            return configs;
        }

        public async Task<int> AddAsync(StationLanguageConfig config)
        {
            using (var connection = new SQLiteConnection(_connectionString))
            {
                await connection.OpenAsync();
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = @"
                        INSERT INTO Station_Language_Config (Station_Name, Language_Code, Is_Enabled, Wave_File_Path, Created_At)
                        VALUES (@StationName, @LanguageCode, @IsEnabled, @WaveFilePath, @CreatedAt);
                        SELECT last_insert_rowid();";
                    
                    command.Parameters.AddWithValue("@StationName", config.StationName);
                    command.Parameters.AddWithValue("@LanguageCode", config.LanguageCode);
                    command.Parameters.AddWithValue("@IsEnabled", config.IsEnabled ? 1 : 0);
                    command.Parameters.AddWithValue("@WaveFilePath", config.WaveFilePath ?? "");
                    command.Parameters.AddWithValue("@CreatedAt", config.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss"));
                    
                    return Convert.ToInt32(await command.ExecuteScalarAsync());
                }
            }
        }

        public async Task<bool> UpdateAsync(StationLanguageConfig config)
        {
            using (var connection = new SQLiteConnection(_connectionString))
            {
                await connection.OpenAsync();
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = @"
                        UPDATE Station_Language_Config 
                        SET Is_Enabled = @IsEnabled, Wave_File_Path = @WaveFilePath, Updated_At = @UpdatedAt
                        WHERE Station_Name = @StationName AND Language_Code = @LanguageCode";
                    
                    command.Parameters.AddWithValue("@StationName", config.StationName);
                    command.Parameters.AddWithValue("@LanguageCode", config.LanguageCode);
                    command.Parameters.AddWithValue("@IsEnabled", config.IsEnabled ? 1 : 0);
                    command.Parameters.AddWithValue("@WaveFilePath", config.WaveFilePath ?? "");
                    command.Parameters.AddWithValue("@UpdatedAt", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                    
                    return await command.ExecuteNonQueryAsync() > 0;
                }
            }
        }

        public async Task<bool> DeleteAsync(int id)
        {
            using (var connection = new SQLiteConnection(_connectionString))
            {
                await connection.OpenAsync();
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = "DELETE FROM Station_Language_Config WHERE Id = @Id";
                    command.Parameters.AddWithValue("@Id", id);
                    
                    return await command.ExecuteNonQueryAsync() > 0;
                }
            }
        }

        public async Task<bool> DeleteByStationAndLanguageAsync(string stationName, string languageCode)
        {
            using (var connection = new SQLiteConnection(_connectionString))
            {
                await connection.OpenAsync();
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = "DELETE FROM Station_Language_Config WHERE Station_Name = @StationName AND Language_Code = @LanguageCode";
                    command.Parameters.AddWithValue("@StationName", stationName);
                    command.Parameters.AddWithValue("@LanguageCode", languageCode);
                    
                    return await command.ExecuteNonQueryAsync() > 0;
                }
            }
        }

        public async Task<bool> ExistsAsync(string stationName, string languageCode)
        {
            using (var connection = new SQLiteConnection(_connectionString))
            {
                await connection.OpenAsync();
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = "SELECT COUNT(*) FROM Station_Language_Config WHERE Station_Name = @StationName AND Language_Code = @LanguageCode";
                    command.Parameters.AddWithValue("@StationName", stationName);
                    command.Parameters.AddWithValue("@LanguageCode", languageCode);
                    
                    var count = Convert.ToInt32(await command.ExecuteScalarAsync());
                    return count > 0;
                }
            }
        }

        public async Task<List<string>> GetEnabledLanguageCodesAsync(string stationName)
        {
            var languageCodes = new List<string>();
            
            using (var connection = new SQLiteConnection(_connectionString))
            {
                await connection.OpenAsync();
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = "SELECT Language_Code FROM Station_Language_Config WHERE Station_Name = @StationName AND Is_Enabled = 1";
                    command.Parameters.AddWithValue("@StationName", stationName);
                    
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            languageCodes.Add(reader["Language_Code"].ToString());
                        }
                    }
                }
            }
            
            return languageCodes;
        }

        public async Task<Dictionary<string, string>> GetWaveFilePathsAsync(string stationName)
        {
            var waveFilePaths = new Dictionary<string, string>();
            
            using (var connection = new SQLiteConnection(_connectionString))
            {
                await connection.OpenAsync();
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = "SELECT Language_Code, Wave_File_Path FROM Station_Language_Config WHERE Station_Name = @StationName AND Is_Enabled = 1";
                    command.Parameters.AddWithValue("@StationName", stationName);
                    
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            var languageCode = reader["Language_Code"].ToString();
                            var waveFilePath = reader["Wave_File_Path"].ToString();
                            if (!string.IsNullOrEmpty(waveFilePath))
                            {
                                waveFilePaths[languageCode] = waveFilePath;
                            }
                        }
                    }
                }
            }
            
            return waveFilePaths;
        }

        private StationLanguageConfig MapReaderToStationLanguageConfig(System.Data.Common.DbDataReader reader)
        {
            return new StationLanguageConfig
            {
                Id = Convert.ToInt32(reader["Id"]),
                StationName = reader["Station_Name"].ToString(),
                LanguageCode = reader["Language_Code"].ToString(),
                IsEnabled = Convert.ToBoolean(reader["Is_Enabled"]),
                WaveFilePath = reader["Wave_File_Path"].ToString(),
                CreatedAt = DateTime.Parse(reader["Created_At"].ToString()),
                UpdatedAt = reader["Updated_At"] != DBNull.Value ? DateTime.Parse(reader["Updated_At"].ToString()) : null,
                Language = new Language
                {
                    Code = reader["Language_Code"].ToString(),
                    Name = reader["LanguageName"]?.ToString() ?? "",
                    NativeName = reader["LanguageNativeName"]?.ToString() ?? ""
                }
            };
        }
    }
} 