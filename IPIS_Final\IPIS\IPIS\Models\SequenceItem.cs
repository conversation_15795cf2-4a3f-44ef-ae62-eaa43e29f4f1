using System;

namespace IPIS.Models
{
    public enum ItemType
    {
        AudioFile = 1,
        Placeholder = 2
    }

    public class SequenceItem
    {
        public int Id { get; set; }
        public int SequenceId { get; set; }
        public int OrderIndex { get; set; }
        public ItemType Type { get; set; }
        public string Content { get; set; } // Audio file path or placeholder name
        public string Description { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }

        // Navigation properties
        public virtual AnnouncementSequence Sequence { get; set; }

        public SequenceItem()
        {
            IsActive = true;
            CreatedAt = DateTime.Now;
        }

        public override string ToString()
        {
            return $"{OrderIndex}: {Content} ({Type})";
        }
    }
} 