using System;

namespace IPIS.Models
{
    public class TrainType
    {
        public string ID { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }

        public TrainType()
        {
            ID = Guid.NewGuid().ToString();
            CreatedDate = DateTime.Now;
            IsActive = true;
        }
    }
}
