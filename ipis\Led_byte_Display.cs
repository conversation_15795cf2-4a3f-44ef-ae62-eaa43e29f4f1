// Decompiled with JetBrains decompiler
// Type: ipis.Led_byte_Display
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Diagnostics;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Text;

namespace ipis
{

public class Led_byte_Display
{
  public int _TADDBTrainNoSize;
  public int _TADDBTimeSize;
  public int _TADDBStatusSize;
  public int _TADDBPlatformSize;
  public int _TADDBWordSpace;
  public Font _EnglishFont;
  public Font _DisplayFont;
  public int _XPos;
  public int _YPos;
  public int _Columns;
  public byte _DisplayTimeModes;
  public int _Lines;

  [DebuggerNonUserCode]
  public Led_byte_Display()
  {
  }

  public int TADDBTrainNoSize
  {
    get { return this._TADDBTrainNoSize; }
    set { this._TADDBTrainNoSize = value; }
  }

  public int TADDBTimeSize
  {
    get { return this._TADDBTimeSize; }
    set { this._TADDBTimeSize = value; }
  }

  public int TADDBStatusSize
  {
    get { return this._TADDBStatusSize; }
    set { this._TADDBStatusSize = value; }
  }

  public int TADDBPlatformSize
  {
    get { return this._TADDBPlatformSize; }
    set { this._TADDBPlatformSize = value; }
  }

  public int TADDBWordSpace
  {
    get { return this._TADDBWordSpace; }
    set { this._TADDBWordSpace = value; }
  }

  public Font EnglishFont
  {
    get { return this._EnglishFont; }
    set { this._EnglishFont = value; }
  }

  public Font DisplayFont
  {
    get { return this._DisplayFont; }
    set { this._DisplayFont = value; }
  }

  public int XPos
  {
    get { return this._XPos; }
    set { this._XPos = value; }
  }

  public int YPos
  {
    get { return this._YPos; }
    set { this._YPos = value; }
  }

  public int Columns
  {
    get { return this._Columns; }
    set { this._Columns = value; }
  }

  public byte DisplayTimeModes
  {
    get { return this._DisplayTimeModes; }
    set { this._DisplayTimeModes = value; }
  }

  public int Lines
  {
    get { return this._Lines; }
    set { this._Lines = value; }
  }

  public byte[] GetDisplayByteArray(string[] message)
  {
    byte[] displayByteArray = new byte[checked ((int) Math.Round(unchecked ((double) checked (this.Lines * 16 /*0x10*/ * this.Columns) / 8.0 - 1.0)) + 1)];
    int num1 = 1;
    try
    {
      if (Strings.Len(message[6].Length) == 1)
        message[6] = "0" + message[6];
      if (Operators.CompareString(message[6], string.Empty, false) != 0)
        num1 = Strings.Len(message[6]);
      int taddbTrainNoSize = this.TADDBTrainNoSize;
      int taddbTimeSize = this.TADDBTimeSize;
      int num2 = this.TADDBStatusSize;
      int num3 = checked (num1 * this.TADDBPlatformSize);
      object taddbWordSpace = (object) this.TADDBWordSpace;
      string str1 = Conversions.ToString(Interaction.IIf(Operators.CompareString(message[3], "A", false) == 0, (object) message[4], (object) message[5]));
      string str2 = message[3];
      if (this.DisplayTimeModes == (byte) 0)
      {
        str1 = message[4];
        str2 = message[5];
        num2 = this.TADDBTimeSize;
      }
      int num4 = checked (Conversions.ToInteger(Operators.SubtractObject((object) (this.Columns - taddbTrainNoSize - taddbTimeSize - num2 - num3), Operators.MultiplyObject((object) 4, taddbWordSpace))) - 10);
      int num5 = checked (num3 + 10);
      int num6 = checked (num2 + 8);
      string[][] array = new string[5][]
      {
        new string[2]{ message[0], taddbTrainNoSize.ToString() },
        new string[2]{ message[1], num4.ToString() },
        new string[2]{ str1, taddbTimeSize.ToString() },
        new string[2]{ str2, num6.ToString() },
        new string[2]{ message[6], num5.ToString() }
      };
      Bitmap bitmap = new Bitmap(checked (this.Columns + 10), checked (this.Lines * 16 /*0x10*/ + 2));
      Graphics graphics = Graphics.FromImage((Image) bitmap);
      graphics.Clear(Color.Black);
      graphics.SmoothingMode = SmoothingMode.HighSpeed;
      graphics.TextRenderingHint = TextRenderingHint.AntiAliasGridFit;
      graphics.CompositingQuality = CompositingQuality.HighQuality;
      int x1 = 0;
      int num7 = 0;
      string[][] strArray1 = array;
      int index1 = 0;
      while (index1 < strArray1.Length)
      {
        string[] strArray2 = strArray1[index1];
        Array.IndexOf<string[]>(array, strArray2);
        Font font = this.DisplayFont;
        int ypos = this.YPos;
        if (strArray2.Length == 2)
        {
          int integer = Conversions.ToInteger(strArray2[1]);
          if (num7 != 1)
            font = this.EnglishFont;
          graphics.DrawString(strArray2[0], font, Brushes.Red, (float) x1, (float) ypos);
          x1 = Conversions.ToInteger(Operators.AddObject((object) checked (x1 + integer), taddbWordSpace));
          checked { ++num7; }
        }
        checked { ++index1; }
      }
      int num8 = 0;
      int index2 = 0;
      short num9 = checked ((short) (this.Columns - 1));
      bitmap.Save("c:\\IPIS\\test1.bmp");
      displayByteArray[index2] = (byte) 0;
      int num10 = checked (this.Columns - 1);
      int x2 = 0;
      while (x2 <= num10)
      {
        int y = checked (this.Lines * 16 /*0x10*/ - 1);
        while (y >= 0)
        {
          if (bitmap.GetPixel(x2, y).R == byte.MaxValue)
            displayByteArray[index2] = checked ((byte) ((long) displayByteArray[index2] | (long) Math.Round(Math.Pow(2.0, unchecked ((double) checked (7 - unchecked (num8 % 8)))))));
          checked { ++num8; }
          if (num8 % 8 == 0 & index2 < checked (displayByteArray.Length - 1) & num8 != 0)
          {
            checked { ++index2; }
            displayByteArray[index2] = (byte) 0;
          }
          checked { y += -1; }
        }
        checked { ++x2; }
      }
      graphics.Dispose();
      bitmap.Dispose();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num11 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    return displayByteArray;
  }

  public byte[] GetDisplayMsgByteArray(string message)
  {
    byte[] displayMsgByteArray = new byte[checked ((int) Math.Round(unchecked ((double) checked (this.Lines * 16 /*0x10*/ * this.Columns) / 8.0 - 1.0)) + 1)];
    try
    {
      Bitmap bitmap = new Bitmap(checked (this.Columns + 10), checked (this.Lines * 16 /*0x10*/ + 2));
      Graphics graphics = Graphics.FromImage((Image) bitmap);
      graphics.Clear(Color.Black);
      graphics.SmoothingMode = SmoothingMode.HighSpeed;
      graphics.TextRenderingHint = TextRenderingHint.AntiAliasGridFit;
      graphics.CompositingQuality = CompositingQuality.HighQuality;
      int x1 = 0;
      Font displayFont = this.DisplayFont;
      int ypos = this.YPos;
      graphics.DrawString(message, displayFont, Brushes.Red, (float) x1, (float) ypos);
      int num1 = 0;
      int index = 0;
      short num2 = checked ((short) (this.Columns - 1));
      bitmap.Save("c:\\IPIS\\test1.bmp");
      displayMsgByteArray[index] = (byte) 0;
      int num3 = checked (this.Columns - 1);
      int x2 = 0;
      while (x2 <= num3)
      {
        int y = checked (this.Lines * 16 /*0x10*/ - 1);
        while (y >= 0)
        {
          if (bitmap.GetPixel(x2, y).R == byte.MaxValue)
            displayMsgByteArray[index] = checked ((byte) ((long) displayMsgByteArray[index] | (long) Math.Round(Math.Pow(2.0, unchecked ((double) checked (7 - unchecked (num1 % 8)))))));
          checked { ++num1; }
          if (num1 % 8 == 0 & index < checked (displayMsgByteArray.Length - 1) & num1 != 0)
          {
            checked { ++index; }
            displayMsgByteArray[index] = (byte) 0;
          }
          checked { y += -1; }
        }
        checked { ++x2; }
      }
      graphics.Dispose();
      bitmap.Dispose();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    return displayMsgByteArray;
  }

  public byte[] GetDisplayByteArrayStatus(string[] message)
  {
    byte[] displayByteArrayStatus = new byte[checked ((int) Math.Round(unchecked ((double) checked (this.Lines * 16 /*0x10*/ * this.Columns) / 8.0 - 1.0)) + 1)];
    int num1 = 1;
    try
    {
      int taddbTrainNoSize = this.TADDBTrainNoSize;
      int taddbTimeSize1 = this.TADDBTimeSize;
      int num2 = this.TADDBStatusSize;
      int num3 = checked (num1 * this.TADDBPlatformSize);
      object taddbWordSpace = (object) this.TADDBWordSpace;
      int taddbTimeSize2 = this.TADDBTimeSize;
      int num4 = checked (this.TADDBTimeSize + taddbTimeSize1);
      int num5 = checked (Conversions.ToInteger(Operators.SubtractObject((object) (this.Columns - taddbTrainNoSize - taddbTimeSize1 - taddbTimeSize2 - num3), Operators.MultiplyObject((object) 4, taddbWordSpace))) - 10);
      int num6 = checked (num3 + 6);
      num2 = checked (taddbTimeSize2 + 8);
      string[][] array = new string[4][]
      {
        new string[2]{ message[0], taddbTrainNoSize.ToString() },
        new string[2]{ message[1], num5.ToString() },
        new string[2]{ message[2], num4.ToString() },
        new string[2]{ message[3], num6.ToString() }
      };
      Bitmap bitmap = new Bitmap(checked (this.Columns + 10), checked (this.Lines * 16 /*0x10*/ + 2));
      Graphics graphics = Graphics.FromImage((Image) bitmap);
      graphics.Clear(Color.Black);
      graphics.SmoothingMode = SmoothingMode.HighSpeed;
      graphics.TextRenderingHint = TextRenderingHint.AntiAliasGridFit;
      graphics.CompositingQuality = CompositingQuality.HighQuality;
      int x1 = 0;
      int num7 = 0;
      string[][] strArray1 = array;
      int index1 = 0;
      while (index1 < strArray1.Length)
      {
        string[] strArray2 = strArray1[index1];
        Array.IndexOf<string[]>(array, strArray2);
        Font displayFont = this.DisplayFont;
        int ypos = this.YPos;
        if (strArray2.Length == 2)
        {
          int integer = Conversions.ToInteger(strArray2[1]);
          Font font = !(num7 == 1 | num7 == 2) ? this.EnglishFont : this.DisplayFont;
          graphics.DrawString(strArray2[0], font, Brushes.Red, (float) x1, (float) ypos);
          x1 = Conversions.ToInteger(Operators.AddObject((object) checked (x1 + integer), taddbWordSpace));
          checked { ++num7; }
        }
        checked { ++index1; }
      }
      int num8 = 0;
      int index2 = 0;
      short num9 = checked ((short) (this.Columns - 1));
      bitmap.Save("c:\\IPIS\\test1.bmp");
      displayByteArrayStatus[index2] = (byte) 0;
      int num10 = checked (this.Columns - 1);
      int x2 = 0;
      while (x2 <= num10)
      {
        int y = checked (this.Lines * 16 /*0x10*/ - 1);
        while (y >= 0)
        {
          if (bitmap.GetPixel(x2, y).R == byte.MaxValue)
            displayByteArrayStatus[index2] = checked ((byte) ((long) displayByteArrayStatus[index2] | (long) Math.Round(Math.Pow(2.0, unchecked ((double) checked (7 - unchecked (num8 % 8)))))));
          checked { ++num8; }
          if (num8 % 8 == 0 & index2 < checked (displayByteArrayStatus.Length - 1) & num8 != 0)
          {
            checked { ++index2; }
            displayByteArrayStatus[index2] = (byte) 0;
          }
          checked { y += -1; }
        }
        checked { ++x2; }
      }
      graphics.Dispose();
      bitmap.Dispose();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num11 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    return displayByteArrayStatus;
  }

  public byte[] GetDisplayTrainNameByteArray(string message)
  {
    byte[] trainNameByteArray = new byte[checked ((int) Math.Round(unchecked ((double) checked (this.Lines * 16 /*0x10*/ * this.Columns) / 8.0 - 1.0)) + 1)];
    try
    {
      Bitmap bitmap = new Bitmap(checked (this.Columns + 10), checked (this.Lines * 16 /*0x10*/ + 2));
      Graphics graphics = Graphics.FromImage((Image) bitmap);
      graphics.Clear(Color.Black);
      graphics.SmoothingMode = SmoothingMode.HighSpeed;
      graphics.TextRenderingHint = TextRenderingHint.AntiAliasGridFit;
      graphics.CompositingQuality = CompositingQuality.HighQuality;
      int Left = 0;
      object taddbWordSpace = (object) this.TADDBWordSpace;
      int ypos = this.YPos;
      int integer = Conversions.ToInteger(Operators.AddObject((object) Left, taddbWordSpace));
      graphics.DrawString(message, this.DisplayFont, Brushes.Red, (float) integer, (float) ypos);
      int num1 = 0;
      int index = 0;
      short num2 = checked ((short) (this.Columns - 1));
      bitmap.Save("C:\\IPIS\\test1.bmp");
      trainNameByteArray[index] = (byte) 0;
      int num3 = checked (this.Columns - 1);
      int x = 0;
      while (x <= num3)
      {
        int y = checked (this.Lines * 16 /*0x10*/ - 1);
        while (y >= 0)
        {
          if (bitmap.GetPixel(x, y).R == byte.MaxValue)
            trainNameByteArray[index] = checked ((byte) ((long) trainNameByteArray[index] | (long) Math.Round(Math.Pow(2.0, unchecked ((double) checked (7 - unchecked (num1 % 8)))))));
          checked { ++num1; }
          if (num1 % 8 == 0 & index < checked (trainNameByteArray.Length - 1) & num1 != 0)
          {
            checked { ++index; }
            trainNameByteArray[index] = (byte) 0;
          }
          checked { y += -1; }
        }
        checked { ++x; }
      }
      graphics.Dispose();
      bitmap.Dispose();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    return trainNameByteArray;
  }
}

}