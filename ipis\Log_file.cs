// Decompiled with JetBrains decompiler
// Type: ipis.Log_file
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using ipis.My;
using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Diagnostics;
using System.IO;

namespace ipis
{

public class Log_file
{
  [DebuggerNonUserCode]
  public Log_file()
  {
  }

  public static void Log(string Message)
  {
    try
    {
      string path = "C:\\IPIS\\shared_info\\log\\" + Strings.Format((object) DateTime.Today, "dd-MM-yyyy") + ".txt";
      FileStream fileStream = !File.Exists(path) ? new FileStream(path, FileMode.Create, FileAccess.Write, FileShare.ReadWrite) : new FileStream(path, FileMode.Append, FileAccess.Write, FileShare.ReadWrite);
      StreamWriter streamWriter = new StreamWriter((Stream) fileStream);
      streamWriter.Write(DateTime.Now.ToLongTimeString() + "   ");
      streamWriter.WriteLine(Message);
      streamWriter.Flush();
      streamWriter.Close();
      fileStream.Close();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
  }

  public static void Train_Log(string Message)
  {
    try
    {
      string path = "C:\\IPIS\\shared_info\\Train_Log\\" + Strings.Format((object) DateTime.Today, "dd-MM-yyyy") + ".txt";
      FileStream fileStream = !File.Exists(path) ? new FileStream(path, FileMode.Create, FileAccess.Write, FileShare.None) : new FileStream(path, FileMode.Append, FileAccess.Write, FileShare.None);
      StreamWriter streamWriter = new StreamWriter((Stream) fileStream);
      streamWriter.WriteLine(Message);
      streamWriter.Flush();
      streamWriter.Close();
      fileStream.Close();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  public static void cgs_Log(string Message)
  {
    try
    {
      string path = "C:\\IPIS\\shared_info\\Cgs_Log\\" + Strings.Format((object) DateTime.Today, "dd-MM-yyyy") + ".txt";
      FileStream fileStream = !File.Exists(path) ? new FileStream(path, FileMode.Create, FileAccess.Write, FileShare.None) : new FileStream(path, FileMode.Append, FileAccess.Write, FileShare.None);
      StreamWriter streamWriter = new StreamWriter((Stream) fileStream);
      streamWriter.Write(DateTime.Now.ToLongTimeString() + "   ");
      streamWriter.WriteLine(Message);
      streamWriter.Flush();
      streamWriter.Close();
      fileStream.Close();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  public static void login_details_log(string Message)
  {
    try
    {
      string path = "C:\\IPIS\\shared_info\\login_details\\" + Strings.Format((object) DateTime.Today, "dd-MM-yyyy") + ".txt";
      FileStream fileStream = !File.Exists(path) ? new FileStream(path, FileMode.Create, FileAccess.Write, FileShare.None) : new FileStream(path, FileMode.Append, FileAccess.Write, FileShare.None);
      StreamWriter streamWriter = new StreamWriter((Stream) fileStream);
      streamWriter.Write(DateTime.Now.ToLongTimeString() + "   ");
      streamWriter.WriteLine(Message);
      streamWriter.Flush();
      streamWriter.Close();
      fileStream.Close();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  public static void ReadLogFile(string filepath, ref string[] fstr)
  {
    string[] strArray = new string[5];
    try
    {
      if (!File.Exists(filepath))
        return;
      FileStream fileStream1 = new FileStream(filepath, FileMode.Open, FileAccess.Read, FileShare.None);
      StreamReader streamReader1 = new StreamReader((Stream) fileStream1);
      int num = 0;
      while (streamReader1.Peek() >= 0)
      {
        streamReader1.ReadLine();
        checked { ++num; }
      }
      fstr = new string[checked (num + 1)];
      streamReader1.Close();
      fileStream1.Close();
      FileStream fileStream2 = new FileStream(filepath, FileMode.Open, FileAccess.Read, FileShare.None);
      StreamReader streamReader2 = new StreamReader((Stream) fileStream2);
      int index = 0;
      string empty = string.Empty;
      while (streamReader2.Peek() >= 0)
      {
        string str = streamReader2.ReadLine();
        fstr[index] = str;
        checked { ++index; }
      }
      fileStream2.Close();
      streamReader2.Close();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      int num = (int) Interaction.MsgBox((object) ex.Message);
      ProjectData.ClearProjectError();
    }
  }

  public static void read_redundant_log_file()
  {
    string str1 = Strings.Format((object) DateTime.Now.Date, "dd-MM-yyyy");
    try
    {
      string destinationFileName = "Z:\\shared_info\\log\\" + str1 + ".txt";
      string sourceFileName = "C:\\IPIS\\shared_info\\log\\" + str1 + ".txt";
      bool overwrite = true;
      MyProject.Computer.FileSystem.CopyFile(sourceFileName, destinationFileName, overwrite);
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
    string str2;
    bool flag;
    try
    {
      string path = "Z:\\shared_info\\Train_log\\" + str1 + ".txt";
      str2 = "C:\\IPIS\\shared_info\\Train_log\\" + str1 + ".txt";
      if (!File.Exists(path))
        File.Create(path);
      flag = true;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
    try
    {
      string path = "Z:\\shared_info\\Cgs_Log\\" + str1 + ".txt";
      str2 = "C:\\IPIS\\shared_info\\Cgs_Log\\" + str1 + ".txt";
      if (!File.Exists(path))
        File.Create(path);
      flag = true;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
  }
}

}