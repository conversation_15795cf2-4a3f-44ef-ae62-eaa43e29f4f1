using System;
using System.IO.Ports; // For Parity and StopBits enums

namespace ipis_V2_jules.Hardware
{
    public interface ICommunicationService : IDisposable
    {
        bool OpenPort(string portName, int baudRate, int dataBits, Parity parity, StopBits stopBits);
        void ClosePort();
        bool SendData(byte[] data);
        byte[] ReadData(int expectedBytes, int timeoutMs);
        // Task<bool> SendDataAsync(string ipAddress, int port, byte[] data); // For future network boards
        // Task<byte[]> ReadDataAsync(string ipAddress, int port, int expectedBytes, int timeoutMs); // For future network boards
    }
}
