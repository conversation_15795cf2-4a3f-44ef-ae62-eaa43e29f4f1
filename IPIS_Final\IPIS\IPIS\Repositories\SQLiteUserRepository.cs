using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SQLite;
using IPIS.Repositories.Interfaces;
using IPIS.Utils;
using IPIS.Models;

namespace IPIS.Repositories
{
    public class SQLiteUserRepository : IUserRepository
    {
        private readonly string connectionString;

        public SQLiteUserRepository()
        {
            connectionString = Database.ConnectionString;
        }

        public void AddUser(string username, string password, string role)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string insertUserQuery = "INSERT INTO Users (Username, Password, Role, LastLogin, Status) VALUES (@Username, @Password, @Role, @LastLogin, @Status)";
                using (var command = new SQLiteCommand(insertUserQuery, connection))
                {
                    command.Parameters.AddWithValue("@Username", username);
                    command.Parameters.AddWithValue("@Password", password);
                    command.Parameters.AddWithValue("@Role", role);
                    command.Parameters.AddWithValue("@LastLogin", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                    command.Parameters.AddWithValue("@Status", "Active");
                    command.ExecuteNonQuery();
                }
            }
        }

        public void UpdateUser(long userId, string username, string password, string role)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string updateUserQuery = "UPDATE Users SET Username = @Username, Password = @Password, Role = @Role, LastLogin = @LastLogin WHERE Id = @UserId";
                using (var command = new SQLiteCommand(updateUserQuery, connection))
                {
                    command.Parameters.AddWithValue("@Username", username);
                    command.Parameters.AddWithValue("@Password", password);
                    command.Parameters.AddWithValue("@Role", role);
                    command.Parameters.AddWithValue("@LastLogin", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                    command.Parameters.AddWithValue("@UserId", userId);
                    command.ExecuteNonQuery();
                }
            }
        }

        public void DeleteUser(long userId)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
                {
                    try
                    {
                        string deletePermissionsQuery = "DELETE FROM UserPermissions WHERE UserId = @UserId";
                        using (var command = new SQLiteCommand(deletePermissionsQuery, connection, transaction))
                        {
                            command.Parameters.AddWithValue("@UserId", userId);
                            command.ExecuteNonQuery();
                        }

                        string deleteUserQuery = "DELETE FROM Users WHERE Id = @UserId";
                        using (var command = new SQLiteCommand(deleteUserQuery, connection, transaction))
                        {
                            command.Parameters.AddWithValue("@UserId", userId);
                            command.ExecuteNonQuery();
                        }

                        transaction.Commit();
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        throw new Exception("Error deleting user: " + ex.Message, ex);
                    }
                }
            }
        }

        public DataTable GetAllUsers()
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = "SELECT Id, Username, Role, LastLogin, Status FROM Users";
                using (var command = new SQLiteCommand(query, connection))
                {
                    using (var adapter = new SQLiteDataAdapter(command))
                    {
                        var dataTable = new DataTable();
                        adapter.Fill(dataTable);
                        return dataTable;
                    }
                }
            }
        }

        public bool ValidateUser(string username, string password)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = "SELECT COUNT(*) FROM Users WHERE Username = @Username AND Password = @Password";
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Username", username);
                    command.Parameters.AddWithValue("@Password", password);
                    long count = (long)command.ExecuteScalar();
                    return count > 0;
                }
            }
        }

        public List<string> GetUserPermissions(long userId)
        {
            var permissions = new List<string>();
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = "SELECT Permission FROM UserPermissions WHERE UserId = @UserId";
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@UserId", userId);
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            permissions.Add(reader.GetString(0));
                        }
                    }
                }
            }
            return permissions;
        }

        public User AuthenticateUser(string username, string password)
        {
            // First try the new Users table
            var user = GetUserByUsername(username);
            if (user != null && user.Password == password && user.IsActive())
            {
                UpdateLastLogin(user.Id);
                return user;
            }

            // If not found in new table, try legacy User_Details table
            var legacyUser = GetLegacyUser(username);
            if (legacyUser != null && legacyUser.Pass == password)
            {
                return legacyUser;
            }

            return null;
        }

        public User GetUserByUsername(string username)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = "SELECT Id, Username, Password, Role, LastLogin, Status FROM Users WHERE Username = @Username";
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Username", username);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            var user = new User
                            {
                                Id = reader.GetInt64("Id"),
                                Username = reader.GetString("Username"),
                                Password = reader.GetString("Password"),
                                Role = reader.GetString("Role"),
                                Status = reader.GetString("Status")
                            };

                            if (!reader.IsDBNull("LastLogin"))
                            {
                                user.LastLogin = DateTime.Parse(reader.GetString("LastLogin"));
                            }

                            // Load permissions
                            user.Permissions = GetUserPermissions(user.Id);
                            return user;
                        }
                    }
                }
            }
            return null;
        }

        public User GetUserById(long userId)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = "SELECT Id, Username, Password, Role, LastLogin, Status FROM Users WHERE Id = @UserId";
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@UserId", userId);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            var user = new User
                            {
                                Id = reader.GetInt64("Id"),
                                Username = reader.GetString("Username"),
                                Password = reader.GetString("Password"),
                                Role = reader.GetString("Role"),
                                Status = reader.GetString("Status")
                            };

                            if (!reader.IsDBNull("LastLogin"))
                            {
                                user.LastLogin = DateTime.Parse(reader.GetString("LastLogin"));
                            }

                            // Load permissions
                            user.Permissions = GetUserPermissions(user.Id);
                            return user;
                        }
                    }
                }
            }
            return null;
        }

        public void UpdateLastLogin(long userId)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = "UPDATE Users SET LastLogin = @LastLogin WHERE Id = @UserId";
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@LastLogin", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                    command.Parameters.AddWithValue("@UserId", userId);
                    command.ExecuteNonQuery();
                }
            }
        }

        public bool UserExists(string username)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = "SELECT COUNT(*) FROM Users WHERE Username = @Username";
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Username", username);
                    long count = (long)command.ExecuteScalar();
                    return count > 0;
                }
            }
        }

        public bool IsLegacyUser(string username)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = "SELECT COUNT(*) FROM User_Details WHERE User_Name = @Username";
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Username", username);
                    long count = (long)command.ExecuteScalar();
                    return count > 0;
                }
            }
        }

        public User GetLegacyUser(string username)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = "SELECT User_Name, Pass, User_type, Hint_Pass, Chk_Adver, Chk_TDEntry, Chk_Reports, Chk_SD, Chk_AUser, Chk_ASCode, Chk_Rep FROM User_Details WHERE User_Name = @Username";
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Username", username);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            var user = new User
                            {
                                Id = -1, // Legacy users don't have an ID in the new system
                                Username = reader.GetString("User_Name"),
                                UserName = reader.GetString("User_Name"),
                                Password = reader.GetString("Pass"),
                                Pass = reader.GetString("Pass"),
                                Role = reader.GetString("User_type"),
                                UserType = reader.GetString("User_type"),
                                Status = "Active"
                            };

                            if (!reader.IsDBNull("Hint_Pass"))
                            {
                                user.HintPass = reader.GetString("Hint_Pass");
                            }

                            // Convert legacy permissions to new format
                            user.ChkAdver = reader.GetInt32("Chk_Adver") == 1;
                            user.ChkTDEntry = reader.GetInt32("Chk_TDEntry") == 1;
                            user.ChkReports = reader.GetInt32("Chk_Reports") == 1;
                            user.ChkSD = reader.GetInt32("Chk_SD") == 1;
                            user.ChkAUser = reader.GetInt32("Chk_AUser") == 1;
                            user.ChkASCode = reader.GetInt32("Chk_ASCode") == 1;
                            user.ChkRep = reader.GetInt32("Chk_Rep") == 1;

                            // Map legacy permissions to new permission names
                            if (user.ChkAdver) user.Permissions.Add("Add Advertising");
                            if (user.ChkTDEntry) user.Permissions.Add("Train Data Entry");
                            if (user.ChkReports) user.Permissions.Add("Reports");
                            if (user.ChkSD) user.Permissions.Add("Station Details");
                            if (user.ChkAUser) user.Permissions.Add("Add User");
                            if (user.ChkASCode) user.Permissions.Add("Add Station Code");

                            return user;
                        }
                    }
                }
            }
            return null;
        }
    }
} 