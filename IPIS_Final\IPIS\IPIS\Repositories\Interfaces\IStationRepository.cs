using System.Data;
using System.Collections.Generic;
using IPIS.Models; // Assuming models will be here

namespace IPIS.Repositories.Interfaces
{
    public interface IStationRepository
    {
        StationDetails GetStationDetails(string stationName);
        void AddStation(StationDetails station);
        void UpdateStation(StationDetails station);
        void DeleteStation(string stationName);
        List<string> GetAllStationNames();
        Dictionary<string, string> GetStationNamesAndCodes();
        List<StationDetails> GetAllStations();
        StationDetails GetCurrentStation();
        void SetCurrentStation(string stationName);
        List<StationDetails> SearchStations(string searchTerm);
        void ClearAllStations();
    }
} 