// Decompiled with JetBrains decompiler
// Type: ipis.frmChangePassword
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

namespace ipis
{

[DesignerGenerated]
public class frmChangePassword : Form
{
  private static List<WeakReference> __ENCList = new List<WeakReference>();
  private IContainer components;
  [AccessedThroughProperty("btnCancel")]
  private Button _btnCancel;
  [AccessedThroughProperty("btnSave")]
  private Button _btnSave;
  [AccessedThroughProperty("txtConfirmPwd")]
  private TextBox _txtConfirmPwd;
  [AccessedThroughProperty("txtNewPwd")]
  private TextBox _txtNewPwd;
  [AccessedThroughProperty("txtPwd")]
  private TextBox _txtPwd;
  [AccessedThroughProperty("lblCpwd")]
  private Label _lblCpwd;
  [AccessedThroughProperty("lblNpwd")]
  private Label _lblNpwd;
  [AccessedThroughProperty("lblPwd")]
  private Label _lblPwd;

  [DebuggerNonUserCode]
  static frmChangePassword()
  {
  }

  [DebuggerNonUserCode]
  public frmChangePassword()
  {
    frmChangePassword.__ENCAddToList((object) this);
    this.InitializeComponent();
  }

  [DebuggerNonUserCode]
  private static void __ENCAddToList(object value)
  {
    lock (frmChangePassword.__ENCList)
    {
      if (frmChangePassword.__ENCList.Count == frmChangePassword.__ENCList.Capacity)
      {
        int index1 = 0;
        int num = checked (frmChangePassword.__ENCList.Count - 1);
        int index2 = 0;
        while (index2 <= num)
        {
          if (frmChangePassword.__ENCList[index2].IsAlive)
          {
            if (index2 != index1)
              frmChangePassword.__ENCList[index1] = frmChangePassword.__ENCList[index2];
            checked { ++index1; }
          }
          checked { ++index2; }
        }
        frmChangePassword.__ENCList.RemoveRange(index1, checked (frmChangePassword.__ENCList.Count - index1));
        frmChangePassword.__ENCList.Capacity = frmChangePassword.__ENCList.Count;
      }
      frmChangePassword.__ENCList.Add(new WeakReference(RuntimeHelpers.GetObjectValue(value)));
    }
  }

  [DebuggerNonUserCode]
  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  [DebuggerStepThrough]
  private void InitializeComponent()
  {
    this.btnCancel = new Button();
    this.btnSave = new Button();
    this.txtConfirmPwd = new TextBox();
    this.txtNewPwd = new TextBox();
    this.txtPwd = new TextBox();
    this.lblCpwd = new Label();
    this.lblNpwd = new Label();
    this.lblPwd = new Label();
    this.SuspendLayout();
    this.btnCancel.BackColor = Color.SeaShell;
    this.btnCancel.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnCancel1 = this.btnCancel;
    Point point1 = new Point(291, 208 /*0xD0*/);
    Point point2 = point1;
    btnCancel1.Location = point2;
    this.btnCancel.Name = "btnCancel";
    Button btnCancel2 = this.btnCancel;
    Size size1 = new Size(79, 25);
    Size size2 = size1;
    btnCancel2.Size = size2;
    this.btnCancel.TabIndex = 5;
    this.btnCancel.Text = "&Cancel";
    this.btnCancel.UseVisualStyleBackColor = false;
    this.btnSave.BackColor = Color.SeaShell;
    this.btnSave.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnSave1 = this.btnSave;
    point1 = new Point(84, 208 /*0xD0*/);
    Point point3 = point1;
    btnSave1.Location = point3;
    this.btnSave.Name = "btnSave";
    Button btnSave2 = this.btnSave;
    size1 = new Size(156, 25);
    Size size3 = size1;
    btnSave2.Size = size3;
    this.btnSave.TabIndex = 4;
    this.btnSave.Text = "Change Password";
    this.btnSave.UseVisualStyleBackColor = false;
    this.txtConfirmPwd.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtConfirmPwd1 = this.txtConfirmPwd;
    point1 = new Point(221, 132);
    Point point4 = point1;
    txtConfirmPwd1.Location = point4;
    this.txtConfirmPwd.MaxLength = 15;
    this.txtConfirmPwd.Name = "txtConfirmPwd";
    this.txtConfirmPwd.PasswordChar = '*';
    TextBox txtConfirmPwd2 = this.txtConfirmPwd;
    size1 = new Size(197, 22);
    Size size4 = size1;
    txtConfirmPwd2.Size = size4;
    this.txtConfirmPwd.TabIndex = 3;
    this.txtNewPwd.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtNewPwd1 = this.txtNewPwd;
    point1 = new Point(221, 83);
    Point point5 = point1;
    txtNewPwd1.Location = point5;
    this.txtNewPwd.MaxLength = 15;
    this.txtNewPwd.Name = "txtNewPwd";
    this.txtNewPwd.PasswordChar = '*';
    TextBox txtNewPwd2 = this.txtNewPwd;
    size1 = new Size(197, 22);
    Size size5 = size1;
    txtNewPwd2.Size = size5;
    this.txtNewPwd.TabIndex = 2;
    this.txtPwd.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtPwd1 = this.txtPwd;
    point1 = new Point(221, 37);
    Point point6 = point1;
    txtPwd1.Location = point6;
    this.txtPwd.MaxLength = 15;
    this.txtPwd.Name = "txtPwd";
    this.txtPwd.PasswordChar = '*';
    TextBox txtPwd2 = this.txtPwd;
    size1 = new Size(197, 22);
    Size size6 = size1;
    txtPwd2.Size = size6;
    this.txtPwd.TabIndex = 1;
    this.lblCpwd.AutoSize = true;
    this.lblCpwd.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblCpwd1 = this.lblCpwd;
    point1 = new Point(49, 135);
    Point point7 = point1;
    lblCpwd1.Location = point7;
    this.lblCpwd.Name = "lblCpwd";
    Label lblCpwd2 = this.lblCpwd;
    size1 = new Size(132, 16 /*0x10*/);
    Size size7 = size1;
    lblCpwd2.Size = size7;
    this.lblCpwd.TabIndex = 14;
    this.lblCpwd.Text = "Confirm Password";
    this.lblNpwd.AutoSize = true;
    this.lblNpwd.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblNpwd1 = this.lblNpwd;
    point1 = new Point(66, 86);
    Point point8 = point1;
    lblNpwd1.Location = point8;
    this.lblNpwd.Name = "lblNpwd";
    Label lblNpwd2 = this.lblNpwd;
    size1 = new Size(110, 16 /*0x10*/);
    Size size8 = size1;
    lblNpwd2.Size = size8;
    this.lblNpwd.TabIndex = 13;
    this.lblNpwd.Text = "New Password";
    this.lblPwd.AutoSize = true;
    this.lblPwd.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblPwd1 = this.lblPwd;
    point1 = new Point(72, 37);
    Point point9 = point1;
    lblPwd1.Location = point9;
    this.lblPwd.Name = "lblPwd";
    Label lblPwd2 = this.lblPwd;
    size1 = new Size(104, 16 /*0x10*/);
    Size size9 = size1;
    lblPwd2.Size = size9;
    this.lblPwd.TabIndex = 12;
    this.lblPwd.Text = "Old Password";
    this.AcceptButton = (IButtonControl) this.btnSave;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.BackColor = Color.PowderBlue;
    size1 = new Size(468, 259);
    this.ClientSize = size1;
    this.Controls.Add((Control) this.btnCancel);
    this.Controls.Add((Control) this.btnSave);
    this.Controls.Add((Control) this.txtConfirmPwd);
    this.Controls.Add((Control) this.txtNewPwd);
    this.Controls.Add((Control) this.txtPwd);
    this.Controls.Add((Control) this.lblCpwd);
    this.Controls.Add((Control) this.lblNpwd);
    this.Controls.Add((Control) this.lblPwd);
    this.FormBorderStyle = FormBorderStyle.FixedToolWindow;
    this.Name = "frmChangePassword";
    this.Text = "Change Password";
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  internal virtual Button btnCancel
  {
    [DebuggerNonUserCode] get { return this._btnCancel; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnCancel_Click);
      if (this._btnCancel != null)
        this._btnCancel.Click -= eventHandler;
      this._btnCancel = value;
      if (this._btnCancel == null)
        return;
      this._btnCancel.Click += eventHandler;
    }
  }

  internal virtual Button btnSave
  {
    [DebuggerNonUserCode] get { return this._btnSave; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnSave_Click);
      if (this._btnSave != null)
        this._btnSave.Click -= eventHandler;
      this._btnSave = value;
      if (this._btnSave == null)
        return;
      this._btnSave.Click += eventHandler;
    }
  }

  internal virtual TextBox txtConfirmPwd
  {
    [DebuggerNonUserCode] get { return this._txtConfirmPwd; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtConfirmPwd = value;
    }
  }

  internal virtual TextBox txtNewPwd
  {
    [DebuggerNonUserCode] get { return this._txtNewPwd; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtNewPwd = value;
    }
  }

  internal virtual TextBox txtPwd
  {
    [DebuggerNonUserCode] get { return this._txtPwd; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._txtPwd = value; }
  }

  internal virtual Label lblCpwd
  {
    [DebuggerNonUserCode] get { return this._lblCpwd; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._lblCpwd = value; }
  }

  internal virtual Label lblNpwd
  {
    [DebuggerNonUserCode] get { return this._lblNpwd; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._lblNpwd = value; }
  }

  internal virtual Label lblPwd
  {
    [DebuggerNonUserCode] get { return this._lblPwd; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._lblPwd = value; }
  }

  private void btnCancel_Click(object sender, EventArgs e)
  {
    this.txtNewPwd.Text = " ";
    this.txtPwd.Text = " ";
    this.txtConfirmPwd.Text = " ";
  }

  private void btnExit_Click(object sender, EventArgs e)
{
  this.Close();
}

  private void btnSave_Click(object sender, EventArgs e)
  {
    string empty1 = string.Empty;
    string empty2 = string.Empty;
    bool result = false;
    try
    {
      int index = 0;
      while (index < (int) frmMainFormIPIS.user_cnt.cnt)
      {
        network_db_read.dec_pwd(frmMainFormIPIS.user_details[index].pwd, ref empty1, Conversions.ToString(frmMainFormIPIS.user_details[index].pwd_length));
        if (Operators.CompareString(frmMainFormIPIS.user_login_details.user_id, frmMainFormIPIS.user_details[index].user_name, false) == 0 & Operators.CompareString(this.txtPwd.Text, empty1, false) == 0)
        {
          if (Operators.CompareString(this.txtNewPwd.Text, this.txtConfirmPwd.Text, false) == 0)
          {
            network_db_read.enc_pwd(this.txtNewPwd.Text, ref empty2);
            network_db_read.set_user_pwd(empty2, Conversions.ToString(this.txtNewPwd.Text.Length), frmMainFormIPIS.user_details[index].user_name, ref result);
            if (result)
            {
              int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Password changed successfully", "Msg Box", 0, 0, 0);
              frmMainFormIPIS.user_details[index].pwd = empty2;
              frmMainFormIPIS.user_details[index].pwd_length = checked ((byte) this.txtNewPwd.Text.Length);
              this.txtPwd.Text = string.Empty;
              this.txtNewPwd.Text = string.Empty;
              this.txtConfirmPwd.Text = string.Empty;
              this.Close();
              return;
            }
          }
          else
          {
            int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "confirm Password do not match with New password , Please retype the password", "Msg Box", 0, 0, 0);
            this.txtNewPwd.Text = string.Empty;
            this.txtConfirmPwd.Text = string.Empty;
            return;
          }
        }
        checked { ++index; }
      }
      int num1 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Either Name or Password typed Incorrectly , Please retype", "Msg Box", 0, 0, 0);
      this.txtPwd.Text = string.Empty;
      this.txtNewPwd.Text = string.Empty;
      this.txtConfirmPwd.Text = string.Empty;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }
}

}