using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using IPIS.Models;
using IPIS.Repositories.Interfaces;

namespace IPIS.Services
{
    public class StationLanguageService
    {
        private readonly IStationLanguageConfigRepository _languageConfigRepository;
        private readonly IStationAnnouncementConfigRepository _announcementConfigRepository;
        private readonly ILanguageRepository _languageRepository;

        public StationLanguageService(
            IStationLanguageConfigRepository languageConfigRepository,
            IStationAnnouncementConfigRepository announcementConfigRepository,
            ILanguageRepository languageRepository)
        {
            _languageConfigRepository = languageConfigRepository;
            _announcementConfigRepository = announcementConfigRepository;
            _languageRepository = languageRepository;
        }

        public async Task<List<StationLanguageConfig>> GetStationLanguagesAsync(string stationName)
        {
            return await _languageConfigRepository.GetByStationNameAsync(stationName);
        }

        public async Task<List<StationLanguageConfig>> GetEnabledStationLanguagesAsync(string stationName)
        {
            return await _languageConfigRepository.GetEnabledByStationAsync(stationName);
        }

        public async Task<StationLanguageConfig> GetStationLanguageAsync(string stationName, string languageCode)
        {
            return await _languageConfigRepository.GetByStationAndLanguageAsync(stationName, languageCode);
        }

        public async Task<bool> AddStationLanguageAsync(string stationName, string languageCode, bool isEnabled = true, string waveFilePath = null)
        {
            var config = new StationLanguageConfig
            {
                StationName = stationName,
                LanguageCode = languageCode,
                IsEnabled = isEnabled,
                WaveFilePath = waveFilePath,
                CreatedAt = DateTime.Now
            };

            var id = await _languageConfigRepository.AddAsync(config);
            return id > 0;
        }

        public async Task<bool> UpdateStationLanguageAsync(StationLanguageConfig config)
        {
            return await _languageConfigRepository.UpdateAsync(config);
        }

        public async Task<bool> DeleteStationLanguageAsync(string stationName, string languageCode)
        {
            return await _languageConfigRepository.DeleteByStationAndLanguageAsync(stationName, languageCode);
        }

        public async Task<bool> ToggleStationLanguageAsync(string stationName, string languageCode)
        {
            var config = await _languageConfigRepository.GetByStationAndLanguageAsync(stationName, languageCode);
            if (config == null)
            {
                // Create new config if it doesn't exist
                return await AddStationLanguageAsync(stationName, languageCode, true);
            }

            config.IsEnabled = !config.IsEnabled;
            return await _languageConfigRepository.UpdateAsync(config);
        }

        public async Task<StationAnnouncementConfig> GetStationAnnouncementConfigAsync(string stationName)
        {
            return await _announcementConfigRepository.GetByStationNameAsync(stationName);
        }

        public async Task<bool> SaveStationAnnouncementConfigAsync(StationAnnouncementConfig config)
        {
            if (await _announcementConfigRepository.ExistsAsync(config.StationName))
            {
                return await _announcementConfigRepository.UpdateAsync(config);
            }
            else
            {
                var id = await _announcementConfigRepository.AddAsync(config);
                return id > 0;
            }
        }

        public async Task<bool> DeleteStationAnnouncementConfigAsync(string stationName)
        {
            return await _announcementConfigRepository.DeleteByStationAsync(stationName);
        }

        public async Task<List<string>> GetEnabledLanguageCodesAsync(string stationName)
        {
            return await _languageConfigRepository.GetEnabledLanguageCodesAsync(stationName);
        }

        public async Task<Dictionary<string, string>> GetWaveFilePathsAsync(string stationName)
        {
            return await _languageConfigRepository.GetWaveFilePathsAsync(stationName);
        }

        public async Task<bool> SetStationWaveFileAsync(string stationName, string languageCode, string waveFilePath)
        {
            var config = await _languageConfigRepository.GetByStationAndLanguageAsync(stationName, languageCode);
            if (config == null)
            {
                // Create new config if it doesn't exist
                return await AddStationLanguageAsync(stationName, languageCode, true, waveFilePath);
            }

            config.WaveFilePath = waveFilePath;
            return await _languageConfigRepository.UpdateAsync(config);
        }

        public async Task<bool> ValidateStationLanguagesAsync(string stationName)
        {
            var enabledLanguages = await _languageConfigRepository.GetEnabledLanguageCodesAsync(stationName);
            if (!enabledLanguages.Any())
                return false;

            // Check if all enabled languages exist in the Languages table
            foreach (var languageCode in enabledLanguages)
            {
                var language = await _languageRepository.GetByCodeAsync(languageCode);
                if (language == null || !language.IsActive)
                    return false;
            }

            return true;
        }

        public async Task<List<Language>> GetAvailableLanguagesForStationAsync(string stationName)
        {
            var allLanguages = await _languageRepository.GetActiveAsync();
            var stationLanguages = await _languageConfigRepository.GetByStationNameAsync(stationName);
            var stationLanguageCodes = stationLanguages.Select(sl => sl.LanguageCode).ToHashSet();

            return allLanguages.Where(l => !stationLanguageCodes.Contains(l.Code)).ToList();
        }

        public async Task<bool> AddAllLanguagesToStationAsync(string stationName)
        {
            try
            {
                var allLanguages = await _languageRepository.GetActiveAsync();
                foreach (var language in allLanguages)
                {
                    if (!await _languageConfigRepository.ExistsAsync(stationName, language.Code))
                    {
                        await AddStationLanguageAsync(stationName, language.Code, true);
                    }
                }
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> RemoveAllLanguagesFromStationAsync(string stationName)
        {
            try
            {
                var stationLanguages = await _languageConfigRepository.GetByStationNameAsync(stationName);
                foreach (var stationLanguage in stationLanguages)
                {
                    await _languageConfigRepository.DeleteAsync(stationLanguage.Id);
                }
                return true;
            }
            catch
            {
                return false;
            }
        }
    }
} 