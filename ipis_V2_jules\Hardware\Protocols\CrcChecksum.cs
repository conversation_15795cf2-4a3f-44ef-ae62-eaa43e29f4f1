using System;

namespace ipis_V2_jules.Hardware.Protocols
{
    /// <summary>
    /// Provides CRC-16-CCITT (Kermit variant style) checksum calculation.
    /// Polynomial: 0x1021, Initial Value: 0xFFFF.
    /// The implementation details mirror the logic from the original system's Checksum.cs.
    /// </summary>
    public static class CrcChecksum
    {
        private const ushort Polynomial = 0x1021;
        private const ushort InitialCrcValue = 0xFFFF;

        /// <summary>
        /// Calculates the CRC for the given data and appends it to the packetData array.
        /// </summary>
        /// <param name="packetData">
        /// The byte array of the packet. The CRC will be written into this array.
        /// The array must be large enough to hold the data and the 2-byte CRC.
        /// </param>
        /// <param name="length">
        /// The total length of the packet data *including* the 2 bytes reserved for the checksum.
        /// For example, if you have 10 data bytes, length should be 12.
        /// The CRC is calculated on the first (length - 2) bytes.
        /// </param>
        /// <exception cref="ArgumentNullException">If packetData is null.</exception>
        /// <exception cref="ArgumentOutOfRangeException">
        /// If length is less than 2 (not enough space for CRC) or
        /// if packetData array length is less than specified length.
        /// </exception>
        public static void PrepareChecksum(ref byte[] packetData, ushort length)
        {
            if (packetData == null)
                throw new ArgumentNullException(nameof(packetData));

            if (length < 2)
                throw new ArgumentOutOfRangeException(nameof(length), "Length must be at least 2 to include CRC bytes.");

            if (packetData.Length < length)
                throw new ArgumentOutOfRangeException(nameof(packetData), "Packet data array is smaller than the specified length.");

            ushort crc = InitialCrcValue;
            ushort dataLengthForCrc = (ushort)(length - 2);

            for (int i = 0; i < dataLengthForCrc; i++)
            {
                // XOR the current data byte (shifted left by 8) with the CRC
                // This effectively treats the data byte as the MSB of a 16-bit word for XORing with the CRC
                crc ^= (ushort)(packetData[i] << 8);

                for (int bit = 0; bit < 8; bit++)
                {
                    if ((crc & 0x8000) != 0) // Check if MSB of CRC is 1
                    {
                        crc = (ushort)((crc << 1) ^ Polynomial);
                    }
                    else
                    {
                        crc <<= 1;
                    }
                }
            }

            packetData[dataLengthForCrc] = (byte)(crc >> 8);     // MSB of CRC
            packetData[dataLengthForCrc + 1] = (byte)(crc & 0xFF); // LSB of CRC
        }

        /// <summary>
        /// Validates the checksum of a received packet.
        /// </summary>
        /// <param name="receivedPacket">The byte array including the received checksum.</param>
        /// <param name="packetLengthWithChecksum">The total length of the packet, including the 2 checksum bytes.</param>
        /// <returns>True if the calculated CRC matches the received CRC, false otherwise.</returns>
        /// <exception cref="ArgumentNullException">If receivedPacket is null.</exception>
        /// <exception cref="ArgumentOutOfRangeException">
        /// If packetLengthWithChecksum is less than 2 (not enough space for CRC) or
        /// if receivedPacket array length is less than packetLengthWithChecksum.
        /// </exception>
        public static bool ValidateChecksum(byte[] receivedPacket, ushort packetLengthWithChecksum)
        {
            if (receivedPacket == null)
                throw new ArgumentNullException(nameof(receivedPacket));

            if (packetLengthWithChecksum < 2)
                throw new ArgumentOutOfRangeException(nameof(packetLengthWithChecksum), "Packet length must be at least 2 to include CRC bytes.");

            if (receivedPacket.Length < packetLengthWithChecksum)
                throw new ArgumentOutOfRangeException(nameof(receivedPacket), "Received packet array is smaller than the specified packet length.");

            ushort crcFromPacket = (ushort)((receivedPacket[packetLengthWithChecksum - 2] << 8) | receivedPacket[packetLengthWithChecksum - 1]);
            ushort dataLengthForCrc = (ushort)(packetLengthWithChecksum - 2);
            ushort calculatedCrc = InitialCrcValue;

            for (int i = 0; i < dataLengthForCrc; i++)
            {
                calculatedCrc ^= (ushort)(receivedPacket[i] << 8);

                for (int bit = 0; bit < 8; bit++)
                {
                    if ((calculatedCrc & 0x8000) != 0)
                    {
                        calculatedCrc = (ushort)((calculatedCrc << 1) ^ Polynomial);
                    }
                    else
                    {
                        calculatedCrc <<= 1;
                    }
                }
            }
            return calculatedCrc == crcFromPacket;
        }
    }
}
