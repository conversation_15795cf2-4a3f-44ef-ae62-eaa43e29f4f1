// Decompiled with JetBrains decompiler
// Type: ipis.intensity_Setting
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Diagnostics;

namespace ipis
{

public class intensity_Setting
{
  [DebuggerNonUserCode]
  public intensity_Setting()
  {
  }

  public static void intensity_load(
    ref string day_intensity,
    ref string night_intensity,
    ref DateTime day_time,
    ref DateTime night_time)
  {
    try
    {
      byte day_intensity1 = 0;
      byte night_intensity1 = 0;
      network_db_read.get_intensity(ref day_intensity1, ref night_intensity1, ref day_time, ref night_time);
      switch (day_intensity1)
      {
        case 0:
          day_intensity = "100%";
          break;
        case 1:
          day_intensity = "75%";
          break;
        case 2:
          day_intensity = "50%";
          break;
        case 3:
          day_intensity = "25%";
          break;
      }
      switch (night_intensity1)
      {
        case 0:
          night_intensity = "100%";
          break;
        case 1:
          night_intensity = "75%";
          break;
        case 2:
          night_intensity = "50%";
          break;
        case 3:
          night_intensity = "25%";
          break;
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }
}

}