using System;
using System.IO;
using IPIS.Models;
using IPIS.Repositories.Interfaces;

namespace IPIS.Services
{
    public class LoginConfigurationService
    {
        private readonly ILoginConfigurationRepository _repository;
        private LoginConfiguration _cachedConfiguration;

        public LoginConfigurationService(ILoginConfigurationRepository repository)
        {
            _repository = repository;
        }

        public LoginConfiguration GetLoginConfiguration()
        {
            if (_cachedConfiguration == null)
            {
                _cachedConfiguration = _repository.GetLoginConfiguration();

                // If no configuration exists, create a default one
                if (_cachedConfiguration.Id == 0 && !_repository.ConfigurationExists())
                {
                    _cachedConfiguration = CreateDefaultConfiguration();
                    SaveLoginConfiguration(_cachedConfiguration);
                }
            }

            return _cachedConfiguration;
        }

        public void SaveLoginConfiguration(LoginConfiguration configuration)
        {
            if (configuration == null)
                throw new ArgumentNullException(nameof(configuration));

            configuration.UpdatedAt = DateTime.Now;

            if (configuration.Id == 0)
            {
                configuration.CreatedAt = DateTime.Now;
                _repository.SaveLoginConfiguration(configuration);
            }
            else
            {
                _repository.UpdateLoginConfiguration(configuration);
            }

            // Update cache
            _cachedConfiguration = configuration;
        }

        public void UpdateLoginConfiguration(LoginConfiguration configuration)
        {
            if (configuration == null)
                throw new ArgumentNullException(nameof(configuration));

            // Get existing configuration to preserve ID
            var existing = GetLoginConfiguration();
            configuration.Id = existing.Id;
            configuration.CreatedAt = existing.CreatedAt;

            SaveLoginConfiguration(configuration);
        }

        public void RefreshCache()
        {
            _cachedConfiguration = null;
        }

        public bool ValidateImagePath(string imagePath)
        {
            if (string.IsNullOrWhiteSpace(imagePath))
                return true; // Empty path is valid (means no custom image)

            try
            {
                return File.Exists(imagePath) && IsImageFile(imagePath);
            }
            catch
            {
                return false;
            }
        }

        public string CopyImageToAppDirectory(string sourcePath, string imageType)
        {
            if (string.IsNullOrWhiteSpace(sourcePath) || !File.Exists(sourcePath))
                return "";

            try
            {
                // Create images directory if it doesn't exist
                string imagesDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Images", "Login");
                Directory.CreateDirectory(imagesDir);

                // Generate unique filename
                string extension = Path.GetExtension(sourcePath);
                string fileName = $"{imageType}_{DateTime.Now:yyyyMMdd_HHmmss}{extension}";
                string destinationPath = Path.Combine(imagesDir, fileName);

                // Copy file
                File.Copy(sourcePath, destinationPath, true);

                return destinationPath;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to copy image: {ex.Message}", ex);
            }
        }

        private bool IsImageFile(string filePath)
        {
            string[] validExtensions = { ".jpg", ".jpeg", ".png", ".bmp", ".gif", ".ico" };
            string extension = Path.GetExtension(filePath).ToLowerInvariant();
            return Array.Exists(validExtensions, ext => ext == extension);
        }

        private LoginConfiguration CreateDefaultConfiguration()
        {
            return new LoginConfiguration
            {
                StationName = "IPIS",
                WelcomeMessage = "Welcome to",
                SubtitleMessage = "Integrated Passenger\nInformation System",
                LogoPath = "",
                BackgroundImagePath = "",
                PrimaryColor = "#007BFF",
                SecondaryColor = "#C8DCFF",
                BackgroundColor = "#F0F4F8",
                StationTextColor = "#333333",
                UseCustomLogo = false,
                UseBackgroundImage = false,
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now
            };
        }
    }
}
