// Decompiled with JetBrains decompiler
// Type: ipis.frmChangeAccountType
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

namespace ipis
{

[DesignerGenerated]
public class frmChangeAccountType : Form
{
  private static List<WeakReference> __ENCList = new List<WeakReference>();
  private IContainer components;
  [AccessedThroughProperty("btnOk")]
  private Button _btnOk;
  [AccessedThroughProperty("radAdmin")]
  private RadioButton _radAdmin;
  [AccessedThroughProperty("radNormal")]
  private RadioButton _radNormal;
  [AccessedThroughProperty("Button1")]
  private Button _Button1;

  [DebuggerNonUserCode]
  static frmChangeAccountType()
  {
  }

  [DebuggerNonUserCode]
  public frmChangeAccountType()
  {
    frmChangeAccountType.__ENCAddToList((object) this);
    this.InitializeComponent();
  }

  [DebuggerNonUserCode]
  private static void __ENCAddToList(object value)
  {
    lock (frmChangeAccountType.__ENCList)
    {
      if (frmChangeAccountType.__ENCList.Count == frmChangeAccountType.__ENCList.Capacity)
      {
        int index1 = 0;
        int num = checked (frmChangeAccountType.__ENCList.Count - 1);
        int index2 = 0;
        while (index2 <= num)
        {
          if (frmChangeAccountType.__ENCList[index2].IsAlive)
          {
            if (index2 != index1)
              frmChangeAccountType.__ENCList[index1] = frmChangeAccountType.__ENCList[index2];
            checked { ++index1; }
          }
          checked { ++index2; }
        }
        frmChangeAccountType.__ENCList.RemoveRange(index1, checked (frmChangeAccountType.__ENCList.Count - index1));
        frmChangeAccountType.__ENCList.Capacity = frmChangeAccountType.__ENCList.Count;
      }
      frmChangeAccountType.__ENCList.Add(new WeakReference(RuntimeHelpers.GetObjectValue(value)));
    }
  }

  [DebuggerNonUserCode]
  protected override void Dispose(bool disposing)
  {
    try
    {
      if (!disposing || this.components == null)
        return;
      this.components.Dispose();
    }
    finally
    {
      base.Dispose(disposing);
    }
  }

  [DebuggerStepThrough]
  private void InitializeComponent()
  {
    this.btnOk = new Button();
    this.radAdmin = new RadioButton();
    this.radNormal = new RadioButton();
    this.Button1 = new Button();
    this.SuspendLayout();
    this.btnOk.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold);
    Button btnOk1 = this.btnOk;
    Point point1 = new Point(12, 103);
    Point point2 = point1;
    btnOk1.Location = point2;
    this.btnOk.Name = "btnOk";
    Button btnOk2 = this.btnOk;
    Size size1 = new Size(181, 23);
    Size size2 = size1;
    btnOk2.Size = size2;
    this.btnOk.TabIndex = 2;
    this.btnOk.Text = "Change Account Type";
    this.btnOk.UseVisualStyleBackColor = true;
    this.radAdmin.AutoSize = true;
    this.radAdmin.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold);
    RadioButton radAdmin1 = this.radAdmin;
    point1 = new Point(92, 22);
    Point point3 = point1;
    radAdmin1.Location = point3;
    this.radAdmin.Name = "radAdmin";
    RadioButton radAdmin2 = this.radAdmin;
    size1 = new Size(69, 20);
    Size size3 = size1;
    radAdmin2.Size = size3;
    this.radAdmin.TabIndex = 3;
    this.radAdmin.TabStop = true;
    this.radAdmin.Text = "Admin";
    this.radAdmin.UseVisualStyleBackColor = true;
    this.radNormal.AutoSize = true;
    this.radNormal.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold);
    RadioButton radNormal1 = this.radNormal;
    point1 = new Point(92, 57);
    Point point4 = point1;
    radNormal1.Location = point4;
    this.radNormal.Name = "radNormal";
    RadioButton radNormal2 = this.radNormal;
    size1 = new Size(76, 20);
    Size size4 = size1;
    radNormal2.Size = size4;
    this.radNormal.TabIndex = 4;
    this.radNormal.TabStop = true;
    this.radNormal.Text = "Normal";
    this.radNormal.UseVisualStyleBackColor = true;
    this.Button1.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold);
    Button button1_1 = this.Button1;
    point1 = new Point(218, 103);
    Point point5 = point1;
    button1_1.Location = point5;
    this.Button1.Name = "Button1";
    Button button1_2 = this.Button1;
    size1 = new Size(75, 23);
    Size size5 = size1;
    button1_2.Size = size5;
    this.Button1.TabIndex = 5;
    this.Button1.Text = "Exit";
    this.Button1.UseVisualStyleBackColor = true;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.BackColor = Color.PowderBlue;
    size1 = new Size(313, 138);
    this.ClientSize = size1;
    this.Controls.Add((Control) this.Button1);
    this.Controls.Add((Control) this.radNormal);
    this.Controls.Add((Control) this.radAdmin);
    this.Controls.Add((Control) this.btnOk);
    this.FormBorderStyle = FormBorderStyle.FixedToolWindow;
    this.Name = "frmChangeAccountType";
    this.Text = "Change Account Type";
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  internal virtual Button btnOk
  {
    [DebuggerNonUserCode] get { return this._btnOk; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnOk_Click);
      if (this._btnOk != null)
        this._btnOk.Click -= eventHandler;
      this._btnOk = value;
      if (this._btnOk == null)
        return;
      this._btnOk.Click += eventHandler;
    }
  }

  internal virtual RadioButton radAdmin
  {
    [DebuggerNonUserCode] get { return this._radAdmin; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._radAdmin = value; }
  }

  internal virtual RadioButton radNormal
  {
    [DebuggerNonUserCode] get { return this._radNormal; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._radNormal = value;
    }
  }

  internal virtual Button Button1
  {
    [DebuggerNonUserCode] get { return this._Button1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.Button1_Click);
      if (this._Button1 != null)
        this._Button1.Click -= eventHandler;
      this._Button1 = value;
      if (this._Button1 == null)
        return;
      this._Button1.Click += eventHandler;
    }
  }

  private void Button1_Click(object sender, EventArgs e)
{
  this.Close();
}

  private void btnOk_Click(object sender, EventArgs e)
  {
    string empty = string.Empty;
    string group = !this.radAdmin.Checked ? "Normal" : "Admin";
    network_db_read.set_user_group(ref group, frmChangeUserDetails.user_name);
    int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Account Type Changed", "Msg Box", 0, 0, 0);
    this.Close();
  }
}

}