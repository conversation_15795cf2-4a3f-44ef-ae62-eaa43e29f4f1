// Decompiled with JetBrains decompiler
// Type: ipis.agdb_byte_construct
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Diagnostics;
using System.Text;

namespace ipis
{

public class agdb_byte_construct
{
  [DebuggerNonUserCode]
  public agdb_byte_construct()
  {
  }

  public static void train_inf_data_bytes(
    string train_no,
    string train_name,
    string arrivalordeparture_time,
    string ad,
    string platform_no,
    ref byte[] agdb_msg)
  {
    byte[] numArray = new byte[4];
    int num1 = 0;
    StringBuilder stringBuilder1 = new StringBuilder(train_no);
    StringBuilder stringBuilder2 = new StringBuilder(platform_no);
    int index = 0;
    while (index < agdb_msg.Length)
    {
      agdb_msg[index] = (byte) 0;
      checked { ++index; }
    }
    num1 = 0;
    switch (checked ((byte) stringBuilder1.Length))
    {
      case 2:
        string train_number1 = Conversions.ToString(stringBuilder1[0]);
        byte num2 = 0;
        byte local1 = num2;
        byte[] local2 = agdb_msg;
        agdb_byte_construct.train_number_byte(train_number1, ref local1, ref local2);
        string train_number2 = Conversions.ToString(stringBuilder1[1]);
        byte num3 = 6;
        byte local3 = num3;
        byte[] local4 = agdb_msg;
        agdb_byte_construct.train_number_byte(train_number2, ref local3, ref local4);
        break;
      case 3:
        string train_number3 = Conversions.ToString(stringBuilder1[0]);
        byte num4 = 0;
        byte local5 = num4;
        byte[] local6 = agdb_msg;
        agdb_byte_construct.train_number_byte(train_number3, ref local5, ref local6);
        string train_number4 = Conversions.ToString(stringBuilder1[1]);
        byte num5 = 6;
        byte local7 = num5;
        byte[] local8 = agdb_msg;
        agdb_byte_construct.train_number_byte(train_number4, ref local7, ref local8);
        string train_number5 = Conversions.ToString(stringBuilder1[2]);
        byte num6 = 12;
        byte local9 = num6;
        byte[] local10 = agdb_msg;
        agdb_byte_construct.train_number_byte(train_number5, ref local9, ref local10);
        break;
      case 4:
        string train_number6 = Conversions.ToString(stringBuilder1[0]);
        byte num7 = 0;
        byte local11 = num7;
        byte[] local12 = agdb_msg;
        agdb_byte_construct.train_number_byte(train_number6, ref local11, ref local12);
        string train_number7 = Conversions.ToString(stringBuilder1[1]);
        byte num8 = 6;
        byte local13 = num8;
        byte[] local14 = agdb_msg;
        agdb_byte_construct.train_number_byte(train_number7, ref local13, ref local14);
        string train_number8 = Conversions.ToString(stringBuilder1[2]);
        byte num9 = 12;
        byte local15 = num9;
        byte[] local16 = agdb_msg;
        agdb_byte_construct.train_number_byte(train_number8, ref local15, ref local16);
        string train_number9 = Conversions.ToString(stringBuilder1[3]);
        byte num10 = 18;
        byte local17 = num10;
        byte[] local18 = agdb_msg;
        agdb_byte_construct.train_number_byte(train_number9, ref local17, ref local18);
        break;
      case 5:
        string train_number10 = Conversions.ToString(stringBuilder1[0]);
        byte num11 = 0;
        byte local19 = num11;
        byte[] local20 = agdb_msg;
        agdb_byte_construct.train_number_byte(train_number10, ref local19, ref local20);
        string train_number11 = Conversions.ToString(stringBuilder1[1]);
        byte num12 = 6;
        byte local21 = num12;
        byte[] local22 = agdb_msg;
        agdb_byte_construct.train_number_byte(train_number11, ref local21, ref local22);
        string train_number12 = Conversions.ToString(stringBuilder1[2]);
        byte num13 = 12;
        byte local23 = num13;
        byte[] local24 = agdb_msg;
        agdb_byte_construct.train_number_byte(train_number12, ref local23, ref local24);
        string train_number13 = Conversions.ToString(stringBuilder1[3]);
        byte num14 = 18;
        byte local25 = num14;
        byte[] local26 = agdb_msg;
        agdb_byte_construct.train_number_byte(train_number13, ref local25, ref local26);
        string train_number14 = Conversions.ToString(stringBuilder1[4]);
        byte num15 = 24;
        byte local27 = num15;
        byte[] local28 = agdb_msg;
        agdb_byte_construct.train_number_byte(train_number14, ref local27, ref local28);
        break;
    }
    string train_name1 = train_name;
    byte num16 = 32 /*0x20*/;
    byte local29 = num16;
    byte[] local30 = agdb_msg;
    agdb_byte_construct.name_byte(train_name1, ref local29, ref local30);
    string train_name2 = arrivalordeparture_time;
    byte count = 181;
    byte local31 = count;
    byte[] local32 = agdb_msg;
    agdb_byte_construct.name_byte(train_name2, ref local31, ref local32);
    string train_name3 = ad;
    count = (byte) 210;
    byte local33 = count;
    byte[] local34 = agdb_msg;
    agdb_byte_construct.name_byte(train_name3, ref local33, ref local34);
    switch (checked ((byte) stringBuilder2.Length))
    {
      case 1:
        count = (byte) 222;
        agdb_byte_construct.platform_number_byte(" ", ref count, ref agdb_msg);
        count = (byte) 228;
        agdb_byte_construct.platform_number_byte(" ", ref count, ref agdb_msg);
        string platform_no1 = Conversions.ToString(stringBuilder2[0]);
        count = (byte) 234;
        byte local35 = count;
        byte[] local36 = agdb_msg;
        agdb_byte_construct.platform_number_byte(platform_no1, ref local35, ref local36);
        break;
      case 2:
        count = (byte) 222;
        agdb_byte_construct.platform_number_byte(" ", ref count, ref agdb_msg);
        string platform_no2 = Conversions.ToString(stringBuilder2[0]);
        count = (byte) 228;
        byte local37 = count;
        byte[] local38 = agdb_msg;
        agdb_byte_construct.platform_number_byte(platform_no2, ref local37, ref local38);
        string platform_no3 = Conversions.ToString(stringBuilder2[1]);
        count = (byte) 234;
        byte local39 = count;
        byte[] local40 = agdb_msg;
        agdb_byte_construct.platform_number_byte(platform_no3, ref local39, ref local40);
        break;
      case 3:
        string platform_no4 = Conversions.ToString(stringBuilder2[0]);
        count = (byte) 222;
        byte local41 = count;
        byte[] local42 = agdb_msg;
        agdb_byte_construct.platform_number_byte(platform_no4, ref local41, ref local42);
        string platform_no5 = Conversions.ToString(stringBuilder2[1]);
        count = (byte) 228;
        byte local43 = count;
        byte[] local44 = agdb_msg;
        agdb_byte_construct.platform_number_byte(platform_no5, ref local43, ref local44);
        string platform_no6 = Conversions.ToString(stringBuilder2[2]);
        count = (byte) 234;
        byte local45 = count;
        byte[] local46 = agdb_msg;
        agdb_byte_construct.platform_number_byte(platform_no6, ref local45, ref local46);
        break;
    }
  }

  public static void cgs_inf_data_bytes(
    string[] cgs_array,
    byte coach_count,
    string ad,
    ref byte[] agdb_line2_bitmap,
    ref byte[] agdb_line3_bitmap)
  {
    int index1 = 0;
    int num1 = 0;
    byte num2 = 0;
    byte num3 = 0;
    while (index1 < 26)
    {
      if (!(Operators.CompareString(cgs_array[index1], "", false) == 0 | cgs_array[index1] == null))
      {
        string cgs = cgs_array[index1];
        num2 = checked ((byte) num1);
        byte local1 = num2;
        byte[] local2 = agdb_line2_bitmap;
        agdb_byte_construct.name_byte(cgs, ref local1, ref local2);
        num1 = (int) num2;
        if (index1 < 25)
        {
          if (cgs_array[checked (index1 + 1)].Length == 2)
            num3 = (byte) 12;
          else if (cgs_array[checked (index1 + 1)].Length == 3)
            num3 = (byte) 18;
          checked { num1 += 2; }
        }
        if (checked (240 /*0xF0*/ - num1) < (int) num3)
        {
          checked { ++index1; }
          break;
        }
      }
      checked { ++index1; }
    }
    int index2 = 0;
    try
    {
      while (index1 <= 26)
      {
        if (!(Operators.CompareString(cgs_array[index1], "", false) == 0 | cgs_array[index1] == null))
        {
          string cgs = cgs_array[index1];
          num2 = checked ((byte) index2);
          byte local3 = num2;
          byte[] local4 = agdb_line3_bitmap;
          agdb_byte_construct.name_byte(cgs, ref local3, ref local4);
          index2 = (int) num2;
          if (index1 < 25)
          {
            if (cgs_array[checked (index1 + 1)].Length == 2)
              num3 = (byte) 12;
            else if (cgs_array[checked (index1 + 1)].Length == 3)
              num3 = (byte) 18;
            checked { index2 += 2; }
          }
          if (checked (240 /*0xF0*/ - index2) < (int) num3)
            break;
        }
        checked { ++index1; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
    while (index2 < 240 /*0xF0*/)
    {
      agdb_line3_bitmap[index2] = (byte) 0;
      checked { ++index2; }
    }
  }

  public static void number_byte(int number, ref byte count, ref byte[] agdb_bytearray)
  {
    byte[] numArray = new byte[5];
    switch (number)
    {
      case 0:
        numArray = agdb_lookup_table.BYTE_ZERO_0;
        break;
      case 1:
        numArray = agdb_lookup_table.BYTE_ONE_1;
        break;
      case 2:
        numArray = agdb_lookup_table.BYTE_TWO_2;
        break;
      case 3:
        numArray = agdb_lookup_table.BYTE_THREE_3;
        break;
      case 4:
        numArray = agdb_lookup_table.BYTE_FOUR_4;
        break;
      case 5:
        numArray = agdb_lookup_table.BYTE_FIVE_5;
        break;
      case 6:
        numArray = agdb_lookup_table.BYTE_SIX_6;
        break;
      case 7:
        numArray = agdb_lookup_table.BYTE_SEVEN_7;
        break;
      case 8:
        numArray = agdb_lookup_table.BYTE_EIGHT_8;
        break;
      case 9:
        numArray = agdb_lookup_table.BYTE_NINE_9;
        break;
    }
    byte index = 0;
    int num = (int) count;
    while ((int) count < checked (num + 5))
    {
      agdb_bytearray[(int) count] = numArray[(int) index];
      checked { ++count; }
      checked { ++index; }
    }
    checked { ++count; }
  }

  public static void train_number_byte(
    string train_number,
    ref byte count,
    ref byte[] agdb_bytearray)
  {
    byte[] numArray = new byte[5];
    string Left = train_number;
    if (Operators.CompareString(Left, Conversions.ToString(0), false) == 0)
      numArray = agdb_lookup_table.BYTE_ZERO_0;
    else if (Operators.CompareString(Left, Conversions.ToString(1), false) == 0)
      numArray = agdb_lookup_table.BYTE_ONE_1;
    else if (Operators.CompareString(Left, Conversions.ToString(2), false) == 0)
      numArray = agdb_lookup_table.BYTE_TWO_2;
    else if (Operators.CompareString(Left, Conversions.ToString(3), false) == 0)
      numArray = agdb_lookup_table.BYTE_THREE_3;
    else if (Operators.CompareString(Left, Conversions.ToString(4), false) == 0)
      numArray = agdb_lookup_table.BYTE_FOUR_4;
    else if (Operators.CompareString(Left, Conversions.ToString(5), false) == 0)
      numArray = agdb_lookup_table.BYTE_FIVE_5;
    else if (Operators.CompareString(Left, Conversions.ToString(6), false) == 0)
      numArray = agdb_lookup_table.BYTE_SIX_6;
    else if (Operators.CompareString(Left, Conversions.ToString(7), false) == 0)
      numArray = agdb_lookup_table.BYTE_SEVEN_7;
    else if (Operators.CompareString(Left, Conversions.ToString(8), false) == 0)
      numArray = agdb_lookup_table.BYTE_EIGHT_8;
    else if (Operators.CompareString(Left, Conversions.ToString(9), false) == 0)
      numArray = agdb_lookup_table.BYTE_NINE_9;
    else if (Operators.CompareString(Left, "A", false) == 0)
      numArray = agdb_lookup_table.A_A;
    else if (Operators.CompareString(Left, "B", false) == 0)
      numArray = agdb_lookup_table.B_B;
    else if (Operators.CompareString(Left, "C", false) == 0)
      numArray = agdb_lookup_table.C_C;
    else if (Operators.CompareString(Left, "D", false) == 0)
      numArray = agdb_lookup_table.D_D;
    else if (Operators.CompareString(Left, "E", false) == 0)
      numArray = agdb_lookup_table.E_E;
    else if (Operators.CompareString(Left, "F", false) == 0)
      numArray = agdb_lookup_table.F_F;
    else if (Operators.CompareString(Left, "G", false) == 0)
      numArray = agdb_lookup_table.G_G;
    else if (Operators.CompareString(Left, "H", false) == 0)
      numArray = agdb_lookup_table.H_H;
    else if (Operators.CompareString(Left, "I", false) == 0)
      numArray = agdb_lookup_table.I_I;
    else if (Operators.CompareString(Left, "J", false) == 0)
      numArray = agdb_lookup_table.J_J;
    else if (Operators.CompareString(Left, "K", false) == 0)
      numArray = agdb_lookup_table.K_K;
    else if (Operators.CompareString(Left, "L", false) == 0)
      numArray = agdb_lookup_table.L_L;
    else if (Operators.CompareString(Left, "M", false) == 0)
      numArray = agdb_lookup_table.M_M;
    else if (Operators.CompareString(Left, "N", false) == 0)
      numArray = agdb_lookup_table.N_N;
    else if (Operators.CompareString(Left, "O", false) == 0)
      numArray = agdb_lookup_table.O_O;
    else if (Operators.CompareString(Left, "P", false) == 0)
      numArray = agdb_lookup_table.P_P;
    else if (Operators.CompareString(Left, "Q", false) == 0)
      numArray = agdb_lookup_table.Q_Q;
    else if (Operators.CompareString(Left, "R", false) == 0)
      numArray = agdb_lookup_table.R_R;
    else if (Operators.CompareString(Left, "S", false) == 0)
      numArray = agdb_lookup_table.S_S;
    else if (Operators.CompareString(Left, "T", false) == 0)
      numArray = agdb_lookup_table.T_T;
    else if (Operators.CompareString(Left, "U", false) == 0)
      numArray = agdb_lookup_table.U_U;
    else if (Operators.CompareString(Left, "V", false) == 0)
      numArray = agdb_lookup_table.V_V;
    else if (Operators.CompareString(Left, "W", false) == 0)
      numArray = agdb_lookup_table.W_W;
    else if (Operators.CompareString(Left, "X", false) == 0)
      numArray = agdb_lookup_table.X_X;
    else if (Operators.CompareString(Left, "Y", false) == 0)
      numArray = agdb_lookup_table.Y_Y;
    else if (Operators.CompareString(Left, "Z", false) == 0)
      numArray = agdb_lookup_table.Z_Z;
    else if (Operators.CompareString(Left, " ", false) == 0)
      numArray = agdb_lookup_table.BYTE_SPACE;
    byte index = 0;
    int num = (int) count;
    while ((int) count < checked (num + 5))
    {
      agdb_bytearray[(int) count] = numArray[(int) index];
      checked { ++count; }
      checked { ++index; }
    }
    checked { ++count; }
  }

  public static void platform_number_byte(
    string platform_no,
    ref byte count,
    ref byte[] agdb_bytearray)
  {
    byte[] numArray = new byte[5];
    string Left = platform_no;
    if (Operators.CompareString(Left, Conversions.ToString(0), false) == 0)
      numArray = agdb_lookup_table.BYTE_ZERO_0;
    else if (Operators.CompareString(Left, Conversions.ToString(1), false) == 0)
      numArray = agdb_lookup_table.BYTE_ONE_1;
    else if (Operators.CompareString(Left, Conversions.ToString(2), false) == 0)
      numArray = agdb_lookup_table.BYTE_TWO_2;
    else if (Operators.CompareString(Left, Conversions.ToString(3), false) == 0)
      numArray = agdb_lookup_table.BYTE_THREE_3;
    else if (Operators.CompareString(Left, Conversions.ToString(4), false) == 0)
      numArray = agdb_lookup_table.BYTE_FOUR_4;
    else if (Operators.CompareString(Left, Conversions.ToString(5), false) == 0)
      numArray = agdb_lookup_table.BYTE_FIVE_5;
    else if (Operators.CompareString(Left, Conversions.ToString(6), false) == 0)
      numArray = agdb_lookup_table.BYTE_SIX_6;
    else if (Operators.CompareString(Left, Conversions.ToString(7), false) == 0)
      numArray = agdb_lookup_table.BYTE_SEVEN_7;
    else if (Operators.CompareString(Left, Conversions.ToString(8), false) == 0)
      numArray = agdb_lookup_table.BYTE_EIGHT_8;
    else if (Operators.CompareString(Left, Conversions.ToString(9), false) == 0)
      numArray = agdb_lookup_table.BYTE_NINE_9;
    else if (Operators.CompareString(Left, "A", false) == 0)
      numArray = agdb_lookup_table.A_A;
    else if (Operators.CompareString(Left, "B", false) == 0)
      numArray = agdb_lookup_table.B_B;
    else if (Operators.CompareString(Left, "C", false) == 0)
      numArray = agdb_lookup_table.C_C;
    else if (Operators.CompareString(Left, "D", false) == 0)
      numArray = agdb_lookup_table.D_D;
    else if (Operators.CompareString(Left, "E", false) == 0)
      numArray = agdb_lookup_table.E_E;
    else if (Operators.CompareString(Left, "F", false) == 0)
      numArray = agdb_lookup_table.F_F;
    else if (Operators.CompareString(Left, "G", false) == 0)
      numArray = agdb_lookup_table.G_G;
    else if (Operators.CompareString(Left, "H", false) == 0)
      numArray = agdb_lookup_table.H_H;
    else if (Operators.CompareString(Left, "I", false) == 0)
      numArray = agdb_lookup_table.I_I;
    else if (Operators.CompareString(Left, "J", false) == 0)
      numArray = agdb_lookup_table.J_J;
    else if (Operators.CompareString(Left, "K", false) == 0)
      numArray = agdb_lookup_table.K_K;
    else if (Operators.CompareString(Left, "L", false) == 0)
      numArray = agdb_lookup_table.L_L;
    else if (Operators.CompareString(Left, "M", false) == 0)
      numArray = agdb_lookup_table.M_M;
    else if (Operators.CompareString(Left, "N", false) == 0)
      numArray = agdb_lookup_table.N_N;
    else if (Operators.CompareString(Left, "O", false) == 0)
      numArray = agdb_lookup_table.O_O;
    else if (Operators.CompareString(Left, "P", false) == 0)
      numArray = agdb_lookup_table.P_P;
    else if (Operators.CompareString(Left, "Q", false) == 0)
      numArray = agdb_lookup_table.Q_Q;
    else if (Operators.CompareString(Left, "R", false) == 0)
      numArray = agdb_lookup_table.R_R;
    else if (Operators.CompareString(Left, "S", false) == 0)
      numArray = agdb_lookup_table.S_S;
    else if (Operators.CompareString(Left, "T", false) == 0)
      numArray = agdb_lookup_table.T_T;
    else if (Operators.CompareString(Left, "U", false) == 0)
      numArray = agdb_lookup_table.U_U;
    else if (Operators.CompareString(Left, "V", false) == 0)
      numArray = agdb_lookup_table.V_V;
    else if (Operators.CompareString(Left, "W", false) == 0)
      numArray = agdb_lookup_table.W_W;
    else if (Operators.CompareString(Left, "X", false) == 0)
      numArray = agdb_lookup_table.X_X;
    else if (Operators.CompareString(Left, "Y", false) == 0)
      numArray = agdb_lookup_table.Y_Y;
    else if (Operators.CompareString(Left, "Z", false) == 0)
      numArray = agdb_lookup_table.Z_Z;
    else if (Operators.CompareString(Left, " ", false) == 0)
      numArray = agdb_lookup_table.BYTE_SPACE;
    byte index = 0;
    int num = (int) count;
    while ((int) count < checked (num + 5))
    {
      agdb_bytearray[(int) count] = numArray[(int) index];
      checked { ++count; }
      checked { ++index; }
    }
    checked { ++count; }
  }

  public static void name_byte(string train_name, ref byte count, ref byte[] agdb_bytearray)
  {
    byte[] numArray = new byte[5];
    StringBuilder stringBuilder = new StringBuilder(train_name);
    byte index1 = 0;
    byte num1 = 0;
    if (train_name == null | Operators.CompareString(train_name, "", false) == 0)
    {
      train_name = " ";
      byte num2 = 0;
      index1 = num2;
    }
    else
      num1 = checked ((byte) train_name.Length);
    if (num1 > (byte) 24)
      num1 = (byte) 24;
    while ((uint) index1 < (uint) num1)
    {
      byte num3 = 5;
      byte byteColon = 0;
      switch ((char) ((int) stringBuilder[(int) index1] - 32 /*0x20*/))
      {
        case char.MinValue:
          numArray = agdb_lookup_table.BYTE_SPACE;
          break;
        case '\u0001':
          numArray = agdb_lookup_table.SIGN_LINE;
          num3 = (byte) 2;
          break;
        case '\u0010':
          numArray = agdb_lookup_table.BYTE_ZERO_0;
          break;
        case '\u0011':
          numArray = agdb_lookup_table.BYTE_ONE_1;
          break;
        case '\u0012':
          numArray = agdb_lookup_table.BYTE_TWO_2;
          break;
        case '\u0013':
          numArray = agdb_lookup_table.BYTE_THREE_3;
          break;
        case '\u0014':
          numArray = agdb_lookup_table.BYTE_FOUR_4;
          break;
        case '\u0015':
          numArray = agdb_lookup_table.BYTE_FIVE_5;
          break;
        case '\u0016':
          numArray = agdb_lookup_table.BYTE_SIX_6;
          break;
        case '\u0017':
          numArray = agdb_lookup_table.BYTE_SEVEN_7;
          break;
        case '\u0018':
          numArray = agdb_lookup_table.BYTE_EIGHT_8;
          break;
        case '\u0019':
          numArray = agdb_lookup_table.BYTE_NINE_9;
          break;
        case '\u001A':
          byteColon = agdb_lookup_table.BYTE_COLON;
          num3 = (byte) 1;
          break;
        case '\u001C':
          numArray = agdb_lookup_table.SIGN_LEFT;
          break;
        case '\u001E':
          numArray = agdb_lookup_table.SIGN_RIGHT;
          break;
        case '!':
          numArray = agdb_lookup_table.A_A;
          break;
        case '"':
          numArray = agdb_lookup_table.B_B;
          break;
        case '#':
          numArray = agdb_lookup_table.C_C;
          break;
        case '$':
          numArray = agdb_lookup_table.D_D;
          break;
        case '%':
          numArray = agdb_lookup_table.E_E;
          break;
        case '&':
          numArray = agdb_lookup_table.F_F;
          break;
        case '\'':
          numArray = agdb_lookup_table.G_G;
          break;
        case '(':
          numArray = agdb_lookup_table.H_H;
          break;
        case ')':
          numArray = agdb_lookup_table.I_I;
          break;
        case '*':
          numArray = agdb_lookup_table.J_J;
          break;
        case '+':
          numArray = agdb_lookup_table.K_K;
          break;
        case ',':
          numArray = agdb_lookup_table.L_L;
          break;
        case '-':
          numArray = agdb_lookup_table.M_M;
          break;
        case '.':
          numArray = agdb_lookup_table.N_N;
          break;
        case '/':
          numArray = agdb_lookup_table.O_O;
          break;
        case '0':
          numArray = agdb_lookup_table.P_P;
          break;
        case '1':
          numArray = agdb_lookup_table.Q_Q;
          break;
        case '2':
          numArray = agdb_lookup_table.R_R;
          break;
        case '3':
          numArray = agdb_lookup_table.S_S;
          break;
        case '4':
          numArray = agdb_lookup_table.T_T;
          break;
        case '5':
          numArray = agdb_lookup_table.U_U;
          break;
        case '6':
          numArray = agdb_lookup_table.V_V;
          break;
        case '7':
          numArray = agdb_lookup_table.W_W;
          break;
        case '8':
          numArray = agdb_lookup_table.X_X;
          break;
        case '9':
          numArray = agdb_lookup_table.Y_Y;
          break;
        case ':':
          numArray = agdb_lookup_table.Z_Z;
          break;
      }
      try
      {
        byte index2 = 0;
        if (num3 == (byte) 1)
        {
          agdb_bytearray[(int) checked ((byte) unchecked ((int) count + (int) index2))] = byteColon;
        }
        else
        {
          while ((uint) index2 < (uint) num3)
          {
            agdb_bytearray[(int) checked ((byte) unchecked ((int) count + (int) index2))] = numArray[(int) index2];
            checked { ++index2; }
          }
        }
        count = checked ((byte) ((int) (byte) unchecked ((int) count + (int) num3) + 1));
      }
      catch (Exception ex)
      {
        ProjectData.SetProjectError(ex);
        ProjectData.ClearProjectError();
        break;
      }
      checked { ++index1; }
    }
  }

  public static void name_byte_default_msg(
    string train_name,
    ref byte count,
    ref byte[] agdb_bytearray)
  {
    byte[] numArray = new byte[5];
    StringBuilder stringBuilder = new StringBuilder(train_name);
    byte index1 = 0;
    byte length = 0;
    if (train_name == null | Operators.CompareString(train_name, " ", false) == 0)
    {
      train_name = " ";
      byte num = 0;
      index1 = num;
    }
    else
      length = checked ((byte) train_name.Length);
    while ((uint) index1 < (uint) length)
    {
      byte num = 5;
      byte byteColon = 0;
      switch ((char) ((int) stringBuilder[(int) index1] - 32 /*0x20*/))
      {
        case char.MinValue:
          numArray = agdb_lookup_table.BYTE_SPACE;
          break;
        case '\u0010':
          numArray = agdb_lookup_table.BYTE_ZERO_0;
          break;
        case '\u0011':
          numArray = agdb_lookup_table.BYTE_ONE_1;
          break;
        case '\u0012':
          numArray = agdb_lookup_table.BYTE_TWO_2;
          break;
        case '\u0013':
          numArray = agdb_lookup_table.BYTE_THREE_3;
          break;
        case '\u0014':
          numArray = agdb_lookup_table.BYTE_FOUR_4;
          break;
        case '\u0015':
          numArray = agdb_lookup_table.BYTE_FIVE_5;
          break;
        case '\u0016':
          numArray = agdb_lookup_table.BYTE_SIX_6;
          break;
        case '\u0017':
          numArray = agdb_lookup_table.BYTE_SEVEN_7;
          break;
        case '\u0018':
          numArray = agdb_lookup_table.BYTE_EIGHT_8;
          break;
        case '\u0019':
          numArray = agdb_lookup_table.BYTE_NINE_9;
          break;
        case '\u001A':
          byteColon = agdb_lookup_table.BYTE_COLON;
          num = (byte) 1;
          break;
        case '!':
          numArray = agdb_lookup_table.A_A;
          break;
        case '"':
          numArray = agdb_lookup_table.B_B;
          break;
        case '#':
          numArray = agdb_lookup_table.C_C;
          break;
        case '$':
          numArray = agdb_lookup_table.D_D;
          break;
        case '%':
          numArray = agdb_lookup_table.E_E;
          break;
        case '&':
          numArray = agdb_lookup_table.F_F;
          break;
        case '\'':
          numArray = agdb_lookup_table.G_G;
          break;
        case '(':
          numArray = agdb_lookup_table.H_H;
          break;
        case ')':
          numArray = agdb_lookup_table.I_I;
          break;
        case '*':
          numArray = agdb_lookup_table.J_J;
          break;
        case '+':
          numArray = agdb_lookup_table.K_K;
          break;
        case ',':
          numArray = agdb_lookup_table.L_L;
          break;
        case '-':
          numArray = agdb_lookup_table.M_M;
          break;
        case '.':
          numArray = agdb_lookup_table.N_N;
          break;
        case '/':
          numArray = agdb_lookup_table.O_O;
          break;
        case '0':
          numArray = agdb_lookup_table.P_P;
          break;
        case '1':
          numArray = agdb_lookup_table.Q_Q;
          break;
        case '2':
          numArray = agdb_lookup_table.R_R;
          break;
        case '3':
          numArray = agdb_lookup_table.S_S;
          break;
        case '4':
          numArray = agdb_lookup_table.T_T;
          break;
        case '5':
          numArray = agdb_lookup_table.U_U;
          break;
        case '6':
          numArray = agdb_lookup_table.V_V;
          break;
        case '7':
          numArray = agdb_lookup_table.W_W;
          break;
        case '8':
          numArray = agdb_lookup_table.X_X;
          break;
        case '9':
          numArray = agdb_lookup_table.Y_Y;
          break;
        case ':':
          numArray = agdb_lookup_table.Z_Z;
          break;
      }
      byte index2 = 0;
      if (num == (byte) 1)
      {
        agdb_bytearray[(int) checked ((byte) unchecked ((int) count + (int) index2))] = byteColon;
      }
      else
      {
        while ((uint) index2 < (uint) num)
        {
          agdb_bytearray[(int) checked ((byte) unchecked ((int) count + (int) index2))] = numArray[(int) index2];
          checked { ++index2; }
        }
      }
      count = checked ((byte) ((int) (byte) unchecked ((int) count + (int) num) + 1));
      checked { ++index1; }
    }
  }

  public static void agdb_default_msg_bytes(string msg, ref byte[] agdb_default_msg)
  {
    byte count = 0;
    int num = 0;
    byte[] agdb_bytearray = new byte[240 /*0xF0*/];
    int index1 = 0;
    while (index1 < 240 /*0xF0*/)
    {
      agdb_default_msg[index1] = (byte) 0;
      checked { ++index1; }
    }
    int length = Operators.CompareString(msg, "", false) != 0 ? msg.Length : 0;
    agdb_byte_construct.name_byte_default_msg(msg, ref count, ref agdb_bytearray);
    num = checked ((int) Math.Round(unchecked ((double) checked (length * 6) / 2.0)));
    count = checked ((byte) Math.Round(unchecked ((double) checked (240 /*0xF0*/ - length * 6) / 2.0)));
    int index2 = 0;
    while (index2 < checked (length * 6))
    {
      agdb_default_msg[checked ((int) count + index2)] = agdb_bytearray[index2];
      checked { ++index2; }
    }
  }
}

}