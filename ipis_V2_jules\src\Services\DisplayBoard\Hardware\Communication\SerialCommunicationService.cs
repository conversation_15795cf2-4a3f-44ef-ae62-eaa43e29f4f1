using System;
using System.IO.Ports;
using System.Threading;
using System.Threading.Tasks;
using System.IO; // For IOException

namespace ipis_V2_jules.Services.DisplayBoard.Hardware.Communication // Adjusted namespace
{
    public class SerialCommunicationService : ICommunicationService
    {
        private readonly SerialPort _serialPort;

        public string PortName { get; }
        public int BaudRate { get; }
        public Parity Parity { get; }
        public int DataBits { get; }
        public StopBits StopBits { get; }

        public bool IsPortOpen { get; private set; }

        public SerialCommunicationService(string portName, int baudRate, Parity parity, int dataBits, StopBits stopBits)
        {
            PortName = portName;
            BaudRate = baudRate;
            Parity = parity;
            DataBits = dataBits;
            StopBits = stopBits;

            _serialPort = new SerialPort();
        }

        public bool OpenPort()
        {
            if (IsPortOpen)
            {
                Console.WriteLine($"Warning: Port {PortName} is already open.");
                return true;
            }

            try
            {
                _serialPort.PortName = PortName;
                _serialPort.BaudRate = BaudRate;
                _serialPort.Parity = Parity;
                _serialPort.DataBits = DataBits;
                _serialPort.StopBits = StopBits;
                _serialPort.Handshake = Handshake.None; // Or RequestToSend, as specified

                // Standard timeouts, can be overridden per read/write if necessary
                _serialPort.ReadTimeout = 2000;
                _serialPort.WriteTimeout = 2000;

                _serialPort.Open();
                IsPortOpen = true;
                Console.WriteLine($"Serial port {PortName} opened successfully.");
                return true;
            }
            catch (IOException ex)
            {
                Console.WriteLine($"Error opening serial port {PortName} (IO): {ex.Message}");
                IsPortOpen = false;
            }
            catch (UnauthorizedAccessException ex)
            {
                Console.WriteLine($"Error opening serial port {PortName} (Access Denied): {ex.Message}");
                IsPortOpen = false;
            }
            catch (InvalidOperationException ex)
            {
                Console.WriteLine($"Error opening serial port {PortName} (Invalid Operation, already open or config issue): {ex.Message}");
                // IsPortOpen might be true if InvalidOperationException is due to it being already open by this instance
                IsPortOpen = _serialPort.IsOpen;
            }
            catch (ArgumentException ex)
            {
                 Console.WriteLine($"Error opening serial port {PortName} (Argument Exception, invalid parameters): {ex.Message}");
                 IsPortOpen = false;
            }
            catch (Exception ex) // Catch-all for other unexpected exceptions
            {
                Console.WriteLine($"An unexpected error occurred while opening serial port {PortName}: {ex.Message}");
                IsPortOpen = false;
            }
            return false;
        }

        public void ClosePort()
        {
            if (IsPortOpen && _serialPort.IsOpen)
            {
                try
                {
                    _serialPort.Close();
                    IsPortOpen = false;
                    Console.WriteLine($"Serial port {PortName} closed.");
                }
                catch (IOException ex)
                {
                    Console.WriteLine($"Error closing serial port {PortName}: {ex.Message}");
                    // State might be uncertain here, but likely closed or unusable
                    IsPortOpen = false;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"An unexpected error occurred while closing serial port {PortName}: {ex.Message}");
                    IsPortOpen = false;
                }
            }
            else
            {
                Console.WriteLine($"Serial port {PortName} is not open or already closed.");
                IsPortOpen = false; // Ensure consistent state
            }
        }

        public async Task WriteDataAsync(byte[] data)
        {
            if (!IsPortOpen || !_serialPort.IsOpen)
            {
                Console.WriteLine($"Error: Port {PortName} is not open. Cannot write data.");
                // Consider throwing InvalidOperationException
                return;
            }

            if (data == null || data.Length == 0)
            {
                Console.WriteLine("Warning: No data provided to WriteDataAsync.");
                return;
            }

            try
            {
                await _serialPort.BaseStream.WriteAsync(data, 0, data.Length);
                Console.WriteLine($"Data written to {PortName}: {BitConverter.ToString(data)}");
            }
            catch (TimeoutException ex)
            {
                Console.WriteLine($"Error writing data to {PortName} (Timeout): {ex.Message}");
            }
            catch (InvalidOperationException ex)
            {
                Console.WriteLine($"Error writing data to {PortName} (Invalid Operation): {ex.Message}");
            }
            catch (IOException ex)
            {
                Console.WriteLine($"Error writing data to {PortName} (IO): {ex.Message}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"An unexpected error occurred while writing data to {PortName}: {ex.Message}");
            }
        }

        public async Task<byte[]> ReadDataAsync(int bytesToRead, int timeoutMs)
        {
            if (!IsPortOpen || !_serialPort.IsOpen)
            {
                Console.WriteLine($"Error: Port {PortName} is not open. Cannot read data.");
                return null;
            }

            if (bytesToRead <= 0)
            {
                Console.WriteLine("Error: bytesToRead must be greater than zero.");
                return null;
            }

            byte[] buffer = new byte[bytesToRead];
            int totalBytesRead = 0;

            // Use CancellationTokenSource for timeout
            using (var cts = new CancellationTokenSource(timeoutMs))
            {
                try
                {
                    // Set the ReadTimeout on the BaseStream if possible, or manage timeout via CancellationToken
                    // _serialPort.BaseStream.ReadTimeout = timeoutMs; // This might not work as expected with async

                    while (totalBytesRead < bytesToRead)
                    {
                        int bytesReadThisIteration = await _serialPort.BaseStream.ReadAsync(buffer, totalBytesRead, bytesToRead - totalBytesRead, cts.Token);
                        if (bytesReadThisIteration == 0)
                        {
                            // End of stream or port closed unexpectedly
                            Console.WriteLine($"Warning: Read from {PortName} returned 0 bytes before expected amount. Total read: {totalBytesRead}");
                            break;
                        }
                        totalBytesRead += bytesReadThisIteration;
                    }

                    if (totalBytesRead > 0 && totalBytesRead < bytesToRead)
                    {
                        Console.WriteLine($"Warning: Read from {PortName} expected {bytesToRead} bytes, but received {totalBytesRead}.");
                        byte[] partialData = new byte[totalBytesRead];
                        Array.Copy(buffer, partialData, totalBytesRead);
                        return partialData;
                    }
                    else if (totalBytesRead == bytesToRead)
                    {
                        Console.WriteLine($"Data read from {PortName}: {BitConverter.ToString(buffer)}");
                        return buffer;
                    }
                    else // totalBytesRead == 0
                    {
                        Console.WriteLine($"No data read from {PortName} within the timeout or stream ended.");
                        return null;
                    }
                }
                catch (OperationCanceledException) // Catches TaskCanceledException from CancellationToken
                {
                    Console.WriteLine($"Error reading data from {PortName} (Timeout): Operation was canceled.");
                    if (totalBytesRead > 0) // Return partial data if any was read before timeout
                    {
                        byte[] partialData = new byte[totalBytesRead];
                        Array.Copy(buffer, partialData, totalBytesRead);
                        Console.WriteLine($"Returning partially read data ({totalBytesRead} bytes) due to timeout.");
                        return partialData;
                    }
                    return null;
                }
                catch (TimeoutException ex) // This might be thrown by SerialPort's internal timeouts if configured and hit.
                {
                     Console.WriteLine($"Error reading data from {PortName} (TimeoutException): {ex.Message}");
                     return null; // Or handle partial data as above
                }
                catch (InvalidOperationException ex)
                {
                    Console.WriteLine($"Error reading data from {PortName} (Invalid Operation): {ex.Message}");
                    return null;
                }
                catch (IOException ex)
                {
                    Console.WriteLine($"Error reading data from {PortName} (IO): {ex.Message}");
                    return null;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"An unexpected error occurred while reading data from {PortName}: {ex.Message}");
                    return null;
                }
            }
        }

        private bool _disposed = false;
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    // Dispose managed state (managed objects).
                    ClosePort(); // Ensure port is closed
                    if (_serialPort != null)
                    {
                        _serialPort.Dispose();
                    }
                }
                // Free unmanaged resources (unmanaged objects) and override a finalizer below.
                // Set large fields to null.
                _disposed = true;
            }
        }

        // Optional: Finalizer if you have unmanaged resources directly in this class
        // ~SerialCommunicationService()
        // {
        //     Dispose(false);
        // }
    }
}
