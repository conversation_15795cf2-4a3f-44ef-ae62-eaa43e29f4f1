using System;
using System.Collections.Generic;
using System.IO;
// Required for OleDb connections - user will need to ensure System.Data.OleDb is available
// using System.Data.OleDb;
using ipis_V2_jules.Data; // For DatabaseHelper

namespace ipis_V2_jules.Migration
{
    public class MigrationService
    {
        private readonly DatabaseHelper _dbHelper;
        private readonly string _mdbDirectoryPath;

        /// <summary>
        /// Initializes a new instance of the MigrationService class.
        /// </summary>
        /// <param name="dbHelper">Instance of DatabaseHelper for SQLite operations.</param>
        /// <param name="mdbDirectoryPath">Path to the directory containing the old Access MDB files.</param>
        public MigrationService(DatabaseHelper dbHelper, string mdbDirectoryPath)
        {
            _dbHelper = dbHelper ?? throw new ArgumentNullException(nameof(dbHelper));
            _mdbDirectoryPath = mdbDirectoryPath ?? throw new ArgumentNullException(nameof(mdbDirectoryPath));

            if (!Directory.Exists(_mdbDirectoryPath))
            {
                throw new DirectoryNotFoundException($"The specified MDB directory path does not exist: {mdbDirectoryPath}");
            }
        }

        /// <summary>
        /// Migrates user data from logindb.mdb to the SQLite Users table.
        /// </summary>
        public void MigrateUsers()
        {
            // --- CONFIGURATION ---
            // Source Access DB: "logindb.mdb"
            // Source Table: "logintable"
            // Target SQLite Table: "Users"
            //
            // --- COLUMN MAPPINGS & TRANSFORMATIONS ---
            // Access `User_ID` (Text) -> SQLite `OriginalUserID` (TEXT)
            // Access `User_Name` (Text) -> SQLite `Username` (TEXT, UNIQUE NOT NULL)
            // Access `User_Password` (Text), `p_length` (Number) -> SQLite `PasswordHash` (TEXT NOT NULL)
            //      Transformation: Passwords cannot be decrypted. A new temporary hash should be generated.
            //                      Users must be forced to reset their passwords upon first login.
            //                      Example: "NEEDS_RESET_" + Guid.NewGuid().ToString()
            // Access `User_Group` (Text) -> SQLite `UserGroup` (TEXT)

            string mdbFileName = "logindb.mdb";
            string mdbPath = Path.Combine(_mdbDirectoryPath, mdbFileName);

            if (!File.Exists(mdbPath))
            {
                Console.WriteLine($"Warning: Source MDB file not found at {mdbPath}. Skipping MigrateUsers.");
                return;
            }

            // string connectionString = $"Provider=Microsoft.Jet.OLEDB.4.0;Data Source={mdbPath};";
            // // For newer .accdb files, if any:
            // // string connectionString = $"Provider=Microsoft.ACE.OLEDB.12.0;Data Source={mdbPath};";

            Console.WriteLine($"Attempting to migrate users from {mdbPath} to SQLite Users table.");
            Console.WriteLine("-------------------------------------------------------------------");
            Console.WriteLine("INFO: The following C# code block for OleDb operations is commented out.");
            Console.WriteLine("To perform the actual migration, you need to:");
            Console.WriteLine("1. Ensure you are in a Windows environment with Microsoft Access Database Engine (or Jet OLEDB Provider) installed.");
            Console.WriteLine("2. Add a reference to the System.Data.OleDb NuGet package if not already present in your project.");
            Console.WriteLine("3. Uncomment the code block below.");
            Console.WriteLine("-------------------------------------------------------------------");

            /*
            try
            {
                using (var connection = new OleDbConnection(connectionString))
                {
                    connection.Open();
                    var command = new OleDbCommand("SELECT User_ID, User_Name, User_Password, p_length, User_Group FROM logintable", connection);

                    int count = 0;
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            string originalUserId = reader["User_ID"]?.ToString();
                            string username = reader["User_Name"]?.ToString();
                            string userGroup = reader["User_Group"]?.ToString();

                            // Basic validation
                            if (string.IsNullOrWhiteSpace(username))
                            {
                                Console.WriteLine($"Skipping user with OriginalUserID '{originalUserId}' due to empty username.");
                                continue;
                            }

                            // Password Handling: As direct migration is insecure/impossible,
                            // set a temporary placeholder and advise password reset.
                            // The old system seems to store password and its length, suggesting custom non-standard hashing or encryption.
                            // We cannot replicate or decrypt this reliably.
                            string passwordHash = "TEMP_HASH_NEEDS_RESET_" + Guid.NewGuid().ToString("N");

                            var parameters = new Dictionary<string, object>
                            {
                                { "@OriginalUserID", originalUserId },
                                { "@Username", username },
                                { "@PasswordHash", passwordHash },
                                { "@UserGroup", userGroup }
                            };

                            try
                            {
                                _dbHelper.ExecuteNonQuery(
                                    "INSERT INTO Users (OriginalUserID, Username, PasswordHash, UserGroup) " +
                                    "VALUES (@OriginalUserID, @Username, @PasswordHash, @UserGroup)",
                                    parameters);
                                count++;
                            }
                            catch (Exception ex)
                            {
                                // Handle potential duplicate usernames or other insertion errors
                                Console.WriteLine($"Error inserting user '{username}' (OriginalID: {originalUserId}): {ex.Message}");
                            }
                        }
                    }
                    Console.WriteLine($"Successfully migrated {count} users from {mdbFileName}.");
                }
            }
            catch (OleDbException ex)
            {
                Console.WriteLine($"OleDbException during user migration: {ex.Message}");
                Console.WriteLine("Ensure Microsoft Access Database Engine (or Jet OLEDB Provider) is installed and the MDB file is accessible.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"An unexpected error occurred during user migration: {ex.Message}");
            }
            */
            Console.WriteLine($"Placeholder executed for Migrating Users from {mdbPath}. User action required to uncomment and run OleDb code.");
        }

        /// <summary>
        /// Migrates station data from StationCode_db.mdb and StationDetails.mdb to the SQLite Stations table.
        /// </summary>
        public void MigrateStations()
        {
            // --- CONFIGURATION ---
            // Source Access DB 1: "StationCode_db.mdb" (Table: "tbl_stationCode") - Primary source for codes and names
            // Source Access DB 2: "StationDetails.mdb" (Table: "stationdetails") - Secondary source for more details
            // Target SQLite Table: "Stations"
            //
            // --- COLUMN MAPPINGS & TRANSFORMATIONS (Primary: tbl_stationCode) ---
            // Access `Station_Code` (Text) -> SQLite `StationCode` (TEXT UNIQUE NOT NULL)
            // Access `Station_Name` (Text) -> SQLite `StationName` (TEXT NOT NULL)
            // Access `Station_Name_H` (Text) -> SQLite `StationNameHindi` (TEXT)
            // Access `Station_Name_R` (Text) -> SQLite `StationNameRegional` (TEXT)
            // Access `Language` (Text) -> SQLite `RegionalLangName` (TEXT) - From tbl_stationCode, if it indicates the language of Station_Name_R
            // (Constant) -> `APISource` (TEXT) - Set to e.g., "LegacyImport_StationCodeDB"
            // (Current Timestamp) -> `LastUpdated` (TEXT)
            //
            // --- COLUMN MAPPINGS & TRANSFORMATIONS (Secondary: stationdetails, if code matches) ---
            // Access `stcode` (Text) - Use to match with `Station_Code` from primary source.
            // Access `stname` (Text) - Can be an alternative/confirmation for StationName.
            // Access `sthindiname` (Text) - Can be an alternative/confirmation for StationNameHindi.
            // Access `stregionalname` (Text) - Can be an alternative/confirmation for StationNameRegional.
            // Access `regional_language_name` (Text) - Can be an alternative/confirmation for RegionalLangName.
            //
            // --- LOGIC ---
            // 1. Read all stations from "StationCode_db.mdb".
            // 2. For each station, try to find matching details in "StationDetails.mdb" using the station code.
            // 3. Consolidate information, giving preference to one source or merging if appropriate.
            // 4. Insert into SQLite `Stations` table.

            string primaryMdbFileName = "StationCode_db.mdb";
            string secondaryMdbFileName = "StationDetails.mdb";
            string primaryMdbPath = Path.Combine(_mdbDirectoryPath, primaryMdbFileName);
            string secondaryMdbPath = Path.Combine(_mdbDirectoryPath, secondaryMdbFileName);

            if (!File.Exists(primaryMdbPath))
            {
                Console.WriteLine($"Warning: Primary source MDB file not found at {primaryMdbPath}. Skipping MigrateStations.");
                return;
            }
            // Secondary MDB is optional, so just warn if not found.
            if (!File.Exists(secondaryMdbPath))
            {
                Console.WriteLine($"Warning: Secondary source MDB file not found at {secondaryMdbPath}. Station details might be limited.");
            }

            // string primaryConnStr = $"Provider=Microsoft.Jet.OLEDB.4.0;Data Source={primaryMdbPath};";
            // string secondaryConnStr = $"Provider=Microsoft.Jet.OLEDB.4.0;Data Source={secondaryMdbPath};";

            Console.WriteLine($"Attempting to migrate stations from {primaryMdbPath} (and optionally {secondaryMdbPath}) to SQLite Stations table.");
            Console.WriteLine("-------------------------------------------------------------------");
            Console.WriteLine("INFO: The following C# code block for OleDb operations is commented out.");
            Console.WriteLine("User action required (see MigrateUsers for details).");
            Console.WriteLine("-------------------------------------------------------------------");

            /*
            try
            {
                var stationsData = new Dictionary<string, Dictionary<string, object>>();

                // 1. Read from primary source (StationCode_db.mdb)
                using (var pConnection = new OleDbConnection(primaryConnStr))
                {
                    pConnection.Open();
                    var pCommand = new OleDbCommand("SELECT Station_Code, Station_Name, Station_Name_H, Station_Name_R, Language FROM tbl_stationCode", pConnection);
                    using (var pReader = pCommand.ExecuteReader())
                    {
                        while (pReader.Read())
                        {
                            string stationCode = pReader["Station_Code"]?.ToString()?.Trim();
                            if (string.IsNullOrWhiteSpace(stationCode)) continue;

                            stationsData[stationCode] = new Dictionary<string, object>
                            {
                                { "StationCode", stationCode },
                                { "StationName", pReader["Station_Name"]?.ToString()?.Trim() },
                                { "StationNameHindi", pReader["Station_Name_H"]?.ToString()?.Trim() },
                                { "StationNameRegional", pReader["Station_Name_R"]?.ToString()?.Trim() },
                                { "RegionalLangName", pReader["Language"]?.ToString()?.Trim() }, // Assuming 'Language' field in tbl_stationCode refers to the regional language
                                { "APISource", "LegacyImport_StationCodeDB" },
                                { "LastUpdated", DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss") }
                            };
                        }
                    }
                }

                // 2. Optionally, augment with data from secondary source (StationDetails.mdb)
                if (File.Exists(secondaryMdbPath))
                {
                    using (var sConnection = new OleDbConnection(secondaryConnStr))
                    {
                        sConnection.Open();
                        // Example: SELECT stcode, stname, sthindiname, stregionalname, regional_language_name FROM stationdetails
                        // This query depends on the actual column names in stationdetails table.
                        var sCommand = new OleDbCommand("SELECT stcode, stname, sthindiname, stregionalname FROM stationdetails", sConnection); // Adjust SQL as per actual schema
                        using (var sReader = sCommand.ExecuteReader())
                        {
                            while (sReader.Read())
                            {
                                string stationCode = sReader["stcode"]?.ToString()?.Trim();
                                if (string.IsNullOrWhiteSpace(stationCode) || !stationsData.ContainsKey(stationCode)) continue;

                                // Augment or override existing data. Example:
                                if (stationsData[stationCode]["StationName"] == null && sReader["stname"] != DBNull.Value)
                                    stationsData[stationCode]["StationName"] = sReader["stname"]?.ToString()?.Trim();
                                if (stationsData[stationCode]["StationNameHindi"] == null && sReader["sthindiname"] != DBNull.Value)
                                    stationsData[stationCode]["StationNameHindi"] = sReader["sthindiname"]?.ToString()?.Trim();
                                // Add more logic for other fields as needed.
                            }
                        }
                    }
                }

                // 3. Insert consolidated data into SQLite
                int count = 0;
                foreach (var stationEntry in stationsData)
                {
                    var parameters = new Dictionary<string, object>
                    {
                        { "@StationCode", stationEntry.Value["StationCode"] },
                        { "@StationName", stationEntry.Value["StationName"] },
                        { "@StationNameHindi", stationEntry.Value["StationNameHindi"] },
                        { "@StationNameRegional", stationEntry.Value["StationNameRegional"] },
                        { "@RegionalLangName", stationEntry.Value["RegionalLangName"] },
                        { "@APISource", stationEntry.Value["APISource"] },
                        { "@LastUpdated", stationEntry.Value["LastUpdated"] }
                    };

                    try
                    {
                        _dbHelper.ExecuteNonQuery(
                            "INSERT INTO Stations (StationCode, StationName, StationNameHindi, StationNameRegional, RegionalLangName, APISource, LastUpdated) " +
                            "VALUES (@StationCode, @StationName, @StationNameHindi, @StationNameRegional, @RegionalLangName, @APISource, @LastUpdated)",
                            parameters);
                        count++;
                    }
                    catch (Exception ex)
                    {
                         Console.WriteLine($"Error inserting station '{stationEntry.Key}': {ex.Message}");
                    }
                }
                Console.WriteLine($"Successfully migrated {count} stations.");
            }
            catch (OleDbException ex)
            {
                Console.WriteLine($"OleDbException during station migration: {ex.Message}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"An unexpected error occurred during station migration: {ex.Message}");
            }
            */
            Console.WriteLine($"Placeholder executed for Migrating Stations. User action required.");
        }

        /// <summary>
        /// Migrates platform data from platformno_db.mdb to the SQLite Platforms table.
        /// </summary>
        public void MigratePlatforms()
        {
            // --- CONFIGURATION ---
            // Source Access DB: "platformno_db.mdb"
            // Source Table: "tbl_pfno"
            // Target SQLite Table: "Platforms"
            //
            // --- COLUMN MAPPINGS & TRANSFORMATIONS ---
            // Access `pf_no` (Text/Number) -> SQLite `PlatformNumber` (TEXT NOT NULL)
            // (Hardcoded/Configured StationID) -> SQLite `StationID` (INTEGER NOT NULL)
            //      Discussion: The source `tbl_pfno` might not directly link to a station code.
            //                  For a single-station IPIS, this `StationID` could be a known, fixed ID
            //                  (e.g., the ID of the main station this IPIS instance serves, looked up from the Stations table).
            //                  If the MDB contains platform numbers for multiple stations (unlikely for typical old IPIS),
            //                  this logic would need to be more complex, potentially involving another source table or file
            //                  that maps platform numbers or MDB context to specific StationIDs.
            //                  For this placeholder, assume a single, known StationID is used.

            string mdbFileName = "platformno_db.mdb";
            string mdbPath = Path.Combine(_mdbDirectoryPath, mdbFileName);

            if (!File.Exists(mdbPath))
            {
                Console.WriteLine($"Warning: Source MDB file not found at {mdbPath}. Skipping MigratePlatforms.");
                return;
            }

            // string connectionString = $"Provider=Microsoft.Jet.OLEDB.4.0;Data Source={mdbPath};";

            Console.WriteLine($"Attempting to migrate platforms from {mdbPath} to SQLite Platforms table.");
            Console.WriteLine("-------------------------------------------------------------------");
            Console.WriteLine("INFO: The following C# code block for OleDb operations is commented out.");
            Console.WriteLine("User action required (see MigrateUsers for details).");
            Console.WriteLine("IMPORTANT: You need to determine the StationID for these platforms.");
            Console.WriteLine("This might be a hardcoded ID for the primary station this IPIS serves.");
            Console.WriteLine("-------------------------------------------------------------------");

            /*
            try
            {
                // !!! USER ACTION REQUIRED !!!
                // Determine the StationID for the platforms being migrated.
                // This is likely the ID of the main station this IPIS installation serves.
                // You might need to query the Stations table first to get this ID based on a known StationCode.
                // Example:
                // object stationIdObj = _dbHelper.ExecuteScalar("SELECT StationID FROM Stations WHERE StationCode = 'YOUR_MAIN_STATION_CODE'");
                // if (stationIdObj == null)
                // {
                //    Console.WriteLine("Error: Main station ID not found. Cannot migrate platforms.");
                //    return;
                // }
                // long mainStationId = Convert.ToInt64(stationIdObj);
                long mainStationId = 1; // <<<< Placeholder - REPLACE WITH ACTUAL LOGIC TO GET StationID

                using (var connection = new OleDbConnection(connectionString))
                {
                    connection.Open();
                    // Assuming the column name in tbl_pfno is 'pf_no'. Adjust if different.
                    var command = new OleDbCommand("SELECT pf_no FROM tbl_pfno", connection);

                    int count = 0;
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            string platformNumber = reader["pf_no"]?.ToString()?.Trim();

                            if (string.IsNullOrWhiteSpace(platformNumber))
                            {
                                Console.WriteLine("Skipping platform due to empty platform number.");
                                continue;
                            }

                            var parameters = new Dictionary<string, object>
                            {
                                { "@StationID", mainStationId },
                                { "@PlatformNumber", platformNumber }
                            };

                            try
                            {
                                // Note: The Platforms table has a UNIQUE constraint on (StationID, PlatformNumber).
                                _dbHelper.ExecuteNonQuery(
                                    "INSERT INTO Platforms (StationID, PlatformNumber) VALUES (@StationID, @PlatformNumber)",
                                    parameters);
                                count++;
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine($"Error inserting platform '{platformNumber}' for StationID '{mainStationId}': {ex.Message}");
                            }
                        }
                    }
                    Console.WriteLine($"Successfully migrated {count} platforms for StationID {mainStationId} from {mdbFileName}.");
                }
            }
            catch (OleDbException ex)
            {
                Console.WriteLine($"OleDbException during platform migration: {ex.Message}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"An unexpected error occurred during platform migration: {ex.Message}");
            }
            */
            Console.WriteLine($"Placeholder executed for Migrating Platforms from {mdbPath}. User action required.");
        }

        /// <summary>
        /// Migrates train schedule data from various tables in train_db.mdb to the SQLite Trains table.
        /// </summary>
        public void MigrateTrainSchedules()
        {
            // --- CONFIGURATION ---
            // Source Access DB: "train_db.mdb"
            // Source Tables:
            //    - "trainconfigtable" (Primary train info: TrainNo, TrainName, ArrTime, DepTime, Source, Dest, Direction etc.)
            //    - "daysofweek" (Days of operation flags: Sun, Mon, Tue, etc. linked by TrainNo or an ID)
            //    - "ftdate" (From/To dates for periodic trains, linked by TrainNo or an ID)
            //    - "specificdate" (Specific dates of operation, linked by TrainNo or an ID)
            //    - "cgstble" (Coach composition info, linked by TrainNo or an ID)
            // Target SQLite Table: "Trains"
            //
            // --- COLUMN MAPPINGS & TRANSFORMATIONS (trainconfigtable) ---
            // Access `Train_No` -> SQLite `TrainNo` (TEXT UNIQUE NOT NULL)
            // Access `Train_Name` -> SQLite `TrainName` (TEXT NOT NULL)
            // Access `Train_Name_H` -> SQLite `TrainNameHindi` (TEXT) (If available)
            // Access `Train_Name_R` -> SQLite `TrainNameRegional` (TEXT) (If available)
            // Access `Language` -> SQLite `LanguageName` (TEXT) (If available, for regional name)
            // Access `Arr_Time` -> SQLite `ScheduledArrivalTimeAtStation` (TEXT, HH:MM)
            // Access `Dep_Time` -> SQLite `ScheduledDepartureTimeAtStation` (TEXT, HH:MM)
            // Access `Source` (Station Code) -> SQLite `SourceStationID` (INTEGER)
            //      Transformation: Lookup StationID from Stations table based on Source station code.
            // Access `Destination` (Station Code) -> SQLite `DestinationStationID` (INTEGER)
            //      Transformation: Lookup StationID from Stations table based on Destination station code.
            // Access `Direction` (e.g., "U", "D") -> SQLite `Direction` (TEXT, e.g., "Up", "Down")
            // Access `StationPosition` (e.g., "O", "T", "I") -> SQLite `StationPositionInRoute` (TEXT, e.g., "Origin", "Terminus", "Intermediate")
            // (Constant) -> `APISource` (TEXT) - Set to "LegacyImport_TrainDB"
            // (Current Timestamp) -> `LastUpdated` (TEXT)
            //
            // --- daysofweek Table ---
            // Access flags (e.g., `d_sun`, `d_mon`) -> SQLite `DaysOfOperation` (TEXT, comma-separated e.g., "Mon,Tue,Sun")
            //
            // --- ftdate Table ---
            // Access `from_date`, `to_date` -> SQLite `FromDate` (TEXT, YYYY-MM-DD), `ToDate` (TEXT, YYYY-MM-DD)
            // Set `IsPeriodTrain` = 1 if ftdate entries exist for the train.
            //
            // --- specificdate Table ---
            // Access `sp_date` -> SQLite `SpecificDatesJSON` (TEXT, JSON array of YYYY-MM-DD strings)
            // Set `IsSpecificDatesTrain` = 1 if specificdate entries exist.
            //
            // --- cgstble Table ---
            // Access `cgs_inf` (Coach info) -> SQLite `CoachCompositionInfo` (TEXT)
            //
            // --- LOGIC ---
            // 1. Iterate through `trainconfigtable`.
            // 2. For each train, query related tables (`daysofweek`, `ftdate`, `specificdate`, `cgstble`) using TrainNo.
            // 3. Perform lookups for SourceStationID and DestinationStationID.
            // 4. Consolidate all information and insert into SQLite `Trains` table.

            string mdbFileName = "train_db.mdb";
            string mdbPath = Path.Combine(_mdbDirectoryPath, mdbFileName);

            if (!File.Exists(mdbPath))
            {
                Console.WriteLine($"Warning: Source MDB file not found at {mdbPath}. Skipping MigrateTrainSchedules.");
                return;
            }

            // string connectionString = $"Provider=Microsoft.Jet.OLEDB.4.0;Data Source={mdbPath};";

            Console.WriteLine($"Attempting to migrate train schedules from {mdbPath} to SQLite Trains table.");
            Console.WriteLine("-------------------------------------------------------------------");
            Console.WriteLine("INFO: This is a complex migration. The OleDb code is commented out.");
            Console.WriteLine("User action required (see MigrateUsers for details).");
            Console.WriteLine("Pay close attention to table/column names and join conditions in the MDB.");
            Console.WriteLine("-------------------------------------------------------------------");

            /*
            try
            {
                using (var connection = new OleDbConnection(connectionString))
                {
                    connection.Open();

                    // Adjust SQL based on actual column names in trainconfigtable
                    var trainCmd = new OleDbCommand("SELECT Train_No, Train_Name, Arr_Time, Dep_Time, Source, Destination, Direction FROM trainconfigtable", connection);

                    int count = 0;
                    using (var trainReader = trainCmd.ExecuteReader())
                    {
                        while (trainReader.Read())
                        {
                            string trainNo = trainReader["Train_No"]?.ToString()?.Trim();
                            if (string.IsNullOrWhiteSpace(trainNo)) continue;

                            // --- Basic Train Info ---
                            var parameters = new Dictionary<string, object>
                            {
                                { "@TrainNo", trainNo },
                                { "@TrainName", trainReader["Train_Name"]?.ToString()?.Trim() },
                                // Add TrainNameHindi, TrainNameRegional, LanguageName if available
                                { "@ScheduledArrivalTimeAtStation", trainReader["Arr_Time"]?.ToString()?.Trim() }, // Ensure HH:MM format
                                { "@ScheduledDepartureTimeAtStation", trainReader["Dep_Time"]?.ToString()?.Trim() }, // Ensure HH:MM format
                                { "@APISource", "LegacyImport_TrainDB" },
                                { "@LastUpdated", DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss") }
                            };

                            // --- Source/Destination Station ID Lookups ---
                            string sourceCode = trainReader["Source"]?.ToString()?.Trim();
                            string destCode = trainReader["Destination"]?.ToString()?.Trim();

                            // object sourceStationIdObj = !string.IsNullOrWhiteSpace(sourceCode) ? _dbHelper.ExecuteScalar($"SELECT StationID FROM Stations WHERE StationCode = '{sourceCode}'") : null;
                            // object destStationIdObj = !string.IsNullOrWhiteSpace(destCode) ? _dbHelper.ExecuteScalar($"SELECT StationID FROM Stations WHERE StationCode = '{destCode}'") : null;
                            // parameters["@SourceStationID"] = sourceStationIdObj != null ? Convert.ToInt64(sourceStationIdObj) : (long?)null;
                            // parameters["@DestinationStationID"] = destStationIdObj != null ? Convert.ToInt64(destStationIdObj) : (long?)null;

                            // --- Direction & StationPosition ---
                            // parameters["@Direction"] = MapDirection(trainReader["Direction"]?.ToString()?.Trim());
                            // parameters["@StationPositionInRoute"] = MapStationPosition(trainReader["StationPosition"]?.ToString()?.Trim()); // If column exists

                            // --- Days of Operation (from daysofweek table) ---
                            // Example: SELECT d_sun, d_mon, ... FROM daysofweek WHERE Train_No = @trainNoParam
                            // var days = new List<string>();
                            // if (dayReader["d_sun"] == "Y") days.Add("Sun"); // Adapt based on actual values (Y/N, True/False, 1/0)
                            // parameters["@DaysOfOperation"] = string.Join(",", days);

                            // --- Periodic Train Info (from ftdate table) ---
                            // Example: SELECT from_date, to_date FROM ftdate WHERE Train_No = @trainNoParam
                            // parameters["@FromDate"] = ConvertAccessDate(ftReader["from_date"]); // Ensure YYYY-MM-DD
                            // parameters["@ToDate"] = ConvertAccessDate(ftReader["to_date"]);   // Ensure YYYY-MM-DD
                            // parameters["@IsPeriodTrain"] = 1; // If ftdate entries found

                            // --- Specific Dates (from specificdate table) ---
                            // Example: SELECT sp_date FROM specificdate WHERE Train_No = @trainNoParam
                            // var specificDates = new List<string>();
                            // specificDates.Add(ConvertAccessDate(spReader["sp_date"])); // Ensure YYYY-MM-DD
                            // parameters["@SpecificDatesJSON"] = System.Text.Json.JsonSerializer.Serialize(specificDates); // Requires System.Text.Json
                            // parameters["@IsSpecificDatesTrain"] = 1; // If specificdate entries found

                            // --- Coach Composition (from cgstble table) ---
                            // Example: SELECT cgs_inf FROM cgstble WHERE Train_No = @trainNoParam
                            // parameters["@CoachCompositionInfo"] = cgsReader["cgs_inf"]?.ToString();

                            try
                            {
                                _dbHelper.ExecuteNonQuery(
                                   "INSERT INTO Trains (TrainNo, TrainName, ScheduledArrivalTimeAtStation, ScheduledDepartureTimeAtStation, " +
                                   "SourceStationID, DestinationStationID, DaysOfOperation, Direction, StationPositionInRoute, " +
                                   "FromDate, ToDate, IsPeriodTrain, IsSpecificDatesTrain, SpecificDatesJSON, CoachCompositionInfo, APISource, LastUpdated) " +
                                   "VALUES (@TrainNo, @TrainName, @ScheduledArrivalTimeAtStation, @ScheduledDepartureTimeAtStation, " +
                                   "@SourceStationID, @DestinationStationID, @DaysOfOperation, @Direction, @StationPositionInRoute, " +
                                   "@FromDate, @ToDate, @IsPeriodTrain, @IsSpecificDatesTrain, @SpecificDatesJSON, @CoachCompositionInfo, @APISource, @LastUpdated)",
                                   parameters);
                                count++;
                            }
                            catch(Exception ex)
                            {
                                Console.WriteLine($"Error inserting train '{trainNo}': {ex.Message}");
                            }
                        }
                    }
                    Console.WriteLine($"Successfully migrated {count} train schedules from {mdbFileName}.");
                }
            }
            catch (OleDbException ex)
            {
                Console.WriteLine($"OleDbException during train schedule migration: {ex.Message}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"An unexpected error occurred during train schedule migration: {ex.Message}");
            }
            */
            Console.WriteLine($"Placeholder executed for Migrating Train Schedules from {mdbPath}. User action required.");
        }

        /// <summary>
        /// Migrates network and display board configuration from network.mdb to SQLite DisplayBoardConfig table.
        /// </summary>
        public void MigrateNetworkConfiguration()
        {
            // --- CONFIGURATION ---
            // Source Access DB: "network.mdb"
            // Source Tables: Potentially "tbl_CCUMDCH", "tbl_MDCH", "tbl_PDCH" (and others, schema can be complex)
            //      tbl_CCUMDCH: CCU (Central Control Unit) to MDCH (Main Display Control Hub) links.
            //      tbl_MDCH: MDCH properties, possibly linking to PDCH (Platform Display Control Hub).
            //      tbl_PDCH: PDCH properties, possibly linking to specific boards or platforms.
            // Target SQLite Table: "DisplayBoardConfig"
            //
            // --- COLUMN MAPPINGS & TRANSFORMATIONS ---
            // This is highly dependent on the specific structure of network.mdb.
            // The goal is to flatten the hierarchy into individual board configurations.
            //
            // Example fields to map (actual Access column names will vary):
            // Access `BoardIdentifier` (from some table) -> SQLite `BoardName` (TEXT UNIQUE NOT NULL)
            // Access `BoardTypeIndicator` (e.g., "PFDB", "CGDB") -> SQLite `BoardType` (TEXT NOT NULL)
            // Access `PhysicalAddress` (from some table) -> SQLite `BoardAddress` (INTEGER NOT NULL)
            // Access `PlatformLink` (from PDCH or similar) -> SQLite `PlatformID` (INTEGER)
            //      Transformation: May need to look up PlatformID from Platforms table based on a platform number.
            // Access `HubType` (e.g., "Serial", "Ethernet") -> SQLite `HubType` (TEXT)
            // Access `HubAddress` (from MDCH or CCUMDCH) -> SQLite `HubAddress` (INTEGER)
            // Access `PortOnHub` -> SQLite `PortNumber` (INTEGER)
            // Access `SequenceOnPort` -> SQLite `SerialNumberInPort` (INTEGER)
            // Miscellaneous settings -> SQLite `ConfigurationJSON` (TEXT, JSON blob for extra settings)
            //
            // --- LOGIC ---
            // 1. Understand the relationships in network.mdb (e.g., how CCUs, MDCHs, PDCHs, and boards are linked).
            // 2. Traverse these relationships to identify each individual display board.
            // 3. Collect all relevant configuration parameters for each board.
            // 4. Map and insert into SQLite `DisplayBoardConfig`.

            string mdbFileName = "network.mdb";
            string mdbPath = Path.Combine(_mdbDirectoryPath, mdbFileName);

            if (!File.Exists(mdbPath))
            {
                Console.WriteLine($"Warning: Source MDB file not found at {mdbPath}. Skipping MigrateNetworkConfiguration.");
                return;
            }

            // string connectionString = $"Provider=Microsoft.Jet.OLEDB.4.0;Data Source={mdbPath};";

            Console.WriteLine($"Attempting to migrate network configuration from {mdbPath} to SQLite DisplayBoardConfig table.");
            Console.WriteLine("-------------------------------------------------------------------");
            Console.WriteLine("INFO: This is a VERY complex migration due to varying network.mdb schemas.");
            Console.WriteLine("The OleDb code is commented out and is highly speculative.");
            Console.WriteLine("User action required: Thoroughly analyze your specific network.mdb schema.");
            Console.WriteLine("-------------------------------------------------------------------");

            /*
            try
            {
                using (var connection = new OleDbConnection(connectionString))
                {
                    connection.Open();
                    // This SQL is a guess. You MUST adapt it to your network.mdb schema.
                    // It might involve joining tbl_CCUMDCH, tbl_MDCH, tbl_PDCH, etc.
                    var boardCmd = new OleDbCommand("SELECT BoardID, BoardType, BoardAddress, PlatformNumber, HubType, HubAddr, Port, Serial FROM YourNetworkQueryOrTable", connection);

                    int count = 0;
                    using (var boardReader = boardCmd.ExecuteReader())
                    {
                        while (boardReader.Read())
                        {
                            string boardName = boardReader["BoardID"]?.ToString()?.Trim(); // Or generate one
                            if (string.IsNullOrWhiteSpace(boardName)) continue;

                            var parameters = new Dictionary<string, object>
                            {
                                { "@BoardName", boardName },
                                { "@BoardType", boardReader["BoardType"]?.ToString()?.Trim() }, // e.g. "PFDB", "CGDB"
                                { "@BoardAddress", Convert.ToInt32(boardReader["BoardAddress"]) },
                                // { "@PlatformID", LookupPlatformID(boardReader["PlatformNumber"]?.ToString()?.Trim()) }, // Implement LookupPlatformID
                                { "@HubType", boardReader["HubType"]?.ToString()?.Trim() },
                                { "@HubAddress", Convert.ToInt32(boardReader["HubAddr"]) },
                                { "@PortNumber", Convert.ToInt32(boardReader["Port"]) },
                                { "@SerialNumberInPort", Convert.ToInt32(boardReader["Serial"]) },
                                // { "@ConfigurationJSON", "{ \"customSetting\": \"value\" }" } // Store other specific settings
                            };

                            try
                            {
                                _dbHelper.ExecuteNonQuery(
                                    "INSERT INTO DisplayBoardConfig (BoardName, BoardType, BoardAddress, PlatformID, HubType, HubAddress, PortNumber, SerialNumberInPort, ConfigurationJSON) " +
                                    "VALUES (@BoardName, @BoardType, @BoardAddress, @PlatformID, @HubType, @HubAddress, @PortNumber, @SerialNumberInPort, @ConfigurationJSON)",
                                    parameters);
                                count++;
                            }
                            catch(Exception ex)
                            {
                                Console.WriteLine($"Error inserting display board config '{boardName}': {ex.Message}");
                            }
                        }
                    }
                    Console.WriteLine($"Successfully migrated {count} display board configurations from {mdbFileName}.");
                }
            }
            catch (OleDbException ex)
            {
                Console.WriteLine($"OleDbException during network configuration migration: {ex.Message}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"An unexpected error occurred during network configuration migration: {ex.Message}");
            }
            */
            Console.WriteLine($"Placeholder executed for Migrating Network Configuration from {mdbPath}. User action required.");
        }

        /// <summary>
        /// Migrates predefined messages from message_db.mdb to the SQLite Messages table.
        /// </summary>
        public void MigrateMessages()
        {
            // --- CONFIGURATION ---
            // Source Access DB: "message_db.mdb"
            // Source Tables:
            //    - "tbl_message" (General predefined messages)
            //    - "tbl_train_status" (Messages related to train status, e.g., "ARRIVED", "DEPARTED", "LATE") - These might be better suited for a separate
            //                         handling mechanism or specific MessageTypes if merged into Messages.
            // Target SQLite Table: "Messages"
            //
            // --- COLUMN MAPPINGS & TRANSFORMATIONS (tbl_message) ---
            // Access `msg_id` (Number/Text) -> SQLite `OriginalMessageID` (TEXT)
            // Access `msg_eng` (Text) -> SQLite `MessageTextEnglish` (TEXT)
            // Access `msg_hindi` (Text) -> SQLite `MessageTextHindi` (TEXT)
            // Access `msg_regional` (Text) -> SQLite `MessageTextRegional` (TEXT)
            // Access `msg_type` (Text) -> SQLite `MessageType` (TEXT) (e.g., "Safety", "Welcome", "General")
            //
            // --- COLUMN MAPPINGS & TRANSFORMATIONS (tbl_train_status) ---
            // Access `status_id` -> `OriginalMessageID` (prefixed, e.g., "TS_1")
            // Access `status_eng`, `status_hindi`, `status_regional` -> `MessageTextEnglish`, etc.
            // Set `MessageType` to something like "TrainStatus" or "PlatformStatus".
            //
            // --- LOGIC ---
            // 1. Read messages from "tbl_message" and insert.
            // 2. Read messages from "tbl_train_status" and insert, ensuring distinct OriginalMessageID and appropriate MessageType.

            string mdbFileName = "message_db.mdb";
            string mdbPath = Path.Combine(_mdbDirectoryPath, mdbFileName);

            if (!File.Exists(mdbPath))
            {
                Console.WriteLine($"Warning: Source MDB file not found at {mdbPath}. Skipping MigrateMessages.");
                return;
            }

            // string connectionString = $"Provider=Microsoft.Jet.OLEDB.4.0;Data Source={mdbPath};";

            Console.WriteLine($"Attempting to migrate messages from {mdbPath} to SQLite Messages table.");
            Console.WriteLine("-------------------------------------------------------------------");
            Console.WriteLine("INFO: The OleDb code is commented out.");
            Console.WriteLine("User action required (see MigrateUsers for details).");
            Console.WriteLine("Consider how to handle messages from different source tables (e.g., tbl_message vs tbl_train_status).");
            Console.WriteLine("-------------------------------------------------------------------");

            /*
            try
            {
                int totalCount = 0;
                using (var connection = new OleDbConnection(connectionString))
                {
                    connection.Open();

                    // Migrate from tbl_message
                    // Adjust SQL based on actual column names in tbl_message
                    var msgCmd = new OleDbCommand("SELECT msg_id, msg_eng, msg_hindi, msg_regional, msg_type FROM tbl_message", connection);
                    int msgCount = 0;
                    using (var msgReader = msgCmd.ExecuteReader())
                    {
                        while (msgReader.Read())
                        {
                            var parameters = new Dictionary<string, object>
                            {
                                { "@OriginalMessageID", "MSG_" + msgReader["msg_id"]?.ToString()?.Trim() },
                                { "@MessageTextEnglish", msgReader["msg_eng"]?.ToString()?.Trim() },
                                { "@MessageTextHindi", msgReader["msg_hindi"]?.ToString()?.Trim() },
                                { "@MessageTextRegional", msgReader["msg_regional"]?.ToString()?.Trim() },
                                { "@MessageType", msgReader["msg_type"]?.ToString()?.Trim() ?? "General" }
                            };
                            try
                            {
                                _dbHelper.ExecuteNonQuery(
                                    "INSERT INTO Messages (OriginalMessageID, MessageTextEnglish, MessageTextHindi, MessageTextRegional, MessageType) " +
                                    "VALUES (@OriginalMessageID, @MessageTextEnglish, @MessageTextHindi, @MessageTextRegional, @MessageType)",
                                    parameters);
                                msgCount++;
                            }
                            catch(Exception ex)
                            {
                                Console.WriteLine($"Error inserting message (OriginalID: {parameters["@OriginalMessageID"]}): {ex.Message}");
                            }
                        }
                    }
                    Console.WriteLine($"Migrated {msgCount} messages from tbl_message.");
                    totalCount += msgCount;

                    // Migrate from tbl_train_status (example)
                    // Adjust SQL based on actual column names in tbl_train_status
                    var statusMsgCmd = new OleDbCommand("SELECT status_id, status_name_eng, status_name_hindi FROM tbl_train_status", connection);
                    int statusMsgCount = 0;
                    using (var statusReader = statusMsgCmd.ExecuteReader())
                    {
                        while (statusReader.Read())
                        {
                             var parameters = new Dictionary<string, object>
                            {
                                { "@OriginalMessageID", "STATUS_" + statusReader["status_id"]?.ToString()?.Trim() },
                                { "@MessageTextEnglish", statusReader["status_name_eng"]?.ToString()?.Trim() },
                                { "@MessageTextHindi", statusReader["status_name_hindi"]?.ToString()?.Trim() },
                                // { "@MessageTextRegional", statusReader["status_name_regional"]?.ToString()?.Trim() }, // If exists
                                { "@MessageType", "TrainPlatformStatus" } // Or more specific type
                            };
                            try
                            {
                                _dbHelper.ExecuteNonQuery(
                                    "INSERT INTO Messages (OriginalMessageID, MessageTextEnglish, MessageTextHindi, MessageTextRegional, MessageType) " +
                                    "VALUES (@OriginalMessageID, @MessageTextEnglish, @MessageTextHindi, @MessageTextRegional, @MessageType)",
                                    parameters);
                                statusMsgCount++;
                            }
                            catch(Exception ex)
                            {
                                Console.WriteLine($"Error inserting status message (OriginalID: {parameters["@OriginalMessageID"]}): {ex.Message}");
                            }
                        }
                    }
                    Console.WriteLine($"Migrated {statusMsgCount} messages from tbl_train_status.");
                    totalCount += statusMsgCount;
                }
                Console.WriteLine($"Successfully migrated a total of {totalCount} messages from {mdbFileName}.");
            }
            catch (OleDbException ex)
            {
                Console.WriteLine($"OleDbException during message migration: {ex.Message}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"An unexpected error occurred during message migration: {ex.Message}");
            }
            */
            Console.WriteLine($"Placeholder executed for Migrating Messages from {mdbPath}. User action required.");
        }


        // --- Helper methods for transformations (examples) ---
        /*
        private string MapDirection(string accessDirection)
        {
            if (string.IsNullOrWhiteSpace(accessDirection)) return null;
            switch (accessDirection.ToUpper())
            {
                case "U": return "Up";
                case "D": return "Down";
                case "C": return "Circular";
                default: return accessDirection; // Or log a warning
            }
        }

        private string ConvertAccessDate(object accessDateObj)
        {
            if (accessDateObj == null || accessDateObj == DBNull.Value) return null;
            try
            {
                DateTime date = Convert.ToDateTime(accessDateObj);
                return date.ToString("yyyy-MM-dd");
            }
            catch { return null; } // Log error if conversion fails
        }

        private long? LookupPlatformID(string platformNumber, long mainStationId)
        {
            if (string.IsNullOrWhiteSpace(platformNumber)) return null;
            // This assumes platform numbers are unique for the main station.
            // object platIdObj = _dbHelper.ExecuteScalar(
            //    $"SELECT PlatformID FROM Platforms WHERE StationID = {mainStationId} AND PlatformNumber = '{platformNumber.Replace("'", "''")}'"); // Basic SQL injection prevention for platformNumber
            // return platIdObj != null ? Convert.ToInt64(platIdObj) : (long?)null;
            return null; // Placeholder
        }
        */
    }
}
