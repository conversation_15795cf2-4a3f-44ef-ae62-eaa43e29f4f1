using System;
using System.IO.Ports;
using System.Threading;

namespace ipis_V2_jules.Hardware
{
    public class SerialCommunicationService : ICommunicationService
    {
        private SerialPort _serialPort;

        public SerialCommunicationService()
        {
            _serialPort = new SerialPort();
        }

        public bool OpenPort(string portName, int baudRate, int dataBits, Parity parity, StopBits stopBits)
        {
            try
            {
                if (_serialPort.IsOpen)
                {
                    _serialPort.Close();
                }
                _serialPort.PortName = portName;
                _serialPort.BaudRate = baudRate;
                _serialPort.DataBits = dataBits;
                _serialPort.Parity = parity;
                _serialPort.StopBits = stopBits;
                _serialPort.ReadTimeout = 500; // Default read timeout
                _serialPort.WriteTimeout = 500; // Default write timeout
                _serialPort.Open();
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error opening serial port {portName}: {ex.Message}");
                // TODO: Log this exception properly
                return false;
            }
        }

        public void ClosePort()
        {
            if (_serialPort != null && _serialPort.IsOpen)
            {
                _serialPort.Close();
            }
        }

        public bool SendData(byte[] data)
        {
            if (_serialPort == null || !_serialPort.IsOpen)
            {
                Console.WriteLine("Error: Serial port is not open.");
                return false;
            }
            try
            {
                _serialPort.Write(data, 0, data.Length);
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error sending data: {ex.Message}");
                // TODO: Log this exception properly
                return false;
            }
        }

        public byte[] ReadData(int expectedBytes, int timeoutMs)
        {
            if (_serialPort == null || !_serialPort.IsOpen)
            {
                Console.WriteLine("Error: Serial port is not open.");
                return null;
            }

            // TODO: Implement more robust timeout logic and byte accumulation.
            // The current SerialPort.ReadTimeout might not behave as expected for multi-byte reads
            // if data arrives sporadically. A custom loop with a timer might be better.

            byte[] buffer = new byte[expectedBytes];
            int bytesRead = 0;
            try
            {
                _serialPort.ReadTimeout = timeoutMs; // Set specific timeout for this read operation

                // Simple read attempt. This might not be sufficient for all scenarios.
                // Consider reading byte by byte with a more precise overall timeout.
                bytesRead = _serialPort.Read(buffer, 0, expectedBytes);

                if (bytesRead == expectedBytes)
                {
                    return buffer;
                }
                else if (bytesRead > 0)
                {
                    Console.WriteLine($"Warning: ReadData expected {expectedBytes} bytes, but received {bytesRead}.");
                    byte[] partialBuffer = new byte[bytesRead];
                    Array.Copy(buffer, partialBuffer, bytesRead);
                    return partialBuffer; // Return what was read
                }
                else
                {
                    Console.WriteLine("ReadData: No bytes read (timeout or other issue).");
                    return null;
                }
            }
            catch (TimeoutException)
            {
                Console.WriteLine("ReadData: Timeout occurred.");
                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error reading data: {ex.Message}");
                // TODO: Log this exception properly
                return null;
            }
        }

        public void Dispose()
        {
            ClosePort();
            if (_serialPort != null)
            {
                _serialPort.Dispose();
                // _serialPort = null; // Not strictly necessary if class instance is being disposed
            }
            GC.SuppressFinalize(this);
        }
    }
}
