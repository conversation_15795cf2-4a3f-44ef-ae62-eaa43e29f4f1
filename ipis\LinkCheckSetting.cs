// Decompiled with JetBrains decompiler
// Type: ipis.LinkCheckSetting
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using Microsoft.VisualBasic.CompilerServices;
using System.Diagnostics;

namespace ipis
{

public class LinkCheckSetting
{
  public static int ls_cnt;
  public static LinkCheckSetting.link_status_s[] ls_brd;

  [DebuggerNonUserCode]
  public LinkCheckSetting()
  {
  }

  public static void ls_brd_init()
  {
    LinkCheckSetting.ls_brd = new LinkCheckSetting.link_status_s[1001];
    int index = 0;
    while (index < 1000)
    {
      LinkCheckSetting.ls_brd[index].name = "";
      LinkCheckSetting.ls_brd[index].addr = (byte) 0;
      LinkCheckSetting.ls_brd[index].status = (byte) 2;
      checked { ++index; }
    }
    LinkCheckSetting.ls_cnt = 0;
  }

  public static byte update_link_status()
  {
    int index1 = 0;
    int num1 = 0;
    int index2 = 0;
    while (index2 < (int) taddb_msg.no_of_mldbs)
    {
      LinkCheckSetting.ls_brd[index1].name = taddb_msg.mldb_dis_brd.mdlb[index2].mldb_name;
      LinkCheckSetting.ls_brd[index1].addr = taddb_msg.mldb_dis_brd.mdlb[index2].mldb_addr;
      LinkCheckSetting.ls_brd[index1].status = taddb_msg.mldb_dis_brd.mdlb[index2].link_status;
      checked { ++index1; }
      checked { ++index2; }
    }
    int index3 = 0;
    int index4;
    while (index3 < frmMainFormIPIS.pfno_cnt)
    {
      index4 = 0;
      num1 = 0;
      if (taddb_msg.pdb_dis_brd[index3, index4].shared_platform)
      {
        int integer = Conversions.ToInteger(taddb_msg.pdb_dis_brd[index3, index4].shared_platform_no);
        if (index3 == integer)
          goto label_8;
      }
      while (index4 < (int) taddb_msg.no_of_pdbs_pfno[index3])
      {
        LinkCheckSetting.ls_brd[index1].addr = taddb_msg.pdb_dis_brd[index3, index4].pdb_addr;
        LinkCheckSetting.ls_brd[index1].name = taddb_msg.pdb_dis_brd[index3, index4].pdb_name;
        LinkCheckSetting.ls_brd[index1].status = taddb_msg.pdb_dis_brd[index3, index4].link_status;
        checked { ++index1; }
        checked { ++index4; }
      }
label_8:
      checked { ++index3; }
    }
    int index5 = 0;
    while (index5 < frmMainFormIPIS.pfno_cnt)
    {
      index4 = 0;
      num1 = 0;
      if (taddb_msg.agdb_dis_brd[index5, index4].shared_platform)
      {
        int integer = Conversions.ToInteger(taddb_msg.agdb_dis_brd[index5, index4].shared_platform_no);
        if (index5 == integer)
          goto label_15;
      }
      while (index4 < (int) taddb_msg.no_of_agdbs_pfno[index5])
      {
        LinkCheckSetting.ls_brd[index1].addr = taddb_msg.agdb_dis_brd[index5, index4].agdb_addr;
        LinkCheckSetting.ls_brd[index1].name = taddb_msg.agdb_dis_brd[index5, index4].agdb_name;
        LinkCheckSetting.ls_brd[index1].status = taddb_msg.agdb_dis_brd[index5, index4].link_status;
        checked { ++index1; }
        checked { ++index4; }
      }
label_15:
      checked { ++index5; }
    }
    int index6 = 0;
    while (index6 < (int) taddb_msg.no_of_com_agdbs[0])
    {
      LinkCheckSetting.ls_brd[index1].addr = taddb_msg.agdb_com_dis_brd[0, index6].agdb_addr;
      LinkCheckSetting.ls_brd[index1].name = taddb_msg.agdb_com_dis_brd[0, index6].agdb_name;
      LinkCheckSetting.ls_brd[index1].status = taddb_msg.agdb_com_dis_brd[0, index6].link_status;
      checked { ++index1; }
      checked { ++index6; }
    }
    int index7 = 0;
    while (index7 < frmMainFormIPIS.pfno_cnt)
    {
      index4 = 0;
      while (index4 < (int) cgdb_dis.no_of_cgdb[index7])
      {
        LinkCheckSetting.ls_brd[index1].addr = cgdb_dis.cgdb_dis_brd[index7, index4].cgdb_addr;
        LinkCheckSetting.ls_brd[index1].name = cgdb_dis.cgdb_dis_brd[index7, index4].cgdb_name;
        LinkCheckSetting.ls_brd[index1].status = cgdb_dis.cgdb_dis_brd[index7, index4].link_status;
        checked { ++index1; }
        checked { ++index4; }
      }
      checked { ++index7; }
    }
    LinkCheckSetting.ls_cnt = index1;
    int index8 = 0;
    int num2 = 0;
    byte num3 = 0;
    int index4_count = 0;
    while (index8 < LinkCheckSetting.ls_cnt)
    {
      if (LinkCheckSetting.ls_brd[index8].status == (byte) 2)
        checked { ++index4_count; }
      else if (LinkCheckSetting.ls_brd[index8].status == (byte) 1)
        checked { ++num2; }
      else if (LinkCheckSetting.ls_brd[index8].status == (byte) 0)
      {
        num3 = (byte) 0;
        goto label_39;
      }
      checked { ++index8; }
    }
    if (index8 == index4_count)
      num3 = (byte) 2;
    else if (index8 == num2)
      num3 = (byte) 1;
label_39:
    return num3;
  }

  public struct link_status_s
  {
    public string name;
    public byte addr;
    public byte status;
  }
}

}