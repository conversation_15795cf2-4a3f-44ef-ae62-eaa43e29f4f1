using System.Collections.Generic;

namespace ipis_V2_jules.Services.DisplayBoard.DisplayFormatters
{
    public static class SimpleFont
    {
        public const int FontHeight = 8; // Pixels
        public const int FontWidth = 8;  // Pixels, assuming 1 byte per column for an 8-pixel high font

        public static readonly Dictionary<char, byte[]> CharacterMap = new Dictionary<char, byte[]>
        {
            // Each byte represents a column of 8 pixels (LSB is top pixel, MSB is bottom pixel, or vice-versa)
            // For simplicity, let's assume LSB is top pixel.
            // These are example patterns. Actual patterns depend on the desired font appearance.
            // An 8-byte array per character would mean each byte is a *row* if font is 8x8.
            // If fontHeight is 8, and each character is represented by 8 bytes, it means each byte is a *column*.
            // The prompt's example 'A': new byte[] { 0x1C, 0x36, 0x63, 0x63, 0x7F, 0x63, 0x63, 0x00 } implies 8 bytes wide.
            // Let's assume each char is 8 bytes wide (8 columns), and each byte in the array is a column.

            // Example for 'A' (8x8 pixels, each byte is a column, LSB is top pixel)
            //   ####
            //  ##  ##
            // ##    ##
            // ##    ##
            // ########
            // ##    ##
            // ##    ##
            // (blank row)
            { 'A', new byte[] { 0x7E, 0x18, 0x18, 0x18, 0x18, 0x18, 0x7E, 0x00 } }, // Simplified A (different from prompt example)
            // Prompt example 'A': { 0x1C, 0x36, 0x63, 0x63, 0x7F, 0x63, 0x63, 0x00 } - This is 8 bytes, so likely 8 columns.

            { 'B', new byte[] { 0x7F, 0x63, 0x63, 0x7E, 0x63, 0x63, 0x7F, 0x00 } }, // Using prompt example
            { 'C', new byte[] { 0x3E, 0x63, 0x03, 0x03, 0x03, 0x63, 0x3E, 0x00 } }, // Placeholder 'C'
            { ' ', new byte[] { 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00 } },
            { '0', new byte[] { 0x7E, 0x63, 0x6B, 0x73, 0x6B, 0x63, 0x7E, 0x00 } }, // Placeholder '0'
            { '1', new byte[] { 0x18, 0x38, 0x18, 0x18, 0x18, 0x18, 0x7E, 0x00 } }, // Using prompt example
            { '.', new byte[] { 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x60, 0x00 } }, // Placeholder '.' (bottom)
            { ':', new byte[] { 0x00, 0x00, 0x24, 0x00, 0x24, 0x00, 0x00, 0x00 } }, // Placeholder ':' (centered)
            // Add more characters as needed
        };

        /// <summary>
        /// Gets the bitmap (byte array) for a character.
        /// </summary>
        /// <param name="c">The character.</param>
        /// <param name="fontHeight">Expected font height (currently fixed to 8 in this implementation).</param>
        /// <returns>Byte array representing the character's bitmap, or space if not found.</returns>
        public static byte[] GetCharBitmap(char c, int fontHeight = FontHeight)
        {
            if (fontHeight != FontHeight)
            {
                // This basic implementation only supports the fixed FontHeight.
                // For dynamic heights, more complex font data or scaling would be needed.
                // Returning space for unsupported heights.
                return CharacterMap.TryGetValue(' ', out byte[]? spaceDefault) ? spaceDefault : new byte[FontWidth];
            }

            if (CharacterMap.TryGetValue(char.ToUpper(c), out byte[]? bitmap)) // Assuming font data is for uppercase
            {
                return bitmap;
            }
            // Fallback for lowercase if uppercase not found (simple case, could be more sophisticated)
            if (CharacterMap.TryGetValue(c, out bitmap))
            {
                 return bitmap;
            }
            return CharacterMap.TryGetValue(' ', out byte[]? spaceFallback) ? spaceFallback : new byte[FontWidth]; // Default to space
        }
    }
}
