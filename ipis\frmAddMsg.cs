// Decompiled with JetBrains decompiler
// Type: ipis.frmAddMsg
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using ipis.My;
using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

namespace ipis
{

[DesignerGenerated]
public class frmAddMsg : Form
{
  private static List<WeakReference> __ENCList = new List<WeakReference>();
  private IContainer components;
  [AccessedThroughProperty("lblLang")]
  private Label _lblLang;
  [AccessedThroughProperty("txtMessage")]
  private TextBox _txtMessage;
  [AccessedThroughProperty("lblMsgName")]
  private Label _lblMsgName;
  [AccessedThroughProperty("cmbLang")]
  private ComboBox _cmbLang;
  [AccessedThroughProperty("lblMsgId")]
  private Label _lblMsgId;
  [AccessedThroughProperty("btnExit")]
  private Button _btnExit;
  [AccessedThroughProperty("btnSave")]
  private Button _btnSave;
  [AccessedThroughProperty("btnDelete")]
  private Button _btnDelete;
  [AccessedThroughProperty("btnAdd")]
  private Button _btnAdd;
  [AccessedThroughProperty("cmbMsgId")]
  private ComboBox _cmbMsgId;
  [AccessedThroughProperty("btnCancel")]
  private Button _btnCancel;
  [AccessedThroughProperty("btnEdit")]
  private Button _btnEdit;

  [DebuggerNonUserCode]
  static frmAddMsg()
  {
  }

  [DebuggerNonUserCode]
  public frmAddMsg()
  {
    this.Load += new EventHandler(this.frmAddMsg_Load);
    frmAddMsg.__ENCAddToList((object) this);
    this.InitializeComponent();
  }

  [DebuggerNonUserCode]
  private static void __ENCAddToList(object value)
  {
    lock (frmAddMsg.__ENCList)
    {
      if (frmAddMsg.__ENCList.Count == frmAddMsg.__ENCList.Capacity)
      {
        int index1 = 0;
        int num = checked (frmAddMsg.__ENCList.Count - 1);
        int index2 = 0;
        while (index2 <= num)
        {
          if (frmAddMsg.__ENCList[index2].IsAlive)
          {
            if (index2 != index1)
              frmAddMsg.__ENCList[index1] = frmAddMsg.__ENCList[index2];
            checked { ++index1; }
          }
          checked { ++index2; }
        }
        frmAddMsg.__ENCList.RemoveRange(index1, checked (frmAddMsg.__ENCList.Count - index1));
        frmAddMsg.__ENCList.Capacity = frmAddMsg.__ENCList.Count;
      }
      frmAddMsg.__ENCList.Add(new WeakReference(RuntimeHelpers.GetObjectValue(value)));
    }
  }

  [DebuggerNonUserCode]
  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  [DebuggerStepThrough]
  private void InitializeComponent()
  {
    this.lblLang = new Label();
    this.txtMessage = new TextBox();
    this.lblMsgName = new Label();
    this.cmbLang = new ComboBox();
    this.lblMsgId = new Label();
    this.btnExit = new Button();
    this.btnSave = new Button();
    this.btnDelete = new Button();
    this.btnAdd = new Button();
    this.cmbMsgId = new ComboBox();
    this.btnCancel = new Button();
    this.btnEdit = new Button();
    this.SuspendLayout();
    this.lblLang.AutoSize = true;
    this.lblLang.BackColor = Color.PowderBlue;
    this.lblLang.Enabled = false;
    this.lblLang.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.lblLang.ForeColor = SystemColors.ControlText;
    Label lblLang1 = this.lblLang;
    Point point1 = new Point(78, 68);
    Point point2 = point1;
    lblLang1.Location = point2;
    this.lblLang.Name = "lblLang";
    Label lblLang2 = this.lblLang;
    Size size1 = new Size(77, 16 /*0x10*/);
    Size size2 = size1;
    lblLang2.Size = size2;
    this.lblLang.TabIndex = 62;
    this.lblLang.Text = "Language";
    this.txtMessage.Enabled = false;
    this.txtMessage.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtMessage1 = this.txtMessage;
    point1 = new Point(185, 112 /*0x70*/);
    Point point3 = point1;
    txtMessage1.Location = point3;
    this.txtMessage.MaxLength = 100;
    this.txtMessage.Name = "txtMessage";
    TextBox txtMessage2 = this.txtMessage;
    size1 = new Size(409, 22);
    Size size3 = size1;
    txtMessage2.Size = size3;
    this.txtMessage.TabIndex = 3;
    this.lblMsgName.AutoSize = true;
    this.lblMsgName.Enabled = false;
    this.lblMsgName.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.lblMsgName.ForeColor = SystemColors.ControlText;
    Label lblMsgName1 = this.lblMsgName;
    point1 = new Point(38, 115);
    Point point4 = point1;
    lblMsgName1.Location = point4;
    this.lblMsgName.Name = "lblMsgName";
    Label lblMsgName2 = this.lblMsgName;
    size1 = new Size(117, 16 /*0x10*/);
    Size size4 = size1;
    lblMsgName2.Size = size4;
    this.lblMsgName.TabIndex = 59;
    this.lblMsgName.Text = "Message Name";
    this.cmbLang.Enabled = false;
    this.cmbLang.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.cmbLang.FormattingEnabled = true;
    this.cmbLang.Items.AddRange(new object[21]
    {
      (object) "Assamese",
      (object) "Bengali",
      (object) "Bhilli",
      (object) "Bhojpuri",
      (object) "Bihari",
      (object) "Devanagiri",
      (object) "English",
      (object) "Gujarati",
      (object) "Hindi",
      (object) "Kannada",
      (object) "Kashmiri",
      (object) "Konkani",
      (object) "Marathi",
      (object) "Marwari",
      (object) "Nepali",
      (object) "Oriya",
      (object) "Pahari",
      (object) "Punjabi",
      (object) "Santhali",
      (object) "Tamil",
      (object) "Telugu"
    });
    ComboBox cmbLang1 = this.cmbLang;
    point1 = new Point(185, 60);
    Point point5 = point1;
    cmbLang1.Location = point5;
    this.cmbLang.Name = "cmbLang";
    ComboBox cmbLang2 = this.cmbLang;
    size1 = new Size(83, 24);
    Size size5 = size1;
    cmbLang2.Size = size5;
    this.cmbLang.Sorted = true;
    this.cmbLang.TabIndex = 2;
    this.cmbLang.Tag = (object) "";
    this.cmbLang.Text = "English";
    this.lblMsgId.Enabled = false;
    this.lblMsgId.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.lblMsgId.ForeColor = SystemColors.ControlText;
    Label lblMsgId1 = this.lblMsgId;
    point1 = new Point(61, 25);
    Point point6 = point1;
    lblMsgId1.Location = point6;
    this.lblMsgId.Name = "lblMsgId";
    Label lblMsgId2 = this.lblMsgId;
    size1 = new Size(104, 20);
    Size size6 = size1;
    lblMsgId2.Size = size6;
    this.lblMsgId.TabIndex = 57;
    this.lblMsgId.Text = "Message ID";
    this.btnExit.BackColor = SystemColors.ButtonFace;
    this.btnExit.DialogResult = DialogResult.Cancel;
    this.btnExit.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.btnExit.ForeColor = SystemColors.ControlText;
    Button btnExit1 = this.btnExit;
    point1 = new Point(488, 162);
    Point point7 = point1;
    btnExit1.Location = point7;
    this.btnExit.Name = "btnExit";
    Button btnExit2 = this.btnExit;
    size1 = new Size(51, 23);
    Size size7 = size1;
    btnExit2.Size = size7;
    this.btnExit.TabIndex = 8;
    this.btnExit.Text = "E&xit";
    this.btnExit.UseVisualStyleBackColor = false;
    this.btnSave.BackColor = SystemColors.ButtonFace;
    this.btnSave.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.btnSave.ForeColor = SystemColors.ControlText;
    Button btnSave1 = this.btnSave;
    point1 = new Point(209, 162);
    Point point8 = point1;
    btnSave1.Location = point8;
    this.btnSave.Name = "btnSave";
    Button btnSave2 = this.btnSave;
    size1 = new Size(59, 23);
    Size size8 = size1;
    btnSave2.Size = size8;
    this.btnSave.TabIndex = 5;
    this.btnSave.Text = "&Save";
    this.btnSave.UseVisualStyleBackColor = false;
    this.btnDelete.BackColor = SystemColors.ButtonFace;
    this.btnDelete.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.btnDelete.ForeColor = SystemColors.ControlText;
    Button btnDelete1 = this.btnDelete;
    point1 = new Point(297, 162);
    Point point9 = point1;
    btnDelete1.Location = point9;
    this.btnDelete.Name = "btnDelete";
    Button btnDelete2 = this.btnDelete;
    size1 = new Size(64 /*0x40*/, 23);
    Size size9 = size1;
    btnDelete2.Size = size9;
    this.btnDelete.TabIndex = 6;
    this.btnDelete.Text = "&Delete";
    this.btnDelete.UseVisualStyleBackColor = false;
    this.btnAdd.BackColor = SystemColors.ButtonFace;
    this.btnAdd.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.btnAdd.ForeColor = SystemColors.ControlText;
    Button btnAdd1 = this.btnAdd;
    point1 = new Point(64 /*0x40*/, 162);
    Point point10 = point1;
    btnAdd1.Location = point10;
    this.btnAdd.Name = "btnAdd";
    Button btnAdd2 = this.btnAdd;
    size1 = new Size(48 /*0x30*/, 23);
    Size size10 = size1;
    btnAdd2.Size = size10;
    this.btnAdd.TabIndex = 4;
    this.btnAdd.Text = "&Add ";
    this.btnAdd.UseVisualStyleBackColor = false;
    this.cmbMsgId.Enabled = false;
    this.cmbMsgId.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.cmbMsgId.FormattingEnabled = true;
    ComboBox cmbMsgId1 = this.cmbMsgId;
    point1 = new Point(185, 21);
    Point point11 = point1;
    cmbMsgId1.Location = point11;
    this.cmbMsgId.Name = "cmbMsgId";
    ComboBox cmbMsgId2 = this.cmbMsgId;
    size1 = new Size(83, 24);
    Size size11 = size1;
    cmbMsgId2.Size = size11;
    this.cmbMsgId.Sorted = true;
    this.cmbMsgId.TabIndex = 1;
    this.btnCancel.BackColor = SystemColors.ButtonFace;
    this.btnCancel.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.btnCancel.ForeColor = SystemColors.ControlText;
    Button btnCancel1 = this.btnCancel;
    point1 = new Point(378, 162);
    Point point12 = point1;
    btnCancel1.Location = point12;
    this.btnCancel.Name = "btnCancel";
    Button btnCancel2 = this.btnCancel;
    size1 = new Size(75, 23);
    Size size12 = size1;
    btnCancel2.Size = size12;
    this.btnCancel.TabIndex = 7;
    this.btnCancel.Text = "&Cancel";
    this.btnCancel.UseVisualStyleBackColor = false;
    this.btnEdit.BackColor = SystemColors.ButtonFace;
    this.btnEdit.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.btnEdit.ForeColor = SystemColors.ControlText;
    Button btnEdit1 = this.btnEdit;
    point1 = new Point(136, 162);
    Point point13 = point1;
    btnEdit1.Location = point13;
    this.btnEdit.Name = "btnEdit";
    Button btnEdit2 = this.btnEdit;
    size1 = new Size(48 /*0x30*/, 23);
    Size size13 = size1;
    btnEdit2.Size = size13;
    this.btnEdit.TabIndex = 63 /*0x3F*/;
    this.btnEdit.Text = "&Edit";
    this.btnEdit.UseVisualStyleBackColor = false;
    this.AcceptButton = (IButtonControl) this.btnAdd;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.BackColor = Color.PowderBlue;
    this.CancelButton = (IButtonControl) this.btnExit;
    size1 = new Size(623, 207);
    this.ClientSize = size1;
    this.Controls.Add((Control) this.btnEdit);
    this.Controls.Add((Control) this.btnCancel);
    this.Controls.Add((Control) this.cmbMsgId);
    this.Controls.Add((Control) this.btnExit);
    this.Controls.Add((Control) this.btnSave);
    this.Controls.Add((Control) this.btnDelete);
    this.Controls.Add((Control) this.btnAdd);
    this.Controls.Add((Control) this.lblLang);
    this.Controls.Add((Control) this.txtMessage);
    this.Controls.Add((Control) this.lblMsgName);
    this.Controls.Add((Control) this.cmbLang);
    this.Controls.Add((Control) this.lblMsgId);
    this.FormBorderStyle = FormBorderStyle.FixedToolWindow;
    this.Name = "frmAddMsg";
    this.Text = "Add Message";
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  internal virtual Label lblLang
  {
    [DebuggerNonUserCode] get { return this._lblLang; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._lblLang = value; }
  }

  internal virtual TextBox txtMessage
  {
    [DebuggerNonUserCode] get { return this._txtMessage; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtMessage = value;
    }
  }

  internal virtual Label lblMsgName
  {
    [DebuggerNonUserCode] get { return this._lblMsgName; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblMsgName = value;
    }
  }

  internal virtual ComboBox cmbLang
  {
    [DebuggerNonUserCode] get { return this._cmbLang; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbLang = value; }
  }

  internal virtual Label lblMsgId
  {
    [DebuggerNonUserCode] get { return this._lblMsgId; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._lblMsgId = value; }
  }

  internal virtual Button btnExit
  {
    [DebuggerNonUserCode] get { return this._btnExit; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnExit_Click);
      if (this._btnExit != null)
        this._btnExit.Click -= eventHandler;
      this._btnExit = value;
      if (this._btnExit == null)
        return;
      this._btnExit.Click += eventHandler;
    }
  }

  internal virtual Button btnSave
  {
    [DebuggerNonUserCode] get { return this._btnSave; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnSave_Click);
      if (this._btnSave != null)
        this._btnSave.Click -= eventHandler;
      this._btnSave = value;
      if (this._btnSave == null)
        return;
      this._btnSave.Click += eventHandler;
    }
  }

  internal virtual Button btnDelete
  {
    [DebuggerNonUserCode] get { return this._btnDelete; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnDelete_Click);
      if (this._btnDelete != null)
        this._btnDelete.Click -= eventHandler;
      this._btnDelete = value;
      if (this._btnDelete == null)
        return;
      this._btnDelete.Click += eventHandler;
    }
  }

  internal virtual Button btnAdd
  {
    [DebuggerNonUserCode] get { return this._btnAdd; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnAdd_Click);
      if (this._btnAdd != null)
        this._btnAdd.Click -= eventHandler;
      this._btnAdd = value;
      if (this._btnAdd == null)
        return;
      this._btnAdd.Click += eventHandler;
    }
  }

  internal virtual ComboBox cmbMsgId
  {
    [DebuggerNonUserCode] get { return this._cmbMsgId; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.cmbMsgId_SelectedIndexChanged);
      if (this._cmbMsgId != null)
        this._cmbMsgId.SelectedIndexChanged -= eventHandler;
      this._cmbMsgId = value;
      if (this._cmbMsgId == null)
        return;
      this._cmbMsgId.SelectedIndexChanged += eventHandler;
    }
  }

  internal virtual Button btnCancel
  {
    [DebuggerNonUserCode] get { return this._btnCancel; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnCancel_Click);
      if (this._btnCancel != null)
        this._btnCancel.Click -= eventHandler;
      this._btnCancel = value;
      if (this._btnCancel == null)
        return;
      this._btnCancel.Click += eventHandler;
    }
  }

  internal virtual Button btnEdit
  {
    [DebuggerNonUserCode] get { return this._btnEdit; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnEdit_Click);
      if (this._btnEdit != null)
        this._btnEdit.Click -= eventHandler;
      this._btnEdit = value;
      if (this._btnEdit == null)
        return;
      this._btnEdit.Click += eventHandler;
    }
  }

  private void btnAdd_Click(object sender, EventArgs e)
  {
    this.lblLang.Enabled = true;
    this.lblMsgId.Enabled = true;
    this.lblMsgName.Enabled = true;
    this.cmbLang.Enabled = true;
    this.cmbMsgId.Enabled = true;
    this.txtMessage.Enabled = true;
    this.cmbLang.Text = "";
    this.cmbMsgId.Text = "";
    this.txtMessage.Text = "";
    this.btnDelete.Enabled = false;
    this.btnSave.Enabled = true;
    this.btnEdit.Enabled = false;
  }

  private void btnSave_Click(object sender, EventArgs e)
  {
    bool result = false;
    if (Operators.CompareString(this.cmbMsgId.Text, string.Empty, false) == 0)
    {
      int num1 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Please enter Message ID ", "Msg Box", 0, 0, 0);
    }
    else
    {
      try
      {
        if (this.btnAdd.Enabled)
        {
          int count1 = MyProject.Forms.frmMainFormIPIS.msg_dgv.Rows.Count;
          int num2 = count1 != 0 ? frmMainFormIPIS.msg_struct[checked (count1 - 1)].msg_row_no : 50;
          network_db_read.set_msg(this.cmbMsgId.Text, this.cmbLang.Text, this.txtMessage.Text, Conversions.ToString(checked (num2 + 1)), ref result);
          if (result)
          {
            int num3 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Message Added ", "Msg Box", 0, 0, 0);
            int count2 = MyProject.Forms.frmMainFormIPIS.msg_dgv.Rows.Count;
            MyProject.Forms.frmMainFormIPIS.msg_dgv.Rows.Add();
            MyProject.Forms.frmMainFormIPIS.msg_dgv[0, count2].Value = (object) this.cmbMsgId.Text;
            MyProject.Forms.frmMainFormIPIS.msg_dgv[1, count2].Value = (object) this.cmbLang.Text;
            MyProject.Forms.frmMainFormIPIS.msg_dgv[2, count2].Value = (object) this.txtMessage.Text;
            frmMainFormIPIS.msg_struct[count2].msg_id = this.cmbMsgId.Text;
            frmMainFormIPIS.msg_struct[count2].msg_name = this.txtMessage.Text;
            frmMainFormIPIS.msg_struct[count2].language_name = this.cmbLang.Text;
            frmMainFormIPIS.msg_struct[count2].msg_row_no = checked (num2 + 1);
            checked { ++frmMainFormIPIS.addmsg_count; }
            int index = 0;
            this.cmbMsgId.Items.Clear();
            while (index < frmMainFormIPIS.addmsg_count)
            {
              this.cmbMsgId.Items.Add((object) frmMainFormIPIS.msg_struct[index].msg_id);
              checked { ++index; }
            }
          }
          else
          {
            int num4 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Message  not Added ", "Msg Box", 0, 0, 0);
          }
        }
        else if (this.btnEdit.Enabled)
        {
          string text = this.cmbMsgId.Text;
          int rowIndex = 0;
          int msgRowNo = 0;
          while (rowIndex < frmMainFormIPIS.addmsg_count)
          {
            if (Operators.CompareString(frmMainFormIPIS.msg_struct[rowIndex].msg_id, this.cmbMsgId.Text, false) == 0)
            {
              msgRowNo = frmMainFormIPIS.msg_struct[rowIndex].msg_row_no;
              break;
            }
            checked { ++rowIndex; }
          }
          network_db_read.set_Update_msg(this.cmbMsgId.Text, this.cmbLang.Text, this.txtMessage.Text, Conversions.ToString(msgRowNo), ref result);
          if (result)
          {
            int num5 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Message Updated ", "Msg Box", 0, 0, 0);
            MyProject.Forms.frmMainFormIPIS.msg_dgv[0, rowIndex].Value = (object) this.cmbMsgId.Text;
            MyProject.Forms.frmMainFormIPIS.msg_dgv[1, rowIndex].Value = (object) this.cmbLang.Text;
            MyProject.Forms.frmMainFormIPIS.msg_dgv[2, rowIndex].Value = (object) this.txtMessage.Text;
            frmMainFormIPIS.msg_struct[rowIndex].msg_id = this.cmbMsgId.Text;
            frmMainFormIPIS.msg_struct[rowIndex].msg_name = this.txtMessage.Text;
            frmMainFormIPIS.msg_struct[rowIndex].language_name = this.cmbLang.Text;
          }
          else
          {
            int num6 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Message not Updated ", "Msg Box", 0, 0, 0);
          }
        }
        this.cmbLang.Text = "";
        this.cmbMsgId.Text = "";
        this.txtMessage.Text = "";
        this.btnAdd.Enabled = true;
        this.btnEdit.Enabled = true;
        this.btnDelete.Enabled = true;
        this.btnSave.Enabled = false;
        this.lblLang.Enabled = false;
        this.lblMsgId.Enabled = false;
        this.lblMsgName.Enabled = false;
        this.cmbLang.Enabled = false;
        this.cmbMsgId.Enabled = false;
        this.txtMessage.Enabled = false;
      }
      catch (Exception ex)
      {
        ProjectData.SetProjectError(ex);
        Exception exception = ex;
        int num7 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
        ProjectData.ClearProjectError();
      }
      try
      {
        string str = "Z:\\Database\\message_db.mdb";
        string sourceFileName = "C:\\IPIS\\Database\\message_db.mdb";
        if (!File.Exists(str))
          File.Create(str);
        bool overwrite = true;
        MyProject.Computer.FileSystem.CopyFile(sourceFileName, str, overwrite);
      }
      catch (Exception ex)
      {
        ProjectData.SetProjectError(ex);
        ProjectData.ClearProjectError();
      }
    }
  }

  private void btnDelete_Click(object sender, EventArgs e)
  {
    this.btnAdd.Enabled = false;
    this.btnSave.Enabled = false;
    this.lblLang.Enabled = true;
    this.lblMsgId.Enabled = true;
    this.lblMsgName.Enabled = true;
    this.cmbLang.Enabled = true;
    this.cmbMsgId.Enabled = true;
    this.txtMessage.Enabled = true;
  }

  private void cmbMsgId_SelectedIndexChanged(object sender, EventArgs e)
  {
    int index1 = 0;
    try
    {
      while (index1 < frmMainFormIPIS.addmsg_count)
      {
        if (Operators.CompareString(frmMainFormIPIS.msg_struct[index1].msg_id, this.cmbMsgId.Text, false) == 0)
        {
          this.cmbMsgId.Text = frmMainFormIPIS.msg_struct[index1].msg_id;
          this.cmbLang.Text = frmMainFormIPIS.msg_struct[index1].language_name;
          this.txtMessage.Text = frmMainFormIPIS.msg_struct[index1].msg_name;
        }
        checked { ++index1; }
      }
      if (this.btnDelete.Enabled)
      {
        bool result = false;
        int num1 = 0;
        try
        {
          num1 = 0;
          if (!this.cmbMsgId.Enabled)
          {
            this.cmbMsgId.Enabled = true;
            return;
          }
          if (Operators.CompareString(this.cmbMsgId.Text, "", false) == 0)
          {
            this.cmbMsgId.Enabled = true;
            return;
          }
          int index2 = 0;
          while (index2 < frmMainFormIPIS.addmsg_count && Operators.CompareString(Strings.Trim(frmMainFormIPIS.msg_struct[index2].msg_id), this.cmbMsgId.Text, false) != 0)
            checked { ++index2; }
          network_db_read.delete_msg(this.cmbMsgId.Text, ref result);
          if (result)
          {
            int num2 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Deleted Successfully", "Msg Box", 0, 0, 0);
            MyProject.Forms.frmMainFormIPIS.msg_dgv.Rows.RemoveAt(index2);
            while (index2 < MyProject.Forms.frmMainFormIPIS.msg_dgv.Rows.Count)
            {
              frmMainFormIPIS.msg_struct[index2].language_name = frmMainFormIPIS.msg_struct[checked (index2 + 1)].language_name;
              frmMainFormIPIS.msg_struct[index2].msg_row_no = frmMainFormIPIS.msg_struct[checked (index2 + 1)].msg_row_no;
              frmMainFormIPIS.msg_struct[index2].msg_name = frmMainFormIPIS.msg_struct[checked (index2 + 1)].msg_name;
              frmMainFormIPIS.msg_struct[index2].msg_id = frmMainFormIPIS.msg_struct[checked (index2 + 1)].msg_id;
              checked { ++index2; }
            }
            frmMainFormIPIS.msg_struct[index2].language_name = string.Empty;
            frmMainFormIPIS.msg_struct[index2].msg_row_no = 0;
            frmMainFormIPIS.msg_struct[index2].msg_name = string.Empty;
            checked { --frmMainFormIPIS.addmsg_count; }
            int index3 = 0;
            this.cmbMsgId.Items.Clear();
            while (index3 < frmMainFormIPIS.addmsg_count)
            {
              this.cmbMsgId.Items.Add((object) frmMainFormIPIS.msg_struct[index3].msg_id);
              checked { ++index3; }
            }
          }
          else
          {
            int num3 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "not Deleted", "Msg Box", 0, 0, 0);
          }
        }
        catch (Exception ex)
        {
          ProjectData.SetProjectError(ex);
          Exception exception = ex;
          int num4 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
          ProjectData.ClearProjectError();
        }
        this.cmbLang.Text = "";
        this.cmbMsgId.Text = "";
        this.txtMessage.Text = "";
        this.btnAdd.Enabled = true;
        this.btnSave.Enabled = false;
        this.btnEdit.Enabled = true;
        this.lblLang.Enabled = false;
        this.lblMsgId.Enabled = false;
        this.lblMsgName.Enabled = false;
        this.cmbLang.Enabled = false;
        this.cmbMsgId.Enabled = false;
        this.txtMessage.Enabled = false;
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    try
    {
      string str = "Z:\\Database\\message_db.mdb";
      string sourceFileName = "C:\\IPIS\\Database\\message_db.mdb";
      if (!File.Exists(str))
        File.Create(str);
      bool overwrite = true;
      MyProject.Computer.FileSystem.CopyFile(sourceFileName, str, overwrite);
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
  }

  private void frmAddMsg_Load(object sender, EventArgs e)
  {
    int index = 0;
    try
    {
      while (index < frmMainFormIPIS.addmsg_count)
      {
        this.cmbMsgId.Items.Add((object) frmMainFormIPIS.msg_struct[index].msg_id);
        checked { ++index; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void btnExit_Click(object sender, EventArgs e)
{
  this.Close();
}

  private void btnCancel_Click(object sender, EventArgs e)
  {
    this.cmbLang.Text = "";
    this.cmbMsgId.Text = "";
    this.txtMessage.Text = "";
    this.lblLang.Enabled = false;
    this.lblMsgId.Enabled = false;
    this.lblMsgName.Enabled = false;
    this.cmbLang.Enabled = false;
    this.cmbMsgId.Enabled = false;
    this.txtMessage.Enabled = false;
    this.btnDelete.Enabled = true;
    this.btnSave.Enabled = false;
    this.btnAdd.Enabled = true;
    this.btnEdit.Enabled = true;
  }

  private void btnEdit_Click(object sender, EventArgs e)
  {
    this.btnAdd.Enabled = false;
    this.btnSave.Enabled = true;
    this.btnDelete.Enabled = false;
    this.cmbLang.Text = "";
    this.cmbMsgId.Text = "";
    this.txtMessage.Text = "";
    this.lblLang.Enabled = true;
    this.lblMsgId.Enabled = true;
    this.lblMsgName.Enabled = true;
    this.cmbLang.Enabled = true;
    this.cmbMsgId.Enabled = true;
    this.txtMessage.Enabled = true;
  }
}

}