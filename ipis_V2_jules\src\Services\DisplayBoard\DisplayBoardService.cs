using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ipis_V2_jules.Data; // For DatabaseHelper
using ipis_V2_jules.ApiClients; // For TrainDataErail
using ipis_V2_jules.Hardware.Clients; // For IBoardClient and specific clients
using ipis_V2_jules.DisplayFormatters; // For IDisplayDataFormatter and specific formatters
using ipis_V2_jules.Models; // For DisplayBoardConfig
using ipis_V2_jules.Services.DisplayBoard.Hardware.Communication; // For ICommunicationService and SerialCommunicationService
using System.IO.Ports; // For Parity, StopBits

namespace ipis_V2_jules.Services.DisplayBoard
{
    public class DisplayBoardService : IDisposable
    {
        private readonly DatabaseHelper _databaseHelper;
        private readonly IServiceProvider _serviceProvider; // For potential future use with DI

        private readonly Dictionary<int, IBoardClient> _boardClients;
        private readonly Dictionary<int, DisplayBoardConfig> _boardConfigs;
        private readonly List<ICommunicationService> _communicationServices; // To manage and dispose them

        public DisplayBoardService(DatabaseHelper databaseHelper, IServiceProvider serviceProvider)
        {
            _databaseHelper = databaseHelper ?? throw new ArgumentNullException(nameof(databaseHelper));
            _serviceProvider = serviceProvider; // May not be used in this iteration if direct instantiation is done

            _boardClients = new Dictionary<int, IBoardClient>();
            _boardConfigs = new Dictionary<int, DisplayBoardConfig>();
            _communicationServices = new List<ICommunicationService>();
        }

        public async Task InitializeBoardsAsync()
        {
            Console.WriteLine("DisplayBoardService: Initializing boards...");
            List<DisplayBoardConfig> configs = new List<DisplayBoardConfig>();
            try
            {
                configs = await _databaseHelper.GetAllDisplayBoardConfigsAsync();
                Console.WriteLine($"DisplayBoardService: Found {configs.Count} board configurations in the database.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"DisplayBoardService: Error loading board configurations from database: {ex.Message}");
                // Depending on requirements, could throw or continue without boards
                return;
            }

            if (!configs.Any())
            {
                Console.WriteLine("DisplayBoardService: No board configurations found. Ensure DisplayBoardConfig table is populated and DatabaseHelper query is correct.");
            }


            foreach (var config in configs)
            {
                if (config.PortName == null)
                {
                    Console.WriteLine($"DisplayBoardService: Skipping board {config.BoardName} (ID: {config.BoardId}) due to missing PortName.");
                    continue;
                }

                _boardConfigs[config.BoardId] = config;
                Console.WriteLine($"DisplayBoardService: Processing board {config.BoardName} (ID: {config.BoardId}, Type: {config.BoardType}, Port: {config.PortName})");

                // For now, creating a new communication service for each client.
                // Optimization: Share service if PortName, BaudRate etc. are identical.
                ICommunicationService commService = new SerialCommunicationService(
                    config.PortName,
                    config.BaudRate,
                    config.Parity,
                    config.DataBits,
                    config.StopBits
                );
                _communicationServices.Add(commService); // Keep track for disposal

                IBoardClient? client = null;
                IDisplayDataFormatter? formatter = null;

                try
                {
                    switch (config.BoardType?.ToUpperInvariant())
                    {
                        case "AGDB":
                            formatter = new AgdbDataFormatter();
                            client = new AgdbClient(commService, (AgdbDataFormatter)formatter, config);
                            break;
                        case "MLDB":
                            formatter = new MldbDataFormatter();
                            client = new MldbClient(commService, (MldbDataFormatter)formatter, config);
                            break;
                        case "PDB":
                            formatter = new PdbDataFormatter();
                            client = new PdbClient(commService, (PdbDataFormatter)formatter, config);
                            break;
                        case "CGDB":
                            formatter = new CgdbDataFormatter();
                            client = new CgdbClient(commService, (CgdbDataFormatter)formatter, config);
                            break;
                        case "TADDB":
                            formatter = new TaddbDataFormatter();
                            client = new TaddbClient(commService, (TaddbDataFormatter)formatter, config);
                            break;
                        default:
                            Console.WriteLine($"DisplayBoardService: Unknown board type '{config.BoardType}' for board ID {config.BoardId}. Client not created.");
                            _communicationServices.Remove(commService); // Don't track commService if client not created
                            commService.Dispose(); // Dispose immediately
                            continue;
                    }

                    if (!commService.OpenPort()) // Try to open port after client is created
                    {
                        Console.WriteLine($"DisplayBoardService: Failed to open port {config.PortName} for board ID {config.BoardId}. Client will be non-operational.");
                        // Client is created but port is not open. Its Status should reflect this.
                    }
                    else
                    {
                         Console.WriteLine($"DisplayBoardService: Port {config.PortName} opened for board ID {config.BoardId}.");
                    }

                    _boardClients[config.BoardId] = client;
                    Console.WriteLine($"DisplayBoardService: Client created for board ID {config.BoardId} ({config.BoardType}).");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"DisplayBoardService: Error creating client or opening port for board ID {config.BoardId}. Type: {config.BoardType}. Error: {ex.Message}");
                    if (commService != null && !_communicationServices.Contains(commService))
                    {
                        // If commService was created but client creation failed before adding to list.
                        commService.Dispose();
                    }
                    else if (commService != null && client == null)
                    {
                        // If client creation failed but commService was added to list.
                         _communicationServices.Remove(commService);
                         commService.Dispose();
                    }
                }
            }
            Console.WriteLine("DisplayBoardService: Board initialization complete.");
        }

        public async Task SendMessageToBoardAsync(int boardId, string message)
        {
            if (_boardClients.TryGetValue(boardId, out IBoardClient? client) && client != null)
            {
                Console.WriteLine($"DisplayBoardService: Sending message to BoardID {boardId}.");
                // The specific clients (AgdbClient, MldbClient etc.) have a SendMessageAsync(string) overload.
                // We need to cast to the concrete type or add SendMessageAsync(string) to IBoardClient.
                // For now, let's assume the client has a method that can take a string directly or we adapt.
                // To call the specific SendMessageAsync(string) method:
                if (client is AgdbClient agdbClient) await agdbClient.SendMessageAsync(message);
                else if (client is MldbClient mldbClient) await mldbClient.SendMessageAsync(message);
                else if (client is PdbClient pdbClient) await pdbClient.SendMessageAsync(message);
                else if (client is CgdbClient cgdbClient) await cgdbClient.SendMessageAsync(message);
                else if (client is TaddbClient taddbClient) await taddbClient.SendMessageAsync(message);
                else
                {
                    // Fallback if client type doesn't have specific string overload,
                    // requires IDisplayDataFormatter to be accessible or passed.
                    // This path is less ideal as formatter type is unknown here.
                    Console.WriteLine($"DisplayBoardService: BoardID {boardId} client type unknown for direct string message. Manual formatting would be needed.");
                }
            }
            else
            {
                Console.WriteLine($"DisplayBoardService: Board with ID {boardId} not found or client not initialized.");
            }
        }

        public async Task UpdateTrainDisplayOnBoardAsync(int boardId, TrainDataErail trainData, Dictionary<string, string> platformInfo)
        {
            if (_boardClients.TryGetValue(boardId, out IBoardClient? client) && client != null)
            {
                Console.WriteLine($"DisplayBoardService: Updating train display on BoardID {boardId} for Train {trainData.TrainNo}.");
                await client.UpdateTrainDisplayAsync(trainData, platformInfo);
            }
            else
            {
                Console.WriteLine($"DisplayBoardService: Board with ID {boardId} not found or client not initialized.");
            }
        }

        public async Task UpdateTrainDisplayOnPlatformAsync(string platformNumber, TrainDataErail trainData, Dictionary<string, string> basePlatformInfo)
        {
            if (string.IsNullOrWhiteSpace(platformNumber))
            {
                Console.WriteLine("DisplayBoardService: Platform number cannot be null or empty for UpdateTrainDisplayOnPlatformAsync.");
                return;
            }
            Console.WriteLine($"DisplayBoardService: Updating train displays on platform {platformNumber} for Train {trainData.TrainNo}.");

            var boardsOnPlatform = _boardConfigs.Values.Where(c => c.Platform == platformNumber).ToList();
            if (!boardsOnPlatform.Any())
            {
                Console.WriteLine($"DisplayBoardService: No boards found configured for platform {platformNumber}.");
                return;
            }

            foreach (var boardConfig in boardsOnPlatform)
            {
                // Create platformInfo specific to this board if needed, or pass general info
                var platformInfoForBoard = new Dictionary<string, string>(basePlatformInfo);
                platformInfoForBoard["Platform"] = platformNumber; // Ensure platform number is in the info
                // Add other board-specific details to platformInfoForBoard if necessary

                await UpdateTrainDisplayOnBoardAsync(boardConfig.BoardId, trainData, platformInfoForBoard);
            }
        }

        public async Task ClearBoardAsync(int boardId)
        {
            if (_boardClients.TryGetValue(boardId, out IBoardClient? client) && client != null)
            {
                Console.WriteLine($"DisplayBoardService: Clearing BoardID {boardId}.");
                await client.ClearDisplayAsync(); // Uses the parameterless overload on client
            }
            else
            {
                Console.WriteLine($"DisplayBoardService: Board with ID {boardId} not found for clearing.");
            }
        }

        public async Task ClearPlatformDisplaysAsync(string platformNumber)
        {
             if (string.IsNullOrWhiteSpace(platformNumber))
            {
                Console.WriteLine("DisplayBoardService: Platform number cannot be null or empty for ClearPlatformDisplaysAsync.");
                return;
            }
            Console.WriteLine($"DisplayBoardService: Clearing all displays on platform {platformNumber}.");
            var boardsOnPlatform = _boardConfigs.Values.Where(c => c.Platform == platformNumber).ToList();
            if (!boardsOnPlatform.Any())
            {
                Console.WriteLine($"DisplayBoardService: No boards found configured for platform {platformNumber} to clear.");
                return;
            }
            foreach (var boardConfig in boardsOnPlatform)
            {
                await ClearBoardAsync(boardConfig.BoardId);
            }
        }

        public IBoardClient? GetBoardClient(int boardId)
        {
            _boardClients.TryGetValue(boardId, out IBoardClient? client);
            return client;
        }

        public DisplayBoardConfig? GetBoardConfig(int boardId)
        {
            _boardConfigs.TryGetValue(boardId, out DisplayBoardConfig? config);
            return config;
        }

        public List<IBoardClient> GetAllBoardClients()
        {
            return _boardClients.Values.ToList();
        }

        public List<DisplayBoardConfig> GetAllBoardConfigs()
        {
            return _boardConfigs.Values.ToList();
        }


        public void Dispose()
        {
            Console.WriteLine("DisplayBoardService: Disposing resources.");
            foreach (var commService in _communicationServices)
            {
                try
                {
                    commService.ClosePort(); // Ensure port is closed
                    commService.Dispose();   // Dispose the service itself
                }
                catch(Exception ex)
                {
                    Console.WriteLine($"DisplayBoardService: Error disposing communication service: {ex.Message}");
                }
            }
            _communicationServices.Clear();
            _boardClients.Clear();
            _boardConfigs.Clear();
            GC.SuppressFinalize(this);
        }
    }
}
