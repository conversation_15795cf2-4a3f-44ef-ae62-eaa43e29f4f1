﻿using System;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Windows.Forms;

namespace Announcement
{
	// Token: 0x02000003 RID: 3
	public partial class Advertising : Form
	{
		// Token: 0x06000004 RID: 4 RVA: 0x00002585 File Offset: 0x00000785
		public Advertising()
		{
			this.InitializeComponent();
		}

		// Token: 0x06000005 RID: 5 RVA: 0x000025C1 File Offset: 0x000007C1
		private void folderBrowserDialog1_HelpRequest(object sender, EventArgs e)
		{
		}

		// Token: 0x06000006 RID: 6 RVA: 0x000025C4 File Offset: 0x000007C4
		private void BTN_Exit_Click(object sender, EventArgs e)
		{
			base.Close();
		}

		// Token: 0x06000007 RID: 7 RVA: 0x000025D0 File Offset: 0x000007D0
		private void BTN_New_Click(object sender, EventArgs e)
		{
			this.CB_AName.Text = "";
			this.TB_Hwf.Text = "";
			this.TB_Ewf.Text = "";
			this.BTN_Save.Enabled = true;
			this.BTN_Del.Enabled = false;
			this.BTN_Edit.Enabled = false;
			this.BTN_HW.Enabled = true;
			this.BTN_EW.Enabled = true;
			this.TB_Hwf.Enabled = true;
			this.TB_Ewf.Enabled = true;
			this.GB_ARC.Enabled = true;
			this.CB_AName.Enabled = true;
		}

		// Token: 0x06000008 RID: 8 RVA: 0x00002688 File Offset: 0x00000888
		private void BTN_EW_Click(object sender, EventArgs e)
		{
			try
			{
				this.Open_WaveFile.InitialDirectory = Application.StartupPath + "\\Data\\WAVE\\ENGLISH\\ADVERTISING\\";
				this.Open_WaveFile.FilterIndex = 2;
				this.Open_WaveFile.Filter = "Wave files (*.wav)| *.txt |All files (*.*)|*.*";
				this.Open_WaveFile.RestoreDirectory = false;
				bool flag = this.Open_WaveFile.ShowDialog() == DialogResult.OK;
				if (flag)
				{
					this.TB_Ewf.Text = this.Open_WaveFile.FileName;
					bool flag2 = this.Open_WaveFile.FileName.Length > 0;
					if (flag2)
					{
						string text = Application.StartupPath + "\\Data\\WAVE\\ENGLISH\\ADVERTISING\\";
						bool flag3 = !Directory.Exists(text);
						if (flag3)
						{
							Directory.CreateDirectory(text);
						}
						string text2 = text + this.CB_AName.Text + "wav";
						bool flag4 = !File.Exists(text2);
						if (flag4)
						{
							File.Copy(this.Open_WaveFile.FileName, text2);
						}
					}
				}
			}
			catch (Exception ex)
			{
				MessageBox.Show("Unable To Open Wave File" + ex.Message.ToString());
			}
		}

		// Token: 0x06000009 RID: 9 RVA: 0x000027BC File Offset: 0x000009BC
		private void BTN_Save_Click(object sender, EventArgs e)
		{
			Class_Database class_Database = new Class_Database();
			bool flag = this.TB_APC.Text == "";
			if (flag)
			{
				MessageBox.Show("Enter Play Count");
			}
			else
			{
				class_Database.Insert_Database("DELETE * From Advertising WHERE Adver_Name = '" + this.CB_AName.Text + "'");
				class_Database.Insert_Database(string.Concat(new string[]
				{
					"INSERT INTO Advertising(Msg_Enable,Ann_Type,Adver_Name,Hindi_Wave,Eng_Wave,Adver_Time)VALUES('True','",
					this.CB_MType.Text,
					"','",
					this.CB_AName.Text,
					"','",
					this.TB_Hwf.Text,
					"','",
					this.TB_Ewf.Text,
					"','",
					this.TB_APC.Text,
					"')"
				}));
				this.LB_Messages.Items.Add(this.CB_MType.Text + " - " + this.CB_AName.Text);
				this.LB_Messages.SetItemChecked(this.LB_Messages.Items.Count - 1, true);
				try
				{
					string text = this.LB_Messages.GetItemChecked(this.LB_Messages.SelectedIndex).ToString();
					class_Database.Insert_Database(string.Concat(new string[]
					{
						"UPDATE Advertising SET Ann_Type = '",
						this.CB_MType.Text,
						"', Adver_Name = '",
						this.CB_AName.Text,
						"', Adver_Time = '",
						this.TB_APC.Text,
						"', Msg_Enable = '",
						text,
						"' WHERE Advertising = '",
						this.OldMsg,
						"'"
					}));
					this.LB_Messages.Items.Insert(this.LB_Messages.SelectedIndex, this.CB_MType.Text + " - " + this.CB_AName.Text);
					bool flag2 = text == "True";
					if (flag2)
					{
						this.LB_Messages.SetItemChecked(this.LB_Messages.SelectedIndex, true);
					}
				}
				catch
				{
				}
				for (int i = 0; i < this.LB_Messages.Items.Count; i++)
				{
					bool flag3 = this.LB_Messages.GetItemChecked(i).ToString() == "True";
					if (flag3)
					{
						class_Database.Insert_Database("UPDATE Advertising SET Msg_Enable = 'True' WHERE Advertising = '" + this.LB_Messages.Items[i].ToString().Substring(7) + "'");
					}
					else
					{
						class_Database.Insert_Database("UPDATE Advertising SET Msg_Enable = 'False' WHERE Advertising = '" + this.LB_Messages.Items[i].ToString().Substring(7) + "'");
					}
				}
				MessageBox.Show("Data Saved Sucessfully");
				this.TB_Hwf.Enabled = false;
				this.TB_Ewf.Enabled = false;
				this.BTN_HW.Enabled = true;
				this.BTN_EW.Enabled = false;
				this.BTN_Save.Enabled = false;
				this.BTN_Edit.Enabled = true;
				this.GB_ARC.Enabled = false;
			}
		}

		// Token: 0x0600000A RID: 10 RVA: 0x00002B3C File Offset: 0x00000D3C
		private void Advertising_Load(object sender, EventArgs e)
		{
			this.LB_Messages.Enabled = false;
			this.CB_AName.Enabled = false;
			this.CB_SName.Enabled = false;
			DataTable dataTable = new DataTable();
			dataTable = this.DB.Read_Database("SELECT * From Advertising");
			bool flag = dataTable.Rows.Count > 0;
			if (flag)
			{
				this.LB_Messages.Items.Clear();
				this.CB_AName.Items.Clear();
				this.CB_SName.Items.Clear();
				bool flag2 = false;
				for (int i = 0; i < dataTable.Rows.Count; i++)
				{
					bool flag3 = dataTable.Rows[i]["Msg_Enable"].ToString() == "Font";
					if (flag3)
					{
						flag2 = true;
					}
					else
					{
						bool flag4 = flag2;
						int index;
						if (flag4)
						{
							index = i - 1;
						}
						else
						{
							index = i;
						}
						this.LB_Messages.Items.Add(dataTable.Rows[i]["Ann_Type"].ToString() + " - " + dataTable.Rows[i]["Adver_Name"].ToString());
						bool flag5 = dataTable.Rows[i]["Ann_Type"].ToString() == "Slogans";
						if (flag5)
						{
							this.CB_SName.Items.Add(dataTable.Rows[i]["Adver_Name"].ToString());
						}
						else
						{
							bool flag6 = dataTable.Rows[i]["Ann_Type"].ToString() == "Advertising";
							if (flag6)
							{
								this.CB_AName.Items.Add(dataTable.Rows[i]["Adver_Name"].ToString());
							}
						}
						bool flag7 = dataTable.Rows[i]["Msg_Enable"].ToString() == "True";
						if (flag7)
						{
							this.LB_Messages.SetItemChecked(index, true);
						}
						else
						{
							this.LB_Messages.SetItemChecked(index, false);
						}
					}
				}
				try
				{
					this.CB_SName.Text = this.CB_SName.Items[0].ToString();
				}
				catch
				{
				}
				try
				{
					this.CB_AName.Text = this.CB_AName.Items[0].ToString();
				}
				catch
				{
				}
			}
		}

		// Token: 0x0600000B RID: 11 RVA: 0x00002E0C File Offset: 0x0000100C
		private void BTN_HW_Click(object sender, EventArgs e)
		{
			try
			{
				this.Open_WaveFile.InitialDirectory = Application.StartupPath + "\\Data\\WAVE\\HINDI\\ADVERTISING\\";
				this.Open_WaveFile.FilterIndex = 2;
				this.Open_WaveFile.Filter = "Wave files (*.wav)| *.txt |All files (*.*)|*.*";
				this.Open_WaveFile.RestoreDirectory = false;
				bool flag = this.Open_WaveFile.ShowDialog() == DialogResult.OK;
				if (flag)
				{
					this.TB_Hwf.Text = this.Open_WaveFile.FileName;
					bool flag2 = this.Open_WaveFile.FileName.Length > 0;
					if (flag2)
					{
						string text = Application.StartupPath + "\\Data\\WAVE\\HINDI\\ADVERTISING\\";
						bool flag3 = !Directory.Exists(text);
						if (flag3)
						{
							Directory.CreateDirectory(text);
						}
						string text2 = text + this.CB_AName.Text + "wav";
						bool flag4 = !File.Exists(text2);
						if (flag4)
						{
							File.Copy(this.Open_WaveFile.FileName, text2);
						}
					}
				}
			}
			catch (Exception ex)
			{
				MessageBox.Show("Unable To Open Wave File" + ex.Message.ToString());
			}
		}

		// Token: 0x0600000C RID: 12 RVA: 0x000025C1 File Offset: 0x000007C1
		private void LB_Messages_SelectedIndexChanged(object sender, EventArgs e)
		{
		}

		// Token: 0x0600000D RID: 13 RVA: 0x00002F40 File Offset: 0x00001140
		private void BTN_Edit_Click(object sender, EventArgs e)
		{
			this.Flag_Edit = true;
			this.BTN_New.Enabled = false;
			this.BTN_Del.Enabled = true;
			this.BTN_Save.Enabled = true;
			this.BTN_HW.Enabled = true;
			this.TB_Hwf.Enabled = true;
			this.BTN_EW.Enabled = true;
			this.TB_Ewf.Enabled = true;
			this.GB_ARC.Enabled = true;
		}

		// Token: 0x0600000E RID: 14 RVA: 0x00002FC0 File Offset: 0x000011C0
		private void BTN_Del_Click(object sender, EventArgs e)
		{
			this.StnTable = new DataTable();
			this.DB.Insert_Database("DELETE * From Advertising WHERE Adver_Name = '" + this.CB_AName.Text + "'");
			MessageBox.Show("Data Delete Sucessfully");
			this.StnTable = new DataTable();
			this.StnTable = this.DB.Read_Database("Select * From Advertising WHERE Ann_Type = 'Advertising'");
			this.StnTable = new DataTable();
			this.StnTable = this.DB.Read_Database("Select * From Advertising WHERE Ann_Type = 'Advertising'");
			this.CB_AName.Items.Clear();
			this.LB_Messages.Items.Clear();
			bool flag = this.StnTable.Rows.Count > 0;
			if (flag)
			{
				for (int i = 0; i < this.StnTable.Rows.Count; i++)
				{
					bool flag2 = (string)this.StnTable.Rows[i]["Adver_Name"] != null;
					if (flag2)
					{
						this.CB_AName.Items.Add(this.StnTable.Rows[i]["Adver_Name"].ToString());
						this.LB_Messages.Items.Add(this.StnTable.Rows[i]["Adver_Name"].ToString());
					}
				}
				this.CB_AName.Text = this.StnTable.Rows[0]["Adver_Name"].ToString();
			}
			this.BTN_Save.Enabled = false;
			this.BTN_New.Enabled = true;
			this.BTN_Edit.Enabled = true;
			this.BTN_Del.Enabled = false;
			this.LB_Messages.Enabled = false;
			this.CB_AName.Enabled = false;
			this.CB_SName.Enabled = false;
			DataTable dataTable = new DataTable();
			dataTable = this.DB.Read_Database("SELECT * From Advertising");
			bool flag3 = dataTable.Rows.Count > 0;
			if (flag3)
			{
				this.LB_Messages.Items.Clear();
				this.CB_AName.Items.Clear();
				this.CB_SName.Items.Clear();
				bool flag4 = false;
				for (int j = 0; j < dataTable.Rows.Count; j++)
				{
					bool flag5 = dataTable.Rows[j]["Msg_Enable"].ToString() == "Font";
					if (flag5)
					{
						flag4 = true;
					}
					else
					{
						bool flag6 = flag4;
						int index;
						if (flag6)
						{
							index = j - 1;
						}
						else
						{
							index = j;
						}
						this.LB_Messages.Items.Add(dataTable.Rows[j]["Ann_Type"].ToString() + " - " + dataTable.Rows[j]["Adver_Name"].ToString());
						bool flag7 = dataTable.Rows[j]["Ann_Type"].ToString() == "Slogans";
						if (flag7)
						{
							this.CB_SName.Items.Add(dataTable.Rows[j]["Adver_Name"].ToString());
						}
						else
						{
							bool flag8 = dataTable.Rows[j]["Ann_Type"].ToString() == "Advertising";
							if (flag8)
							{
								this.CB_AName.Items.Add(dataTable.Rows[j]["Adver_Name"].ToString());
							}
						}
						bool flag9 = dataTable.Rows[j]["Msg_Enable"].ToString() == "True";
						if (flag9)
						{
							this.LB_Messages.SetItemChecked(index, true);
						}
						else
						{
							this.LB_Messages.SetItemChecked(index, false);
						}
					}
				}
				try
				{
					this.CB_SName.Text = this.CB_SName.Items[0].ToString();
				}
				catch
				{
				}
				try
				{
					this.CB_AName.Text = this.CB_AName.Items[0].ToString();
				}
				catch
				{
				}
			}
		}

		// Token: 0x0600000F RID: 15 RVA: 0x0000346C File Offset: 0x0000166C
		private void CB_AName_SelectedIndexChanged(object sender, EventArgs e)
		{
			DataTable dataTable = new DataTable();
			dataTable = this.DB.Read_Database("Select * From Advertising WHERE Adver_Name = '" + this.CB_AName.Text + "'");
			this.TB_Hwf.Text = dataTable.Rows[0]["Hindi_Wave"].ToString();
			this.TB_Ewf.Text = dataTable.Rows[0]["Eng_Wave"].ToString();
			this.TB_APC.Text = dataTable.Rows[0]["Adver_Time"].ToString();
			this.TB_PT.Text = dataTable.Rows[0]["Adver_Count"].ToString();
		}

		// Token: 0x06000010 RID: 16 RVA: 0x000025C1 File Offset: 0x000007C1
		private void Lbl_RecTime_Click(object sender, EventArgs e)
		{
		}

		// Token: 0x06000011 RID: 17 RVA: 0x00003544 File Offset: 0x00001744
		private void BTN_SAdd_Click(object sender, EventArgs e)
		{
			this.CB_SName.Text = "";
			this.TB_SHwf.Text = "";
			this.TB_SEwf.Text = "";
			this.CB_SName.Enabled = true;
			this.BTN_SSave.Enabled = true;
			this.BTN_SDel.Enabled = false;
			this.BTN_SEdit.Enabled = false;
			this.BTN_SHW.Enabled = true;
			this.BTN_SEW.Enabled = true;
			this.TB_SHwf.Enabled = true;
			this.TB_SEwf.Enabled = true;
		}

		// Token: 0x06000012 RID: 18 RVA: 0x000035F0 File Offset: 0x000017F0
		private void BTN_SEdit_Click(object sender, EventArgs e)
		{
			this.Flag_Edit = true;
			this.BTN_SAdd.Enabled = false;
			this.BTN_SDel.Enabled = true;
			this.BTN_SSave.Enabled = true;
			this.BTN_SHW.Enabled = true;
			this.BTN_SEW.Enabled = true;
			this.TB_SHwf.Enabled = true;
			this.TB_SEwf.Enabled = true;
			this.CB_SName.Enabled = true;
		}

		// Token: 0x06000013 RID: 19 RVA: 0x00003670 File Offset: 0x00001870
		private void BTN_SDel_Click(object sender, EventArgs e)
		{
			this.StnTable = new DataTable();
			this.DB.Insert_Database("DELETE * From Advertising WHERE Adver_Name = '" + this.CB_SName.Text + "'");
			MessageBox.Show("Data Delete Sucessfully");
			this.StnTable = new DataTable();
			this.StnTable = this.DB.Read_Database("Select * From Advertising WHERE Ann_Type = 'Slogans'");
			this.StnTable = new DataTable();
			this.StnTable = this.DB.Read_Database("Select * From Advertising WHERE Ann_Type = 'Slogans'");
			this.CB_SName.Items.Clear();
			this.LB_Messages.Items.Clear();
			bool flag = this.StnTable.Rows.Count > 0;
			if (flag)
			{
				for (int i = 0; i < this.StnTable.Rows.Count; i++)
				{
					bool flag2 = (string)this.StnTable.Rows[i]["Adver_Name"] != null;
					if (flag2)
					{
						this.CB_SName.Items.Add(this.StnTable.Rows[i]["Adver_Name"].ToString());
						this.LB_Messages.Items.Add(this.StnTable.Rows[i]["Adver_Name"].ToString());
					}
				}
				this.CB_SName.Text = this.StnTable.Rows[0]["Adver_Name"].ToString();
			}
			this.BTN_Save.Enabled = false;
			this.BTN_SDel.Enabled = false;
			this.BTN_SAdd.Enabled = true;
			this.BTN_SEdit.Enabled = true;
			this.LB_Messages.Enabled = false;
			this.CB_AName.Enabled = false;
			this.CB_SName.Enabled = false;
			DataTable dataTable = new DataTable();
			dataTable = this.DB.Read_Database("SELECT * From Advertising");
			bool flag3 = dataTable.Rows.Count > 0;
			if (flag3)
			{
				this.LB_Messages.Items.Clear();
				this.CB_AName.Items.Clear();
				this.CB_SName.Items.Clear();
				bool flag4 = false;
				for (int j = 0; j < dataTable.Rows.Count; j++)
				{
					bool flag5 = dataTable.Rows[j]["Msg_Enable"].ToString() == "Font";
					if (flag5)
					{
						flag4 = true;
					}
					else
					{
						bool flag6 = flag4;
						int index;
						if (flag6)
						{
							index = j - 1;
						}
						else
						{
							index = j;
						}
						this.LB_Messages.Items.Add(dataTable.Rows[j]["Ann_Type"].ToString() + " - " + dataTable.Rows[j]["Adver_Name"].ToString());
						bool flag7 = dataTable.Rows[j]["Ann_Type"].ToString() == "Slogans";
						if (flag7)
						{
							this.CB_SName.Items.Add(dataTable.Rows[j]["Adver_Name"].ToString());
						}
						else
						{
							bool flag8 = dataTable.Rows[j]["Ann_Type"].ToString() == "Advertising";
							if (flag8)
							{
								this.CB_AName.Items.Add(dataTable.Rows[j]["Adver_Name"].ToString());
							}
						}
						bool flag9 = dataTable.Rows[j]["Msg_Enable"].ToString() == "True";
						if (flag9)
						{
							this.LB_Messages.SetItemChecked(index, true);
						}
						else
						{
							this.LB_Messages.SetItemChecked(index, false);
						}
					}
				}
				try
				{
					this.CB_SName.Text = this.CB_SName.Items[0].ToString();
				}
				catch
				{
				}
				try
				{
					this.CB_AName.Text = this.CB_AName.Items[0].ToString();
				}
				catch
				{
				}
			}
		}

		// Token: 0x06000014 RID: 20 RVA: 0x00003B1C File Offset: 0x00001D1C
		private void BTN_SSave_Click(object sender, EventArgs e)
		{
			Class_Database class_Database = new Class_Database();
			class_Database.Insert_Database("DELETE * From Advertising WHERE Adver_Name = '" + this.CB_SName.Text + "'");
			class_Database.Insert_Database(string.Concat(new string[]
			{
				"INSERT INTO Advertising(Msg_Enable,Ann_Type,Adver_Name,Hindi_Wave,Eng_Wave)VALUES('True','",
				this.CB_MType.Text,
				"','",
				this.CB_SName.Text,
				"','",
				this.TB_SHwf.Text,
				"','",
				this.TB_SEwf.Text,
				"')"
			}));
			this.LB_Messages.Items.Add(this.CB_MType.Text + " - " + this.CB_SName.Text);
			this.LB_Messages.SetItemChecked(this.LB_Messages.Items.Count - 1, true);
			try
			{
				string text = this.LB_Messages.GetItemChecked(this.LB_Messages.SelectedIndex).ToString();
				class_Database.Insert_Database(string.Concat(new string[]
				{
					"UPDATE Advertising SET Ann_Type = '",
					this.CB_MType.Text,
					"', Adver_Name = '",
					this.CB_SName.Text,
					"', Msg_Enable = '",
					text,
					"' WHERE Advertising = '",
					this.OldMsg,
					"'"
				}));
				this.LB_Messages.Items.Insert(this.LB_Messages.SelectedIndex, this.CB_MType.Text + " - " + this.CB_SName.Text);
				bool flag = text == "True";
				if (flag)
				{
					this.LB_Messages.SetItemChecked(this.LB_Messages.SelectedIndex, true);
				}
			}
			catch
			{
			}
			for (int i = 0; i < this.LB_Messages.Items.Count; i++)
			{
				bool flag2 = this.LB_Messages.GetItemChecked(i).ToString() == "True";
				if (flag2)
				{
					class_Database.Insert_Database("UPDATE Advertising SET Msg_Enable = 'True' WHERE Advertising = '" + this.LB_Messages.Items[i].ToString().Substring(7) + "'");
				}
				else
				{
					class_Database.Insert_Database("UPDATE Advertising SET Msg_Enable = 'False' WHERE Advertising = '" + this.LB_Messages.Items[i].ToString().Substring(7) + "'");
				}
			}
			MessageBox.Show("Data Saved Sucessfully");
			this.TB_SHwf.Enabled = false;
			this.TB_SEwf.Enabled = false;
			this.BTN_SHW.Enabled = true;
			this.BTN_SEW.Enabled = false;
			this.BTN_SSave.Enabled = false;
			this.BTN_SEdit.Enabled = true;
		}

		// Token: 0x06000015 RID: 21 RVA: 0x00003E28 File Offset: 0x00002028
		private void BTN_SHW_Click(object sender, EventArgs e)
		{
			try
			{
				this.Open_WaveFile.InitialDirectory = Application.StartupPath + "\\Data\\WAVE\\HINDI\\SLOGAN\\";
				this.Open_WaveFile.FilterIndex = 2;
				this.Open_WaveFile.Filter = "Wave files (*.wav)| *.txt |All files (*.*)|*.*";
				this.Open_WaveFile.RestoreDirectory = false;
				bool flag = this.Open_WaveFile.ShowDialog() == DialogResult.OK;
				if (flag)
				{
					this.TB_SHwf.Text = this.Open_WaveFile.FileName;
					bool flag2 = this.Open_WaveFile.FileName.Length > 0;
					if (flag2)
					{
						string text = Application.StartupPath + "\\Data\\WAVE\\HINDI\\SLOGAN\\";
						bool flag3 = !Directory.Exists(text);
						if (flag3)
						{
							Directory.CreateDirectory(text);
						}
						string text2 = text + this.CB_AName.Text + "wav";
						bool flag4 = !File.Exists(text2);
						if (flag4)
						{
							File.Copy(this.Open_WaveFile.FileName, text2);
						}
					}
				}
			}
			catch (Exception ex)
			{
				MessageBox.Show("Unable To Open Wave File" + ex.Message.ToString());
			}
		}

		// Token: 0x06000016 RID: 22 RVA: 0x00003F5C File Offset: 0x0000215C
		private void BTN_SEW_Click(object sender, EventArgs e)
		{
			try
			{
				this.Open_WaveFile.InitialDirectory = Application.StartupPath + "\\Data\\WAVE\\ENGLISH\\SLOGAN\\";
				this.Open_WaveFile.FilterIndex = 2;
				this.Open_WaveFile.Filter = "Wave files (*.wav)| *.txt |All files (*.*)|*.*";
				this.Open_WaveFile.RestoreDirectory = false;
				bool flag = this.Open_WaveFile.ShowDialog() == DialogResult.OK;
				if (flag)
				{
					this.TB_SEwf.Text = this.Open_WaveFile.FileName;
					bool flag2 = this.Open_WaveFile.FileName.Length > 0;
					if (flag2)
					{
						string text = Application.StartupPath + "\\Data\\WAVE\\ENGLISH\\SLOGAN\\";
						bool flag3 = !Directory.Exists(text);
						if (flag3)
						{
							Directory.CreateDirectory(text);
						}
						string text2 = text + this.CB_AName.Text + "wav";
						bool flag4 = !File.Exists(text2);
						if (flag4)
						{
							File.Copy(this.Open_WaveFile.FileName, text2);
						}
					}
				}
			}
			catch (Exception ex)
			{
				MessageBox.Show("Unable To Open Wave File" + ex.Message.ToString());
			}
		}

		// Token: 0x06000017 RID: 23 RVA: 0x000025C1 File Offset: 0x000007C1
		private void LB_MessagesS_SelectedIndexChanged(object sender, EventArgs e)
		{
		}

		// Token: 0x06000018 RID: 24 RVA: 0x00004090 File Offset: 0x00002290
		private void CB_SName_SelectedIndexChanged(object sender, EventArgs e)
		{
			DataTable dataTable = new DataTable();
			dataTable = this.DB.Read_Database("Select * From Advertising WHERE Ann_Type = 'Slogans'");
			this.TB_SHwf.Text = dataTable.Rows[0]["Hindi_Wave"].ToString();
			this.TB_SEwf.Text = dataTable.Rows[0]["Eng_Wave"].ToString();
		}

		// Token: 0x06000019 RID: 25 RVA: 0x000025C1 File Offset: 0x000007C1
		private void Btn_Record_Click(object sender, EventArgs e)
		{
		}

		// Token: 0x0600001A RID: 26 RVA: 0x00004104 File Offset: 0x00002304
		private void CB_MType_SelectedIndexChanged(object sender, EventArgs e)
		{
			bool flag = this.CB_MType.Text == "Advertising";
			if (flag)
			{
				this.groupBox1.Enabled = true;
				this.CB_AName.Enabled = true;
				this.groupBox2.Enabled = false;
				this.LB_Messages.Enabled = false;
				this.BTN_New.Enabled = true;
				this.BTN_Edit.Enabled = true;
			}
			else
			{
				bool flag2 = this.CB_MType.Text == "Slogans";
				if (flag2)
				{
					this.groupBox1.Enabled = false;
					this.groupBox2.Enabled = true;
					this.CB_SName.Enabled = true;
					this.LB_Messages.Enabled = false;
					this.BTN_SAdd.Enabled = true;
					this.BTN_SEdit.Enabled = true;
				}
			}
		}

		// Token: 0x0600001B RID: 27 RVA: 0x000041E6 File Offset: 0x000023E6
		private void BTN_LBEdit_Click(object sender, EventArgs e)
		{
			this.LB_Messages.Enabled = true;
		}

		// Token: 0x17000001 RID: 1
		// (get) Token: 0x0600001C RID: 28 RVA: 0x000041F6 File Offset: 0x000023F6
		// (set) Token: 0x0600001D RID: 29 RVA: 0x000041FE File Offset: 0x000023FE
		public int MsgCnt { get; set; }

		// Token: 0x17000002 RID: 2
		// (get) Token: 0x0600001E RID: 30 RVA: 0x00004207 File Offset: 0x00002407
		// (set) Token: 0x0600001F RID: 31 RVA: 0x0000420F File Offset: 0x0000240F
		public object NewMsgLoc { get; set; }

		// Token: 0x06000020 RID: 32 RVA: 0x000025C1 File Offset: 0x000007C1
		private void TB_PT_TextChanged(object sender, EventArgs e)
		{
		}

		// Token: 0x06000021 RID: 33 RVA: 0x000025C1 File Offset: 0x000007C1
		private void LB_Messages_SelectedIndexChanged_1(object sender, EventArgs e)
		{
		}

		// Token: 0x06000022 RID: 34 RVA: 0x000025C1 File Offset: 0x000007C1
		private void groupBox1_Enter(object sender, EventArgs e)
		{
		}

		// Token: 0x06000023 RID: 35 RVA: 0x000025C1 File Offset: 0x000007C1
		private void BTN_LBSave_Click(object sender, EventArgs e)
		{
		}

		// Token: 0x06000024 RID: 36 RVA: 0x000025C1 File Offset: 0x000007C1
		private void textBox1_TextChanged(object sender, EventArgs e)
		{
		}

		// Token: 0x04000008 RID: 8
		private Class_Database DB = new Class_Database();

		// Token: 0x04000009 RID: 9
		private DataTable TrainTable;

		// Token: 0x0400000A RID: 10
		private DataTable StnTable;

		// Token: 0x0400000B RID: 11
		private bool Flag_Edit = false;

		// Token: 0x0400000C RID: 12
		private bool Flag_New = false;

		// Token: 0x0400000D RID: 13
		private int GP_Counter;

		// Token: 0x0400000E RID: 14
		private DataTable MassTable;

		// Token: 0x0400000F RID: 15
		public static bool Flag_Message;

		// Token: 0x04000010 RID: 16
		public static bool Flag_AddMsg;

		// Token: 0x04000011 RID: 17
		public string OldMsg = "";

		// Token: 0x04000012 RID: 18
		public static string Msg_Speed;

		// Token: 0x04000013 RID: 19
		public static string Msg_Effect;
	}
}
