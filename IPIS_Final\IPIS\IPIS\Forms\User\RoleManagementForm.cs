using System;
using System.Collections.Generic;
using System.Windows.Forms;
using IPIS.Utils;
using IPIS.Services;
using IPIS.Repositories;

namespace IPIS.Forms.User
{
    public partial class RoleManagementForm : Form
    {
        private readonly RoleService roleService;

        public RoleManagementForm()
        {
            InitializeComponent();
            roleService = new RoleService(new SQLiteRoleRepository());
            roleList.DataBindingComplete += roleList_DataBindingComplete;
            LoadRoles();
            LoadPermissions();
        }

        private void InitializeComponent()
        {
            this.splitContainer = new SplitContainer();
            this.roleList = new DataGridView();
            this.roleGroup = new GroupBox();
            this.roleNameLabel = new Label();
            this.roleNameTextBox = new TextBox();
            this.descriptionLabel = new Label();
            this.descriptionTextBox = new TextBox();
            this.permissionsLabel = new Label();
            this.permissionsCheckedListBox = new CheckedListBox();
            this.addRoleButton = new Button();
            this.editRoleButton = new Button();
            this.refreshButton = new Button();
            this.clearButton = new Button();
            this.statusStrip = new StatusStrip();
            this.statusLabel = new ToolStripStatusLabel();

            // RoleManagementForm
            this.ClientSize = new System.Drawing.Size(1024, 768);
            this.Name = "RoleManagementForm";
            this.Text = "Role Management";
            this.WindowState = FormWindowState.Maximized;

            // SplitContainer
            this.splitContainer.Dock = DockStyle.Fill;
            this.splitContainer.Name = "splitContainer";
            this.splitContainer.Orientation = Orientation.Vertical;
            this.splitContainer.SplitterDistance = 250;

            // Role List (DataGridView)
            this.roleList.Dock = DockStyle.Fill;
            this.roleList.Name = "roleList";
            this.roleList.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            this.roleList.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            this.roleList.MultiSelect = false;
            this.roleList.AllowUserToAddRows = false;
            this.roleList.AllowUserToDeleteRows = false;
            this.roleList.ReadOnly = true;
            this.roleList.Columns.Add("Name", "Role Name");
            this.roleList.Columns.Add("Description", "Description");
            this.roleList.Columns.Add("Permissions", "Permissions");
            this.roleList.Columns.Add("Status", "Status");
            this.roleList.Columns.Add("Edit", "Edit");
            this.roleList.Columns.Add("Delete", "Delete");
            this.roleList.CellClick += new DataGridViewCellEventHandler(roleList_CellClick);
            this.roleList.CellPainting += roleList_CellPainting;

            // Role Group
            this.roleGroup.Dock = DockStyle.Fill;
            this.roleGroup.Name = "roleGroup";
            this.roleGroup.Text = "Add/Edit Role";

            // Role Name Label
            this.roleNameLabel.AutoSize = true;
            this.roleNameLabel.Location = new System.Drawing.Point(20, 30);
            this.roleNameLabel.Name = "roleNameLabel";
            this.roleNameLabel.Size = new System.Drawing.Size(60, 15);
            this.roleNameLabel.Text = "Role Name";

            // Role Name TextBox
            this.roleNameTextBox.Location = new System.Drawing.Point(120, 27);
            this.roleNameTextBox.Name = "roleNameTextBox";
            this.roleNameTextBox.Size = new System.Drawing.Size(200, 23);

            // Description Label
            this.descriptionLabel.AutoSize = true;
            this.descriptionLabel.Location = new System.Drawing.Point(20, 70);
            this.descriptionLabel.Name = "descriptionLabel";
            this.descriptionLabel.Size = new System.Drawing.Size(70, 15);
            this.descriptionLabel.Text = "Description";

            // Description TextBox
            this.descriptionTextBox.Location = new System.Drawing.Point(120, 67);
            this.descriptionTextBox.Name = "descriptionTextBox";
            this.descriptionTextBox.Size = new System.Drawing.Size(200, 23);

            // Permissions Label
            this.permissionsLabel.AutoSize = true;
            this.permissionsLabel.Location = new System.Drawing.Point(20, 110);
            this.permissionsLabel.Name = "permissionsLabel";
            this.permissionsLabel.Size = new System.Drawing.Size(70, 15);
            this.permissionsLabel.Text = "Permissions";

            // Permissions CheckedListBox
            this.permissionsCheckedListBox.Location = new System.Drawing.Point(120, 107);
            this.permissionsCheckedListBox.Name = "permissionsCheckedListBox";
            this.permissionsCheckedListBox.Size = new System.Drawing.Size(200, 120);

            // Add Role Button
            this.addRoleButton.Location = new System.Drawing.Point(120, 240);
            this.addRoleButton.Name = "addRoleButton";
            this.addRoleButton.Size = new System.Drawing.Size(200, 30);
            this.addRoleButton.Text = "Add Role";
            ButtonStyler.ApplyStandardStyle(this.addRoleButton, "primary");
            this.addRoleButton.Click += new EventHandler(this.addRoleButton_Click);

            // Edit Role Button
            this.editRoleButton.Location = new System.Drawing.Point(120, 280);
            this.editRoleButton.Name = "editRoleButton";
            this.editRoleButton.Size = new System.Drawing.Size(200, 30);
            this.editRoleButton.Text = "Edit Role";
            ButtonStyler.ApplyStandardStyle(this.editRoleButton, "secondary");
            this.editRoleButton.Click += new EventHandler(this.editRoleButton_Click);

            // Refresh Button
            this.refreshButton.Location = new System.Drawing.Point(120, 320);
            this.refreshButton.Name = "refreshButton";
            this.refreshButton.Size = new System.Drawing.Size(200, 30);
            this.refreshButton.Text = "Refresh";
            ButtonStyler.ApplyStandardStyle(this.refreshButton, "info");
            this.refreshButton.Click += new EventHandler(this.refreshButton_Click);

            // Clear Button
            this.clearButton.Location = new System.Drawing.Point(120, 360);
            this.clearButton.Name = "clearButton";
            this.clearButton.Size = new System.Drawing.Size(200, 30);
            this.clearButton.Text = "Clear";
            ButtonStyler.ApplyStandardStyle(this.clearButton, "info");
            this.clearButton.Click += new EventHandler(this.clearButton_Click);

            // Add controls to role group
            this.roleGroup.Controls.AddRange(new Control[] {
                this.roleNameLabel,
                this.roleNameTextBox,
                this.descriptionLabel,
                this.descriptionTextBox,
                this.permissionsLabel,
                this.permissionsCheckedListBox,
                this.addRoleButton,
                this.editRoleButton,
                this.refreshButton,
                this.clearButton
            });

            // StatusStrip
            this.statusStrip.Items.AddRange(new ToolStripItem[] {
                this.statusLabel
            });
            this.statusStrip.Location = new System.Drawing.Point(0, 746);
            this.statusStrip.Name = "statusStrip";
            this.statusStrip.Size = new System.Drawing.Size(1024, 22);
            this.statusStrip.TabIndex = 2;

            // Status Label
            this.statusLabel.Name = "statusLabel";
            this.statusLabel.Text = "Ready";

            // Add controls to form
            this.splitContainer.Panel1.Controls.Add(this.roleList);
            this.splitContainer.Panel2.Controls.Add(this.roleGroup);
            this.Controls.AddRange(new Control[] {
                this.splitContainer,
                this.statusStrip
            });
        }

        private SplitContainer splitContainer;
        private DataGridView roleList;
        private GroupBox roleGroup;
        private Label roleNameLabel;
        private TextBox roleNameTextBox;
        private Label descriptionLabel;
        private TextBox descriptionTextBox;
        private Label permissionsLabel;
        private CheckedListBox permissionsCheckedListBox;
        private Button addRoleButton;
        private Button editRoleButton;
        private Button refreshButton;
        private Button clearButton;
        private StatusStrip statusStrip;
        private ToolStripStatusLabel statusLabel;

        private void roleList_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex < 0) return;

            if (e.ColumnIndex == roleList.Columns["Edit"].Index)
            {
                DataGridViewRow row = roleList.Rows[e.RowIndex];
                string roleName = row.Cells["Name"].Value.ToString();
                
                // Prevent editing Administrator role
                if (roleName.Equals("Administrator", StringComparison.OrdinalIgnoreCase))
                {
                    MessageBox.Show("Administrator role cannot be edited.", "Access Denied", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }
                
                roleNameTextBox.Text = roleName;
                descriptionTextBox.Text = row.Cells["Description"].Value?.ToString() ?? "";
                
                // Clear and set permissions
                permissionsCheckedListBox.ClearSelected();
                string permissions = row.Cells["Permissions"].Value?.ToString() ?? "";
                string[] permissionArray = permissions.Split(',');
                for (int i = 0; i < permissionsCheckedListBox.Items.Count; i++)
                {
                    permissionsCheckedListBox.SetItemChecked(i, permissionArray.Contains(permissionsCheckedListBox.Items[i].ToString()));
                }
            }
            else if (e.ColumnIndex == roleList.Columns["Delete"].Index)
            {
                DataGridViewRow row = roleList.Rows[e.RowIndex];
                string roleName = row.Cells["Name"].Value.ToString();
                
                // Prevent deleting Administrator role
                if (roleName.Equals("Administrator", StringComparison.OrdinalIgnoreCase))
                {
                    MessageBox.Show("Administrator role cannot be deleted.", "Access Denied", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }
                
                long roleId = Convert.ToInt64(row.Cells["Id"].Value);
                var result = MessageBox.Show("Are you sure you want to delete this role?", "Confirm Delete", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);
                if (result == DialogResult.Yes)
                {
                    try
                    {
                        roleService.DeleteRole(roleId);
                        LoadRoles();
                        ClearInputs();
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show("Error deleting role: " + ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private void roleList_CellPainting(object sender, DataGridViewCellPaintingEventArgs e)
        {
            if ((e.ColumnIndex == roleList.Columns["Edit"].Index || e.ColumnIndex == roleList.Columns["Delete"].Index) && e.RowIndex >= 0)
            {
                // Check if this is an Administrator role
                string roleName = roleList.Rows[e.RowIndex].Cells["Name"].Value?.ToString() ?? "";
                if (roleName.Equals("Administrator", StringComparison.OrdinalIgnoreCase))
                {
                    // Hide buttons for Administrator role
                    e.PaintBackground(e.ClipBounds, true);
                    e.Handled = true;
                    return;
                }
                
                e.PaintBackground(e.ClipBounds, true);
                e.PaintContent(e.ClipBounds);

                var buttonRect = e.CellBounds;
                buttonRect.Inflate(-6, -6);
                System.Drawing.Color buttonColor = e.ColumnIndex == roleList.Columns["Edit"].Index ? System.Drawing.Color.FromArgb(0, 123, 255) : System.Drawing.Color.FromArgb(220, 53, 69);
                System.Drawing.Color borderColor = e.ColumnIndex == roleList.Columns["Edit"].Index ? System.Drawing.Color.FromArgb(0, 80, 200) : System.Drawing.Color.FromArgb(200, 35, 51);
                string buttonText = e.ColumnIndex == roleList.Columns["Edit"].Index ? "Edit" : "Delete";
                using (var brush = new System.Drawing.SolidBrush(buttonColor))
                using (var pen = new System.Drawing.Pen(borderColor))
                using (var format = new System.Drawing.StringFormat { Alignment = System.Drawing.StringAlignment.Center, LineAlignment = System.Drawing.StringAlignment.Center })
                {
                    e.Graphics.FillRectangle(brush, buttonRect);
                    e.Graphics.DrawRectangle(pen, buttonRect);
                    e.Graphics.DrawString(buttonText, e.CellStyle.Font, System.Drawing.Brushes.White, buttonRect, format);
                }
                e.Handled = true;
            }
        }

        private void roleList_DataBindingComplete(object sender, DataGridViewBindingCompleteEventArgs e)
        {
            // Hide Id column if it exists
            if (roleList.Columns.Contains("Id"))
            {
                roleList.Columns["Id"].Visible = false;
            }

            // Add Edit button column if it doesn't exist
            if (!roleList.Columns.Contains("Edit"))
            {
                DataGridViewButtonColumn editColumn = new DataGridViewButtonColumn();
                editColumn.Name = "Edit";
                editColumn.HeaderText = "Edit";
                editColumn.Text = "Edit";
                editColumn.UseColumnTextForButtonValue = true;
                roleList.Columns.Add(editColumn);
            }

            // Add Delete button column if it doesn't exist
            if (!roleList.Columns.Contains("Delete"))
            {
                DataGridViewButtonColumn deleteColumn = new DataGridViewButtonColumn();
                deleteColumn.Name = "Delete";
                deleteColumn.HeaderText = "Delete";
                deleteColumn.Text = "Delete";
                deleteColumn.UseColumnTextForButtonValue = true;
                roleList.Columns.Add(deleteColumn);
            }

            // Set column order for Edit and Delete
            if (roleList.Columns.Contains("Edit"))
            {
                roleList.Columns["Edit"].DisplayIndex = roleList.Columns.Count - 2;
            }
            if (roleList.Columns.Contains("Delete"))
            {
                roleList.Columns["Delete"].DisplayIndex = roleList.Columns.Count - 1;
            }
        }

        private void LoadRoles()
        {
            try
            {
                roleList.Columns.Clear(); // This will remove all columns, including Edit/Delete
                roleList.DataSource = roleService.GetAllRoles();
            }
            catch (Exception ex)
            {
                MessageBox.Show("Error loading roles: " + ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadPermissions()
        {
            try
            {
                permissionsCheckedListBox.Items.Clear();
                var permissions = roleService.GetAvailablePermissions();
                foreach (var permission in permissions)
                {
                    permissionsCheckedListBox.Items.Add(permission);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("Error loading permissions: " + ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void addRoleButton_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(roleNameTextBox.Text) || string.IsNullOrWhiteSpace(descriptionTextBox.Text))
            {
                MessageBox.Show("Please fill in all fields.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            // Prevent creating Administrator role
            if (roleNameTextBox.Text.Equals("Administrator", StringComparison.OrdinalIgnoreCase))
            {
                MessageBox.Show("Administrator role cannot be created through this interface.", "Access Denied", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            List<string> permissions = new List<string>();
            foreach (object item in permissionsCheckedListBox.CheckedItems)
            {
                permissions.Add(item.ToString());
            }

            try
            {
                roleService.AddRole(
                    roleNameTextBox.Text,
                    descriptionTextBox.Text,
                    permissions
                );

                LoadRoles();
                ClearInputs();
                MessageBox.Show("Role added successfully.", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show("Error adding role: " + ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void editRoleButton_Click(object sender, EventArgs e)
        {
            if (roleList.SelectedRows.Count == 0)
            {
                MessageBox.Show("Please select a role to edit.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            DataGridViewRow row = roleList.SelectedRows[0];
            long roleId = Convert.ToInt64(row.Cells["Id"].Value);
            string currentRoleName = row.Cells["Name"].Value.ToString();

            // Prevent editing Administrator role
            if (currentRoleName.Equals("Administrator", StringComparison.OrdinalIgnoreCase))
            {
                MessageBox.Show("Administrator role cannot be edited.", "Access Denied", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (string.IsNullOrWhiteSpace(roleNameTextBox.Text) || string.IsNullOrWhiteSpace(descriptionTextBox.Text))
            {
                MessageBox.Show("Please fill in all fields.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            // Prevent changing name to Administrator
            if (roleNameTextBox.Text.Equals("Administrator", StringComparison.OrdinalIgnoreCase))
            {
                MessageBox.Show("Role name cannot be changed to Administrator.", "Access Denied", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            List<string> permissions = new List<string>();
            foreach (object item in permissionsCheckedListBox.CheckedItems)
            {
                permissions.Add(item.ToString());
            }

            try
            {
                roleService.UpdateRole(
                    roleId,
                    roleNameTextBox.Text,
                    descriptionTextBox.Text,
                    permissions
                );

                LoadRoles();
                ClearInputs();
                MessageBox.Show("Role updated successfully.", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show("Error updating role: " + ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void refreshButton_Click(object sender, EventArgs e)
        {
            LoadRoles();
        }

        private void clearButton_Click(object sender, EventArgs e)
        {
            ClearInputs();
        }

        private void ClearInputs()
        {
            roleNameTextBox.Clear();
            descriptionTextBox.Clear();
            for (int i = 0; i < permissionsCheckedListBox.Items.Count; i++)
            {
                permissionsCheckedListBox.SetItemChecked(i, false);
            }
        }
    }
} 