# Dependencies
node_modules/
/.pnp
.pnp.js
yarn.lock
package-lock.json

# Testing
/coverage
.nyc_output

# Production
/build
/dist
/.next/
/out/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Debug logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE and editor files
.idea/
.vscode/
*.swp
*.swo
.DS_Store
Thumbs.db

# TypeScript
*.tsbuildinfo
next-env.d.ts

# Misc
.cache/
.temp/
.tmp/ 
.vs/