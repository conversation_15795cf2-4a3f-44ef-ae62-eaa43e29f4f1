using System.Data;
using System.Data.SQLite;
using IPIS.Repositories.Interfaces;
using IPIS.Utils;

namespace IPIS.Repositories
{
    public class SQLitePlayPathRepository : IPlayPathRepository
    {
        private readonly string connectionString;

        public SQLitePlayPathRepository()
        {
            connectionString = Database.ConnectionString;
        }

        public string GetPlayPath(string type)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = "SELECT Path FROM Play_Path WHERE Type = @Type";
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Type", type);
                    var result = command.ExecuteScalar();
                    return result?.ToString();
                }
            }
        }

        public void AddPlayPath(string type, string path)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = "INSERT INTO Play_Path (Type, Path) VALUES (@Type, @Path)";
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Type", type);
                    command.Parameters.AddWithValue("@Path", path);
                    command.ExecuteNonQuery();
                }
            }
        }

        public void UpdatePlayPath(string type, string path)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = "UPDATE Play_Path SET Path = @Path WHERE Type = @Type";
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Type", type);
                    command.Parameters.AddWithValue("@Path", path);
                    command.ExecuteNonQuery();
                }
            }
        }

        public void DeletePlayPath(string type)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = "DELETE FROM Play_Path WHERE Type = @Type";
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Type", type);
                    command.ExecuteNonQuery();
                }
            }
        }

        public DataTable GetAllPlayPaths()
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = "SELECT * FROM Play_Path";
                using (var command = new SQLiteCommand(query, connection))
                {
                    using (var adapter = new SQLiteDataAdapter(command))
                    {
                        var dataTable = new DataTable();
                        adapter.Fill(dataTable);
                        return dataTable;
                    }
                }
            }
        }
    }
} 