using System.Collections.Generic;
using System.Data;
using IPIS.Models;

namespace IPIS.Repositories.Interfaces
{
    public interface IUserRepository
    {
        void AddUser(string username, string password, string role);
        void UpdateUser(long userId, string username, string password, string role);
        void DeleteUser(long userId);
        DataTable GetAllUsers();
        bool ValidateUser(string username, string password);
        List<string> GetUserPermissions(long userId);
        
        // New authentication methods
        User AuthenticateUser(string username, string password);
        User GetUserByUsername(string username);
        User GetUserById(long userId);
        void UpdateLastLogin(long userId);
        bool UserExists(string username);
        
        // Legacy user support
        bool IsLegacyUser(string username);
        User GetLegacyUser(string username);
    }
} 