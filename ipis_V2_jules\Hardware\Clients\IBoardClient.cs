using System.Threading.Tasks;
using ipis_V2_jules.DisplayFormatters; // For FormattedDisplayData

namespace ipis_V2_jules.Hardware.Clients
{
    // Define BoardStatus as per the prompt
    public class BoardStatus
    {
        public bool IsLinkOk { get; set; }
        public string? StatusMessage { get; set; }
        // Add other relevant fields like FirmwareVersion, ErrorCodes, etc.
        public string? FirmwareVersion { get; set; }
        public List<string>? ErrorCodes { get; set; }

        public BoardStatus()
        {
            ErrorCodes = new List<string>();
        }
    }

    public interface IBoardClient
    {
        Task<bool> SendMessageAsync(FormattedDisplayData data, byte boardAddress, byte subAddress, byte serialNo);
        Task<bool> ClearDisplayAsync(byte boardAddress, byte subAddress, byte serialNo);
        Task<BoardStatus> CheckLinkAsync(byte boardAddress, byte subAddress, byte serialNo);
        // Add other common board commands as needed (e.g., SetConfig, Reset)
        Task<bool> SetConfigurationAsync(byte boardAddress, byte subAddress, byte serialNo, byte[] configData);
        Task<byte[]> GetConfigurationAsync(byte boardAddress, byte subAddress, byte serialNo);
        Task<bool> ResetBoardAsync(byte boardAddress, byte subAddress, byte serialNo);
    }
}
