// Decompiled with JetBrains decompiler
// Type: ipis.frmChangeAnotherUserPwd
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

namespace ipis
{

[DesignerGenerated]
public class frmChangeAnotherUserPwd : Form
{
  private static List<WeakReference> __ENCList = new List<WeakReference>();
  private IContainer components;
  [AccessedThroughProperty("btnCancel")]
  private Button _btnCancel;
  [AccessedThroughProperty("btnSave")]
  private Button _btnSave;
  [AccessedThroughProperty("txtConfirmPwd")]
  private TextBox _txtConfirmPwd;
  [AccessedThroughProperty("txtNewPwd")]
  private TextBox _txtNewPwd;
  [AccessedThroughProperty("lblCpwd")]
  private Label _lblCpwd;
  [AccessedThroughProperty("lblNpwd")]
  private Label _lblNpwd;

  [DebuggerNonUserCode]
  static frmChangeAnotherUserPwd()
  {
  }

  [DebuggerNonUserCode]
  public frmChangeAnotherUserPwd()
  {
    frmChangeAnotherUserPwd.__ENCAddToList((object) this);
    this.InitializeComponent();
  }

  [DebuggerNonUserCode]
  private static void __ENCAddToList(object value)
  {
    lock (frmChangeAnotherUserPwd.__ENCList)
    {
      if (frmChangeAnotherUserPwd.__ENCList.Count == frmChangeAnotherUserPwd.__ENCList.Capacity)
      {
        int index1 = 0;
        int num = checked (frmChangeAnotherUserPwd.__ENCList.Count - 1);
        int index2 = 0;
        while (index2 <= num)
        {
          if (frmChangeAnotherUserPwd.__ENCList[index2].IsAlive)
          {
            if (index2 != index1)
              frmChangeAnotherUserPwd.__ENCList[index1] = frmChangeAnotherUserPwd.__ENCList[index2];
            checked { ++index1; }
          }
          checked { ++index2; }
        }
        frmChangeAnotherUserPwd.__ENCList.RemoveRange(index1, checked (frmChangeAnotherUserPwd.__ENCList.Count - index1));
        frmChangeAnotherUserPwd.__ENCList.Capacity = frmChangeAnotherUserPwd.__ENCList.Count;
      }
      frmChangeAnotherUserPwd.__ENCList.Add(new WeakReference(RuntimeHelpers.GetObjectValue(value)));
    }
  }

  [DebuggerNonUserCode]
  protected override void Dispose(bool disposing)
  {
    try
    {
      if (!disposing || this.components == null)
        return;
      this.components.Dispose();
    }
    finally
    {
      base.Dispose(disposing);
    }
  }

  [DebuggerStepThrough]
  private void InitializeComponent()
  {
    this.btnCancel = new Button();
    this.btnSave = new Button();
    this.txtConfirmPwd = new TextBox();
    this.txtNewPwd = new TextBox();
    this.lblCpwd = new Label();
    this.lblNpwd = new Label();
    this.SuspendLayout();
    this.btnCancel.BackColor = SystemColors.ButtonFace;
    this.btnCancel.DialogResult = DialogResult.Cancel;
    this.btnCancel.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnCancel1 = this.btnCancel;
    Point point1 = new Point(270, 131);
    Point point2 = point1;
    btnCancel1.Location = point2;
    this.btnCancel.Name = "btnCancel";
    Button btnCancel2 = this.btnCancel;
    Size size1 = new Size(60, 25);
    Size size2 = size1;
    btnCancel2.Size = size2;
    this.btnCancel.TabIndex = 4;
    this.btnCancel.Text = "&Exit";
    this.btnCancel.UseVisualStyleBackColor = false;
    this.btnSave.BackColor = SystemColors.ButtonFace;
    this.btnSave.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnSave1 = this.btnSave;
    point1 = new Point(90, 131);
    Point point3 = point1;
    btnSave1.Location = point3;
    this.btnSave.Name = "btnSave";
    Button btnSave2 = this.btnSave;
    size1 = new Size(156, 25);
    Size size3 = size1;
    btnSave2.Size = size3;
    this.btnSave.TabIndex = 3;
    this.btnSave.Text = "Change Password";
    this.btnSave.UseVisualStyleBackColor = false;
    this.txtConfirmPwd.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtConfirmPwd1 = this.txtConfirmPwd;
    point1 = new Point(182, 75);
    Point point4 = point1;
    txtConfirmPwd1.Location = point4;
    this.txtConfirmPwd.MaxLength = 15;
    this.txtConfirmPwd.Name = "txtConfirmPwd";
    this.txtConfirmPwd.PasswordChar = '*';
    TextBox txtConfirmPwd2 = this.txtConfirmPwd;
    size1 = new Size(197, 22);
    Size size4 = size1;
    txtConfirmPwd2.Size = size4;
    this.txtConfirmPwd.TabIndex = 2;
    this.txtNewPwd.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtNewPwd1 = this.txtNewPwd;
    point1 = new Point(182, 26);
    Point point5 = point1;
    txtNewPwd1.Location = point5;
    this.txtNewPwd.MaxLength = 15;
    this.txtNewPwd.Name = "txtNewPwd";
    this.txtNewPwd.PasswordChar = '*';
    TextBox txtNewPwd2 = this.txtNewPwd;
    size1 = new Size(197, 22);
    Size size5 = size1;
    txtNewPwd2.Size = size5;
    this.txtNewPwd.TabIndex = 1;
    this.lblCpwd.AutoSize = true;
    this.lblCpwd.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblCpwd1 = this.lblCpwd;
    point1 = new Point(17, 78);
    Point point6 = point1;
    lblCpwd1.Location = point6;
    this.lblCpwd.Name = "lblCpwd";
    Label lblCpwd2 = this.lblCpwd;
    size1 = new Size(132, 16 /*0x10*/);
    Size size6 = size1;
    lblCpwd2.Size = size6;
    this.lblCpwd.TabIndex = 26;
    this.lblCpwd.Text = "Confirm Password";
    this.lblNpwd.AutoSize = true;
    this.lblNpwd.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblNpwd1 = this.lblNpwd;
    point1 = new Point(34, 29);
    Point point7 = point1;
    lblNpwd1.Location = point7;
    this.lblNpwd.Name = "lblNpwd";
    Label lblNpwd2 = this.lblNpwd;
    size1 = new Size(110, 16 /*0x10*/);
    Size size7 = size1;
    lblNpwd2.Size = size7;
    this.lblNpwd.TabIndex = 25;
    this.lblNpwd.Text = "New Password";
    this.AcceptButton = (IButtonControl) this.btnSave;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.BackColor = Color.PowderBlue;
    this.CancelButton = (IButtonControl) this.btnCancel;
    size1 = new Size(414, 189);
    this.ClientSize = size1;
    this.Controls.Add((Control) this.btnCancel);
    this.Controls.Add((Control) this.btnSave);
    this.Controls.Add((Control) this.txtConfirmPwd);
    this.Controls.Add((Control) this.txtNewPwd);
    this.Controls.Add((Control) this.lblCpwd);
    this.Controls.Add((Control) this.lblNpwd);
    this.FormBorderStyle = FormBorderStyle.FixedToolWindow;
    this.Name = "frmChangeAnotherUserPwd";
    this.Text = "Change Password";
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  internal virtual Button btnCancel
  {
    [DebuggerNonUserCode] get { return this._btnCancel; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnCancel_Click);
      if (this._btnCancel != null)
        this._btnCancel.Click -= eventHandler;
      this._btnCancel = value;
      if (this._btnCancel == null)
        return;
      this._btnCancel.Click += eventHandler;
    }
  }

  internal virtual Button btnSave
  {
    [DebuggerNonUserCode] get { return this._btnSave; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnSave_Click);
      if (this._btnSave != null)
        this._btnSave.Click -= eventHandler;
      this._btnSave = value;
      if (this._btnSave == null)
        return;
      this._btnSave.Click += eventHandler;
    }
  }

  internal virtual TextBox txtConfirmPwd
  {
    [DebuggerNonUserCode] get { return this._txtConfirmPwd; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtConfirmPwd = value;
    }
  }

  internal virtual TextBox txtNewPwd
  {
    [DebuggerNonUserCode] get { return this._txtNewPwd; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtNewPwd = value;
    }
  }

  internal virtual Label lblCpwd
  {
    [DebuggerNonUserCode] get { return this._lblCpwd; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._lblCpwd = value; }
  }

  internal virtual Label lblNpwd
  {
    [DebuggerNonUserCode] get { return this._lblNpwd; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._lblNpwd = value; }
  }

  private void btnSave_Click(object sender, EventArgs e)
  {
    string empty1 = string.Empty;
    string empty2 = string.Empty;
    bool result = false;
    try
    {
      int index = 0;
      while (index < (int) frmMainFormIPIS.user_cnt.cnt)
      {
        if (Operators.CompareString(frmChangeUserDetails.user_name, frmMainFormIPIS.user_details[index].user_name, false) == 0)
        {
          if (Operators.CompareString(this.txtNewPwd.Text, this.txtConfirmPwd.Text, false) == 0)
          {
            network_db_read.enc_pwd(this.txtNewPwd.Text, ref empty2);
            network_db_read.set_user_pwd(empty2, Conversions.ToString(this.txtNewPwd.Text.Length), frmMainFormIPIS.user_details[index].user_name, ref result);
            if (result)
            {
              int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Password changed successfully", "Msg Box", 0, 0, 0);
              frmMainFormIPIS.user_details[index].pwd = empty2;
              frmMainFormIPIS.user_details[index].pwd_length = checked ((byte) this.txtNewPwd.Text.Length);
              this.txtNewPwd.Text = string.Empty;
              this.txtConfirmPwd.Text = string.Empty;
              this.Close();
              return;
            }
          }
          else
          {
            int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "confirm Password do not match with New password , Please retype the password", "Msg Box", 0, 0, 0);
            this.txtNewPwd.Text = string.Empty;
            this.txtConfirmPwd.Text = string.Empty;
            return;
          }
        }
        checked { ++index; }
      }
      this.txtNewPwd.Text = string.Empty;
      this.txtConfirmPwd.Text = string.Empty;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void btnCancel_Click(object sender, EventArgs e)
{
  this.Close();
}
}

}