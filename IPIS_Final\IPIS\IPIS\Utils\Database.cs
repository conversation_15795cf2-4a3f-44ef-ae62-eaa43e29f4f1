using System.Data.SQLite;
using System;
using System.IO;

namespace IPIS.Utils
{
    public static class Database
    {
        private static string GetDatabasePath()
        {
            // Get the project root directory (3 levels up from bin/Debug/net8.0-windows)
            string baseDirectory = AppDomain.CurrentDomain.BaseDirectory;
            string projectRoot = Path.GetFullPath(Path.Combine(baseDirectory, @"..\..\..\"));
            string dataDirectory = Path.Combine(projectRoot, "data");

            // Create Data directory if it doesn't exist
            if (!Directory.Exists(dataDirectory))
            {
                Directory.CreateDirectory(dataDirectory);
            }

            return Path.Combine(dataDirectory, "ipis.db");
        }

        private static string GetLogsPath()
        {
            // Get the project root directory (3 levels up from bin/Debug/net8.0-windows)
            string baseDirectory = AppDomain.CurrentDomain.BaseDirectory;
            string projectRoot = Path.GetFullPath(Path.Combine(baseDirectory, @"..\..\..\"));
            string dataDirectory = Path.Combine(projectRoot, "data");

            // Create Data directory if it doesn't exist
            if (!Directory.Exists(dataDirectory))
            {
                Directory.CreateDirectory(dataDirectory);
            }

            return Path.Combine(dataDirectory, "ipis.db");
        }

        public static string ConnectionString { get; } = $"Data Source={GetDatabasePath()};Version=3;Journal Mode=WAL;Synchronous=NORMAL;Cache=Shared;Foreign Keys=True;";

        public static string LogsConnectionString { get; } = $"Data Source={GetLogsPath()};Version=3;Journal Mode=WAL;Synchronous=NORMAL;Cache=Shared;Foreign Keys=True;";

        public static SQLiteConnection GetConnection()
        {
            var conn = new SQLiteConnection(ConnectionString);
            conn.Open();
            return conn;
        }

        public static SQLiteConnection GetLogsConnection()
        {
            var conn = new SQLiteConnection(LogsConnectionString);
            conn.Open();
            return conn;
        }

        public static void CloseConnection(SQLiteConnection connection)
        {
            if (connection != null && connection.State == System.Data.ConnectionState.Open)
            {
                connection.Close();
                connection.Dispose();
            }
        }

        public static void EnsureDatabaseInitialized()
        {
            using var dbLogger = BatchLoggerExtensions.CreateDatabaseInitLogger();

            try
            {
                dbLogger.LogStep("Initializing main database");
                InitializeDatabase();
                dbLogger.LogSuccess("Main database initialized successfully");

                dbLogger.LogStep("Initializing logs database");
                InitializeLogsDatabase();
                dbLogger.LogSuccess("Logs database initialized successfully");
            }
            catch (Exception ex)
            {
                dbLogger.LogFailure("Database initialization", ex.Message);
                Console.WriteLine($"Warning: Database initialization failed: {ex.Message}");
                // Don't throw - allow application to continue
            }
        }

        public static void InitializeDatabase()
        {
            using (var connection = GetConnection())
            {
                string createTableQuery = @"
                    CREATE TABLE IF NOT EXISTS Train_Data (
                        Train_No VARCHAR(50) PRIMARY KEY,
                        Train_NameEng VARCHAR(100),
                        Train_Type VARCHAR(50),
                        Train_AD VARCHAR(10),
                        Sch_AT VARCHAR(5),
                        Sch_DT VARCHAR(5),
                        Sch_PF VARCHAR(10),
                        Src_Stn VARCHAR(100),
                        Desti_Stn VARCHAR(100),
                        Via1 VARCHAR(100),
                        Via2 VARCHAR(100),
                        Via3 VARCHAR(100),
                        Via4 VARCHAR(100),
                        All_Days BOOLEAN,
                        Chk_Mon BOOLEAN,
                        Chk_Tue BOOLEAN,
                        Chk_Wed BOOLEAN,
                        Chk_Thu BOOLEAN,
                        Chk_Fri BOOLEAN,
                        Chk_Sat BOOLEAN,
                        Chk_Sun BOOLEAN
                    );

                    CREATE TABLE IF NOT EXISTS Online_Trains (
                        Sl_No INTEGER,
                        Train_No VARCHAR(50) PRIMARY KEY,
                        Train_NameEng VARCHAR(100),
                        Train_AD VARCHAR(10),
                        Train_Status VARCHAR(100),
                        Sch_AT VARCHAR(5),
                        Sch_DT VARCHAR(5),
                        Late VARCHAR(10),
                        Exp_AT VARCHAR(5),
                        Exp_DT VARCHAR(5),
                        Sch_PF VARCHAR(10),
                        AN VARCHAR(1),
                        Div_City VARCHAR(100),
                        Del_Train BOOLEAN DEFAULT FALSE,
                        FOREIGN KEY (Train_No) REFERENCES Train_Data(Train_No)
                    );

                    CREATE TABLE IF NOT EXISTS Play_Configuration (
                        Train_Status VARCHAR(100) PRIMARY KEY,
                        Train_Type VARCHAR(50),
                        PF_Avl BOOLEAN,
                        English BOOLEAN,
                        Hindi BOOLEAN,
                        Bengali BOOLEAN,
                        Tamil BOOLEAN,
                        Telugu BOOLEAN,
                        Kannada BOOLEAN,
                        Malayalam BOOLEAN,
                        Marathi BOOLEAN,
                        Gujarati BOOLEAN,
                        Punjabi BOOLEAN
                    );

                    CREATE TABLE IF NOT EXISTS Play_Path (
                        Type VARCHAR(50) PRIMARY KEY,
                        Path TEXT NOT NULL
                    );

                    CREATE TABLE IF NOT EXISTS Station_Details (
                        Station_Name VARCHAR(100) PRIMARY KEY,
                        Station_Code VARCHAR(10),
                        Station_Type VARCHAR(50),
                        Station_Zone VARCHAR(50),
                        Station_Division VARCHAR(50),
                        Station_State VARCHAR(50),
                        Station_Address TEXT,
                        Station_Phone VARCHAR(20),
                        Station_Email VARCHAR(100),
                        Station_Website VARCHAR(100),
                        Station_Description TEXT,
                        Auto_Load BOOLEAN DEFAULT FALSE,
                        AutoLoad_Interval INT DEFAULT 30,
                        Auto_Delete BOOLEAN DEFAULT FALSE,
                        AutoDelete_Interval INT DEFAULT 60,
                        AutoDeletePost_Interval INT DEFAULT 30,
                        Avilable_PF INT DEFAULT 3,
                        P1 VARCHAR(50),
                        P2 VARCHAR(50),
                        P3 VARCHAR(50),
                        P4 VARCHAR(50),
                        P5 VARCHAR(50),
                        P6 VARCHAR(50),
                        P7 VARCHAR(50),
                        P8 VARCHAR(50),
                        P9 VARCHAR(50),
                        P10 VARCHAR(50),
                        English BOOLEAN DEFAULT TRUE,
                        Hindi BOOLEAN DEFAULT FALSE,
                        First_Lang VARCHAR(50),
                        Second_Lang VARCHAR(50),
                        Lang1_Enb BOOLEAN DEFAULT TRUE,
                        Lang2_Enb BOOLEAN DEFAULT TRUE
                    );

                    CREATE TABLE IF NOT EXISTS Station_Language_Config (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Station_Name VARCHAR(100) NOT NULL,
                        Language_Code VARCHAR(10) NOT NULL,
                        Is_Enabled BOOLEAN DEFAULT TRUE,
                        Wave_File_Path TEXT,
                        Created_At TEXT DEFAULT CURRENT_TIMESTAMP,
                        Updated_At TEXT,
                        FOREIGN KEY (Station_Name) REFERENCES Station_Details(Station_Name),
                        FOREIGN KEY (Language_Code) REFERENCES Languages(Code),
                        UNIQUE(Station_Name, Language_Code)
                    );

                    CREATE TABLE IF NOT EXISTS Station_Announcement_Config (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Station_Name VARCHAR(100) NOT NULL,
                        First_Language_Code VARCHAR(10),
                        Second_Language_Code VARCHAR(10),
                        First_Language_Enabled BOOLEAN DEFAULT TRUE,
                        Second_Language_Enabled BOOLEAN DEFAULT TRUE,
                        Created_At TEXT DEFAULT CURRENT_TIMESTAMP,
                        Updated_At TEXT,
                        FOREIGN KEY (Station_Name) REFERENCES Station_Details(Station_Name),
                        FOREIGN KEY (First_Language_Code) REFERENCES Languages(Code),
                        FOREIGN KEY (Second_Language_Code) REFERENCES Languages(Code)
                    );

                    -- User Management Tables
                    CREATE TABLE IF NOT EXISTS Users (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Username TEXT NOT NULL UNIQUE,
                        Password TEXT NOT NULL,
                        Role TEXT NOT NULL,
                        LastLogin TEXT,
                        Status TEXT DEFAULT 'Active'
                    );

                    CREATE TABLE IF NOT EXISTS UserPermissions (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        UserId INTEGER NOT NULL,
                        Permission TEXT NOT NULL,
                        FOREIGN KEY(UserId) REFERENCES Users(Id)
                    );

                    CREATE TABLE IF NOT EXISTS User_Details (
                        User_Name TEXT PRIMARY KEY,
                        Pass TEXT,
                        User_type TEXT,
                        Hint_Pass TEXT,
                        Chk_Adver INTEGER DEFAULT 0,
                        Chk_TDEntry INTEGER DEFAULT 0,
                        Chk_Reports INTEGER DEFAULT 0,
                        Chk_SD INTEGER DEFAULT 0,
                        Chk_AUser INTEGER DEFAULT 0,
                        Chk_ASCode INTEGER DEFAULT 0,
                        Chk_Rep INTEGER DEFAULT 0
                    );

                    -- Role Management Tables
                    CREATE TABLE IF NOT EXISTS Roles (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Name TEXT NOT NULL UNIQUE,
                        Description TEXT,
                        IsActive BOOLEAN DEFAULT TRUE,
                        CreatedAt TEXT DEFAULT CURRENT_TIMESTAMP,
                        UpdatedAt TEXT
                    );

                    CREATE TABLE IF NOT EXISTS RolePermissions (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        RoleId INTEGER NOT NULL,
                        Permission TEXT NOT NULL,
                        FOREIGN KEY(RoleId) REFERENCES Roles(Id)
                    );";

                string createTrainTypesTableQuery = @"
                    CREATE TABLE IF NOT EXISTS Train_Types (
                        ID VARCHAR(50) PRIMARY KEY,
                        Name VARCHAR(50) NOT NULL UNIQUE,
                        Description VARCHAR(200),
                        IsActive BOOLEAN DEFAULT TRUE,
                        CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                        ModifiedDate DATETIME
                    )";

                using (var command = new SQLiteCommand(createTableQuery, connection))
                {
                    command.ExecuteNonQuery();
                }

                using (var command = new SQLiteCommand(createTrainTypesTableQuery, connection))
                {
                    command.ExecuteNonQuery();
                }

                // Insert default train types if table is empty
                InsertDefaultTrainTypes(connection);

                // Initialize user tables and default users
                InitializeUserTables(connection);

                // Migrate existing station data to new structure
                MigrateStationData(connection);

                // Apply current station migration
                MigrateCurrentStation(connection);
            }
        }

        private static void InsertDefaultTrainTypes(SQLiteConnection connection)
        {
            // Check if train types already exist
            string checkQuery = "SELECT COUNT(*) FROM Train_Types";
            using (var checkCommand = new SQLiteCommand(checkQuery, connection))
            {
                int count = Convert.ToInt32(checkCommand.ExecuteScalar());
                if (count > 0) return; // Train types already exist
            }

            // Insert default train types (the ones that were previously hardcoded)
            string[] defaultTrainTypes = { "DEMU", "Express", "Passenger", "Garib Rath", "Superfast" };

            foreach (string trainType in defaultTrainTypes)
            {
                string insertQuery = @"INSERT INTO Train_Types (ID, Name, Description, IsActive, CreatedDate)
                                     VALUES (@ID, @Name, @Description, @IsActive, @CreatedDate)";
                using (var insertCommand = new SQLiteCommand(insertQuery, connection))
                {
                    insertCommand.Parameters.AddWithValue("@ID", Guid.NewGuid().ToString());
                    insertCommand.Parameters.AddWithValue("@Name", trainType);
                    insertCommand.Parameters.AddWithValue("@Description", $"Default {trainType} train type");
                    insertCommand.Parameters.AddWithValue("@IsActive", true);
                    insertCommand.Parameters.AddWithValue("@CreatedDate", DateTime.Now);
                    insertCommand.ExecuteNonQuery();
                }
            }
        }

        private static void InitializeUserTables(SQLiteConnection connection)
        {
            try
            {
                // Insert default Administrator role
                string insertAdminRoleQuery = @"
                    INSERT OR IGNORE INTO Roles (Name, Description, IsActive, CreatedAt) 
                    VALUES ('Administrator', 'Full system access with all permissions', TRUE, @CreatedAt)";
                using (var command = new SQLiteCommand(insertAdminRoleQuery, connection))
                {
                    command.Parameters.AddWithValue("@CreatedAt", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                    command.ExecuteNonQuery();
                }

                // Get Administrator role ID
                long adminRoleId;
                using (var command = new SQLiteCommand("SELECT Id FROM Roles WHERE Name = 'Administrator'", connection))
                {
                    adminRoleId = Convert.ToInt64(command.ExecuteScalar());
                }

                // Insert all permissions for Administrator role
                string[] allPermissions = { "Reports", "Add User", "Station Details", "Add Station Code", "Train Data Entry", "Add Advertising" };
                string insertAdminPermissionsQuery = @"
                    INSERT OR IGNORE INTO RolePermissions (RoleId, Permission) VALUES (@RoleId, @Permission)";
                foreach (string permission in allPermissions)
                {
                    using (var command = new SQLiteCommand(insertAdminPermissionsQuery, connection))
                    {
                        command.Parameters.AddWithValue("@RoleId", adminRoleId);
                        command.Parameters.AddWithValue("@Permission", permission);
                        command.ExecuteNonQuery();
                    }
                }

                // Insert default administrator user
                string insertAdminQuery = @"
                    INSERT OR IGNORE INTO Users (Username, Password, Role, Status) 
                    VALUES ('admin', 'admin123', 'Administrator', 'Active')";
                using (var command = new SQLiteCommand(insertAdminQuery, connection))
                {
                    command.ExecuteNonQuery();
                }

                // Insert some legacy users for testing
                string insertLegacyUsersQuery = @"
                    INSERT OR IGNORE INTO User_Details (User_Name, Pass, User_type, Chk_Adver, Chk_TDEntry, Chk_Reports, Chk_SD, Chk_AUser, Chk_ASCode, Chk_Rep)
                    VALUES 
                        ('operator', 'operator123', 'Operator', 1, 1, 0, 0, 0, 0, 0),
                        ('supervisor', 'supervisor123', 'Supervisor', 1, 1, 1, 1, 0, 0, 0),
                        ('user', 'user123', 'User', 1, 0, 0, 0, 0, 0, 0)";
                using (var command = new SQLiteCommand(insertLegacyUsersQuery, connection))
                {
                    command.ExecuteNonQuery();
                }

                // Create indexes for better performance
                string[] indexQueries = {
                    "CREATE INDEX IF NOT EXISTS idx_users_username ON Users(Username)",
                    "CREATE INDEX IF NOT EXISTS idx_userpermissions_userid ON UserPermissions(UserId)",
                    "CREATE INDEX IF NOT EXISTS idx_userdetails_username ON User_Details(User_Name)",
                    "CREATE INDEX IF NOT EXISTS idx_roles_name ON Roles(Name)",
                    "CREATE INDEX IF NOT EXISTS idx_rolepermissions_roleid ON RolePermissions(RoleId)"
                };

                foreach (string indexQuery in indexQueries)
                {
                    using (var command = new SQLiteCommand(indexQuery, connection))
                    {
                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                // Log initialization error but don't fail the application
                System.Diagnostics.Debug.WriteLine($"User table initialization error: {ex.Message}");
            }
        }

        private static void MigrateStationData(SQLiteConnection connection)
        {
            try
            {
                // Check if migration is needed
                string checkQuery = "SELECT COUNT(*) FROM Station_Language_Config";
                using (var checkCommand = new SQLiteCommand(checkQuery, connection))
                {
                    int count = Convert.ToInt32(checkCommand.ExecuteScalar());
                    if (count > 0) return; // Migration already done
                }

                // Get all stations
                string selectQuery = "SELECT Station_Name, English, Hindi, First_Lang, Second_Lang, Lang1_Enb, Lang2_Enb FROM Station_Details";
                using (var selectCommand = new SQLiteCommand(selectQuery, connection))
                {
                    using (var reader = selectCommand.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            string stationName = reader["Station_Name"].ToString();
                            bool english = Convert.ToBoolean(reader["English"]);
                            bool hindi = Convert.ToBoolean(reader["Hindi"]);
                            string firstLang = reader["First_Lang"]?.ToString();
                            string secondLang = reader["Second_Lang"]?.ToString();
                            bool lang1Enb = Convert.ToBoolean(reader["Lang1_Enb"]);
                            bool lang2Enb = Convert.ToBoolean(reader["Lang2_Enb"]);

                            // Insert English language config
                            if (english)
                            {
                                string insertLangQuery = @"
                                    INSERT INTO Station_Language_Config (Station_Name, Language_Code, Is_Enabled, Created_At)
                                    VALUES (@StationName, 'EN', @IsEnabled, @CreatedAt)";
                                using (var insertCommand = new SQLiteCommand(insertLangQuery, connection))
                                {
                                    insertCommand.Parameters.AddWithValue("@StationName", stationName);
                                    insertCommand.Parameters.AddWithValue("@IsEnabled", true);
                                    insertCommand.Parameters.AddWithValue("@CreatedAt", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                                    insertCommand.ExecuteNonQuery();
                                }
                            }

                            // Insert Hindi language config
                            if (hindi)
                            {
                                string insertLangQuery = @"
                                    INSERT INTO Station_Language_Config (Station_Name, Language_Code, Is_Enabled, Created_At)
                                    VALUES (@StationName, 'HI', @IsEnabled, @CreatedAt)";
                                using (var insertCommand = new SQLiteCommand(insertLangQuery, connection))
                                {
                                    insertCommand.Parameters.AddWithValue("@StationName", stationName);
                                    insertCommand.Parameters.AddWithValue("@IsEnabled", true);
                                    insertCommand.Parameters.AddWithValue("@CreatedAt", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                                    insertCommand.ExecuteNonQuery();
                                }
                            }

                            // Insert announcement config
                            string insertAnnouncementQuery = @"
                                INSERT INTO Station_Announcement_Config (Station_Name, First_Language_Code, Second_Language_Code, First_Language_Enabled, Second_Language_Enabled, Created_At)
                                VALUES (@StationName, @FirstLang, @SecondLang, @Lang1Enb, @Lang2Enb, @CreatedAt)";
                            using (var insertCommand = new SQLiteCommand(insertAnnouncementQuery, connection))
                            {
                                insertCommand.Parameters.AddWithValue("@StationName", stationName);
                                insertCommand.Parameters.AddWithValue("@FirstLang", firstLang ?? "EN");
                                insertCommand.Parameters.AddWithValue("@SecondLang", secondLang ?? "HI");
                                insertCommand.Parameters.AddWithValue("@Lang1Enb", lang1Enb);
                                insertCommand.Parameters.AddWithValue("@Lang2Enb", lang2Enb);
                                insertCommand.Parameters.AddWithValue("@CreatedAt", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                                insertCommand.ExecuteNonQuery();
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Log migration error but don't fail the application
                System.Diagnostics.Debug.WriteLine($"Migration error: {ex.Message}");
            }
        }

        private static void MigrateCurrentStation(SQLiteConnection connection)
        {
            try
            {
                // Check if Is_Current column already exists
                string checkColumnQuery = "PRAGMA table_info(Station_Details)";
                bool columnExists = false;
                using (var checkCommand = new SQLiteCommand(checkColumnQuery, connection))
                {
                    using (var reader = checkCommand.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            if (reader["name"].ToString() == "Is_Current")
                            {
                                columnExists = true;
                                break;
                            }
                        }
                    }
                }

                if (!columnExists)
                {
                    // Add Is_Current column to Station_Details table
                    string alterQuery = "ALTER TABLE Station_Details ADD COLUMN Is_Current BOOLEAN DEFAULT FALSE";
                    using (var alterCommand = new SQLiteCommand(alterQuery, connection))
                    {
                        alterCommand.ExecuteNonQuery();
                    }

                    // Create index for better performance when querying current station
                    string indexQuery = "CREATE INDEX IF NOT EXISTS idx_station_details_is_current ON Station_Details(Is_Current)";
                    using (var indexCommand = new SQLiteCommand(indexQuery, connection))
                    {
                        indexCommand.ExecuteNonQuery();
                    }

                    // Set the first station as current if no station is currently marked
                    string updateQuery = @"
                        UPDATE Station_Details 
                        SET Is_Current = TRUE 
                        WHERE Station_Name = (
                            SELECT Station_Name 
                            FROM Station_Details 
                            WHERE Is_Current = FALSE OR Is_Current IS NULL 
                            ORDER BY Station_Name 
                            LIMIT 1
                        ) 
                        AND (SELECT COUNT(*) FROM Station_Details WHERE Is_Current = TRUE) = 0";
                    using (var updateCommand = new SQLiteCommand(updateQuery, connection))
                    {
                        updateCommand.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                // Log migration error but don't fail the application
                System.Diagnostics.Debug.WriteLine($"Current station migration error: {ex.Message}");
            }
        }
        public static void InitializeLogsDatabase()
        {
            using (var connection = GetLogsConnection())
            {
                string createLogsTableQuery = @"
                    CREATE TABLE IF NOT EXISTS System_Logs (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Timestamp TEXT NOT NULL,
                        Level TEXT NOT NULL,
                        Category TEXT NOT NULL,
                        Message TEXT NOT NULL,
                        Details TEXT,
                        UserId INTEGER,
                        Username TEXT,
                        Source TEXT,
                        Exception TEXT,
                        CreatedAt TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
                    );

                    CREATE TABLE IF NOT EXISTS User_Activity_Logs (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Timestamp TEXT NOT NULL,
                        UserId INTEGER NOT NULL,
                        Username TEXT NOT NULL,
                        Action TEXT NOT NULL,
                        Category TEXT NOT NULL,
                        EntityType TEXT,
                        EntityId TEXT,
                        OldValues TEXT,
                        NewValues TEXT,
                        IPAddress TEXT,
                        UserAgent TEXT,
                        SessionId TEXT,
                        CreatedAt TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
                    );

                    CREATE TABLE IF NOT EXISTS Log_Categories (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Name TEXT NOT NULL UNIQUE,
                        Description TEXT,
                        IsActive BOOLEAN DEFAULT TRUE,
                        CreatedAt TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
                    );

                    -- Create indexes for better performance
                    CREATE INDEX IF NOT EXISTS idx_system_logs_timestamp ON System_Logs(Timestamp);
                    CREATE INDEX IF NOT EXISTS idx_system_logs_level ON System_Logs(Level);
                    CREATE INDEX IF NOT EXISTS idx_system_logs_category ON System_Logs(Category);
                    CREATE INDEX IF NOT EXISTS idx_system_logs_userid ON System_Logs(UserId);

                    CREATE INDEX IF NOT EXISTS idx_user_activity_timestamp ON User_Activity_Logs(Timestamp);
                    CREATE INDEX IF NOT EXISTS idx_user_activity_userid ON User_Activity_Logs(UserId);
                    CREATE INDEX IF NOT EXISTS idx_user_activity_action ON User_Activity_Logs(Action);
                    CREATE INDEX IF NOT EXISTS idx_user_activity_category ON User_Activity_Logs(Category);
                ";

                using (var command = new SQLiteCommand(createLogsTableQuery, connection))
                {
                    command.ExecuteNonQuery();
                }

                // Insert default log categories
                InsertDefaultLogCategories(connection);
            }
        }

        private static void InsertDefaultLogCategories(SQLiteConnection connection)
        {
            var defaultCategories = new[]
            {
                ("Train Management", "Train addition, modification, deletion operations"),
                ("Station Management", "Station details and configuration changes"),
                ("User Management", "User account operations and authentication"),
                ("Announcement", "Manual and automatic announcement activities"),
                ("Advertising", "Advertisement management and playback"),
                ("System", "System configuration and maintenance"),
                ("Database", "Database operations and maintenance"),
                ("Audio", "Audio file operations and playback"),
                ("Error", "System errors and exceptions"),
                ("Security", "Security-related events and access control")
            };

            foreach (var (name, description) in defaultCategories)
            {
                string checkQuery = "SELECT COUNT(*) FROM Log_Categories WHERE Name = @Name";
                using (var checkCommand = new SQLiteCommand(checkQuery, connection))
                {
                    checkCommand.Parameters.AddWithValue("@Name", name);
                    int count = Convert.ToInt32(checkCommand.ExecuteScalar());

                    if (count == 0)
                    {
                        string insertQuery = @"INSERT INTO Log_Categories (Name, Description, CreatedAt)
                                             VALUES (@Name, @Description, @CreatedAt)";
                        using (var insertCommand = new SQLiteCommand(insertQuery, connection))
                        {
                            insertCommand.Parameters.AddWithValue("@Name", name);
                            insertCommand.Parameters.AddWithValue("@Description", description);
                            insertCommand.Parameters.AddWithValue("@CreatedAt", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                            insertCommand.ExecuteNonQuery();
                        }
                    }
                }
            }
        }
    }
}