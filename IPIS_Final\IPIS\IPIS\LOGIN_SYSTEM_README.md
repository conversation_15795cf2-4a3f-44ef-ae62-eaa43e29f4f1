# IPIS Login System Documentation

## Overview

The IPIS (Integrated Passenger Information System) now includes a complete authentication and authorization system that supports both new and legacy user tables. The system provides secure login, role-based access control, and session management.

## Features

### Authentication
- **Dual Table Support**: Supports both new `Users` table and legacy `User_Details` table
- **Secure Login**: Username/password authentication with session management
- **Session Tracking**: Real-time session duration and user information display
- **Automatic Logout**: Session cleanup on application exit

### Authorization
- **Role-Based Access Control**: Four user roles (Administrator, Supervisor, Operator, User)
- **Permission-Based Access**: Granular permissions for different features
- **Menu Access Control**: Dynamic menu visibility based on user permissions
- **Form Access Control**: Permission checks before opening forms

### User Management
- **User CRUD Operations**: Create, read, update, delete users
- **Permission Management**: Assign and manage user permissions
- **Role Assignment**: Assign roles to users
- **User Status**: Active/Inactive user status management

## Database Schema

### New Users Table (`Users`)
```sql
CREATE TABLE Users (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    Username TEXT NOT NULL UNIQUE,
    Password TEXT NOT NULL,
    Role TEXT NOT NULL,
    LastLogin TEXT,
    Status TEXT DEFAULT 'Active'
);
```

### User Permissions Table (`UserPermissions`)
```sql
CREATE TABLE UserPermissions (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    UserId INTEGER NOT NULL,
    Permission TEXT NOT NULL,
    FOREIGN KEY(UserId) REFERENCES Users(Id)
);
```

### Legacy User Details Table (`User_Details`)
```sql
CREATE TABLE User_Details (
    User_Name TEXT PRIMARY KEY,
    Pass TEXT,
    User_type TEXT,
    Hint_Pass TEXT,
    Chk_Adver INTEGER DEFAULT 0,
    Chk_TDEntry INTEGER DEFAULT 0,
    Chk_Reports INTEGER DEFAULT 0,
    Chk_SD INTEGER DEFAULT 0,
    Chk_AUser INTEGER DEFAULT 0,
    Chk_ASCode INTEGER DEFAULT 0,
    Chk_Rep INTEGER DEFAULT 0
);
```

## User Roles and Permissions

### Administrator
- **Full System Access**: All features and permissions
- **User Management**: Can create, edit, delete users
- **System Settings**: Can access system configuration
- **All Permissions**: Reports, Add User, Station Details, Add Station Code, Train Data Entry, Add Advertising

### Supervisor
- **Limited User Management**: Can manage users but not system settings
- **Reports Access**: Can access reporting features
- **Station Management**: Can manage station details
- **Permissions**: Reports, Station Details, Add Station Code, Train Data Entry, Add Advertising

### Operator
- **Basic Operations**: Can perform basic system operations
- **Advertising**: Can manage advertisements
- **Train Operations**: Can manage train data
- **Permissions**: Add Advertising, Train Data Entry

### User
- **Basic Access**: Limited access to basic features
- **Advertising**: Can access advertising features
- **Permissions**: Add Advertising

## Default Users

The system comes with pre-configured users for testing:

### New System Users
- **Username**: `admin`
- **Password**: `admin123`
- **Role**: Administrator
- **Permissions**: All permissions

### Legacy System Users
- **Username**: `operator`
- **Password**: `operator123`
- **Role**: Operator
- **Permissions**: Advertising, Train Data Entry

- **Username**: `supervisor`
- **Password**: `supervisor123`
- **Role**: Supervisor
- **Permissions**: Advertising, Train Data Entry, Reports, Station Details

- **Username**: `user`
- **Password**: `user123`
- **Role**: User
- **Permissions**: Advertising

## Login Flow

1. **Application Start**: Program.cs shows LoginForm
2. **User Input**: User enters username and password
3. **Authentication**: System checks both new and legacy tables
4. **Session Creation**: If valid, creates user session
5. **Main Form**: Opens MainForm with role-based access
6. **Session Management**: Real-time session tracking
7. **Logout**: User can logout or application closes

## Code Structure

### Models
- `User.cs`: User model with both new and legacy properties

### Services
- `UserService.cs`: Business logic for user operations
- `SessionManager.cs`: Static class for session management

### Repositories
- `IUserRepository.cs`: Interface for user data access
- `SQLiteUserRepository.cs`: SQLite implementation

### Forms
- `LoginForm.cs`: Modern login interface
- `UserManagementForm.cs`: User management interface
- `MainForm.cs`: Main application with role-based menus

### Utils
- `SessionManager.cs`: Session state management
- `Database.cs`: Database initialization including user tables

## Security Features

### Password Security
- Passwords are stored in plain text (for demo purposes)
- In production, implement password hashing (bcrypt, PBKDF2, etc.)

### Session Security
- Session validation on form access
- Automatic session cleanup on logout
- Session timeout handling

### Access Control
- Role-based menu visibility
- Permission-based form access
- Input validation and sanitization

## Usage Examples

### Login
```csharp
// User enters credentials in LoginForm
User authenticatedUser = userService.AuthenticateUser(username, password);
if (authenticatedUser != null)
{
    SessionManager.Login(authenticatedUser);
    // Open main form
}
```

### Permission Check
```csharp
// Check if user has specific permission
if (SessionManager.HasPermission("Add Advertising"))
{
    // Allow access to advertising features
}

// Check user role
if (SessionManager.IsAdministrator())
{
    // Show admin features
}
```

### Session Information
```csharp
// Get current user
User currentUser = SessionManager.CurrentUser;

// Check if logged in
bool isLoggedIn = SessionManager.IsLoggedIn;

// Get session duration
TimeSpan duration = SessionManager.SessionDuration;
```

## Configuration

### Database Connection
The system uses SQLite database located at `data/ipis.db`. The connection string is configured in `Database.cs`.

### Default Users
Default users are created during database initialization in `Database.InitializeUserTables()`.

### Permissions
Available permissions are defined in the user management interface:
- Reports
- Add User
- Station Details
- Add Station Code
- Train Data Entry
- Add Advertising

## Troubleshooting

### Common Issues

1. **Login Fails**
   - Check if user exists in database
   - Verify username/password combination
   - Check if user status is 'Active'

2. **Permission Denied**
   - Verify user has required permissions
   - Check user role assignments
   - Ensure permissions are properly mapped

3. **Database Errors**
   - Verify database file exists
   - Check database connection string
   - Ensure tables are properly initialized

### Debug Information
- Session information is displayed in the status bar
- User role and permissions are shown in user info
- Database errors are logged to console

## Future Enhancements

### Security Improvements
- Password hashing and salting
- Session timeout configuration
- Failed login attempt tracking
- Account lockout mechanism

### Additional Features
- Password reset functionality
- User profile management
- Audit logging
- Multi-factor authentication
- LDAP/Active Directory integration

### Performance Optimizations
- Connection pooling
- Caching mechanisms
- Query optimization
- Index improvements

## Conclusion

The IPIS login system provides a robust foundation for user authentication and authorization. It supports both modern and legacy user management approaches, ensuring compatibility with existing systems while providing enhanced security and user experience features.

The system is designed to be extensible and can be easily enhanced with additional security features and functionality as needed. 