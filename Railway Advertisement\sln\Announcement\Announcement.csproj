﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{DB73A398-BDAF-470F-8553-29C500FFB03D}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>Announcement</RootNamespace>
    <AssemblyName>Announcement</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <ApplicationManifest>app.manifest</ApplicationManifest>
    <StartupObject>Announcement.Program</StartupObject>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AxInterop.WMPLib">
      <HintPath>..\..\Debug\Debug\AxInterop.WMPLib.dll</HintPath>
    </Reference>
    <Reference Include="Interop.WMPLib">
      <HintPath>..\..\Debug\Debug\Interop.WMPLib.dll</HintPath>
    </Reference>
    <Reference Include="lcpi.data.oledb.net4_8_0, Version=1.30.0.4837, Culture=neutral, PublicKeyToken=ff716095e8002e7e, processorArchitecture=MSIL">
      <HintPath>..\packages\lcpi.data.oledb.1.30.0.4837\lib\net48\lcpi.data.oledb.net4_8_0.dll</HintPath>
    </Reference>
    <Reference Include="lcpi.lib.net4_8_0, Version=3.2.0.1938, Culture=neutral, PublicKeyToken=ff716095e8002e7e, processorArchitecture=MSIL">
      <HintPath>..\packages\lcpi.lib.3.2.0.1938\lib\net48\lcpi.lib.net4_8_0.dll</HintPath>
    </Reference>
    <Reference Include="NAudio">
      <HintPath>..\..\Debug\Debug\NAudio.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
  </ItemGroup>
  <ItemGroup>
    <AppDesigner Include="Properties\" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="About.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="About.Designer.cs">
      <DependentUpon>About.cs</DependentUpon>
    </Compile>
    <Compile Include="Advertising.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Advertising.Designer.cs">
      <DependentUpon>Advertising.cs</DependentUpon>
    </Compile>
    <Compile Include="Annaouncment.cs" />
    <Compile Include="battleship.cs" />
    <Compile Include="Class_Database.cs" />
    <Compile Include="Load_TrainGrid.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Load_TrainGrid.Designer.cs">
      <DependentUpon>Load_TrainGrid.cs</DependentUpon>
    </Compile>
    <Compile Include="Login.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Login.Designer.cs">
      <DependentUpon>Login.cs</DependentUpon>
    </Compile>
    <Compile Include="Main.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Main.Designer.cs">
      <DependentUpon>Main.cs</DependentUpon>
    </Compile>
    <Compile Include="Misc_Functions.cs" />
    <Compile Include="Online_Trains.cs" />
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Resources.cs" />
    <Compile Include="Properties\Settings.Designer.cs">
      <DependentUpon>Settings.settings</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <Compile Include="Reports.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports.Designer.cs">
      <DependentUpon>Reports.cs</DependentUpon>
    </Compile>
    <Compile Include="Station_Code.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Station_Code.Designer.cs">
      <DependentUpon>Station_Code.cs</DependentUpon>
    </Compile>
    <Compile Include="Station_Details.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Station_Details.Designer.cs">
      <DependentUpon>Station_Details.cs</DependentUpon>
    </Compile>
    <Compile Include="TrainData.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="TrainData.Designer.cs">
      <DependentUpon>TrainData.cs</DependentUpon>
    </Compile>
    <Compile Include="User.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="User.Designer.cs">
      <DependentUpon>User.cs</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="About.resources" />
    <EmbeddedResource Include="Advertising.resources" />
    <EmbeddedResource Include="Load_TrainGrid.resources" />
    <EmbeddedResource Include="Login.resources" />
    <EmbeddedResource Include="Main.resources" />
    <EmbeddedResource Include="Properties\Resources.resources" />
    <EmbeddedResource Include="Reports.resources" />
    <EmbeddedResource Include="Station_Code.resources" />
    <EmbeddedResource Include="Station_Details.resources" />
    <EmbeddedResource Include="TrainData.resources" />
    <EmbeddedResource Include="User.resources" />
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="app.manifest" />
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Database.accdb">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>