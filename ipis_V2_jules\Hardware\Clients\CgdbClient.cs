using System;
using System.Collections.Generic; // Required for List in BoardStatus
using System.Threading.Tasks;
using ipis_V2_jules.DisplayFormatters;
using ipis_V2_jules.Hardware.Protocols;

namespace ipis_V2_jules.Hardware.Clients
{
    public class CgdbClient : IBoardClient
    {
        private readonly ICommunicationService _communicationService;
        private readonly IDisplayDataFormatter _dataFormatter; // Specifically CgdbDataFormatter

        public CgdbClient(ICommunicationService communicationService, IDisplayDataFormatter dataFormatter)
        {
            _communicationService = communicationService ?? throw new ArgumentNullException(nameof(communicationService));
            if (!(dataFormatter is CgdbDataFormatter))
            {
                throw new ArgumentException("CgdbClient requires a CgdbDataFormatter.", nameof(dataFormatter));
            }
            _dataFormatter = dataFormatter;
        }

        public async Task<bool> SendMessageAsync(FormattedDisplayData data, byte boardAddress, byte subAddress, byte serialNo)
        {
            Console.WriteLine($"CGDB Client: Sending coach guidance message to address {boardAddress}, sub-address {subAddress}, serial {serialNo}.");
            // CGDBs display coach composition. Formatter prepares this into Line1 or specific structure.
            if (data.Line1 == null) {
                Console.WriteLine("CGDB Client: No data in Line1 to send for coach guidance.");
                return false;
            }

            byte[] payload = data.Line1; // CgdbDataFormatter should place formatted coach data here.
            byte functionCode = 0x02; // Example: Function code for "display coach guidance"

            byte[] packet = DisplayPacketBuilder.BuildMessagePacket(boardAddress, subAddress, serialNo, functionCode, payload, data.AdditionalHeaderBytes);

            bool success = _communicationService.SendData(packet);
            return await Task.FromResult(success);
        }

        public async Task<bool> ClearDisplayAsync(byte boardAddress, byte subAddress, byte serialNo)
        {
            Console.WriteLine($"CGDB Client: Clearing display for address {boardAddress}, sub-address {subAddress}, serial {serialNo}.");
            byte[] packet = DisplayPacketBuilder.BuildClearDisplayPacket(boardAddress, subAddress, serialNo);
            bool success = _communicationService.SendData(packet);
            return await Task.FromResult(success);
        }

        public async Task<BoardStatus> CheckLinkAsync(byte boardAddress, byte subAddress, byte serialNo)
        {
            Console.WriteLine($"CGDB Client: Checking link for address {boardAddress}, sub-address {subAddress}, serial {serialNo}.");
            byte[] packet = DisplayPacketBuilder.BuildLinkCheckPacket(boardAddress, subAddress, serialNo);
            _communicationService.SendData(packet);

            await Task.Delay(50);
            return new BoardStatus { IsLinkOk = true, StatusMessage = "Link check placeholder: OK (No actual response parsing)" };
        }

        public async Task<bool> SetConfigurationAsync(byte boardAddress, byte subAddress, byte serialNo, byte[] configData)
        {
            Console.WriteLine($"CGDB Client: Setting configuration for address {boardAddress}, sub-address {subAddress}, serial {serialNo}.");
            byte[] packet = DisplayPacketBuilder.BuildSetConfigPacket(boardAddress, subAddress, serialNo, configData);
            bool success = _communicationService.SendData(packet);
            return await Task.FromResult(success);
        }

        public async Task<byte[]> GetConfigurationAsync(byte boardAddress, byte subAddress, byte serialNo)
        {
            Console.WriteLine($"CGDB Client: Getting configuration for address {boardAddress}, sub-address {subAddress}, serial {serialNo}.");
            byte[] packet = DisplayPacketBuilder.BuildGetConfigPacket(boardAddress, subAddress, serialNo);
            _communicationService.SendData(packet);
            await Task.Delay(50);
            return await Task.FromResult(Array.Empty<byte>());
        }

        public async Task<bool> ResetBoardAsync(byte boardAddress, byte subAddress, byte serialNo)
        {
            Console.WriteLine($"CGDB Client: Resetting board address {boardAddress}, sub-address {subAddress}, serial {serialNo}.");
            byte[] packet = DisplayPacketBuilder.BuildResetPacket(boardAddress, subAddress, serialNo);
            bool success = _communicationService.SendData(packet);
            return await Task.FromResult(success);
        }
    }
}
