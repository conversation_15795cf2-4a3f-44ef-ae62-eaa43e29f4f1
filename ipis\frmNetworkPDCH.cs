// Decompiled with JetBrains decompiler
// Type: ipis.frmNetworkPDCH
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using ipis.My;
using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

namespace ipis
{

[DesignerGenerated]
public class frmNetworkPDCH : Form
{
  private static List<WeakReference> __ENCList = new List<WeakReference>();
  private IContainer components;
  [AccessedThroughProperty("btnExit")]
  private Button _btnExit;
  [AccessedThroughProperty("GroupBox1")]
  private GroupBox _GroupBox1;
  [AccessedThroughProperty("btnP16S8")]
  private Button _btnP16S8;
  [AccessedThroughProperty("btnP15S8")]
  private Button _btnP15S8;
  [AccessedThroughProperty("btnP14S8")]
  private Button _btnP14S8;
  [AccessedThroughProperty("btnP13S8")]
  private Button _btnP13S8;
  [AccessedThroughProperty("btnP12S8")]
  private Button _btnP12S8;
  [AccessedThroughProperty("btnP11S8")]
  private Button _btnP11S8;
  [AccessedThroughProperty("btnP10S8")]
  private Button _btnP10S8;
  [AccessedThroughProperty("btnP9S8")]
  private Button _btnP9S8;
  [AccessedThroughProperty("btnP8S8")]
  private Button _btnP8S8;
  [AccessedThroughProperty("btnP7S8")]
  private Button _btnP7S8;
  [AccessedThroughProperty("btnP6S8")]
  private Button _btnP6S8;
  [AccessedThroughProperty("btnP5S8")]
  private Button _btnP5S8;
  [AccessedThroughProperty("btnP4S8")]
  private Button _btnP4S8;
  [AccessedThroughProperty("btnP3S8")]
  private Button _btnP3S8;
  [AccessedThroughProperty("btnP2S8")]
  private Button _btnP2S8;
  [AccessedThroughProperty("btnP1S8")]
  private Button _btnP1S8;
  [AccessedThroughProperty("cmbP16S8")]
  private ComboBox _cmbP16S8;
  [AccessedThroughProperty("cmbP15S8")]
  private ComboBox _cmbP15S8;
  [AccessedThroughProperty("cmbP14S8")]
  private ComboBox _cmbP14S8;
  [AccessedThroughProperty("cmbP13S8")]
  private ComboBox _cmbP13S8;
  [AccessedThroughProperty("cmbP12S8")]
  private ComboBox _cmbP12S8;
  [AccessedThroughProperty("cmbP11S8")]
  private ComboBox _cmbP11S8;
  [AccessedThroughProperty("cmbP10S8")]
  private ComboBox _cmbP10S8;
  [AccessedThroughProperty("cmbP9S8")]
  private ComboBox _cmbP9S8;
  [AccessedThroughProperty("cmbP8S8")]
  private ComboBox _cmbP8S8;
  [AccessedThroughProperty("cmbP7S8")]
  private ComboBox _cmbP7S8;
  [AccessedThroughProperty("cmbP6S8")]
  private ComboBox _cmbP6S8;
  [AccessedThroughProperty("cmbP5S8")]
  private ComboBox _cmbP5S8;
  [AccessedThroughProperty("cmbP4S8")]
  private ComboBox _cmbP4S8;
  [AccessedThroughProperty("cmbP3S8")]
  private ComboBox _cmbP3S8;
  [AccessedThroughProperty("cmbP2S8")]
  private ComboBox _cmbP2S8;
  [AccessedThroughProperty("cmbP1S8")]
  private ComboBox _cmbP1S8;
  [AccessedThroughProperty("btnP16S7")]
  private Button _btnP16S7;
  [AccessedThroughProperty("btnP15S7")]
  private Button _btnP15S7;
  [AccessedThroughProperty("btnP14S7")]
  private Button _btnP14S7;
  [AccessedThroughProperty("btnP13S7")]
  private Button _btnP13S7;
  [AccessedThroughProperty("btnP12S7")]
  private Button _btnP12S7;
  [AccessedThroughProperty("btnP11S7")]
  private Button _btnP11S7;
  [AccessedThroughProperty("btnP10S7")]
  private Button _btnP10S7;
  [AccessedThroughProperty("btnP9S7")]
  private Button _btnP9S7;
  [AccessedThroughProperty("btnP8S7")]
  private Button _btnP8S7;
  [AccessedThroughProperty("btnP7S7")]
  private Button _btnP7S7;
  [AccessedThroughProperty("btnP6S7")]
  private Button _btnP6S7;
  [AccessedThroughProperty("btnP5S7")]
  private Button _btnP5S7;
  [AccessedThroughProperty("btnP4S7")]
  private Button _btnP4S7;
  [AccessedThroughProperty("btnP3S7")]
  private Button _btnP3S7;
  [AccessedThroughProperty("btnP2S7")]
  private Button _btnP2S7;
  [AccessedThroughProperty("btnP1S7")]
  private Button _btnP1S7;
  [AccessedThroughProperty("cmbP16S7")]
  private ComboBox _cmbP16S7;
  [AccessedThroughProperty("cmbP15S7")]
  private ComboBox _cmbP15S7;
  [AccessedThroughProperty("cmbP14S7")]
  private ComboBox _cmbP14S7;
  [AccessedThroughProperty("cmbP13S7")]
  private ComboBox _cmbP13S7;
  [AccessedThroughProperty("cmbP12S7")]
  private ComboBox _cmbP12S7;
  [AccessedThroughProperty("cmbP11S7")]
  private ComboBox _cmbP11S7;
  [AccessedThroughProperty("cmbP10S7")]
  private ComboBox _cmbP10S7;
  [AccessedThroughProperty("cmbP9S7")]
  private ComboBox _cmbP9S7;
  [AccessedThroughProperty("cmbP8S7")]
  private ComboBox _cmbP8S7;
  [AccessedThroughProperty("cmbP7S7")]
  private ComboBox _cmbP7S7;
  [AccessedThroughProperty("cmbP6S7")]
  private ComboBox _cmbP6S7;
  [AccessedThroughProperty("cmbP5S7")]
  private ComboBox _cmbP5S7;
  [AccessedThroughProperty("cmbP4S7")]
  private ComboBox _cmbP4S7;
  [AccessedThroughProperty("cmbP3S7")]
  private ComboBox _cmbP3S7;
  [AccessedThroughProperty("cmbP2S7")]
  private ComboBox _cmbP2S7;
  [AccessedThroughProperty("cmbP1S7")]
  private ComboBox _cmbP1S7;
  [AccessedThroughProperty("btnP16S6")]
  private Button _btnP16S6;
  [AccessedThroughProperty("btnP15S6")]
  private Button _btnP15S6;
  [AccessedThroughProperty("btnP14S6")]
  private Button _btnP14S6;
  [AccessedThroughProperty("btnP13S6")]
  private Button _btnP13S6;
  [AccessedThroughProperty("btnP12S6")]
  private Button _btnP12S6;
  [AccessedThroughProperty("btnP11S6")]
  private Button _btnP11S6;
  [AccessedThroughProperty("btnP10S6")]
  private Button _btnP10S6;
  [AccessedThroughProperty("btnP9S6")]
  private Button _btnP9S6;
  [AccessedThroughProperty("btnP8S6")]
  private Button _btnP8S6;
  [AccessedThroughProperty("btnP7S6")]
  private Button _btnP7S6;
  [AccessedThroughProperty("btnP6S6")]
  private Button _btnP6S6;
  [AccessedThroughProperty("btnP5S6")]
  private Button _btnP5S6;
  [AccessedThroughProperty("btnP4S6")]
  private Button _btnP4S6;
  [AccessedThroughProperty("btnP3S6")]
  private Button _btnP3S6;
  [AccessedThroughProperty("btnP2S6")]
  private Button _btnP2S6;
  [AccessedThroughProperty("btnP1S6")]
  private Button _btnP1S6;
  [AccessedThroughProperty("cmbP16S6")]
  private ComboBox _cmbP16S6;
  [AccessedThroughProperty("cmbP15S6")]
  private ComboBox _cmbP15S6;
  [AccessedThroughProperty("cmbP14S6")]
  private ComboBox _cmbP14S6;
  [AccessedThroughProperty("cmbP13S6")]
  private ComboBox _cmbP13S6;
  [AccessedThroughProperty("cmbP12S6")]
  private ComboBox _cmbP12S6;
  [AccessedThroughProperty("cmbP11S6")]
  private ComboBox _cmbP11S6;
  [AccessedThroughProperty("cmbP10S6")]
  private ComboBox _cmbP10S6;
  [AccessedThroughProperty("cmbP9S6")]
  private ComboBox _cmbP9S6;
  [AccessedThroughProperty("cmbP8S6")]
  private ComboBox _cmbP8S6;
  [AccessedThroughProperty("cmbP7S6")]
  private ComboBox _cmbP7S6;
  [AccessedThroughProperty("cmbP6S6")]
  private ComboBox _cmbP6S6;
  [AccessedThroughProperty("cmbP5S6")]
  private ComboBox _cmbP5S6;
  [AccessedThroughProperty("cmbP4S6")]
  private ComboBox _cmbP4S6;
  [AccessedThroughProperty("cmbP3S6")]
  private ComboBox _cmbP3S6;
  [AccessedThroughProperty("cmbP2S6")]
  private ComboBox _cmbP2S6;
  [AccessedThroughProperty("cmbP1S6")]
  private ComboBox _cmbP1S6;
  [AccessedThroughProperty("btnP16S5")]
  private Button _btnP16S5;
  [AccessedThroughProperty("btnP15S5")]
  private Button _btnP15S5;
  [AccessedThroughProperty("btnP14S5")]
  private Button _btnP14S5;
  [AccessedThroughProperty("btnP13S5")]
  private Button _btnP13S5;
  [AccessedThroughProperty("btnP12S5")]
  private Button _btnP12S5;
  [AccessedThroughProperty("btnP11S5")]
  private Button _btnP11S5;
  [AccessedThroughProperty("btnP10S5")]
  private Button _btnP10S5;
  [AccessedThroughProperty("btnP9S5")]
  private Button _btnP9S5;
  [AccessedThroughProperty("btnP8S5")]
  private Button _btnP8S5;
  [AccessedThroughProperty("btnP7S5")]
  private Button _btnP7S5;
  [AccessedThroughProperty("btnP6S5")]
  private Button _btnP6S5;
  [AccessedThroughProperty("btnP5S5")]
  private Button _btnP5S5;
  [AccessedThroughProperty("btnP4S5")]
  private Button _btnP4S5;
  [AccessedThroughProperty("btnP3S5")]
  private Button _btnP3S5;
  [AccessedThroughProperty("btnP2S5")]
  private Button _btnP2S5;
  [AccessedThroughProperty("btnP1S5")]
  private Button _btnP1S5;
  [AccessedThroughProperty("cmbP16S5")]
  private ComboBox _cmbP16S5;
  [AccessedThroughProperty("cmbP15S5")]
  private ComboBox _cmbP15S5;
  [AccessedThroughProperty("cmbP14S5")]
  private ComboBox _cmbP14S5;
  [AccessedThroughProperty("cmbP13S5")]
  private ComboBox _cmbP13S5;
  [AccessedThroughProperty("cmbP12S5")]
  private ComboBox _cmbP12S5;
  [AccessedThroughProperty("cmbP11S5")]
  private ComboBox _cmbP11S5;
  [AccessedThroughProperty("cmbP10S5")]
  private ComboBox _cmbP10S5;
  [AccessedThroughProperty("cmbP9S5")]
  private ComboBox _cmbP9S5;
  [AccessedThroughProperty("cmbP8S5")]
  private ComboBox _cmbP8S5;
  [AccessedThroughProperty("cmbP7S5")]
  private ComboBox _cmbP7S5;
  [AccessedThroughProperty("cmbP6S5")]
  private ComboBox _cmbP6S5;
  [AccessedThroughProperty("cmbP5S5")]
  private ComboBox _cmbP5S5;
  [AccessedThroughProperty("cmbP4S5")]
  private ComboBox _cmbP4S5;
  [AccessedThroughProperty("cmbP3S5")]
  private ComboBox _cmbP3S5;
  [AccessedThroughProperty("cmbP2S5")]
  private ComboBox _cmbP2S5;
  [AccessedThroughProperty("cmbP1S5")]
  private ComboBox _cmbP1S5;
  [AccessedThroughProperty("btnP16S4")]
  private Button _btnP16S4;
  [AccessedThroughProperty("btnP15S4")]
  private Button _btnP15S4;
  [AccessedThroughProperty("btnP14S4")]
  private Button _btnP14S4;
  [AccessedThroughProperty("btnP13S4")]
  private Button _btnP13S4;
  [AccessedThroughProperty("btnP12S4")]
  private Button _btnP12S4;
  [AccessedThroughProperty("btnP11S4")]
  private Button _btnP11S4;
  [AccessedThroughProperty("btnP10S4")]
  private Button _btnP10S4;
  [AccessedThroughProperty("btnP9S4")]
  private Button _btnP9S4;
  [AccessedThroughProperty("btnP8S4")]
  private Button _btnP8S4;
  [AccessedThroughProperty("btnP7S4")]
  private Button _btnP7S4;
  [AccessedThroughProperty("btnP6S4")]
  private Button _btnP6S4;
  [AccessedThroughProperty("btnP5S4")]
  private Button _btnP5S4;
  [AccessedThroughProperty("btnP4S4")]
  private Button _btnP4S4;
  [AccessedThroughProperty("btnP3S4")]
  private Button _btnP3S4;
  [AccessedThroughProperty("btnP2S4")]
  private Button _btnP2S4;
  [AccessedThroughProperty("btnP1S4")]
  private Button _btnP1S4;
  [AccessedThroughProperty("cmbP16S4")]
  private ComboBox _cmbP16S4;
  [AccessedThroughProperty("cmbP15S4")]
  private ComboBox _cmbP15S4;
  [AccessedThroughProperty("cmbP14S4")]
  private ComboBox _cmbP14S4;
  [AccessedThroughProperty("cmbP13S4")]
  private ComboBox _cmbP13S4;
  [AccessedThroughProperty("cmbP12S4")]
  private ComboBox _cmbP12S4;
  [AccessedThroughProperty("cmbP11S4")]
  private ComboBox _cmbP11S4;
  [AccessedThroughProperty("cmbP10S4")]
  private ComboBox _cmbP10S4;
  [AccessedThroughProperty("cmbP9S4")]
  private ComboBox _cmbP9S4;
  [AccessedThroughProperty("cmbP8S4")]
  private ComboBox _cmbP8S4;
  [AccessedThroughProperty("cmbP7S4")]
  private ComboBox _cmbP7S4;
  [AccessedThroughProperty("cmbP6S4")]
  private ComboBox _cmbP6S4;
  [AccessedThroughProperty("cmbP5S4")]
  private ComboBox _cmbP5S4;
  [AccessedThroughProperty("cmbP4S4")]
  private ComboBox _cmbP4S4;
  [AccessedThroughProperty("cmbP3S4")]
  private ComboBox _cmbP3S4;
  [AccessedThroughProperty("cmbP2S4")]
  private ComboBox _cmbP2S4;
  [AccessedThroughProperty("cmbP1S4")]
  private ComboBox _cmbP1S4;
  [AccessedThroughProperty("btnP16S3")]
  private Button _btnP16S3;
  [AccessedThroughProperty("btnP15S3")]
  private Button _btnP15S3;
  [AccessedThroughProperty("btnP14S3")]
  private Button _btnP14S3;
  [AccessedThroughProperty("btnP13S3")]
  private Button _btnP13S3;
  [AccessedThroughProperty("btnP12S3")]
  private Button _btnP12S3;
  [AccessedThroughProperty("btnP11S3")]
  private Button _btnP11S3;
  [AccessedThroughProperty("btnP10S3")]
  private Button _btnP10S3;
  [AccessedThroughProperty("btnP9S3")]
  private Button _btnP9S3;
  [AccessedThroughProperty("btnP8S3")]
  private Button _btnP8S3;
  [AccessedThroughProperty("btnP7S3")]
  private Button _btnP7S3;
  [AccessedThroughProperty("btnP6S3")]
  private Button _btnP6S3;
  [AccessedThroughProperty("btnP5S3")]
  private Button _btnP5S3;
  [AccessedThroughProperty("btnP4S3")]
  private Button _btnP4S3;
  [AccessedThroughProperty("btnP3S3")]
  private Button _btnP3S3;
  [AccessedThroughProperty("btnP2S3")]
  private Button _btnP2S3;
  [AccessedThroughProperty("btnP1S3")]
  private Button _btnP1S3;
  [AccessedThroughProperty("cmbP16S3")]
  private ComboBox _cmbP16S3;
  [AccessedThroughProperty("cmbP15S3")]
  private ComboBox _cmbP15S3;
  [AccessedThroughProperty("cmbP14S3")]
  private ComboBox _cmbP14S3;
  [AccessedThroughProperty("cmbP13S3")]
  private ComboBox _cmbP13S3;
  [AccessedThroughProperty("cmbP12S3")]
  private ComboBox _cmbP12S3;
  [AccessedThroughProperty("cmbP11S3")]
  private ComboBox _cmbP11S3;
  [AccessedThroughProperty("cmbP10S3")]
  private ComboBox _cmbP10S3;
  [AccessedThroughProperty("cmbP9S3")]
  private ComboBox _cmbP9S3;
  [AccessedThroughProperty("cmbP8S3")]
  private ComboBox _cmbP8S3;
  [AccessedThroughProperty("cmbP7S3")]
  private ComboBox _cmbP7S3;
  [AccessedThroughProperty("cmbP6S3")]
  private ComboBox _cmbP6S3;
  [AccessedThroughProperty("cmbP5S3")]
  private ComboBox _cmbP5S3;
  [AccessedThroughProperty("cmbP4S3")]
  private ComboBox _cmbP4S3;
  [AccessedThroughProperty("cmbP3S3")]
  private ComboBox _cmbP3S3;
  [AccessedThroughProperty("cmbP2S3")]
  private ComboBox _cmbP2S3;
  [AccessedThroughProperty("cmbP1S3")]
  private ComboBox _cmbP1S3;
  [AccessedThroughProperty("btnP16S2")]
  private Button _btnP16S2;
  [AccessedThroughProperty("btnP15S2")]
  private Button _btnP15S2;
  [AccessedThroughProperty("btnP14S2")]
  private Button _btnP14S2;
  [AccessedThroughProperty("btnP13S2")]
  private Button _btnP13S2;
  [AccessedThroughProperty("btnP12S2")]
  private Button _btnP12S2;
  [AccessedThroughProperty("btnP11S2")]
  private Button _btnP11S2;
  [AccessedThroughProperty("btnP10S2")]
  private Button _btnP10S2;
  [AccessedThroughProperty("btnP9S2")]
  private Button _btnP9S2;
  [AccessedThroughProperty("btnP8S2")]
  private Button _btnP8S2;
  [AccessedThroughProperty("btnP7S2")]
  private Button _btnP7S2;
  [AccessedThroughProperty("btnP6S2")]
  private Button _btnP6S2;
  [AccessedThroughProperty("btnP5S2")]
  private Button _btnP5S2;
  [AccessedThroughProperty("btnP4S2")]
  private Button _btnP4S2;
  [AccessedThroughProperty("btnP3S2")]
  private Button _btnP3S2;
  [AccessedThroughProperty("btnP2S2")]
  private Button _btnP2S2;
  [AccessedThroughProperty("btnP1S2")]
  private Button _btnP1S2;
  [AccessedThroughProperty("cmbP16S2")]
  private ComboBox _cmbP16S2;
  [AccessedThroughProperty("cmbP15S2")]
  private ComboBox _cmbP15S2;
  [AccessedThroughProperty("cmbP14S2")]
  private ComboBox _cmbP14S2;
  [AccessedThroughProperty("cmbP13S2")]
  private ComboBox _cmbP13S2;
  [AccessedThroughProperty("cmbP12S2")]
  private ComboBox _cmbP12S2;
  [AccessedThroughProperty("cmbP11S2")]
  private ComboBox _cmbP11S2;
  [AccessedThroughProperty("cmbP10S2")]
  private ComboBox _cmbP10S2;
  [AccessedThroughProperty("cmbP9S2")]
  private ComboBox _cmbP9S2;
  [AccessedThroughProperty("cmbP8S2")]
  private ComboBox _cmbP8S2;
  [AccessedThroughProperty("cmbP7S2")]
  private ComboBox _cmbP7S2;
  [AccessedThroughProperty("cmbP6S2")]
  private ComboBox _cmbP6S2;
  [AccessedThroughProperty("cmbP5S2")]
  private ComboBox _cmbP5S2;
  [AccessedThroughProperty("cmbP4S2")]
  private ComboBox _cmbP4S2;
  [AccessedThroughProperty("cmbP3S2")]
  private ComboBox _cmbP3S2;
  [AccessedThroughProperty("cmbP2S2")]
  private ComboBox _cmbP2S2;
  [AccessedThroughProperty("cmbP1S2")]
  private ComboBox _cmbP1S2;
  [AccessedThroughProperty("btnP16S1")]
  private Button _btnP16S1;
  [AccessedThroughProperty("btnP15S1")]
  private Button _btnP15S1;
  [AccessedThroughProperty("btnP14S1")]
  private Button _btnP14S1;
  [AccessedThroughProperty("btnP13S1")]
  private Button _btnP13S1;
  [AccessedThroughProperty("btnP12S1")]
  private Button _btnP12S1;
  [AccessedThroughProperty("btnP11S1")]
  private Button _btnP11S1;
  [AccessedThroughProperty("btnP10S1")]
  private Button _btnP10S1;
  [AccessedThroughProperty("btnP9S1")]
  private Button _btnP9S1;
  [AccessedThroughProperty("btnP8S1")]
  private Button _btnP8S1;
  [AccessedThroughProperty("btnP7S1")]
  private Button _btnP7S1;
  [AccessedThroughProperty("btnP6S1")]
  private Button _btnP6S1;
  [AccessedThroughProperty("btnP5S1")]
  private Button _btnP5S1;
  [AccessedThroughProperty("btnP4S1")]
  private Button _btnP4S1;
  [AccessedThroughProperty("btnP3S1")]
  private Button _btnP3S1;
  [AccessedThroughProperty("btnP2S1")]
  private Button _btnP2S1;
  [AccessedThroughProperty("btnP1S1")]
  private Button _btnP1S1;
  [AccessedThroughProperty("cmbP16S1")]
  private ComboBox _cmbP16S1;
  [AccessedThroughProperty("cmbP15S1")]
  private ComboBox _cmbP15S1;
  [AccessedThroughProperty("cmbP14S1")]
  private ComboBox _cmbP14S1;
  [AccessedThroughProperty("cmbP13S1")]
  private ComboBox _cmbP13S1;
  [AccessedThroughProperty("cmbP12S1")]
  private ComboBox _cmbP12S1;
  [AccessedThroughProperty("cmbP11S1")]
  private ComboBox _cmbP11S1;
  [AccessedThroughProperty("cmbP10S1")]
  private ComboBox _cmbP10S1;
  [AccessedThroughProperty("cmbP9S1")]
  private ComboBox _cmbP9S1;
  [AccessedThroughProperty("cmbP8S1")]
  private ComboBox _cmbP8S1;
  [AccessedThroughProperty("cmbP7S1")]
  private ComboBox _cmbP7S1;
  [AccessedThroughProperty("cmbP6S1")]
  private ComboBox _cmbP6S1;
  [AccessedThroughProperty("cmbP5S1")]
  private ComboBox _cmbP5S1;
  [AccessedThroughProperty("cmbP4S1")]
  private ComboBox _cmbP4S1;
  [AccessedThroughProperty("cmbP3S1")]
  private ComboBox _cmbP3S1;
  [AccessedThroughProperty("cmbP2S1")]
  private ComboBox _cmbP2S1;
  [AccessedThroughProperty("cmbP1S1")]
  private ComboBox _cmbP1S1;
  [AccessedThroughProperty("chkPort16")]
  private CheckBox _chkPort16;
  [AccessedThroughProperty("chkPort15")]
  private CheckBox _chkPort15;
  [AccessedThroughProperty("chkPort14")]
  private CheckBox _chkPort14;
  [AccessedThroughProperty("chkPort13")]
  private CheckBox _chkPort13;
  [AccessedThroughProperty("chkPort12")]
  private CheckBox _chkPort12;
  [AccessedThroughProperty("chkPort11")]
  private CheckBox _chkPort11;
  [AccessedThroughProperty("chkPort10")]
  private CheckBox _chkPort10;
  [AccessedThroughProperty("chkPort9")]
  private CheckBox _chkPort9;
  [AccessedThroughProperty("chkPort8")]
  private CheckBox _chkPort8;
  [AccessedThroughProperty("chkPort7")]
  private CheckBox _chkPort7;
  [AccessedThroughProperty("chkPort6")]
  private CheckBox _chkPort6;
  [AccessedThroughProperty("chkPort5")]
  private CheckBox _chkPort5;
  [AccessedThroughProperty("chkPort4")]
  private CheckBox _chkPort4;
  [AccessedThroughProperty("chkPort3")]
  private CheckBox _chkPort3;
  [AccessedThroughProperty("chkPort2")]
  private CheckBox _chkPort2;
  [AccessedThroughProperty("chkPort1")]
  private CheckBox _chkPort1;
  [AccessedThroughProperty("btnOk")]
  private Button _btnOk;
  [AccessedThroughProperty("lblPfno")]
  private Label _lblPfno;
  [AccessedThroughProperty("txtPdchName")]
  private TextBox _txtPdchName;
  [AccessedThroughProperty("txtPdchAddress")]
  private TextBox _txtPdchAddress;
  [AccessedThroughProperty("lblName")]
  private Label _lblName;
  [AccessedThroughProperty("lblAddress")]
  private Label _lblAddress;
  [AccessedThroughProperty("lblSharedPfno")]
  private Label _lblSharedPfno;
  [AccessedThroughProperty("Label1")]
  private Label _Label1;
  [AccessedThroughProperty("chkPDCHSharedPfNo")]
  private CheckBox _chkPDCHSharedPfNo;
  [AccessedThroughProperty("cmbPdchPfno")]
  private ComboBox _cmbPdchPfno;
  [AccessedThroughProperty("cmbPdchSharedPfno")]
  private ComboBox _cmbPdchSharedPfno;
  public static byte pdch_port_num;
  public static byte pdch_system_num;
  [AccessedThroughProperty("event_cgdb")]
  private frmNetworkCGDB _event_cgdb;
  [AccessedThroughProperty("event_agdb")]
  private frmNetworkAGDB _event_agdb;
  [AccessedThroughProperty("event_pdb")]
  private frmNetworkPDB _event_pdb;

  [DebuggerNonUserCode]
  static frmNetworkPDCH()
  {
  }

  [DebuggerNonUserCode]
  public frmNetworkPDCH()
  {
    this.Load += new EventHandler(this.frmNetworkPDCH_Load);
    frmNetworkPDCH.__ENCAddToList((object) this);
    this.InitializeComponent();
  }

  [DebuggerNonUserCode]
  private static void __ENCAddToList(object value)
  {
    lock (frmNetworkPDCH.__ENCList)
    {
      if (frmNetworkPDCH.__ENCList.Count == frmNetworkPDCH.__ENCList.Capacity)
      {
        int index1 = 0;
        int num = checked (frmNetworkPDCH.__ENCList.Count - 1);
        int index2 = 0;
        while (index2 <= num)
        {
          if (frmNetworkPDCH.__ENCList[index2].IsAlive)
          {
            if (index2 != index1)
              frmNetworkPDCH.__ENCList[index1] = frmNetworkPDCH.__ENCList[index2];
            checked { ++index1; }
          }
          checked { ++index2; }
        }
        frmNetworkPDCH.__ENCList.RemoveRange(index1, checked (frmNetworkPDCH.__ENCList.Count - index1));
        frmNetworkPDCH.__ENCList.Capacity = frmNetworkPDCH.__ENCList.Count;
      }
      frmNetworkPDCH.__ENCList.Add(new WeakReference(RuntimeHelpers.GetObjectValue(value)));
    }
  }

  [DebuggerNonUserCode]
  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  [DebuggerStepThrough]
  private void InitializeComponent()
  {
    this.btnExit = new Button();
    this.GroupBox1 = new GroupBox();
    this.btnP16S8 = new Button();
    this.btnP15S8 = new Button();
    this.btnP14S8 = new Button();
    this.btnP13S8 = new Button();
    this.btnP12S8 = new Button();
    this.btnP11S8 = new Button();
    this.btnP10S8 = new Button();
    this.btnP9S8 = new Button();
    this.btnP8S8 = new Button();
    this.btnP7S8 = new Button();
    this.btnP6S8 = new Button();
    this.btnP5S8 = new Button();
    this.btnP4S8 = new Button();
    this.btnP3S8 = new Button();
    this.btnP2S8 = new Button();
    this.btnP1S8 = new Button();
    this.cmbP16S8 = new ComboBox();
    this.cmbP15S8 = new ComboBox();
    this.cmbP14S8 = new ComboBox();
    this.cmbP13S8 = new ComboBox();
    this.cmbP12S8 = new ComboBox();
    this.cmbP11S8 = new ComboBox();
    this.cmbP10S8 = new ComboBox();
    this.cmbP9S8 = new ComboBox();
    this.cmbP8S8 = new ComboBox();
    this.cmbP7S8 = new ComboBox();
    this.cmbP6S8 = new ComboBox();
    this.cmbP5S8 = new ComboBox();
    this.cmbP4S8 = new ComboBox();
    this.cmbP3S8 = new ComboBox();
    this.cmbP2S8 = new ComboBox();
    this.cmbP1S8 = new ComboBox();
    this.btnP16S7 = new Button();
    this.btnP15S7 = new Button();
    this.btnP14S7 = new Button();
    this.btnP13S7 = new Button();
    this.btnP12S7 = new Button();
    this.btnP11S7 = new Button();
    this.btnP10S7 = new Button();
    this.btnP9S7 = new Button();
    this.btnP8S7 = new Button();
    this.btnP7S7 = new Button();
    this.btnP6S7 = new Button();
    this.btnP5S7 = new Button();
    this.btnP4S7 = new Button();
    this.btnP3S7 = new Button();
    this.btnP2S7 = new Button();
    this.btnP1S7 = new Button();
    this.cmbP16S7 = new ComboBox();
    this.cmbP15S7 = new ComboBox();
    this.cmbP14S7 = new ComboBox();
    this.cmbP13S7 = new ComboBox();
    this.cmbP12S7 = new ComboBox();
    this.cmbP11S7 = new ComboBox();
    this.cmbP10S7 = new ComboBox();
    this.cmbP9S7 = new ComboBox();
    this.cmbP8S7 = new ComboBox();
    this.cmbP7S7 = new ComboBox();
    this.cmbP6S7 = new ComboBox();
    this.cmbP5S7 = new ComboBox();
    this.cmbP4S7 = new ComboBox();
    this.cmbP3S7 = new ComboBox();
    this.cmbP2S7 = new ComboBox();
    this.cmbP1S7 = new ComboBox();
    this.btnP16S6 = new Button();
    this.btnP15S6 = new Button();
    this.btnP14S6 = new Button();
    this.btnP13S6 = new Button();
    this.btnP12S6 = new Button();
    this.btnP11S6 = new Button();
    this.btnP10S6 = new Button();
    this.btnP9S6 = new Button();
    this.btnP8S6 = new Button();
    this.btnP7S6 = new Button();
    this.btnP6S6 = new Button();
    this.btnP5S6 = new Button();
    this.btnP4S6 = new Button();
    this.btnP3S6 = new Button();
    this.btnP2S6 = new Button();
    this.btnP1S6 = new Button();
    this.cmbP16S6 = new ComboBox();
    this.cmbP15S6 = new ComboBox();
    this.cmbP14S6 = new ComboBox();
    this.cmbP13S6 = new ComboBox();
    this.cmbP12S6 = new ComboBox();
    this.cmbP11S6 = new ComboBox();
    this.cmbP10S6 = new ComboBox();
    this.cmbP9S6 = new ComboBox();
    this.cmbP8S6 = new ComboBox();
    this.cmbP7S6 = new ComboBox();
    this.cmbP6S6 = new ComboBox();
    this.cmbP5S6 = new ComboBox();
    this.cmbP4S6 = new ComboBox();
    this.cmbP3S6 = new ComboBox();
    this.cmbP2S6 = new ComboBox();
    this.cmbP1S6 = new ComboBox();
    this.btnP16S5 = new Button();
    this.btnP15S5 = new Button();
    this.btnP14S5 = new Button();
    this.btnP13S5 = new Button();
    this.btnP12S5 = new Button();
    this.btnP11S5 = new Button();
    this.btnP10S5 = new Button();
    this.btnP9S5 = new Button();
    this.btnP8S5 = new Button();
    this.btnP7S5 = new Button();
    this.btnP6S5 = new Button();
    this.btnP5S5 = new Button();
    this.btnP4S5 = new Button();
    this.btnP3S5 = new Button();
    this.btnP2S5 = new Button();
    this.btnP1S5 = new Button();
    this.cmbP16S5 = new ComboBox();
    this.cmbP15S5 = new ComboBox();
    this.cmbP14S5 = new ComboBox();
    this.cmbP13S5 = new ComboBox();
    this.cmbP12S5 = new ComboBox();
    this.cmbP11S5 = new ComboBox();
    this.cmbP10S5 = new ComboBox();
    this.cmbP9S5 = new ComboBox();
    this.cmbP8S5 = new ComboBox();
    this.cmbP7S5 = new ComboBox();
    this.cmbP6S5 = new ComboBox();
    this.cmbP5S5 = new ComboBox();
    this.cmbP4S5 = new ComboBox();
    this.cmbP3S5 = new ComboBox();
    this.cmbP2S5 = new ComboBox();
    this.cmbP1S5 = new ComboBox();
    this.btnP16S4 = new Button();
    this.btnP15S4 = new Button();
    this.btnP14S4 = new Button();
    this.btnP13S4 = new Button();
    this.btnP12S4 = new Button();
    this.btnP11S4 = new Button();
    this.btnP10S4 = new Button();
    this.btnP9S4 = new Button();
    this.btnP8S4 = new Button();
    this.btnP7S4 = new Button();
    this.btnP6S4 = new Button();
    this.btnP5S4 = new Button();
    this.btnP4S4 = new Button();
    this.btnP3S4 = new Button();
    this.btnP2S4 = new Button();
    this.btnP1S4 = new Button();
    this.cmbP16S4 = new ComboBox();
    this.cmbP15S4 = new ComboBox();
    this.cmbP14S4 = new ComboBox();
    this.cmbP13S4 = new ComboBox();
    this.cmbP12S4 = new ComboBox();
    this.cmbP11S4 = new ComboBox();
    this.cmbP10S4 = new ComboBox();
    this.cmbP9S4 = new ComboBox();
    this.cmbP8S4 = new ComboBox();
    this.cmbP7S4 = new ComboBox();
    this.cmbP6S4 = new ComboBox();
    this.cmbP5S4 = new ComboBox();
    this.cmbP4S4 = new ComboBox();
    this.cmbP3S4 = new ComboBox();
    this.cmbP2S4 = new ComboBox();
    this.cmbP1S4 = new ComboBox();
    this.btnP16S3 = new Button();
    this.btnP15S3 = new Button();
    this.btnP14S3 = new Button();
    this.btnP13S3 = new Button();
    this.btnP12S3 = new Button();
    this.btnP11S3 = new Button();
    this.btnP10S3 = new Button();
    this.btnP9S3 = new Button();
    this.btnP8S3 = new Button();
    this.btnP7S3 = new Button();
    this.btnP6S3 = new Button();
    this.btnP5S3 = new Button();
    this.btnP4S3 = new Button();
    this.btnP3S3 = new Button();
    this.btnP2S3 = new Button();
    this.btnP1S3 = new Button();
    this.cmbP16S3 = new ComboBox();
    this.cmbP15S3 = new ComboBox();
    this.cmbP14S3 = new ComboBox();
    this.cmbP13S3 = new ComboBox();
    this.cmbP12S3 = new ComboBox();
    this.cmbP11S3 = new ComboBox();
    this.cmbP10S3 = new ComboBox();
    this.cmbP9S3 = new ComboBox();
    this.cmbP8S3 = new ComboBox();
    this.cmbP7S3 = new ComboBox();
    this.cmbP6S3 = new ComboBox();
    this.cmbP5S3 = new ComboBox();
    this.cmbP4S3 = new ComboBox();
    this.cmbP3S3 = new ComboBox();
    this.cmbP2S3 = new ComboBox();
    this.cmbP1S3 = new ComboBox();
    this.btnP16S2 = new Button();
    this.btnP15S2 = new Button();
    this.btnP14S2 = new Button();
    this.btnP13S2 = new Button();
    this.btnP12S2 = new Button();
    this.btnP11S2 = new Button();
    this.btnP10S2 = new Button();
    this.btnP9S2 = new Button();
    this.btnP8S2 = new Button();
    this.btnP7S2 = new Button();
    this.btnP6S2 = new Button();
    this.btnP5S2 = new Button();
    this.btnP4S2 = new Button();
    this.btnP3S2 = new Button();
    this.btnP2S2 = new Button();
    this.btnP1S2 = new Button();
    this.cmbP16S2 = new ComboBox();
    this.cmbP15S2 = new ComboBox();
    this.cmbP14S2 = new ComboBox();
    this.cmbP13S2 = new ComboBox();
    this.cmbP12S2 = new ComboBox();
    this.cmbP11S2 = new ComboBox();
    this.cmbP10S2 = new ComboBox();
    this.cmbP9S2 = new ComboBox();
    this.cmbP8S2 = new ComboBox();
    this.cmbP7S2 = new ComboBox();
    this.cmbP6S2 = new ComboBox();
    this.cmbP5S2 = new ComboBox();
    this.cmbP4S2 = new ComboBox();
    this.cmbP3S2 = new ComboBox();
    this.cmbP2S2 = new ComboBox();
    this.cmbP1S2 = new ComboBox();
    this.btnP16S1 = new Button();
    this.btnP15S1 = new Button();
    this.btnP14S1 = new Button();
    this.btnP13S1 = new Button();
    this.btnP12S1 = new Button();
    this.btnP11S1 = new Button();
    this.btnP10S1 = new Button();
    this.btnP9S1 = new Button();
    this.btnP8S1 = new Button();
    this.btnP7S1 = new Button();
    this.btnP6S1 = new Button();
    this.btnP5S1 = new Button();
    this.btnP4S1 = new Button();
    this.btnP3S1 = new Button();
    this.btnP2S1 = new Button();
    this.btnP1S1 = new Button();
    this.cmbP16S1 = new ComboBox();
    this.cmbP15S1 = new ComboBox();
    this.cmbP14S1 = new ComboBox();
    this.cmbP13S1 = new ComboBox();
    this.cmbP12S1 = new ComboBox();
    this.cmbP11S1 = new ComboBox();
    this.cmbP10S1 = new ComboBox();
    this.cmbP9S1 = new ComboBox();
    this.cmbP8S1 = new ComboBox();
    this.cmbP7S1 = new ComboBox();
    this.cmbP6S1 = new ComboBox();
    this.cmbP5S1 = new ComboBox();
    this.cmbP4S1 = new ComboBox();
    this.cmbP3S1 = new ComboBox();
    this.cmbP2S1 = new ComboBox();
    this.cmbP1S1 = new ComboBox();
    this.chkPort16 = new CheckBox();
    this.chkPort15 = new CheckBox();
    this.chkPort14 = new CheckBox();
    this.chkPort13 = new CheckBox();
    this.chkPort12 = new CheckBox();
    this.chkPort11 = new CheckBox();
    this.chkPort10 = new CheckBox();
    this.chkPort9 = new CheckBox();
    this.chkPort8 = new CheckBox();
    this.chkPort7 = new CheckBox();
    this.chkPort6 = new CheckBox();
    this.chkPort5 = new CheckBox();
    this.chkPort4 = new CheckBox();
    this.chkPort3 = new CheckBox();
    this.chkPort2 = new CheckBox();
    this.chkPort1 = new CheckBox();
    this.btnOk = new Button();
    this.lblPfno = new Label();
    this.txtPdchName = new TextBox();
    this.txtPdchAddress = new TextBox();
    this.lblName = new Label();
    this.lblAddress = new Label();
    this.lblSharedPfno = new Label();
    this.Label1 = new Label();
    this.chkPDCHSharedPfNo = new CheckBox();
    this.cmbPdchPfno = new ComboBox();
    this.cmbPdchSharedPfno = new ComboBox();
    this.GroupBox1.SuspendLayout();
    this.SuspendLayout();
    this.btnExit.BackColor = Color.SeaShell;
    this.btnExit.DialogResult = DialogResult.Cancel;
    this.btnExit.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnExit1 = this.btnExit;
    Point point1 = new Point(647, 632);
    Point point2 = point1;
    btnExit1.Location = point2;
    this.btnExit.Name = "btnExit";
    Button btnExit2 = this.btnExit;
    Size size1 = new Size(59, 23);
    Size size2 = size1;
    btnExit2.Size = size2;
    this.btnExit.TabIndex = 159;
    this.btnExit.Text = "Exit";
    this.btnExit.UseVisualStyleBackColor = false;
    this.GroupBox1.BackColor = SystemColors.ButtonHighlight;
    this.GroupBox1.Controls.Add((Control) this.btnP16S8);
    this.GroupBox1.Controls.Add((Control) this.btnP15S8);
    this.GroupBox1.Controls.Add((Control) this.btnP14S8);
    this.GroupBox1.Controls.Add((Control) this.btnP13S8);
    this.GroupBox1.Controls.Add((Control) this.btnP12S8);
    this.GroupBox1.Controls.Add((Control) this.btnP11S8);
    this.GroupBox1.Controls.Add((Control) this.btnP10S8);
    this.GroupBox1.Controls.Add((Control) this.btnP9S8);
    this.GroupBox1.Controls.Add((Control) this.btnP8S8);
    this.GroupBox1.Controls.Add((Control) this.btnP7S8);
    this.GroupBox1.Controls.Add((Control) this.btnP6S8);
    this.GroupBox1.Controls.Add((Control) this.btnP5S8);
    this.GroupBox1.Controls.Add((Control) this.btnP4S8);
    this.GroupBox1.Controls.Add((Control) this.btnP3S8);
    this.GroupBox1.Controls.Add((Control) this.btnP2S8);
    this.GroupBox1.Controls.Add((Control) this.btnP1S8);
    this.GroupBox1.Controls.Add((Control) this.cmbP16S8);
    this.GroupBox1.Controls.Add((Control) this.cmbP15S8);
    this.GroupBox1.Controls.Add((Control) this.cmbP14S8);
    this.GroupBox1.Controls.Add((Control) this.cmbP13S8);
    this.GroupBox1.Controls.Add((Control) this.cmbP12S8);
    this.GroupBox1.Controls.Add((Control) this.cmbP11S8);
    this.GroupBox1.Controls.Add((Control) this.cmbP10S8);
    this.GroupBox1.Controls.Add((Control) this.cmbP9S8);
    this.GroupBox1.Controls.Add((Control) this.cmbP8S8);
    this.GroupBox1.Controls.Add((Control) this.cmbP7S8);
    this.GroupBox1.Controls.Add((Control) this.cmbP6S8);
    this.GroupBox1.Controls.Add((Control) this.cmbP5S8);
    this.GroupBox1.Controls.Add((Control) this.cmbP4S8);
    this.GroupBox1.Controls.Add((Control) this.cmbP3S8);
    this.GroupBox1.Controls.Add((Control) this.cmbP2S8);
    this.GroupBox1.Controls.Add((Control) this.cmbP1S8);
    this.GroupBox1.Controls.Add((Control) this.btnP16S7);
    this.GroupBox1.Controls.Add((Control) this.btnP15S7);
    this.GroupBox1.Controls.Add((Control) this.btnP14S7);
    this.GroupBox1.Controls.Add((Control) this.btnP13S7);
    this.GroupBox1.Controls.Add((Control) this.btnP12S7);
    this.GroupBox1.Controls.Add((Control) this.btnP11S7);
    this.GroupBox1.Controls.Add((Control) this.btnP10S7);
    this.GroupBox1.Controls.Add((Control) this.btnP9S7);
    this.GroupBox1.Controls.Add((Control) this.btnP8S7);
    this.GroupBox1.Controls.Add((Control) this.btnP7S7);
    this.GroupBox1.Controls.Add((Control) this.btnP6S7);
    this.GroupBox1.Controls.Add((Control) this.btnP5S7);
    this.GroupBox1.Controls.Add((Control) this.btnP4S7);
    this.GroupBox1.Controls.Add((Control) this.btnP3S7);
    this.GroupBox1.Controls.Add((Control) this.btnP2S7);
    this.GroupBox1.Controls.Add((Control) this.btnP1S7);
    this.GroupBox1.Controls.Add((Control) this.cmbP16S7);
    this.GroupBox1.Controls.Add((Control) this.cmbP15S7);
    this.GroupBox1.Controls.Add((Control) this.cmbP14S7);
    this.GroupBox1.Controls.Add((Control) this.cmbP13S7);
    this.GroupBox1.Controls.Add((Control) this.cmbP12S7);
    this.GroupBox1.Controls.Add((Control) this.cmbP11S7);
    this.GroupBox1.Controls.Add((Control) this.cmbP10S7);
    this.GroupBox1.Controls.Add((Control) this.cmbP9S7);
    this.GroupBox1.Controls.Add((Control) this.cmbP8S7);
    this.GroupBox1.Controls.Add((Control) this.cmbP7S7);
    this.GroupBox1.Controls.Add((Control) this.cmbP6S7);
    this.GroupBox1.Controls.Add((Control) this.cmbP5S7);
    this.GroupBox1.Controls.Add((Control) this.cmbP4S7);
    this.GroupBox1.Controls.Add((Control) this.cmbP3S7);
    this.GroupBox1.Controls.Add((Control) this.cmbP2S7);
    this.GroupBox1.Controls.Add((Control) this.cmbP1S7);
    this.GroupBox1.Controls.Add((Control) this.btnP16S6);
    this.GroupBox1.Controls.Add((Control) this.btnP15S6);
    this.GroupBox1.Controls.Add((Control) this.btnP14S6);
    this.GroupBox1.Controls.Add((Control) this.btnP13S6);
    this.GroupBox1.Controls.Add((Control) this.btnP12S6);
    this.GroupBox1.Controls.Add((Control) this.btnP11S6);
    this.GroupBox1.Controls.Add((Control) this.btnP10S6);
    this.GroupBox1.Controls.Add((Control) this.btnP9S6);
    this.GroupBox1.Controls.Add((Control) this.btnP8S6);
    this.GroupBox1.Controls.Add((Control) this.btnP7S6);
    this.GroupBox1.Controls.Add((Control) this.btnP6S6);
    this.GroupBox1.Controls.Add((Control) this.btnP5S6);
    this.GroupBox1.Controls.Add((Control) this.btnP4S6);
    this.GroupBox1.Controls.Add((Control) this.btnP3S6);
    this.GroupBox1.Controls.Add((Control) this.btnP2S6);
    this.GroupBox1.Controls.Add((Control) this.btnP1S6);
    this.GroupBox1.Controls.Add((Control) this.cmbP16S6);
    this.GroupBox1.Controls.Add((Control) this.cmbP15S6);
    this.GroupBox1.Controls.Add((Control) this.cmbP14S6);
    this.GroupBox1.Controls.Add((Control) this.cmbP13S6);
    this.GroupBox1.Controls.Add((Control) this.cmbP12S6);
    this.GroupBox1.Controls.Add((Control) this.cmbP11S6);
    this.GroupBox1.Controls.Add((Control) this.cmbP10S6);
    this.GroupBox1.Controls.Add((Control) this.cmbP9S6);
    this.GroupBox1.Controls.Add((Control) this.cmbP8S6);
    this.GroupBox1.Controls.Add((Control) this.cmbP7S6);
    this.GroupBox1.Controls.Add((Control) this.cmbP6S6);
    this.GroupBox1.Controls.Add((Control) this.cmbP5S6);
    this.GroupBox1.Controls.Add((Control) this.cmbP4S6);
    this.GroupBox1.Controls.Add((Control) this.cmbP3S6);
    this.GroupBox1.Controls.Add((Control) this.cmbP2S6);
    this.GroupBox1.Controls.Add((Control) this.cmbP1S6);
    this.GroupBox1.Controls.Add((Control) this.btnP16S5);
    this.GroupBox1.Controls.Add((Control) this.btnP15S5);
    this.GroupBox1.Controls.Add((Control) this.btnP14S5);
    this.GroupBox1.Controls.Add((Control) this.btnP13S5);
    this.GroupBox1.Controls.Add((Control) this.btnP12S5);
    this.GroupBox1.Controls.Add((Control) this.btnP11S5);
    this.GroupBox1.Controls.Add((Control) this.btnP10S5);
    this.GroupBox1.Controls.Add((Control) this.btnP9S5);
    this.GroupBox1.Controls.Add((Control) this.btnP8S5);
    this.GroupBox1.Controls.Add((Control) this.btnP7S5);
    this.GroupBox1.Controls.Add((Control) this.btnP6S5);
    this.GroupBox1.Controls.Add((Control) this.btnP5S5);
    this.GroupBox1.Controls.Add((Control) this.btnP4S5);
    this.GroupBox1.Controls.Add((Control) this.btnP3S5);
    this.GroupBox1.Controls.Add((Control) this.btnP2S5);
    this.GroupBox1.Controls.Add((Control) this.btnP1S5);
    this.GroupBox1.Controls.Add((Control) this.cmbP16S5);
    this.GroupBox1.Controls.Add((Control) this.cmbP15S5);
    this.GroupBox1.Controls.Add((Control) this.cmbP14S5);
    this.GroupBox1.Controls.Add((Control) this.cmbP13S5);
    this.GroupBox1.Controls.Add((Control) this.cmbP12S5);
    this.GroupBox1.Controls.Add((Control) this.cmbP11S5);
    this.GroupBox1.Controls.Add((Control) this.cmbP10S5);
    this.GroupBox1.Controls.Add((Control) this.cmbP9S5);
    this.GroupBox1.Controls.Add((Control) this.cmbP8S5);
    this.GroupBox1.Controls.Add((Control) this.cmbP7S5);
    this.GroupBox1.Controls.Add((Control) this.cmbP6S5);
    this.GroupBox1.Controls.Add((Control) this.cmbP5S5);
    this.GroupBox1.Controls.Add((Control) this.cmbP4S5);
    this.GroupBox1.Controls.Add((Control) this.cmbP3S5);
    this.GroupBox1.Controls.Add((Control) this.cmbP2S5);
    this.GroupBox1.Controls.Add((Control) this.cmbP1S5);
    this.GroupBox1.Controls.Add((Control) this.btnP16S4);
    this.GroupBox1.Controls.Add((Control) this.btnP15S4);
    this.GroupBox1.Controls.Add((Control) this.btnP14S4);
    this.GroupBox1.Controls.Add((Control) this.btnP13S4);
    this.GroupBox1.Controls.Add((Control) this.btnP12S4);
    this.GroupBox1.Controls.Add((Control) this.btnP11S4);
    this.GroupBox1.Controls.Add((Control) this.btnP10S4);
    this.GroupBox1.Controls.Add((Control) this.btnP9S4);
    this.GroupBox1.Controls.Add((Control) this.btnP8S4);
    this.GroupBox1.Controls.Add((Control) this.btnP7S4);
    this.GroupBox1.Controls.Add((Control) this.btnP6S4);
    this.GroupBox1.Controls.Add((Control) this.btnP5S4);
    this.GroupBox1.Controls.Add((Control) this.btnP4S4);
    this.GroupBox1.Controls.Add((Control) this.btnP3S4);
    this.GroupBox1.Controls.Add((Control) this.btnP2S4);
    this.GroupBox1.Controls.Add((Control) this.btnP1S4);
    this.GroupBox1.Controls.Add((Control) this.cmbP16S4);
    this.GroupBox1.Controls.Add((Control) this.cmbP15S4);
    this.GroupBox1.Controls.Add((Control) this.cmbP14S4);
    this.GroupBox1.Controls.Add((Control) this.cmbP13S4);
    this.GroupBox1.Controls.Add((Control) this.cmbP12S4);
    this.GroupBox1.Controls.Add((Control) this.cmbP11S4);
    this.GroupBox1.Controls.Add((Control) this.cmbP10S4);
    this.GroupBox1.Controls.Add((Control) this.cmbP9S4);
    this.GroupBox1.Controls.Add((Control) this.cmbP8S4);
    this.GroupBox1.Controls.Add((Control) this.cmbP7S4);
    this.GroupBox1.Controls.Add((Control) this.cmbP6S4);
    this.GroupBox1.Controls.Add((Control) this.cmbP5S4);
    this.GroupBox1.Controls.Add((Control) this.cmbP4S4);
    this.GroupBox1.Controls.Add((Control) this.cmbP3S4);
    this.GroupBox1.Controls.Add((Control) this.cmbP2S4);
    this.GroupBox1.Controls.Add((Control) this.cmbP1S4);
    this.GroupBox1.Controls.Add((Control) this.btnP16S3);
    this.GroupBox1.Controls.Add((Control) this.btnP15S3);
    this.GroupBox1.Controls.Add((Control) this.btnP14S3);
    this.GroupBox1.Controls.Add((Control) this.btnP13S3);
    this.GroupBox1.Controls.Add((Control) this.btnP12S3);
    this.GroupBox1.Controls.Add((Control) this.btnP11S3);
    this.GroupBox1.Controls.Add((Control) this.btnP10S3);
    this.GroupBox1.Controls.Add((Control) this.btnP9S3);
    this.GroupBox1.Controls.Add((Control) this.btnP8S3);
    this.GroupBox1.Controls.Add((Control) this.btnP7S3);
    this.GroupBox1.Controls.Add((Control) this.btnP6S3);
    this.GroupBox1.Controls.Add((Control) this.btnP5S3);
    this.GroupBox1.Controls.Add((Control) this.btnP4S3);
    this.GroupBox1.Controls.Add((Control) this.btnP3S3);
    this.GroupBox1.Controls.Add((Control) this.btnP2S3);
    this.GroupBox1.Controls.Add((Control) this.btnP1S3);
    this.GroupBox1.Controls.Add((Control) this.cmbP16S3);
    this.GroupBox1.Controls.Add((Control) this.cmbP15S3);
    this.GroupBox1.Controls.Add((Control) this.cmbP14S3);
    this.GroupBox1.Controls.Add((Control) this.cmbP13S3);
    this.GroupBox1.Controls.Add((Control) this.cmbP12S3);
    this.GroupBox1.Controls.Add((Control) this.cmbP11S3);
    this.GroupBox1.Controls.Add((Control) this.cmbP10S3);
    this.GroupBox1.Controls.Add((Control) this.cmbP9S3);
    this.GroupBox1.Controls.Add((Control) this.cmbP8S3);
    this.GroupBox1.Controls.Add((Control) this.cmbP7S3);
    this.GroupBox1.Controls.Add((Control) this.cmbP6S3);
    this.GroupBox1.Controls.Add((Control) this.cmbP5S3);
    this.GroupBox1.Controls.Add((Control) this.cmbP4S3);
    this.GroupBox1.Controls.Add((Control) this.cmbP3S3);
    this.GroupBox1.Controls.Add((Control) this.cmbP2S3);
    this.GroupBox1.Controls.Add((Control) this.cmbP1S3);
    this.GroupBox1.Controls.Add((Control) this.btnP16S2);
    this.GroupBox1.Controls.Add((Control) this.btnP15S2);
    this.GroupBox1.Controls.Add((Control) this.btnP14S2);
    this.GroupBox1.Controls.Add((Control) this.btnP13S2);
    this.GroupBox1.Controls.Add((Control) this.btnP12S2);
    this.GroupBox1.Controls.Add((Control) this.btnP11S2);
    this.GroupBox1.Controls.Add((Control) this.btnP10S2);
    this.GroupBox1.Controls.Add((Control) this.btnP9S2);
    this.GroupBox1.Controls.Add((Control) this.btnP8S2);
    this.GroupBox1.Controls.Add((Control) this.btnP7S2);
    this.GroupBox1.Controls.Add((Control) this.btnP6S2);
    this.GroupBox1.Controls.Add((Control) this.btnP5S2);
    this.GroupBox1.Controls.Add((Control) this.btnP4S2);
    this.GroupBox1.Controls.Add((Control) this.btnP3S2);
    this.GroupBox1.Controls.Add((Control) this.btnP2S2);
    this.GroupBox1.Controls.Add((Control) this.btnP1S2);
    this.GroupBox1.Controls.Add((Control) this.cmbP16S2);
    this.GroupBox1.Controls.Add((Control) this.cmbP15S2);
    this.GroupBox1.Controls.Add((Control) this.cmbP14S2);
    this.GroupBox1.Controls.Add((Control) this.cmbP13S2);
    this.GroupBox1.Controls.Add((Control) this.cmbP12S2);
    this.GroupBox1.Controls.Add((Control) this.cmbP11S2);
    this.GroupBox1.Controls.Add((Control) this.cmbP10S2);
    this.GroupBox1.Controls.Add((Control) this.cmbP9S2);
    this.GroupBox1.Controls.Add((Control) this.cmbP8S2);
    this.GroupBox1.Controls.Add((Control) this.cmbP7S2);
    this.GroupBox1.Controls.Add((Control) this.cmbP6S2);
    this.GroupBox1.Controls.Add((Control) this.cmbP5S2);
    this.GroupBox1.Controls.Add((Control) this.cmbP4S2);
    this.GroupBox1.Controls.Add((Control) this.cmbP3S2);
    this.GroupBox1.Controls.Add((Control) this.cmbP2S2);
    this.GroupBox1.Controls.Add((Control) this.cmbP1S2);
    this.GroupBox1.Controls.Add((Control) this.btnP16S1);
    this.GroupBox1.Controls.Add((Control) this.btnP15S1);
    this.GroupBox1.Controls.Add((Control) this.btnP14S1);
    this.GroupBox1.Controls.Add((Control) this.btnP13S1);
    this.GroupBox1.Controls.Add((Control) this.btnP12S1);
    this.GroupBox1.Controls.Add((Control) this.btnP11S1);
    this.GroupBox1.Controls.Add((Control) this.btnP10S1);
    this.GroupBox1.Controls.Add((Control) this.btnP9S1);
    this.GroupBox1.Controls.Add((Control) this.btnP8S1);
    this.GroupBox1.Controls.Add((Control) this.btnP7S1);
    this.GroupBox1.Controls.Add((Control) this.btnP6S1);
    this.GroupBox1.Controls.Add((Control) this.btnP5S1);
    this.GroupBox1.Controls.Add((Control) this.btnP4S1);
    this.GroupBox1.Controls.Add((Control) this.btnP3S1);
    this.GroupBox1.Controls.Add((Control) this.btnP2S1);
    this.GroupBox1.Controls.Add((Control) this.btnP1S1);
    this.GroupBox1.Controls.Add((Control) this.cmbP16S1);
    this.GroupBox1.Controls.Add((Control) this.cmbP15S1);
    this.GroupBox1.Controls.Add((Control) this.cmbP14S1);
    this.GroupBox1.Controls.Add((Control) this.cmbP13S1);
    this.GroupBox1.Controls.Add((Control) this.cmbP12S1);
    this.GroupBox1.Controls.Add((Control) this.cmbP11S1);
    this.GroupBox1.Controls.Add((Control) this.cmbP10S1);
    this.GroupBox1.Controls.Add((Control) this.cmbP9S1);
    this.GroupBox1.Controls.Add((Control) this.cmbP8S1);
    this.GroupBox1.Controls.Add((Control) this.cmbP7S1);
    this.GroupBox1.Controls.Add((Control) this.cmbP6S1);
    this.GroupBox1.Controls.Add((Control) this.cmbP5S1);
    this.GroupBox1.Controls.Add((Control) this.cmbP4S1);
    this.GroupBox1.Controls.Add((Control) this.cmbP3S1);
    this.GroupBox1.Controls.Add((Control) this.cmbP2S1);
    this.GroupBox1.Controls.Add((Control) this.cmbP1S1);
    this.GroupBox1.Controls.Add((Control) this.chkPort16);
    this.GroupBox1.Controls.Add((Control) this.chkPort15);
    this.GroupBox1.Controls.Add((Control) this.chkPort14);
    this.GroupBox1.Controls.Add((Control) this.chkPort13);
    this.GroupBox1.Controls.Add((Control) this.chkPort12);
    this.GroupBox1.Controls.Add((Control) this.chkPort11);
    this.GroupBox1.Controls.Add((Control) this.chkPort10);
    this.GroupBox1.Controls.Add((Control) this.chkPort9);
    this.GroupBox1.Controls.Add((Control) this.chkPort8);
    this.GroupBox1.Controls.Add((Control) this.chkPort7);
    this.GroupBox1.Controls.Add((Control) this.chkPort6);
    this.GroupBox1.Controls.Add((Control) this.chkPort5);
    this.GroupBox1.Controls.Add((Control) this.chkPort4);
    this.GroupBox1.Controls.Add((Control) this.chkPort3);
    this.GroupBox1.Controls.Add((Control) this.chkPort2);
    this.GroupBox1.Controls.Add((Control) this.chkPort1);
    this.GroupBox1.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.GroupBox1.ForeColor = Color.Navy;
    GroupBox groupBox1_1 = this.GroupBox1;
    point1 = new Point(12, 149);
    Point point3 = point1;
    groupBox1_1.Location = point3;
    this.GroupBox1.Name = "GroupBox1";
    GroupBox groupBox1_2 = this.GroupBox1;
    size1 = new Size(1201, 468);
    Size size3 = size1;
    groupBox1_2.Size = size3;
    this.GroupBox1.TabIndex = 157;
    this.GroupBox1.TabStop = false;
    this.GroupBox1.Text = "PDCH Port";
    this.btnP16S8.BackColor = Color.Tomato;
    Button btnP16S8_1 = this.btnP16S8;
    point1 = new Point(1159, 424);
    Point point4 = point1;
    btnP16S8_1.Location = point4;
    this.btnP16S8.Name = "btnP16S8";
    Button btnP16S8_2 = this.btnP16S8;
    size1 = new Size(20, 17);
    Size size4 = size1;
    btnP16S8_2.Size = size4;
    this.btnP16S8.TabIndex = 274;
    this.btnP16S8.Text = " ";
    this.btnP16S8.UseVisualStyleBackColor = false;
    this.btnP16S8.Visible = false;
    this.btnP15S8.BackColor = Color.Tomato;
    Button btnP15S8_1 = this.btnP15S8;
    point1 = new Point(1159, 397);
    Point point5 = point1;
    btnP15S8_1.Location = point5;
    this.btnP15S8.Name = "btnP15S8";
    Button btnP15S8_2 = this.btnP15S8;
    size1 = new Size(19, 17);
    Size size5 = size1;
    btnP15S8_2.Size = size5;
    this.btnP15S8.TabIndex = 257;
    this.btnP15S8.Text = " ";
    this.btnP15S8.UseVisualStyleBackColor = false;
    this.btnP15S8.Visible = false;
    this.btnP14S8.BackColor = Color.Tomato;
    Button btnP14S8_1 = this.btnP14S8;
    point1 = new Point(1158, 372);
    Point point6 = point1;
    btnP14S8_1.Location = point6;
    this.btnP14S8.Name = "btnP14S8";
    Button btnP14S8_2 = this.btnP14S8;
    size1 = new Size(20, 17);
    Size size6 = size1;
    btnP14S8_2.Size = size6;
    this.btnP14S8.TabIndex = 240 /*0xF0*/;
    this.btnP14S8.Text = " ";
    this.btnP14S8.UseVisualStyleBackColor = false;
    this.btnP14S8.Visible = false;
    this.btnP13S8.BackColor = Color.Tomato;
    Button btnP13S8_1 = this.btnP13S8;
    point1 = new Point(1159, 343);
    Point point7 = point1;
    btnP13S8_1.Location = point7;
    this.btnP13S8.Name = "btnP13S8";
    Button btnP13S8_2 = this.btnP13S8;
    size1 = new Size(19, 17);
    Size size7 = size1;
    btnP13S8_2.Size = size7;
    this.btnP13S8.TabIndex = 223;
    this.btnP13S8.Text = " ";
    this.btnP13S8.UseVisualStyleBackColor = false;
    this.btnP13S8.Visible = false;
    this.btnP12S8.BackColor = Color.Tomato;
    Button btnP12S8_1 = this.btnP12S8;
    point1 = new Point(1159, 314);
    Point point8 = point1;
    btnP12S8_1.Location = point8;
    this.btnP12S8.Name = "btnP12S8";
    Button btnP12S8_2 = this.btnP12S8;
    size1 = new Size(19, 19);
    Size size8 = size1;
    btnP12S8_2.Size = size8;
    this.btnP12S8.TabIndex = 206;
    this.btnP12S8.Text = " ";
    this.btnP12S8.UseVisualStyleBackColor = false;
    this.btnP12S8.Visible = false;
    this.btnP11S8.BackColor = Color.Tomato;
    Button btnP11S8_1 = this.btnP11S8;
    point1 = new Point(1159, 289);
    Point point9 = point1;
    btnP11S8_1.Location = point9;
    this.btnP11S8.Name = "btnP11S8";
    Button btnP11S8_2 = this.btnP11S8;
    size1 = new Size(19, 17);
    Size size9 = size1;
    btnP11S8_2.Size = size9;
    this.btnP11S8.TabIndex = 189;
    this.btnP11S8.Text = " ";
    this.btnP11S8.UseVisualStyleBackColor = false;
    this.btnP11S8.Visible = false;
    this.btnP10S8.BackColor = Color.Tomato;
    Button btnP10S8_1 = this.btnP10S8;
    point1 = new Point(1159, 262);
    Point point10 = point1;
    btnP10S8_1.Location = point10;
    this.btnP10S8.Name = "btnP10S8";
    Button btnP10S8_2 = this.btnP10S8;
    size1 = new Size(19, 17);
    Size size10 = size1;
    btnP10S8_2.Size = size10;
    this.btnP10S8.TabIndex = 173;
    this.btnP10S8.Text = " ";
    this.btnP10S8.UseVisualStyleBackColor = false;
    this.btnP10S8.Visible = false;
    this.btnP9S8.BackColor = Color.Tomato;
    Button btnP9S8_1 = this.btnP9S8;
    point1 = new Point(1159, 235);
    Point point11 = point1;
    btnP9S8_1.Location = point11;
    this.btnP9S8.Name = "btnP9S8";
    Button btnP9S8_2 = this.btnP9S8;
    size1 = new Size(19, 17);
    Size size11 = size1;
    btnP9S8_2.Size = size11;
    this.btnP9S8.TabIndex = 156;
    this.btnP9S8.Text = " ";
    this.btnP9S8.UseVisualStyleBackColor = false;
    this.btnP9S8.Visible = false;
    this.btnP8S8.BackColor = Color.Tomato;
    Button btnP8S8_1 = this.btnP8S8;
    point1 = new Point(1159, 210);
    Point point12 = point1;
    btnP8S8_1.Location = point12;
    this.btnP8S8.Name = "btnP8S8";
    Button btnP8S8_2 = this.btnP8S8;
    size1 = new Size(17, 18);
    Size size12 = size1;
    btnP8S8_2.Size = size12;
    this.btnP8S8.TabIndex = 139;
    this.btnP8S8.Text = " ";
    this.btnP8S8.UseVisualStyleBackColor = false;
    this.btnP8S8.Visible = false;
    this.btnP7S8.BackColor = Color.Tomato;
    Button btnP7S8_1 = this.btnP7S8;
    point1 = new Point(1159, 181);
    Point point13 = point1;
    btnP7S8_1.Location = point13;
    this.btnP7S8.Name = "btnP7S8";
    Button btnP7S8_2 = this.btnP7S8;
    size1 = new Size(17, 17);
    Size size13 = size1;
    btnP7S8_2.Size = size13;
    this.btnP7S8.TabIndex = 122;
    this.btnP7S8.Text = " ";
    this.btnP7S8.UseVisualStyleBackColor = false;
    this.btnP7S8.Visible = false;
    this.btnP6S8.BackColor = Color.Tomato;
    Button btnP6S8_1 = this.btnP6S8;
    point1 = new Point(1159, 152);
    Point point14 = point1;
    btnP6S8_1.Location = point14;
    this.btnP6S8.Name = "btnP6S8";
    Button btnP6S8_2 = this.btnP6S8;
    size1 = new Size(17, 17);
    Size size14 = size1;
    btnP6S8_2.Size = size14;
    this.btnP6S8.TabIndex = 105;
    this.btnP6S8.Text = " ";
    this.btnP6S8.UseVisualStyleBackColor = false;
    this.btnP6S8.Visible = false;
    this.btnP5S8.BackColor = Color.Tomato;
    Button btnP5S8_1 = this.btnP5S8;
    point1 = new Point(1159, (int) sbyte.MaxValue);
    Point point15 = point1;
    btnP5S8_1.Location = point15;
    this.btnP5S8.Name = "btnP5S8";
    Button btnP5S8_2 = this.btnP5S8;
    size1 = new Size(17, 17);
    Size size15 = size1;
    btnP5S8_2.Size = size15;
    this.btnP5S8.TabIndex = 88;
    this.btnP5S8.Text = " ";
    this.btnP5S8.UseVisualStyleBackColor = false;
    this.btnP5S8.Visible = false;
    this.btnP4S8.BackColor = Color.Tomato;
    Button btnP4S8_1 = this.btnP4S8;
    point1 = new Point(1159, 100);
    Point point16 = point1;
    btnP4S8_1.Location = point16;
    this.btnP4S8.Name = "btnP4S8";
    Button btnP4S8_2 = this.btnP4S8;
    size1 = new Size(17, 19);
    Size size16 = size1;
    btnP4S8_2.Size = size16;
    this.btnP4S8.TabIndex = 71;
    this.btnP4S8.Text = " ";
    this.btnP4S8.UseVisualStyleBackColor = false;
    this.btnP4S8.Visible = false;
    this.btnP3S8.BackColor = Color.Tomato;
    Button btnP3S8_1 = this.btnP3S8;
    point1 = new Point(1159, 73);
    Point point17 = point1;
    btnP3S8_1.Location = point17;
    this.btnP3S8.Name = "btnP3S8";
    Button btnP3S8_2 = this.btnP3S8;
    size1 = new Size(17, 19);
    Size size17 = size1;
    btnP3S8_2.Size = size17;
    this.btnP3S8.TabIndex = 54;
    this.btnP3S8.Text = " ";
    this.btnP3S8.UseVisualStyleBackColor = false;
    this.btnP3S8.Visible = false;
    this.btnP2S8.BackColor = Color.Tomato;
    Button btnP2S8_1 = this.btnP2S8;
    point1 = new Point(1159, 44);
    Point point18 = point1;
    btnP2S8_1.Location = point18;
    this.btnP2S8.Name = "btnP2S8";
    Button btnP2S8_2 = this.btnP2S8;
    size1 = new Size(17, 19);
    Size size18 = size1;
    btnP2S8_2.Size = size18;
    this.btnP2S8.TabIndex = 37;
    this.btnP2S8.Text = " ";
    this.btnP2S8.UseVisualStyleBackColor = false;
    this.btnP2S8.Visible = false;
    this.btnP1S8.BackColor = Color.Tomato;
    Button btnP1S8_1 = this.btnP1S8;
    point1 = new Point(1159, 17);
    Point point19 = point1;
    btnP1S8_1.Location = point19;
    this.btnP1S8.Name = "btnP1S8";
    Button btnP1S8_2 = this.btnP1S8;
    size1 = new Size(17, 19);
    Size size19 = size1;
    btnP1S8_2.Size = size19;
    this.btnP1S8.TabIndex = 20;
    this.btnP1S8.UseVisualStyleBackColor = false;
    this.btnP1S8.Visible = false;
    this.cmbP16S8.BackColor = SystemColors.ButtonFace;
    this.cmbP16S8.FormattingEnabled = true;
    this.cmbP16S8.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP16S8_1 = this.cmbP16S8;
    point1 = new Point(1050, 424);
    Point point20 = point1;
    cmbP16S8_1.Location = point20;
    this.cmbP16S8.Name = "cmbP16S8";
    ComboBox cmbP16S8_2 = this.cmbP16S8;
    size1 = new Size(91, 21);
    Size size20 = size1;
    cmbP16S8_2.Size = size20;
    this.cmbP16S8.TabIndex = 273;
    this.cmbP16S8.Visible = false;
    this.cmbP15S8.BackColor = SystemColors.ButtonFace;
    this.cmbP15S8.FormattingEnabled = true;
    this.cmbP15S8.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP15S8_1 = this.cmbP15S8;
    point1 = new Point(1050, 397);
    Point point21 = point1;
    cmbP15S8_1.Location = point21;
    this.cmbP15S8.Name = "cmbP15S8";
    ComboBox cmbP15S8_2 = this.cmbP15S8;
    size1 = new Size(91, 21);
    Size size21 = size1;
    cmbP15S8_2.Size = size21;
    this.cmbP15S8.TabIndex = 256 /*0x0100*/;
    this.cmbP15S8.Visible = false;
    this.cmbP14S8.BackColor = SystemColors.ButtonFace;
    this.cmbP14S8.FormattingEnabled = true;
    this.cmbP14S8.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP14S8_1 = this.cmbP14S8;
    point1 = new Point(1050, 370);
    Point point22 = point1;
    cmbP14S8_1.Location = point22;
    this.cmbP14S8.Name = "cmbP14S8";
    ComboBox cmbP14S8_2 = this.cmbP14S8;
    size1 = new Size(91, 21);
    Size size22 = size1;
    cmbP14S8_2.Size = size22;
    this.cmbP14S8.TabIndex = 239;
    this.cmbP14S8.Visible = false;
    this.cmbP13S8.BackColor = SystemColors.ButtonFace;
    this.cmbP13S8.FormattingEnabled = true;
    this.cmbP13S8.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP13S8_1 = this.cmbP13S8;
    point1 = new Point(1050, 343);
    Point point23 = point1;
    cmbP13S8_1.Location = point23;
    this.cmbP13S8.Name = "cmbP13S8";
    ComboBox cmbP13S8_2 = this.cmbP13S8;
    size1 = new Size(91, 21);
    Size size23 = size1;
    cmbP13S8_2.Size = size23;
    this.cmbP13S8.TabIndex = 223;
    this.cmbP13S8.Visible = false;
    this.cmbP12S8.BackColor = SystemColors.ButtonFace;
    this.cmbP12S8.FormattingEnabled = true;
    this.cmbP12S8.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP12S8_1 = this.cmbP12S8;
    point1 = new Point(1050, 316);
    Point point24 = point1;
    cmbP12S8_1.Location = point24;
    this.cmbP12S8.Name = "cmbP12S8";
    ComboBox cmbP12S8_2 = this.cmbP12S8;
    size1 = new Size(91, 21);
    Size size24 = size1;
    cmbP12S8_2.Size = size24;
    this.cmbP12S8.TabIndex = 206;
    this.cmbP12S8.Visible = false;
    this.cmbP11S8.BackColor = SystemColors.ButtonFace;
    this.cmbP11S8.FormattingEnabled = true;
    this.cmbP11S8.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP11S8_1 = this.cmbP11S8;
    point1 = new Point(1050, 289);
    Point point25 = point1;
    cmbP11S8_1.Location = point25;
    this.cmbP11S8.Name = "cmbP11S8";
    ComboBox cmbP11S8_2 = this.cmbP11S8;
    size1 = new Size(91, 21);
    Size size25 = size1;
    cmbP11S8_2.Size = size25;
    this.cmbP11S8.TabIndex = 189;
    this.cmbP11S8.Visible = false;
    this.cmbP10S8.BackColor = SystemColors.ButtonFace;
    this.cmbP10S8.FormattingEnabled = true;
    this.cmbP10S8.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP10S8_1 = this.cmbP10S8;
    point1 = new Point(1050, 263);
    Point point26 = point1;
    cmbP10S8_1.Location = point26;
    this.cmbP10S8.Name = "cmbP10S8";
    ComboBox cmbP10S8_2 = this.cmbP10S8;
    size1 = new Size(91, 21);
    Size size26 = size1;
    cmbP10S8_2.Size = size26;
    this.cmbP10S8.TabIndex = 172;
    this.cmbP10S8.Visible = false;
    this.cmbP9S8.BackColor = SystemColors.ButtonFace;
    this.cmbP9S8.FormattingEnabled = true;
    this.cmbP9S8.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP9S8_1 = this.cmbP9S8;
    point1 = new Point(1050, 235);
    Point point27 = point1;
    cmbP9S8_1.Location = point27;
    this.cmbP9S8.Name = "cmbP9S8";
    ComboBox cmbP9S8_2 = this.cmbP9S8;
    size1 = new Size(91, 21);
    Size size27 = size1;
    cmbP9S8_2.Size = size27;
    this.cmbP9S8.TabIndex = 155;
    this.cmbP9S8.Visible = false;
    this.cmbP8S8.BackColor = SystemColors.ButtonFace;
    this.cmbP8S8.FormattingEnabled = true;
    this.cmbP8S8.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP8S8_1 = this.cmbP8S8;
    point1 = new Point(1050, 208 /*0xD0*/);
    Point point28 = point1;
    cmbP8S8_1.Location = point28;
    this.cmbP8S8.Name = "cmbP8S8";
    ComboBox cmbP8S8_2 = this.cmbP8S8;
    size1 = new Size(91, 21);
    Size size28 = size1;
    cmbP8S8_2.Size = size28;
    this.cmbP8S8.TabIndex = 138;
    this.cmbP8S8.Visible = false;
    this.cmbP7S8.BackColor = SystemColors.ButtonFace;
    this.cmbP7S8.FormattingEnabled = true;
    this.cmbP7S8.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP7S8_1 = this.cmbP7S8;
    point1 = new Point(1050, 181);
    Point point29 = point1;
    cmbP7S8_1.Location = point29;
    this.cmbP7S8.Name = "cmbP7S8";
    ComboBox cmbP7S8_2 = this.cmbP7S8;
    size1 = new Size(91, 21);
    Size size29 = size1;
    cmbP7S8_2.Size = size29;
    this.cmbP7S8.TabIndex = 122;
    this.cmbP7S8.Visible = false;
    this.cmbP6S8.BackColor = SystemColors.ButtonFace;
    this.cmbP6S8.FormattingEnabled = true;
    this.cmbP6S8.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP6S8_1 = this.cmbP6S8;
    point1 = new Point(1050, 154);
    Point point30 = point1;
    cmbP6S8_1.Location = point30;
    this.cmbP6S8.Name = "cmbP6S8";
    ComboBox cmbP6S8_2 = this.cmbP6S8;
    size1 = new Size(91, 21);
    Size size30 = size1;
    cmbP6S8_2.Size = size30;
    this.cmbP6S8.TabIndex = 105;
    this.cmbP6S8.Visible = false;
    this.cmbP5S8.BackColor = SystemColors.ButtonFace;
    this.cmbP5S8.FormattingEnabled = true;
    this.cmbP5S8.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP5S8_1 = this.cmbP5S8;
    point1 = new Point(1050, (int) sbyte.MaxValue);
    Point point31 = point1;
    cmbP5S8_1.Location = point31;
    this.cmbP5S8.Name = "cmbP5S8";
    ComboBox cmbP5S8_2 = this.cmbP5S8;
    size1 = new Size(91, 21);
    Size size31 = size1;
    cmbP5S8_2.Size = size31;
    this.cmbP5S8.TabIndex = 87;
    this.cmbP5S8.Visible = false;
    this.cmbP4S8.BackColor = SystemColors.ButtonFace;
    this.cmbP4S8.FormattingEnabled = true;
    this.cmbP4S8.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP4S8_1 = this.cmbP4S8;
    point1 = new Point(1050, 100);
    Point point32 = point1;
    cmbP4S8_1.Location = point32;
    this.cmbP4S8.Name = "cmbP4S8";
    ComboBox cmbP4S8_2 = this.cmbP4S8;
    size1 = new Size(91, 21);
    Size size32 = size1;
    cmbP4S8_2.Size = size32;
    this.cmbP4S8.TabIndex = 70;
    this.cmbP4S8.Visible = false;
    this.cmbP3S8.BackColor = SystemColors.ButtonFace;
    this.cmbP3S8.FormattingEnabled = true;
    this.cmbP3S8.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP3S8_1 = this.cmbP3S8;
    point1 = new Point(1050, 73);
    Point point33 = point1;
    cmbP3S8_1.Location = point33;
    this.cmbP3S8.Name = "cmbP3S8";
    ComboBox cmbP3S8_2 = this.cmbP3S8;
    size1 = new Size(91, 21);
    Size size33 = size1;
    cmbP3S8_2.Size = size33;
    this.cmbP3S8.TabIndex = 53;
    this.cmbP3S8.Visible = false;
    this.cmbP2S8.BackColor = SystemColors.ButtonFace;
    this.cmbP2S8.FormattingEnabled = true;
    this.cmbP2S8.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP2S8_1 = this.cmbP2S8;
    point1 = new Point(1050, 46);
    Point point34 = point1;
    cmbP2S8_1.Location = point34;
    this.cmbP2S8.Name = "cmbP2S8";
    ComboBox cmbP2S8_2 = this.cmbP2S8;
    size1 = new Size(91, 21);
    Size size34 = size1;
    cmbP2S8_2.Size = size34;
    this.cmbP2S8.TabIndex = 36;
    this.cmbP2S8.Visible = false;
    this.cmbP1S8.BackColor = SystemColors.ButtonFace;
    this.cmbP1S8.FormattingEnabled = true;
    this.cmbP1S8.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP1S8_1 = this.cmbP1S8;
    point1 = new Point(1050, 19);
    Point point35 = point1;
    cmbP1S8_1.Location = point35;
    this.cmbP1S8.Name = "cmbP1S8";
    ComboBox cmbP1S8_2 = this.cmbP1S8;
    size1 = new Size(91, 21);
    Size size35 = size1;
    cmbP1S8_2.Size = size35;
    this.cmbP1S8.TabIndex = 19;
    this.cmbP1S8.Visible = false;
    this.btnP16S7.BackColor = Color.Tomato;
    Button btnP16S7_1 = this.btnP16S7;
    point1 = new Point(1013, 425);
    Point point36 = point1;
    btnP16S7_1.Location = point36;
    this.btnP16S7.Name = "btnP16S7";
    Button btnP16S7_2 = this.btnP16S7;
    size1 = new Size(20, 17);
    Size size36 = size1;
    btnP16S7_2.Size = size36;
    this.btnP16S7.TabIndex = 272;
    this.btnP16S7.Text = " ";
    this.btnP16S7.UseVisualStyleBackColor = false;
    this.btnP16S7.Visible = false;
    this.btnP15S7.BackColor = Color.Tomato;
    Button btnP15S7_1 = this.btnP15S7;
    point1 = new Point(1013, 398);
    Point point37 = point1;
    btnP15S7_1.Location = point37;
    this.btnP15S7.Name = "btnP15S7";
    Button btnP15S7_2 = this.btnP15S7;
    size1 = new Size(19, 17);
    Size size37 = size1;
    btnP15S7_2.Size = size37;
    this.btnP15S7.TabIndex = (int) byte.MaxValue;
    this.btnP15S7.Text = " ";
    this.btnP15S7.UseVisualStyleBackColor = false;
    this.btnP15S7.Visible = false;
    this.btnP14S7.BackColor = Color.Tomato;
    Button btnP14S7_1 = this.btnP14S7;
    point1 = new Point(1012, 373);
    Point point38 = point1;
    btnP14S7_1.Location = point38;
    this.btnP14S7.Name = "btnP14S7";
    Button btnP14S7_2 = this.btnP14S7;
    size1 = new Size(20, 17);
    Size size38 = size1;
    btnP14S7_2.Size = size38;
    this.btnP14S7.TabIndex = 238;
    this.btnP14S7.Text = " ";
    this.btnP14S7.UseVisualStyleBackColor = false;
    this.btnP14S7.Visible = false;
    this.btnP13S7.BackColor = Color.Tomato;
    Button btnP13S7_1 = this.btnP13S7;
    point1 = new Point(1013, 344);
    Point point39 = point1;
    btnP13S7_1.Location = point39;
    this.btnP13S7.Name = "btnP13S7";
    Button btnP13S7_2 = this.btnP13S7;
    size1 = new Size(19, 17);
    Size size39 = size1;
    btnP13S7_2.Size = size39;
    this.btnP13S7.TabIndex = 221;
    this.btnP13S7.Text = " ";
    this.btnP13S7.UseVisualStyleBackColor = false;
    this.btnP13S7.Visible = false;
    this.btnP12S7.BackColor = Color.Tomato;
    Button btnP12S7_1 = this.btnP12S7;
    point1 = new Point(1013, 315);
    Point point40 = point1;
    btnP12S7_1.Location = point40;
    this.btnP12S7.Name = "btnP12S7";
    Button btnP12S7_2 = this.btnP12S7;
    size1 = new Size(19, 19);
    Size size40 = size1;
    btnP12S7_2.Size = size40;
    this.btnP12S7.TabIndex = 204;
    this.btnP12S7.Text = " ";
    this.btnP12S7.UseVisualStyleBackColor = false;
    this.btnP12S7.Visible = false;
    this.btnP11S7.BackColor = Color.Tomato;
    Button btnP11S7_1 = this.btnP11S7;
    point1 = new Point(1013, 290);
    Point point41 = point1;
    btnP11S7_1.Location = point41;
    this.btnP11S7.Name = "btnP11S7";
    Button btnP11S7_2 = this.btnP11S7;
    size1 = new Size(19, 17);
    Size size41 = size1;
    btnP11S7_2.Size = size41;
    this.btnP11S7.TabIndex = 187;
    this.btnP11S7.Text = " ";
    this.btnP11S7.UseVisualStyleBackColor = false;
    this.btnP11S7.Visible = false;
    this.btnP10S7.BackColor = Color.Tomato;
    Button btnP10S7_1 = this.btnP10S7;
    point1 = new Point(1013, 263);
    Point point42 = point1;
    btnP10S7_1.Location = point42;
    this.btnP10S7.Name = "btnP10S7";
    Button btnP10S7_2 = this.btnP10S7;
    size1 = new Size(19, 17);
    Size size42 = size1;
    btnP10S7_2.Size = size42;
    this.btnP10S7.TabIndex = 171;
    this.btnP10S7.Text = " ";
    this.btnP10S7.UseVisualStyleBackColor = false;
    this.btnP10S7.Visible = false;
    this.btnP9S7.BackColor = Color.Tomato;
    Button btnP9S7_1 = this.btnP9S7;
    point1 = new Point(1013, 236);
    Point point43 = point1;
    btnP9S7_1.Location = point43;
    this.btnP9S7.Name = "btnP9S7";
    Button btnP9S7_2 = this.btnP9S7;
    size1 = new Size(19, 17);
    Size size43 = size1;
    btnP9S7_2.Size = size43;
    this.btnP9S7.TabIndex = 154;
    this.btnP9S7.Text = " ";
    this.btnP9S7.UseVisualStyleBackColor = false;
    this.btnP9S7.Visible = false;
    this.btnP8S7.BackColor = Color.Tomato;
    Button btnP8S7_1 = this.btnP8S7;
    point1 = new Point(1013, 211);
    Point point44 = point1;
    btnP8S7_1.Location = point44;
    this.btnP8S7.Name = "btnP8S7";
    Button btnP8S7_2 = this.btnP8S7;
    size1 = new Size(17, 18);
    Size size44 = size1;
    btnP8S7_2.Size = size44;
    this.btnP8S7.TabIndex = 137;
    this.btnP8S7.Text = " ";
    this.btnP8S7.UseVisualStyleBackColor = false;
    this.btnP8S7.Visible = false;
    this.btnP7S7.BackColor = Color.Tomato;
    Button btnP7S7_1 = this.btnP7S7;
    point1 = new Point(1013, 182);
    Point point45 = point1;
    btnP7S7_1.Location = point45;
    this.btnP7S7.Name = "btnP7S7";
    Button btnP7S7_2 = this.btnP7S7;
    size1 = new Size(17, 17);
    Size size45 = size1;
    btnP7S7_2.Size = size45;
    this.btnP7S7.TabIndex = 120;
    this.btnP7S7.Text = " ";
    this.btnP7S7.UseVisualStyleBackColor = false;
    this.btnP7S7.Visible = false;
    this.btnP6S7.BackColor = Color.Tomato;
    Button btnP6S7_1 = this.btnP6S7;
    point1 = new Point(1013, 153);
    Point point46 = point1;
    btnP6S7_1.Location = point46;
    this.btnP6S7.Name = "btnP6S7";
    Button btnP6S7_2 = this.btnP6S7;
    size1 = new Size(17, 17);
    Size size46 = size1;
    btnP6S7_2.Size = size46;
    this.btnP6S7.TabIndex = 103;
    this.btnP6S7.Text = " ";
    this.btnP6S7.UseVisualStyleBackColor = false;
    this.btnP6S7.Visible = false;
    this.btnP5S7.BackColor = Color.Tomato;
    Button btnP5S7_1 = this.btnP5S7;
    point1 = new Point(1013, 128 /*0x80*/);
    Point point47 = point1;
    btnP5S7_1.Location = point47;
    this.btnP5S7.Name = "btnP5S7";
    Button btnP5S7_2 = this.btnP5S7;
    size1 = new Size(17, 17);
    Size size47 = size1;
    btnP5S7_2.Size = size47;
    this.btnP5S7.TabIndex = 86;
    this.btnP5S7.Text = " ";
    this.btnP5S7.UseVisualStyleBackColor = false;
    this.btnP5S7.Visible = false;
    this.btnP4S7.BackColor = Color.Tomato;
    Button btnP4S7_1 = this.btnP4S7;
    point1 = new Point(1013, 101);
    Point point48 = point1;
    btnP4S7_1.Location = point48;
    this.btnP4S7.Name = "btnP4S7";
    Button btnP4S7_2 = this.btnP4S7;
    size1 = new Size(17, 19);
    Size size48 = size1;
    btnP4S7_2.Size = size48;
    this.btnP4S7.TabIndex = 69;
    this.btnP4S7.Text = " ";
    this.btnP4S7.UseVisualStyleBackColor = false;
    this.btnP4S7.Visible = false;
    this.btnP3S7.BackColor = Color.Tomato;
    Button btnP3S7_1 = this.btnP3S7;
    point1 = new Point(1013, 74);
    Point point49 = point1;
    btnP3S7_1.Location = point49;
    this.btnP3S7.Name = "btnP3S7";
    Button btnP3S7_2 = this.btnP3S7;
    size1 = new Size(17, 19);
    Size size49 = size1;
    btnP3S7_2.Size = size49;
    this.btnP3S7.TabIndex = 52;
    this.btnP3S7.Text = " ";
    this.btnP3S7.UseVisualStyleBackColor = false;
    this.btnP3S7.Visible = false;
    this.btnP2S7.BackColor = Color.Tomato;
    Button btnP2S7_1 = this.btnP2S7;
    point1 = new Point(1013, 45);
    Point point50 = point1;
    btnP2S7_1.Location = point50;
    this.btnP2S7.Name = "btnP2S7";
    Button btnP2S7_2 = this.btnP2S7;
    size1 = new Size(17, 19);
    Size size50 = size1;
    btnP2S7_2.Size = size50;
    this.btnP2S7.TabIndex = 35;
    this.btnP2S7.Text = " ";
    this.btnP2S7.UseVisualStyleBackColor = false;
    this.btnP2S7.Visible = false;
    this.btnP1S7.BackColor = Color.Tomato;
    Button btnP1S7_1 = this.btnP1S7;
    point1 = new Point(1013, 18);
    Point point51 = point1;
    btnP1S7_1.Location = point51;
    this.btnP1S7.Name = "btnP1S7";
    Button btnP1S7_2 = this.btnP1S7;
    size1 = new Size(17, 19);
    Size size51 = size1;
    btnP1S7_2.Size = size51;
    this.btnP1S7.TabIndex = 18;
    this.btnP1S7.UseVisualStyleBackColor = false;
    this.btnP1S7.Visible = false;
    this.cmbP16S7.BackColor = SystemColors.ButtonFace;
    this.cmbP16S7.FormattingEnabled = true;
    this.cmbP16S7.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP16S7_1 = this.cmbP16S7;
    point1 = new Point(914, 421);
    Point point52 = point1;
    cmbP16S7_1.Location = point52;
    this.cmbP16S7.Name = "cmbP16S7";
    ComboBox cmbP16S7_2 = this.cmbP16S7;
    size1 = new Size(91, 21);
    Size size52 = size1;
    cmbP16S7_2.Size = size52;
    this.cmbP16S7.TabIndex = 271;
    this.cmbP16S7.Visible = false;
    this.cmbP15S7.BackColor = SystemColors.ButtonFace;
    this.cmbP15S7.FormattingEnabled = true;
    this.cmbP15S7.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP15S7_1 = this.cmbP15S7;
    point1 = new Point(914, 394);
    Point point53 = point1;
    cmbP15S7_1.Location = point53;
    this.cmbP15S7.Name = "cmbP15S7";
    ComboBox cmbP15S7_2 = this.cmbP15S7;
    size1 = new Size(91, 21);
    Size size53 = size1;
    cmbP15S7_2.Size = size53;
    this.cmbP15S7.TabIndex = 254;
    this.cmbP15S7.Visible = false;
    this.cmbP14S7.BackColor = SystemColors.ButtonFace;
    this.cmbP14S7.FormattingEnabled = true;
    this.cmbP14S7.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP14S7_1 = this.cmbP14S7;
    point1 = new Point(914, 367);
    Point point54 = point1;
    cmbP14S7_1.Location = point54;
    this.cmbP14S7.Name = "cmbP14S7";
    ComboBox cmbP14S7_2 = this.cmbP14S7;
    size1 = new Size(91, 21);
    Size size54 = size1;
    cmbP14S7_2.Size = size54;
    this.cmbP14S7.TabIndex = 237;
    this.cmbP14S7.Visible = false;
    this.cmbP13S7.BackColor = SystemColors.ButtonFace;
    this.cmbP13S7.FormattingEnabled = true;
    this.cmbP13S7.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP13S7_1 = this.cmbP13S7;
    point1 = new Point(914, 340);
    Point point55 = point1;
    cmbP13S7_1.Location = point55;
    this.cmbP13S7.Name = "cmbP13S7";
    ComboBox cmbP13S7_2 = this.cmbP13S7;
    size1 = new Size(91, 21);
    Size size55 = size1;
    cmbP13S7_2.Size = size55;
    this.cmbP13S7.TabIndex = 221;
    this.cmbP13S7.Visible = false;
    this.cmbP12S7.BackColor = SystemColors.ButtonFace;
    this.cmbP12S7.FormattingEnabled = true;
    this.cmbP12S7.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP12S7_1 = this.cmbP12S7;
    point1 = new Point(914, 313);
    Point point56 = point1;
    cmbP12S7_1.Location = point56;
    this.cmbP12S7.Name = "cmbP12S7";
    ComboBox cmbP12S7_2 = this.cmbP12S7;
    size1 = new Size(91, 21);
    Size size56 = size1;
    cmbP12S7_2.Size = size56;
    this.cmbP12S7.TabIndex = 204;
    this.cmbP12S7.Visible = false;
    this.cmbP11S7.BackColor = SystemColors.ButtonFace;
    this.cmbP11S7.FormattingEnabled = true;
    this.cmbP11S7.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP11S7_1 = this.cmbP11S7;
    point1 = new Point(914, 286);
    Point point57 = point1;
    cmbP11S7_1.Location = point57;
    this.cmbP11S7.Name = "cmbP11S7";
    ComboBox cmbP11S7_2 = this.cmbP11S7;
    size1 = new Size(91, 21);
    Size size57 = size1;
    cmbP11S7_2.Size = size57;
    this.cmbP11S7.TabIndex = 187;
    this.cmbP11S7.Visible = false;
    this.cmbP10S7.BackColor = SystemColors.ButtonFace;
    this.cmbP10S7.FormattingEnabled = true;
    this.cmbP10S7.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP10S7_1 = this.cmbP10S7;
    point1 = new Point(914, 259);
    Point point58 = point1;
    cmbP10S7_1.Location = point58;
    this.cmbP10S7.Name = "cmbP10S7";
    ComboBox cmbP10S7_2 = this.cmbP10S7;
    size1 = new Size(91, 21);
    Size size58 = size1;
    cmbP10S7_2.Size = size58;
    this.cmbP10S7.TabIndex = 170;
    this.cmbP10S7.Visible = false;
    this.cmbP9S7.BackColor = SystemColors.ButtonFace;
    this.cmbP9S7.FormattingEnabled = true;
    this.cmbP9S7.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP9S7_1 = this.cmbP9S7;
    point1 = new Point(914, 232);
    Point point59 = point1;
    cmbP9S7_1.Location = point59;
    this.cmbP9S7.Name = "cmbP9S7";
    ComboBox cmbP9S7_2 = this.cmbP9S7;
    size1 = new Size(91, 21);
    Size size59 = size1;
    cmbP9S7_2.Size = size59;
    this.cmbP9S7.TabIndex = 153;
    this.cmbP9S7.Visible = false;
    this.cmbP8S7.BackColor = SystemColors.ButtonFace;
    this.cmbP8S7.FormattingEnabled = true;
    this.cmbP8S7.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP8S7_1 = this.cmbP8S7;
    point1 = new Point(914, 205);
    Point point60 = point1;
    cmbP8S7_1.Location = point60;
    this.cmbP8S7.Name = "cmbP8S7";
    ComboBox cmbP8S7_2 = this.cmbP8S7;
    size1 = new Size(91, 21);
    Size size60 = size1;
    cmbP8S7_2.Size = size60;
    this.cmbP8S7.TabIndex = 136;
    this.cmbP8S7.Visible = false;
    this.cmbP7S7.BackColor = SystemColors.ButtonFace;
    this.cmbP7S7.FormattingEnabled = true;
    this.cmbP7S7.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP7S7_1 = this.cmbP7S7;
    point1 = new Point(914, 178);
    Point point61 = point1;
    cmbP7S7_1.Location = point61;
    this.cmbP7S7.Name = "cmbP7S7";
    ComboBox cmbP7S7_2 = this.cmbP7S7;
    size1 = new Size(91, 21);
    Size size61 = size1;
    cmbP7S7_2.Size = size61;
    this.cmbP7S7.TabIndex = 120;
    this.cmbP7S7.Visible = false;
    this.cmbP6S7.BackColor = SystemColors.ButtonFace;
    this.cmbP6S7.FormattingEnabled = true;
    this.cmbP6S7.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP6S7_1 = this.cmbP6S7;
    point1 = new Point(914, 151);
    Point point62 = point1;
    cmbP6S7_1.Location = point62;
    this.cmbP6S7.Name = "cmbP6S7";
    ComboBox cmbP6S7_2 = this.cmbP6S7;
    size1 = new Size(91, 21);
    Size size62 = size1;
    cmbP6S7_2.Size = size62;
    this.cmbP6S7.TabIndex = 103;
    this.cmbP6S7.Visible = false;
    this.cmbP5S7.BackColor = SystemColors.ButtonFace;
    this.cmbP5S7.FormattingEnabled = true;
    this.cmbP5S7.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP5S7_1 = this.cmbP5S7;
    point1 = new Point(914, 124);
    Point point63 = point1;
    cmbP5S7_1.Location = point63;
    this.cmbP5S7.Name = "cmbP5S7";
    ComboBox cmbP5S7_2 = this.cmbP5S7;
    size1 = new Size(91, 21);
    Size size63 = size1;
    cmbP5S7_2.Size = size63;
    this.cmbP5S7.TabIndex = 85;
    this.cmbP5S7.Visible = false;
    this.cmbP4S7.BackColor = SystemColors.ButtonFace;
    this.cmbP4S7.FormattingEnabled = true;
    this.cmbP4S7.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP4S7_1 = this.cmbP4S7;
    point1 = new Point(914, 97);
    Point point64 = point1;
    cmbP4S7_1.Location = point64;
    this.cmbP4S7.Name = "cmbP4S7";
    ComboBox cmbP4S7_2 = this.cmbP4S7;
    size1 = new Size(91, 21);
    Size size64 = size1;
    cmbP4S7_2.Size = size64;
    this.cmbP4S7.TabIndex = 68;
    this.cmbP4S7.Visible = false;
    this.cmbP3S7.BackColor = SystemColors.ButtonFace;
    this.cmbP3S7.FormattingEnabled = true;
    this.cmbP3S7.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP3S7_1 = this.cmbP3S7;
    point1 = new Point(914, 70);
    Point point65 = point1;
    cmbP3S7_1.Location = point65;
    this.cmbP3S7.Name = "cmbP3S7";
    ComboBox cmbP3S7_2 = this.cmbP3S7;
    size1 = new Size(91, 21);
    Size size65 = size1;
    cmbP3S7_2.Size = size65;
    this.cmbP3S7.TabIndex = 51;
    this.cmbP3S7.Visible = false;
    this.cmbP2S7.BackColor = SystemColors.ButtonFace;
    this.cmbP2S7.FormattingEnabled = true;
    this.cmbP2S7.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP2S7_1 = this.cmbP2S7;
    point1 = new Point(914, 43);
    Point point66 = point1;
    cmbP2S7_1.Location = point66;
    this.cmbP2S7.Name = "cmbP2S7";
    ComboBox cmbP2S7_2 = this.cmbP2S7;
    size1 = new Size(91, 21);
    Size size66 = size1;
    cmbP2S7_2.Size = size66;
    this.cmbP2S7.TabIndex = 34;
    this.cmbP2S7.Visible = false;
    this.cmbP1S7.BackColor = SystemColors.ButtonFace;
    this.cmbP1S7.FormattingEnabled = true;
    this.cmbP1S7.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP1S7_1 = this.cmbP1S7;
    point1 = new Point(914, 16 /*0x10*/);
    Point point67 = point1;
    cmbP1S7_1.Location = point67;
    this.cmbP1S7.Name = "cmbP1S7";
    ComboBox cmbP1S7_2 = this.cmbP1S7;
    size1 = new Size(91, 21);
    Size size67 = size1;
    cmbP1S7_2.Size = size67;
    this.cmbP1S7.TabIndex = 17;
    this.cmbP1S7.Visible = false;
    this.btnP16S6.BackColor = Color.Tomato;
    Button btnP16S6_1 = this.btnP16S6;
    point1 = new Point(875, 425);
    Point point68 = point1;
    btnP16S6_1.Location = point68;
    this.btnP16S6.Name = "btnP16S6";
    Button btnP16S6_2 = this.btnP16S6;
    size1 = new Size(20, 17);
    Size size68 = size1;
    btnP16S6_2.Size = size68;
    this.btnP16S6.TabIndex = 270;
    this.btnP16S6.Text = " ";
    this.btnP16S6.UseVisualStyleBackColor = false;
    this.btnP16S6.Visible = false;
    this.btnP15S6.BackColor = Color.Tomato;
    Button btnP15S6_1 = this.btnP15S6;
    point1 = new Point(875, 398);
    Point point69 = point1;
    btnP15S6_1.Location = point69;
    this.btnP15S6.Name = "btnP15S6";
    Button btnP15S6_2 = this.btnP15S6;
    size1 = new Size(19, 17);
    Size size69 = size1;
    btnP15S6_2.Size = size69;
    this.btnP15S6.TabIndex = 253;
    this.btnP15S6.Text = " ";
    this.btnP15S6.UseVisualStyleBackColor = false;
    this.btnP15S6.Visible = false;
    this.btnP14S6.BackColor = Color.Tomato;
    Button btnP14S6_1 = this.btnP14S6;
    point1 = new Point(874, 373);
    Point point70 = point1;
    btnP14S6_1.Location = point70;
    this.btnP14S6.Name = "btnP14S6";
    Button btnP14S6_2 = this.btnP14S6;
    size1 = new Size(20, 17);
    Size size70 = size1;
    btnP14S6_2.Size = size70;
    this.btnP14S6.TabIndex = 236;
    this.btnP14S6.Text = " ";
    this.btnP14S6.UseVisualStyleBackColor = false;
    this.btnP14S6.Visible = false;
    this.btnP13S6.BackColor = Color.Tomato;
    Button btnP13S6_1 = this.btnP13S6;
    point1 = new Point(875, 344);
    Point point71 = point1;
    btnP13S6_1.Location = point71;
    this.btnP13S6.Name = "btnP13S6";
    Button btnP13S6_2 = this.btnP13S6;
    size1 = new Size(19, 17);
    Size size71 = size1;
    btnP13S6_2.Size = size71;
    this.btnP13S6.TabIndex = 219;
    this.btnP13S6.Text = " ";
    this.btnP13S6.UseVisualStyleBackColor = false;
    this.btnP13S6.Visible = false;
    this.btnP12S6.BackColor = Color.Tomato;
    Button btnP12S6_1 = this.btnP12S6;
    point1 = new Point(875, 315);
    Point point72 = point1;
    btnP12S6_1.Location = point72;
    this.btnP12S6.Name = "btnP12S6";
    Button btnP12S6_2 = this.btnP12S6;
    size1 = new Size(19, 19);
    Size size72 = size1;
    btnP12S6_2.Size = size72;
    this.btnP12S6.TabIndex = 202;
    this.btnP12S6.Text = " ";
    this.btnP12S6.UseVisualStyleBackColor = false;
    this.btnP12S6.Visible = false;
    this.btnP11S6.BackColor = Color.Tomato;
    Button btnP11S6_1 = this.btnP11S6;
    point1 = new Point(875, 290);
    Point point73 = point1;
    btnP11S6_1.Location = point73;
    this.btnP11S6.Name = "btnP11S6";
    Button btnP11S6_2 = this.btnP11S6;
    size1 = new Size(19, 17);
    Size size73 = size1;
    btnP11S6_2.Size = size73;
    this.btnP11S6.TabIndex = 185;
    this.btnP11S6.Text = " ";
    this.btnP11S6.UseVisualStyleBackColor = false;
    this.btnP11S6.Visible = false;
    this.btnP10S6.BackColor = Color.Tomato;
    Button btnP10S6_1 = this.btnP10S6;
    point1 = new Point(875, 263);
    Point point74 = point1;
    btnP10S6_1.Location = point74;
    this.btnP10S6.Name = "btnP10S6";
    Button btnP10S6_2 = this.btnP10S6;
    size1 = new Size(19, 17);
    Size size74 = size1;
    btnP10S6_2.Size = size74;
    this.btnP10S6.TabIndex = 169;
    this.btnP10S6.Text = " ";
    this.btnP10S6.UseVisualStyleBackColor = false;
    this.btnP10S6.Visible = false;
    this.btnP9S6.BackColor = Color.Tomato;
    Button btnP9S6_1 = this.btnP9S6;
    point1 = new Point(875, 236);
    Point point75 = point1;
    btnP9S6_1.Location = point75;
    this.btnP9S6.Name = "btnP9S6";
    Button btnP9S6_2 = this.btnP9S6;
    size1 = new Size(19, 17);
    Size size75 = size1;
    btnP9S6_2.Size = size75;
    this.btnP9S6.TabIndex = 152;
    this.btnP9S6.Text = " ";
    this.btnP9S6.UseVisualStyleBackColor = false;
    this.btnP9S6.Visible = false;
    this.btnP8S6.BackColor = Color.Tomato;
    Button btnP8S6_1 = this.btnP8S6;
    point1 = new Point(875, 211);
    Point point76 = point1;
    btnP8S6_1.Location = point76;
    this.btnP8S6.Name = "btnP8S6";
    Button btnP8S6_2 = this.btnP8S6;
    size1 = new Size(17, 18);
    Size size76 = size1;
    btnP8S6_2.Size = size76;
    this.btnP8S6.TabIndex = 135;
    this.btnP8S6.Text = " ";
    this.btnP8S6.UseVisualStyleBackColor = false;
    this.btnP8S6.Visible = false;
    this.btnP7S6.BackColor = Color.Tomato;
    Button btnP7S6_1 = this.btnP7S6;
    point1 = new Point(875, 182);
    Point point77 = point1;
    btnP7S6_1.Location = point77;
    this.btnP7S6.Name = "btnP7S6";
    Button btnP7S6_2 = this.btnP7S6;
    size1 = new Size(17, 17);
    Size size77 = size1;
    btnP7S6_2.Size = size77;
    this.btnP7S6.TabIndex = 118;
    this.btnP7S6.Text = " ";
    this.btnP7S6.UseVisualStyleBackColor = false;
    this.btnP7S6.Visible = false;
    this.btnP6S6.BackColor = Color.Tomato;
    Button btnP6S6_1 = this.btnP6S6;
    point1 = new Point(875, 153);
    Point point78 = point1;
    btnP6S6_1.Location = point78;
    this.btnP6S6.Name = "btnP6S6";
    Button btnP6S6_2 = this.btnP6S6;
    size1 = new Size(17, 17);
    Size size78 = size1;
    btnP6S6_2.Size = size78;
    this.btnP6S6.TabIndex = 101;
    this.btnP6S6.Text = " ";
    this.btnP6S6.UseVisualStyleBackColor = false;
    this.btnP6S6.Visible = false;
    this.btnP5S6.BackColor = Color.Tomato;
    Button btnP5S6_1 = this.btnP5S6;
    point1 = new Point(875, 128 /*0x80*/);
    Point point79 = point1;
    btnP5S6_1.Location = point79;
    this.btnP5S6.Name = "btnP5S6";
    Button btnP5S6_2 = this.btnP5S6;
    size1 = new Size(17, 17);
    Size size79 = size1;
    btnP5S6_2.Size = size79;
    this.btnP5S6.TabIndex = 84;
    this.btnP5S6.Text = " ";
    this.btnP5S6.UseVisualStyleBackColor = false;
    this.btnP5S6.Visible = false;
    this.btnP4S6.BackColor = Color.Tomato;
    Button btnP4S6_1 = this.btnP4S6;
    point1 = new Point(875, 101);
    Point point80 = point1;
    btnP4S6_1.Location = point80;
    this.btnP4S6.Name = "btnP4S6";
    Button btnP4S6_2 = this.btnP4S6;
    size1 = new Size(17, 19);
    Size size80 = size1;
    btnP4S6_2.Size = size80;
    this.btnP4S6.TabIndex = 67;
    this.btnP4S6.Text = " ";
    this.btnP4S6.UseVisualStyleBackColor = false;
    this.btnP4S6.Visible = false;
    this.btnP3S6.BackColor = Color.Tomato;
    Button btnP3S6_1 = this.btnP3S6;
    point1 = new Point(875, 74);
    Point point81 = point1;
    btnP3S6_1.Location = point81;
    this.btnP3S6.Name = "btnP3S6";
    Button btnP3S6_2 = this.btnP3S6;
    size1 = new Size(17, 19);
    Size size81 = size1;
    btnP3S6_2.Size = size81;
    this.btnP3S6.TabIndex = 50;
    this.btnP3S6.Text = " ";
    this.btnP3S6.UseVisualStyleBackColor = false;
    this.btnP3S6.Visible = false;
    this.btnP2S6.BackColor = Color.Tomato;
    Button btnP2S6_1 = this.btnP2S6;
    point1 = new Point(875, 45);
    Point point82 = point1;
    btnP2S6_1.Location = point82;
    this.btnP2S6.Name = "btnP2S6";
    Button btnP2S6_2 = this.btnP2S6;
    size1 = new Size(17, 19);
    Size size82 = size1;
    btnP2S6_2.Size = size82;
    this.btnP2S6.TabIndex = 33;
    this.btnP2S6.Text = " ";
    this.btnP2S6.UseVisualStyleBackColor = false;
    this.btnP2S6.Visible = false;
    this.btnP1S6.BackColor = Color.Tomato;
    Button btnP1S6_1 = this.btnP1S6;
    point1 = new Point(875, 18);
    Point point83 = point1;
    btnP1S6_1.Location = point83;
    this.btnP1S6.Name = "btnP1S6";
    Button btnP1S6_2 = this.btnP1S6;
    size1 = new Size(17, 19);
    Size size83 = size1;
    btnP1S6_2.Size = size83;
    this.btnP1S6.TabIndex = 16 /*0x10*/;
    this.btnP1S6.UseVisualStyleBackColor = false;
    this.btnP1S6.Visible = false;
    this.cmbP16S6.BackColor = SystemColors.ButtonFace;
    this.cmbP16S6.FormattingEnabled = true;
    this.cmbP16S6.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP16S6_1 = this.cmbP16S6;
    point1 = new Point(776, 422);
    Point point84 = point1;
    cmbP16S6_1.Location = point84;
    this.cmbP16S6.Name = "cmbP16S6";
    ComboBox cmbP16S6_2 = this.cmbP16S6;
    size1 = new Size(91, 21);
    Size size84 = size1;
    cmbP16S6_2.Size = size84;
    this.cmbP16S6.TabIndex = 269;
    this.cmbP16S6.Visible = false;
    this.cmbP15S6.BackColor = SystemColors.ButtonFace;
    this.cmbP15S6.FormattingEnabled = true;
    this.cmbP15S6.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP15S6_1 = this.cmbP15S6;
    point1 = new Point(776, 395);
    Point point85 = point1;
    cmbP15S6_1.Location = point85;
    this.cmbP15S6.Name = "cmbP15S6";
    ComboBox cmbP15S6_2 = this.cmbP15S6;
    size1 = new Size(91, 21);
    Size size85 = size1;
    cmbP15S6_2.Size = size85;
    this.cmbP15S6.TabIndex = 252;
    this.cmbP15S6.Visible = false;
    this.cmbP14S6.BackColor = SystemColors.ButtonFace;
    this.cmbP14S6.FormattingEnabled = true;
    this.cmbP14S6.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP14S6_1 = this.cmbP14S6;
    point1 = new Point(776, 368);
    Point point86 = point1;
    cmbP14S6_1.Location = point86;
    this.cmbP14S6.Name = "cmbP14S6";
    ComboBox cmbP14S6_2 = this.cmbP14S6;
    size1 = new Size(91, 21);
    Size size86 = size1;
    cmbP14S6_2.Size = size86;
    this.cmbP14S6.TabIndex = 235;
    this.cmbP14S6.Visible = false;
    this.cmbP13S6.BackColor = SystemColors.ButtonFace;
    this.cmbP13S6.FormattingEnabled = true;
    this.cmbP13S6.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP13S6_1 = this.cmbP13S6;
    point1 = new Point(776, 341);
    Point point87 = point1;
    cmbP13S6_1.Location = point87;
    this.cmbP13S6.Name = "cmbP13S6";
    ComboBox cmbP13S6_2 = this.cmbP13S6;
    size1 = new Size(91, 21);
    Size size87 = size1;
    cmbP13S6_2.Size = size87;
    this.cmbP13S6.TabIndex = 219;
    this.cmbP13S6.Visible = false;
    this.cmbP12S6.BackColor = SystemColors.ButtonFace;
    this.cmbP12S6.FormattingEnabled = true;
    this.cmbP12S6.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP12S6_1 = this.cmbP12S6;
    point1 = new Point(776, 314);
    Point point88 = point1;
    cmbP12S6_1.Location = point88;
    this.cmbP12S6.Name = "cmbP12S6";
    ComboBox cmbP12S6_2 = this.cmbP12S6;
    size1 = new Size(91, 21);
    Size size88 = size1;
    cmbP12S6_2.Size = size88;
    this.cmbP12S6.TabIndex = 202;
    this.cmbP12S6.Visible = false;
    this.cmbP11S6.BackColor = SystemColors.ButtonFace;
    this.cmbP11S6.FormattingEnabled = true;
    this.cmbP11S6.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP11S6_1 = this.cmbP11S6;
    point1 = new Point(776, 287);
    Point point89 = point1;
    cmbP11S6_1.Location = point89;
    this.cmbP11S6.Name = "cmbP11S6";
    ComboBox cmbP11S6_2 = this.cmbP11S6;
    size1 = new Size(91, 21);
    Size size89 = size1;
    cmbP11S6_2.Size = size89;
    this.cmbP11S6.TabIndex = 185;
    this.cmbP11S6.Visible = false;
    this.cmbP10S6.BackColor = SystemColors.ButtonFace;
    this.cmbP10S6.FormattingEnabled = true;
    this.cmbP10S6.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP10S6_1 = this.cmbP10S6;
    point1 = new Point(776, 260);
    Point point90 = point1;
    cmbP10S6_1.Location = point90;
    this.cmbP10S6.Name = "cmbP10S6";
    ComboBox cmbP10S6_2 = this.cmbP10S6;
    size1 = new Size(91, 21);
    Size size90 = size1;
    cmbP10S6_2.Size = size90;
    this.cmbP10S6.TabIndex = 168;
    this.cmbP10S6.Visible = false;
    this.cmbP9S6.BackColor = SystemColors.ButtonFace;
    this.cmbP9S6.FormattingEnabled = true;
    this.cmbP9S6.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP9S6_1 = this.cmbP9S6;
    point1 = new Point(776, 233);
    Point point91 = point1;
    cmbP9S6_1.Location = point91;
    this.cmbP9S6.Name = "cmbP9S6";
    ComboBox cmbP9S6_2 = this.cmbP9S6;
    size1 = new Size(91, 21);
    Size size91 = size1;
    cmbP9S6_2.Size = size91;
    this.cmbP9S6.TabIndex = 151;
    this.cmbP9S6.Visible = false;
    this.cmbP8S6.BackColor = SystemColors.ButtonFace;
    this.cmbP8S6.FormattingEnabled = true;
    this.cmbP8S6.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP8S6_1 = this.cmbP8S6;
    point1 = new Point(776, 206);
    Point point92 = point1;
    cmbP8S6_1.Location = point92;
    this.cmbP8S6.Name = "cmbP8S6";
    ComboBox cmbP8S6_2 = this.cmbP8S6;
    size1 = new Size(91, 21);
    Size size92 = size1;
    cmbP8S6_2.Size = size92;
    this.cmbP8S6.TabIndex = 134;
    this.cmbP8S6.Visible = false;
    this.cmbP7S6.BackColor = SystemColors.ButtonFace;
    this.cmbP7S6.FormattingEnabled = true;
    this.cmbP7S6.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP7S6_1 = this.cmbP7S6;
    point1 = new Point(776, 179);
    Point point93 = point1;
    cmbP7S6_1.Location = point93;
    this.cmbP7S6.Name = "cmbP7S6";
    ComboBox cmbP7S6_2 = this.cmbP7S6;
    size1 = new Size(91, 21);
    Size size93 = size1;
    cmbP7S6_2.Size = size93;
    this.cmbP7S6.TabIndex = 118;
    this.cmbP7S6.Visible = false;
    this.cmbP6S6.BackColor = SystemColors.ButtonFace;
    this.cmbP6S6.FormattingEnabled = true;
    this.cmbP6S6.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP6S6_1 = this.cmbP6S6;
    point1 = new Point(776, 152);
    Point point94 = point1;
    cmbP6S6_1.Location = point94;
    this.cmbP6S6.Name = "cmbP6S6";
    ComboBox cmbP6S6_2 = this.cmbP6S6;
    size1 = new Size(91, 21);
    Size size94 = size1;
    cmbP6S6_2.Size = size94;
    this.cmbP6S6.TabIndex = 101;
    this.cmbP6S6.Visible = false;
    this.cmbP5S6.BackColor = SystemColors.ButtonFace;
    this.cmbP5S6.FormattingEnabled = true;
    this.cmbP5S6.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP5S6_1 = this.cmbP5S6;
    point1 = new Point(776, 125);
    Point point95 = point1;
    cmbP5S6_1.Location = point95;
    this.cmbP5S6.Name = "cmbP5S6";
    ComboBox cmbP5S6_2 = this.cmbP5S6;
    size1 = new Size(91, 21);
    Size size95 = size1;
    cmbP5S6_2.Size = size95;
    this.cmbP5S6.TabIndex = 83;
    this.cmbP5S6.Visible = false;
    this.cmbP4S6.BackColor = SystemColors.ButtonFace;
    this.cmbP4S6.FormattingEnabled = true;
    this.cmbP4S6.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP4S6_1 = this.cmbP4S6;
    point1 = new Point(776, 98);
    Point point96 = point1;
    cmbP4S6_1.Location = point96;
    this.cmbP4S6.Name = "cmbP4S6";
    ComboBox cmbP4S6_2 = this.cmbP4S6;
    size1 = new Size(91, 21);
    Size size96 = size1;
    cmbP4S6_2.Size = size96;
    this.cmbP4S6.TabIndex = 66;
    this.cmbP4S6.Visible = false;
    this.cmbP3S6.BackColor = SystemColors.ButtonFace;
    this.cmbP3S6.FormattingEnabled = true;
    this.cmbP3S6.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP3S6_1 = this.cmbP3S6;
    point1 = new Point(776, 71);
    Point point97 = point1;
    cmbP3S6_1.Location = point97;
    this.cmbP3S6.Name = "cmbP3S6";
    ComboBox cmbP3S6_2 = this.cmbP3S6;
    size1 = new Size(91, 21);
    Size size97 = size1;
    cmbP3S6_2.Size = size97;
    this.cmbP3S6.TabIndex = 49;
    this.cmbP3S6.Visible = false;
    this.cmbP2S6.BackColor = SystemColors.ButtonFace;
    this.cmbP2S6.FormattingEnabled = true;
    this.cmbP2S6.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP2S6_1 = this.cmbP2S6;
    point1 = new Point(776, 44);
    Point point98 = point1;
    cmbP2S6_1.Location = point98;
    this.cmbP2S6.Name = "cmbP2S6";
    ComboBox cmbP2S6_2 = this.cmbP2S6;
    size1 = new Size(91, 21);
    Size size98 = size1;
    cmbP2S6_2.Size = size98;
    this.cmbP2S6.TabIndex = 32 /*0x20*/;
    this.cmbP2S6.Visible = false;
    this.cmbP1S6.BackColor = SystemColors.ButtonFace;
    this.cmbP1S6.FormattingEnabled = true;
    this.cmbP1S6.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP1S6_1 = this.cmbP1S6;
    point1 = new Point(776, 17);
    Point point99 = point1;
    cmbP1S6_1.Location = point99;
    this.cmbP1S6.Name = "cmbP1S6";
    ComboBox cmbP1S6_2 = this.cmbP1S6;
    size1 = new Size(91, 21);
    Size size99 = size1;
    cmbP1S6_2.Size = size99;
    this.cmbP1S6.TabIndex = 15;
    this.cmbP1S6.Visible = false;
    this.btnP16S5.BackColor = Color.Tomato;
    Button btnP16S5_1 = this.btnP16S5;
    point1 = new Point(735, 426);
    Point point100 = point1;
    btnP16S5_1.Location = point100;
    this.btnP16S5.Name = "btnP16S5";
    Button btnP16S5_2 = this.btnP16S5;
    size1 = new Size(20, 17);
    Size size100 = size1;
    btnP16S5_2.Size = size100;
    this.btnP16S5.TabIndex = 268;
    this.btnP16S5.Text = " ";
    this.btnP16S5.UseVisualStyleBackColor = false;
    this.btnP16S5.Visible = false;
    this.btnP15S5.BackColor = Color.Tomato;
    Button btnP15S5_1 = this.btnP15S5;
    point1 = new Point(735, 399);
    Point point101 = point1;
    btnP15S5_1.Location = point101;
    this.btnP15S5.Name = "btnP15S5";
    Button btnP15S5_2 = this.btnP15S5;
    size1 = new Size(19, 17);
    Size size101 = size1;
    btnP15S5_2.Size = size101;
    this.btnP15S5.TabIndex = 251;
    this.btnP15S5.Text = " ";
    this.btnP15S5.UseVisualStyleBackColor = false;
    this.btnP15S5.Visible = false;
    this.btnP14S5.BackColor = Color.Tomato;
    Button btnP14S5_1 = this.btnP14S5;
    point1 = new Point(734, 374);
    Point point102 = point1;
    btnP14S5_1.Location = point102;
    this.btnP14S5.Name = "btnP14S5";
    Button btnP14S5_2 = this.btnP14S5;
    size1 = new Size(20, 17);
    Size size102 = size1;
    btnP14S5_2.Size = size102;
    this.btnP14S5.TabIndex = 234;
    this.btnP14S5.Text = " ";
    this.btnP14S5.UseVisualStyleBackColor = false;
    this.btnP14S5.Visible = false;
    this.btnP13S5.BackColor = Color.Tomato;
    Button btnP13S5_1 = this.btnP13S5;
    point1 = new Point(735, 345);
    Point point103 = point1;
    btnP13S5_1.Location = point103;
    this.btnP13S5.Name = "btnP13S5";
    Button btnP13S5_2 = this.btnP13S5;
    size1 = new Size(19, 17);
    Size size103 = size1;
    btnP13S5_2.Size = size103;
    this.btnP13S5.TabIndex = 217;
    this.btnP13S5.Text = " ";
    this.btnP13S5.UseVisualStyleBackColor = false;
    this.btnP13S5.Visible = false;
    this.btnP12S5.BackColor = Color.Tomato;
    Button btnP12S5_1 = this.btnP12S5;
    point1 = new Point(735, 316);
    Point point104 = point1;
    btnP12S5_1.Location = point104;
    this.btnP12S5.Name = "btnP12S5";
    Button btnP12S5_2 = this.btnP12S5;
    size1 = new Size(19, 19);
    Size size104 = size1;
    btnP12S5_2.Size = size104;
    this.btnP12S5.TabIndex = 200;
    this.btnP12S5.Text = " ";
    this.btnP12S5.UseVisualStyleBackColor = false;
    this.btnP12S5.Visible = false;
    this.btnP11S5.BackColor = Color.Tomato;
    Button btnP11S5_1 = this.btnP11S5;
    point1 = new Point(735, 291);
    Point point105 = point1;
    btnP11S5_1.Location = point105;
    this.btnP11S5.Name = "btnP11S5";
    Button btnP11S5_2 = this.btnP11S5;
    size1 = new Size(19, 17);
    Size size105 = size1;
    btnP11S5_2.Size = size105;
    this.btnP11S5.TabIndex = 183;
    this.btnP11S5.Text = " ";
    this.btnP11S5.UseVisualStyleBackColor = false;
    this.btnP11S5.Visible = false;
    this.btnP10S5.BackColor = Color.Tomato;
    Button btnP10S5_1 = this.btnP10S5;
    point1 = new Point(735, 264);
    Point point106 = point1;
    btnP10S5_1.Location = point106;
    this.btnP10S5.Name = "btnP10S5";
    Button btnP10S5_2 = this.btnP10S5;
    size1 = new Size(19, 17);
    Size size106 = size1;
    btnP10S5_2.Size = size106;
    this.btnP10S5.TabIndex = 167;
    this.btnP10S5.Text = " ";
    this.btnP10S5.UseVisualStyleBackColor = false;
    this.btnP10S5.Visible = false;
    this.btnP9S5.BackColor = Color.Tomato;
    Button btnP9S5_1 = this.btnP9S5;
    point1 = new Point(735, 237);
    Point point107 = point1;
    btnP9S5_1.Location = point107;
    this.btnP9S5.Name = "btnP9S5";
    Button btnP9S5_2 = this.btnP9S5;
    size1 = new Size(19, 17);
    Size size107 = size1;
    btnP9S5_2.Size = size107;
    this.btnP9S5.TabIndex = 150;
    this.btnP9S5.Text = " ";
    this.btnP9S5.UseVisualStyleBackColor = false;
    this.btnP9S5.Visible = false;
    this.btnP8S5.BackColor = Color.Tomato;
    Button btnP8S5_1 = this.btnP8S5;
    point1 = new Point(735, 212);
    Point point108 = point1;
    btnP8S5_1.Location = point108;
    this.btnP8S5.Name = "btnP8S5";
    Button btnP8S5_2 = this.btnP8S5;
    size1 = new Size(17, 18);
    Size size108 = size1;
    btnP8S5_2.Size = size108;
    this.btnP8S5.TabIndex = 133;
    this.btnP8S5.Text = " ";
    this.btnP8S5.UseVisualStyleBackColor = false;
    this.btnP8S5.Visible = false;
    this.btnP7S5.BackColor = Color.Tomato;
    Button btnP7S5_1 = this.btnP7S5;
    point1 = new Point(735, 183);
    Point point109 = point1;
    btnP7S5_1.Location = point109;
    this.btnP7S5.Name = "btnP7S5";
    Button btnP7S5_2 = this.btnP7S5;
    size1 = new Size(17, 17);
    Size size109 = size1;
    btnP7S5_2.Size = size109;
    this.btnP7S5.TabIndex = 116;
    this.btnP7S5.Text = " ";
    this.btnP7S5.UseVisualStyleBackColor = false;
    this.btnP7S5.Visible = false;
    this.btnP6S5.BackColor = Color.Tomato;
    Button btnP6S5_1 = this.btnP6S5;
    point1 = new Point(735, 154);
    Point point110 = point1;
    btnP6S5_1.Location = point110;
    this.btnP6S5.Name = "btnP6S5";
    Button btnP6S5_2 = this.btnP6S5;
    size1 = new Size(17, 17);
    Size size110 = size1;
    btnP6S5_2.Size = size110;
    this.btnP6S5.TabIndex = 99;
    this.btnP6S5.Text = " ";
    this.btnP6S5.UseVisualStyleBackColor = false;
    this.btnP6S5.Visible = false;
    this.btnP5S5.BackColor = Color.Tomato;
    Button btnP5S5_1 = this.btnP5S5;
    point1 = new Point(735, 129);
    Point point111 = point1;
    btnP5S5_1.Location = point111;
    this.btnP5S5.Name = "btnP5S5";
    Button btnP5S5_2 = this.btnP5S5;
    size1 = new Size(17, 17);
    Size size111 = size1;
    btnP5S5_2.Size = size111;
    this.btnP5S5.TabIndex = 82;
    this.btnP5S5.Text = " ";
    this.btnP5S5.UseVisualStyleBackColor = false;
    this.btnP5S5.Visible = false;
    this.btnP4S5.BackColor = Color.Tomato;
    Button btnP4S5_1 = this.btnP4S5;
    point1 = new Point(735, 102);
    Point point112 = point1;
    btnP4S5_1.Location = point112;
    this.btnP4S5.Name = "btnP4S5";
    Button btnP4S5_2 = this.btnP4S5;
    size1 = new Size(17, 19);
    Size size112 = size1;
    btnP4S5_2.Size = size112;
    this.btnP4S5.TabIndex = 65;
    this.btnP4S5.Text = " ";
    this.btnP4S5.UseVisualStyleBackColor = false;
    this.btnP4S5.Visible = false;
    this.btnP3S5.BackColor = Color.Tomato;
    Button btnP3S5_1 = this.btnP3S5;
    point1 = new Point(735, 75);
    Point point113 = point1;
    btnP3S5_1.Location = point113;
    this.btnP3S5.Name = "btnP3S5";
    Button btnP3S5_2 = this.btnP3S5;
    size1 = new Size(17, 19);
    Size size113 = size1;
    btnP3S5_2.Size = size113;
    this.btnP3S5.TabIndex = 48 /*0x30*/;
    this.btnP3S5.Text = " ";
    this.btnP3S5.UseVisualStyleBackColor = false;
    this.btnP3S5.Visible = false;
    this.btnP2S5.BackColor = Color.Tomato;
    Button btnP2S5_1 = this.btnP2S5;
    point1 = new Point(735, 46);
    Point point114 = point1;
    btnP2S5_1.Location = point114;
    this.btnP2S5.Name = "btnP2S5";
    Button btnP2S5_2 = this.btnP2S5;
    size1 = new Size(17, 19);
    Size size114 = size1;
    btnP2S5_2.Size = size114;
    this.btnP2S5.TabIndex = 31 /*0x1F*/;
    this.btnP2S5.Text = " ";
    this.btnP2S5.UseVisualStyleBackColor = false;
    this.btnP2S5.Visible = false;
    this.btnP1S5.BackColor = Color.Tomato;
    Button btnP1S5_1 = this.btnP1S5;
    point1 = new Point(735, 19);
    Point point115 = point1;
    btnP1S5_1.Location = point115;
    this.btnP1S5.Name = "btnP1S5";
    Button btnP1S5_2 = this.btnP1S5;
    size1 = new Size(17, 19);
    Size size115 = size1;
    btnP1S5_2.Size = size115;
    this.btnP1S5.TabIndex = 14;
    this.btnP1S5.UseVisualStyleBackColor = false;
    this.btnP1S5.Visible = false;
    this.cmbP16S5.BackColor = SystemColors.ButtonFace;
    this.cmbP16S5.FormattingEnabled = true;
    this.cmbP16S5.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP16S5_1 = this.cmbP16S5;
    point1 = new Point(636, 422);
    Point point116 = point1;
    cmbP16S5_1.Location = point116;
    this.cmbP16S5.Name = "cmbP16S5";
    ComboBox cmbP16S5_2 = this.cmbP16S5;
    size1 = new Size(91, 21);
    Size size116 = size1;
    cmbP16S5_2.Size = size116;
    this.cmbP16S5.TabIndex = 267;
    this.cmbP16S5.Visible = false;
    this.cmbP15S5.BackColor = SystemColors.ButtonFace;
    this.cmbP15S5.FormattingEnabled = true;
    this.cmbP15S5.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP15S5_1 = this.cmbP15S5;
    point1 = new Point(635, 393);
    Point point117 = point1;
    cmbP15S5_1.Location = point117;
    this.cmbP15S5.Name = "cmbP15S5";
    ComboBox cmbP15S5_2 = this.cmbP15S5;
    size1 = new Size(91, 21);
    Size size117 = size1;
    cmbP15S5_2.Size = size117;
    this.cmbP15S5.TabIndex = 250;
    this.cmbP15S5.Visible = false;
    this.cmbP14S5.BackColor = SystemColors.ButtonFace;
    this.cmbP14S5.FormattingEnabled = true;
    this.cmbP14S5.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP14S5_1 = this.cmbP14S5;
    point1 = new Point(636, 368);
    Point point118 = point1;
    cmbP14S5_1.Location = point118;
    this.cmbP14S5.Name = "cmbP14S5";
    ComboBox cmbP14S5_2 = this.cmbP14S5;
    size1 = new Size(91, 21);
    Size size118 = size1;
    cmbP14S5_2.Size = size118;
    this.cmbP14S5.TabIndex = 233;
    this.cmbP14S5.Visible = false;
    this.cmbP13S5.BackColor = SystemColors.ButtonFace;
    this.cmbP13S5.FormattingEnabled = true;
    this.cmbP13S5.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP13S5_1 = this.cmbP13S5;
    point1 = new Point(636, 341);
    Point point119 = point1;
    cmbP13S5_1.Location = point119;
    this.cmbP13S5.Name = "cmbP13S5";
    ComboBox cmbP13S5_2 = this.cmbP13S5;
    size1 = new Size(91, 21);
    Size size119 = size1;
    cmbP13S5_2.Size = size119;
    this.cmbP13S5.TabIndex = 217;
    this.cmbP13S5.Visible = false;
    this.cmbP12S5.BackColor = SystemColors.ButtonFace;
    this.cmbP12S5.FormattingEnabled = true;
    this.cmbP12S5.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP12S5_1 = this.cmbP12S5;
    point1 = new Point(636, 314);
    Point point120 = point1;
    cmbP12S5_1.Location = point120;
    this.cmbP12S5.Name = "cmbP12S5";
    ComboBox cmbP12S5_2 = this.cmbP12S5;
    size1 = new Size(91, 21);
    Size size120 = size1;
    cmbP12S5_2.Size = size120;
    this.cmbP12S5.TabIndex = 200;
    this.cmbP12S5.Visible = false;
    this.cmbP11S5.BackColor = SystemColors.ButtonFace;
    this.cmbP11S5.FormattingEnabled = true;
    this.cmbP11S5.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP11S5_1 = this.cmbP11S5;
    point1 = new Point(636, 287);
    Point point121 = point1;
    cmbP11S5_1.Location = point121;
    this.cmbP11S5.Name = "cmbP11S5";
    ComboBox cmbP11S5_2 = this.cmbP11S5;
    size1 = new Size(91, 21);
    Size size121 = size1;
    cmbP11S5_2.Size = size121;
    this.cmbP11S5.TabIndex = 183;
    this.cmbP11S5.Visible = false;
    this.cmbP10S5.BackColor = SystemColors.ButtonFace;
    this.cmbP10S5.FormattingEnabled = true;
    this.cmbP10S5.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP10S5_1 = this.cmbP10S5;
    point1 = new Point(636, 260);
    Point point122 = point1;
    cmbP10S5_1.Location = point122;
    this.cmbP10S5.Name = "cmbP10S5";
    ComboBox cmbP10S5_2 = this.cmbP10S5;
    size1 = new Size(91, 21);
    Size size122 = size1;
    cmbP10S5_2.Size = size122;
    this.cmbP10S5.TabIndex = 166;
    this.cmbP10S5.Visible = false;
    this.cmbP9S5.BackColor = SystemColors.ButtonFace;
    this.cmbP9S5.FormattingEnabled = true;
    this.cmbP9S5.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP9S5_1 = this.cmbP9S5;
    point1 = new Point(636, 233);
    Point point123 = point1;
    cmbP9S5_1.Location = point123;
    this.cmbP9S5.Name = "cmbP9S5";
    ComboBox cmbP9S5_2 = this.cmbP9S5;
    size1 = new Size(91, 21);
    Size size123 = size1;
    cmbP9S5_2.Size = size123;
    this.cmbP9S5.TabIndex = 149;
    this.cmbP9S5.Visible = false;
    this.cmbP8S5.BackColor = SystemColors.ButtonFace;
    this.cmbP8S5.FormattingEnabled = true;
    this.cmbP8S5.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP8S5_1 = this.cmbP8S5;
    point1 = new Point(636, 206);
    Point point124 = point1;
    cmbP8S5_1.Location = point124;
    this.cmbP8S5.Name = "cmbP8S5";
    ComboBox cmbP8S5_2 = this.cmbP8S5;
    size1 = new Size(91, 21);
    Size size124 = size1;
    cmbP8S5_2.Size = size124;
    this.cmbP8S5.TabIndex = 132;
    this.cmbP8S5.Visible = false;
    this.cmbP7S5.BackColor = SystemColors.ButtonFace;
    this.cmbP7S5.FormattingEnabled = true;
    this.cmbP7S5.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP7S5_1 = this.cmbP7S5;
    point1 = new Point(636, 179);
    Point point125 = point1;
    cmbP7S5_1.Location = point125;
    this.cmbP7S5.Name = "cmbP7S5";
    ComboBox cmbP7S5_2 = this.cmbP7S5;
    size1 = new Size(91, 21);
    Size size125 = size1;
    cmbP7S5_2.Size = size125;
    this.cmbP7S5.TabIndex = 116;
    this.cmbP7S5.Visible = false;
    this.cmbP6S5.BackColor = SystemColors.ButtonFace;
    this.cmbP6S5.FormattingEnabled = true;
    this.cmbP6S5.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP6S5_1 = this.cmbP6S5;
    point1 = new Point(636, 152);
    Point point126 = point1;
    cmbP6S5_1.Location = point126;
    this.cmbP6S5.Name = "cmbP6S5";
    ComboBox cmbP6S5_2 = this.cmbP6S5;
    size1 = new Size(91, 21);
    Size size126 = size1;
    cmbP6S5_2.Size = size126;
    this.cmbP6S5.TabIndex = 99;
    this.cmbP6S5.Visible = false;
    this.cmbP5S5.BackColor = SystemColors.ButtonFace;
    this.cmbP5S5.FormattingEnabled = true;
    this.cmbP5S5.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP5S5_1 = this.cmbP5S5;
    point1 = new Point(636, 125);
    Point point127 = point1;
    cmbP5S5_1.Location = point127;
    this.cmbP5S5.Name = "cmbP5S5";
    ComboBox cmbP5S5_2 = this.cmbP5S5;
    size1 = new Size(91, 21);
    Size size127 = size1;
    cmbP5S5_2.Size = size127;
    this.cmbP5S5.TabIndex = 81;
    this.cmbP5S5.Visible = false;
    this.cmbP4S5.BackColor = SystemColors.ButtonFace;
    this.cmbP4S5.FormattingEnabled = true;
    this.cmbP4S5.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP4S5_1 = this.cmbP4S5;
    point1 = new Point(636, 98);
    Point point128 = point1;
    cmbP4S5_1.Location = point128;
    this.cmbP4S5.Name = "cmbP4S5";
    ComboBox cmbP4S5_2 = this.cmbP4S5;
    size1 = new Size(91, 21);
    Size size128 = size1;
    cmbP4S5_2.Size = size128;
    this.cmbP4S5.TabIndex = 64 /*0x40*/;
    this.cmbP4S5.Visible = false;
    this.cmbP3S5.BackColor = SystemColors.ButtonFace;
    this.cmbP3S5.FormattingEnabled = true;
    this.cmbP3S5.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP3S5_1 = this.cmbP3S5;
    point1 = new Point(636, 71);
    Point point129 = point1;
    cmbP3S5_1.Location = point129;
    this.cmbP3S5.Name = "cmbP3S5";
    ComboBox cmbP3S5_2 = this.cmbP3S5;
    size1 = new Size(91, 21);
    Size size129 = size1;
    cmbP3S5_2.Size = size129;
    this.cmbP3S5.TabIndex = 47;
    this.cmbP3S5.Visible = false;
    this.cmbP2S5.BackColor = SystemColors.ButtonFace;
    this.cmbP2S5.FormattingEnabled = true;
    this.cmbP2S5.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP2S5_1 = this.cmbP2S5;
    point1 = new Point(636, 44);
    Point point130 = point1;
    cmbP2S5_1.Location = point130;
    this.cmbP2S5.Name = "cmbP2S5";
    ComboBox cmbP2S5_2 = this.cmbP2S5;
    size1 = new Size(91, 21);
    Size size130 = size1;
    cmbP2S5_2.Size = size130;
    this.cmbP2S5.TabIndex = 30;
    this.cmbP2S5.Visible = false;
    this.cmbP1S5.BackColor = SystemColors.ButtonFace;
    this.cmbP1S5.FormattingEnabled = true;
    this.cmbP1S5.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP1S5_1 = this.cmbP1S5;
    point1 = new Point(636, 17);
    Point point131 = point1;
    cmbP1S5_1.Location = point131;
    this.cmbP1S5.Name = "cmbP1S5";
    ComboBox cmbP1S5_2 = this.cmbP1S5;
    size1 = new Size(91, 21);
    Size size131 = size1;
    cmbP1S5_2.Size = size131;
    this.cmbP1S5.TabIndex = 13;
    this.cmbP1S5.Visible = false;
    this.btnP16S4.BackColor = Color.Tomato;
    Button btnP16S4_1 = this.btnP16S4;
    point1 = new Point(595, 426);
    Point point132 = point1;
    btnP16S4_1.Location = point132;
    this.btnP16S4.Name = "btnP16S4";
    Button btnP16S4_2 = this.btnP16S4;
    size1 = new Size(20, 17);
    Size size132 = size1;
    btnP16S4_2.Size = size132;
    this.btnP16S4.TabIndex = 266;
    this.btnP16S4.Text = " ";
    this.btnP16S4.UseVisualStyleBackColor = false;
    this.btnP16S4.Visible = false;
    this.btnP15S4.BackColor = Color.Tomato;
    Button btnP15S4_1 = this.btnP15S4;
    point1 = new Point(595, 399);
    Point point133 = point1;
    btnP15S4_1.Location = point133;
    this.btnP15S4.Name = "btnP15S4";
    Button btnP15S4_2 = this.btnP15S4;
    size1 = new Size(19, 17);
    Size size133 = size1;
    btnP15S4_2.Size = size133;
    this.btnP15S4.TabIndex = 249;
    this.btnP15S4.Text = " ";
    this.btnP15S4.UseVisualStyleBackColor = false;
    this.btnP15S4.Visible = false;
    this.btnP14S4.BackColor = Color.Tomato;
    Button btnP14S4_1 = this.btnP14S4;
    point1 = new Point(594, 374);
    Point point134 = point1;
    btnP14S4_1.Location = point134;
    this.btnP14S4.Name = "btnP14S4";
    Button btnP14S4_2 = this.btnP14S4;
    size1 = new Size(20, 17);
    Size size134 = size1;
    btnP14S4_2.Size = size134;
    this.btnP14S4.TabIndex = 232;
    this.btnP14S4.Text = " ";
    this.btnP14S4.UseVisualStyleBackColor = false;
    this.btnP14S4.Visible = false;
    this.btnP13S4.BackColor = Color.Tomato;
    Button btnP13S4_1 = this.btnP13S4;
    point1 = new Point(595, 345);
    Point point135 = point1;
    btnP13S4_1.Location = point135;
    this.btnP13S4.Name = "btnP13S4";
    Button btnP13S4_2 = this.btnP13S4;
    size1 = new Size(19, 17);
    Size size135 = size1;
    btnP13S4_2.Size = size135;
    this.btnP13S4.TabIndex = 215;
    this.btnP13S4.Text = " ";
    this.btnP13S4.UseVisualStyleBackColor = false;
    this.btnP13S4.Visible = false;
    this.btnP12S4.BackColor = Color.Tomato;
    Button btnP12S4_1 = this.btnP12S4;
    point1 = new Point(595, 316);
    Point point136 = point1;
    btnP12S4_1.Location = point136;
    this.btnP12S4.Name = "btnP12S4";
    Button btnP12S4_2 = this.btnP12S4;
    size1 = new Size(19, 19);
    Size size136 = size1;
    btnP12S4_2.Size = size136;
    this.btnP12S4.TabIndex = 198;
    this.btnP12S4.Text = " ";
    this.btnP12S4.UseVisualStyleBackColor = false;
    this.btnP12S4.Visible = false;
    this.btnP11S4.BackColor = Color.Tomato;
    Button btnP11S4_1 = this.btnP11S4;
    point1 = new Point(595, 291);
    Point point137 = point1;
    btnP11S4_1.Location = point137;
    this.btnP11S4.Name = "btnP11S4";
    Button btnP11S4_2 = this.btnP11S4;
    size1 = new Size(19, 17);
    Size size137 = size1;
    btnP11S4_2.Size = size137;
    this.btnP11S4.TabIndex = 181;
    this.btnP11S4.Text = " ";
    this.btnP11S4.UseVisualStyleBackColor = false;
    this.btnP11S4.Visible = false;
    this.btnP10S4.BackColor = Color.Tomato;
    Button btnP10S4_1 = this.btnP10S4;
    point1 = new Point(595, 264);
    Point point138 = point1;
    btnP10S4_1.Location = point138;
    this.btnP10S4.Name = "btnP10S4";
    Button btnP10S4_2 = this.btnP10S4;
    size1 = new Size(19, 17);
    Size size138 = size1;
    btnP10S4_2.Size = size138;
    this.btnP10S4.TabIndex = 165;
    this.btnP10S4.Text = " ";
    this.btnP10S4.UseVisualStyleBackColor = false;
    this.btnP10S4.Visible = false;
    this.btnP9S4.BackColor = Color.Tomato;
    Button btnP9S4_1 = this.btnP9S4;
    point1 = new Point(595, 237);
    Point point139 = point1;
    btnP9S4_1.Location = point139;
    this.btnP9S4.Name = "btnP9S4";
    Button btnP9S4_2 = this.btnP9S4;
    size1 = new Size(19, 17);
    Size size139 = size1;
    btnP9S4_2.Size = size139;
    this.btnP9S4.TabIndex = 148;
    this.btnP9S4.Text = " ";
    this.btnP9S4.UseVisualStyleBackColor = false;
    this.btnP9S4.Visible = false;
    this.btnP8S4.BackColor = Color.Tomato;
    Button btnP8S4_1 = this.btnP8S4;
    point1 = new Point(595, 212);
    Point point140 = point1;
    btnP8S4_1.Location = point140;
    this.btnP8S4.Name = "btnP8S4";
    Button btnP8S4_2 = this.btnP8S4;
    size1 = new Size(17, 18);
    Size size140 = size1;
    btnP8S4_2.Size = size140;
    this.btnP8S4.TabIndex = 131;
    this.btnP8S4.Text = " ";
    this.btnP8S4.UseVisualStyleBackColor = false;
    this.btnP8S4.Visible = false;
    this.btnP7S4.BackColor = Color.Tomato;
    Button btnP7S4_1 = this.btnP7S4;
    point1 = new Point(595, 183);
    Point point141 = point1;
    btnP7S4_1.Location = point141;
    this.btnP7S4.Name = "btnP7S4";
    Button btnP7S4_2 = this.btnP7S4;
    size1 = new Size(17, 17);
    Size size141 = size1;
    btnP7S4_2.Size = size141;
    this.btnP7S4.TabIndex = 114;
    this.btnP7S4.Text = " ";
    this.btnP7S4.UseVisualStyleBackColor = false;
    this.btnP7S4.Visible = false;
    this.btnP6S4.BackColor = Color.Tomato;
    Button btnP6S4_1 = this.btnP6S4;
    point1 = new Point(595, 154);
    Point point142 = point1;
    btnP6S4_1.Location = point142;
    this.btnP6S4.Name = "btnP6S4";
    Button btnP6S4_2 = this.btnP6S4;
    size1 = new Size(17, 17);
    Size size142 = size1;
    btnP6S4_2.Size = size142;
    this.btnP6S4.TabIndex = 97;
    this.btnP6S4.Text = " ";
    this.btnP6S4.UseVisualStyleBackColor = false;
    this.btnP6S4.Visible = false;
    this.btnP5S4.BackColor = Color.Tomato;
    Button btnP5S4_1 = this.btnP5S4;
    point1 = new Point(595, 129);
    Point point143 = point1;
    btnP5S4_1.Location = point143;
    this.btnP5S4.Name = "btnP5S4";
    Button btnP5S4_2 = this.btnP5S4;
    size1 = new Size(17, 17);
    Size size143 = size1;
    btnP5S4_2.Size = size143;
    this.btnP5S4.TabIndex = 80 /*0x50*/;
    this.btnP5S4.Text = " ";
    this.btnP5S4.UseVisualStyleBackColor = false;
    this.btnP5S4.Visible = false;
    this.btnP4S4.BackColor = Color.Tomato;
    Button btnP4S4_1 = this.btnP4S4;
    point1 = new Point(595, 102);
    Point point144 = point1;
    btnP4S4_1.Location = point144;
    this.btnP4S4.Name = "btnP4S4";
    Button btnP4S4_2 = this.btnP4S4;
    size1 = new Size(17, 19);
    Size size144 = size1;
    btnP4S4_2.Size = size144;
    this.btnP4S4.TabIndex = 63 /*0x3F*/;
    this.btnP4S4.Text = " ";
    this.btnP4S4.UseVisualStyleBackColor = false;
    this.btnP4S4.Visible = false;
    this.btnP3S4.BackColor = Color.Tomato;
    Button btnP3S4_1 = this.btnP3S4;
    point1 = new Point(595, 75);
    Point point145 = point1;
    btnP3S4_1.Location = point145;
    this.btnP3S4.Name = "btnP3S4";
    Button btnP3S4_2 = this.btnP3S4;
    size1 = new Size(17, 19);
    Size size145 = size1;
    btnP3S4_2.Size = size145;
    this.btnP3S4.TabIndex = 46;
    this.btnP3S4.Text = " ";
    this.btnP3S4.UseVisualStyleBackColor = false;
    this.btnP3S4.Visible = false;
    this.btnP2S4.BackColor = Color.Tomato;
    Button btnP2S4_1 = this.btnP2S4;
    point1 = new Point(595, 46);
    Point point146 = point1;
    btnP2S4_1.Location = point146;
    this.btnP2S4.Name = "btnP2S4";
    Button btnP2S4_2 = this.btnP2S4;
    size1 = new Size(17, 19);
    Size size146 = size1;
    btnP2S4_2.Size = size146;
    this.btnP2S4.TabIndex = 29;
    this.btnP2S4.Text = " ";
    this.btnP2S4.UseVisualStyleBackColor = false;
    this.btnP2S4.Visible = false;
    this.btnP1S4.BackColor = Color.Tomato;
    Button btnP1S4_1 = this.btnP1S4;
    point1 = new Point(595, 19);
    Point point147 = point1;
    btnP1S4_1.Location = point147;
    this.btnP1S4.Name = "btnP1S4";
    Button btnP1S4_2 = this.btnP1S4;
    size1 = new Size(17, 19);
    Size size147 = size1;
    btnP1S4_2.Size = size147;
    this.btnP1S4.TabIndex = 12;
    this.btnP1S4.UseVisualStyleBackColor = false;
    this.btnP1S4.Visible = false;
    this.cmbP16S4.BackColor = SystemColors.ButtonFace;
    this.cmbP16S4.FormattingEnabled = true;
    this.cmbP16S4.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP16S4_1 = this.cmbP16S4;
    point1 = new Point(496, 422);
    Point point148 = point1;
    cmbP16S4_1.Location = point148;
    this.cmbP16S4.Name = "cmbP16S4";
    ComboBox cmbP16S4_2 = this.cmbP16S4;
    size1 = new Size(91, 21);
    Size size148 = size1;
    cmbP16S4_2.Size = size148;
    this.cmbP16S4.TabIndex = 265;
    this.cmbP16S4.Visible = false;
    this.cmbP15S4.BackColor = SystemColors.ButtonFace;
    this.cmbP15S4.FormattingEnabled = true;
    this.cmbP15S4.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP15S4_1 = this.cmbP15S4;
    point1 = new Point(496, 395);
    Point point149 = point1;
    cmbP15S4_1.Location = point149;
    this.cmbP15S4.Name = "cmbP15S4";
    ComboBox cmbP15S4_2 = this.cmbP15S4;
    size1 = new Size(91, 21);
    Size size149 = size1;
    cmbP15S4_2.Size = size149;
    this.cmbP15S4.TabIndex = 248;
    this.cmbP15S4.Visible = false;
    this.cmbP14S4.BackColor = SystemColors.ButtonFace;
    this.cmbP14S4.FormattingEnabled = true;
    this.cmbP14S4.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP14S4_1 = this.cmbP14S4;
    point1 = new Point(496, 368);
    Point point150 = point1;
    cmbP14S4_1.Location = point150;
    this.cmbP14S4.Name = "cmbP14S4";
    ComboBox cmbP14S4_2 = this.cmbP14S4;
    size1 = new Size(91, 21);
    Size size150 = size1;
    cmbP14S4_2.Size = size150;
    this.cmbP14S4.TabIndex = 231;
    this.cmbP14S4.Visible = false;
    this.cmbP13S4.BackColor = SystemColors.ButtonFace;
    this.cmbP13S4.FormattingEnabled = true;
    this.cmbP13S4.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP13S4_1 = this.cmbP13S4;
    point1 = new Point(496, 341);
    Point point151 = point1;
    cmbP13S4_1.Location = point151;
    this.cmbP13S4.Name = "cmbP13S4";
    ComboBox cmbP13S4_2 = this.cmbP13S4;
    size1 = new Size(91, 21);
    Size size151 = size1;
    cmbP13S4_2.Size = size151;
    this.cmbP13S4.TabIndex = 215;
    this.cmbP13S4.Visible = false;
    this.cmbP12S4.BackColor = SystemColors.ButtonFace;
    this.cmbP12S4.FormattingEnabled = true;
    this.cmbP12S4.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP12S4_1 = this.cmbP12S4;
    point1 = new Point(496, 314);
    Point point152 = point1;
    cmbP12S4_1.Location = point152;
    this.cmbP12S4.Name = "cmbP12S4";
    ComboBox cmbP12S4_2 = this.cmbP12S4;
    size1 = new Size(91, 21);
    Size size152 = size1;
    cmbP12S4_2.Size = size152;
    this.cmbP12S4.TabIndex = 198;
    this.cmbP12S4.Visible = false;
    this.cmbP11S4.BackColor = SystemColors.ButtonFace;
    this.cmbP11S4.FormattingEnabled = true;
    this.cmbP11S4.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP11S4_1 = this.cmbP11S4;
    point1 = new Point(496, 287);
    Point point153 = point1;
    cmbP11S4_1.Location = point153;
    this.cmbP11S4.Name = "cmbP11S4";
    ComboBox cmbP11S4_2 = this.cmbP11S4;
    size1 = new Size(91, 21);
    Size size153 = size1;
    cmbP11S4_2.Size = size153;
    this.cmbP11S4.TabIndex = 181;
    this.cmbP11S4.Visible = false;
    this.cmbP10S4.BackColor = SystemColors.ButtonFace;
    this.cmbP10S4.FormattingEnabled = true;
    this.cmbP10S4.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP10S4_1 = this.cmbP10S4;
    point1 = new Point(496, 260);
    Point point154 = point1;
    cmbP10S4_1.Location = point154;
    this.cmbP10S4.Name = "cmbP10S4";
    ComboBox cmbP10S4_2 = this.cmbP10S4;
    size1 = new Size(91, 21);
    Size size154 = size1;
    cmbP10S4_2.Size = size154;
    this.cmbP10S4.TabIndex = 164;
    this.cmbP10S4.Visible = false;
    this.cmbP9S4.BackColor = SystemColors.ButtonFace;
    this.cmbP9S4.FormattingEnabled = true;
    this.cmbP9S4.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP9S4_1 = this.cmbP9S4;
    point1 = new Point(496, 233);
    Point point155 = point1;
    cmbP9S4_1.Location = point155;
    this.cmbP9S4.Name = "cmbP9S4";
    ComboBox cmbP9S4_2 = this.cmbP9S4;
    size1 = new Size(91, 21);
    Size size155 = size1;
    cmbP9S4_2.Size = size155;
    this.cmbP9S4.TabIndex = 147;
    this.cmbP9S4.Visible = false;
    this.cmbP8S4.BackColor = SystemColors.ButtonFace;
    this.cmbP8S4.FormattingEnabled = true;
    this.cmbP8S4.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP8S4_1 = this.cmbP8S4;
    point1 = new Point(496, 206);
    Point point156 = point1;
    cmbP8S4_1.Location = point156;
    this.cmbP8S4.Name = "cmbP8S4";
    ComboBox cmbP8S4_2 = this.cmbP8S4;
    size1 = new Size(91, 21);
    Size size156 = size1;
    cmbP8S4_2.Size = size156;
    this.cmbP8S4.TabIndex = 130;
    this.cmbP8S4.Visible = false;
    this.cmbP7S4.BackColor = SystemColors.ButtonFace;
    this.cmbP7S4.FormattingEnabled = true;
    this.cmbP7S4.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP7S4_1 = this.cmbP7S4;
    point1 = new Point(496, 179);
    Point point157 = point1;
    cmbP7S4_1.Location = point157;
    this.cmbP7S4.Name = "cmbP7S4";
    ComboBox cmbP7S4_2 = this.cmbP7S4;
    size1 = new Size(91, 21);
    Size size157 = size1;
    cmbP7S4_2.Size = size157;
    this.cmbP7S4.TabIndex = 114;
    this.cmbP7S4.Visible = false;
    this.cmbP6S4.BackColor = SystemColors.ButtonFace;
    this.cmbP6S4.FormattingEnabled = true;
    this.cmbP6S4.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP6S4_1 = this.cmbP6S4;
    point1 = new Point(496, 152);
    Point point158 = point1;
    cmbP6S4_1.Location = point158;
    this.cmbP6S4.Name = "cmbP6S4";
    ComboBox cmbP6S4_2 = this.cmbP6S4;
    size1 = new Size(91, 21);
    Size size158 = size1;
    cmbP6S4_2.Size = size158;
    this.cmbP6S4.TabIndex = 97;
    this.cmbP6S4.Visible = false;
    this.cmbP5S4.BackColor = SystemColors.ButtonFace;
    this.cmbP5S4.FormattingEnabled = true;
    this.cmbP5S4.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP5S4_1 = this.cmbP5S4;
    point1 = new Point(496, 125);
    Point point159 = point1;
    cmbP5S4_1.Location = point159;
    this.cmbP5S4.Name = "cmbP5S4";
    ComboBox cmbP5S4_2 = this.cmbP5S4;
    size1 = new Size(91, 21);
    Size size159 = size1;
    cmbP5S4_2.Size = size159;
    this.cmbP5S4.TabIndex = 79;
    this.cmbP5S4.Visible = false;
    this.cmbP4S4.BackColor = SystemColors.ButtonFace;
    this.cmbP4S4.FormattingEnabled = true;
    this.cmbP4S4.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP4S4_1 = this.cmbP4S4;
    point1 = new Point(496, 98);
    Point point160 = point1;
    cmbP4S4_1.Location = point160;
    this.cmbP4S4.Name = "cmbP4S4";
    ComboBox cmbP4S4_2 = this.cmbP4S4;
    size1 = new Size(91, 21);
    Size size160 = size1;
    cmbP4S4_2.Size = size160;
    this.cmbP4S4.TabIndex = 62;
    this.cmbP4S4.Visible = false;
    this.cmbP3S4.BackColor = SystemColors.ButtonFace;
    this.cmbP3S4.FormattingEnabled = true;
    this.cmbP3S4.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP3S4_1 = this.cmbP3S4;
    point1 = new Point(496, 71);
    Point point161 = point1;
    cmbP3S4_1.Location = point161;
    this.cmbP3S4.Name = "cmbP3S4";
    ComboBox cmbP3S4_2 = this.cmbP3S4;
    size1 = new Size(91, 21);
    Size size161 = size1;
    cmbP3S4_2.Size = size161;
    this.cmbP3S4.TabIndex = 45;
    this.cmbP3S4.Visible = false;
    this.cmbP2S4.BackColor = SystemColors.ButtonFace;
    this.cmbP2S4.FormattingEnabled = true;
    this.cmbP2S4.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP2S4_1 = this.cmbP2S4;
    point1 = new Point(496, 44);
    Point point162 = point1;
    cmbP2S4_1.Location = point162;
    this.cmbP2S4.Name = "cmbP2S4";
    ComboBox cmbP2S4_2 = this.cmbP2S4;
    size1 = new Size(91, 21);
    Size size162 = size1;
    cmbP2S4_2.Size = size162;
    this.cmbP2S4.TabIndex = 28;
    this.cmbP2S4.Visible = false;
    this.cmbP1S4.BackColor = SystemColors.ButtonFace;
    this.cmbP1S4.FormattingEnabled = true;
    this.cmbP1S4.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP1S4_1 = this.cmbP1S4;
    point1 = new Point(496, 17);
    Point point163 = point1;
    cmbP1S4_1.Location = point163;
    this.cmbP1S4.Name = "cmbP1S4";
    ComboBox cmbP1S4_2 = this.cmbP1S4;
    size1 = new Size(91, 21);
    Size size163 = size1;
    cmbP1S4_2.Size = size163;
    this.cmbP1S4.TabIndex = 11;
    this.cmbP1S4.Visible = false;
    this.btnP16S3.BackColor = Color.Tomato;
    Button btnP16S3_1 = this.btnP16S3;
    point1 = new Point(459, 423);
    Point point164 = point1;
    btnP16S3_1.Location = point164;
    this.btnP16S3.Name = "btnP16S3";
    Button btnP16S3_2 = this.btnP16S3;
    size1 = new Size(20, 17);
    Size size164 = size1;
    btnP16S3_2.Size = size164;
    this.btnP16S3.TabIndex = 264;
    this.btnP16S3.Text = " ";
    this.btnP16S3.UseVisualStyleBackColor = false;
    this.btnP16S3.Visible = false;
    this.btnP15S3.BackColor = Color.Tomato;
    Button btnP15S3_1 = this.btnP15S3;
    point1 = new Point(459, 396);
    Point point165 = point1;
    btnP15S3_1.Location = point165;
    this.btnP15S3.Name = "btnP15S3";
    Button btnP15S3_2 = this.btnP15S3;
    size1 = new Size(19, 17);
    Size size165 = size1;
    btnP15S3_2.Size = size165;
    this.btnP15S3.TabIndex = 247;
    this.btnP15S3.Text = " ";
    this.btnP15S3.UseVisualStyleBackColor = false;
    this.btnP15S3.Visible = false;
    this.btnP14S3.BackColor = Color.Tomato;
    Button btnP14S3_1 = this.btnP14S3;
    point1 = new Point(458, 371);
    Point point166 = point1;
    btnP14S3_1.Location = point166;
    this.btnP14S3.Name = "btnP14S3";
    Button btnP14S3_2 = this.btnP14S3;
    size1 = new Size(20, 17);
    Size size166 = size1;
    btnP14S3_2.Size = size166;
    this.btnP14S3.TabIndex = 230;
    this.btnP14S3.Text = " ";
    this.btnP14S3.UseVisualStyleBackColor = false;
    this.btnP14S3.Visible = false;
    this.btnP13S3.BackColor = Color.Tomato;
    Button btnP13S3_1 = this.btnP13S3;
    point1 = new Point(459, 342);
    Point point167 = point1;
    btnP13S3_1.Location = point167;
    this.btnP13S3.Name = "btnP13S3";
    Button btnP13S3_2 = this.btnP13S3;
    size1 = new Size(19, 17);
    Size size167 = size1;
    btnP13S3_2.Size = size167;
    this.btnP13S3.TabIndex = 213;
    this.btnP13S3.Text = " ";
    this.btnP13S3.UseVisualStyleBackColor = false;
    this.btnP13S3.Visible = false;
    this.btnP12S3.BackColor = Color.Tomato;
    Button btnP12S3_1 = this.btnP12S3;
    point1 = new Point(459, 313);
    Point point168 = point1;
    btnP12S3_1.Location = point168;
    this.btnP12S3.Name = "btnP12S3";
    Button btnP12S3_2 = this.btnP12S3;
    size1 = new Size(19, 19);
    Size size168 = size1;
    btnP12S3_2.Size = size168;
    this.btnP12S3.TabIndex = 196;
    this.btnP12S3.Text = " ";
    this.btnP12S3.UseVisualStyleBackColor = false;
    this.btnP12S3.Visible = false;
    this.btnP11S3.BackColor = Color.Tomato;
    Button btnP11S3_1 = this.btnP11S3;
    point1 = new Point(459, 288);
    Point point169 = point1;
    btnP11S3_1.Location = point169;
    this.btnP11S3.Name = "btnP11S3";
    Button btnP11S3_2 = this.btnP11S3;
    size1 = new Size(19, 17);
    Size size169 = size1;
    btnP11S3_2.Size = size169;
    this.btnP11S3.TabIndex = 179;
    this.btnP11S3.Text = " ";
    this.btnP11S3.UseVisualStyleBackColor = false;
    this.btnP11S3.Visible = false;
    this.btnP10S3.BackColor = Color.Tomato;
    Button btnP10S3_1 = this.btnP10S3;
    point1 = new Point(459, 261);
    Point point170 = point1;
    btnP10S3_1.Location = point170;
    this.btnP10S3.Name = "btnP10S3";
    Button btnP10S3_2 = this.btnP10S3;
    size1 = new Size(19, 17);
    Size size170 = size1;
    btnP10S3_2.Size = size170;
    this.btnP10S3.TabIndex = 163;
    this.btnP10S3.Text = " ";
    this.btnP10S3.UseVisualStyleBackColor = false;
    this.btnP10S3.Visible = false;
    this.btnP9S3.BackColor = Color.Tomato;
    Button btnP9S3_1 = this.btnP9S3;
    point1 = new Point(459, 234);
    Point point171 = point1;
    btnP9S3_1.Location = point171;
    this.btnP9S3.Name = "btnP9S3";
    Button btnP9S3_2 = this.btnP9S3;
    size1 = new Size(19, 17);
    Size size171 = size1;
    btnP9S3_2.Size = size171;
    this.btnP9S3.TabIndex = 146;
    this.btnP9S3.Text = " ";
    this.btnP9S3.UseVisualStyleBackColor = false;
    this.btnP9S3.Visible = false;
    this.btnP8S3.BackColor = Color.Tomato;
    Button btnP8S3_1 = this.btnP8S3;
    point1 = new Point(459, 209);
    Point point172 = point1;
    btnP8S3_1.Location = point172;
    this.btnP8S3.Name = "btnP8S3";
    Button btnP8S3_2 = this.btnP8S3;
    size1 = new Size(17, 18);
    Size size172 = size1;
    btnP8S3_2.Size = size172;
    this.btnP8S3.TabIndex = 129;
    this.btnP8S3.Text = " ";
    this.btnP8S3.UseVisualStyleBackColor = false;
    this.btnP8S3.Visible = false;
    this.btnP7S3.BackColor = Color.Tomato;
    Button btnP7S3_1 = this.btnP7S3;
    point1 = new Point(459, 180);
    Point point173 = point1;
    btnP7S3_1.Location = point173;
    this.btnP7S3.Name = "btnP7S3";
    Button btnP7S3_2 = this.btnP7S3;
    size1 = new Size(17, 17);
    Size size173 = size1;
    btnP7S3_2.Size = size173;
    this.btnP7S3.TabIndex = 112 /*0x70*/;
    this.btnP7S3.Text = " ";
    this.btnP7S3.UseVisualStyleBackColor = false;
    this.btnP7S3.Visible = false;
    this.btnP6S3.BackColor = Color.Tomato;
    Button btnP6S3_1 = this.btnP6S3;
    point1 = new Point(459, 151);
    Point point174 = point1;
    btnP6S3_1.Location = point174;
    this.btnP6S3.Name = "btnP6S3";
    Button btnP6S3_2 = this.btnP6S3;
    size1 = new Size(17, 17);
    Size size174 = size1;
    btnP6S3_2.Size = size174;
    this.btnP6S3.TabIndex = 95;
    this.btnP6S3.Text = " ";
    this.btnP6S3.UseVisualStyleBackColor = false;
    this.btnP6S3.Visible = false;
    this.btnP5S3.BackColor = Color.Tomato;
    Button btnP5S3_1 = this.btnP5S3;
    point1 = new Point(459, 126);
    Point point175 = point1;
    btnP5S3_1.Location = point175;
    this.btnP5S3.Name = "btnP5S3";
    Button btnP5S3_2 = this.btnP5S3;
    size1 = new Size(17, 17);
    Size size175 = size1;
    btnP5S3_2.Size = size175;
    this.btnP5S3.TabIndex = 78;
    this.btnP5S3.Text = " ";
    this.btnP5S3.UseVisualStyleBackColor = false;
    this.btnP5S3.Visible = false;
    this.btnP4S3.BackColor = Color.Tomato;
    Button btnP4S3_1 = this.btnP4S3;
    point1 = new Point(459, 99);
    Point point176 = point1;
    btnP4S3_1.Location = point176;
    this.btnP4S3.Name = "btnP4S3";
    Button btnP4S3_2 = this.btnP4S3;
    size1 = new Size(17, 19);
    Size size176 = size1;
    btnP4S3_2.Size = size176;
    this.btnP4S3.TabIndex = 61;
    this.btnP4S3.Text = " ";
    this.btnP4S3.UseVisualStyleBackColor = false;
    this.btnP4S3.Visible = false;
    this.btnP3S3.BackColor = Color.Tomato;
    Button btnP3S3_1 = this.btnP3S3;
    point1 = new Point(459, 72);
    Point point177 = point1;
    btnP3S3_1.Location = point177;
    this.btnP3S3.Name = "btnP3S3";
    Button btnP3S3_2 = this.btnP3S3;
    size1 = new Size(17, 19);
    Size size177 = size1;
    btnP3S3_2.Size = size177;
    this.btnP3S3.TabIndex = 44;
    this.btnP3S3.Text = " ";
    this.btnP3S3.UseVisualStyleBackColor = false;
    this.btnP3S3.Visible = false;
    this.btnP2S3.BackColor = Color.Tomato;
    Button btnP2S3_1 = this.btnP2S3;
    point1 = new Point(459, 43);
    Point point178 = point1;
    btnP2S3_1.Location = point178;
    this.btnP2S3.Name = "btnP2S3";
    Button btnP2S3_2 = this.btnP2S3;
    size1 = new Size(17, 19);
    Size size178 = size1;
    btnP2S3_2.Size = size178;
    this.btnP2S3.TabIndex = 27;
    this.btnP2S3.Text = " ";
    this.btnP2S3.UseVisualStyleBackColor = false;
    this.btnP2S3.Visible = false;
    this.btnP1S3.BackColor = Color.Tomato;
    Button btnP1S3_1 = this.btnP1S3;
    point1 = new Point(459, 16 /*0x10*/);
    Point point179 = point1;
    btnP1S3_1.Location = point179;
    this.btnP1S3.Name = "btnP1S3";
    Button btnP1S3_2 = this.btnP1S3;
    size1 = new Size(17, 19);
    Size size179 = size1;
    btnP1S3_2.Size = size179;
    this.btnP1S3.TabIndex = 10;
    this.btnP1S3.UseVisualStyleBackColor = false;
    this.btnP1S3.Visible = false;
    this.cmbP16S3.BackColor = SystemColors.ButtonFace;
    this.cmbP16S3.FormattingEnabled = true;
    this.cmbP16S3.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP16S3_1 = this.cmbP16S3;
    point1 = new Point(360, 419);
    Point point180 = point1;
    cmbP16S3_1.Location = point180;
    this.cmbP16S3.Name = "cmbP16S3";
    ComboBox cmbP16S3_2 = this.cmbP16S3;
    size1 = new Size(91, 21);
    Size size180 = size1;
    cmbP16S3_2.Size = size180;
    this.cmbP16S3.TabIndex = 263;
    this.cmbP16S3.Visible = false;
    this.cmbP15S3.BackColor = SystemColors.ButtonFace;
    this.cmbP15S3.FormattingEnabled = true;
    this.cmbP15S3.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP15S3_1 = this.cmbP15S3;
    point1 = new Point(360, 392);
    Point point181 = point1;
    cmbP15S3_1.Location = point181;
    this.cmbP15S3.Name = "cmbP15S3";
    ComboBox cmbP15S3_2 = this.cmbP15S3;
    size1 = new Size(91, 21);
    Size size181 = size1;
    cmbP15S3_2.Size = size181;
    this.cmbP15S3.TabIndex = 246;
    this.cmbP15S3.Visible = false;
    this.cmbP14S3.BackColor = SystemColors.ButtonFace;
    this.cmbP14S3.FormattingEnabled = true;
    this.cmbP14S3.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP14S3_1 = this.cmbP14S3;
    point1 = new Point(360, 365);
    Point point182 = point1;
    cmbP14S3_1.Location = point182;
    this.cmbP14S3.Name = "cmbP14S3";
    ComboBox cmbP14S3_2 = this.cmbP14S3;
    size1 = new Size(91, 21);
    Size size182 = size1;
    cmbP14S3_2.Size = size182;
    this.cmbP14S3.TabIndex = 229;
    this.cmbP14S3.Visible = false;
    this.cmbP13S3.BackColor = SystemColors.ButtonFace;
    this.cmbP13S3.FormattingEnabled = true;
    this.cmbP13S3.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP13S3_1 = this.cmbP13S3;
    point1 = new Point(360, 338);
    Point point183 = point1;
    cmbP13S3_1.Location = point183;
    this.cmbP13S3.Name = "cmbP13S3";
    ComboBox cmbP13S3_2 = this.cmbP13S3;
    size1 = new Size(91, 21);
    Size size183 = size1;
    cmbP13S3_2.Size = size183;
    this.cmbP13S3.TabIndex = 213;
    this.cmbP13S3.Visible = false;
    this.cmbP12S3.BackColor = SystemColors.ButtonFace;
    this.cmbP12S3.FormattingEnabled = true;
    this.cmbP12S3.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP12S3_1 = this.cmbP12S3;
    point1 = new Point(360, 311);
    Point point184 = point1;
    cmbP12S3_1.Location = point184;
    this.cmbP12S3.Name = "cmbP12S3";
    ComboBox cmbP12S3_2 = this.cmbP12S3;
    size1 = new Size(91, 21);
    Size size184 = size1;
    cmbP12S3_2.Size = size184;
    this.cmbP12S3.TabIndex = 196;
    this.cmbP12S3.Visible = false;
    this.cmbP11S3.BackColor = SystemColors.ButtonFace;
    this.cmbP11S3.FormattingEnabled = true;
    this.cmbP11S3.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP11S3_1 = this.cmbP11S3;
    point1 = new Point(360, 284);
    Point point185 = point1;
    cmbP11S3_1.Location = point185;
    this.cmbP11S3.Name = "cmbP11S3";
    ComboBox cmbP11S3_2 = this.cmbP11S3;
    size1 = new Size(91, 21);
    Size size185 = size1;
    cmbP11S3_2.Size = size185;
    this.cmbP11S3.TabIndex = 179;
    this.cmbP11S3.Visible = false;
    this.cmbP10S3.BackColor = SystemColors.ButtonFace;
    this.cmbP10S3.FormattingEnabled = true;
    this.cmbP10S3.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP10S3_1 = this.cmbP10S3;
    point1 = new Point(360, 257);
    Point point186 = point1;
    cmbP10S3_1.Location = point186;
    this.cmbP10S3.Name = "cmbP10S3";
    ComboBox cmbP10S3_2 = this.cmbP10S3;
    size1 = new Size(91, 21);
    Size size186 = size1;
    cmbP10S3_2.Size = size186;
    this.cmbP10S3.TabIndex = 162;
    this.cmbP10S3.Visible = false;
    this.cmbP9S3.BackColor = SystemColors.ButtonFace;
    this.cmbP9S3.FormattingEnabled = true;
    this.cmbP9S3.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP9S3_1 = this.cmbP9S3;
    point1 = new Point(360, 230);
    Point point187 = point1;
    cmbP9S3_1.Location = point187;
    this.cmbP9S3.Name = "cmbP9S3";
    ComboBox cmbP9S3_2 = this.cmbP9S3;
    size1 = new Size(91, 21);
    Size size187 = size1;
    cmbP9S3_2.Size = size187;
    this.cmbP9S3.TabIndex = 145;
    this.cmbP9S3.Visible = false;
    this.cmbP8S3.BackColor = SystemColors.ButtonFace;
    this.cmbP8S3.FormattingEnabled = true;
    this.cmbP8S3.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP8S3_1 = this.cmbP8S3;
    point1 = new Point(360, 203);
    Point point188 = point1;
    cmbP8S3_1.Location = point188;
    this.cmbP8S3.Name = "cmbP8S3";
    ComboBox cmbP8S3_2 = this.cmbP8S3;
    size1 = new Size(91, 21);
    Size size188 = size1;
    cmbP8S3_2.Size = size188;
    this.cmbP8S3.TabIndex = 128 /*0x80*/;
    this.cmbP8S3.Visible = false;
    this.cmbP7S3.BackColor = SystemColors.ButtonFace;
    this.cmbP7S3.FormattingEnabled = true;
    this.cmbP7S3.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP7S3_1 = this.cmbP7S3;
    point1 = new Point(360, 176 /*0xB0*/);
    Point point189 = point1;
    cmbP7S3_1.Location = point189;
    this.cmbP7S3.Name = "cmbP7S3";
    ComboBox cmbP7S3_2 = this.cmbP7S3;
    size1 = new Size(91, 21);
    Size size189 = size1;
    cmbP7S3_2.Size = size189;
    this.cmbP7S3.TabIndex = 112 /*0x70*/;
    this.cmbP7S3.Visible = false;
    this.cmbP6S3.BackColor = SystemColors.ButtonFace;
    this.cmbP6S3.FormattingEnabled = true;
    this.cmbP6S3.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP6S3_1 = this.cmbP6S3;
    point1 = new Point(360, 149);
    Point point190 = point1;
    cmbP6S3_1.Location = point190;
    this.cmbP6S3.Name = "cmbP6S3";
    ComboBox cmbP6S3_2 = this.cmbP6S3;
    size1 = new Size(91, 21);
    Size size190 = size1;
    cmbP6S3_2.Size = size190;
    this.cmbP6S3.TabIndex = 94;
    this.cmbP6S3.Visible = false;
    this.cmbP5S3.BackColor = SystemColors.ButtonFace;
    this.cmbP5S3.FormattingEnabled = true;
    this.cmbP5S3.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP5S3_1 = this.cmbP5S3;
    point1 = new Point(360, 122);
    Point point191 = point1;
    cmbP5S3_1.Location = point191;
    this.cmbP5S3.Name = "cmbP5S3";
    ComboBox cmbP5S3_2 = this.cmbP5S3;
    size1 = new Size(91, 21);
    Size size191 = size1;
    cmbP5S3_2.Size = size191;
    this.cmbP5S3.TabIndex = 77;
    this.cmbP5S3.Visible = false;
    this.cmbP4S3.BackColor = SystemColors.ButtonFace;
    this.cmbP4S3.FormattingEnabled = true;
    this.cmbP4S3.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP4S3_1 = this.cmbP4S3;
    point1 = new Point(360, 95);
    Point point192 = point1;
    cmbP4S3_1.Location = point192;
    this.cmbP4S3.Name = "cmbP4S3";
    ComboBox cmbP4S3_2 = this.cmbP4S3;
    size1 = new Size(91, 21);
    Size size192 = size1;
    cmbP4S3_2.Size = size192;
    this.cmbP4S3.TabIndex = 60;
    this.cmbP4S3.Visible = false;
    this.cmbP3S3.BackColor = SystemColors.ButtonFace;
    this.cmbP3S3.FormattingEnabled = true;
    this.cmbP3S3.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP3S3_1 = this.cmbP3S3;
    point1 = new Point(360, 68);
    Point point193 = point1;
    cmbP3S3_1.Location = point193;
    this.cmbP3S3.Name = "cmbP3S3";
    ComboBox cmbP3S3_2 = this.cmbP3S3;
    size1 = new Size(91, 21);
    Size size193 = size1;
    cmbP3S3_2.Size = size193;
    this.cmbP3S3.TabIndex = 43;
    this.cmbP3S3.Visible = false;
    this.cmbP2S3.BackColor = SystemColors.ButtonFace;
    this.cmbP2S3.FormattingEnabled = true;
    this.cmbP2S3.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP2S3_1 = this.cmbP2S3;
    point1 = new Point(360, 41);
    Point point194 = point1;
    cmbP2S3_1.Location = point194;
    this.cmbP2S3.Name = "cmbP2S3";
    ComboBox cmbP2S3_2 = this.cmbP2S3;
    size1 = new Size(91, 21);
    Size size194 = size1;
    cmbP2S3_2.Size = size194;
    this.cmbP2S3.TabIndex = 26;
    this.cmbP2S3.Visible = false;
    this.cmbP1S3.BackColor = SystemColors.ButtonFace;
    this.cmbP1S3.FormattingEnabled = true;
    this.cmbP1S3.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP1S3_1 = this.cmbP1S3;
    point1 = new Point(360, 14);
    Point point195 = point1;
    cmbP1S3_1.Location = point195;
    this.cmbP1S3.Name = "cmbP1S3";
    ComboBox cmbP1S3_2 = this.cmbP1S3;
    size1 = new Size(91, 21);
    Size size195 = size1;
    cmbP1S3_2.Size = size195;
    this.cmbP1S3.TabIndex = 9;
    this.cmbP1S3.Visible = false;
    this.btnP16S2.BackColor = Color.Tomato;
    Button btnP16S2_1 = this.btnP16S2;
    point1 = new Point(321, 423);
    Point point196 = point1;
    btnP16S2_1.Location = point196;
    this.btnP16S2.Name = "btnP16S2";
    Button btnP16S2_2 = this.btnP16S2;
    size1 = new Size(20, 17);
    Size size196 = size1;
    btnP16S2_2.Size = size196;
    this.btnP16S2.TabIndex = 262;
    this.btnP16S2.Text = " ";
    this.btnP16S2.UseVisualStyleBackColor = false;
    this.btnP16S2.Visible = false;
    this.btnP15S2.BackColor = Color.Tomato;
    Button btnP15S2_1 = this.btnP15S2;
    point1 = new Point(321, 396);
    Point point197 = point1;
    btnP15S2_1.Location = point197;
    this.btnP15S2.Name = "btnP15S2";
    Button btnP15S2_2 = this.btnP15S2;
    size1 = new Size(19, 17);
    Size size197 = size1;
    btnP15S2_2.Size = size197;
    this.btnP15S2.TabIndex = 245;
    this.btnP15S2.Text = " ";
    this.btnP15S2.UseVisualStyleBackColor = false;
    this.btnP15S2.Visible = false;
    this.btnP14S2.BackColor = Color.Tomato;
    Button btnP14S2_1 = this.btnP14S2;
    point1 = new Point(320, 371);
    Point point198 = point1;
    btnP14S2_1.Location = point198;
    this.btnP14S2.Name = "btnP14S2";
    Button btnP14S2_2 = this.btnP14S2;
    size1 = new Size(20, 17);
    Size size198 = size1;
    btnP14S2_2.Size = size198;
    this.btnP14S2.TabIndex = 228;
    this.btnP14S2.Text = " ";
    this.btnP14S2.UseVisualStyleBackColor = false;
    this.btnP14S2.Visible = false;
    this.btnP13S2.BackColor = Color.Tomato;
    Button btnP13S2_1 = this.btnP13S2;
    point1 = new Point(321, 342);
    Point point199 = point1;
    btnP13S2_1.Location = point199;
    this.btnP13S2.Name = "btnP13S2";
    Button btnP13S2_2 = this.btnP13S2;
    size1 = new Size(19, 17);
    Size size199 = size1;
    btnP13S2_2.Size = size199;
    this.btnP13S2.TabIndex = 211;
    this.btnP13S2.Text = " ";
    this.btnP13S2.UseVisualStyleBackColor = false;
    this.btnP13S2.Visible = false;
    this.btnP12S2.BackColor = Color.Tomato;
    Button btnP12S2_1 = this.btnP12S2;
    point1 = new Point(321, 313);
    Point point200 = point1;
    btnP12S2_1.Location = point200;
    this.btnP12S2.Name = "btnP12S2";
    Button btnP12S2_2 = this.btnP12S2;
    size1 = new Size(19, 19);
    Size size200 = size1;
    btnP12S2_2.Size = size200;
    this.btnP12S2.TabIndex = 194;
    this.btnP12S2.Text = " ";
    this.btnP12S2.UseVisualStyleBackColor = false;
    this.btnP12S2.Visible = false;
    this.btnP11S2.BackColor = Color.Tomato;
    Button btnP11S2_1 = this.btnP11S2;
    point1 = new Point(321, 288);
    Point point201 = point1;
    btnP11S2_1.Location = point201;
    this.btnP11S2.Name = "btnP11S2";
    Button btnP11S2_2 = this.btnP11S2;
    size1 = new Size(19, 17);
    Size size201 = size1;
    btnP11S2_2.Size = size201;
    this.btnP11S2.TabIndex = 177;
    this.btnP11S2.Text = " ";
    this.btnP11S2.UseVisualStyleBackColor = false;
    this.btnP11S2.Visible = false;
    this.btnP10S2.BackColor = Color.Tomato;
    Button btnP10S2_1 = this.btnP10S2;
    point1 = new Point(321, 261);
    Point point202 = point1;
    btnP10S2_1.Location = point202;
    this.btnP10S2.Name = "btnP10S2";
    Button btnP10S2_2 = this.btnP10S2;
    size1 = new Size(19, 17);
    Size size202 = size1;
    btnP10S2_2.Size = size202;
    this.btnP10S2.TabIndex = 161;
    this.btnP10S2.Text = " ";
    this.btnP10S2.UseVisualStyleBackColor = false;
    this.btnP10S2.Visible = false;
    this.btnP9S2.BackColor = Color.Tomato;
    Button btnP9S2_1 = this.btnP9S2;
    point1 = new Point(321, 234);
    Point point203 = point1;
    btnP9S2_1.Location = point203;
    this.btnP9S2.Name = "btnP9S2";
    Button btnP9S2_2 = this.btnP9S2;
    size1 = new Size(19, 17);
    Size size203 = size1;
    btnP9S2_2.Size = size203;
    this.btnP9S2.TabIndex = 144 /*0x90*/;
    this.btnP9S2.Text = " ";
    this.btnP9S2.UseVisualStyleBackColor = false;
    this.btnP9S2.Visible = false;
    this.btnP8S2.BackColor = Color.Tomato;
    Button btnP8S2_1 = this.btnP8S2;
    point1 = new Point(321, 209);
    Point point204 = point1;
    btnP8S2_1.Location = point204;
    this.btnP8S2.Name = "btnP8S2";
    Button btnP8S2_2 = this.btnP8S2;
    size1 = new Size(17, 18);
    Size size204 = size1;
    btnP8S2_2.Size = size204;
    this.btnP8S2.TabIndex = (int) sbyte.MaxValue;
    this.btnP8S2.Text = " ";
    this.btnP8S2.UseVisualStyleBackColor = false;
    this.btnP8S2.Visible = false;
    this.btnP7S2.BackColor = Color.Tomato;
    Button btnP7S2_1 = this.btnP7S2;
    point1 = new Point(321, 180);
    Point point205 = point1;
    btnP7S2_1.Location = point205;
    this.btnP7S2.Name = "btnP7S2";
    Button btnP7S2_2 = this.btnP7S2;
    size1 = new Size(17, 17);
    Size size205 = size1;
    btnP7S2_2.Size = size205;
    this.btnP7S2.TabIndex = 110;
    this.btnP7S2.Text = " ";
    this.btnP7S2.UseVisualStyleBackColor = false;
    this.btnP7S2.Visible = false;
    this.btnP6S2.BackColor = Color.Tomato;
    Button btnP6S2_1 = this.btnP6S2;
    point1 = new Point(321, 151);
    Point point206 = point1;
    btnP6S2_1.Location = point206;
    this.btnP6S2.Name = "btnP6S2";
    Button btnP6S2_2 = this.btnP6S2;
    size1 = new Size(17, 17);
    Size size206 = size1;
    btnP6S2_2.Size = size206;
    this.btnP6S2.TabIndex = 93;
    this.btnP6S2.Text = " ";
    this.btnP6S2.UseVisualStyleBackColor = false;
    this.btnP6S2.Visible = false;
    this.btnP5S2.BackColor = Color.Tomato;
    Button btnP5S2_1 = this.btnP5S2;
    point1 = new Point(321, 126);
    Point point207 = point1;
    btnP5S2_1.Location = point207;
    this.btnP5S2.Name = "btnP5S2";
    Button btnP5S2_2 = this.btnP5S2;
    size1 = new Size(17, 17);
    Size size207 = size1;
    btnP5S2_2.Size = size207;
    this.btnP5S2.TabIndex = 76;
    this.btnP5S2.Text = " ";
    this.btnP5S2.UseVisualStyleBackColor = false;
    this.btnP5S2.Visible = false;
    this.btnP4S2.BackColor = Color.Tomato;
    Button btnP4S2_1 = this.btnP4S2;
    point1 = new Point(321, 99);
    Point point208 = point1;
    btnP4S2_1.Location = point208;
    this.btnP4S2.Name = "btnP4S2";
    Button btnP4S2_2 = this.btnP4S2;
    size1 = new Size(17, 19);
    Size size208 = size1;
    btnP4S2_2.Size = size208;
    this.btnP4S2.TabIndex = 59;
    this.btnP4S2.Text = " ";
    this.btnP4S2.UseVisualStyleBackColor = false;
    this.btnP4S2.Visible = false;
    this.btnP3S2.BackColor = Color.Tomato;
    Button btnP3S2_1 = this.btnP3S2;
    point1 = new Point(321, 72);
    Point point209 = point1;
    btnP3S2_1.Location = point209;
    this.btnP3S2.Name = "btnP3S2";
    Button btnP3S2_2 = this.btnP3S2;
    size1 = new Size(17, 19);
    Size size209 = size1;
    btnP3S2_2.Size = size209;
    this.btnP3S2.TabIndex = 42;
    this.btnP3S2.Text = " ";
    this.btnP3S2.UseVisualStyleBackColor = false;
    this.btnP3S2.Visible = false;
    this.btnP2S2.BackColor = Color.Tomato;
    Button btnP2S2_1 = this.btnP2S2;
    point1 = new Point(321, 43);
    Point point210 = point1;
    btnP2S2_1.Location = point210;
    this.btnP2S2.Name = "btnP2S2";
    Button btnP2S2_2 = this.btnP2S2;
    size1 = new Size(17, 19);
    Size size210 = size1;
    btnP2S2_2.Size = size210;
    this.btnP2S2.TabIndex = 25;
    this.btnP2S2.Text = " ";
    this.btnP2S2.UseVisualStyleBackColor = false;
    this.btnP2S2.Visible = false;
    this.btnP1S2.BackColor = Color.Tomato;
    Button btnP1S2_1 = this.btnP1S2;
    point1 = new Point(321, 16 /*0x10*/);
    Point point211 = point1;
    btnP1S2_1.Location = point211;
    this.btnP1S2.Name = "btnP1S2";
    Button btnP1S2_2 = this.btnP1S2;
    size1 = new Size(17, 19);
    Size size211 = size1;
    btnP1S2_2.Size = size211;
    this.btnP1S2.TabIndex = 8;
    this.btnP1S2.UseVisualStyleBackColor = false;
    this.btnP1S2.Visible = false;
    this.cmbP16S2.BackColor = SystemColors.ButtonFace;
    this.cmbP16S2.FormattingEnabled = true;
    this.cmbP16S2.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP16S2_1 = this.cmbP16S2;
    point1 = new Point(222, 420);
    Point point212 = point1;
    cmbP16S2_1.Location = point212;
    this.cmbP16S2.Name = "cmbP16S2";
    ComboBox cmbP16S2_2 = this.cmbP16S2;
    size1 = new Size(91, 21);
    Size size212 = size1;
    cmbP16S2_2.Size = size212;
    this.cmbP16S2.TabIndex = 262;
    this.cmbP16S2.Visible = false;
    this.cmbP15S2.BackColor = SystemColors.ButtonFace;
    this.cmbP15S2.FormattingEnabled = true;
    this.cmbP15S2.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP15S2_1 = this.cmbP15S2;
    point1 = new Point(222, 393);
    Point point213 = point1;
    cmbP15S2_1.Location = point213;
    this.cmbP15S2.Name = "cmbP15S2";
    ComboBox cmbP15S2_2 = this.cmbP15S2;
    size1 = new Size(91, 21);
    Size size213 = size1;
    cmbP15S2_2.Size = size213;
    this.cmbP15S2.TabIndex = 244;
    this.cmbP15S2.Visible = false;
    this.cmbP14S2.BackColor = SystemColors.ButtonFace;
    this.cmbP14S2.FormattingEnabled = true;
    this.cmbP14S2.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP14S2_1 = this.cmbP14S2;
    point1 = new Point(222, 366);
    Point point214 = point1;
    cmbP14S2_1.Location = point214;
    this.cmbP14S2.Name = "cmbP14S2";
    ComboBox cmbP14S2_2 = this.cmbP14S2;
    size1 = new Size(91, 21);
    Size size214 = size1;
    cmbP14S2_2.Size = size214;
    this.cmbP14S2.TabIndex = 227;
    this.cmbP14S2.Visible = false;
    this.cmbP13S2.BackColor = SystemColors.ButtonFace;
    this.cmbP13S2.FormattingEnabled = true;
    this.cmbP13S2.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP13S2_1 = this.cmbP13S2;
    point1 = new Point(222, 339);
    Point point215 = point1;
    cmbP13S2_1.Location = point215;
    this.cmbP13S2.Name = "cmbP13S2";
    ComboBox cmbP13S2_2 = this.cmbP13S2;
    size1 = new Size(91, 21);
    Size size215 = size1;
    cmbP13S2_2.Size = size215;
    this.cmbP13S2.TabIndex = 211;
    this.cmbP13S2.Visible = false;
    this.cmbP12S2.BackColor = SystemColors.ButtonFace;
    this.cmbP12S2.FormattingEnabled = true;
    this.cmbP12S2.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP12S2_1 = this.cmbP12S2;
    point1 = new Point(222, 312);
    Point point216 = point1;
    cmbP12S2_1.Location = point216;
    this.cmbP12S2.Name = "cmbP12S2";
    ComboBox cmbP12S2_2 = this.cmbP12S2;
    size1 = new Size(91, 21);
    Size size216 = size1;
    cmbP12S2_2.Size = size216;
    this.cmbP12S2.TabIndex = 194;
    this.cmbP12S2.Visible = false;
    this.cmbP11S2.BackColor = SystemColors.ButtonFace;
    this.cmbP11S2.FormattingEnabled = true;
    this.cmbP11S2.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP11S2_1 = this.cmbP11S2;
    point1 = new Point(222, 285);
    Point point217 = point1;
    cmbP11S2_1.Location = point217;
    this.cmbP11S2.Name = "cmbP11S2";
    ComboBox cmbP11S2_2 = this.cmbP11S2;
    size1 = new Size(91, 21);
    Size size217 = size1;
    cmbP11S2_2.Size = size217;
    this.cmbP11S2.TabIndex = 177;
    this.cmbP11S2.Visible = false;
    this.cmbP10S2.BackColor = SystemColors.ButtonFace;
    this.cmbP10S2.FormattingEnabled = true;
    this.cmbP10S2.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP10S2_1 = this.cmbP10S2;
    point1 = new Point(222, 258);
    Point point218 = point1;
    cmbP10S2_1.Location = point218;
    this.cmbP10S2.Name = "cmbP10S2";
    ComboBox cmbP10S2_2 = this.cmbP10S2;
    size1 = new Size(91, 21);
    Size size218 = size1;
    cmbP10S2_2.Size = size218;
    this.cmbP10S2.TabIndex = 160 /*0xA0*/;
    this.cmbP10S2.Visible = false;
    this.cmbP9S2.BackColor = SystemColors.ButtonFace;
    this.cmbP9S2.FormattingEnabled = true;
    this.cmbP9S2.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP9S2_1 = this.cmbP9S2;
    point1 = new Point(222, 231);
    Point point219 = point1;
    cmbP9S2_1.Location = point219;
    this.cmbP9S2.Name = "cmbP9S2";
    ComboBox cmbP9S2_2 = this.cmbP9S2;
    size1 = new Size(91, 21);
    Size size219 = size1;
    cmbP9S2_2.Size = size219;
    this.cmbP9S2.TabIndex = 143;
    this.cmbP9S2.Visible = false;
    this.cmbP8S2.BackColor = SystemColors.ButtonFace;
    this.cmbP8S2.FormattingEnabled = true;
    this.cmbP8S2.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP8S2_1 = this.cmbP8S2;
    point1 = new Point(222, 204);
    Point point220 = point1;
    cmbP8S2_1.Location = point220;
    this.cmbP8S2.Name = "cmbP8S2";
    ComboBox cmbP8S2_2 = this.cmbP8S2;
    size1 = new Size(91, 21);
    Size size220 = size1;
    cmbP8S2_2.Size = size220;
    this.cmbP8S2.TabIndex = 126;
    this.cmbP8S2.Visible = false;
    this.cmbP7S2.BackColor = SystemColors.ButtonFace;
    this.cmbP7S2.FormattingEnabled = true;
    this.cmbP7S2.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP7S2_1 = this.cmbP7S2;
    point1 = new Point(222, 177);
    Point point221 = point1;
    cmbP7S2_1.Location = point221;
    this.cmbP7S2.Name = "cmbP7S2";
    ComboBox cmbP7S2_2 = this.cmbP7S2;
    size1 = new Size(91, 21);
    Size size221 = size1;
    cmbP7S2_2.Size = size221;
    this.cmbP7S2.TabIndex = 110;
    this.cmbP7S2.Visible = false;
    this.cmbP6S2.BackColor = SystemColors.ButtonFace;
    this.cmbP6S2.FormattingEnabled = true;
    this.cmbP6S2.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP6S2_1 = this.cmbP6S2;
    point1 = new Point(222, 150);
    Point point222 = point1;
    cmbP6S2_1.Location = point222;
    this.cmbP6S2.Name = "cmbP6S2";
    ComboBox cmbP6S2_2 = this.cmbP6S2;
    size1 = new Size(91, 21);
    Size size222 = size1;
    cmbP6S2_2.Size = size222;
    this.cmbP6S2.TabIndex = 92;
    this.cmbP6S2.Visible = false;
    this.cmbP5S2.BackColor = SystemColors.ButtonFace;
    this.cmbP5S2.FormattingEnabled = true;
    this.cmbP5S2.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP5S2_1 = this.cmbP5S2;
    point1 = new Point(222, 123);
    Point point223 = point1;
    cmbP5S2_1.Location = point223;
    this.cmbP5S2.Name = "cmbP5S2";
    ComboBox cmbP5S2_2 = this.cmbP5S2;
    size1 = new Size(91, 21);
    Size size223 = size1;
    cmbP5S2_2.Size = size223;
    this.cmbP5S2.TabIndex = 75;
    this.cmbP5S2.Visible = false;
    this.cmbP4S2.BackColor = SystemColors.ButtonFace;
    this.cmbP4S2.FormattingEnabled = true;
    this.cmbP4S2.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP4S2_1 = this.cmbP4S2;
    point1 = new Point(222, 96 /*0x60*/);
    Point point224 = point1;
    cmbP4S2_1.Location = point224;
    this.cmbP4S2.Name = "cmbP4S2";
    ComboBox cmbP4S2_2 = this.cmbP4S2;
    size1 = new Size(91, 21);
    Size size224 = size1;
    cmbP4S2_2.Size = size224;
    this.cmbP4S2.TabIndex = 58;
    this.cmbP4S2.Visible = false;
    this.cmbP3S2.BackColor = SystemColors.ButtonFace;
    this.cmbP3S2.FormattingEnabled = true;
    this.cmbP3S2.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP3S2_1 = this.cmbP3S2;
    point1 = new Point(222, 69);
    Point point225 = point1;
    cmbP3S2_1.Location = point225;
    this.cmbP3S2.Name = "cmbP3S2";
    ComboBox cmbP3S2_2 = this.cmbP3S2;
    size1 = new Size(91, 21);
    Size size225 = size1;
    cmbP3S2_2.Size = size225;
    this.cmbP3S2.TabIndex = 41;
    this.cmbP3S2.Visible = false;
    this.cmbP2S2.BackColor = SystemColors.ButtonFace;
    this.cmbP2S2.FormattingEnabled = true;
    this.cmbP2S2.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP2S2_1 = this.cmbP2S2;
    point1 = new Point(222, 42);
    Point point226 = point1;
    cmbP2S2_1.Location = point226;
    this.cmbP2S2.Name = "cmbP2S2";
    ComboBox cmbP2S2_2 = this.cmbP2S2;
    size1 = new Size(91, 21);
    Size size226 = size1;
    cmbP2S2_2.Size = size226;
    this.cmbP2S2.TabIndex = 24;
    this.cmbP2S2.Visible = false;
    this.cmbP1S2.BackColor = SystemColors.ButtonFace;
    this.cmbP1S2.FormattingEnabled = true;
    this.cmbP1S2.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP1S2_1 = this.cmbP1S2;
    point1 = new Point(222, 15);
    Point point227 = point1;
    cmbP1S2_1.Location = point227;
    this.cmbP1S2.Name = "cmbP1S2";
    ComboBox cmbP1S2_2 = this.cmbP1S2;
    size1 = new Size(91, 21);
    Size size227 = size1;
    cmbP1S2_2.Size = size227;
    this.cmbP1S2.TabIndex = 7;
    this.cmbP1S2.Visible = false;
    this.btnP16S1.BackColor = Color.Tomato;
    Button btnP16S1_1 = this.btnP16S1;
    point1 = new Point(181, 424);
    Point point228 = point1;
    btnP16S1_1.Location = point228;
    this.btnP16S1.Name = "btnP16S1";
    Button btnP16S1_2 = this.btnP16S1;
    size1 = new Size(20, 17);
    Size size228 = size1;
    btnP16S1_2.Size = size228;
    this.btnP16S1.TabIndex = 260;
    this.btnP16S1.Text = " ";
    this.btnP16S1.UseVisualStyleBackColor = false;
    this.btnP16S1.Visible = false;
    this.btnP15S1.BackColor = Color.Tomato;
    Button btnP15S1_1 = this.btnP15S1;
    point1 = new Point(181, 397);
    Point point229 = point1;
    btnP15S1_1.Location = point229;
    this.btnP15S1.Name = "btnP15S1";
    Button btnP15S1_2 = this.btnP15S1;
    size1 = new Size(19, 17);
    Size size229 = size1;
    btnP15S1_2.Size = size229;
    this.btnP15S1.TabIndex = 243;
    this.btnP15S1.Text = " ";
    this.btnP15S1.UseVisualStyleBackColor = false;
    this.btnP15S1.Visible = false;
    this.btnP14S1.BackColor = Color.Tomato;
    Button btnP14S1_1 = this.btnP14S1;
    point1 = new Point(180, 372);
    Point point230 = point1;
    btnP14S1_1.Location = point230;
    this.btnP14S1.Name = "btnP14S1";
    Button btnP14S1_2 = this.btnP14S1;
    size1 = new Size(20, 17);
    Size size230 = size1;
    btnP14S1_2.Size = size230;
    this.btnP14S1.TabIndex = 226;
    this.btnP14S1.Text = " ";
    this.btnP14S1.UseVisualStyleBackColor = false;
    this.btnP14S1.Visible = false;
    this.btnP13S1.BackColor = Color.Tomato;
    Button btnP13S1_1 = this.btnP13S1;
    point1 = new Point(181, 343);
    Point point231 = point1;
    btnP13S1_1.Location = point231;
    this.btnP13S1.Name = "btnP13S1";
    Button btnP13S1_2 = this.btnP13S1;
    size1 = new Size(19, 17);
    Size size231 = size1;
    btnP13S1_2.Size = size231;
    this.btnP13S1.TabIndex = 209;
    this.btnP13S1.Text = " ";
    this.btnP13S1.UseVisualStyleBackColor = false;
    this.btnP13S1.Visible = false;
    this.btnP12S1.BackColor = Color.Tomato;
    Button btnP12S1_1 = this.btnP12S1;
    point1 = new Point(181, 314);
    Point point232 = point1;
    btnP12S1_1.Location = point232;
    this.btnP12S1.Name = "btnP12S1";
    Button btnP12S1_2 = this.btnP12S1;
    size1 = new Size(19, 19);
    Size size232 = size1;
    btnP12S1_2.Size = size232;
    this.btnP12S1.TabIndex = 192 /*0xC0*/;
    this.btnP12S1.Text = " ";
    this.btnP12S1.UseVisualStyleBackColor = false;
    this.btnP12S1.Visible = false;
    this.btnP11S1.BackColor = Color.Tomato;
    Button btnP11S1_1 = this.btnP11S1;
    point1 = new Point(181, 289);
    Point point233 = point1;
    btnP11S1_1.Location = point233;
    this.btnP11S1.Name = "btnP11S1";
    Button btnP11S1_2 = this.btnP11S1;
    size1 = new Size(19, 17);
    Size size233 = size1;
    btnP11S1_2.Size = size233;
    this.btnP11S1.TabIndex = 175;
    this.btnP11S1.Text = " ";
    this.btnP11S1.UseVisualStyleBackColor = false;
    this.btnP11S1.Visible = false;
    this.btnP10S1.BackColor = Color.Tomato;
    Button btnP10S1_1 = this.btnP10S1;
    point1 = new Point(181, 262);
    Point point234 = point1;
    btnP10S1_1.Location = point234;
    this.btnP10S1.Name = "btnP10S1";
    Button btnP10S1_2 = this.btnP10S1;
    size1 = new Size(19, 17);
    Size size234 = size1;
    btnP10S1_2.Size = size234;
    this.btnP10S1.TabIndex = 159;
    this.btnP10S1.Text = " ";
    this.btnP10S1.UseVisualStyleBackColor = false;
    this.btnP10S1.Visible = false;
    this.btnP9S1.BackColor = Color.Tomato;
    Button btnP9S1_1 = this.btnP9S1;
    point1 = new Point(181, 235);
    Point point235 = point1;
    btnP9S1_1.Location = point235;
    this.btnP9S1.Name = "btnP9S1";
    Button btnP9S1_2 = this.btnP9S1;
    size1 = new Size(19, 17);
    Size size235 = size1;
    btnP9S1_2.Size = size235;
    this.btnP9S1.TabIndex = 142;
    this.btnP9S1.Text = " ";
    this.btnP9S1.UseVisualStyleBackColor = false;
    this.btnP9S1.Visible = false;
    this.btnP8S1.BackColor = Color.Tomato;
    Button btnP8S1_1 = this.btnP8S1;
    point1 = new Point(181, 210);
    Point point236 = point1;
    btnP8S1_1.Location = point236;
    this.btnP8S1.Name = "btnP8S1";
    Button btnP8S1_2 = this.btnP8S1;
    size1 = new Size(17, 18);
    Size size236 = size1;
    btnP8S1_2.Size = size236;
    this.btnP8S1.TabIndex = 125;
    this.btnP8S1.Text = " ";
    this.btnP8S1.UseVisualStyleBackColor = false;
    this.btnP8S1.Visible = false;
    this.btnP7S1.BackColor = Color.Tomato;
    Button btnP7S1_1 = this.btnP7S1;
    point1 = new Point(181, 181);
    Point point237 = point1;
    btnP7S1_1.Location = point237;
    this.btnP7S1.Name = "btnP7S1";
    Button btnP7S1_2 = this.btnP7S1;
    size1 = new Size(17, 17);
    Size size237 = size1;
    btnP7S1_2.Size = size237;
    this.btnP7S1.TabIndex = 108;
    this.btnP7S1.Text = " ";
    this.btnP7S1.UseVisualStyleBackColor = false;
    this.btnP7S1.Visible = false;
    this.btnP6S1.BackColor = Color.Tomato;
    Button btnP6S1_1 = this.btnP6S1;
    point1 = new Point(181, 152);
    Point point238 = point1;
    btnP6S1_1.Location = point238;
    this.btnP6S1.Name = "btnP6S1";
    Button btnP6S1_2 = this.btnP6S1;
    size1 = new Size(17, 17);
    Size size238 = size1;
    btnP6S1_2.Size = size238;
    this.btnP6S1.TabIndex = 91;
    this.btnP6S1.Text = " ";
    this.btnP6S1.UseVisualStyleBackColor = false;
    this.btnP6S1.Visible = false;
    this.btnP5S1.BackColor = Color.Tomato;
    Button btnP5S1_1 = this.btnP5S1;
    point1 = new Point(181, (int) sbyte.MaxValue);
    Point point239 = point1;
    btnP5S1_1.Location = point239;
    this.btnP5S1.Name = "btnP5S1";
    Button btnP5S1_2 = this.btnP5S1;
    size1 = new Size(17, 17);
    Size size239 = size1;
    btnP5S1_2.Size = size239;
    this.btnP5S1.TabIndex = 74;
    this.btnP5S1.Text = " ";
    this.btnP5S1.UseVisualStyleBackColor = false;
    this.btnP5S1.Visible = false;
    this.btnP4S1.BackColor = Color.Tomato;
    Button btnP4S1_1 = this.btnP4S1;
    point1 = new Point(181, 100);
    Point point240 = point1;
    btnP4S1_1.Location = point240;
    this.btnP4S1.Name = "btnP4S1";
    Button btnP4S1_2 = this.btnP4S1;
    size1 = new Size(17, 19);
    Size size240 = size1;
    btnP4S1_2.Size = size240;
    this.btnP4S1.TabIndex = 57;
    this.btnP4S1.Text = " ";
    this.btnP4S1.UseVisualStyleBackColor = false;
    this.btnP4S1.Visible = false;
    this.btnP3S1.BackColor = Color.Tomato;
    Button btnP3S1_1 = this.btnP3S1;
    point1 = new Point(181, 73);
    Point point241 = point1;
    btnP3S1_1.Location = point241;
    this.btnP3S1.Name = "btnP3S1";
    Button btnP3S1_2 = this.btnP3S1;
    size1 = new Size(17, 19);
    Size size241 = size1;
    btnP3S1_2.Size = size241;
    this.btnP3S1.TabIndex = 40;
    this.btnP3S1.UseVisualStyleBackColor = false;
    this.btnP3S1.Visible = false;
    this.btnP2S1.BackColor = Color.Tomato;
    Button btnP2S1_1 = this.btnP2S1;
    point1 = new Point(181, 44);
    Point point242 = point1;
    btnP2S1_1.Location = point242;
    this.btnP2S1.Name = "btnP2S1";
    Button btnP2S1_2 = this.btnP2S1;
    size1 = new Size(17, 19);
    Size size242 = size1;
    btnP2S1_2.Size = size242;
    this.btnP2S1.TabIndex = 23;
    this.btnP2S1.Text = " ";
    this.btnP2S1.UseVisualStyleBackColor = false;
    this.btnP2S1.Visible = false;
    this.btnP1S1.BackColor = Color.Tomato;
    Button btnP1S1_1 = this.btnP1S1;
    point1 = new Point(181, 17);
    Point point243 = point1;
    btnP1S1_1.Location = point243;
    this.btnP1S1.Name = "btnP1S1";
    Button btnP1S1_2 = this.btnP1S1;
    size1 = new Size(17, 19);
    Size size243 = size1;
    btnP1S1_2.Size = size243;
    this.btnP1S1.TabIndex = 6;
    this.btnP1S1.UseVisualStyleBackColor = false;
    this.btnP1S1.Visible = false;
    this.cmbP16S1.BackColor = SystemColors.ButtonFace;
    this.cmbP16S1.FormattingEnabled = true;
    this.cmbP16S1.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP16S1_1 = this.cmbP16S1;
    point1 = new Point(82, 420);
    Point point244 = point1;
    cmbP16S1_1.Location = point244;
    this.cmbP16S1.Name = "cmbP16S1";
    ComboBox cmbP16S1_2 = this.cmbP16S1;
    size1 = new Size(91, 21);
    Size size244 = size1;
    cmbP16S1_2.Size = size244;
    this.cmbP16S1.TabIndex = 259;
    this.cmbP16S1.Visible = false;
    this.cmbP15S1.BackColor = SystemColors.ButtonFace;
    this.cmbP15S1.FormattingEnabled = true;
    this.cmbP15S1.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP15S1_1 = this.cmbP15S1;
    point1 = new Point(81, 391);
    Point point245 = point1;
    cmbP15S1_1.Location = point245;
    this.cmbP15S1.Name = "cmbP15S1";
    ComboBox cmbP15S1_2 = this.cmbP15S1;
    size1 = new Size(91, 21);
    Size size245 = size1;
    cmbP15S1_2.Size = size245;
    this.cmbP15S1.TabIndex = 242;
    this.cmbP15S1.Visible = false;
    this.cmbP14S1.BackColor = SystemColors.ButtonFace;
    this.cmbP14S1.FormattingEnabled = true;
    this.cmbP14S1.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP14S1_1 = this.cmbP14S1;
    point1 = new Point(82, 366);
    Point point246 = point1;
    cmbP14S1_1.Location = point246;
    this.cmbP14S1.Name = "cmbP14S1";
    ComboBox cmbP14S1_2 = this.cmbP14S1;
    size1 = new Size(91, 21);
    Size size246 = size1;
    cmbP14S1_2.Size = size246;
    this.cmbP14S1.TabIndex = 225;
    this.cmbP14S1.Visible = false;
    this.cmbP13S1.BackColor = SystemColors.ButtonFace;
    this.cmbP13S1.FormattingEnabled = true;
    this.cmbP13S1.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP13S1_1 = this.cmbP13S1;
    point1 = new Point(82, 339);
    Point point247 = point1;
    cmbP13S1_1.Location = point247;
    this.cmbP13S1.Name = "cmbP13S1";
    ComboBox cmbP13S1_2 = this.cmbP13S1;
    size1 = new Size(91, 21);
    Size size247 = size1;
    cmbP13S1_2.Size = size247;
    this.cmbP13S1.TabIndex = 209;
    this.cmbP13S1.Visible = false;
    this.cmbP12S1.BackColor = SystemColors.ButtonFace;
    this.cmbP12S1.FormattingEnabled = true;
    this.cmbP12S1.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP12S1_1 = this.cmbP12S1;
    point1 = new Point(82, 312);
    Point point248 = point1;
    cmbP12S1_1.Location = point248;
    this.cmbP12S1.Name = "cmbP12S1";
    ComboBox cmbP12S1_2 = this.cmbP12S1;
    size1 = new Size(91, 21);
    Size size248 = size1;
    cmbP12S1_2.Size = size248;
    this.cmbP12S1.TabIndex = 192 /*0xC0*/;
    this.cmbP12S1.Visible = false;
    this.cmbP11S1.BackColor = SystemColors.ButtonFace;
    this.cmbP11S1.FormattingEnabled = true;
    this.cmbP11S1.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP11S1_1 = this.cmbP11S1;
    point1 = new Point(82, 285);
    Point point249 = point1;
    cmbP11S1_1.Location = point249;
    this.cmbP11S1.Name = "cmbP11S1";
    ComboBox cmbP11S1_2 = this.cmbP11S1;
    size1 = new Size(91, 21);
    Size size249 = size1;
    cmbP11S1_2.Size = size249;
    this.cmbP11S1.TabIndex = 175;
    this.cmbP11S1.Visible = false;
    this.cmbP10S1.BackColor = SystemColors.ButtonFace;
    this.cmbP10S1.FormattingEnabled = true;
    this.cmbP10S1.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP10S1_1 = this.cmbP10S1;
    point1 = new Point(82, 258);
    Point point250 = point1;
    cmbP10S1_1.Location = point250;
    this.cmbP10S1.Name = "cmbP10S1";
    ComboBox cmbP10S1_2 = this.cmbP10S1;
    size1 = new Size(91, 21);
    Size size250 = size1;
    cmbP10S1_2.Size = size250;
    this.cmbP10S1.TabIndex = 158;
    this.cmbP10S1.Visible = false;
    this.cmbP9S1.BackColor = SystemColors.ButtonFace;
    this.cmbP9S1.FormattingEnabled = true;
    this.cmbP9S1.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP9S1_1 = this.cmbP9S1;
    point1 = new Point(82, 231);
    Point point251 = point1;
    cmbP9S1_1.Location = point251;
    this.cmbP9S1.Name = "cmbP9S1";
    ComboBox cmbP9S1_2 = this.cmbP9S1;
    size1 = new Size(91, 21);
    Size size251 = size1;
    cmbP9S1_2.Size = size251;
    this.cmbP9S1.TabIndex = 141;
    this.cmbP9S1.Visible = false;
    this.cmbP8S1.BackColor = SystemColors.ButtonFace;
    this.cmbP8S1.FormattingEnabled = true;
    this.cmbP8S1.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP8S1_1 = this.cmbP8S1;
    point1 = new Point(82, 204);
    Point point252 = point1;
    cmbP8S1_1.Location = point252;
    this.cmbP8S1.Name = "cmbP8S1";
    ComboBox cmbP8S1_2 = this.cmbP8S1;
    size1 = new Size(91, 21);
    Size size252 = size1;
    cmbP8S1_2.Size = size252;
    this.cmbP8S1.TabIndex = 124;
    this.cmbP8S1.Visible = false;
    this.cmbP7S1.BackColor = SystemColors.ButtonFace;
    this.cmbP7S1.FormattingEnabled = true;
    this.cmbP7S1.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP7S1_1 = this.cmbP7S1;
    point1 = new Point(82, 177);
    Point point253 = point1;
    cmbP7S1_1.Location = point253;
    this.cmbP7S1.Name = "cmbP7S1";
    ComboBox cmbP7S1_2 = this.cmbP7S1;
    size1 = new Size(91, 21);
    Size size253 = size1;
    cmbP7S1_2.Size = size253;
    this.cmbP7S1.TabIndex = 108;
    this.cmbP7S1.Visible = false;
    this.cmbP6S1.BackColor = SystemColors.ButtonFace;
    this.cmbP6S1.CausesValidation = false;
    this.cmbP6S1.FormattingEnabled = true;
    this.cmbP6S1.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP6S1_1 = this.cmbP6S1;
    point1 = new Point(82, 150);
    Point point254 = point1;
    cmbP6S1_1.Location = point254;
    this.cmbP6S1.Name = "cmbP6S1";
    ComboBox cmbP6S1_2 = this.cmbP6S1;
    size1 = new Size(91, 21);
    Size size254 = size1;
    cmbP6S1_2.Size = size254;
    this.cmbP6S1.TabIndex = 90;
    this.cmbP6S1.Visible = false;
    this.cmbP5S1.BackColor = SystemColors.ButtonFace;
    this.cmbP5S1.FormattingEnabled = true;
    this.cmbP5S1.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP5S1_1 = this.cmbP5S1;
    point1 = new Point(82, 123);
    Point point255 = point1;
    cmbP5S1_1.Location = point255;
    this.cmbP5S1.Name = "cmbP5S1";
    ComboBox cmbP5S1_2 = this.cmbP5S1;
    size1 = new Size(91, 21);
    Size size255 = size1;
    cmbP5S1_2.Size = size255;
    this.cmbP5S1.TabIndex = 73;
    this.cmbP5S1.Visible = false;
    this.cmbP4S1.BackColor = SystemColors.ButtonFace;
    this.cmbP4S1.FormattingEnabled = true;
    this.cmbP4S1.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP4S1_1 = this.cmbP4S1;
    point1 = new Point(82, 96 /*0x60*/);
    Point point256 = point1;
    cmbP4S1_1.Location = point256;
    this.cmbP4S1.Name = "cmbP4S1";
    ComboBox cmbP4S1_2 = this.cmbP4S1;
    size1 = new Size(91, 21);
    Size size256 = size1;
    cmbP4S1_2.Size = size256;
    this.cmbP4S1.TabIndex = 56;
    this.cmbP4S1.Visible = false;
    this.cmbP3S1.BackColor = SystemColors.ButtonFace;
    this.cmbP3S1.FormattingEnabled = true;
    this.cmbP3S1.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP3S1_1 = this.cmbP3S1;
    point1 = new Point(82, 69);
    Point point257 = point1;
    cmbP3S1_1.Location = point257;
    this.cmbP3S1.Name = "cmbP3S1";
    ComboBox cmbP3S1_2 = this.cmbP3S1;
    size1 = new Size(91, 21);
    Size size257 = size1;
    cmbP3S1_2.Size = size257;
    this.cmbP3S1.TabIndex = 39;
    this.cmbP3S1.Visible = false;
    this.cmbP2S1.BackColor = SystemColors.ButtonFace;
    this.cmbP2S1.FormattingEnabled = true;
    this.cmbP2S1.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP2S1_1 = this.cmbP2S1;
    point1 = new Point(82, 42);
    Point point258 = point1;
    cmbP2S1_1.Location = point258;
    this.cmbP2S1.Name = "cmbP2S1";
    ComboBox cmbP2S1_2 = this.cmbP2S1;
    size1 = new Size(91, 21);
    Size size258 = size1;
    cmbP2S1_2.Size = size258;
    this.cmbP2S1.TabIndex = 22;
    this.cmbP2S1.Visible = false;
    this.cmbP1S1.BackColor = SystemColors.ButtonFace;
    this.cmbP1S1.FormattingEnabled = true;
    this.cmbP1S1.Items.AddRange(new object[4]
    {
      (object) "AGDB",
      (object) "CGDB",
      (object) "PDB",
      (object) "None"
    });
    ComboBox cmbP1S1_1 = this.cmbP1S1;
    point1 = new Point(82, 15);
    Point point259 = point1;
    cmbP1S1_1.Location = point259;
    this.cmbP1S1.Name = "cmbP1S1";
    ComboBox cmbP1S1_2 = this.cmbP1S1;
    size1 = new Size(91, 21);
    Size size259 = size1;
    cmbP1S1_2.Size = size259;
    this.cmbP1S1.TabIndex = 5;
    this.cmbP1S1.Visible = false;
    this.chkPort16.AutoSize = true;
    this.chkPort16.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    CheckBox chkPort16_1 = this.chkPort16;
    point1 = new Point(6, 422);
    Point point260 = point1;
    chkPort16_1.Location = point260;
    this.chkPort16.Name = "chkPort16";
    CheckBox chkPort16_2 = this.chkPort16;
    size1 = new Size(63 /*0x3F*/, 17);
    Size size260 = size1;
    chkPort16_2.Size = size260;
    this.chkPort16.TabIndex = 258;
    this.chkPort16.Text = "Port16";
    this.chkPort16.UseVisualStyleBackColor = true;
    this.chkPort15.AutoSize = true;
    this.chkPort15.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    CheckBox chkPort15_1 = this.chkPort15;
    point1 = new Point(6, 395);
    Point point261 = point1;
    chkPort15_1.Location = point261;
    this.chkPort15.Name = "chkPort15";
    CheckBox chkPort15_2 = this.chkPort15;
    size1 = new Size(63 /*0x3F*/, 17);
    Size size261 = size1;
    chkPort15_2.Size = size261;
    this.chkPort15.TabIndex = 241;
    this.chkPort15.Text = "Port15";
    this.chkPort15.UseVisualStyleBackColor = true;
    this.chkPort14.AutoSize = true;
    this.chkPort14.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    CheckBox chkPort14_1 = this.chkPort14;
    point1 = new Point(6, 368);
    Point point262 = point1;
    chkPort14_1.Location = point262;
    this.chkPort14.Name = "chkPort14";
    CheckBox chkPort14_2 = this.chkPort14;
    size1 = new Size(63 /*0x3F*/, 17);
    Size size262 = size1;
    chkPort14_2.Size = size262;
    this.chkPort14.TabIndex = 224 /*0xE0*/;
    this.chkPort14.Text = "Port14";
    this.chkPort14.UseVisualStyleBackColor = true;
    this.chkPort13.AutoSize = true;
    this.chkPort13.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    CheckBox chkPort13_1 = this.chkPort13;
    point1 = new Point(6, 341);
    Point point263 = point1;
    chkPort13_1.Location = point263;
    this.chkPort13.Name = "chkPort13";
    CheckBox chkPort13_2 = this.chkPort13;
    size1 = new Size(63 /*0x3F*/, 17);
    Size size263 = size1;
    chkPort13_2.Size = size263;
    this.chkPort13.TabIndex = 208 /*0xD0*/;
    this.chkPort13.Text = "Port13";
    this.chkPort13.UseVisualStyleBackColor = true;
    this.chkPort12.AutoSize = true;
    this.chkPort12.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    CheckBox chkPort12_1 = this.chkPort12;
    point1 = new Point(6, 314);
    Point point264 = point1;
    chkPort12_1.Location = point264;
    this.chkPort12.Name = "chkPort12";
    CheckBox chkPort12_2 = this.chkPort12;
    size1 = new Size(63 /*0x3F*/, 17);
    Size size264 = size1;
    chkPort12_2.Size = size264;
    this.chkPort12.TabIndex = 191;
    this.chkPort12.Text = "Port12";
    this.chkPort12.UseVisualStyleBackColor = true;
    this.chkPort11.AutoSize = true;
    this.chkPort11.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    CheckBox chkPort11_1 = this.chkPort11;
    point1 = new Point(6, 287);
    Point point265 = point1;
    chkPort11_1.Location = point265;
    this.chkPort11.Name = "chkPort11";
    CheckBox chkPort11_2 = this.chkPort11;
    size1 = new Size(63 /*0x3F*/, 17);
    Size size265 = size1;
    chkPort11_2.Size = size265;
    this.chkPort11.TabIndex = 174;
    this.chkPort11.Text = "Port11";
    this.chkPort11.UseVisualStyleBackColor = true;
    this.chkPort10.AutoSize = true;
    this.chkPort10.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    CheckBox chkPort10_1 = this.chkPort10;
    point1 = new Point(6, 260);
    Point point266 = point1;
    chkPort10_1.Location = point266;
    this.chkPort10.Name = "chkPort10";
    CheckBox chkPort10_2 = this.chkPort10;
    size1 = new Size(63 /*0x3F*/, 17);
    Size size266 = size1;
    chkPort10_2.Size = size266;
    this.chkPort10.TabIndex = 157;
    this.chkPort10.Text = "Port10";
    this.chkPort10.UseVisualStyleBackColor = true;
    this.chkPort9.AutoSize = true;
    this.chkPort9.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    CheckBox chkPort9_1 = this.chkPort9;
    point1 = new Point(6, 233);
    Point point267 = point1;
    chkPort9_1.Location = point267;
    this.chkPort9.Name = "chkPort9";
    CheckBox chkPort9_2 = this.chkPort9;
    size1 = new Size(56, 17);
    Size size267 = size1;
    chkPort9_2.Size = size267;
    this.chkPort9.TabIndex = 140;
    this.chkPort9.Text = "Port9";
    this.chkPort9.UseVisualStyleBackColor = true;
    this.chkPort8.AutoSize = true;
    this.chkPort8.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    CheckBox chkPort8_1 = this.chkPort8;
    point1 = new Point(6, 206);
    Point point268 = point1;
    chkPort8_1.Location = point268;
    this.chkPort8.Name = "chkPort8";
    CheckBox chkPort8_2 = this.chkPort8;
    size1 = new Size(56, 17);
    Size size268 = size1;
    chkPort8_2.Size = size268;
    this.chkPort8.TabIndex = 123;
    this.chkPort8.Text = "Port8";
    this.chkPort8.UseVisualStyleBackColor = true;
    this.chkPort7.AutoSize = true;
    this.chkPort7.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    CheckBox chkPort7_1 = this.chkPort7;
    point1 = new Point(6, 179);
    Point point269 = point1;
    chkPort7_1.Location = point269;
    this.chkPort7.Name = "chkPort7";
    CheckBox chkPort7_2 = this.chkPort7;
    size1 = new Size(56, 17);
    Size size269 = size1;
    chkPort7_2.Size = size269;
    this.chkPort7.TabIndex = 107;
    this.chkPort7.Text = "Port7";
    this.chkPort7.UseVisualStyleBackColor = true;
    this.chkPort6.AutoSize = true;
    this.chkPort6.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    CheckBox chkPort6_1 = this.chkPort6;
    point1 = new Point(6, 152);
    Point point270 = point1;
    chkPort6_1.Location = point270;
    this.chkPort6.Name = "chkPort6";
    CheckBox chkPort6_2 = this.chkPort6;
    size1 = new Size(56, 17);
    Size size270 = size1;
    chkPort6_2.Size = size270;
    this.chkPort6.TabIndex = 89;
    this.chkPort6.Text = "Port6";
    this.chkPort6.UseVisualStyleBackColor = true;
    this.chkPort5.AutoSize = true;
    this.chkPort5.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    CheckBox chkPort5_1 = this.chkPort5;
    point1 = new Point(6, 124);
    Point point271 = point1;
    chkPort5_1.Location = point271;
    this.chkPort5.Name = "chkPort5";
    CheckBox chkPort5_2 = this.chkPort5;
    size1 = new Size(56, 17);
    Size size271 = size1;
    chkPort5_2.Size = size271;
    this.chkPort5.TabIndex = 72;
    this.chkPort5.Text = "Port5";
    this.chkPort5.UseVisualStyleBackColor = true;
    this.chkPort4.AutoSize = true;
    this.chkPort4.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    CheckBox chkPort4_1 = this.chkPort4;
    point1 = new Point(6, 98);
    Point point272 = point1;
    chkPort4_1.Location = point272;
    this.chkPort4.Name = "chkPort4";
    CheckBox chkPort4_2 = this.chkPort4;
    size1 = new Size(56, 17);
    Size size272 = size1;
    chkPort4_2.Size = size272;
    this.chkPort4.TabIndex = 55;
    this.chkPort4.Text = "Port4";
    this.chkPort4.UseVisualStyleBackColor = true;
    this.chkPort3.AutoSize = true;
    this.chkPort3.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    CheckBox chkPort3_1 = this.chkPort3;
    point1 = new Point(6, 71);
    Point point273 = point1;
    chkPort3_1.Location = point273;
    this.chkPort3.Name = "chkPort3";
    CheckBox chkPort3_2 = this.chkPort3;
    size1 = new Size(56, 17);
    Size size273 = size1;
    chkPort3_2.Size = size273;
    this.chkPort3.TabIndex = 38;
    this.chkPort3.Text = "Port3";
    this.chkPort3.UseVisualStyleBackColor = true;
    this.chkPort2.AutoSize = true;
    this.chkPort2.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    CheckBox chkPort2_1 = this.chkPort2;
    point1 = new Point(6, 46);
    Point point274 = point1;
    chkPort2_1.Location = point274;
    this.chkPort2.Name = "chkPort2";
    CheckBox chkPort2_2 = this.chkPort2;
    size1 = new Size(56, 17);
    Size size274 = size1;
    chkPort2_2.Size = size274;
    this.chkPort2.TabIndex = 21;
    this.chkPort2.Text = "Port2";
    this.chkPort2.UseVisualStyleBackColor = true;
    this.chkPort1.AutoSize = true;
    this.chkPort1.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    CheckBox chkPort1_1 = this.chkPort1;
    point1 = new Point(6, 19);
    Point point275 = point1;
    chkPort1_1.Location = point275;
    this.chkPort1.Name = "chkPort1";
    CheckBox chkPort1_2 = this.chkPort1;
    size1 = new Size(56, 17);
    Size size275 = size1;
    chkPort1_2.Size = size275;
    this.chkPort1.TabIndex = 4;
    this.chkPort1.Text = "Port1";
    this.chkPort1.UseVisualStyleBackColor = true;
    this.btnOk.BackColor = Color.SeaShell;
    this.btnOk.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnOk1 = this.btnOk;
    point1 = new Point(530, 632);
    Point point276 = point1;
    btnOk1.Location = point276;
    this.btnOk.Name = "btnOk";
    Button btnOk2 = this.btnOk;
    size1 = new Size(69, 23);
    Size size276 = size1;
    btnOk2.Size = size276;
    this.btnOk.TabIndex = 158;
    this.btnOk.Text = "Ok";
    this.btnOk.UseVisualStyleBackColor = false;
    this.lblPfno.AutoSize = true;
    this.lblPfno.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblPfno1 = this.lblPfno;
    point1 = new Point(234, 90);
    Point point277 = point1;
    lblPfno1.Location = point277;
    this.lblPfno.Name = "lblPfno";
    Label lblPfno2 = this.lblPfno;
    size1 = new Size(85, 16 /*0x10*/);
    Size size277 = size1;
    lblPfno2.Size = size277;
    this.lblPfno.TabIndex = 165;
    this.lblPfno.Text = "PlatformNo";
    this.txtPdchName.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtPdchName1 = this.txtPdchName;
    point1 = new Point(353, 9);
    Point point278 = point1;
    txtPdchName1.Location = point278;
    this.txtPdchName.MaxLength = 15;
    this.txtPdchName.Name = "txtPdchName";
    TextBox txtPdchName2 = this.txtPdchName;
    size1 = new Size(100, 22);
    Size size278 = size1;
    txtPdchName2.Size = size278;
    this.txtPdchName.TabIndex = 1;
    this.txtPdchAddress.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtPdchAddress1 = this.txtPdchAddress;
    point1 = new Point(353, 47);
    Point point279 = point1;
    txtPdchAddress1.Location = point279;
    this.txtPdchAddress.MaxLength = 3;
    this.txtPdchAddress.Name = "txtPdchAddress";
    TextBox txtPdchAddress2 = this.txtPdchAddress;
    size1 = new Size(54, 22);
    Size size279 = size1;
    txtPdchAddress2.Size = size279;
    this.txtPdchAddress.TabIndex = 2;
    this.lblName.AutoSize = true;
    this.lblName.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblName1 = this.lblName;
    point1 = new Point(269, 12);
    Point point280 = point1;
    lblName1.Location = point280;
    this.lblName.Name = "lblName";
    Label lblName2 = this.lblName;
    size1 = new Size(49, 16 /*0x10*/);
    Size size280 = size1;
    lblName2.Size = size280;
    this.lblName.TabIndex = 164;
    this.lblName.Text = "Name";
    this.lblAddress.AutoSize = true;
    this.lblAddress.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblAddress1 = this.lblAddress;
    point1 = new Point(252, 53);
    Point point281 = point1;
    lblAddress1.Location = point281;
    this.lblAddress.Name = "lblAddress";
    Label lblAddress2 = this.lblAddress;
    size1 = new Size(66, 16 /*0x10*/);
    Size size281 = size1;
    lblAddress2.Size = size281;
    this.lblAddress.TabIndex = 163;
    this.lblAddress.Text = "Address";
    this.lblSharedPfno.AutoSize = true;
    this.lblSharedPfno.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblSharedPfno1 = this.lblSharedPfno;
    point1 = new Point(505, 121);
    Point point282 = point1;
    lblSharedPfno1.Location = point282;
    this.lblSharedPfno.Name = "lblSharedPfno";
    Label lblSharedPfno2 = this.lblSharedPfno;
    size1 = new Size(139, 16 /*0x10*/);
    Size size282 = size1;
    lblSharedPfno2.Size = size282;
    this.lblSharedPfno.TabIndex = 167;
    this.lblSharedPfno.Text = "Shared PlatformNo";
    this.lblSharedPfno.Visible = false;
    this.Label1.AutoSize = true;
    this.Label1.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label label1_1 = this.Label1;
    point1 = new Point(199, 121);
    Point point283 = point1;
    label1_1.Location = point283;
    this.Label1.Name = "Label1";
    Label label1_2 = this.Label1;
    size1 = new Size(119, 16 /*0x10*/);
    Size size283 = size1;
    label1_2.Size = size283;
    this.Label1.TabIndex = 168;
    this.Label1.Text = "Shared Platform\r\n";
    this.chkPDCHSharedPfNo.AutoSize = true;
    this.chkPDCHSharedPfNo.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    CheckBox chkPdchSharedPfNo1 = this.chkPDCHSharedPfNo;
    point1 = new Point(353, 123);
    Point point284 = point1;
    chkPdchSharedPfNo1.Location = point284;
    this.chkPDCHSharedPfNo.Name = "chkPDCHSharedPfNo";
    CheckBox chkPdchSharedPfNo2 = this.chkPDCHSharedPfNo;
    size1 = new Size(15, 14);
    Size size284 = size1;
    chkPdchSharedPfNo2.Size = size284;
    this.chkPDCHSharedPfNo.TabIndex = 4;
    this.chkPDCHSharedPfNo.UseVisualStyleBackColor = true;
    this.cmbPdchPfno.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.cmbPdchPfno.FormattingEnabled = true;
    ComboBox cmbPdchPfno1 = this.cmbPdchPfno;
    point1 = new Point(353, 82);
    Point point285 = point1;
    cmbPdchPfno1.Location = point285;
    this.cmbPdchPfno.Name = "cmbPdchPfno";
    ComboBox cmbPdchPfno2 = this.cmbPdchPfno;
    size1 = new Size(77, 24);
    Size size285 = size1;
    cmbPdchPfno2.Size = size285;
    this.cmbPdchPfno.TabIndex = 3;
    this.cmbPdchSharedPfno.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.cmbPdchSharedPfno.FormattingEnabled = true;
    ComboBox cmbPdchSharedPfno1 = this.cmbPdchSharedPfno;
    point1 = new Point(661, 118);
    Point point286 = point1;
    cmbPdchSharedPfno1.Location = point286;
    this.cmbPdchSharedPfno.Name = "cmbPdchSharedPfno";
    ComboBox cmbPdchSharedPfno2 = this.cmbPdchSharedPfno;
    size1 = new Size(77, 24);
    Size size286 = size1;
    cmbPdchSharedPfno2.Size = size286;
    this.cmbPdchSharedPfno.TabIndex = 5;
    this.cmbPdchSharedPfno.Visible = false;
    this.AcceptButton = (IButtonControl) this.btnOk;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.BackColor = Color.PowderBlue;
    this.CancelButton = (IButtonControl) this.btnExit;
    size1 = new Size(1225, 667);
    this.ClientSize = size1;
    this.Controls.Add((Control) this.cmbPdchSharedPfno);
    this.Controls.Add((Control) this.cmbPdchPfno);
    this.Controls.Add((Control) this.chkPDCHSharedPfNo);
    this.Controls.Add((Control) this.Label1);
    this.Controls.Add((Control) this.lblSharedPfno);
    this.Controls.Add((Control) this.lblPfno);
    this.Controls.Add((Control) this.txtPdchName);
    this.Controls.Add((Control) this.txtPdchAddress);
    this.Controls.Add((Control) this.lblName);
    this.Controls.Add((Control) this.lblAddress);
    this.Controls.Add((Control) this.btnExit);
    this.Controls.Add((Control) this.GroupBox1);
    this.Controls.Add((Control) this.btnOk);
    this.FormBorderStyle = FormBorderStyle.FixedToolWindow;
    this.Name = "frmNetworkPDCH";
    this.Text = "PDCH";
    this.GroupBox1.ResumeLayout(false);
    this.GroupBox1.PerformLayout();
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  internal virtual Button btnExit
  {
    [DebuggerNonUserCode] get { return this._btnExit; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnExit_Click);
      if (this._btnExit != null)
        this._btnExit.Click -= eventHandler;
      this._btnExit = value;
      if (this._btnExit == null)
        return;
      this._btnExit.Click += eventHandler;
    }
  }

  internal virtual GroupBox GroupBox1
  {
    [DebuggerNonUserCode] get { return this._GroupBox1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._GroupBox1 = value;
    }
  }

  internal virtual Button btnP16S8
  {
    [DebuggerNonUserCode] get { return this._btnP16S8; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP16S8_Click);
      if (this._btnP16S8 != null)
        this._btnP16S8.Click -= eventHandler;
      this._btnP16S8 = value;
      if (this._btnP16S8 == null)
        return;
      this._btnP16S8.Click += eventHandler;
    }
  }

  internal virtual Button btnP15S8
  {
    [DebuggerNonUserCode] get { return this._btnP15S8; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP15S8_Click);
      if (this._btnP15S8 != null)
        this._btnP15S8.Click -= eventHandler;
      this._btnP15S8 = value;
      if (this._btnP15S8 == null)
        return;
      this._btnP15S8.Click += eventHandler;
    }
  }

  internal virtual Button btnP14S8
  {
    [DebuggerNonUserCode] get { return this._btnP14S8; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP14S8_Click);
      if (this._btnP14S8 != null)
        this._btnP14S8.Click -= eventHandler;
      this._btnP14S8 = value;
      if (this._btnP14S8 == null)
        return;
      this._btnP14S8.Click += eventHandler;
    }
  }

  internal virtual Button btnP13S8
  {
    [DebuggerNonUserCode] get { return this._btnP13S8; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP13S8_Click);
      if (this._btnP13S8 != null)
        this._btnP13S8.Click -= eventHandler;
      this._btnP13S8 = value;
      if (this._btnP13S8 == null)
        return;
      this._btnP13S8.Click += eventHandler;
    }
  }

  internal virtual Button btnP12S8
  {
    [DebuggerNonUserCode] get { return this._btnP12S8; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP12S8_Click);
      if (this._btnP12S8 != null)
        this._btnP12S8.Click -= eventHandler;
      this._btnP12S8 = value;
      if (this._btnP12S8 == null)
        return;
      this._btnP12S8.Click += eventHandler;
    }
  }

  internal virtual Button btnP11S8
  {
    [DebuggerNonUserCode] get { return this._btnP11S8; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP11S8_Click);
      if (this._btnP11S8 != null)
        this._btnP11S8.Click -= eventHandler;
      this._btnP11S8 = value;
      if (this._btnP11S8 == null)
        return;
      this._btnP11S8.Click += eventHandler;
    }
  }

  internal virtual Button btnP10S8
  {
    [DebuggerNonUserCode] get { return this._btnP10S8; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP10S8_Click);
      if (this._btnP10S8 != null)
        this._btnP10S8.Click -= eventHandler;
      this._btnP10S8 = value;
      if (this._btnP10S8 == null)
        return;
      this._btnP10S8.Click += eventHandler;
    }
  }

  internal virtual Button btnP9S8
  {
    [DebuggerNonUserCode] get { return this._btnP9S8; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP9S8_Click);
      if (this._btnP9S8 != null)
        this._btnP9S8.Click -= eventHandler;
      this._btnP9S8 = value;
      if (this._btnP9S8 == null)
        return;
      this._btnP9S8.Click += eventHandler;
    }
  }

  internal virtual Button btnP8S8
  {
    [DebuggerNonUserCode] get { return this._btnP8S8; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP8S8_Click);
      if (this._btnP8S8 != null)
        this._btnP8S8.Click -= eventHandler;
      this._btnP8S8 = value;
      if (this._btnP8S8 == null)
        return;
      this._btnP8S8.Click += eventHandler;
    }
  }

  internal virtual Button btnP7S8
  {
    [DebuggerNonUserCode] get { return this._btnP7S8; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP7S8_Click);
      if (this._btnP7S8 != null)
        this._btnP7S8.Click -= eventHandler;
      this._btnP7S8 = value;
      if (this._btnP7S8 == null)
        return;
      this._btnP7S8.Click += eventHandler;
    }
  }

  internal virtual Button btnP6S8
  {
    [DebuggerNonUserCode] get { return this._btnP6S8; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP6S8_Click);
      if (this._btnP6S8 != null)
        this._btnP6S8.Click -= eventHandler;
      this._btnP6S8 = value;
      if (this._btnP6S8 == null)
        return;
      this._btnP6S8.Click += eventHandler;
    }
  }

  internal virtual Button btnP5S8
  {
    [DebuggerNonUserCode] get { return this._btnP5S8; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP5S8_Click);
      if (this._btnP5S8 != null)
        this._btnP5S8.Click -= eventHandler;
      this._btnP5S8 = value;
      if (this._btnP5S8 == null)
        return;
      this._btnP5S8.Click += eventHandler;
    }
  }

  internal virtual Button btnP4S8
  {
    [DebuggerNonUserCode] get { return this._btnP4S8; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP4S8_Click);
      if (this._btnP4S8 != null)
        this._btnP4S8.Click -= eventHandler;
      this._btnP4S8 = value;
      if (this._btnP4S8 == null)
        return;
      this._btnP4S8.Click += eventHandler;
    }
  }

  internal virtual Button btnP3S8
  {
    [DebuggerNonUserCode] get { return this._btnP3S8; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP3S8_Click);
      if (this._btnP3S8 != null)
        this._btnP3S8.Click -= eventHandler;
      this._btnP3S8 = value;
      if (this._btnP3S8 == null)
        return;
      this._btnP3S8.Click += eventHandler;
    }
  }

  internal virtual Button btnP2S8
  {
    [DebuggerNonUserCode] get { return this._btnP2S8; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP2S8_Click);
      if (this._btnP2S8 != null)
        this._btnP2S8.Click -= eventHandler;
      this._btnP2S8 = value;
      if (this._btnP2S8 == null)
        return;
      this._btnP2S8.Click += eventHandler;
    }
  }

  internal virtual Button btnP1S8
  {
    [DebuggerNonUserCode] get { return this._btnP1S8; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP1S8_Click);
      if (this._btnP1S8 != null)
        this._btnP1S8.Click -= eventHandler;
      this._btnP1S8 = value;
      if (this._btnP1S8 == null)
        return;
      this._btnP1S8.Click += eventHandler;
    }
  }

  internal virtual ComboBox cmbP16S8
  {
    [DebuggerNonUserCode] get { return this._cmbP16S8; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP16S8 = value; }
  }

  internal virtual ComboBox cmbP15S8
  {
    [DebuggerNonUserCode] get { return this._cmbP15S8; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP15S8 = value; }
  }

  internal virtual ComboBox cmbP14S8
  {
    [DebuggerNonUserCode] get { return this._cmbP14S8; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP14S8 = value; }
  }

  internal virtual ComboBox cmbP13S8
  {
    [DebuggerNonUserCode] get { return this._cmbP13S8; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP13S8 = value; }
  }

  internal virtual ComboBox cmbP12S8
  {
    [DebuggerNonUserCode] get { return this._cmbP12S8; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP12S8 = value; }
  }

  internal virtual ComboBox cmbP11S8
  {
    [DebuggerNonUserCode] get { return this._cmbP11S8; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP11S8 = value; }
  }

  internal virtual ComboBox cmbP10S8
  {
    [DebuggerNonUserCode] get { return this._cmbP10S8; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP10S8 = value; }
  }

  internal virtual ComboBox cmbP9S8
  {
    [DebuggerNonUserCode] get { return this._cmbP9S8; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP9S8 = value; }
  }

  internal virtual ComboBox cmbP8S8
  {
    [DebuggerNonUserCode] get { return this._cmbP8S8; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP8S8 = value; }
  }

  internal virtual ComboBox cmbP7S8
  {
    [DebuggerNonUserCode] get { return this._cmbP7S8; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP7S8 = value; }
  }

  internal virtual ComboBox cmbP6S8
  {
    [DebuggerNonUserCode] get { return this._cmbP6S8; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP6S8 = value; }
  }

  internal virtual ComboBox cmbP5S8
  {
    [DebuggerNonUserCode] get { return this._cmbP5S8; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP5S8 = value; }
  }

  internal virtual ComboBox cmbP4S8
  {
    [DebuggerNonUserCode] get { return this._cmbP4S8; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP4S8 = value; }
  }

  internal virtual ComboBox cmbP3S8
  {
    [DebuggerNonUserCode] get { return this._cmbP3S8; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP3S8 = value; }
  }

  internal virtual ComboBox cmbP2S8
  {
    [DebuggerNonUserCode] get { return this._cmbP2S8; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP2S8 = value; }
  }

  internal virtual ComboBox cmbP1S8
  {
    [DebuggerNonUserCode] get { return this._cmbP1S8; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP1S8 = value; }
  }

  internal virtual Button btnP16S7
  {
    [DebuggerNonUserCode] get { return this._btnP16S7; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP16S7_Click);
      if (this._btnP16S7 != null)
        this._btnP16S7.Click -= eventHandler;
      this._btnP16S7 = value;
      if (this._btnP16S7 == null)
        return;
      this._btnP16S7.Click += eventHandler;
    }
  }

  internal virtual Button btnP15S7
  {
    [DebuggerNonUserCode] get { return this._btnP15S7; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP15S7_Click);
      if (this._btnP15S7 != null)
        this._btnP15S7.Click -= eventHandler;
      this._btnP15S7 = value;
      if (this._btnP15S7 == null)
        return;
      this._btnP15S7.Click += eventHandler;
    }
  }

  internal virtual Button btnP14S7
  {
    [DebuggerNonUserCode] get { return this._btnP14S7; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP14S7_Click);
      if (this._btnP14S7 != null)
        this._btnP14S7.Click -= eventHandler;
      this._btnP14S7 = value;
      if (this._btnP14S7 == null)
        return;
      this._btnP14S7.Click += eventHandler;
    }
  }

  internal virtual Button btnP13S7
  {
    [DebuggerNonUserCode] get { return this._btnP13S7; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP13S7_Click);
      if (this._btnP13S7 != null)
        this._btnP13S7.Click -= eventHandler;
      this._btnP13S7 = value;
      if (this._btnP13S7 == null)
        return;
      this._btnP13S7.Click += eventHandler;
    }
  }

  internal virtual Button btnP12S7
  {
    [DebuggerNonUserCode] get { return this._btnP12S7; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP12S7_Click);
      if (this._btnP12S7 != null)
        this._btnP12S7.Click -= eventHandler;
      this._btnP12S7 = value;
      if (this._btnP12S7 == null)
        return;
      this._btnP12S7.Click += eventHandler;
    }
  }

  internal virtual Button btnP11S7
  {
    [DebuggerNonUserCode] get { return this._btnP11S7; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP11S7_Click);
      if (this._btnP11S7 != null)
        this._btnP11S7.Click -= eventHandler;
      this._btnP11S7 = value;
      if (this._btnP11S7 == null)
        return;
      this._btnP11S7.Click += eventHandler;
    }
  }

  internal virtual Button btnP10S7
  {
    [DebuggerNonUserCode] get { return this._btnP10S7; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP10S7_Click);
      if (this._btnP10S7 != null)
        this._btnP10S7.Click -= eventHandler;
      this._btnP10S7 = value;
      if (this._btnP10S7 == null)
        return;
      this._btnP10S7.Click += eventHandler;
    }
  }

  internal virtual Button btnP9S7
  {
    [DebuggerNonUserCode] get { return this._btnP9S7; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP9S7_Click);
      if (this._btnP9S7 != null)
        this._btnP9S7.Click -= eventHandler;
      this._btnP9S7 = value;
      if (this._btnP9S7 == null)
        return;
      this._btnP9S7.Click += eventHandler;
    }
  }

  internal virtual Button btnP8S7
  {
    [DebuggerNonUserCode] get { return this._btnP8S7; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP8S7_Click);
      if (this._btnP8S7 != null)
        this._btnP8S7.Click -= eventHandler;
      this._btnP8S7 = value;
      if (this._btnP8S7 == null)
        return;
      this._btnP8S7.Click += eventHandler;
    }
  }

  internal virtual Button btnP7S7
  {
    [DebuggerNonUserCode] get { return this._btnP7S7; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP7S7_Click);
      if (this._btnP7S7 != null)
        this._btnP7S7.Click -= eventHandler;
      this._btnP7S7 = value;
      if (this._btnP7S7 == null)
        return;
      this._btnP7S7.Click += eventHandler;
    }
  }

  internal virtual Button btnP6S7
  {
    [DebuggerNonUserCode] get { return this._btnP6S7; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP6S7_Click);
      if (this._btnP6S7 != null)
        this._btnP6S7.Click -= eventHandler;
      this._btnP6S7 = value;
      if (this._btnP6S7 == null)
        return;
      this._btnP6S7.Click += eventHandler;
    }
  }

  internal virtual Button btnP5S7
  {
    [DebuggerNonUserCode] get { return this._btnP5S7; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP5S7_Click);
      if (this._btnP5S7 != null)
        this._btnP5S7.Click -= eventHandler;
      this._btnP5S7 = value;
      if (this._btnP5S7 == null)
        return;
      this._btnP5S7.Click += eventHandler;
    }
  }

  internal virtual Button btnP4S7
  {
    [DebuggerNonUserCode] get { return this._btnP4S7; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP4S7_Click);
      if (this._btnP4S7 != null)
        this._btnP4S7.Click -= eventHandler;
      this._btnP4S7 = value;
      if (this._btnP4S7 == null)
        return;
      this._btnP4S7.Click += eventHandler;
    }
  }

  internal virtual Button btnP3S7
  {
    [DebuggerNonUserCode] get { return this._btnP3S7; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP3S7_Click);
      if (this._btnP3S7 != null)
        this._btnP3S7.Click -= eventHandler;
      this._btnP3S7 = value;
      if (this._btnP3S7 == null)
        return;
      this._btnP3S7.Click += eventHandler;
    }
  }

  internal virtual Button btnP2S7
  {
    [DebuggerNonUserCode] get { return this._btnP2S7; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP2S7_Click);
      if (this._btnP2S7 != null)
        this._btnP2S7.Click -= eventHandler;
      this._btnP2S7 = value;
      if (this._btnP2S7 == null)
        return;
      this._btnP2S7.Click += eventHandler;
    }
  }

  internal virtual Button btnP1S7
  {
    [DebuggerNonUserCode] get { return this._btnP1S7; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP1S7_Click);
      if (this._btnP1S7 != null)
        this._btnP1S7.Click -= eventHandler;
      this._btnP1S7 = value;
      if (this._btnP1S7 == null)
        return;
      this._btnP1S7.Click += eventHandler;
    }
  }

  internal virtual ComboBox cmbP16S7
  {
    [DebuggerNonUserCode] get { return this._cmbP16S7; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP16S7 = value; }
  }

  internal virtual ComboBox cmbP15S7
  {
    [DebuggerNonUserCode] get { return this._cmbP15S7; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP15S7 = value; }
  }

  internal virtual ComboBox cmbP14S7
  {
    [DebuggerNonUserCode] get { return this._cmbP14S7; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP14S7 = value; }
  }

  internal virtual ComboBox cmbP13S7
  {
    [DebuggerNonUserCode] get { return this._cmbP13S7; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP13S7 = value; }
  }

  internal virtual ComboBox cmbP12S7
  {
    [DebuggerNonUserCode] get { return this._cmbP12S7; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP12S7 = value; }
  }

  internal virtual ComboBox cmbP11S7
  {
    [DebuggerNonUserCode] get { return this._cmbP11S7; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP11S7 = value; }
  }

  internal virtual ComboBox cmbP10S7
  {
    [DebuggerNonUserCode] get { return this._cmbP10S7; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP10S7 = value; }
  }

  internal virtual ComboBox cmbP9S7
  {
    [DebuggerNonUserCode] get { return this._cmbP9S7; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP9S7 = value; }
  }

  internal virtual ComboBox cmbP8S7
  {
    [DebuggerNonUserCode] get { return this._cmbP8S7; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP8S7 = value; }
  }

  internal virtual ComboBox cmbP7S7
  {
    [DebuggerNonUserCode] get { return this._cmbP7S7; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP7S7 = value; }
  }

  internal virtual ComboBox cmbP6S7
  {
    [DebuggerNonUserCode] get { return this._cmbP6S7; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP6S7 = value; }
  }

  internal virtual ComboBox cmbP5S7
  {
    [DebuggerNonUserCode] get { return this._cmbP5S7; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP5S7 = value; }
  }

  internal virtual ComboBox cmbP4S7
  {
    [DebuggerNonUserCode] get { return this._cmbP4S7; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP4S7 = value; }
  }

  internal virtual ComboBox cmbP3S7
  {
    [DebuggerNonUserCode] get { return this._cmbP3S7; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP3S7 = value; }
  }

  internal virtual ComboBox cmbP2S7
  {
    [DebuggerNonUserCode] get { return this._cmbP2S7; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP2S7 = value; }
  }

  internal virtual ComboBox cmbP1S7
  {
    [DebuggerNonUserCode] get { return this._cmbP1S7; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP1S7 = value; }
  }

  internal virtual Button btnP16S6
  {
    [DebuggerNonUserCode] get { return this._btnP16S6; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP16S6_Click);
      if (this._btnP16S6 != null)
        this._btnP16S6.Click -= eventHandler;
      this._btnP16S6 = value;
      if (this._btnP16S6 == null)
        return;
      this._btnP16S6.Click += eventHandler;
    }
  }

  internal virtual Button btnP15S6
  {
    [DebuggerNonUserCode] get { return this._btnP15S6; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP15S6_Click);
      if (this._btnP15S6 != null)
        this._btnP15S6.Click -= eventHandler;
      this._btnP15S6 = value;
      if (this._btnP15S6 == null)
        return;
      this._btnP15S6.Click += eventHandler;
    }
  }

  internal virtual Button btnP14S6
  {
    [DebuggerNonUserCode] get { return this._btnP14S6; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP14S6_Click);
      if (this._btnP14S6 != null)
        this._btnP14S6.Click -= eventHandler;
      this._btnP14S6 = value;
      if (this._btnP14S6 == null)
        return;
      this._btnP14S6.Click += eventHandler;
    }
  }

  internal virtual Button btnP13S6
  {
    [DebuggerNonUserCode] get { return this._btnP13S6; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP13S6_Click);
      if (this._btnP13S6 != null)
        this._btnP13S6.Click -= eventHandler;
      this._btnP13S6 = value;
      if (this._btnP13S6 == null)
        return;
      this._btnP13S6.Click += eventHandler;
    }
  }

  internal virtual Button btnP12S6
  {
    [DebuggerNonUserCode] get { return this._btnP12S6; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP12S6_Click);
      if (this._btnP12S6 != null)
        this._btnP12S6.Click -= eventHandler;
      this._btnP12S6 = value;
      if (this._btnP12S6 == null)
        return;
      this._btnP12S6.Click += eventHandler;
    }
  }

  internal virtual Button btnP11S6
  {
    [DebuggerNonUserCode] get { return this._btnP11S6; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP11S6_Click);
      if (this._btnP11S6 != null)
        this._btnP11S6.Click -= eventHandler;
      this._btnP11S6 = value;
      if (this._btnP11S6 == null)
        return;
      this._btnP11S6.Click += eventHandler;
    }
  }

  internal virtual Button btnP10S6
  {
    [DebuggerNonUserCode] get { return this._btnP10S6; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP10S6_Click);
      if (this._btnP10S6 != null)
        this._btnP10S6.Click -= eventHandler;
      this._btnP10S6 = value;
      if (this._btnP10S6 == null)
        return;
      this._btnP10S6.Click += eventHandler;
    }
  }

  internal virtual Button btnP9S6
  {
    [DebuggerNonUserCode] get { return this._btnP9S6; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP9S6_Click);
      if (this._btnP9S6 != null)
        this._btnP9S6.Click -= eventHandler;
      this._btnP9S6 = value;
      if (this._btnP9S6 == null)
        return;
      this._btnP9S6.Click += eventHandler;
    }
  }

  internal virtual Button btnP8S6
  {
    [DebuggerNonUserCode] get { return this._btnP8S6; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP8S6_Click);
      if (this._btnP8S6 != null)
        this._btnP8S6.Click -= eventHandler;
      this._btnP8S6 = value;
      if (this._btnP8S6 == null)
        return;
      this._btnP8S6.Click += eventHandler;
    }
  }

  internal virtual Button btnP7S6
  {
    [DebuggerNonUserCode] get { return this._btnP7S6; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP7S6_Click);
      if (this._btnP7S6 != null)
        this._btnP7S6.Click -= eventHandler;
      this._btnP7S6 = value;
      if (this._btnP7S6 == null)
        return;
      this._btnP7S6.Click += eventHandler;
    }
  }

  internal virtual Button btnP6S6
  {
    [DebuggerNonUserCode] get { return this._btnP6S6; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP6S6_Click);
      if (this._btnP6S6 != null)
        this._btnP6S6.Click -= eventHandler;
      this._btnP6S6 = value;
      if (this._btnP6S6 == null)
        return;
      this._btnP6S6.Click += eventHandler;
    }
  }

  internal virtual Button btnP5S6
  {
    [DebuggerNonUserCode] get { return this._btnP5S6; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP5S6_Click);
      if (this._btnP5S6 != null)
        this._btnP5S6.Click -= eventHandler;
      this._btnP5S6 = value;
      if (this._btnP5S6 == null)
        return;
      this._btnP5S6.Click += eventHandler;
    }
  }

  internal virtual Button btnP4S6
  {
    [DebuggerNonUserCode] get { return this._btnP4S6; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP4S6_Click);
      if (this._btnP4S6 != null)
        this._btnP4S6.Click -= eventHandler;
      this._btnP4S6 = value;
      if (this._btnP4S6 == null)
        return;
      this._btnP4S6.Click += eventHandler;
    }
  }

  internal virtual Button btnP3S6
  {
    [DebuggerNonUserCode] get { return this._btnP3S6; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP3S6_Click);
      if (this._btnP3S6 != null)
        this._btnP3S6.Click -= eventHandler;
      this._btnP3S6 = value;
      if (this._btnP3S6 == null)
        return;
      this._btnP3S6.Click += eventHandler;
    }
  }

  internal virtual Button btnP2S6
  {
    [DebuggerNonUserCode] get { return this._btnP2S6; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP2S6_Click);
      if (this._btnP2S6 != null)
        this._btnP2S6.Click -= eventHandler;
      this._btnP2S6 = value;
      if (this._btnP2S6 == null)
        return;
      this._btnP2S6.Click += eventHandler;
    }
  }

  internal virtual Button btnP1S6
  {
    [DebuggerNonUserCode] get { return this._btnP1S6; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP1S6_Click);
      if (this._btnP1S6 != null)
        this._btnP1S6.Click -= eventHandler;
      this._btnP1S6 = value;
      if (this._btnP1S6 == null)
        return;
      this._btnP1S6.Click += eventHandler;
    }
  }

  internal virtual ComboBox cmbP16S6
  {
    [DebuggerNonUserCode] get { return this._cmbP16S6; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP16S6 = value; }
  }

  internal virtual ComboBox cmbP15S6
  {
    [DebuggerNonUserCode] get { return this._cmbP15S6; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP15S6 = value; }
  }

  internal virtual ComboBox cmbP14S6
  {
    [DebuggerNonUserCode] get { return this._cmbP14S6; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP14S6 = value; }
  }

  internal virtual ComboBox cmbP13S6
  {
    [DebuggerNonUserCode] get { return this._cmbP13S6; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP13S6 = value; }
  }

  internal virtual ComboBox cmbP12S6
  {
    [DebuggerNonUserCode] get { return this._cmbP12S6; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP12S6 = value; }
  }

  internal virtual ComboBox cmbP11S6
  {
    [DebuggerNonUserCode] get { return this._cmbP11S6; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP11S6 = value; }
  }

  internal virtual ComboBox cmbP10S6
  {
    [DebuggerNonUserCode] get { return this._cmbP10S6; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP10S6 = value; }
  }

  internal virtual ComboBox cmbP9S6
  {
    [DebuggerNonUserCode] get { return this._cmbP9S6; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP9S6 = value; }
  }

  internal virtual ComboBox cmbP8S6
  {
    [DebuggerNonUserCode] get { return this._cmbP8S6; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP8S6 = value; }
  }

  internal virtual ComboBox cmbP7S6
  {
    [DebuggerNonUserCode] get { return this._cmbP7S6; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP7S6 = value; }
  }

  internal virtual ComboBox cmbP6S6
  {
    [DebuggerNonUserCode] get { return this._cmbP6S6; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP6S6 = value; }
  }

  internal virtual ComboBox cmbP5S6
  {
    [DebuggerNonUserCode] get { return this._cmbP5S6; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP5S6 = value; }
  }

  internal virtual ComboBox cmbP4S6
  {
    [DebuggerNonUserCode] get { return this._cmbP4S6; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP4S6 = value; }
  }

  internal virtual ComboBox cmbP3S6
  {
    [DebuggerNonUserCode] get { return this._cmbP3S6; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP3S6 = value; }
  }

  internal virtual ComboBox cmbP2S6
  {
    [DebuggerNonUserCode] get { return this._cmbP2S6; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP2S6 = value; }
  }

  internal virtual ComboBox cmbP1S6
  {
    [DebuggerNonUserCode] get { return this._cmbP1S6; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP1S6 = value; }
  }

  internal virtual Button btnP16S5
  {
    [DebuggerNonUserCode] get { return this._btnP16S5; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP16S5_Click);
      if (this._btnP16S5 != null)
        this._btnP16S5.Click -= eventHandler;
      this._btnP16S5 = value;
      if (this._btnP16S5 == null)
        return;
      this._btnP16S5.Click += eventHandler;
    }
  }

  internal virtual Button btnP15S5
  {
    [DebuggerNonUserCode] get { return this._btnP15S5; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP15S5_Click);
      if (this._btnP15S5 != null)
        this._btnP15S5.Click -= eventHandler;
      this._btnP15S5 = value;
      if (this._btnP15S5 == null)
        return;
      this._btnP15S5.Click += eventHandler;
    }
  }

  internal virtual Button btnP14S5
  {
    [DebuggerNonUserCode] get { return this._btnP14S5; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP14S5_Click);
      if (this._btnP14S5 != null)
        this._btnP14S5.Click -= eventHandler;
      this._btnP14S5 = value;
      if (this._btnP14S5 == null)
        return;
      this._btnP14S5.Click += eventHandler;
    }
  }

  internal virtual Button btnP13S5
  {
    [DebuggerNonUserCode] get { return this._btnP13S5; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP13S5_Click);
      if (this._btnP13S5 != null)
        this._btnP13S5.Click -= eventHandler;
      this._btnP13S5 = value;
      if (this._btnP13S5 == null)
        return;
      this._btnP13S5.Click += eventHandler;
    }
  }

  internal virtual Button btnP12S5
  {
    [DebuggerNonUserCode] get { return this._btnP12S5; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP12S5_Click);
      if (this._btnP12S5 != null)
        this._btnP12S5.Click -= eventHandler;
      this._btnP12S5 = value;
      if (this._btnP12S5 == null)
        return;
      this._btnP12S5.Click += eventHandler;
    }
  }

  internal virtual Button btnP11S5
  {
    [DebuggerNonUserCode] get { return this._btnP11S5; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP11S5_Click);
      if (this._btnP11S5 != null)
        this._btnP11S5.Click -= eventHandler;
      this._btnP11S5 = value;
      if (this._btnP11S5 == null)
        return;
      this._btnP11S5.Click += eventHandler;
    }
  }

  internal virtual Button btnP10S5
  {
    [DebuggerNonUserCode] get { return this._btnP10S5; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP10S5_Click);
      if (this._btnP10S5 != null)
        this._btnP10S5.Click -= eventHandler;
      this._btnP10S5 = value;
      if (this._btnP10S5 == null)
        return;
      this._btnP10S5.Click += eventHandler;
    }
  }

  internal virtual Button btnP9S5
  {
    [DebuggerNonUserCode] get { return this._btnP9S5; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP9S5_Click);
      if (this._btnP9S5 != null)
        this._btnP9S5.Click -= eventHandler;
      this._btnP9S5 = value;
      if (this._btnP9S5 == null)
        return;
      this._btnP9S5.Click += eventHandler;
    }
  }

  internal virtual Button btnP8S5
  {
    [DebuggerNonUserCode] get { return this._btnP8S5; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP8S5_Click);
      if (this._btnP8S5 != null)
        this._btnP8S5.Click -= eventHandler;
      this._btnP8S5 = value;
      if (this._btnP8S5 == null)
        return;
      this._btnP8S5.Click += eventHandler;
    }
  }

  internal virtual Button btnP7S5
  {
    [DebuggerNonUserCode] get { return this._btnP7S5; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP7S5_Click);
      if (this._btnP7S5 != null)
        this._btnP7S5.Click -= eventHandler;
      this._btnP7S5 = value;
      if (this._btnP7S5 == null)
        return;
      this._btnP7S5.Click += eventHandler;
    }
  }

  internal virtual Button btnP6S5
  {
    [DebuggerNonUserCode] get { return this._btnP6S5; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP6S5_Click);
      if (this._btnP6S5 != null)
        this._btnP6S5.Click -= eventHandler;
      this._btnP6S5 = value;
      if (this._btnP6S5 == null)
        return;
      this._btnP6S5.Click += eventHandler;
    }
  }

  internal virtual Button btnP5S5
  {
    [DebuggerNonUserCode] get { return this._btnP5S5; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP5S5_Click);
      if (this._btnP5S5 != null)
        this._btnP5S5.Click -= eventHandler;
      this._btnP5S5 = value;
      if (this._btnP5S5 == null)
        return;
      this._btnP5S5.Click += eventHandler;
    }
  }

  internal virtual Button btnP4S5
  {
    [DebuggerNonUserCode] get { return this._btnP4S5; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP4S5_Click);
      if (this._btnP4S5 != null)
        this._btnP4S5.Click -= eventHandler;
      this._btnP4S5 = value;
      if (this._btnP4S5 == null)
        return;
      this._btnP4S5.Click += eventHandler;
    }
  }

  internal virtual Button btnP3S5
  {
    [DebuggerNonUserCode] get { return this._btnP3S5; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP3S5_Click);
      if (this._btnP3S5 != null)
        this._btnP3S5.Click -= eventHandler;
      this._btnP3S5 = value;
      if (this._btnP3S5 == null)
        return;
      this._btnP3S5.Click += eventHandler;
    }
  }

  internal virtual Button btnP2S5
  {
    [DebuggerNonUserCode] get { return this._btnP2S5; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP2S5_Click);
      if (this._btnP2S5 != null)
        this._btnP2S5.Click -= eventHandler;
      this._btnP2S5 = value;
      if (this._btnP2S5 == null)
        return;
      this._btnP2S5.Click += eventHandler;
    }
  }

  internal virtual Button btnP1S5
  {
    [DebuggerNonUserCode] get { return this._btnP1S5; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP1S5_Click);
      if (this._btnP1S5 != null)
        this._btnP1S5.Click -= eventHandler;
      this._btnP1S5 = value;
      if (this._btnP1S5 == null)
        return;
      this._btnP1S5.Click += eventHandler;
    }
  }

  internal virtual ComboBox cmbP16S5
  {
    [DebuggerNonUserCode] get { return this._cmbP16S5; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP16S5 = value; }
  }

  internal virtual ComboBox cmbP15S5
  {
    [DebuggerNonUserCode] get { return this._cmbP15S5; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP15S5 = value; }
  }

  internal virtual ComboBox cmbP14S5
  {
    [DebuggerNonUserCode] get { return this._cmbP14S5; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP14S5 = value; }
  }

  internal virtual ComboBox cmbP13S5
  {
    [DebuggerNonUserCode] get { return this._cmbP13S5; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP13S5 = value; }
  }

  internal virtual ComboBox cmbP12S5
  {
    [DebuggerNonUserCode] get { return this._cmbP12S5; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP12S5 = value; }
  }

  internal virtual ComboBox cmbP11S5
  {
    [DebuggerNonUserCode] get { return this._cmbP11S5; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP11S5 = value; }
  }

  internal virtual ComboBox cmbP10S5
  {
    [DebuggerNonUserCode] get { return this._cmbP10S5; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP10S5 = value; }
  }

  internal virtual ComboBox cmbP9S5
  {
    [DebuggerNonUserCode] get { return this._cmbP9S5; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP9S5 = value; }
  }

  internal virtual ComboBox cmbP8S5
  {
    [DebuggerNonUserCode] get { return this._cmbP8S5; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP8S5 = value; }
  }

  internal virtual ComboBox cmbP7S5
  {
    [DebuggerNonUserCode] get { return this._cmbP7S5; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP7S5 = value; }
  }

  internal virtual ComboBox cmbP6S5
  {
    [DebuggerNonUserCode] get { return this._cmbP6S5; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP6S5 = value; }
  }

  internal virtual ComboBox cmbP5S5
  {
    [DebuggerNonUserCode] get { return this._cmbP5S5; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP5S5 = value; }
  }

  internal virtual ComboBox cmbP4S5
  {
    [DebuggerNonUserCode] get { return this._cmbP4S5; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP4S5 = value; }
  }

  internal virtual ComboBox cmbP3S5
  {
    [DebuggerNonUserCode] get { return this._cmbP3S5; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP3S5 = value; }
  }

  internal virtual ComboBox cmbP2S5
  {
    [DebuggerNonUserCode] get { return this._cmbP2S5; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP2S5 = value; }
  }

  internal virtual ComboBox cmbP1S5
  {
    [DebuggerNonUserCode] get { return this._cmbP1S5; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP1S5 = value; }
  }

  internal virtual Button btnP16S4
  {
    [DebuggerNonUserCode] get { return this._btnP16S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP16S4_Click);
      if (this._btnP16S4 != null)
        this._btnP16S4.Click -= eventHandler;
      this._btnP16S4 = value;
      if (this._btnP16S4 == null)
        return;
      this._btnP16S4.Click += eventHandler;
    }
  }

  internal virtual Button btnP15S4
  {
    [DebuggerNonUserCode] get { return this._btnP15S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP15S4_Click);
      if (this._btnP15S4 != null)
        this._btnP15S4.Click -= eventHandler;
      this._btnP15S4 = value;
      if (this._btnP15S4 == null)
        return;
      this._btnP15S4.Click += eventHandler;
    }
  }

  internal virtual Button btnP14S4
  {
    [DebuggerNonUserCode] get { return this._btnP14S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP14S4_Click);
      if (this._btnP14S4 != null)
        this._btnP14S4.Click -= eventHandler;
      this._btnP14S4 = value;
      if (this._btnP14S4 == null)
        return;
      this._btnP14S4.Click += eventHandler;
    }
  }

  internal virtual Button btnP13S4
  {
    [DebuggerNonUserCode] get { return this._btnP13S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP13S4_Click);
      if (this._btnP13S4 != null)
        this._btnP13S4.Click -= eventHandler;
      this._btnP13S4 = value;
      if (this._btnP13S4 == null)
        return;
      this._btnP13S4.Click += eventHandler;
    }
  }

  internal virtual Button btnP12S4
  {
    [DebuggerNonUserCode] get { return this._btnP12S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP12S4_Click);
      if (this._btnP12S4 != null)
        this._btnP12S4.Click -= eventHandler;
      this._btnP12S4 = value;
      if (this._btnP12S4 == null)
        return;
      this._btnP12S4.Click += eventHandler;
    }
  }

  internal virtual Button btnP11S4
  {
    [DebuggerNonUserCode] get { return this._btnP11S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP11S4_Click);
      if (this._btnP11S4 != null)
        this._btnP11S4.Click -= eventHandler;
      this._btnP11S4 = value;
      if (this._btnP11S4 == null)
        return;
      this._btnP11S4.Click += eventHandler;
    }
  }

  internal virtual Button btnP10S4
  {
    [DebuggerNonUserCode] get { return this._btnP10S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP10S4_Click);
      if (this._btnP10S4 != null)
        this._btnP10S4.Click -= eventHandler;
      this._btnP10S4 = value;
      if (this._btnP10S4 == null)
        return;
      this._btnP10S4.Click += eventHandler;
    }
  }

  internal virtual Button btnP9S4
  {
    [DebuggerNonUserCode] get { return this._btnP9S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP9S4_Click);
      if (this._btnP9S4 != null)
        this._btnP9S4.Click -= eventHandler;
      this._btnP9S4 = value;
      if (this._btnP9S4 == null)
        return;
      this._btnP9S4.Click += eventHandler;
    }
  }

  internal virtual Button btnP8S4
  {
    [DebuggerNonUserCode] get { return this._btnP8S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP8S4_Click);
      if (this._btnP8S4 != null)
        this._btnP8S4.Click -= eventHandler;
      this._btnP8S4 = value;
      if (this._btnP8S4 == null)
        return;
      this._btnP8S4.Click += eventHandler;
    }
  }

  internal virtual Button btnP7S4
  {
    [DebuggerNonUserCode] get { return this._btnP7S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP7S4_Click);
      if (this._btnP7S4 != null)
        this._btnP7S4.Click -= eventHandler;
      this._btnP7S4 = value;
      if (this._btnP7S4 == null)
        return;
      this._btnP7S4.Click += eventHandler;
    }
  }

  internal virtual Button btnP6S4
  {
    [DebuggerNonUserCode] get { return this._btnP6S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP6S4_Click);
      if (this._btnP6S4 != null)
        this._btnP6S4.Click -= eventHandler;
      this._btnP6S4 = value;
      if (this._btnP6S4 == null)
        return;
      this._btnP6S4.Click += eventHandler;
    }
  }

  internal virtual Button btnP5S4
  {
    [DebuggerNonUserCode] get { return this._btnP5S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP5S4_Click);
      if (this._btnP5S4 != null)
        this._btnP5S4.Click -= eventHandler;
      this._btnP5S4 = value;
      if (this._btnP5S4 == null)
        return;
      this._btnP5S4.Click += eventHandler;
    }
  }

  internal virtual Button btnP4S4
  {
    [DebuggerNonUserCode] get { return this._btnP4S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP4S4_Click);
      if (this._btnP4S4 != null)
        this._btnP4S4.Click -= eventHandler;
      this._btnP4S4 = value;
      if (this._btnP4S4 == null)
        return;
      this._btnP4S4.Click += eventHandler;
    }
  }

  internal virtual Button btnP3S4
  {
    [DebuggerNonUserCode] get { return this._btnP3S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP3S4_Click);
      if (this._btnP3S4 != null)
        this._btnP3S4.Click -= eventHandler;
      this._btnP3S4 = value;
      if (this._btnP3S4 == null)
        return;
      this._btnP3S4.Click += eventHandler;
    }
  }

  internal virtual Button btnP2S4
  {
    [DebuggerNonUserCode] get { return this._btnP2S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP2S4_Click);
      if (this._btnP2S4 != null)
        this._btnP2S4.Click -= eventHandler;
      this._btnP2S4 = value;
      if (this._btnP2S4 == null)
        return;
      this._btnP2S4.Click += eventHandler;
    }
  }

  internal virtual Button btnP1S4
  {
    [DebuggerNonUserCode] get { return this._btnP1S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP1S4_Click);
      if (this._btnP1S4 != null)
        this._btnP1S4.Click -= eventHandler;
      this._btnP1S4 = value;
      if (this._btnP1S4 == null)
        return;
      this._btnP1S4.Click += eventHandler;
    }
  }

  internal virtual ComboBox cmbP16S4
  {
    [DebuggerNonUserCode] get { return this._cmbP16S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP16S4 = value; }
  }

  internal virtual ComboBox cmbP15S4
  {
    [DebuggerNonUserCode] get { return this._cmbP15S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP15S4 = value; }
  }

  internal virtual ComboBox cmbP14S4
  {
    [DebuggerNonUserCode] get { return this._cmbP14S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP14S4 = value; }
  }

  internal virtual ComboBox cmbP13S4
  {
    [DebuggerNonUserCode] get { return this._cmbP13S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP13S4 = value; }
  }

  internal virtual ComboBox cmbP12S4
  {
    [DebuggerNonUserCode] get { return this._cmbP12S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP12S4 = value; }
  }

  internal virtual ComboBox cmbP11S4
  {
    [DebuggerNonUserCode] get { return this._cmbP11S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP11S4 = value; }
  }

  internal virtual ComboBox cmbP10S4
  {
    [DebuggerNonUserCode] get { return this._cmbP10S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP10S4 = value; }
  }

  internal virtual ComboBox cmbP9S4
  {
    [DebuggerNonUserCode] get { return this._cmbP9S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP9S4 = value; }
  }

  internal virtual ComboBox cmbP8S4
  {
    [DebuggerNonUserCode] get { return this._cmbP8S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP8S4 = value; }
  }

  internal virtual ComboBox cmbP7S4
  {
    [DebuggerNonUserCode] get { return this._cmbP7S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP7S4 = value; }
  }

  internal virtual ComboBox cmbP6S4
  {
    [DebuggerNonUserCode] get { return this._cmbP6S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP6S4 = value; }
  }

  internal virtual ComboBox cmbP5S4
  {
    [DebuggerNonUserCode] get { return this._cmbP5S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP5S4 = value; }
  }

  internal virtual ComboBox cmbP4S4
  {
    [DebuggerNonUserCode] get { return this._cmbP4S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP4S4 = value; }
  }

  internal virtual ComboBox cmbP3S4
  {
    [DebuggerNonUserCode] get { return this._cmbP3S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP3S4 = value; }
  }

  internal virtual ComboBox cmbP2S4
  {
    [DebuggerNonUserCode] get { return this._cmbP2S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP2S4 = value; }
  }

  internal virtual ComboBox cmbP1S4
  {
    [DebuggerNonUserCode] get { return this._cmbP1S4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP1S4 = value; }
  }

  internal virtual Button btnP16S3
  {
    [DebuggerNonUserCode] get { return this._btnP16S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP16S3_Click);
      if (this._btnP16S3 != null)
        this._btnP16S3.Click -= eventHandler;
      this._btnP16S3 = value;
      if (this._btnP16S3 == null)
        return;
      this._btnP16S3.Click += eventHandler;
    }
  }

  internal virtual Button btnP15S3
  {
    [DebuggerNonUserCode] get { return this._btnP15S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP15S3_Click);
      if (this._btnP15S3 != null)
        this._btnP15S3.Click -= eventHandler;
      this._btnP15S3 = value;
      if (this._btnP15S3 == null)
        return;
      this._btnP15S3.Click += eventHandler;
    }
  }

  internal virtual Button btnP14S3
  {
    [DebuggerNonUserCode] get { return this._btnP14S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP14S3_Click);
      if (this._btnP14S3 != null)
        this._btnP14S3.Click -= eventHandler;
      this._btnP14S3 = value;
      if (this._btnP14S3 == null)
        return;
      this._btnP14S3.Click += eventHandler;
    }
  }

  internal virtual Button btnP13S3
  {
    [DebuggerNonUserCode] get { return this._btnP13S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP13S3_Click);
      if (this._btnP13S3 != null)
        this._btnP13S3.Click -= eventHandler;
      this._btnP13S3 = value;
      if (this._btnP13S3 == null)
        return;
      this._btnP13S3.Click += eventHandler;
    }
  }

  internal virtual Button btnP12S3
  {
    [DebuggerNonUserCode] get { return this._btnP12S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP12S3_Click);
      if (this._btnP12S3 != null)
        this._btnP12S3.Click -= eventHandler;
      this._btnP12S3 = value;
      if (this._btnP12S3 == null)
        return;
      this._btnP12S3.Click += eventHandler;
    }
  }

  internal virtual Button btnP11S3
  {
    [DebuggerNonUserCode] get { return this._btnP11S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP11S3_Click);
      if (this._btnP11S3 != null)
        this._btnP11S3.Click -= eventHandler;
      this._btnP11S3 = value;
      if (this._btnP11S3 == null)
        return;
      this._btnP11S3.Click += eventHandler;
    }
  }

  internal virtual Button btnP10S3
  {
    [DebuggerNonUserCode] get { return this._btnP10S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP10S3_Click);
      if (this._btnP10S3 != null)
        this._btnP10S3.Click -= eventHandler;
      this._btnP10S3 = value;
      if (this._btnP10S3 == null)
        return;
      this._btnP10S3.Click += eventHandler;
    }
  }

  internal virtual Button btnP9S3
  {
    [DebuggerNonUserCode] get { return this._btnP9S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP9S3_Click);
      if (this._btnP9S3 != null)
        this._btnP9S3.Click -= eventHandler;
      this._btnP9S3 = value;
      if (this._btnP9S3 == null)
        return;
      this._btnP9S3.Click += eventHandler;
    }
  }

  internal virtual Button btnP8S3
  {
    [DebuggerNonUserCode] get { return this._btnP8S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP8S3_Click);
      if (this._btnP8S3 != null)
        this._btnP8S3.Click -= eventHandler;
      this._btnP8S3 = value;
      if (this._btnP8S3 == null)
        return;
      this._btnP8S3.Click += eventHandler;
    }
  }

  internal virtual Button btnP7S3
  {
    [DebuggerNonUserCode] get { return this._btnP7S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP7S3_Click);
      if (this._btnP7S3 != null)
        this._btnP7S3.Click -= eventHandler;
      this._btnP7S3 = value;
      if (this._btnP7S3 == null)
        return;
      this._btnP7S3.Click += eventHandler;
    }
  }

  internal virtual Button btnP6S3
  {
    [DebuggerNonUserCode] get { return this._btnP6S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP6S3_Click);
      if (this._btnP6S3 != null)
        this._btnP6S3.Click -= eventHandler;
      this._btnP6S3 = value;
      if (this._btnP6S3 == null)
        return;
      this._btnP6S3.Click += eventHandler;
    }
  }

  internal virtual Button btnP5S3
  {
    [DebuggerNonUserCode] get { return this._btnP5S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP5S3_Click);
      if (this._btnP5S3 != null)
        this._btnP5S3.Click -= eventHandler;
      this._btnP5S3 = value;
      if (this._btnP5S3 == null)
        return;
      this._btnP5S3.Click += eventHandler;
    }
  }

  internal virtual Button btnP4S3
  {
    [DebuggerNonUserCode] get { return this._btnP4S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP4S3_Click);
      if (this._btnP4S3 != null)
        this._btnP4S3.Click -= eventHandler;
      this._btnP4S3 = value;
      if (this._btnP4S3 == null)
        return;
      this._btnP4S3.Click += eventHandler;
    }
  }

  internal virtual Button btnP3S3
  {
    [DebuggerNonUserCode] get { return this._btnP3S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP3S3_Click);
      if (this._btnP3S3 != null)
        this._btnP3S3.Click -= eventHandler;
      this._btnP3S3 = value;
      if (this._btnP3S3 == null)
        return;
      this._btnP3S3.Click += eventHandler;
    }
  }

  internal virtual Button btnP2S3
  {
    [DebuggerNonUserCode] get { return this._btnP2S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP2S3_Click);
      if (this._btnP2S3 != null)
        this._btnP2S3.Click -= eventHandler;
      this._btnP2S3 = value;
      if (this._btnP2S3 == null)
        return;
      this._btnP2S3.Click += eventHandler;
    }
  }

  internal virtual Button btnP1S3
  {
    [DebuggerNonUserCode] get { return this._btnP1S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP1S3_Click);
      if (this._btnP1S3 != null)
        this._btnP1S3.Click -= eventHandler;
      this._btnP1S3 = value;
      if (this._btnP1S3 == null)
        return;
      this._btnP1S3.Click += eventHandler;
    }
  }

  internal virtual ComboBox cmbP16S3
  {
    [DebuggerNonUserCode] get { return this._cmbP16S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP16S3 = value; }
  }

  internal virtual ComboBox cmbP15S3
  {
    [DebuggerNonUserCode] get { return this._cmbP15S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP15S3 = value; }
  }

  internal virtual ComboBox cmbP14S3
  {
    [DebuggerNonUserCode] get { return this._cmbP14S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP14S3 = value; }
  }

  internal virtual ComboBox cmbP13S3
  {
    [DebuggerNonUserCode] get { return this._cmbP13S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP13S3 = value; }
  }

  internal virtual ComboBox cmbP12S3
  {
    [DebuggerNonUserCode] get { return this._cmbP12S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP12S3 = value; }
  }

  internal virtual ComboBox cmbP11S3
  {
    [DebuggerNonUserCode] get { return this._cmbP11S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP11S3 = value; }
  }

  internal virtual ComboBox cmbP10S3
  {
    [DebuggerNonUserCode] get { return this._cmbP10S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP10S3 = value; }
  }

  internal virtual ComboBox cmbP9S3
  {
    [DebuggerNonUserCode] get { return this._cmbP9S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP9S3 = value; }
  }

  internal virtual ComboBox cmbP8S3
  {
    [DebuggerNonUserCode] get { return this._cmbP8S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP8S3 = value; }
  }

  internal virtual ComboBox cmbP7S3
  {
    [DebuggerNonUserCode] get { return this._cmbP7S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP7S3 = value; }
  }

  internal virtual ComboBox cmbP6S3
  {
    [DebuggerNonUserCode] get { return this._cmbP6S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP6S3 = value; }
  }

  internal virtual ComboBox cmbP5S3
  {
    [DebuggerNonUserCode] get { return this._cmbP5S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP5S3 = value; }
  }

  internal virtual ComboBox cmbP4S3
  {
    [DebuggerNonUserCode] get { return this._cmbP4S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP4S3 = value; }
  }

  internal virtual ComboBox cmbP3S3
  {
    [DebuggerNonUserCode] get { return this._cmbP3S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP3S3 = value; }
  }

  internal virtual ComboBox cmbP2S3
  {
    [DebuggerNonUserCode] get { return this._cmbP2S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP2S3 = value; }
  }

  internal virtual ComboBox cmbP1S3
  {
    [DebuggerNonUserCode] get { return this._cmbP1S3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP1S3 = value; }
  }

  internal virtual Button btnP16S2
  {
    [DebuggerNonUserCode] get { return this._btnP16S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP16S2_Click);
      if (this._btnP16S2 != null)
        this._btnP16S2.Click -= eventHandler;
      this._btnP16S2 = value;
      if (this._btnP16S2 == null)
        return;
      this._btnP16S2.Click += eventHandler;
    }
  }

  internal virtual Button btnP15S2
  {
    [DebuggerNonUserCode] get { return this._btnP15S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP15S2_Click);
      if (this._btnP15S2 != null)
        this._btnP15S2.Click -= eventHandler;
      this._btnP15S2 = value;
      if (this._btnP15S2 == null)
        return;
      this._btnP15S2.Click += eventHandler;
    }
  }

  internal virtual Button btnP14S2
  {
    [DebuggerNonUserCode] get { return this._btnP14S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP14S2_Click);
      if (this._btnP14S2 != null)
        this._btnP14S2.Click -= eventHandler;
      this._btnP14S2 = value;
      if (this._btnP14S2 == null)
        return;
      this._btnP14S2.Click += eventHandler;
    }
  }

  internal virtual Button btnP13S2
  {
    [DebuggerNonUserCode] get { return this._btnP13S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP13S2_Click);
      if (this._btnP13S2 != null)
        this._btnP13S2.Click -= eventHandler;
      this._btnP13S2 = value;
      if (this._btnP13S2 == null)
        return;
      this._btnP13S2.Click += eventHandler;
    }
  }

  internal virtual Button btnP12S2
  {
    [DebuggerNonUserCode] get { return this._btnP12S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP12S2_Click);
      if (this._btnP12S2 != null)
        this._btnP12S2.Click -= eventHandler;
      this._btnP12S2 = value;
      if (this._btnP12S2 == null)
        return;
      this._btnP12S2.Click += eventHandler;
    }
  }

  internal virtual Button btnP11S2
  {
    [DebuggerNonUserCode] get { return this._btnP11S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP11S2_Click);
      if (this._btnP11S2 != null)
        this._btnP11S2.Click -= eventHandler;
      this._btnP11S2 = value;
      if (this._btnP11S2 == null)
        return;
      this._btnP11S2.Click += eventHandler;
    }
  }

  internal virtual Button btnP10S2
  {
    [DebuggerNonUserCode] get { return this._btnP10S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP10S2_Click);
      if (this._btnP10S2 != null)
        this._btnP10S2.Click -= eventHandler;
      this._btnP10S2 = value;
      if (this._btnP10S2 == null)
        return;
      this._btnP10S2.Click += eventHandler;
    }
  }

  internal virtual Button btnP9S2
  {
    [DebuggerNonUserCode] get { return this._btnP9S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP9S2_Click);
      if (this._btnP9S2 != null)
        this._btnP9S2.Click -= eventHandler;
      this._btnP9S2 = value;
      if (this._btnP9S2 == null)
        return;
      this._btnP9S2.Click += eventHandler;
    }
  }

  internal virtual Button btnP8S2
  {
    [DebuggerNonUserCode] get { return this._btnP8S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP8S2_Click);
      if (this._btnP8S2 != null)
        this._btnP8S2.Click -= eventHandler;
      this._btnP8S2 = value;
      if (this._btnP8S2 == null)
        return;
      this._btnP8S2.Click += eventHandler;
    }
  }

  internal virtual Button btnP7S2
  {
    [DebuggerNonUserCode] get { return this._btnP7S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP7S2_Click);
      if (this._btnP7S2 != null)
        this._btnP7S2.Click -= eventHandler;
      this._btnP7S2 = value;
      if (this._btnP7S2 == null)
        return;
      this._btnP7S2.Click += eventHandler;
    }
  }

  internal virtual Button btnP6S2
  {
    [DebuggerNonUserCode] get { return this._btnP6S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP6S2_Click);
      if (this._btnP6S2 != null)
        this._btnP6S2.Click -= eventHandler;
      this._btnP6S2 = value;
      if (this._btnP6S2 == null)
        return;
      this._btnP6S2.Click += eventHandler;
    }
  }

  internal virtual Button btnP5S2
  {
    [DebuggerNonUserCode] get { return this._btnP5S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP5S2_Click);
      if (this._btnP5S2 != null)
        this._btnP5S2.Click -= eventHandler;
      this._btnP5S2 = value;
      if (this._btnP5S2 == null)
        return;
      this._btnP5S2.Click += eventHandler;
    }
  }

  internal virtual Button btnP4S2
  {
    [DebuggerNonUserCode] get { return this._btnP4S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP4S2_Click);
      if (this._btnP4S2 != null)
        this._btnP4S2.Click -= eventHandler;
      this._btnP4S2 = value;
      if (this._btnP4S2 == null)
        return;
      this._btnP4S2.Click += eventHandler;
    }
  }

  internal virtual Button btnP3S2
  {
    [DebuggerNonUserCode] get { return this._btnP3S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP3S2_Click);
      if (this._btnP3S2 != null)
        this._btnP3S2.Click -= eventHandler;
      this._btnP3S2 = value;
      if (this._btnP3S2 == null)
        return;
      this._btnP3S2.Click += eventHandler;
    }
  }

  internal virtual Button btnP2S2
  {
    [DebuggerNonUserCode] get { return this._btnP2S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP2S2_Click);
      if (this._btnP2S2 != null)
        this._btnP2S2.Click -= eventHandler;
      this._btnP2S2 = value;
      if (this._btnP2S2 == null)
        return;
      this._btnP2S2.Click += eventHandler;
    }
  }

  internal virtual Button btnP1S2
  {
    [DebuggerNonUserCode] get { return this._btnP1S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP1S2_Click);
      if (this._btnP1S2 != null)
        this._btnP1S2.Click -= eventHandler;
      this._btnP1S2 = value;
      if (this._btnP1S2 == null)
        return;
      this._btnP1S2.Click += eventHandler;
    }
  }

  internal virtual ComboBox cmbP16S2
  {
    [DebuggerNonUserCode] get { return this._cmbP16S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP16S2 = value; }
  }

  internal virtual ComboBox cmbP15S2
  {
    [DebuggerNonUserCode] get { return this._cmbP15S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP15S2 = value; }
  }

  internal virtual ComboBox cmbP14S2
  {
    [DebuggerNonUserCode] get { return this._cmbP14S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP14S2 = value; }
  }

  internal virtual ComboBox cmbP13S2
  {
    [DebuggerNonUserCode] get { return this._cmbP13S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP13S2 = value; }
  }

  internal virtual ComboBox cmbP12S2
  {
    [DebuggerNonUserCode] get { return this._cmbP12S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP12S2 = value; }
  }

  internal virtual ComboBox cmbP11S2
  {
    [DebuggerNonUserCode] get { return this._cmbP11S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP11S2 = value; }
  }

  internal virtual ComboBox cmbP10S2
  {
    [DebuggerNonUserCode] get { return this._cmbP10S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP10S2 = value; }
  }

  internal virtual ComboBox cmbP9S2
  {
    [DebuggerNonUserCode] get { return this._cmbP9S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP9S2 = value; }
  }

  internal virtual ComboBox cmbP8S2
  {
    [DebuggerNonUserCode] get { return this._cmbP8S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP8S2 = value; }
  }

  internal virtual ComboBox cmbP7S2
  {
    [DebuggerNonUserCode] get { return this._cmbP7S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP7S2 = value; }
  }

  internal virtual ComboBox cmbP6S2
  {
    [DebuggerNonUserCode] get { return this._cmbP6S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP6S2 = value; }
  }

  internal virtual ComboBox cmbP5S2
  {
    [DebuggerNonUserCode] get { return this._cmbP5S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP5S2 = value; }
  }

  internal virtual ComboBox cmbP4S2
  {
    [DebuggerNonUserCode] get { return this._cmbP4S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP4S2 = value; }
  }

  internal virtual ComboBox cmbP3S2
  {
    [DebuggerNonUserCode] get { return this._cmbP3S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP3S2 = value; }
  }

  internal virtual ComboBox cmbP2S2
  {
    [DebuggerNonUserCode] get { return this._cmbP2S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP2S2 = value; }
  }

  internal virtual ComboBox cmbP1S2
  {
    [DebuggerNonUserCode] get { return this._cmbP1S2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP1S2 = value; }
  }

  internal virtual Button btnP16S1
  {
    [DebuggerNonUserCode] get { return this._btnP16S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP16S1_Click);
      if (this._btnP16S1 != null)
        this._btnP16S1.Click -= eventHandler;
      this._btnP16S1 = value;
      if (this._btnP16S1 == null)
        return;
      this._btnP16S1.Click += eventHandler;
    }
  }

  internal virtual Button btnP15S1
  {
    [DebuggerNonUserCode] get { return this._btnP15S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP15S1_Click);
      if (this._btnP15S1 != null)
        this._btnP15S1.Click -= eventHandler;
      this._btnP15S1 = value;
      if (this._btnP15S1 == null)
        return;
      this._btnP15S1.Click += eventHandler;
    }
  }

  internal virtual Button btnP14S1
  {
    [DebuggerNonUserCode] get { return this._btnP14S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP14S1_Click);
      if (this._btnP14S1 != null)
        this._btnP14S1.Click -= eventHandler;
      this._btnP14S1 = value;
      if (this._btnP14S1 == null)
        return;
      this._btnP14S1.Click += eventHandler;
    }
  }

  internal virtual Button btnP13S1
  {
    [DebuggerNonUserCode] get { return this._btnP13S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP13S1_Click);
      if (this._btnP13S1 != null)
        this._btnP13S1.Click -= eventHandler;
      this._btnP13S1 = value;
      if (this._btnP13S1 == null)
        return;
      this._btnP13S1.Click += eventHandler;
    }
  }

  internal virtual Button btnP12S1
  {
    [DebuggerNonUserCode] get { return this._btnP12S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP12S1_Click);
      if (this._btnP12S1 != null)
        this._btnP12S1.Click -= eventHandler;
      this._btnP12S1 = value;
      if (this._btnP12S1 == null)
        return;
      this._btnP12S1.Click += eventHandler;
    }
  }

  internal virtual Button btnP11S1
  {
    [DebuggerNonUserCode] get { return this._btnP11S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP11S1_Click);
      if (this._btnP11S1 != null)
        this._btnP11S1.Click -= eventHandler;
      this._btnP11S1 = value;
      if (this._btnP11S1 == null)
        return;
      this._btnP11S1.Click += eventHandler;
    }
  }

  internal virtual Button btnP10S1
  {
    [DebuggerNonUserCode] get { return this._btnP10S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP10S1_Click);
      if (this._btnP10S1 != null)
        this._btnP10S1.Click -= eventHandler;
      this._btnP10S1 = value;
      if (this._btnP10S1 == null)
        return;
      this._btnP10S1.Click += eventHandler;
    }
  }

  internal virtual Button btnP9S1
  {
    [DebuggerNonUserCode] get { return this._btnP9S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP9S1_Click);
      if (this._btnP9S1 != null)
        this._btnP9S1.Click -= eventHandler;
      this._btnP9S1 = value;
      if (this._btnP9S1 == null)
        return;
      this._btnP9S1.Click += eventHandler;
    }
  }

  internal virtual Button btnP8S1
  {
    [DebuggerNonUserCode] get { return this._btnP8S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP8S1_Click);
      if (this._btnP8S1 != null)
        this._btnP8S1.Click -= eventHandler;
      this._btnP8S1 = value;
      if (this._btnP8S1 == null)
        return;
      this._btnP8S1.Click += eventHandler;
    }
  }

  internal virtual Button btnP7S1
  {
    [DebuggerNonUserCode] get { return this._btnP7S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP7S1_Click);
      if (this._btnP7S1 != null)
        this._btnP7S1.Click -= eventHandler;
      this._btnP7S1 = value;
      if (this._btnP7S1 == null)
        return;
      this._btnP7S1.Click += eventHandler;
    }
  }

  internal virtual Button btnP6S1
  {
    [DebuggerNonUserCode] get { return this._btnP6S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP6S1_Click);
      if (this._btnP6S1 != null)
        this._btnP6S1.Click -= eventHandler;
      this._btnP6S1 = value;
      if (this._btnP6S1 == null)
        return;
      this._btnP6S1.Click += eventHandler;
    }
  }

  internal virtual Button btnP5S1
  {
    [DebuggerNonUserCode] get { return this._btnP5S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP5S1_Click);
      if (this._btnP5S1 != null)
        this._btnP5S1.Click -= eventHandler;
      this._btnP5S1 = value;
      if (this._btnP5S1 == null)
        return;
      this._btnP5S1.Click += eventHandler;
    }
  }

  internal virtual Button btnP4S1
  {
    [DebuggerNonUserCode] get { return this._btnP4S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP4S1_Click);
      if (this._btnP4S1 != null)
        this._btnP4S1.Click -= eventHandler;
      this._btnP4S1 = value;
      if (this._btnP4S1 == null)
        return;
      this._btnP4S1.Click += eventHandler;
    }
  }

  internal virtual Button btnP3S1
  {
    [DebuggerNonUserCode] get { return this._btnP3S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP3S1_Click);
      if (this._btnP3S1 != null)
        this._btnP3S1.Click -= eventHandler;
      this._btnP3S1 = value;
      if (this._btnP3S1 == null)
        return;
      this._btnP3S1.Click += eventHandler;
    }
  }

  internal virtual Button btnP2S1
  {
    [DebuggerNonUserCode] get { return this._btnP2S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP2S1_Click);
      if (this._btnP2S1 != null)
        this._btnP2S1.Click -= eventHandler;
      this._btnP2S1 = value;
      if (this._btnP2S1 == null)
        return;
      this._btnP2S1.Click += eventHandler;
    }
  }

  internal virtual Button btnP1S1
  {
    [DebuggerNonUserCode] get { return this._btnP1S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnP1S1_Click);
      if (this._btnP1S1 != null)
        this._btnP1S1.Click -= eventHandler;
      this._btnP1S1 = value;
      if (this._btnP1S1 == null)
        return;
      this._btnP1S1.Click += eventHandler;
    }
  }

  internal virtual ComboBox cmbP16S1
  {
    [DebuggerNonUserCode] get { return this._cmbP16S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP16S1 = value; }
  }

  internal virtual ComboBox cmbP15S1
  {
    [DebuggerNonUserCode] get { return this._cmbP15S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP15S1 = value; }
  }

  internal virtual ComboBox cmbP14S1
  {
    [DebuggerNonUserCode] get { return this._cmbP14S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP14S1 = value; }
  }

  internal virtual ComboBox cmbP13S1
  {
    [DebuggerNonUserCode] get { return this._cmbP13S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP13S1 = value; }
  }

  internal virtual ComboBox cmbP12S1
  {
    [DebuggerNonUserCode] get { return this._cmbP12S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP12S1 = value; }
  }

  internal virtual ComboBox cmbP11S1
  {
    [DebuggerNonUserCode] get { return this._cmbP11S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP11S1 = value; }
  }

  internal virtual ComboBox cmbP10S1
  {
    [DebuggerNonUserCode] get { return this._cmbP10S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP10S1 = value; }
  }

  internal virtual ComboBox cmbP9S1
  {
    [DebuggerNonUserCode] get { return this._cmbP9S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP9S1 = value; }
  }

  internal virtual ComboBox cmbP8S1
  {
    [DebuggerNonUserCode] get { return this._cmbP8S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP8S1 = value; }
  }

  internal virtual ComboBox cmbP7S1
  {
    [DebuggerNonUserCode] get { return this._cmbP7S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP7S1 = value; }
  }

  internal virtual ComboBox cmbP6S1
  {
    [DebuggerNonUserCode] get { return this._cmbP6S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP6S1 = value; }
  }

  internal virtual ComboBox cmbP5S1
  {
    [DebuggerNonUserCode] get { return this._cmbP5S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP5S1 = value; }
  }

  internal virtual ComboBox cmbP4S1
  {
    [DebuggerNonUserCode] get { return this._cmbP4S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP4S1 = value; }
  }

  internal virtual ComboBox cmbP3S1
  {
    [DebuggerNonUserCode] get { return this._cmbP3S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP3S1 = value; }
  }

  internal virtual ComboBox cmbP2S1
  {
    [DebuggerNonUserCode] get { return this._cmbP2S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP2S1 = value; }
  }

  internal virtual ComboBox cmbP1S1
  {
    [DebuggerNonUserCode] get { return this._cmbP1S1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbP1S1 = value; }
  }

  internal virtual CheckBox chkPort16
  {
    [DebuggerNonUserCode] get { return this._chkPort16; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.chkPort16_CheckedChanged);
      if (this._chkPort16 != null)
        this._chkPort16.CheckedChanged -= eventHandler;
      this._chkPort16 = value;
      if (this._chkPort16 == null)
        return;
      this._chkPort16.CheckedChanged += eventHandler;
    }
  }

  internal virtual CheckBox chkPort15
  {
    [DebuggerNonUserCode] get { return this._chkPort15; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.chkPort15_CheckedChanged);
      if (this._chkPort15 != null)
        this._chkPort15.CheckedChanged -= eventHandler;
      this._chkPort15 = value;
      if (this._chkPort15 == null)
        return;
      this._chkPort15.CheckedChanged += eventHandler;
    }
  }

  internal virtual CheckBox chkPort14
  {
    [DebuggerNonUserCode] get { return this._chkPort14; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.chkPort14_CheckedChanged);
      if (this._chkPort14 != null)
        this._chkPort14.CheckedChanged -= eventHandler;
      this._chkPort14 = value;
      if (this._chkPort14 == null)
        return;
      this._chkPort14.CheckedChanged += eventHandler;
    }
  }

  internal virtual CheckBox chkPort13
  {
    [DebuggerNonUserCode] get { return this._chkPort13; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.chkPort13_CheckedChanged);
      if (this._chkPort13 != null)
        this._chkPort13.CheckedChanged -= eventHandler;
      this._chkPort13 = value;
      if (this._chkPort13 == null)
        return;
      this._chkPort13.CheckedChanged += eventHandler;
    }
  }

  internal virtual CheckBox chkPort12
  {
    [DebuggerNonUserCode] get { return this._chkPort12; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.chkPort12_CheckedChanged);
      if (this._chkPort12 != null)
        this._chkPort12.CheckedChanged -= eventHandler;
      this._chkPort12 = value;
      if (this._chkPort12 == null)
        return;
      this._chkPort12.CheckedChanged += eventHandler;
    }
  }

  internal virtual CheckBox chkPort11
  {
    [DebuggerNonUserCode] get { return this._chkPort11; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.chkPort11_CheckedChanged);
      if (this._chkPort11 != null)
        this._chkPort11.CheckedChanged -= eventHandler;
      this._chkPort11 = value;
      if (this._chkPort11 == null)
        return;
      this._chkPort11.CheckedChanged += eventHandler;
    }
  }

  internal virtual CheckBox chkPort10
  {
    [DebuggerNonUserCode] get { return this._chkPort10; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.chkPort10_CheckedChanged);
      if (this._chkPort10 != null)
        this._chkPort10.CheckedChanged -= eventHandler;
      this._chkPort10 = value;
      if (this._chkPort10 == null)
        return;
      this._chkPort10.CheckedChanged += eventHandler;
    }
  }

  internal virtual CheckBox chkPort9
  {
    [DebuggerNonUserCode] get { return this._chkPort9; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.chkPort9_CheckedChanged);
      if (this._chkPort9 != null)
        this._chkPort9.CheckedChanged -= eventHandler;
      this._chkPort9 = value;
      if (this._chkPort9 == null)
        return;
      this._chkPort9.CheckedChanged += eventHandler;
    }
  }

  internal virtual CheckBox chkPort8
  {
    [DebuggerNonUserCode] get { return this._chkPort8; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.chkPort8_CheckedChanged);
      if (this._chkPort8 != null)
        this._chkPort8.CheckedChanged -= eventHandler;
      this._chkPort8 = value;
      if (this._chkPort8 == null)
        return;
      this._chkPort8.CheckedChanged += eventHandler;
    }
  }

  internal virtual CheckBox chkPort7
  {
    [DebuggerNonUserCode] get { return this._chkPort7; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.chkPort7_CheckedChanged);
      if (this._chkPort7 != null)
        this._chkPort7.CheckedChanged -= eventHandler;
      this._chkPort7 = value;
      if (this._chkPort7 == null)
        return;
      this._chkPort7.CheckedChanged += eventHandler;
    }
  }

  internal virtual CheckBox chkPort6
  {
    [DebuggerNonUserCode] get { return this._chkPort6; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.chkPort6_CheckedChanged);
      if (this._chkPort6 != null)
        this._chkPort6.CheckedChanged -= eventHandler;
      this._chkPort6 = value;
      if (this._chkPort6 == null)
        return;
      this._chkPort6.CheckedChanged += eventHandler;
    }
  }

  internal virtual CheckBox chkPort5
  {
    [DebuggerNonUserCode] get { return this._chkPort5; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.chkPort5_CheckedChanged);
      if (this._chkPort5 != null)
        this._chkPort5.CheckedChanged -= eventHandler;
      this._chkPort5 = value;
      if (this._chkPort5 == null)
        return;
      this._chkPort5.CheckedChanged += eventHandler;
    }
  }

  internal virtual CheckBox chkPort4
  {
    [DebuggerNonUserCode] get { return this._chkPort4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.chkPort4_CheckedChanged);
      if (this._chkPort4 != null)
        this._chkPort4.CheckedChanged -= eventHandler;
      this._chkPort4 = value;
      if (this._chkPort4 == null)
        return;
      this._chkPort4.CheckedChanged += eventHandler;
    }
  }

  internal virtual CheckBox chkPort3
  {
    [DebuggerNonUserCode] get { return this._chkPort3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.chkPort3_CheckedChanged);
      if (this._chkPort3 != null)
        this._chkPort3.CheckedChanged -= eventHandler;
      this._chkPort3 = value;
      if (this._chkPort3 == null)
        return;
      this._chkPort3.CheckedChanged += eventHandler;
    }
  }

  internal virtual CheckBox chkPort2
  {
    [DebuggerNonUserCode] get { return this._chkPort2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.chkPort2_CheckedChanged);
      if (this._chkPort2 != null)
        this._chkPort2.CheckedChanged -= eventHandler;
      this._chkPort2 = value;
      if (this._chkPort2 == null)
        return;
      this._chkPort2.CheckedChanged += eventHandler;
    }
  }

  internal virtual CheckBox chkPort1
  {
    [DebuggerNonUserCode] get { return this._chkPort1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.chkPort1_CheckedChanged);
      if (this._chkPort1 != null)
        this._chkPort1.CheckedChanged -= eventHandler;
      this._chkPort1 = value;
      if (this._chkPort1 == null)
        return;
      this._chkPort1.CheckedChanged += eventHandler;
    }
  }

  internal virtual Button btnOk
  {
    [DebuggerNonUserCode] get { return this._btnOk; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnOk_Click);
      if (this._btnOk != null)
        this._btnOk.Click -= eventHandler;
      this._btnOk = value;
      if (this._btnOk == null)
        return;
      this._btnOk.Click += eventHandler;
    }
  }

  internal virtual Label lblPfno
  {
    [DebuggerNonUserCode] get { return this._lblPfno; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._lblPfno = value; }
  }

  internal virtual TextBox txtPdchName
  {
    [DebuggerNonUserCode] get { return this._txtPdchName; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtPdchName = value;
    }
  }

  internal virtual TextBox txtPdchAddress
  {
    [DebuggerNonUserCode] get { return this._txtPdchAddress; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtPdchAddress = value;
    }
  }

  internal virtual Label lblName
  {
    [DebuggerNonUserCode] get { return this._lblName; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._lblName = value; }
  }

  internal virtual Label lblAddress
  {
    [DebuggerNonUserCode] get { return this._lblAddress; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblAddress = value;
    }
  }

  internal virtual Label lblSharedPfno
  {
    [DebuggerNonUserCode] get { return this._lblSharedPfno; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblSharedPfno = value;
    }
  }

  internal virtual Label Label1
  {
    [DebuggerNonUserCode] get { return this._Label1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Label1 = value; }
  }

  internal virtual CheckBox chkPDCHSharedPfNo
  {
    [DebuggerNonUserCode] get { return this._chkPDCHSharedPfNo; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.chkPDCHSharedPfNo_CheckedChanged);
      if (this._chkPDCHSharedPfNo != null)
        this._chkPDCHSharedPfNo.CheckedChanged -= eventHandler;
      this._chkPDCHSharedPfNo = value;
      if (this._chkPDCHSharedPfNo == null)
        return;
      this._chkPDCHSharedPfNo.CheckedChanged += eventHandler;
    }
  }

  internal virtual ComboBox cmbPdchPfno
  {
    [DebuggerNonUserCode] get { return this._cmbPdchPfno; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._cmbPdchPfno = value;
    }
  }

  internal virtual ComboBox cmbPdchSharedPfno
  {
    [DebuggerNonUserCode] get { return this._cmbPdchSharedPfno; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.cmbPdchSharedPfno_DropDown);
      if (this._cmbPdchSharedPfno != null)
        this._cmbPdchSharedPfno.DropDown -= eventHandler;
      this._cmbPdchSharedPfno = value;
      if (this._cmbPdchSharedPfno == null)
        return;
      this._cmbPdchSharedPfno.DropDown += eventHandler;
    }
  }

  protected virtual frmNetworkCGDB event_cgdb
  {
    [DebuggerNonUserCode] get { return this._event_cgdb; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._event_cgdb = value;
    }
  }

  protected virtual frmNetworkAGDB event_agdb
  {
    [DebuggerNonUserCode] get { return this._event_agdb; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._event_agdb = value;
    }
  }

  protected virtual frmNetworkPDB event_pdb
  {
    [DebuggerNonUserCode] get { return this._event_pdb; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._event_pdb = value;
    }
  }

  private void pdch_count_no_of_systems(CheckBox chkport, byte port_no, ComboBox[] cmbps)
  {
    byte index = 0;
    try
    {
      if (!chkport.Checked)
        return;
      frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].pdch_port[(int) port_no].no_of_systems = (byte) 0;
      while (index < (byte) 8)
      {
        if (Operators.CompareString(cmbps[(int) index].Text, "AGDB", false) == 0 | Operators.CompareString(cmbps[(int) index].Text, "PDB", false) == 0 | Operators.CompareString(cmbps[(int) index].Text, "CGDB", false) == 0)
          checked { ++frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].pdch_port[(int) port_no].no_of_systems; }
        checked { ++index; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void pdch_update_no_systems()
  {
    byte port_no1 = 0;
    ComboBox[] cmbps = new ComboBox[8];
    try
    {
      cmbps[0] = this.cmbP1S1;
      cmbps[1] = this.cmbP1S2;
      cmbps[2] = this.cmbP1S3;
      cmbps[3] = this.cmbP1S4;
      cmbps[4] = this.cmbP1S5;
      cmbps[5] = this.cmbP1S6;
      cmbps[6] = this.cmbP1S7;
      cmbps[7] = this.cmbP1S8;
      this.pdch_count_no_of_systems(this.chkPort1, port_no1, cmbps);
      byte port_no2 = checked ((byte) ((int) port_no1 + 1));
      cmbps[0] = this.cmbP2S1;
      cmbps[1] = this.cmbP2S2;
      cmbps[2] = this.cmbP2S3;
      cmbps[3] = this.cmbP2S4;
      cmbps[4] = this.cmbP2S5;
      cmbps[5] = this.cmbP2S6;
      cmbps[6] = this.cmbP2S7;
      cmbps[7] = this.cmbP2S8;
      this.pdch_count_no_of_systems(this.chkPort2, port_no2, cmbps);
      byte port_no3 = checked ((byte) ((int) port_no2 + 1));
      cmbps[0] = this.cmbP3S1;
      cmbps[1] = this.cmbP3S2;
      cmbps[2] = this.cmbP3S3;
      cmbps[3] = this.cmbP3S4;
      cmbps[4] = this.cmbP3S5;
      cmbps[5] = this.cmbP3S6;
      cmbps[6] = this.cmbP3S7;
      cmbps[7] = this.cmbP3S8;
      this.pdch_count_no_of_systems(this.chkPort3, port_no3, cmbps);
      byte port_no4 = checked ((byte) ((int) port_no3 + 1));
      cmbps[0] = this.cmbP4S1;
      cmbps[1] = this.cmbP4S2;
      cmbps[2] = this.cmbP4S3;
      cmbps[3] = this.cmbP4S4;
      cmbps[4] = this.cmbP4S5;
      cmbps[5] = this.cmbP4S6;
      cmbps[6] = this.cmbP4S7;
      cmbps[7] = this.cmbP4S8;
      this.pdch_count_no_of_systems(this.chkPort4, port_no4, cmbps);
      byte port_no5 = checked ((byte) ((int) port_no4 + 1));
      cmbps[0] = this.cmbP5S1;
      cmbps[1] = this.cmbP5S2;
      cmbps[2] = this.cmbP5S3;
      cmbps[3] = this.cmbP5S4;
      cmbps[4] = this.cmbP5S5;
      cmbps[5] = this.cmbP5S6;
      cmbps[6] = this.cmbP5S7;
      cmbps[7] = this.cmbP5S8;
      this.pdch_count_no_of_systems(this.chkPort5, port_no5, cmbps);
      byte port_no6 = checked ((byte) ((int) port_no5 + 1));
      cmbps[0] = this.cmbP6S1;
      cmbps[1] = this.cmbP6S2;
      cmbps[2] = this.cmbP6S3;
      cmbps[3] = this.cmbP6S4;
      cmbps[4] = this.cmbP6S5;
      cmbps[5] = this.cmbP6S6;
      cmbps[6] = this.cmbP6S7;
      cmbps[7] = this.cmbP6S8;
      this.pdch_count_no_of_systems(this.chkPort6, port_no6, cmbps);
      byte port_no7 = checked ((byte) ((int) port_no6 + 1));
      cmbps[0] = this.cmbP7S1;
      cmbps[1] = this.cmbP7S2;
      cmbps[2] = this.cmbP7S3;
      cmbps[3] = this.cmbP7S4;
      cmbps[4] = this.cmbP7S5;
      cmbps[5] = this.cmbP7S6;
      cmbps[6] = this.cmbP7S7;
      cmbps[7] = this.cmbP7S8;
      this.pdch_count_no_of_systems(this.chkPort7, port_no7, cmbps);
      byte port_no8 = checked ((byte) ((int) port_no7 + 1));
      cmbps[0] = this.cmbP8S1;
      cmbps[1] = this.cmbP8S2;
      cmbps[2] = this.cmbP8S3;
      cmbps[3] = this.cmbP8S4;
      cmbps[4] = this.cmbP8S5;
      cmbps[5] = this.cmbP8S6;
      cmbps[6] = this.cmbP8S7;
      cmbps[7] = this.cmbP8S8;
      this.pdch_count_no_of_systems(this.chkPort8, port_no8, cmbps);
      byte port_no9 = checked ((byte) ((int) port_no8 + 1));
      cmbps[0] = this.cmbP9S1;
      cmbps[1] = this.cmbP9S2;
      cmbps[2] = this.cmbP9S3;
      cmbps[3] = this.cmbP9S4;
      cmbps[4] = this.cmbP9S5;
      cmbps[5] = this.cmbP9S6;
      cmbps[6] = this.cmbP9S7;
      cmbps[7] = this.cmbP9S8;
      this.pdch_count_no_of_systems(this.chkPort9, port_no9, cmbps);
      byte port_no10 = checked ((byte) ((int) port_no9 + 1));
      cmbps[0] = this.cmbP10S1;
      cmbps[1] = this.cmbP10S2;
      cmbps[2] = this.cmbP10S3;
      cmbps[3] = this.cmbP10S4;
      cmbps[4] = this.cmbP10S5;
      cmbps[5] = this.cmbP10S6;
      cmbps[6] = this.cmbP10S7;
      cmbps[7] = this.cmbP10S8;
      this.pdch_count_no_of_systems(this.chkPort10, port_no10, cmbps);
      byte port_no11 = checked ((byte) ((int) port_no10 + 1));
      cmbps[0] = this.cmbP11S1;
      cmbps[1] = this.cmbP11S2;
      cmbps[2] = this.cmbP11S3;
      cmbps[3] = this.cmbP11S4;
      cmbps[4] = this.cmbP11S5;
      cmbps[5] = this.cmbP11S6;
      cmbps[6] = this.cmbP11S7;
      cmbps[7] = this.cmbP11S8;
      this.pdch_count_no_of_systems(this.chkPort11, port_no11, cmbps);
      byte port_no12 = checked ((byte) ((int) port_no11 + 1));
      cmbps[0] = this.cmbP12S1;
      cmbps[1] = this.cmbP12S2;
      cmbps[2] = this.cmbP12S3;
      cmbps[3] = this.cmbP12S4;
      cmbps[4] = this.cmbP12S5;
      cmbps[5] = this.cmbP12S6;
      cmbps[6] = this.cmbP12S7;
      cmbps[7] = this.cmbP12S8;
      this.pdch_count_no_of_systems(this.chkPort12, port_no12, cmbps);
      byte port_no13 = checked ((byte) ((int) port_no12 + 1));
      cmbps[0] = this.cmbP13S1;
      cmbps[1] = this.cmbP13S2;
      cmbps[2] = this.cmbP13S3;
      cmbps[3] = this.cmbP13S4;
      cmbps[4] = this.cmbP13S5;
      cmbps[5] = this.cmbP13S6;
      cmbps[6] = this.cmbP13S7;
      cmbps[7] = this.cmbP13S8;
      this.pdch_count_no_of_systems(this.chkPort13, port_no13, cmbps);
      byte port_no14 = checked ((byte) ((int) port_no13 + 1));
      cmbps[0] = this.cmbP14S1;
      cmbps[1] = this.cmbP14S2;
      cmbps[2] = this.cmbP14S3;
      cmbps[3] = this.cmbP14S4;
      cmbps[4] = this.cmbP14S5;
      cmbps[5] = this.cmbP14S6;
      cmbps[6] = this.cmbP14S7;
      cmbps[7] = this.cmbP14S8;
      this.pdch_count_no_of_systems(this.chkPort14, port_no14, cmbps);
      byte port_no15 = checked ((byte) ((int) port_no14 + 1));
      cmbps[0] = this.cmbP15S1;
      cmbps[1] = this.cmbP15S2;
      cmbps[2] = this.cmbP15S3;
      cmbps[3] = this.cmbP15S4;
      cmbps[4] = this.cmbP15S5;
      cmbps[5] = this.cmbP15S6;
      cmbps[6] = this.cmbP15S7;
      cmbps[7] = this.cmbP15S8;
      this.pdch_count_no_of_systems(this.chkPort15, port_no15, cmbps);
      byte port_no16 = checked ((byte) ((int) port_no15 + 1));
      cmbps[0] = this.cmbP16S1;
      cmbps[1] = this.cmbP16S2;
      cmbps[2] = this.cmbP16S3;
      cmbps[3] = this.cmbP16S4;
      cmbps[4] = this.cmbP16S5;
      cmbps[5] = this.cmbP16S6;
      cmbps[6] = this.cmbP16S7;
      cmbps[7] = this.cmbP16S8;
      this.pdch_count_no_of_systems(this.chkPort16, port_no16, cmbps);
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void chkPort1_CheckedChanged(object sender, EventArgs e)
  {
    if (this.chkPort1.Checked)
    {
      frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].port_status[0] = true;
      this.cmbP1S1.Visible = true;
      this.cmbP1S2.Visible = true;
      this.cmbP1S3.Visible = true;
      this.cmbP1S4.Visible = true;
      this.cmbP1S5.Visible = true;
      this.cmbP1S6.Visible = true;
      this.cmbP1S7.Visible = true;
      this.cmbP1S8.Visible = true;
      this.btnP1S1.Visible = true;
      this.btnP1S2.Visible = true;
      this.btnP1S3.Visible = true;
      this.btnP1S4.Visible = true;
      this.btnP1S5.Visible = true;
      this.btnP1S6.Visible = true;
      this.btnP1S7.Visible = true;
      this.btnP1S8.Visible = true;
    }
    else
    {
      if (this.chkPort1.Checked)
        return;
      frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].port_status[0] = false;
      this.cmbP1S1.Visible = false;
      this.cmbP1S2.Visible = false;
      this.cmbP1S3.Visible = false;
      this.cmbP1S4.Visible = false;
      this.cmbP1S5.Visible = false;
      this.cmbP1S6.Visible = false;
      this.cmbP1S7.Visible = false;
      this.cmbP1S8.Visible = false;
      this.btnP1S1.Visible = false;
      this.btnP1S2.Visible = false;
      this.btnP1S3.Visible = false;
      this.btnP1S4.Visible = false;
      this.btnP1S5.Visible = false;
      this.btnP1S6.Visible = false;
      this.btnP1S7.Visible = false;
      this.btnP1S8.Visible = false;
    }
  }

  private void chkPort2_CheckedChanged(object sender, EventArgs e)
  {
    if (this.chkPort2.Checked)
    {
      frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].port_status[1] = true;
      this.cmbP2S1.Visible = true;
      this.cmbP2S2.Visible = true;
      this.cmbP2S3.Visible = true;
      this.cmbP2S4.Visible = true;
      this.cmbP2S5.Visible = true;
      this.cmbP2S6.Visible = true;
      this.cmbP2S7.Visible = true;
      this.cmbP2S8.Visible = true;
      this.btnP2S1.Visible = true;
      this.btnP2S2.Visible = true;
      this.btnP2S3.Visible = true;
      this.btnP2S4.Visible = true;
      this.btnP2S5.Visible = true;
      this.btnP2S6.Visible = true;
      this.btnP2S7.Visible = true;
      this.btnP2S8.Visible = true;
    }
    else
    {
      if (this.chkPort2.Checked)
        return;
      frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].port_status[1] = false;
      this.cmbP2S1.Visible = false;
      this.cmbP2S2.Visible = false;
      this.cmbP2S3.Visible = false;
      this.cmbP2S4.Visible = false;
      this.cmbP2S5.Visible = false;
      this.cmbP2S6.Visible = false;
      this.cmbP2S7.Visible = false;
      this.cmbP2S8.Visible = false;
      this.btnP2S1.Visible = false;
      this.btnP2S2.Visible = false;
      this.btnP2S3.Visible = false;
      this.btnP2S4.Visible = false;
      this.btnP2S5.Visible = false;
      this.btnP2S6.Visible = false;
      this.btnP2S7.Visible = false;
      this.btnP2S8.Visible = false;
    }
  }

  private void chkPort3_CheckedChanged(object sender, EventArgs e)
  {
    if (this.chkPort3.Checked)
    {
      frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].port_status[2] = true;
      this.cmbP3S1.Visible = true;
      this.cmbP3S2.Visible = true;
      this.cmbP3S3.Visible = true;
      this.cmbP3S4.Visible = true;
      this.cmbP3S5.Visible = true;
      this.cmbP3S6.Visible = true;
      this.cmbP3S7.Visible = true;
      this.cmbP3S8.Visible = true;
      this.btnP3S1.Visible = true;
      this.btnP3S2.Visible = true;
      this.btnP3S3.Visible = true;
      this.btnP3S4.Visible = true;
      this.btnP3S5.Visible = true;
      this.btnP3S6.Visible = true;
      this.btnP3S7.Visible = true;
      this.btnP3S8.Visible = true;
    }
    else
    {
      if (this.chkPort3.Checked)
        return;
      frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].port_status[2] = false;
      this.cmbP3S1.Visible = false;
      this.cmbP3S2.Visible = false;
      this.cmbP3S3.Visible = false;
      this.cmbP3S4.Visible = false;
      this.cmbP3S5.Visible = false;
      this.cmbP3S6.Visible = false;
      this.cmbP3S7.Visible = false;
      this.cmbP3S8.Visible = false;
      this.btnP3S1.Visible = false;
      this.btnP3S2.Visible = false;
      this.btnP3S3.Visible = false;
      this.btnP3S4.Visible = false;
      this.btnP3S5.Visible = false;
      this.btnP3S6.Visible = false;
      this.btnP3S7.Visible = false;
      this.btnP3S8.Visible = false;
    }
  }

  private void chkPort4_CheckedChanged(object sender, EventArgs e)
  {
    if (this.chkPort4.Checked)
    {
      frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].port_status[3] = true;
      this.cmbP4S1.Visible = true;
      this.cmbP4S2.Visible = true;
      this.cmbP4S3.Visible = true;
      this.cmbP4S4.Visible = true;
      this.cmbP4S5.Visible = true;
      this.cmbP4S6.Visible = true;
      this.cmbP4S7.Visible = true;
      this.cmbP4S8.Visible = true;
      this.btnP4S1.Visible = true;
      this.btnP4S2.Visible = true;
      this.btnP4S3.Visible = true;
      this.btnP4S4.Visible = true;
      this.btnP4S5.Visible = true;
      this.btnP4S6.Visible = true;
      this.btnP4S7.Visible = true;
      this.btnP4S8.Visible = true;
    }
    else
    {
      if (this.chkPort4.Checked)
        return;
      frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].port_status[3] = false;
      this.cmbP4S1.Visible = false;
      this.cmbP4S2.Visible = false;
      this.cmbP4S3.Visible = false;
      this.cmbP4S4.Visible = false;
      this.cmbP4S5.Visible = false;
      this.cmbP4S6.Visible = false;
      this.cmbP4S7.Visible = false;
      this.cmbP4S8.Visible = false;
      this.btnP4S1.Visible = false;
      this.btnP4S2.Visible = false;
      this.btnP4S3.Visible = false;
      this.btnP4S4.Visible = false;
      this.btnP4S5.Visible = false;
      this.btnP4S6.Visible = false;
      this.btnP4S7.Visible = false;
      this.btnP4S8.Visible = false;
    }
  }

  private void chkPort5_CheckedChanged(object sender, EventArgs e)
  {
    if (this.chkPort5.Checked)
    {
      frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].port_status[4] = true;
      this.cmbP5S1.Visible = true;
      this.cmbP5S2.Visible = true;
      this.cmbP5S3.Visible = true;
      this.cmbP5S4.Visible = true;
      this.cmbP5S5.Visible = true;
      this.cmbP5S6.Visible = true;
      this.cmbP5S7.Visible = true;
      this.cmbP5S8.Visible = true;
      this.btnP5S1.Visible = true;
      this.btnP5S2.Visible = true;
      this.btnP5S3.Visible = true;
      this.btnP5S4.Visible = true;
      this.btnP5S5.Visible = true;
      this.btnP5S6.Visible = true;
      this.btnP5S7.Visible = true;
      this.btnP5S8.Visible = true;
    }
    else
    {
      if (this.chkPort5.Checked)
        return;
      frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].port_status[4] = false;
      this.cmbP5S1.Visible = false;
      this.cmbP5S2.Visible = false;
      this.cmbP5S3.Visible = false;
      this.cmbP5S4.Visible = false;
      this.cmbP5S5.Visible = false;
      this.cmbP5S6.Visible = false;
      this.cmbP5S7.Visible = false;
      this.cmbP5S8.Visible = false;
      this.btnP5S1.Visible = false;
      this.btnP5S2.Visible = false;
      this.btnP5S3.Visible = false;
      this.btnP5S4.Visible = false;
      this.btnP5S5.Visible = false;
      this.btnP5S6.Visible = false;
      this.btnP5S7.Visible = false;
      this.btnP5S8.Visible = false;
    }
  }

  private void chkPort6_CheckedChanged(object sender, EventArgs e)
  {
    if (this.chkPort6.Checked)
    {
      frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].port_status[5] = true;
      this.cmbP6S1.Visible = true;
      this.cmbP6S2.Visible = true;
      this.cmbP6S3.Visible = true;
      this.cmbP6S4.Visible = true;
      this.cmbP6S5.Visible = true;
      this.cmbP6S6.Visible = true;
      this.cmbP6S7.Visible = true;
      this.cmbP6S8.Visible = true;
      this.btnP6S1.Visible = true;
      this.btnP6S2.Visible = true;
      this.btnP6S3.Visible = true;
      this.btnP6S4.Visible = true;
      this.btnP6S5.Visible = true;
      this.btnP6S6.Visible = true;
      this.btnP6S7.Visible = true;
      this.btnP6S8.Visible = true;
    }
    else
    {
      if (this.chkPort6.Checked)
        return;
      frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].port_status[5] = false;
      this.cmbP6S1.Visible = false;
      this.cmbP6S2.Visible = false;
      this.cmbP6S3.Visible = false;
      this.cmbP6S4.Visible = false;
      this.cmbP6S5.Visible = false;
      this.cmbP6S6.Visible = false;
      this.cmbP6S7.Visible = false;
      this.cmbP6S8.Visible = false;
      this.btnP6S1.Visible = false;
      this.btnP6S2.Visible = false;
      this.btnP6S3.Visible = false;
      this.btnP6S4.Visible = false;
      this.btnP6S5.Visible = false;
      this.btnP6S6.Visible = false;
      this.btnP6S7.Visible = false;
      this.btnP6S8.Visible = false;
    }
  }

  private void chkPort7_CheckedChanged(object sender, EventArgs e)
  {
    if (this.chkPort7.Checked)
    {
      frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].port_status[6] = true;
      this.cmbP7S1.Visible = true;
      this.cmbP7S2.Visible = true;
      this.cmbP7S3.Visible = true;
      this.cmbP7S4.Visible = true;
      this.cmbP7S5.Visible = true;
      this.cmbP7S6.Visible = true;
      this.cmbP7S7.Visible = true;
      this.cmbP7S8.Visible = true;
      this.btnP7S1.Visible = true;
      this.btnP7S2.Visible = true;
      this.btnP7S3.Visible = true;
      this.btnP7S4.Visible = true;
      this.btnP7S5.Visible = true;
      this.btnP7S6.Visible = true;
      this.btnP7S7.Visible = true;
      this.btnP7S8.Visible = true;
    }
    else
    {
      if (this.chkPort7.Checked)
        return;
      frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].port_status[6] = false;
      this.cmbP7S1.Visible = false;
      this.cmbP7S2.Visible = false;
      this.cmbP7S3.Visible = false;
      this.cmbP7S4.Visible = false;
      this.cmbP7S5.Visible = false;
      this.cmbP7S6.Visible = false;
      this.cmbP7S7.Visible = false;
      this.cmbP7S8.Visible = false;
      this.btnP7S1.Visible = false;
      this.btnP7S2.Visible = false;
      this.btnP7S3.Visible = false;
      this.btnP7S4.Visible = false;
      this.btnP7S5.Visible = false;
      this.btnP7S6.Visible = false;
      this.btnP7S7.Visible = false;
      this.btnP7S8.Visible = false;
    }
  }

  private void chkPort8_CheckedChanged(object sender, EventArgs e)
  {
    if (this.chkPort8.Checked)
    {
      frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].port_status[7] = true;
      this.cmbP8S1.Visible = true;
      this.cmbP8S2.Visible = true;
      this.cmbP8S3.Visible = true;
      this.cmbP8S4.Visible = true;
      this.cmbP8S5.Visible = true;
      this.cmbP8S6.Visible = true;
      this.cmbP8S7.Visible = true;
      this.cmbP8S8.Visible = true;
      this.btnP8S1.Visible = true;
      this.btnP8S2.Visible = true;
      this.btnP8S3.Visible = true;
      this.btnP8S4.Visible = true;
      this.btnP8S5.Visible = true;
      this.btnP8S6.Visible = true;
      this.btnP8S7.Visible = true;
      this.btnP8S8.Visible = true;
    }
    else
    {
      if (this.chkPort8.Checked)
        return;
      frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].port_status[7] = false;
      this.cmbP8S1.Visible = false;
      this.cmbP8S2.Visible = false;
      this.cmbP8S3.Visible = false;
      this.cmbP8S4.Visible = false;
      this.cmbP8S5.Visible = false;
      this.cmbP8S6.Visible = false;
      this.cmbP8S7.Visible = false;
      this.cmbP8S8.Visible = false;
      this.btnP8S1.Visible = false;
      this.btnP8S2.Visible = false;
      this.btnP8S3.Visible = false;
      this.btnP8S4.Visible = false;
      this.btnP8S5.Visible = false;
      this.btnP8S6.Visible = false;
      this.btnP8S7.Visible = false;
      this.btnP8S8.Visible = false;
    }
  }

  private void chkPort9_CheckedChanged(object sender, EventArgs e)
  {
    if (this.chkPort9.Checked)
    {
      frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].port_status[8] = true;
      this.cmbP9S1.Visible = true;
      this.cmbP9S2.Visible = true;
      this.cmbP9S3.Visible = true;
      this.cmbP9S4.Visible = true;
      this.cmbP9S5.Visible = true;
      this.cmbP9S6.Visible = true;
      this.cmbP9S7.Visible = true;
      this.cmbP9S8.Visible = true;
      this.btnP9S1.Visible = true;
      this.btnP9S2.Visible = true;
      this.btnP9S3.Visible = true;
      this.btnP9S4.Visible = true;
      this.btnP9S5.Visible = true;
      this.btnP9S6.Visible = true;
      this.btnP9S7.Visible = true;
      this.btnP9S8.Visible = true;
    }
    else
    {
      if (this.chkPort9.Checked)
        return;
      frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].port_status[8] = false;
      this.cmbP9S1.Visible = false;
      this.cmbP9S2.Visible = false;
      this.cmbP9S3.Visible = false;
      this.cmbP9S4.Visible = false;
      this.cmbP9S5.Visible = false;
      this.cmbP9S6.Visible = false;
      this.cmbP9S7.Visible = false;
      this.cmbP9S8.Visible = false;
      this.btnP9S1.Visible = false;
      this.btnP9S2.Visible = false;
      this.btnP9S3.Visible = false;
      this.btnP9S4.Visible = false;
      this.btnP9S5.Visible = false;
      this.btnP9S6.Visible = false;
      this.btnP9S7.Visible = false;
      this.btnP9S8.Visible = false;
    }
  }

  private void chkPort10_CheckedChanged(object sender, EventArgs e)
  {
    if (this.chkPort10.Checked)
    {
      frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].port_status[9] = true;
      this.cmbP10S1.Visible = true;
      this.cmbP10S2.Visible = true;
      this.cmbP10S3.Visible = true;
      this.cmbP10S4.Visible = true;
      this.cmbP10S5.Visible = true;
      this.cmbP10S6.Visible = true;
      this.cmbP10S7.Visible = true;
      this.cmbP10S8.Visible = true;
      this.btnP10S1.Visible = true;
      this.btnP10S2.Visible = true;
      this.btnP10S3.Visible = true;
      this.btnP10S4.Visible = true;
      this.btnP10S5.Visible = true;
      this.btnP10S6.Visible = true;
      this.btnP10S7.Visible = true;
      this.btnP10S8.Visible = true;
    }
    else
    {
      if (this.chkPort10.Checked)
        return;
      frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].port_status[9] = false;
      this.cmbP10S1.Visible = false;
      this.cmbP10S2.Visible = false;
      this.cmbP10S3.Visible = false;
      this.cmbP10S4.Visible = false;
      this.cmbP10S5.Visible = false;
      this.cmbP10S6.Visible = false;
      this.cmbP10S7.Visible = false;
      this.cmbP10S8.Visible = false;
      this.btnP10S1.Visible = false;
      this.btnP10S2.Visible = false;
      this.btnP10S3.Visible = false;
      this.btnP10S4.Visible = false;
      this.btnP10S5.Visible = false;
      this.btnP10S6.Visible = false;
      this.btnP10S7.Visible = false;
      this.btnP10S8.Visible = false;
    }
  }

  private void chkPort11_CheckedChanged(object sender, EventArgs e)
  {
    if (this.chkPort11.Checked)
    {
      frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].port_status[10] = true;
      this.cmbP11S1.Visible = true;
      this.cmbP11S2.Visible = true;
      this.cmbP11S3.Visible = true;
      this.cmbP11S4.Visible = true;
      this.cmbP11S5.Visible = true;
      this.cmbP11S6.Visible = true;
      this.cmbP11S7.Visible = true;
      this.cmbP11S8.Visible = true;
      this.btnP11S1.Visible = true;
      this.btnP11S2.Visible = true;
      this.btnP11S3.Visible = true;
      this.btnP11S4.Visible = true;
      this.btnP11S5.Visible = true;
      this.btnP11S6.Visible = true;
      this.btnP11S7.Visible = true;
      this.btnP11S8.Visible = true;
    }
    else
    {
      if (this.chkPort11.Checked)
        return;
      frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].port_status[10] = false;
      this.cmbP11S1.Visible = false;
      this.cmbP11S2.Visible = false;
      this.cmbP11S3.Visible = false;
      this.cmbP11S4.Visible = false;
      this.cmbP11S5.Visible = false;
      this.cmbP11S6.Visible = false;
      this.cmbP11S7.Visible = false;
      this.cmbP11S8.Visible = false;
      this.btnP11S1.Visible = false;
      this.btnP11S2.Visible = false;
      this.btnP11S3.Visible = false;
      this.btnP11S4.Visible = false;
      this.btnP11S5.Visible = false;
      this.btnP11S6.Visible = false;
      this.btnP11S7.Visible = false;
      this.btnP11S8.Visible = false;
    }
  }

  private void chkPort12_CheckedChanged(object sender, EventArgs e)
  {
    if (this.chkPort12.Checked)
    {
      frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].port_status[11] = true;
      this.cmbP12S1.Visible = true;
      this.cmbP12S2.Visible = true;
      this.cmbP12S3.Visible = true;
      this.cmbP12S4.Visible = true;
      this.cmbP12S5.Visible = true;
      this.cmbP12S6.Visible = true;
      this.cmbP12S7.Visible = true;
      this.cmbP12S8.Visible = true;
      this.btnP12S1.Visible = true;
      this.btnP12S2.Visible = true;
      this.btnP12S3.Visible = true;
      this.btnP12S4.Visible = true;
      this.btnP12S5.Visible = true;
      this.btnP12S6.Visible = true;
      this.btnP12S7.Visible = true;
      this.btnP12S8.Visible = true;
    }
    else
    {
      if (this.chkPort12.Checked)
        return;
      frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].port_status[11] = false;
      this.cmbP12S1.Visible = false;
      this.cmbP12S2.Visible = false;
      this.cmbP12S3.Visible = false;
      this.cmbP12S4.Visible = false;
      this.cmbP12S5.Visible = false;
      this.cmbP12S6.Visible = false;
      this.cmbP12S7.Visible = false;
      this.cmbP12S8.Visible = false;
      this.btnP12S1.Visible = false;
      this.btnP12S2.Visible = false;
      this.btnP12S3.Visible = false;
      this.btnP12S4.Visible = false;
      this.btnP12S5.Visible = false;
      this.btnP12S6.Visible = false;
      this.btnP12S7.Visible = false;
      this.btnP12S8.Visible = false;
    }
  }

  private void chkPort13_CheckedChanged(object sender, EventArgs e)
  {
    if (this.chkPort13.Checked)
    {
      frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].port_status[12] = true;
      this.cmbP13S1.Visible = true;
      this.cmbP13S2.Visible = true;
      this.cmbP13S3.Visible = true;
      this.cmbP13S4.Visible = true;
      this.cmbP13S5.Visible = true;
      this.cmbP13S6.Visible = true;
      this.cmbP13S7.Visible = true;
      this.cmbP13S8.Visible = true;
      this.btnP13S1.Visible = true;
      this.btnP13S2.Visible = true;
      this.btnP13S3.Visible = true;
      this.btnP13S4.Visible = true;
      this.btnP13S5.Visible = true;
      this.btnP13S6.Visible = true;
      this.btnP13S7.Visible = true;
      this.btnP13S8.Visible = true;
    }
    else
    {
      if (this.chkPort13.Checked)
        return;
      frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].port_status[12] = false;
      this.cmbP13S1.Visible = false;
      this.cmbP13S2.Visible = false;
      this.cmbP13S3.Visible = false;
      this.cmbP13S4.Visible = false;
      this.cmbP13S5.Visible = false;
      this.cmbP13S6.Visible = false;
      this.cmbP13S7.Visible = false;
      this.cmbP13S8.Visible = false;
      this.btnP13S1.Visible = false;
      this.btnP13S2.Visible = false;
      this.btnP13S3.Visible = false;
      this.btnP13S4.Visible = false;
      this.btnP13S5.Visible = false;
      this.btnP13S6.Visible = false;
      this.btnP13S7.Visible = false;
      this.btnP13S8.Visible = false;
    }
  }

  private void chkPort14_CheckedChanged(object sender, EventArgs e)
  {
    if (this.chkPort14.Checked)
    {
      frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].port_status[13] = true;
      this.cmbP14S1.Visible = true;
      this.cmbP14S2.Visible = true;
      this.cmbP14S3.Visible = true;
      this.cmbP14S4.Visible = true;
      this.cmbP14S5.Visible = true;
      this.cmbP14S6.Visible = true;
      this.cmbP14S7.Visible = true;
      this.cmbP14S8.Visible = true;
      this.btnP14S1.Visible = true;
      this.btnP14S2.Visible = true;
      this.btnP14S3.Visible = true;
      this.btnP14S4.Visible = true;
      this.btnP14S5.Visible = true;
      this.btnP14S6.Visible = true;
      this.btnP14S7.Visible = true;
      this.btnP14S8.Visible = true;
    }
    else
    {
      if (this.chkPort14.Checked)
        return;
      frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].port_status[13] = false;
      this.cmbP14S1.Visible = false;
      this.cmbP14S2.Visible = false;
      this.cmbP14S3.Visible = false;
      this.cmbP14S4.Visible = false;
      this.cmbP14S5.Visible = false;
      this.cmbP14S6.Visible = false;
      this.cmbP14S7.Visible = false;
      this.cmbP14S8.Visible = false;
      this.btnP14S1.Visible = false;
      this.btnP14S2.Visible = false;
      this.btnP14S3.Visible = false;
      this.btnP14S4.Visible = false;
      this.btnP14S5.Visible = false;
      this.btnP14S6.Visible = false;
      this.btnP14S7.Visible = false;
      this.btnP14S8.Visible = false;
    }
  }

  private void chkPort15_CheckedChanged(object sender, EventArgs e)
  {
    if (this.chkPort15.Checked)
    {
      frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].port_status[14] = true;
      this.cmbP15S1.Visible = true;
      this.cmbP15S2.Visible = true;
      this.cmbP15S3.Visible = true;
      this.cmbP15S4.Visible = true;
      this.cmbP15S5.Visible = true;
      this.cmbP15S6.Visible = true;
      this.cmbP15S7.Visible = true;
      this.cmbP15S8.Visible = true;
      this.btnP15S1.Visible = true;
      this.btnP15S2.Visible = true;
      this.btnP15S3.Visible = true;
      this.btnP15S4.Visible = true;
      this.btnP15S5.Visible = true;
      this.btnP15S6.Visible = true;
      this.btnP15S7.Visible = true;
      this.btnP15S8.Visible = true;
    }
    else
    {
      if (this.chkPort15.Checked)
        return;
      frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].port_status[14] = false;
      this.cmbP15S1.Visible = false;
      this.cmbP15S2.Visible = false;
      this.cmbP15S3.Visible = false;
      this.cmbP15S4.Visible = false;
      this.cmbP15S5.Visible = false;
      this.cmbP15S6.Visible = false;
      this.cmbP15S7.Visible = false;
      this.cmbP15S8.Visible = false;
      this.btnP15S1.Visible = false;
      this.btnP15S2.Visible = false;
      this.btnP15S3.Visible = false;
      this.btnP15S4.Visible = false;
      this.btnP15S5.Visible = false;
      this.btnP15S6.Visible = false;
      this.btnP15S7.Visible = false;
      this.btnP15S8.Visible = false;
    }
  }

  private void chkPort16_CheckedChanged(object sender, EventArgs e)
  {
    if (this.chkPort16.Checked)
    {
      frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].port_status[15] = true;
      this.cmbP16S1.Visible = true;
      this.cmbP16S2.Visible = true;
      this.cmbP16S3.Visible = true;
      this.cmbP16S4.Visible = true;
      this.cmbP16S5.Visible = true;
      this.cmbP16S6.Visible = true;
      this.cmbP16S7.Visible = true;
      this.cmbP16S8.Visible = true;
      this.btnP16S1.Visible = true;
      this.btnP16S2.Visible = true;
      this.btnP16S3.Visible = true;
      this.btnP16S4.Visible = true;
      this.btnP16S5.Visible = true;
      this.btnP16S6.Visible = true;
      this.btnP16S7.Visible = true;
      this.btnP16S8.Visible = true;
    }
    else
    {
      if (this.chkPort16.Checked)
        return;
      frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].port_status[15] = false;
      this.cmbP16S1.Visible = false;
      this.cmbP16S2.Visible = false;
      this.cmbP16S3.Visible = false;
      this.cmbP16S4.Visible = false;
      this.cmbP16S5.Visible = false;
      this.cmbP16S6.Visible = false;
      this.cmbP16S7.Visible = false;
      this.cmbP16S8.Visible = false;
      this.btnP16S1.Visible = false;
      this.btnP16S2.Visible = false;
      this.btnP16S3.Visible = false;
      this.btnP16S4.Visible = false;
      this.btnP16S5.Visible = false;
      this.btnP16S6.Visible = false;
      this.btnP16S7.Visible = false;
      this.btnP16S8.Visible = false;
    }
  }

  private void agdb_display_board_data(int k, int l)
  {
    int mdchPortNum = (int) frmNetworkMDCH.mdch_port_num;
    int mdchSystemNum = (int) frmNetworkMDCH.mdch_system_num;
    checked { --k; }
    checked { --l; }
    try
    {
      MyProject.Forms.frmNetworkAGDB.chkAgdbAllPfno.Visible = false;
      MyProject.Forms.frmNetworkAGDB.lblAllPfno.Visible = false;
      MyProject.Forms.frmNetworkAGDB.txtAgdbName.Text = frmMainFormIPIS.mdch_db.mdch_port[mdchPortNum].pdch[mdchSystemNum].pdch_port[k].dis_board[l].dis_board_name;
      MyProject.Forms.frmNetworkAGDB.txtAgdbAddress.Text = Conversions.ToString(frmMainFormIPIS.mdch_db.mdch_port[mdchPortNum].pdch[mdchSystemNum].pdch_port[k].dis_board[l].dis_board_addr);
      if (!frmMainFormIPIS.mdch_db.mdch_port[mdchPortNum].pdch[mdchSystemNum].pdch_port[k].dis_board[l].all_platfroms)
      {
        MyProject.Forms.frmNetworkAGDB.cmbAgdbPfno.Text = frmMainFormIPIS.mdch_db.mdch_port[mdchPortNum].pdch[mdchSystemNum].pdch_port[k].dis_board[l].platform_no;
        MyProject.Forms.frmNetworkAGDB.chkAgdbAllPfno.Checked = false;
      }
      else
      {
        MyProject.Forms.frmNetworkAGDB.cmbAgdbPfno.Text = string.Empty;
        MyProject.Forms.frmNetworkAGDB.chkAgdbAllPfno.Checked = true;
      }
      if (frmMainFormIPIS.mdch_db.mdch_port[mdchPortNum].pdch[mdchSystemNum].pdch_port[k].dis_board[l].shared_platform)
      {
        MyProject.Forms.frmNetworkAGDB.chkAGDBSharedPfNo.Checked = true;
        MyProject.Forms.frmNetworkAGDB.cmbAgdbSharedPfno.Text = frmMainFormIPIS.mdch_db.mdch_port[mdchPortNum].pdch[mdchSystemNum].pdch_port[k].dis_board[l].shared_platform_no;
      }
      else
      {
        MyProject.Forms.frmNetworkAGDB.chkAGDBSharedPfNo.Checked = false;
        MyProject.Forms.frmNetworkAGDB.cmbAgdbSharedPfno.Text = string.Empty;
      }
      MyProject.Forms.frmNetworkAGDB.txtAgdbMsgSwDly.Text = Conversions.ToString(frmMainFormIPIS.mdch_db.mdch_port[mdchPortNum].pdch[mdchSystemNum].pdch_port[k].dis_board[l].switching_time);
      if (Conversions.ToDouble(MyProject.Forms.frmNetworkAGDB.txtAgdbAddress.Text) == 0.0)
        MyProject.Forms.frmNetworkAGDB.txtAgdbAddress.Text = string.Empty;
      if (Conversions.ToDouble(MyProject.Forms.frmNetworkAGDB.txtAgdbMsgSwDly.Text) == 0.0)
        MyProject.Forms.frmNetworkAGDB.txtAgdbMsgSwDly.Text = string.Empty;
      MyProject.Forms.frmNetworkAGDB.numSign.Value = new Decimal((int) frmMainFormIPIS.mdch_db.mdch_port[mdchPortNum].pdch[mdchSystemNum].pdch_port[k].dis_board[l].agdb_sign);
      MyProject.Forms.frmNetworkAGDB.Show();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void pdb_display_board_data(int k, int l)
  {
    int mdchPortNum = (int) frmNetworkMDCH.mdch_port_num;
    int mdchSystemNum = (int) frmNetworkMDCH.mdch_system_num;
    try
    {
      checked { --k; }
      checked { --l; }
      MyProject.Forms.frmNetworkPDB.txtPdbName.Text = frmMainFormIPIS.mdch_db.mdch_port[mdchPortNum].pdch[mdchSystemNum].pdch_port[k].dis_board[l].dis_board_name;
      MyProject.Forms.frmNetworkPDB.txtPdbAddress.Text = Conversions.ToString(frmMainFormIPIS.mdch_db.mdch_port[mdchPortNum].pdch[mdchSystemNum].pdch_port[k].dis_board[l].dis_board_addr);
      MyProject.Forms.frmNetworkPDB.cmbPdbPdno.Text = frmMainFormIPIS.mdch_db.mdch_port[mdchPortNum].pdch[mdchSystemNum].pdch_port[k].dis_board[l].platform_no;
      MyProject.Forms.frmNetworkPDB.txtMultiCastAddress.Text = Conversions.ToString(frmMainFormIPIS.mdch_db.mdch_port[mdchPortNum].pdch[mdchSystemNum].pdch_port[k].dis_board[l].multicast_addr);
      if (frmMainFormIPIS.mdch_db.mdch_port[mdchPortNum].pdch[mdchSystemNum].pdch_port[k].dis_board[l].shared_platform)
      {
        MyProject.Forms.frmNetworkPDB.chkPDBSharedPfNo.Checked = true;
        MyProject.Forms.frmNetworkPDB.cmbPdbSharedPlatformNo.Text = frmMainFormIPIS.mdch_db.mdch_port[mdchPortNum].pdch[mdchSystemNum].pdch_port[k].dis_board[l].shared_platform_no;
      }
      else
      {
        MyProject.Forms.frmNetworkPDB.chkPDBSharedPfNo.Checked = false;
        MyProject.Forms.frmNetworkPDB.cmbPdbSharedPlatformNo.Text = string.Empty;
      }
      MyProject.Forms.frmNetworkPDB.txtPdbMsgSwDly.Text = Conversions.ToString(frmMainFormIPIS.mdch_db.mdch_port[mdchPortNum].pdch[mdchSystemNum].pdch_port[k].dis_board[l].switching_time);
      if (Conversions.ToDouble(MyProject.Forms.frmNetworkPDB.txtPdbAddress.Text) == 0.0)
        MyProject.Forms.frmNetworkPDB.txtPdbAddress.Text = string.Empty;
      if (Conversions.ToDouble(MyProject.Forms.frmNetworkPDB.txtMultiCastAddress.Text) == 0.0)
        MyProject.Forms.frmNetworkPDB.txtMultiCastAddress.Text = string.Empty;
      if (Conversions.ToDouble(MyProject.Forms.frmNetworkPDB.txtPdbMsgSwDly.Text) == 0.0)
        MyProject.Forms.frmNetworkPDB.txtPdbMsgSwDly.Text = string.Empty;
      MyProject.Forms.frmNetworkPDB.Show();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void cgdb_display_board_data(string k, int l)
  {
    int mdchPortNum = (int) frmNetworkMDCH.mdch_port_num;
    int mdchSystemNum = (int) frmNetworkMDCH.mdch_system_num;
    try
    {
      k = Conversions.ToString(Conversions.ToDouble(k) - 1.0);
      checked { --l; }
      MyProject.Forms.frmNetworkCGDB.txtCgdbName.Text = frmMainFormIPIS.mdch_db.mdch_port[mdchPortNum].pdch[mdchSystemNum].pdch_port[Conversions.ToInteger(k)].dis_board[l].dis_board_name;
      MyProject.Forms.frmNetworkCGDB.txtCgdbAddress.Text = Conversions.ToString(frmMainFormIPIS.mdch_db.mdch_port[mdchPortNum].pdch[mdchSystemNum].pdch_port[Conversions.ToInteger(k)].dis_board[l].dis_board_addr);
      MyProject.Forms.frmNetworkCGDB.cmbCgdbPfno.Text = frmMainFormIPIS.mdch_db.mdch_port[mdchPortNum].pdch[mdchSystemNum].pdch_port[Conversions.ToInteger(k)].dis_board[l].platform_no;
      MyProject.Forms.frmNetworkCGDB.txtCgdbDir.Text = frmMainFormIPIS.mdch_db.mdch_port[mdchPortNum].pdch[mdchSystemNum].pdch_port[Conversions.ToInteger(k)].dis_board[l].cgdb_direction;
      MyProject.Forms.frmNetworkCGDB.txtCgdbMsgSwDly.Text = Conversions.ToString(frmMainFormIPIS.mdch_db.mdch_port[mdchPortNum].pdch[mdchSystemNum].pdch_port[Conversions.ToInteger(k)].dis_board[l].switching_time);
      MyProject.Forms.frmNetworkCGDB.txtMultiCastAddress.Text = Conversions.ToString(frmMainFormIPIS.mdch_db.mdch_port[mdchPortNum].pdch[mdchSystemNum].pdch_port[Conversions.ToInteger(k)].dis_board[l].multicast_addr);
      if (Conversions.ToDouble(MyProject.Forms.frmNetworkCGDB.txtCgdbAddress.Text) == 0.0)
        MyProject.Forms.frmNetworkCGDB.txtCgdbAddress.Text = string.Empty;
      if (Conversions.ToDouble(MyProject.Forms.frmNetworkCGDB.txtCgdbMsgSwDly.Text) == 0.0)
        MyProject.Forms.frmNetworkCGDB.txtCgdbMsgSwDly.Text = string.Empty;
      if (Conversions.ToDouble(MyProject.Forms.frmNetworkCGDB.txtMultiCastAddress.Text) == 0.0)
        MyProject.Forms.frmNetworkCGDB.txtMultiCastAddress.Text = string.Empty;
      MyProject.Forms.frmNetworkCGDB.Show();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void display_board_type_data(ComboBox cmbps, int pdch_pno, byte pdch_sno)
  {
    if (Operators.CompareString(cmbps.Text, "AGDB", false) == 0)
      this.agdb_display_board_data(pdch_pno, (int) pdch_sno);
    else if (Operators.CompareString(cmbps.Text, "PDB", false) == 0)
    {
      this.pdb_display_board_data(pdch_pno, (int) pdch_sno);
    }
    else
    {
      if (Operators.CompareString(cmbps.Text, "CGDB", false) != 0)
        return;
      this.cgdb_display_board_data(Conversions.ToString(pdch_pno), (int) pdch_sno);
    }
  }

  private void btnP1S1_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 1;
    frmNetworkPDCH.pdch_system_num = (byte) 1;
    this.display_board_type_data(this.cmbP1S1, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP1S2_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 1;
    frmNetworkPDCH.pdch_system_num = (byte) 2;
    this.display_board_type_data(this.cmbP1S2, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP1S3_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 1;
    frmNetworkPDCH.pdch_system_num = (byte) 3;
    this.display_board_type_data(this.cmbP1S3, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP1S4_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 1;
    frmNetworkPDCH.pdch_system_num = (byte) 4;
    this.display_board_type_data(this.cmbP1S4, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP1S5_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 1;
    frmNetworkPDCH.pdch_system_num = (byte) 5;
    this.display_board_type_data(this.cmbP1S5, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP1S6_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 1;
    frmNetworkPDCH.pdch_system_num = (byte) 6;
    this.display_board_type_data(this.cmbP1S6, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP1S7_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 1;
    frmNetworkPDCH.pdch_system_num = (byte) 7;
    this.display_board_type_data(this.cmbP1S7, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP1S8_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 1;
    frmNetworkPDCH.pdch_system_num = (byte) 8;
    this.display_board_type_data(this.cmbP1S8, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP2S1_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 2;
    frmNetworkPDCH.pdch_system_num = (byte) 1;
    this.display_board_type_data(this.cmbP2S1, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP2S2_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 2;
    frmNetworkPDCH.pdch_system_num = (byte) 2;
    this.display_board_type_data(this.cmbP2S2, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP2S3_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 2;
    frmNetworkPDCH.pdch_system_num = (byte) 3;
    this.display_board_type_data(this.cmbP2S3, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP2S4_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 2;
    frmNetworkPDCH.pdch_system_num = (byte) 4;
    this.display_board_type_data(this.cmbP2S4, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP2S5_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 2;
    frmNetworkPDCH.pdch_system_num = (byte) 5;
    this.display_board_type_data(this.cmbP2S5, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP2S6_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 2;
    frmNetworkPDCH.pdch_system_num = (byte) 6;
    this.display_board_type_data(this.cmbP2S6, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP2S7_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 2;
    frmNetworkPDCH.pdch_system_num = (byte) 7;
    this.display_board_type_data(this.cmbP2S7, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP2S8_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 2;
    frmNetworkPDCH.pdch_system_num = (byte) 8;
    this.display_board_type_data(this.cmbP2S8, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP3S1_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 3;
    frmNetworkPDCH.pdch_system_num = (byte) 1;
    this.display_board_type_data(this.cmbP3S1, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP3S2_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 3;
    frmNetworkPDCH.pdch_system_num = (byte) 2;
    this.display_board_type_data(this.cmbP3S2, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP3S3_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 3;
    frmNetworkPDCH.pdch_system_num = (byte) 3;
    this.display_board_type_data(this.cmbP3S3, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP3S4_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 3;
    frmNetworkPDCH.pdch_system_num = (byte) 4;
    this.display_board_type_data(this.cmbP3S4, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP3S5_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 3;
    frmNetworkPDCH.pdch_system_num = (byte) 5;
    this.display_board_type_data(this.cmbP3S5, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP3S6_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 3;
    frmNetworkPDCH.pdch_system_num = (byte) 6;
    this.display_board_type_data(this.cmbP3S6, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP3S7_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 3;
    frmNetworkPDCH.pdch_system_num = (byte) 7;
    this.display_board_type_data(this.cmbP3S7, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP3S8_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 3;
    frmNetworkPDCH.pdch_system_num = (byte) 8;
    this.display_board_type_data(this.cmbP3S8, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP4S1_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 4;
    frmNetworkPDCH.pdch_system_num = (byte) 1;
    this.display_board_type_data(this.cmbP4S1, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP4S2_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 4;
    frmNetworkPDCH.pdch_system_num = (byte) 2;
    this.display_board_type_data(this.cmbP4S2, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP4S3_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 4;
    frmNetworkPDCH.pdch_system_num = (byte) 3;
    this.display_board_type_data(this.cmbP4S3, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP4S4_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 4;
    frmNetworkPDCH.pdch_system_num = (byte) 4;
    this.display_board_type_data(this.cmbP4S4, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP4S5_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 4;
    frmNetworkPDCH.pdch_system_num = (byte) 5;
    this.display_board_type_data(this.cmbP4S5, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP4S6_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 4;
    frmNetworkPDCH.pdch_system_num = (byte) 6;
    this.display_board_type_data(this.cmbP4S6, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP4S7_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 4;
    frmNetworkPDCH.pdch_system_num = (byte) 7;
    this.display_board_type_data(this.cmbP4S7, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP4S8_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 4;
    frmNetworkPDCH.pdch_system_num = (byte) 8;
    this.display_board_type_data(this.cmbP4S8, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP5S1_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 5;
    frmNetworkPDCH.pdch_system_num = (byte) 1;
    this.display_board_type_data(this.cmbP5S1, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP5S2_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 5;
    frmNetworkPDCH.pdch_system_num = (byte) 2;
    this.display_board_type_data(this.cmbP5S2, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP5S3_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 5;
    frmNetworkPDCH.pdch_system_num = (byte) 3;
    this.display_board_type_data(this.cmbP5S3, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP5S4_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 5;
    frmNetworkPDCH.pdch_system_num = (byte) 4;
    this.display_board_type_data(this.cmbP5S4, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP5S5_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 5;
    frmNetworkPDCH.pdch_system_num = (byte) 5;
    this.display_board_type_data(this.cmbP5S5, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP5S6_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 5;
    frmNetworkPDCH.pdch_system_num = (byte) 6;
    this.display_board_type_data(this.cmbP5S6, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP5S7_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 5;
    frmNetworkPDCH.pdch_system_num = (byte) 7;
    this.display_board_type_data(this.cmbP5S7, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP5S8_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 5;
    frmNetworkPDCH.pdch_system_num = (byte) 8;
    this.display_board_type_data(this.cmbP5S8, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP6S1_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 6;
    frmNetworkPDCH.pdch_system_num = (byte) 1;
    this.display_board_type_data(this.cmbP6S1, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP6S2_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 6;
    frmNetworkPDCH.pdch_system_num = (byte) 2;
    this.display_board_type_data(this.cmbP6S2, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP6S3_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 6;
    frmNetworkPDCH.pdch_system_num = (byte) 3;
    this.display_board_type_data(this.cmbP6S3, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP6S4_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 6;
    frmNetworkPDCH.pdch_system_num = (byte) 4;
    this.display_board_type_data(this.cmbP6S4, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP6S5_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 6;
    frmNetworkPDCH.pdch_system_num = (byte) 5;
    this.display_board_type_data(this.cmbP6S5, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP6S6_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 6;
    frmNetworkPDCH.pdch_system_num = (byte) 6;
    this.display_board_type_data(this.cmbP6S6, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP6S7_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 6;
    frmNetworkPDCH.pdch_system_num = (byte) 7;
    this.display_board_type_data(this.cmbP6S7, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP6S8_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 6;
    frmNetworkPDCH.pdch_system_num = (byte) 8;
    this.display_board_type_data(this.cmbP6S8, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP7S1_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 7;
    frmNetworkPDCH.pdch_system_num = (byte) 1;
    this.display_board_type_data(this.cmbP7S1, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP7S2_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 7;
    frmNetworkPDCH.pdch_system_num = (byte) 2;
    this.display_board_type_data(this.cmbP7S2, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP7S3_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 7;
    frmNetworkPDCH.pdch_system_num = (byte) 3;
    this.display_board_type_data(this.cmbP7S3, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP7S4_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 7;
    frmNetworkPDCH.pdch_system_num = (byte) 4;
    this.display_board_type_data(this.cmbP7S4, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP7S5_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 7;
    frmNetworkPDCH.pdch_system_num = (byte) 5;
    this.display_board_type_data(this.cmbP7S5, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP7S6_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 7;
    frmNetworkPDCH.pdch_system_num = (byte) 6;
    this.display_board_type_data(this.cmbP7S6, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP7S7_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 7;
    frmNetworkPDCH.pdch_system_num = (byte) 7;
    this.display_board_type_data(this.cmbP7S7, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP7S8_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 7;
    frmNetworkPDCH.pdch_system_num = (byte) 8;
    this.display_board_type_data(this.cmbP7S8, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP8S1_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 8;
    frmNetworkPDCH.pdch_system_num = (byte) 1;
    this.display_board_type_data(this.cmbP8S1, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP8S2_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 8;
    frmNetworkPDCH.pdch_system_num = (byte) 2;
    this.display_board_type_data(this.cmbP8S2, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP8S3_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 8;
    frmNetworkPDCH.pdch_system_num = (byte) 3;
    this.display_board_type_data(this.cmbP8S3, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP8S4_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 8;
    frmNetworkPDCH.pdch_system_num = (byte) 4;
    this.display_board_type_data(this.cmbP8S4, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP8S5_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 8;
    frmNetworkPDCH.pdch_system_num = (byte) 5;
    this.display_board_type_data(this.cmbP8S5, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP8S6_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 8;
    frmNetworkPDCH.pdch_system_num = (byte) 6;
    this.display_board_type_data(this.cmbP8S6, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP8S7_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 8;
    frmNetworkPDCH.pdch_system_num = (byte) 7;
    this.display_board_type_data(this.cmbP8S7, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP8S8_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 8;
    frmNetworkPDCH.pdch_system_num = (byte) 8;
    this.display_board_type_data(this.cmbP8S8, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP9S1_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 9;
    frmNetworkPDCH.pdch_system_num = (byte) 1;
    this.display_board_type_data(this.cmbP9S1, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP9S2_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 9;
    frmNetworkPDCH.pdch_system_num = (byte) 2;
    this.display_board_type_data(this.cmbP9S2, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP9S3_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 9;
    frmNetworkPDCH.pdch_system_num = (byte) 3;
    this.display_board_type_data(this.cmbP9S3, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP9S4_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 9;
    frmNetworkPDCH.pdch_system_num = (byte) 4;
    this.display_board_type_data(this.cmbP9S4, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP9S5_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 9;
    frmNetworkPDCH.pdch_system_num = (byte) 5;
    this.display_board_type_data(this.cmbP9S5, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP9S6_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 9;
    frmNetworkPDCH.pdch_system_num = (byte) 6;
    this.display_board_type_data(this.cmbP9S6, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP9S7_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 9;
    frmNetworkPDCH.pdch_system_num = (byte) 7;
    this.display_board_type_data(this.cmbP9S7, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP9S8_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 9;
    frmNetworkPDCH.pdch_system_num = (byte) 8;
    this.display_board_type_data(this.cmbP9S8, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP10S1_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 10;
    frmNetworkPDCH.pdch_system_num = (byte) 1;
    this.display_board_type_data(this.cmbP10S1, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP10S2_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 10;
    frmNetworkPDCH.pdch_system_num = (byte) 2;
    this.display_board_type_data(this.cmbP10S2, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP10S3_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 10;
    frmNetworkPDCH.pdch_system_num = (byte) 3;
    this.display_board_type_data(this.cmbP10S3, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP10S4_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 10;
    frmNetworkPDCH.pdch_system_num = (byte) 4;
    this.display_board_type_data(this.cmbP10S4, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP10S5_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 10;
    frmNetworkPDCH.pdch_system_num = (byte) 5;
    this.display_board_type_data(this.cmbP10S5, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP10S6_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 10;
    frmNetworkPDCH.pdch_system_num = (byte) 6;
    this.display_board_type_data(this.cmbP10S6, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP10S7_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 10;
    frmNetworkPDCH.pdch_system_num = (byte) 7;
    this.display_board_type_data(this.cmbP10S7, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP10S8_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 10;
    frmNetworkPDCH.pdch_system_num = (byte) 8;
    this.display_board_type_data(this.cmbP10S8, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP11S1_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 11;
    frmNetworkPDCH.pdch_system_num = (byte) 1;
    this.display_board_type_data(this.cmbP11S1, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP11S2_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 11;
    frmNetworkPDCH.pdch_system_num = (byte) 2;
    this.display_board_type_data(this.cmbP11S2, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP11S3_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 11;
    frmNetworkPDCH.pdch_system_num = (byte) 3;
    this.display_board_type_data(this.cmbP11S3, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP11S4_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 11;
    frmNetworkPDCH.pdch_system_num = (byte) 4;
    this.display_board_type_data(this.cmbP11S4, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP11S5_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 11;
    frmNetworkPDCH.pdch_system_num = (byte) 5;
    this.display_board_type_data(this.cmbP11S5, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP11S6_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 11;
    frmNetworkPDCH.pdch_system_num = (byte) 6;
    this.display_board_type_data(this.cmbP11S6, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP11S7_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 11;
    frmNetworkPDCH.pdch_system_num = (byte) 7;
    this.display_board_type_data(this.cmbP11S7, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP11S8_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 11;
    frmNetworkPDCH.pdch_system_num = (byte) 8;
    this.display_board_type_data(this.cmbP11S8, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP12S1_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 12;
    frmNetworkPDCH.pdch_system_num = (byte) 1;
    this.display_board_type_data(this.cmbP12S1, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP12S2_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 12;
    frmNetworkPDCH.pdch_system_num = (byte) 2;
    this.display_board_type_data(this.cmbP12S2, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP12S3_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 12;
    frmNetworkPDCH.pdch_system_num = (byte) 3;
    this.display_board_type_data(this.cmbP12S3, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP12S4_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 12;
    frmNetworkPDCH.pdch_system_num = (byte) 4;
    this.display_board_type_data(this.cmbP12S4, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP12S5_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 12;
    frmNetworkPDCH.pdch_system_num = (byte) 5;
    this.display_board_type_data(this.cmbP12S5, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP12S6_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 12;
    frmNetworkPDCH.pdch_system_num = (byte) 6;
    this.display_board_type_data(this.cmbP12S6, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP12S7_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 12;
    frmNetworkPDCH.pdch_system_num = (byte) 7;
    this.display_board_type_data(this.cmbP12S7, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP12S8_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 12;
    frmNetworkPDCH.pdch_system_num = (byte) 8;
    this.display_board_type_data(this.cmbP12S8, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP13S1_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 13;
    frmNetworkPDCH.pdch_system_num = (byte) 1;
    this.display_board_type_data(this.cmbP13S1, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP13S2_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 13;
    frmNetworkPDCH.pdch_system_num = (byte) 2;
    this.display_board_type_data(this.cmbP13S2, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP13S3_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 13;
    frmNetworkPDCH.pdch_system_num = (byte) 3;
    this.display_board_type_data(this.cmbP13S3, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP13S4_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 13;
    frmNetworkPDCH.pdch_system_num = (byte) 4;
    this.display_board_type_data(this.cmbP13S4, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP13S5_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 13;
    frmNetworkPDCH.pdch_system_num = (byte) 5;
    this.display_board_type_data(this.cmbP13S5, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP13S6_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 13;
    frmNetworkPDCH.pdch_system_num = (byte) 6;
    this.display_board_type_data(this.cmbP13S6, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP13S7_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 13;
    frmNetworkPDCH.pdch_system_num = (byte) 7;
    this.display_board_type_data(this.cmbP13S7, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP13S8_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 13;
    frmNetworkPDCH.pdch_system_num = (byte) 8;
    this.display_board_type_data(this.cmbP13S8, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP14S1_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 14;
    frmNetworkPDCH.pdch_system_num = (byte) 1;
    this.display_board_type_data(this.cmbP14S1, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP14S2_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 14;
    frmNetworkPDCH.pdch_system_num = (byte) 2;
    this.display_board_type_data(this.cmbP14S2, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP14S3_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 14;
    frmNetworkPDCH.pdch_system_num = (byte) 3;
    this.display_board_type_data(this.cmbP14S3, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP14S4_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 14;
    frmNetworkPDCH.pdch_system_num = (byte) 4;
    this.display_board_type_data(this.cmbP14S4, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP14S5_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 14;
    frmNetworkPDCH.pdch_system_num = (byte) 5;
    this.display_board_type_data(this.cmbP14S5, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP14S6_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 14;
    frmNetworkPDCH.pdch_system_num = (byte) 6;
    this.display_board_type_data(this.cmbP14S6, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP14S7_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 14;
    frmNetworkPDCH.pdch_system_num = (byte) 7;
    this.display_board_type_data(this.cmbP14S7, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP14S8_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 14;
    frmNetworkPDCH.pdch_system_num = (byte) 8;
    this.display_board_type_data(this.cmbP14S8, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP15S1_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 15;
    frmNetworkPDCH.pdch_system_num = (byte) 1;
    this.display_board_type_data(this.cmbP15S1, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP15S2_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 15;
    frmNetworkPDCH.pdch_system_num = (byte) 2;
    this.display_board_type_data(this.cmbP15S2, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP15S3_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 15;
    frmNetworkPDCH.pdch_system_num = (byte) 3;
    this.display_board_type_data(this.cmbP15S3, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP15S4_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 15;
    frmNetworkPDCH.pdch_system_num = (byte) 4;
    this.display_board_type_data(this.cmbP15S4, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP15S5_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 15;
    frmNetworkPDCH.pdch_system_num = (byte) 5;
    this.display_board_type_data(this.cmbP15S5, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP15S6_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 15;
    frmNetworkPDCH.pdch_system_num = (byte) 6;
    this.display_board_type_data(this.cmbP15S6, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP15S7_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 15;
    frmNetworkPDCH.pdch_system_num = (byte) 7;
    this.display_board_type_data(this.cmbP15S7, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP15S8_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 15;
    frmNetworkPDCH.pdch_system_num = (byte) 8;
    this.display_board_type_data(this.cmbP15S8, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP16S1_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 16 /*0x10*/;
    frmNetworkPDCH.pdch_system_num = (byte) 1;
    this.display_board_type_data(this.cmbP16S1, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP16S2_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 16 /*0x10*/;
    frmNetworkPDCH.pdch_system_num = (byte) 2;
    this.display_board_type_data(this.cmbP16S2, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP16S3_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 16 /*0x10*/;
    frmNetworkPDCH.pdch_system_num = (byte) 3;
    this.display_board_type_data(this.cmbP16S3, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP16S4_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 16 /*0x10*/;
    frmNetworkPDCH.pdch_system_num = (byte) 4;
    this.display_board_type_data(this.cmbP16S4, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP16S5_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 16 /*0x10*/;
    frmNetworkPDCH.pdch_system_num = (byte) 5;
    this.display_board_type_data(this.cmbP16S5, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP16S6_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 16 /*0x10*/;
    frmNetworkPDCH.pdch_system_num = (byte) 6;
    this.display_board_type_data(this.cmbP16S6, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP16S7_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 16 /*0x10*/;
    frmNetworkPDCH.pdch_system_num = (byte) 7;
    this.display_board_type_data(this.cmbP16S7, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnP16S8_Click(object sender, EventArgs e)
  {
    frmNetworkPDCH.pdch_port_num = (byte) 16 /*0x10*/;
    frmNetworkPDCH.pdch_system_num = (byte) 8;
    this.display_board_type_data(this.cmbP16S8, (int) frmNetworkPDCH.pdch_port_num, frmNetworkPDCH.pdch_system_num);
  }

  private void btnExit_Click(object sender, EventArgs e)
  {
    try
    {
      frmMainFormIPIS.mdch_db_struc_init();
      // TODO: Implement missing methods in taddb_msg class
      // taddb_msg.mldb_dis_brd_init();
      // taddb_msg.pdb_dis_brd_init();
      // taddb_msg.agdb_dis_brd_init();
      cgdb_dis.cgdb_dis_brd_init();
      frmMainFormIPIS.hub_init();
      lock (this)
      {
        network_db_read.get_nw_database();
        network_db_read.get_mldb_dis_brd_info();
        network_db_read.get_pdb_dis_brd_info();
        network_db_read.get_agdb_dis_brd_info();
        network_db_read.get_cgdb_dis_brd_info();
        network_db_read.get_mdch_port_info();
        network_db_read.get_pdch_port_info();
      }
      this.Close();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void btnOk_Click(object sender, EventArgs e)
  {
    try
    {
      if (Operators.CompareString(this.txtPdchName.Text, "", false) == 0)
      {
        int num1 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Please Enter the Name", "Msg Box", 0, 0, 0);
      }
      else if (Operators.CompareString(this.txtPdchAddress.Text, "", false) == 0)
      {
        int num2 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Please Enter the Address", "Msg Box", 0, 0, 0);
      }
      else if (Operators.CompareString(this.cmbPdchPfno.Text, "", false) == 0)
      {
        int num3 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Please Enter the Platform No", "Msg Box", 0, 0, 0);
      }
      else
      {
        this.pdch_update_no_systems();
        frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].system_type[(int) frmNetworkMDCH.mdch_system_num] = "PDCH";
        frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].pdch_name = this.txtPdchName.Text;
        frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].pdch_addr = Conversions.ToByte(this.txtPdchAddress.Text);
        frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].platform_no = Operators.CompareString(this.cmbPdchPfno.Text, "", false) != 0 ? this.cmbPdchPfno.Text : string.Empty;
        if (!this.chkPDCHSharedPfNo.Checked)
        {
          frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].shared_platform = false;
          frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].shared_platform_no = string.Empty;
        }
        else
        {
          frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].shared_platform = true;
          frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].shared_platform_no = this.cmbPdchSharedPfno.Text;
        }
        this.Close();
        MyProject.Forms.frmNetworkMDCH.BringToFront();
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void chkPDCHSharedPfNo_CheckedChanged(object sender, EventArgs e)
  {
    if (this.chkPDCHSharedPfNo.Checked)
    {
      this.lblSharedPfno.Visible = true;
      this.cmbPdchSharedPfno.Visible = true;
    }
    else
    {
      this.lblSharedPfno.Visible = false;
      this.cmbPdchSharedPfno.Visible = false;
    }
  }

  private void cmbPdchSharedPfno_DropDown(object sender, EventArgs e)
  {
    int index = 0;
    this.cmbPdchSharedPfno.Items.Clear();
    while (index < frmMainFormIPIS.pfno_cnt)
    {
      this.cmbPdchSharedPfno.Items.Add((object) frmMainFormIPIS.platform_nos[index]);
      checked { ++index; }
    }
  }

  private void frmNetworkPDCH_Load(object sender, EventArgs e)
  {
    int index = 0;
    this.cmbPdchPfno.Items.Clear();
    while (index < frmMainFormIPIS.pfno_cnt)
    {
      this.cmbPdchPfno.Items.Add((object) frmMainFormIPIS.platform_nos[index]);
      checked { ++index; }
    }
  }
}

}