using System.Data;
using IPIS.Repositories.Interfaces;

namespace IPIS.Services
{
    public class PlayPathService
    {
        private readonly IPlayPathRepository _repository;

        public PlayPathService(IPlayPathRepository repository)
        {
            _repository = repository;
        }

        public string GetPlayPath(string type)
        {
            if (string.IsNullOrEmpty(type))
                throw new ArgumentException("Type cannot be empty", nameof(type));

            return _repository.GetPlayPath(type);
        }

        public void AddPlayPath(string type, string path)
        {
            if (string.IsNullOrEmpty(type))
                throw new ArgumentException("Type cannot be empty", nameof(type));

            if (string.IsNullOrEmpty(path))
                throw new ArgumentException("Path cannot be empty", nameof(path));

            _repository.AddPlayPath(type, path);
        }

        public void UpdatePlayPath(string type, string path)
        {
            if (string.IsNullOrEmpty(type))
                throw new ArgumentException("Type cannot be empty", nameof(type));

            if (string.IsNullOrEmpty(path))
                throw new ArgumentException("Path cannot be empty", nameof(path));

            _repository.UpdatePlayPath(type, path);
        }

        public void DeletePlayPath(string type)
        {
            if (string.IsNullOrEmpty(type))
                throw new ArgumentException("Type cannot be empty", nameof(type));

            _repository.DeletePlayPath(type);
        }

        public DataTable GetAllPlayPaths()
        {
            return _repository.GetAllPlayPaths();
        }
    }
} 