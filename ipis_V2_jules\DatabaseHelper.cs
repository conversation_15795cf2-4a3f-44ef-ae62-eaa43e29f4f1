using Microsoft.Data.Sqlite;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text; // Required for File.ReadAllText if not implicitly available
using System.Threading.Tasks; // For Task
using ipis_V2_jules.Models; // For DisplayBoardConfig
using System.IO.Ports; // For Parity, StopBits enums

namespace ipis_V2_jules.Data
{
    public class DatabaseHelper
    {
        private readonly string _databaseFilePath;
        private const string SchemaFilePath = "ipis_V2_jules/schema_v2.sql"; // Relative path to schema

        /// <summary>
        /// Initializes a new instance of the DatabaseHelper class.
        /// </summary>
        /// <param name="databaseFilePath">The path to the SQLite database file.</param>
        public DatabaseHelper(string databaseFilePath)
        {
            _databaseFilePath = databaseFilePath ?? throw new ArgumentNullException(nameof(databaseFilePath));
        }

        /// <summary>
        /// Gets the SQLite connection string.
        /// </summary>
        /// <returns>The connection string.</returns>
        public string GetConnectionString()
        {
            return $"Data Source={_databaseFilePath}";
        }

        /// <summary>
        /// Ensures the database connection enforces foreign key constraints.
        /// This method should be called after opening a connection.
        /// </summary>
        /// <param name="connection">The SQLite connection.</param>
        private void EnableForeignKeys(SqliteConnection connection)
        {
            using (var command = connection.CreateCommand())
            {
                command.CommandText = "PRAGMA foreign_keys = ON;";
                command.ExecuteNonQuery();
            }
        }

        /// <summary>
        /// Initializes the database. If the database file doesn't exist or forceRecreate is true,
        /// it creates the database and executes the schema script.
        /// </summary>
        /// <param name="forceRecreate">If true, recreates the database even if it exists.</param>
        public void InitializeDatabase(bool forceRecreate = false)
        {
            try
            {
                bool dbExists = File.Exists(_databaseFilePath);

                if (forceRecreate && dbExists)
                {
                    File.Delete(_databaseFilePath);
                    dbExists = false;
                }

                if (!dbExists)
                {
                    // Create the directory if it doesn't exist
                    string directoryPath = Path.GetDirectoryName(_databaseFilePath);
                    if (!string.IsNullOrEmpty(directoryPath) && !Directory.Exists(directoryPath))
                    {
                        Directory.CreateDirectory(directoryPath);
                    }

                    // Create the database file by opening and closing a connection
                    using (var connection = new SqliteConnection(GetConnectionString()))
                    {
                        connection.Open(); // This creates the file
                        EnableForeignKeys(connection); // Enable FKs for this initial setup connection
                        connection.Close();
                    }

                    // Read and execute schema DDL
                    if (!File.Exists(SchemaFilePath))
                    {
                        throw new FileNotFoundException($"Schema file not found at {Path.GetFullPath(SchemaFilePath)}.", SchemaFilePath);
                    }
                    string schemaSql = File.ReadAllText(SchemaFilePath);

                    // Execute schema DDL statements one by one
                    // Splitting by semicolon might not be robust for all SQL, but common for simple DDL scripts.
                    // Consider a more robust SQL parser for complex scripts with semicolons in strings/comments.
                    var statements = schemaSql.Split(new[] { ';' }, StringSplitOptions.RemoveEmptyEntries);

                    using (var connection = new SqliteConnection(GetConnectionString()))
                    {
                        connection.Open();
                        EnableForeignKeys(connection);
                        foreach (var statement in statements)
                        {
                            if (!string.IsNullOrWhiteSpace(statement))
                            {
                                using (var command = connection.CreateCommand())
                                {
                                    command.CommandText = statement.Trim();
                                    command.ExecuteNonQuery();
                                }
                            }
                        }
                        connection.Close();
                    }
                }
            }
            catch (Exception ex)
            {
                // Log the exception (e.g., using a logging framework)
                Console.WriteLine($"Error initializing database: {ex.Message}");
                throw; // Re-throw to allow higher-level handling
            }
        }

        /// <summary>
        /// Executes a non-query SQL command (INSERT, UPDATE, DELETE).
        /// </summary>
        /// <param name="query">The SQL query string.</param>
        /// <param name="parameters">Optional dictionary of parameters.</param>
        /// <returns>The number of rows affected.</returns>
        public int ExecuteNonQuery(string query, Dictionary<string, object> parameters = null)
        {
            try
            {
                using (var connection = new SqliteConnection(GetConnectionString()))
                {
                    connection.Open();
                    EnableForeignKeys(connection);
                    using (var command = connection.CreateCommand())
                    {
                        command.CommandText = query;
                        if (parameters != null)
                        {
                            foreach (var param in parameters)
                            {
                                command.Parameters.AddWithValue(param.Key, param.Value ?? DBNull.Value);
                            }
                        }
                        return command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error executing non-query: {ex.Message} for query: {query}");
                throw;
            }
        }

        /// <summary>
        /// Executes a query and returns the first column of the first row in the result set.
        /// </summary>
        /// <param name="query">The SQL query string.</param>
        /// <param name="parameters">Optional dictionary of parameters.</param>
        /// <returns>The scalar result, or null if the result is empty or DBNull.</returns>
        public object ExecuteScalar(string query, Dictionary<string, object> parameters = null)
        {
            try
            {
                using (var connection = new SqliteConnection(GetConnectionString()))
                {
                    connection.Open();
                    EnableForeignKeys(connection);
                    using (var command = connection.CreateCommand())
                    {
                        command.CommandText = query;
                        if (parameters != null)
                        {
                            foreach (var param in parameters)
                            {
                                command.Parameters.AddWithValue(param.Key, param.Value ?? DBNull.Value);
                            }
                        }
                        object result = command.ExecuteScalar();
                        return (result == DBNull.Value) ? null : result;
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error executing scalar: {ex.Message} for query: {query}");
                throw;
            }
        }

        /// <summary>
        /// Executes a query and returns the results as a list of dictionaries.
        /// Each dictionary represents a row, with column names as keys.
        /// </summary>
        /// <param name="query">The SQL query string.</param>
        /// <param name="parameters">Optional dictionary of parameters.</param>
        /// <returns>A list of dictionaries, where each dictionary is a row.</returns>
        public List<Dictionary<string, object>> ExecuteQuery(string query, Dictionary<string, object> parameters = null)
        {
            var results = new List<Dictionary<string, object>>();
            try
            {
                using (var connection = new SqliteConnection(GetConnectionString()))
                {
                    connection.Open();
                    EnableForeignKeys(connection);
                    using (var command = connection.CreateCommand())
                    {
                        command.CommandText = query;
                        if (parameters != null)
                        {
                            foreach (var param in parameters)
                            {
                                command.Parameters.AddWithValue(param.Key, param.Value ?? DBNull.Value);
                            }
                        }

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var row = new Dictionary<string, object>();
                                for (int i = 0; i < reader.FieldCount; i++)
                                {
                                    row[reader.GetName(i)] = reader.GetValue(i) == DBNull.Value ? null : reader.GetValue(i);
                                }
                                results.Add(row);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error executing query: {ex.Message} for query: {query}");
                throw;
            }
            return results;
        }

        /// <summary>
        /// Retrieves all display board configurations from the database.
        /// Assumes a table named 'DisplayBoardConfig' with columns matching the DisplayBoardConfig model.
        /// </summary>
        /// <returns>A list of DisplayBoardConfig objects.</returns>
        public async Task<List<DisplayBoardConfig>> GetAllDisplayBoardConfigsAsync()
        {
            var configs = new List<DisplayBoardConfig>();
            // The table name in schema_v2.sql is 'DisplayBoardConfig'.
            // Columns: BoardID (INTEGER PK), BoardName (TEXT), BoardType (TEXT), BoardAddress (INTEGER), PlatformID (INTEGER FK),
            // HubType (TEXT), HubAddress (INTEGER), PortNumber (INTEGER), SerialNumberInPort (INTEGER), ConfigurationJSON (TEXT)
            // The C# DisplayBoardConfig model has slightly different field names and types (e.g. PortName, BaudRate).
            // This query needs to map DB columns to Model properties.
            // For now, let's assume a simplified mapping or that the DB table matches the model more closely.
            // A more robust solution would use an ORM or explicit mapping.

            // This is a simplified query. It assumes the DB table 'DisplayBoardConfig'
            // has columns that directly map or can be easily converted to DisplayBoardConfig model properties.
            // Specifically, it assumes PortName, BaudRate, Parity, DataBits, StopBits, Lines, CharsPerLine, Platform are present.
            string query = "SELECT BoardID, BoardName, BoardType, Platform, PortName, BaudRate, Parity, DataBits, StopBits, Lines, CharsPerLine, CoachSlots, CoachSlotLength, PixelWidth, PixelHeight, FormatterSettingsJSON FROM DisplayBoardConfig";

            // Using ExecuteQuery which returns List<Dictionary<string, object>>
            // This needs to run synchronously for now as ExecuteQuery is not async.
            // To make this truly async, ExecuteQuery itself would need to be async.
            // For the purpose of this subtask, we'll call the sync version and wrap in Task.FromResult.

            List<Dictionary<string, object>> rows;
            try
            {
                rows = ExecuteQuery(query); // This is synchronous
            }
            catch (Exception ex)
            {
                // This typically happens if the table or columns don't exist.
                // For initial setup, return an empty list or a default config.
                Console.WriteLine($"Error querying DisplayBoardConfig table: {ex.Message}. Returning empty list or default configs.");
                // Example: return a default config for testing if table is not ready
                // return new List<DisplayBoardConfig> { new DisplayBoardConfig { BoardId = 1, BoardName = "TestAGDB", BoardType = "AGDB", PortName = "COM1" } };
                return new List<DisplayBoardConfig>(); // Return empty list on error
            }

            foreach (var row in rows)
            {
                try
                {
                    var config = new DisplayBoardConfig
                    {
                        BoardId = Convert.ToInt32(row["BoardID"]),
                        BoardName = row["BoardName"]?.ToString() ?? "Unknown",
                        BoardType = row["BoardType"]?.ToString() ?? "AGDB",
                        Platform = row["Platform"]?.ToString(),
                        PortName = row["PortName"]?.ToString(),
                        BaudRate = row["BaudRate"] != DBNull.Value ? Convert.ToInt32(row["BaudRate"]) : 9600,
                        Parity = Enum.TryParse<Parity>(row["Parity"]?.ToString(), true, out var parity) ? parity : Parity.None,
                        DataBits = row["DataBits"] != DBNull.Value ? Convert.ToInt32(row["DataBits"]) : 8,
                        StopBits = Enum.TryParse<StopBits>(row["StopBits"]?.ToString(), true, out var stopBits) ? stopBits : StopBits.One,
                        Lines = row["Lines"] != DBNull.Value ? Convert.ToInt32(row["Lines"]) : 2,
                        CharsPerLine = row["CharsPerLine"] != DBNull.Value ? Convert.ToInt32(row["CharsPerLine"]) : 20,
                        CoachSlots = row["CoachSlots"] != DBNull.Value ? Convert.ToInt32(row["CoachSlots"]) : 12,
                        CoachSlotLength = row["CoachSlotLength"] != DBNull.Value ? Convert.ToInt32(row["CoachSlotLength"]) : 4,
                        PixelWidth = row["PixelWidth"] != DBNull.Value ? Convert.ToInt32(row["PixelWidth"]) : 120,
                        PixelHeight = row["PixelHeight"] != DBNull.Value ? Convert.ToInt32(row["PixelHeight"]) : 16,
                        // FormatterSettings would ideally be stored as JSON in DB and deserialized here.
                        // Assuming a column FormatterSettingsJSON exists.
                        // FormatterSettings = DeserializeFormatterSettings(row["FormatterSettingsJSON"]?.ToString())
                    };
                    configs.Add(config);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error mapping row to DisplayBoardConfig: {ex.Message}. Row data: {string.Join(", ", row.Select(kv => $"{kv.Key}={kv.Value}"))}");
                    // Skip this problematic row
                }
            }

            // Wrap synchronous result in a completed task
            return await Task.FromResult(configs);
        }

        // Helper to deserialize FormatterSettings (example)
        // private Dictionary<string, string> DeserializeFormatterSettings(string json)
        // {
        //     if (string.IsNullOrWhiteSpace(json)) return new Dictionary<string, string>();
        //     try { return System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, string>>(json); }
        //     catch { return new Dictionary<string, string>(); }
        // }
    }
}
