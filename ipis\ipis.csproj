﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <!--Project was exported from assembly: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe-->
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{0DE0A3B4-82B7-4F67-8EBE-941FC9136485}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <AssemblyName>ipis</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <ApplicationVersion>*******</ApplicationVersion>
    <FileAlignment>512</FileAlignment>
    <RootNamespace>ipis</RootNamespace>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <NoWin32Manifest>true</NoWin32Manifest>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Microsoft.VisualBasic" />
    <Reference Include="Microsoft.VisualBasic.Compatibility" />
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="AxInterop.AcroPDFLib">
      <HintPath>lib\AxInterop.AcroPDFLib.dll</HintPath>
    </Reference>
    <Reference Include="Interop.AcroPDFLib">
      <HintPath>lib\Interop.AcroPDFLib.dll</HintPath>
    </Reference>
    <Reference Include="ScrollingTextControl">
      <HintPath>lib\ScrollingTextControl.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="adminDashboard.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="adminLogin.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ConfigAdmin.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="agdb_byte_construct.cs" />
    <Compile Include="agdb_lookup_table.cs" />
    <Compile Include="connection_Database.cs" />
    <Compile Include="common_def.cs" />
    <Compile Include="defines.cs" />
    <Compile Include="cgdb_dis.cs" />
    <Compile Include="LinkCheckSetting.cs" />
    <Compile Include="taddb_msg.cs" />
    <Compile Include="WriteDisplayMessageStruct.cs" />
    <Compile Include="frmDisplayBoardSettings.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmMdchCfgGetDisplay.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmPdchCfgGetDisplay.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmPortConfig.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="agdb_api.cs" />
    <Compile Include="cgdb_api.cs" />
    <Compile Include="Checksum.cs" />
    <Compile Include="mdch_api.cs" />
    <Compile Include="mldb_api.cs" />
    <Compile Include="pdb_api.cs" />
    <Compile Include="pdch_api.cs" />
    <Compile Include="frm_CgsOnlineForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmAddMsg.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmAddPfno.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmCfgInt.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmCom.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmMainFormIPIS.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmAbout.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmContacts.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmHelp.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="intensity_Setting.cs" />
    <Compile Include="frmFont.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmLanguage.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Led_byte_Display.cs" />
    <Compile Include="led_msg_display.cs" />
    <Compile Include="Log_file.cs" />
    <Compile Include="basMsgBoxEx.cs" />
    <Compile Include="frmMsgBoxEx.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmNetworkAGDB.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmNetworkCGDB.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmNetworkMDCH.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmNetworkMLDB.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmNetworkPDB.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmNetworkPDCH.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="network_db_read.cs" />
    <Compile Include="online_trains.cs" />
    <Compile Include="pkt_construct.cs" />
    <Compile Include="RS232.cs" />
    <Compile Include="frmPlatformNo.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmStationCode.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmStationDetails.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmStationNameVoice.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="struct_file.cs" />
    <Compile Include="frmCgs.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmTrainConfig.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmTrainDetails.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmTrainStatusMsg.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmTrainstatusPopup.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmTrainTimings.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmAddNewUser.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmChangeAccount.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmChangeAccountType.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmChangeAnotherUserPwd.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmChangeName.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmChangePassword.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmChangeUserDetails.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmChangeUserPwd.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmDeleteUser.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmLogin.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmPassword.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmUser.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmUserNamePassword.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="CCTV_NewForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmCCTV.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmSuryaLogo.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmAddTrainNameVoice.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmAddVoiceSplMsg.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmDeleteTrainNameVoice.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmRecordPlay.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmVoice.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmVoice_Special_Messages.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="voice_xml_files.cs" />
    <Compile Include="My\MyApplication.cs" />
    <Compile Include="My\MyComputer.cs" />
    <Compile Include="My\MyProject.cs" />
    <Compile Include="My\MySettings.cs" />
    <Compile Include="My\MySettingsProperty.cs" />
    <Compile Include="My\Resources\Resources.cs" />
    <Compile Include="AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="adminDashboard.resx" />
    <EmbeddedResource Include="adminLogin.resx" />
    <EmbeddedResource Include="CCTV_NewForm.resx" />
    <EmbeddedResource Include="ConfigAdmin.resx" />
    <EmbeddedResource Include="frm_CgsOnlineForm.resx" />
    <EmbeddedResource Include="frmAbout.resx" />
    <EmbeddedResource Include="frmAddMsg.resx" />
    <EmbeddedResource Include="frmAddNewUser.resx" />
    <EmbeddedResource Include="frmAddPfno.resx" />
    <EmbeddedResource Include="frmAddTrainNameVoice.resx" />
    <EmbeddedResource Include="frmAddVoiceSplMsg.resx" />
    <EmbeddedResource Include="frmCCTV.resx" />
    <EmbeddedResource Include="frmCfgInt.resx" />
    <EmbeddedResource Include="frmCgs.resx" />
    <EmbeddedResource Include="frmChangeAccount.resx" />
    <EmbeddedResource Include="frmChangeAccountType.resx" />
    <EmbeddedResource Include="frmChangeAnotherUserPwd.resx" />
    <EmbeddedResource Include="frmChangeName.resx" />
    <EmbeddedResource Include="frmChangePassword.resx" />
    <EmbeddedResource Include="frmChangeUserDetails.resx" />
    <EmbeddedResource Include="frmChangeUserPwd.resx" />
    <EmbeddedResource Include="frmCom.resx" />
    <EmbeddedResource Include="frmContacts.resx" />
    <EmbeddedResource Include="frmDeleteTrainNameVoice.resx" />
    <EmbeddedResource Include="frmDeleteUser.resx" />
    <EmbeddedResource Include="frmDisplayBoardSettings.resx" />
    <EmbeddedResource Include="frmFont.resx" />
    <EmbeddedResource Include="frmHelp.resx" />
    <EmbeddedResource Include="frmLanguage.resx" />
    <EmbeddedResource Include="frmLogin.resx" />
    <EmbeddedResource Include="frmMainFormIPIS.resx" />
    <EmbeddedResource Include="frmMdchCfgGetDisplay.resx" />
    <EmbeddedResource Include="frmMsgBoxEx.resx" />
    <EmbeddedResource Include="frmNetworkAGDB.resx" />
    <EmbeddedResource Include="frmNetworkCGDB.resx" />
    <EmbeddedResource Include="frmNetworkMDCH.resx" />
    <EmbeddedResource Include="frmNetworkMLDB.resx" />
    <EmbeddedResource Include="frmNetworkPDB.resx" />
    <EmbeddedResource Include="frmNetworkPDCH.resx" />
    <EmbeddedResource Include="frmPassword.resx" />
    <EmbeddedResource Include="frmPdchCfgGetDisplay.resx" />
    <EmbeddedResource Include="frmPlatformNo.resx" />
    <EmbeddedResource Include="frmPortConfig.resx" />
    <EmbeddedResource Include="frmRecordPlay.resx" />
    <EmbeddedResource Include="frmStationCode.resx" />
    <EmbeddedResource Include="frmStationDetails.resx" />
    <EmbeddedResource Include="frmStationNameVoice.resx" />
    <EmbeddedResource Include="frmSuryaLogo.resx" />
    <EmbeddedResource Include="frmTrainConfig.resx" />
    <EmbeddedResource Include="frmTrainDetails.resx" />
    <EmbeddedResource Include="frmTrainStatusMsg.resx" />
    <EmbeddedResource Include="frmTrainstatusPopup.resx" />
    <EmbeddedResource Include="frmTrainTimings.resx" />
    <EmbeddedResource Include="frmUser.resx" />
    <EmbeddedResource Include="frmUserNamePassword.resx" />
    <EmbeddedResource Include="frmVoice.resx" />
    <EmbeddedResource Include="frmVoice_Special_Messages.resx" />
    <EmbeddedResource Include="Resources.resx" />
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>