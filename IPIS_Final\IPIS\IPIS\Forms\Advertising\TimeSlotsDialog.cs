using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using IPIS.Utils;

namespace IPIS.Forms.Advertising
{
    public partial class TimeSlotsDialog : Form
    {
        private List<(int startHour, int startMinute, int endHour, int endMinute)> slots;
        private ListBox lstSlots;
        private NumericUpDown numStartHour, numStartMinute, numEndHour, numEndMinute;
        private Button btnAdd, btnEdit, btnRemove, btnOK, btnCancel;
        private Label lblError;
        private int editingIndex = -1;

        public TimeSlotsDialog(List<(int startHour, int startMinute, int endHour, int endMinute)> initialSlots)
        {
            slots = initialSlots.Select(x => x).ToList();
            InitializeComponent();
            RefreshSlotList();
        }

        public List<(int startHour, int startMinute, int endHour, int endMinute)> GetTimeSlots() => slots;

        private void InitializeComponent()
        {
            this.Text = "Manage Time Slots";
            this.Size = new Size(420, 420);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            var mainLayout = LayoutHelper.CreateSingleColumnLayout(6);
            mainLayout.Dock = DockStyle.Fill;

            // Slot list
            lstSlots = new ListBox { Height = 140, Dock = DockStyle.Fill };
            lstSlots.SelectedIndexChanged += LstSlots_SelectedIndexChanged;
            lstSlots.DoubleClick += LstSlots_DoubleClick;
            mainLayout.Controls.Add(lstSlots, 0, 0);

            // Time pickers
            var timePanel = new FlowLayoutPanel { FlowDirection = FlowDirection.LeftToRight, AutoSize = true };
            timePanel.Controls.Add(new Label { Text = "Start:" });
            numStartHour = new NumericUpDown { Minimum = 0, Maximum = 23, Width = 36 };
            numStartMinute = new NumericUpDown { Minimum = 0, Maximum = 59, Width = 36 };
            timePanel.Controls.Add(numStartHour);
            timePanel.Controls.Add(new Label { Text = ":" });
            timePanel.Controls.Add(numStartMinute);
            timePanel.Controls.Add(new Label { Text = "  End:" });
            numEndHour = new NumericUpDown { Minimum = 0, Maximum = 23, Width = 36 };
            numEndMinute = new NumericUpDown { Minimum = 0, Maximum = 59, Width = 36 };
            timePanel.Controls.Add(numEndHour);
            timePanel.Controls.Add(new Label { Text = ":" });
            timePanel.Controls.Add(numEndMinute);
            mainLayout.Controls.Add(timePanel, 0, 1);

            // Error label
            lblError = new Label { ForeColor = Color.Red, AutoSize = true, Padding = new Padding(0, 2, 0, 2) };
            mainLayout.Controls.Add(lblError, 0, 2);

            // Add/Edit/Remove buttons
            var btnPanel = new FlowLayoutPanel { FlowDirection = FlowDirection.LeftToRight, AutoSize = true };
            btnAdd = new Button { Text = "Add" };
            ButtonStyler.ApplyStandardStyle(btnAdd, "primary", "small");
            btnAdd.Click += BtnAdd_Click;
            btnEdit = new Button { Text = "Edit" };
            ButtonStyler.ApplyStandardStyle(btnEdit, "info", "small");
            btnEdit.Click += BtnEdit_Click;
            btnRemove = new Button { Text = "Remove" };
            ButtonStyler.ApplyStandardStyle(btnRemove, "danger", "small");
            btnRemove.Click += BtnRemove_Click;
            btnPanel.Controls.AddRange(new Control[] { btnAdd, btnEdit, btnRemove });
            mainLayout.Controls.Add(btnPanel, 0, 3);

            // OK/Cancel buttons
            var okPanel = new FlowLayoutPanel { FlowDirection = FlowDirection.LeftToRight, AutoSize = true };
            btnOK = new Button { Text = "OK", DialogResult = DialogResult.OK };
            ButtonStyler.ApplyStandardStyle(btnOK, "primary");
            btnOK.Click += BtnOK_Click;
            btnCancel = new Button { Text = "Cancel", DialogResult = DialogResult.Cancel };
            ButtonStyler.ApplyStandardStyle(btnCancel, "secondary");
            okPanel.Controls.AddRange(new Control[] { btnOK, btnCancel });
            mainLayout.Controls.Add(okPanel, 0, 4);

            this.Controls.Add(mainLayout);
        }

        private void RefreshSlotList()
        {
            lstSlots.Items.Clear();
            foreach (var (sh, sm, eh, em) in slots)
            {
                lstSlots.Items.Add($"{sh:D2}:{sm:D2} - {eh:D2}:{em:D2}");
            }
            editingIndex = -1;
            lstSlots.ClearSelected();
            lblError.Text = "";
        }

        private void BtnAdd_Click(object sender, EventArgs e)
        {
            var slot = GetSlotFromInputs();
            if (!ValidateSlot(slot, -1, out string error))
            {
                lblError.Text = error;
                return;
            }
            slots.Add(slot);
            RefreshSlotList();
        }

        private void BtnEdit_Click(object sender, EventArgs e)
        {
            if (lstSlots.SelectedIndex < 0) return;
            var slot = GetSlotFromInputs();
            if (!ValidateSlot(slot, lstSlots.SelectedIndex, out string error))
            {
                lblError.Text = error;
                return;
            }
            slots[lstSlots.SelectedIndex] = slot;
            RefreshSlotList();
        }

        private void BtnRemove_Click(object sender, EventArgs e)
        {
            if (lstSlots.SelectedIndex < 0) return;
            slots.RemoveAt(lstSlots.SelectedIndex);
            RefreshSlotList();
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            // Final validation: no overlaps, all valid
            for (int i = 0; i < slots.Count; i++)
            {
                if (!ValidateSlot(slots[i], i, out string error))
                {
                    lblError.Text = $"Slot {i + 1}: {error}";
                    this.DialogResult = DialogResult.None;
                    return;
                }
            }
            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        private void LstSlots_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (lstSlots.SelectedIndex >= 0)
            {
                var (sh, sm, eh, em) = slots[lstSlots.SelectedIndex];
                numStartHour.Value = sh;
                numStartMinute.Value = sm;
                numEndHour.Value = eh;
                numEndMinute.Value = em;
            }
        }

        private void LstSlots_DoubleClick(object sender, EventArgs e)
        {
            if (lstSlots.SelectedIndex >= 0)
            {
                BtnEdit_Click(sender, e);
            }
        }

        private (int, int, int, int) GetSlotFromInputs()
        {
            return ((int)numStartHour.Value, (int)numStartMinute.Value, (int)numEndHour.Value, (int)numEndMinute.Value);
        }

        private bool ValidateSlot((int sh, int sm, int eh, int em) slot, int ignoreIndex, out string error)
        {
            error = "";
            int s = slot.sh * 60 + slot.sm;
            int e = slot.eh * 60 + slot.em;
            if (s >= e)
            {
                error = "Start time must be before end time.";
                return false;
            }
            for (int i = 0; i < slots.Count; i++)
            {
                if (i == ignoreIndex) continue;
                var (osh, osm, oeh, oem) = slots[i];
                int os = osh * 60 + osm;
                int oe = oeh * 60 + oem;
                if (!(e <= os || s >= oe))
                {
                    error = $"Overlaps with slot {i + 1}.";
                    return false;
                }
                if (s == os && e == oe)
                {
                    error = $"Duplicate of slot {i + 1}.";
                    return false;
                }
            }
            return true;
        }
    }
} 