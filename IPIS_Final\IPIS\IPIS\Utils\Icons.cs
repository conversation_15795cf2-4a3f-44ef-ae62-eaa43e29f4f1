using System.Drawing;

namespace IPIS.Utils
{
    public static class Icons
    {
        public static Image Add => SystemIcons.Application.ToBitmap();
        public static Image Edit => SystemIcons.Information.ToBitmap();
        public static Image Delete => SystemIcons.Error.ToBitmap();
        public static Image Refresh => SystemIcons.Application.ToBitmap();
        public static Image Play => SystemIcons.Application.ToBitmap();
        public static Image Pause => SystemIcons.Application.ToBitmap();
        public static Image Stop => SystemIcons.Application.ToBitmap();
    }
} 