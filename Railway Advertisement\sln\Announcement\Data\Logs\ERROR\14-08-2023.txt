August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:13,Error - System.ArgumentException: Empty path name is not legal.
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\31-03-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\31-03-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\01-04-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\01-04-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\02-04-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\02-04-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\03-04-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\03-04-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\04-04-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\04-04-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\05-04-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\05-04-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\06-04-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\06-04-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\07-04-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\07-04-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\08-04-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\08-04-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\09-04-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\09-04-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\10-04-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\10-04-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\11-04-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\11-04-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\12-04-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\12-04-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\13-04-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\13-04-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\14-04-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\14-04-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\15-04-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\15-04-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\16-04-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\16-04-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\17-04-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\17-04-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\18-04-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\18-04-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\19-04-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\19-04-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\20-04-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\20-04-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\21-04-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\21-04-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\22-04-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\22-04-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\23-04-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\23-04-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\24-04-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\24-04-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\25-04-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\25-04-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\26-04-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\26-04-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\27-04-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\27-04-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\28-04-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\28-04-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\29-04-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\29-04-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\30-04-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\30-04-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\01-05-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\01-05-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\02-05-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\02-05-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\03-05-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\03-05-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\04-05-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\04-05-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\05-05-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\05-05-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\06-05-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\06-05-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\07-05-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\07-05-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\08-05-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\08-05-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\09-05-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\09-05-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\10-05-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\10-05-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\11-05-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\11-05-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\12-05-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\12-05-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\13-05-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\13-05-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\14-05-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\14-05-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\15-05-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\15-05-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\16-05-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\16-05-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\17-05-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\17-05-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\18-05-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\18-05-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\19-05-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\19-05-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\20-05-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\20-05-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\21-05-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\21-05-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\22-05-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\22-05-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\23-05-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\23-05-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\28-05-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\28-05-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\29-05-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\29-05-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\30-05-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\30-05-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\31-05-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\31-05-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\01-06-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\01-06-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\02-06-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\02-06-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\03-06-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\03-06-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\04-06-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\04-06-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\05-06-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\05-06-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\06-06-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\06-06-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\07-06-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\07-06-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\08-06-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\08-06-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\09-06-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\09-06-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\10-06-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\10-06-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\11-06-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\11-06-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\12-06-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\12-06-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\14-06-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\14-06-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\15-06-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\15-06-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\16-06-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\16-06-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\17-06-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\17-06-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\18-06-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\18-06-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\19-06-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\19-06-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\20-06-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\20-06-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\21-06-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\21-06-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\22-06-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\22-06-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\23-06-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\23-06-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\24-06-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\24-06-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\25-06-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\25-06-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\26-06-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\26-06-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\27-06-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\27-06-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\28-06-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\28-06-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\29-06-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\29-06-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\30-06-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\30-06-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\01-07-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\01-07-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\02-07-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\02-07-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\03-07-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\03-07-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\04-07-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\04-07-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\05-07-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\05-07-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\06-07-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\06-07-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\07-07-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\07-07-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\08-07-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\08-07-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\09-07-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\09-07-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\10-07-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\10-07-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\11-07-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\11-07-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\12-07-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\12-07-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\13-07-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\13-07-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\14-07-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\14-07-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\20-07-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\20-07-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\21-07-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\21-07-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\22-07-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\22-07-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\23-07-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\23-07-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\24-07-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\24-07-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\25-07-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\25-07-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\26-07-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\26-07-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\27-07-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\27-07-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\28-07-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\28-07-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\30-07-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\30-07-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\31-07-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\31-07-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\01-08-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\01-08-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\02-08-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\02-08-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\03-08-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\03-08-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\04-08-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\04-08-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\05-08-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\05-08-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\06-08-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\06-08-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\07-08-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\07-08-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\08-08-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\08-08-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,11:22:34,Error - System.IO.FileNotFoundException: Could not find file 'D:\ipis exe\Debug\Data\Logs\ANN\09-08-2023.txt'.
File name: 'D:\ipis exe\Debug\Data\Logs\ANN\09-08-2023.txt'
   at System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   at System.IO.FileStream.Init(String path, FileMode mode, FileAccess access, Int32 rights, Boolean useRights, FileShare share, Int32 bufferSize, FileOptions options, SECURITY_ATTRIBUTES secAttrs, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.FileStream..ctor(String path, FileMode mode, FileAccess access, FileShare share, Int32 bufferSize, FileOptions options, String msgPath, Boolean bFromProxy, Boolean useLongPath, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path, Encoding encoding, Boolean detectEncodingFromByteOrderMarks, Int32 bufferSize, Boolean checkHost)
   at System.IO.StreamReader..ctor(String path)
   at Announcement.Reports.BTN_Gen_Click(Object sender, EventArgs e)
August 14 2023,15:27:00,Error - System.Data.OleDb.OleDbException (0x80004005): The changes you requested to the table were not successful because they would create duplicate values in the index, primary key, or relationship.  Change the data in the field or fields that contain duplicate data, remove the index, or redefine the index to permit duplicate entries and try again.
   at System.Data.OleDb.OleDbCommand.ExecuteCommandTextErrorHandling(OleDbHResult hr)
   at System.Data.OleDb.OleDbCommand.ExecuteCommandTextForSingleResult(tagDBPARAMS dbParams, Object& executeResult)
   at System.Data.OleDb.OleDbCommand.ExecuteCommandText(Object& executeResult)
   at System.Data.OleDb.OleDbCommand.ExecuteCommand(CommandBehavior behavior, Object& executeResult)
   at System.Data.OleDb.OleDbCommand.ExecuteReaderInternal(CommandBehavior behavior, String method)
   at System.Data.OleDb.OleDbCommand.ExecuteNonQuery()
   at Announcement.Class_Database.Update_Database(String Query)
