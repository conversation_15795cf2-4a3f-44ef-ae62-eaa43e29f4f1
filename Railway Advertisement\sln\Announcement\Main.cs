﻿using System;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Media;
using System.Runtime.InteropServices;
using System.Threading;
using System.Windows.Forms;
using AxWMPLib;

namespace Announcement
{
	// Token: 0x02000010 RID: 16
	public partial class Main : Form
	{
		// Token: 0x06000096 RID: 150
		[DllImport("user32.dll")]
		public static extern IntPtr SendMessageW(IntPtr hWnd, int Msg, IntPtr wParam, IntPtr lParam);

		// Token: 0x06000097 RID: 151
		[DllImport("winmm.dll", CharSet = CharSet.Ansi, EntryPoint = "mciSendStringA", ExactSpelling = true, SetLastError = true)]
		private static extern int mciSendString(string lpstrCommand, string lpstrReturnString, int uReturnLength, int hwndCallback);

		// Token: 0x17000004 RID: 4
		// (get) Token: 0x06000098 RID: 152 RVA: 0x0001826E File Offset: 0x0001646E
		// (set) Token: 0x06000099 RID: 153 RVA: 0x00018275 File Offset: 0x00016475
		public static string PATH_AUDIO { get; set; }

		// Token: 0x17000005 RID: 5
		// (get) Token: 0x0600009A RID: 154 RVA: 0x0001827D File Offset: 0x0001647D
		// (set) Token: 0x0600009B RID: 155 RVA: 0x00018284 File Offset: 0x00016484
		public static string SAVE_Image { get; set; }

		// Token: 0x17000006 RID: 6
		// (get) Token: 0x0600009C RID: 156 RVA: 0x0001828C File Offset: 0x0001648C
		// (set) Token: 0x0600009D RID: 157 RVA: 0x00018293 File Offset: 0x00016493
		public static string EDIT_Image { get; set; }

		// Token: 0x17000007 RID: 7
		// (get) Token: 0x0600009E RID: 158 RVA: 0x0001829B File Offset: 0x0001649B
		// (set) Token: 0x0600009F RID: 159 RVA: 0x000182A2 File Offset: 0x000164A2
		public static string INSERT_Image { get; set; }

		// Token: 0x17000008 RID: 8
		// (get) Token: 0x060000A0 RID: 160 RVA: 0x000182AA File Offset: 0x000164AA
		// (set) Token: 0x060000A1 RID: 161 RVA: 0x000182B1 File Offset: 0x000164B1
		public static string DELETE_Image { get; set; }

		// Token: 0x17000009 RID: 9
		// (get) Token: 0x060000A2 RID: 162 RVA: 0x000182B9 File Offset: 0x000164B9
		// (set) Token: 0x060000A3 RID: 163 RVA: 0x000182C0 File Offset: 0x000164C0
		public static string EXIT_Image { get; set; }

		// Token: 0x1700000A RID: 10
		// (get) Token: 0x060000A4 RID: 164 RVA: 0x000182C8 File Offset: 0x000164C8
		// (set) Token: 0x060000A5 RID: 165 RVA: 0x000182CF File Offset: 0x000164CF
		public static bool Flag_DatabaseLogin { get; set; }

		// Token: 0x1700000B RID: 11
		// (get) Token: 0x060000A6 RID: 166 RVA: 0x000182D7 File Offset: 0x000164D7
		// (set) Token: 0x060000A7 RID: 167 RVA: 0x000182DE File Offset: 0x000164DE
		public static string MAC1 { get; set; }

		// Token: 0x1700000C RID: 12
		// (get) Token: 0x060000A8 RID: 168 RVA: 0x000182E6 File Offset: 0x000164E6
		// (set) Token: 0x060000A9 RID: 169 RVA: 0x000182ED File Offset: 0x000164ED
		public static string MAC2 { get; set; }

		// Token: 0x1700000D RID: 13
		// (get) Token: 0x060000AA RID: 170 RVA: 0x000182F5 File Offset: 0x000164F5
		// (set) Token: 0x060000AB RID: 171 RVA: 0x000182FC File Offset: 0x000164FC
		public static string Stn_Name { get; set; }

		// Token: 0x1700000E RID: 14
		// (get) Token: 0x060000AC RID: 172 RVA: 0x00018304 File Offset: 0x00016504
		// (set) Token: 0x060000AD RID: 173 RVA: 0x0001830B File Offset: 0x0001650B
		public static string Stn_Code { get; set; }

		// Token: 0x1700000F RID: 15
		// (get) Token: 0x060000AE RID: 174 RVA: 0x00018313 File Offset: 0x00016513
		// (set) Token: 0x060000AF RID: 175 RVA: 0x0001831A File Offset: 0x0001651A
		public static string Station_Name { get; set; }

		// Token: 0x17000010 RID: 16
		// (get) Token: 0x060000B0 RID: 176 RVA: 0x00018322 File Offset: 0x00016522
		// (set) Token: 0x060000B1 RID: 177 RVA: 0x00018329 File Offset: 0x00016529
		public static string Station_Code { get; set; }

		// Token: 0x17000011 RID: 17
		// (get) Token: 0x060000B2 RID: 178 RVA: 0x00018331 File Offset: 0x00016531
		// (set) Token: 0x060000B3 RID: 179 RVA: 0x00018339 File Offset: 0x00016539
		public TabPage TMSI_User { get; set; }

		// Token: 0x17000012 RID: 18
		// (get) Token: 0x060000B4 RID: 180 RVA: 0x00018342 File Offset: 0x00016542
		// (set) Token: 0x060000B5 RID: 181 RVA: 0x0001834A File Offset: 0x0001654A
		public string PF { get; set; }

		// Token: 0x17000013 RID: 19
		// (get) Token: 0x060000B6 RID: 182 RVA: 0x00018353 File Offset: 0x00016553
		// (set) Token: 0x060000B7 RID: 183 RVA: 0x0001835B File Offset: 0x0001655B
		public string PAUSE_Image { get; set; }

		// Token: 0x060000B8 RID: 184 RVA: 0x00018364 File Offset: 0x00016564
		private void Btn_GridLoad_Click(object sender, EventArgs e)
		{
			try
			{
				bool flag = this.TB_LoadTrain.Text == "";
				if (flag)
				{
					Load_TrainGrid load_TrainGrid = new Load_TrainGrid();
					load_TrainGrid.ShowDialog();
					this.DGV_OT.DataSource = Main.Online_TrainsGV;
				}
				else
				{
					string text = this.TB_LoadTrain.Text;
					DataRow[] array = Main.Online_TrainsGV.Select("Train_No = '" + text + "'");
					bool flag2 = array.Length != 0;
					if (flag2)
					{
						string[] array2 = new string[5];
						array2[0] = "Train Number ";
						array2[1] = text;
						array2[2] = " already Exsist at SNo. ";
						int num = 3;
						object obj = array[0][0];
						array2[num] = ((obj != null) ? obj.ToString() : null);
						array2[4] = "  in Online Grid...";
						MessageBox.Show(string.Concat(array2));
						return;
					}
					Main.SelectedTrain = text;
					Online_Trains online_Trains = new Online_Trains();
					Main.Flag_LoadNewTrains = true;
					online_Trains.Load_NewTrain();
					Main.Flag_LoadNewTrains = false;
					Main.Grid_RefreshTime = 3;
					Main.Flag_TrainAdded = true;
				}
			}
			catch
			{
			}
			this.TB_LoadTrain.Text = "";
		}

		// Token: 0x060000B9 RID: 185 RVA: 0x00018484 File Offset: 0x00016684
		private void Btn_Refresh_Click(object sender, EventArgs e)
		{
			Main.Grid_RefreshTime = 0;
			Main.Online_TrainsGV.AcceptChanges();
			this.DGV_OT.DataSource = Main.Online_TrainsGV;
			this.DGV_OT.RefreshEdit();
			Main.Flag_Update_DGVOT = true;
		}

		// Token: 0x060000BA RID: 186 RVA: 0x000025C1 File Offset: 0x000007C1
		private void SS_Lbl_AnnState_Click(object sender, EventArgs e)
		{
		}

		// Token: 0x060000BB RID: 187 RVA: 0x000025C1 File Offset: 0x000007C1
		private void SS_Lbl_Ann_Click(object sender, EventArgs e)
		{
		}

		// Token: 0x060000BC RID: 188 RVA: 0x000184BB File Offset: 0x000166BB
		private void Trk_Volume_Scroll(object sender, EventArgs e)
		{
			Main.MPlayer.settings.volume = this.Trk_Volume.Value;
		}

		// Token: 0x060000BD RID: 189 RVA: 0x000184DC File Offset: 0x000166DC
		private void Btn_Pause_Click(object sender, EventArgs e)
		{
			bool flag_AnnStarted = Main.Flag_AnnStarted;
			if (flag_AnnStarted)
			{
				Main.Flag_TogglePause = !Main.Flag_TogglePause;
				bool flag_TogglePause = Main.Flag_TogglePause;
				if (flag_TogglePause)
				{
					Main.MPlayer.Ctlcontrols.pause();
					this.Btn_Pause.Image = Image.FromFile(Application.StartupPath + "\\Data\\Images\\Paused.png");
					this.SS_SysStatus.Text = "Announcment Paused...";
				}
				else
				{
					Main.MPlayer.Ctlcontrols.play();
					this.Btn_Pause.Image = Image.FromFile(Application.StartupPath + "\\Data\\Images\\Pause.png");
					this.SS_SysStatus.Text = "Announcment Playing...";
				}
			}
		}

		// Token: 0x060000BE RID: 190 RVA: 0x00018598 File Offset: 0x00016798
		private void Chk_Slogan_SelectedIndexChanged(object sender, EventArgs e)
		{
			Main.No_Slg = 0;
			for (int i = 0; i < this.Chk_Slogan.Items.Count; i++)
			{
				bool itemChecked = this.Chk_Slogan.GetItemChecked(i);
				if (itemChecked)
				{
					Main.No_Slg++;
				}
			}
			Main.Selected_Slogan = new string[Main.No_Slg];
			int num = 0;
			for (int j = 0; j < this.Chk_Slogan.Items.Count; j++)
			{
				try
				{
					bool itemChecked2 = this.Chk_Slogan.GetItemChecked(j);
					if (itemChecked2)
					{
						Main.Selected_Slogan[num] = this.Chk_Slogan.Items[j].ToString();
						num++;
					}
				}
				catch (Exception ex)
				{
				}
			}
		}

		// Token: 0x060000BF RID: 191 RVA: 0x00018670 File Offset: 0x00016870
		public void Chk_Adv_SelectedIndexChanged(object sender, EventArgs e)
		{
			Main.No_Adv = 0;
			for (int i = 0; i < this.Chk_Adv.Items.Count; i++)
			{
				bool itemChecked = this.Chk_Adv.GetItemChecked(i);
				if (itemChecked)
				{
					Main.No_Adv++;
				}
			}
			Main.Selected_Adv = new string[Main.No_Adv];
			int num = 0;
			for (int j = 0; j < this.Chk_Adv.Items.Count; j++)
			{
				bool itemChecked2 = this.Chk_Adv.GetItemChecked(j);
				if (itemChecked2)
				{
					Main.Selected_Adv[num] = this.Chk_Adv.Items[j].ToString();
					num++;
				}
			}
		}

		// Token: 0x060000C0 RID: 192 RVA: 0x00018730 File Offset: 0x00016930
		public Main()
		{
			this.InitializeComponent();
			Main.PATH_AUDIO = Application.StartupPath + "\\Data";
			Main.MPlayer = new AxWindowsMediaPlayer();
			Main.MPlayer.Size = new Size(0, 0);
			base.Controls.Add(Main.MPlayer);
			Main.MPlayer.CreateControl();
			Main.MPlayer.PlayStateChange += this.MPlayer_StateChanged;
			Main.PlayList = new string[1000];
			Main.MediaCount = 0;
			Main.CurrentMedia = 0;
			DataTable dataTable = new DataTable();
			dataTable = this.DB.Read_Database("SELECT * FROM User_Details WHERE User_Name =  '" + Login.User_Name + "'");
			bool flag = dataTable.Rows.Count > 0;
			if (flag)
			{
				bool flag2 = dataTable.Rows[0]["Chk_Adver"].ToString() == "True";
				if (flag2)
				{
					this.TSMI_Adver.Enabled = true;
				}
				else
				{
					this.TSMI_Adver.Enabled = false;
				}
				bool flag3 = dataTable.Rows[0]["Chk_User"].ToString() == "True";
				if (flag3)
				{
					this.TSMI_User.Enabled = true;
				}
				else
				{
					this.TSMI_User.Enabled = false;
				}
				bool flag4 = dataTable.Rows[0]["Chk_StnC"].ToString() == "True";
				if (flag4)
				{
					this.TSMI_StnC.Enabled = true;
				}
				else
				{
					this.TSMI_StnC.Enabled = false;
				}
				bool flag5 = dataTable.Rows[0]["Chk_TrnD"].ToString() == "True";
				if (flag5)
				{
					this.TSMI_TrnD.Enabled = true;
				}
				else
				{
					this.TSMI_TrnD.Enabled = false;
				}
				bool flag6 = dataTable.Rows[0]["Chk_StnDet"].ToString() == "True";
				if (flag6)
				{
					this.TSMI_StnDet.Enabled = true;
				}
				else
				{
					this.TSMI_StnDet.Enabled = false;
				}
				bool flag7 = dataTable.Rows[0]["Chk_Rep"].ToString() == "True";
				if (flag7)
				{
					this.TSMI_Rep.Enabled = true;
				}
				else
				{
					this.TSMI_Rep.Enabled = false;
				}
			}
		}

		// Token: 0x060000C1 RID: 193 RVA: 0x000189DC File Offset: 0x00016BDC
		private void MPlayer_StateChanged(object sender, _WMPOCXEvents_PlayStateChangeEvent e)
		{
			switch (e.newState)
			{
			case 3:
				Main.FalseCnt = 0;
				Main.Flag_Play = false;
				break;
			case 8:
			{
				Main.PlayCount++;
				bool flag = Main.PlayCount < Main.MediaCount;
				if (flag)
				{
					while (Main.PlayList[Main.PlayCount] == null)
					{
						Main.PlayCount++;
						bool flag2 = Main.PlayCount > Main.MediaCount;
						if (flag2)
						{
							Main.MPlayer.Ctlcontrols.stop();
							return;
						}
					}
					while (!File.Exists(Main.PlayList[Main.PlayCount]))
					{
						Main.PlayCount++;
						bool flag3 = Main.PlayCount > Main.MediaCount;
						if (flag3)
						{
							Main.MPlayer.Ctlcontrols.stop();
							return;
						}
					}
					Main.MPlayer.URL = Main.PlayList[Main.PlayCount];
				}
				else
				{
					bool flag4 = Main.RepeatCount > 1;
					if (flag4)
					{
						Main.RepeatCount--;
						Main.PlayCount = 0;
						Main.MPlayer.URL = Main.PlayList[Main.PlayCount];
						Main.MPlayer.settings.volume = this.Trk_Volume.Value;
						Main.MPlayer.Ctlcontrols.play();
					}
					else
					{
						Main.Flag_StartAnnounc = false;
						this.Num_AnnRepeat.Enabled = true;
						Main.Flag_TogglePause = false;
						Main.Flag_AudioPlaying = false;
						this.SS_Lbl_AnnState.Text = "None";
						this.SS_SysStatus.Text = "Status : Announcment Complete...";
						Main.Flag_PlayingTrain = false;
						Main.Flag_CoachAnn = false;
						Main.MPlayer.Ctlcontrols.stop();
						this.Num_AnnRepeat.Value = 1m;
						Main.No_PlayTime = 0;
						Main.Replay_Time = 60;
						this.Uncheck_Ann();
					}
				}
				break;
			}
			case 9:
			{
				bool flag5 = Main.Flag_Play && Main.PlayCount > Main.MediaCount;
				if (flag5)
				{
					Main.Flag_StartAnnounc = false;
					this.Num_AnnRepeat.Enabled = true;
					Main.Flag_TogglePause = false;
					Main.Flag_AudioPlaying = false;
					this.SS_Lbl_AnnState.Text = "None";
					this.SS_SysStatus.Text = "Status : Announcment Complete...";
					Main.Flag_PlayingTrain = false;
					Main.Flag_CoachAnn = false;
					Main.MPlayer.Ctlcontrols.stop();
					this.Num_AnnRepeat.Value = 1m;
					Main.No_PlayTime = 0;
					Main.Replay_Time = 60;
				}
				else
				{
					Main.FalseCnt++;
					bool flag6 = Main.FalseCnt > 2;
					if (flag6)
					{
						Main.FalseCnt = 0;
						Main.PlayCount++;
						bool flag7 = Main.PlayCount < Main.MediaCount;
						if (flag7)
						{
							while (Main.PlayList[Main.PlayCount] == null)
							{
								Main.PlayCount++;
								bool flag8 = Main.PlayCount > Main.MediaCount;
								if (flag8)
								{
									Main.MPlayer.Ctlcontrols.stop();
									return;
								}
							}
							Main.MPlayer.URL = Main.PlayList[Main.PlayCount];
						}
					}
				}
				break;
			}
			case 10:
				try
				{
					Main.MPlayer.settings.volume = this.Trk_Volume.Value;
					Main.MPlayer.Ctlcontrols.play();
					Main.Flag_Play = true;
				}
				catch
				{
				}
				break;
			}
		}

		// Token: 0x060000C2 RID: 194 RVA: 0x00018DD0 File Offset: 0x00016FD0
		private void timer1_Tick(object sender, EventArgs e)
		{
			bool flag = Main.Replay_Time > 0;
			if (flag)
			{
				Main.Replay_Time--;
			}
			bool flag2 = DateTime.Now.Second == 0 && !Main.Flag_AudioPlaying && Main.Replay_Time == 0;
			if (flag2)
			{
				this.Btn_Play.PerformClick();
			}
			bool flag3 = Main.Init_Time > 0;
			if (flag3)
			{
				Main.Init_Time--;
			}
			bool flag4 = Main.No_PlayTime > 0;
			if (flag4)
			{
				Main.No_PlayTime--;
			}
			bool flag5 = Main.Ann_StartDelay > 0;
			if (flag5)
			{
				Main.Ann_StartDelay--;
			}
			bool flag6 = (!Main.Flag_AudioPlaying && !Main.Flag_StartAnnounc && Main.Flag_AnnStarted && this.Btn_Play.Text != "&PLAY" && Main.Ann_StartDelay == 0) || Main.No_PlayTime == 1;
			if (flag6)
			{
				Main.No_PlayTime = 0;
				Main.Replay_Time = 60;
				Main.Flag_AnnStarted = false;
				this.Btn_Play.Image = Image.FromFile(Application.StartupPath + "\\Data\\Images\\Play.png");
				this.Btn_Play.Text = "&PLAY";
				this.SS_SysStatus.Text = "Announcment Complete...";
				this.SS_Lbl_AnnState.Text = "None";
				this.Num_AnnRepeat.Enabled = true;
				this.Uncheck_Ann();
			}
			this.Lbl_Time.Text = DateTime.Now.ToString("dd-MMM-yyyy  HH:mm:ss");
			bool flag7 = Main.Grid_RefreshTime > 1;
			if (flag7)
			{
				Main.Grid_RefreshTime--;
			}
			bool flag8 = Main.Grid_RefreshTime == 1;
			if (flag8)
			{
				Main.Flag_Update_DGVOT = true;
			}
			bool flag9 = Main.Flag_Update_DGVOT && Main.Init_Time == 0;
			if (flag9)
			{
				bool flag10 = !this.DGV_OT.IsCurrentCellDirty;
				if (flag10)
				{
					Main.Grid_RefreshTime = 0;
					Main.Flag_Update_DGVOT = false;
					this.Update_OT_DGV();
				}
				else
				{
					Main.Grid_RefreshTime = 3;
				}
			}
		}

		// Token: 0x060000C3 RID: 195 RVA: 0x00018FCC File Offset: 0x000171CC
		private void DGV_OT_SelectionChanged(object sender, EventArgs e)
		{
			try
			{
				this.Del_Trains = this.DGV_OT.CurrentRow.Cells["OT_TrainNo"].Value.ToString();
				this.TB_LoadTrain.Text = this.Del_Trains;
			}
			catch
			{
			}
		}

		// Token: 0x060000C4 RID: 196 RVA: 0x000025C1 File Offset: 0x000007C1
		private void DGV_OT_CellValueChanged_1(object sender, DataGridViewCellEventArgs e)
		{
		}

		// Token: 0x060000C5 RID: 197 RVA: 0x00019030 File Offset: 0x00017230
		private void logoutToolStripMenuItem_Click_1(object sender, EventArgs e)
		{
			Misc_Functions misc_Functions = new Misc_Functions();
			misc_Functions.Write_Log("USER", null, Login.User_Name + "(" + Login.User_Type + ") Logged Out");
			Environment.Exit(0);
		}

		// Token: 0x060000C6 RID: 198 RVA: 0x00019074 File Offset: 0x00017274
		private void addAdvertisingToolStripMenuItem_Click(object sender, EventArgs e)
		{
			Advertising advertising = new Advertising();
			advertising.ShowDialog();
			DataTable dataTable = new DataTable();
			this.Chk_Adv.Items.Clear();
			this.Chk_Slogan.Items.Clear();
			dataTable = this.DB.Read_Database("SELECT * From Advertising");
			bool flag = dataTable.Rows.Count > 0;
			if (flag)
			{
				bool flag2 = false;
				for (int i = 0; i < dataTable.Rows.Count; i++)
				{
					bool flag3 = dataTable.Rows[i]["Msg_Enable"].ToString() == "Font";
					if (flag3)
					{
						flag2 = true;
					}
					else
					{
						bool flag4 = flag2;
						if (flag4)
						{
							int num = i - 1;
						}
						bool flag5 = dataTable.Rows[i]["Ann_Type"].ToString() == "Advertising";
						if (flag5)
						{
							this.Chk_Adv.Items.Add(dataTable.Rows[i]["Adver_Name"].ToString(), true);
						}
						else
						{
							bool flag6 = dataTable.Rows[i]["Ann_Type"].ToString() == "Slogans";
							if (flag6)
							{
								this.Chk_Slogan.Items.Add(dataTable.Rows[i]["Adver_Name"].ToString());
							}
						}
					}
				}
			}
		}

		// Token: 0x060000C7 RID: 199 RVA: 0x0001920C File Offset: 0x0001740C
		private void trainDataEntryToolStripMenuItem_Click_1(object sender, EventArgs e)
		{
			TrainData trainData = new TrainData();
			trainData.ShowDialog();
		}

		// Token: 0x060000C8 RID: 200 RVA: 0x00019228 File Offset: 0x00017428
		private void addStationCodeToolStripMenuItem_Click_1(object sender, EventArgs e)
		{
			Station_Code station_Code = new Station_Code();
			station_Code.ShowDialog();
		}

		// Token: 0x060000C9 RID: 201 RVA: 0x00019244 File Offset: 0x00017444
		private void addUserToolStripMenuItem_Click_1(object sender, EventArgs e)
		{
			User user = new User();
			user.ShowDialog();
		}

		// Token: 0x060000CA RID: 202 RVA: 0x00019260 File Offset: 0x00017460
		private void TSMI_StnDet_Click(object sender, EventArgs e)
		{
			Station_Details station_Details = new Station_Details();
			station_Details.ShowDialog();
		}

		// Token: 0x060000CB RID: 203 RVA: 0x0001927C File Offset: 0x0001747C
		private void reportsToolStripMenuItem_Click(object sender, EventArgs e)
		{
			Reports reports = new Reports();
			reports.ShowDialog();
		}

		// Token: 0x060000CC RID: 204 RVA: 0x00019298 File Offset: 0x00017498
		private void Main_Load(object sender, EventArgs e)
		{
			Main.Online_TrainsGV = new DataTable();
			Misc_Functions misc_Functions = new Misc_Functions();
			Class_Database class_Database = new Class_Database();
			base.Size = new Size(Screen.PrimaryScreen.WorkingArea.Width, Screen.PrimaryScreen.WorkingArea.Height);
			base.WindowState = FormWindowState.Maximized;
			this.MinimumSize = new Size(base.Width, base.Height);
			this.MaximumSize = this.MinimumSize;
			this.DGV_OT.Location = new Point(5, this.LB_Name.Location.Y + 30);
			this.DGV_OT.Size = new Size(base.Width - 180, base.Height - 100);
			this.LB_Name.Size = new Size(base.Width / 2, 30);
			this.LB_Name.Location = new Point(10, 25);
			this.Lbl_Time.Size = new Size(base.Width / 2, 30);
			this.Lbl_Time.Location = new Point(this.LB_Name.Size.Width, 25);
			this.Grp_AnnControl.Width = base.Width - this.DGV_OT.Width - 20;
			this.Grp_AnnControl.Height = base.Height / 4 - 10;
			this.Grp_AnnControl.Location = new Point(this.DGV_OT.Location.X + this.DGV_OT.Width + 5, this.LB_Name.Location.Y + 30);
			int num = (this.Grp_AnnControl.Width - 15) / 3;
			this.Btn_Stop.Width = (this.Btn_Play.Width = (this.Btn_Pause.Width = num));
			this.Btn_Stop.Location = new Point(5, this.Grp_AnnControl.Height / 3);
			this.Btn_Play.Location = new Point(num + 8, this.Grp_AnnControl.Height / 3);
			this.Btn_Pause.Location = new Point(num * 2 + 12, this.Grp_AnnControl.Height / 3);
			this.Trk_Volume.Location = new Point(5, this.Btn_Stop.Location.Y + this.Btn_Stop.Height + 30);
			this.Grp_GridControl.Width = this.Grp_AnnControl.Width;
			this.Grp_GridControl.Height = this.Grp_AnnControl.Height + 3;
			this.Grp_GridControl.Location = new Point(this.Grp_AnnControl.Location.X, this.Grp_AnnControl.Height + this.Grp_AnnControl.Location.Y + 16);
			this.TabControl_Aux.Size = new Size(this.Grp_AnnControl.Width, base.Height - this.TabControl_Aux.Location.Y);
			this.TabControl_Aux.Location = new Point(this.Grp_GridControl.Location.X, this.Grp_GridControl.Location.Y + this.Grp_GridControl.Height + 10);
			this.TabControl_Aux.TabPages.Remove(this.Tab_Adv);
			this.TabControl_Aux.ItemSize = new Size((this.TabControl_Aux.Size.Width - 5) / this.TabControl_Aux.TabCount, 20);
			misc_Functions.Load_PF();
			misc_Functions.Load_City();
			this.Load_StationDetails();
			this.Chk_Adv.Height = this.TabControl_Aux.Size.Height - 30;
			this.Chk_Slogan.Height = this.TabControl_Aux.Size.Height - 30;
			Online_Trains online_Trains = new Online_Trains();
			Main.Flag_TrainDGV = true;
			Main.TP_FileAccesstime = DateTime.Now;
			class_Database.Update_Database("SELECT Train_No,Train_NameEng,Train_AD,Train_Status,Sch_AT,Sch_DT,Late,Exp_AT,Exp_DT,Sch_PF,AN,Div_City FROM Online_Trains");
			this.DGV_OT.Columns["OT_SlNo"].DataPropertyName = "Sl_No";
			this.DGV_OT.Columns["OT_TrainNo"].DataPropertyName = "Train_No";
			this.DGV_OT.Columns["OT_TrainName"].DataPropertyName = "Train_NameEng";
			this.DGV_OT.Columns["OT_AD"].DataPropertyName = "Train_AD";
			this.DGV_OT.Columns["OT_TrainStatus"].DataPropertyName = "Train_Status";
			this.DGV_OT.Columns["OT_SchArr"].DataPropertyName = "Sch_AT";
			this.DGV_OT.Columns["OT_SchDep"].DataPropertyName = "Sch_DT";
			this.DGV_OT.Columns["OT_Late"].DataPropertyName = "Late";
			this.DGV_OT.Columns["OT_ExpArr"].DataPropertyName = "Exp_AT";
			this.DGV_OT.Columns["OT_ExpDep"].DataPropertyName = "Exp_DT";
			this.DGV_OT.Columns["OT_PF"].DataPropertyName = "Sch_PF";
			this.DGV_OT.Columns["OT_Ann"].DataPropertyName = "AN";
			this.DGV_OT.Columns["OT_City"].DataPropertyName = "Div_City";
			this.DGV_OT.Columns["OT_Del"].DataPropertyName = "Del_Train";
			this.DGV_OT.Columns["OT_SlNo"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
			this.DGV_OT.Columns["OT_TrainNo"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
			this.DGV_OT.Columns["OT_TrainName"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleLeft;
			this.DGV_OT.Columns["OT_AD"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
			this.DGV_OT.Columns["OT_TrainStatus"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleLeft;
			this.DGV_OT.Columns["OT_SchArr"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
			this.DGV_OT.Columns["OT_SchDep"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
			this.DGV_OT.Columns["OT_Late"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
			this.DGV_OT.Columns["OT_ExpArr"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
			this.DGV_OT.Columns["OT_ExpDep"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
			this.DGV_OT.Columns["OT_PF"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
			this.DGV_OT.Columns["OT_Ann"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
			this.DGV_OT.Columns["OT_City"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
			this.DGV_OT.Columns["OT_Del"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
			this.DGV_OT.Columns["OT_SlNo"].MinimumWidth = 55;
			this.DGV_OT.Columns["OT_TrainNo"].MinimumWidth = 80;
			this.DGV_OT.Columns["OT_AD"].MinimumWidth = 50;
			this.DGV_OT.Columns["OT_TrainStatus"].MinimumWidth = 200;
			this.DGV_OT.Columns["OT_SchArr"].MinimumWidth = 55;
			this.DGV_OT.Columns["OT_SchDep"].MinimumWidth = 55;
			this.DGV_OT.Columns["OT_Late"].MinimumWidth = 55;
			this.DGV_OT.Columns["OT_ExpArr"].MinimumWidth = 55;
			this.DGV_OT.Columns["OT_ExpDep"].MinimumWidth = 55;
			this.DGV_OT.Columns["OT_PF"].MinimumWidth = 60;
			this.DGV_OT.Columns["OT_Ann"].MinimumWidth = 35;
			this.DGV_OT.Columns["OT_City"].MinimumWidth = 75;
			this.DGV_OT.Columns["OT_Del"].MinimumWidth = 45;
			foreach (object obj in this.DGV_OT.Columns)
			{
				DataGridViewColumn dataGridViewColumn = (DataGridViewColumn)obj;
				dataGridViewColumn.HeaderCell.Style.Alignment = DataGridViewContentAlignment.MiddleCenter;
				dataGridViewColumn.HeaderCell.Style.Font = new Font("Arial", 20f, FontStyle.Regular, GraphicsUnit.Pixel);
				dataGridViewColumn.HeaderCell.Style.BackColor = Color.Yellow;
				dataGridViewColumn.HeaderCell.Style.ForeColor = Color.DarkBlue;
				this.DGV_OT.EnableHeadersVisualStyles = false;
			}
			DataGridViewComboBoxColumn dataGridViewComboBoxColumn = new DataGridViewComboBoxColumn();
			dataGridViewComboBoxColumn = (DataGridViewComboBoxColumn)this.DGV_OT.Columns["OT_PF"];
			dataGridViewComboBoxColumn.Items.Clear();
			dataGridViewComboBoxColumn.Items.Add("--");
			DataGridViewComboBoxCell.ObjectCollection items = dataGridViewComboBoxColumn.Items;
			object[] items2 = Main.PF_Names;
			items.AddRange(items2);
			dataGridViewComboBoxColumn.Items.Remove("N.A");
			DataGridViewComboBoxColumn dataGridViewComboBoxColumn2 = new DataGridViewComboBoxColumn();
			dataGridViewComboBoxColumn2 = (DataGridViewComboBoxColumn)this.DGV_OT.Columns["OT_City"];
			dataGridViewComboBoxColumn2.Items.Clear();
			dataGridViewComboBoxColumn2.Items.Add(" ");
			DataGridViewComboBoxCell.ObjectCollection items3 = dataGridViewComboBoxColumn2.Items;
			items2 = Main.City_Names;
			items3.AddRange(items2);
			this.DGV_OT.DataSource = online_Trains.Load_Trains();
			bool flag_AutoLoad = Main.Flag_AutoLoad;
			if (flag_AutoLoad)
			{
				online_Trains.AutoLoad_Trains();
			}
			this.DGV_OT.Visible = true;
			this.DGV_OT.DataSource = Main.Online_TrainsGV;
			this.DGV_OT.Invalidate();
			Main.Flag_Update_DGVOT = true;
			DataTable dataTable = new DataTable();
			this.Chk_Adv.Items.Clear();
			this.Chk_Slogan.Items.Clear();
			dataTable = class_Database.Read_Database("SELECT * From Advertising WHERE Ann_Type = 'Advertising' ");
			Main.Selected_Adv = new string[dataTable.Rows.Count];
			dataTable = class_Database.Read_Database("SELECT * From Advertising");
			Main.No_Adv = 0;
			bool flag = dataTable.Rows.Count > 0;
			if (flag)
			{
				bool flag2 = false;
				for (int i = 0; i < dataTable.Rows.Count; i++)
				{
					bool flag3 = dataTable.Rows[i]["Msg_Enable"].ToString() == "Font";
					if (flag3)
					{
						flag2 = true;
					}
					else
					{
						bool flag4 = flag2;
						if (flag4)
						{
							int num2 = i - 1;
						}
						bool flag5 = dataTable.Rows[i]["Ann_Type"].ToString() == "Advertising";
						if (flag5)
						{
							this.Chk_Adv.Items.Add(dataTable.Rows[i]["Adver_Name"].ToString(), true);
						}
						else
						{
							bool flag6 = dataTable.Rows[i]["Ann_Type"].ToString() == "Slogans";
							if (flag6)
							{
								this.Chk_Slogan.Items.Add(dataTable.Rows[i]["Adver_Name"].ToString());
							}
						}
					}
				}
			}
		}

		// Token: 0x060000CD RID: 205 RVA: 0x00019FA4 File Offset: 0x000181A4
		private void Load_StationDetails()
		{
			Class_Database class_Database = new Class_Database();
			this.StnTable = new DataTable();
			this.StnTable = class_Database.Read_Database("Select * From Station_Details");
			bool flag = this.StnTable.Rows.Count > 0;
			if (flag)
			{
				bool flag2 = this.StnTable.Rows[0]["Auto_Load"].ToString() == "True";
				if (flag2)
				{
					Main.Flag_AutoLoad = true;
					Main.AutoLoad_Interval = int.Parse(this.StnTable.Rows[0]["AutoLoad_Interval"].ToString());
					Main.AutoLoad_Time = int.Parse(this.StnTable.Rows[0]["AutoLoad_Interval"].ToString()) * 60;
				}
				bool flag3 = this.StnTable.Rows[0]["Auto_Delete"].ToString() == "True";
				if (flag3)
				{
					try
					{
						Main.Flag_AutoDelete = true;
						Main.AutoDelete_Interval = int.Parse(this.StnTable.Rows[0]["AutoDelete_Interval"].ToString());
						Main.AutoDeletePost_Interval = int.Parse(this.StnTable.Rows[0]["AutoDeletePost_Interval"].ToString());
						Main.AutoLoad_Time = int.Parse(this.StnTable.Rows[0]["Auto_Time"].ToString()) * 60;
					}
					catch
					{
					}
				}
				this.Language_Count = 0;
				bool flag4 = this.StnTable.Rows[0]["English"].ToString() == "True";
				if (flag4)
				{
					Main.Disp_Language[this.Language_Count] = "English";
					int language_Count = this.Language_Count;
					this.Language_Count = language_Count + 1;
				}
				bool flag5 = this.StnTable.Rows[0]["Hindi"].ToString() == "True";
				if (flag5)
				{
					Main.Disp_Language[this.Language_Count] = "Hindi";
					int language_Count = this.Language_Count;
					this.Language_Count = language_Count + 1;
				}
			}
		}

		// Token: 0x060000CE RID: 206 RVA: 0x0001A208 File Offset: 0x00018408
		private void Btn_Load_Click(object sender, EventArgs e)
		{
			Load_TrainGrid load_TrainGrid = new Load_TrainGrid();
			load_TrainGrid.ShowDialog();
		}

		// Token: 0x060000CF RID: 207 RVA: 0x0001A224 File Offset: 0x00018424
		private void DGV_OT_DataError(object sender, DataGridViewDataErrorEventArgs e)
		{
			DataGridViewComboBoxCell dataGridViewComboBoxCell = (DataGridViewComboBoxCell)this.DGV_OT.Rows[e.RowIndex].Cells["OT_TrainStatus"];
			string value = this.DGV_OT.Rows[e.RowIndex].Cells["OT_TrainStatus"].Value.ToString();
			dataGridViewComboBoxCell.Items.Clear();
			bool flag = this.DGV_OT.Rows[e.RowIndex].Cells["OT_AD"].Value.ToString() == "D";
			if (flag)
			{
				DataGridViewComboBoxCell.ObjectCollection items = dataGridViewComboBoxCell.Items;
				object[] items2 = Main.DepStatus;
				items.AddRange(items2);
			}
			else
			{
				DataGridViewComboBoxCell.ObjectCollection items3 = dataGridViewComboBoxCell.Items;
				object[] items2 = Main.ArrStatus;
				items3.AddRange(items2);
			}
			dataGridViewComboBoxCell.Value = value;
		}

		// Token: 0x060000D0 RID: 208 RVA: 0x0001A308 File Offset: 0x00018508
		private void DGV_OT_CellClick(object sender, DataGridViewCellEventArgs e)
		{
			bool flag = e.ColumnIndex == 12 || e.ColumnIndex == 4;
			if (flag)
			{
				Main.Flag_Update_DGVOT = false;
			}
		}

		// Token: 0x060000D1 RID: 209 RVA: 0x0001A338 File Offset: 0x00018538
		private void DGV_OT_CellContentClick(object sender, DataGridViewCellEventArgs e)
		{
			DataTable dataTable = new DataTable();
			bool flag = e.RowIndex == -1;
			if (!flag)
			{
				try
				{
					Online_Trains online_Trains = new Online_Trains();
					int columnIndex = e.ColumnIndex;
					int num = columnIndex;
					if (num != 11)
					{
						if (num == 13)
						{
							bool flag2 = this.DGV_OT.Rows.Count == 1;
							if (flag2)
							{
								MessageBox.Show("All Trains from Grid cannot be deleted...");
							}
							else
							{
								bool flag3 = this.DGV_OT.Rows.Count > 1;
								if (flag3)
								{
									this.Delete_GridTrain(e);
									Main.Grid_RefreshTime = 0;
								}
								Main.Online_TrainsGV.AcceptChanges();
								this.DGV_OT.DataSource = Main.Online_TrainsGV;
								this.DGV_OT.RefreshEdit();
								Main.Flag_Update_DGVOT = true;
							}
						}
					}
					else
					{
						this.DGV_OT.CommitEdit(DataGridViewDataErrorContexts.Commit);
					}
				}
				catch (Exception ex)
				{
				}
			}
		}

		// Token: 0x060000D2 RID: 210 RVA: 0x0001A434 File Offset: 0x00018634
		private void DGV_OT_CellValueChanged(object sender, DataGridViewCellEventArgs e)
		{
			bool flag = e.RowIndex > -1;
			if (flag)
			{
				this.DGV_OT.CellValueChanged -= this.DGV_OT_CellValueChanged;
				this.DGV_OT.CellContentClick -= this.DGV_OT_CellContentClick;
				string text = this.DGV_OT.Rows[e.RowIndex].Cells["OT_TrainNo"].Value.ToString();
				int columnIndex = e.ColumnIndex;
				int num = columnIndex;
				if (num <= 4)
				{
					if (num != 3)
					{
						if (num == 4)
						{
							bool flag2 = this.DGV_OT.Rows[e.RowIndex].Cells["OT_TrainStatus"].Value.ToString() == "RUNNING LATE" || this.DGV_OT.Rows[e.RowIndex].Cells["OT_TrainStatus"].Value.ToString() == "RESCHEDULED";
							if (flag2)
							{
								this.DGV_OT.Columns["OT_Late"].ReadOnly = true;
								this.DGV_OT.Rows[e.RowIndex].Cells["OT_Late"].ReadOnly = false;
								this.DGV_OT.Rows[e.RowIndex].Cells["OT_Late"].Value = "00:00";
							}
							else
							{
								this.DGV_OT.Columns["OT_Late"].ReadOnly = true;
								this.DGV_OT.Rows[e.RowIndex].Cells["OT_Late"].ReadOnly = true;
								bool flag3 = this.DGV_OT.Rows[e.RowIndex].Cells["OT_Late"].Value != null && this.DGV_OT.Rows[e.RowIndex].Cells["OT_Late"].Value.ToString() != "" && this.DGV_OT.Rows[e.RowIndex].Cells["OT_Late"].Value.ToString() != "00:00";
								if (flag3)
								{
									string text2 = this.DGV_OT.Rows[e.RowIndex].Cells["OT_Late"].Value.ToString();
									string str = "Maintain Late Time ";
									object value = this.DGV_OT.Rows[e.RowIndex].Cells["OT_Late"].Value;
									DialogResult dialogResult = MessageBox.Show(str + ((value != null) ? value.ToString() : null) + " Hour", "!!WARNING!!", MessageBoxButtons.YesNo);
									bool flag4 = dialogResult == DialogResult.No;
									if (flag4)
									{
										this.DGV_OT.Rows[e.RowIndex].Cells["OT_Late"].Value = "";
										this.DGV_OT.Rows[e.RowIndex].Cells["OT_ExpArr"].Value = this.DGV_OT.Rows[e.RowIndex].Cells["OT_SchArr"].Value;
										this.DGV_OT.Rows[e.RowIndex].Cells["OT_ExpDep"].Value = this.DGV_OT.Rows[e.RowIndex].Cells["OT_SchDep"].Value;
									}
								}
							}
							bool flag5 = this.DGV_OT.Rows[e.RowIndex].Cells["OT_TrainStatus"].Value.ToString() == "TERMINATED" || this.DGV_OT.Rows[e.RowIndex].Cells["OT_TrainStatus"].Value.ToString() == "DIVERTED";
							if (flag5)
							{
								this.DGV_OT.Columns["OT_City"].Visible = true;
								this.DGV_OT.Rows[e.RowIndex].Cells["OT_City"].ReadOnly = false;
							}
							else
							{
								this.DGV_OT.Rows[e.RowIndex].Cells["OT_City"].Value = " ";
							}
							this.DGV_OT.InvalidateCell(e.ColumnIndex + 4, e.RowIndex);
							Main.Flag_TADDBPrompt = true;
							this.DB.Insert_Database(string.Concat(new string[]
							{
								"UPDATE Online_Trains SET Train_Status= '",
								this.DGV_OT.Rows[e.RowIndex].Cells["OT_TrainStatus"].Value.ToString(),
								"' WHERE Train_No = '",
								text,
								"' "
							}));
						}
					}
					else
					{
						DataGridViewComboBoxCell dataGridViewComboBoxCell = (DataGridViewComboBoxCell)this.DGV_OT.Rows[e.RowIndex].Cells["OT_TrainStatus"];
						dataGridViewComboBoxCell.Items.Clear();
						bool flag6 = this.DGV_OT.Rows[e.RowIndex].Cells["OT_AD"].Value.ToString() == "D";
						if (flag6)
						{
							DataGridViewComboBoxCell.ObjectCollection items = dataGridViewComboBoxCell.Items;
							object[] items2 = Main.DepStatus;
							items.AddRange(items2);
						}
						else
						{
							DataGridViewComboBoxCell.ObjectCollection items3 = dataGridViewComboBoxCell.Items;
							object[] items2 = Main.ArrStatus;
							items3.AddRange(items2);
						}
						dataGridViewComboBoxCell.Value = "RUNNING RIGHT TIME";
						Main.Flag_TADDBPrompt = true;
						this.DB.Insert_Database(string.Concat(new string[]
						{
							"UPDATE Online_Trains SET Train_AD = '",
							this.DGV_OT.Rows[e.RowIndex].Cells["OT_AD"].Value.ToString(),
							"', Train_Status= 'RUNNING RIGHT TIME' WHERE Train_No = '",
							text,
							"' "
						}));
					}
				}
				else if (num != 10)
				{
					if (num == 11)
					{
						this.DB.Insert_Database(string.Concat(new string[]
						{
							"UPDATE Online_Trains SET AN = '",
							this.DGV_OT.Rows[e.RowIndex].Cells["OT_Ann"].Value.ToString(),
							"' WHERE Train_No = '",
							text,
							"' "
						}));
					}
				}
				else
				{
					Main.Flag_TADDBPrompt = true;
					this.DB.Insert_Database(string.Concat(new string[]
					{
						"UPDATE Online_Trains SET Sch_PF= '",
						this.DGV_OT.Rows[e.RowIndex].Cells["OT_PF"].Value.ToString(),
						"' WHERE Train_No = '",
						text,
						"' "
					}));
				}
				bool flag7 = e.ColumnIndex != 7;
				if (flag7)
				{
					this.DGV_OT.RefreshEdit();
					this.DGV_OT.Refresh();
					this.DGV_OT.Update();
				}
				this.DGV_OT.CellValueChanged += this.DGV_OT_CellValueChanged;
				this.DGV_OT.CellContentClick += this.DGV_OT_CellContentClick;
			}
		}

		// Token: 0x060000D3 RID: 211 RVA: 0x0001AC2C File Offset: 0x00018E2C
		public void Update_OT_DGV()
		{
			this.DGV_OT.CellValueChanged -= this.DGV_OT_CellValueChanged;
			this.DGV_OT.DataSource = Main.Online_TrainsGV;
			DataGridViewComboBoxColumn dataGridViewComboBoxColumn = new DataGridViewComboBoxColumn();
			dataGridViewComboBoxColumn = (DataGridViewComboBoxColumn)this.DGV_OT.Columns["OT_TrainStatus"];
			dataGridViewComboBoxColumn.Items.Clear();
			this.DGV_OT.Columns["OT_City"].Visible = false;
			this.DGV_OT.Columns["OT_City"].ReadOnly = true;
			try
			{
				for (int i = 0; i < this.DGV_OT.Rows.Count; i++)
				{
					bool flag = i % 2 == 0;
					if (flag)
					{
						this.DGV_OT.Rows[i].DefaultCellStyle.BackColor = Color.White;
					}
					else
					{
						this.DGV_OT.Rows[i].DefaultCellStyle.BackColor = Color.LightGray;
					}
					this.DGV_OT.Rows[i].Cells["OT_SlNo"].Value = i + 1;
					DataGridViewComboBoxCell dataGridViewComboBoxCell = (DataGridViewComboBoxCell)this.DGV_OT.Rows[i].Cells["OT_TrainStatus"];
					dataGridViewComboBoxCell.Items.Clear();
					bool flag2 = this.DGV_OT.Rows[i].Cells["OT_AD"].Value.ToString() == "D";
					if (flag2)
					{
						DataGridViewComboBoxCell.ObjectCollection items = dataGridViewComboBoxCell.Items;
						object[] items2 = Main.DepStatus;
						items.AddRange(items2);
					}
					else
					{
						DataGridViewComboBoxCell.ObjectCollection items3 = dataGridViewComboBoxCell.Items;
						object[] items2 = Main.ArrStatus;
						items3.AddRange(items2);
					}
					bool flag3 = this.DGV_OT.Rows[i].Cells["OT_TrainStatus"].Value.ToString() == "RUNNING LATE" || this.DGV_OT.Rows[i].Cells["OT_TrainStatus"].Value.ToString() == "RESCHEDULED";
					if (flag3)
					{
						this.DGV_OT.Rows[i].Cells["OT_Late"].ReadOnly = false;
					}
					else
					{
						this.DGV_OT.Rows[i].Cells["OT_Late"].ReadOnly = true;
					}
					bool flag4 = !this.DGV_OT.Columns["OT_City"].Visible;
					if (flag4)
					{
						bool flag5 = this.DGV_OT.Rows[i].Cells["OT_TrainStatus"].Value.ToString() == "TERMINATED" || this.DGV_OT.Rows[i].Cells["OT_TrainStatus"].Value.ToString() == "DIVERTED";
						if (flag5)
						{
							this.DGV_OT.Columns["OT_City"].Visible = true;
							this.DGV_OT.Rows[i].Cells["OT_City"].ReadOnly = false;
						}
						else
						{
							this.DGV_OT.Rows[i].Cells["OT_City"].Value = " ";
						}
					}
					this.DGV_OT.Rows[i].Cells[13].Style.BackColor = Color.Red;
				}
			}
			catch
			{
			}
			this.DGV_OT.RefreshEdit();
			this.DGV_OT.CellValueChanged += this.DGV_OT_CellValueChanged;
		}

		// Token: 0x060000D4 RID: 212 RVA: 0x0001B050 File Offset: 0x00019250
		public void Delete_GridTrain(DataGridViewCellEventArgs e)
		{
			Main.Flag_DeleteTrain = true;
			string str = this.DGV_OT.Rows[e.RowIndex].Cells["OT_TrainNo"].Value.ToString();
			DialogResult dialogResult = MessageBox.Show("Are Your Sure You Want To Delete Train " + str + " From Grid", "Delete Train", MessageBoxButtons.YesNo);
			bool flag = dialogResult == DialogResult.No;
			if (!flag)
			{
				this.DB.Insert_Database("DELETE * From Online_Trains where Train_No = '" + str + "'");
				try
				{
					this.DGV_OT.Rows.RemoveAt(e.RowIndex);
				}
				catch
				{
				}
				Main.Online_TrainsGV.AcceptChanges();
				this.DGV_OT.DataSource = Main.Online_TrainsGV;
				this.DGV_OT.RefreshEdit();
				Main.Flag_Update_DGVOT = true;
			}
		}

		// Token: 0x060000D5 RID: 213 RVA: 0x0001B138 File Offset: 0x00019338
		private void DGV_OT_CellEndEdit(object sender, DataGridViewCellEventArgs e)
		{
			bool flag = e.RowIndex == -1;
			if (!flag)
			{
				try
				{
					string text = this.DGV_OT.Rows[e.RowIndex].Cells["OT_TrainNo"].Value.ToString();
					int columnIndex = e.ColumnIndex;
					int num = columnIndex;
					if (num != 7)
					{
						if (num == 11)
						{
							this.DB.Insert_Database(string.Concat(new string[]
							{
								"UPDATE Online_Trains SET AN = '",
								this.DGV_OT.Rows[e.RowIndex].Cells["OT_Ann"].Value.ToString(),
								"' WHERE Train_No = '",
								text,
								"' "
							}));
						}
					}
					else
					{
						Online_Trains online_Trains = new Online_Trains();
						string text2 = "";
						DateTime dateTime = DateTime.ParseExact("00:00", "HH:mm", CultureInfo.CurrentCulture);
						DateTime dateTime2 = DateTime.ParseExact("00:00", "HH:mm", CultureInfo.CurrentCulture);
						DateTime dateTime3 = DateTime.ParseExact("00:00", "HH:mm", CultureInfo.CurrentCulture);
						DateTime dateTime4 = DateTime.ParseExact("00:00", "HH:mm", CultureInfo.CurrentCulture);
						bool flag2 = e.RowIndex > -1 && e.ColumnIndex == 7;
						if (flag2)
						{
							try
							{
								dateTime = DateTime.ParseExact(this.DGV_OT.Rows[e.RowIndex].Cells["OT_SchArr"].Value.ToString(), "HH:mm", CultureInfo.CurrentCulture);
								dateTime2 = DateTime.ParseExact(this.DGV_OT.Rows[e.RowIndex].Cells["OT_SchDep"].Value.ToString(), "HH:mm", CultureInfo.CurrentCulture);
								dateTime3 = DateTime.ParseExact(this.DGV_OT.Rows[e.RowIndex].Cells["OT_ExpArr"].Value.ToString(), "HH:mm", CultureInfo.CurrentCulture);
								dateTime4 = DateTime.ParseExact(this.DGV_OT.Rows[e.RowIndex].Cells["OT_ExpDep"].Value.ToString(), "HH:mm", CultureInfo.CurrentCulture);
							}
							catch
							{
							}
							bool flag3 = this.DGV_OT.Rows[e.RowIndex].Cells["OT_LATE"].Value != null && this.DGV_OT.Rows[e.RowIndex].Cells["OT_LATE"].Value.ToString().Length > 0;
							if (flag3)
							{
								text2 = this.DGV_OT.Rows[e.RowIndex].Cells["OT_LATE"].Value.ToString();
								bool flag4 = text2.Length < 5;
								if (flag4)
								{
									bool flag5 = text2.Length == 0;
									if (flag5)
									{
										return;
									}
									bool flag6 = text2.Length == 1;
									if (flag6)
									{
										text2 = "000" + text2;
									}
									bool flag7 = text2.Length == 2;
									if (flag7)
									{
										text2 = "00" + text2;
									}
									bool flag8 = text2.Length == 3;
									if (flag8)
									{
										text2 = "0" + text2;
									}
								}
								else
								{
									bool flag9 = text2.Length == 5;
									if (!flag9)
									{
										MessageBox.Show("Input Late Time in HHMM Format");
										this.DGV_OT.Rows[e.RowIndex].Cells["OT_Late"].Value = "";
										return;
									}
									bool flag10 = text2.Contains(':');
									if (!flag10)
									{
										MessageBox.Show("Input Late Time in HHMM Format");
										this.DGV_OT.Rows[e.RowIndex].Cells["OT_Late"].Value = "";
										return;
									}
									text2 = text2.Substring(0, 2) + text2.Substring(3, 2);
								}
							}
							DateTime dateTime5 = DateTime.ParseExact("0000", "HHmm", CultureInfo.CurrentCulture);
							bool flag11 = text2 == "";
							if (flag11)
							{
								dateTime5 = DateTime.ParseExact("0000", "HHmm", CultureInfo.CurrentCulture);
							}
							else
							{
								try
								{
									dateTime5 = DateTime.ParseExact(text2, "HHmm", CultureInfo.CurrentCulture);
									this.DGV_OT.Rows[e.RowIndex].Cells["OT_Late"].Value = dateTime5.ToString("HH:mm");
								}
								catch
								{
									MessageBox.Show("Input Time Not in HHMM Format");
									this.DGV_OT.Rows[e.RowIndex].Cells["OT_Late"].Value = "";
									return;
								}
							}
							try
							{
								dateTime3 = dateTime.Add(dateTime5.TimeOfDay);
								this.DGV_OT.Rows[e.RowIndex].Cells["OT_ExpArr"].Value = dateTime3.ToString("HH:mm");
								Main.Online_TrainsGV.Rows[e.RowIndex]["Exp_AT"] = dateTime3.ToString("HH:mm");
								dateTime4 = dateTime2.Add(dateTime5.TimeOfDay);
								this.DGV_OT.Rows[e.RowIndex].Cells["OT_ExpDep"].Value = dateTime4.ToString("HH:mm");
								Main.Online_TrainsGV.Rows[e.RowIndex]["Exp_DT"] = dateTime4.ToString("HH:mm");
							}
							catch
							{
							}
							this.DB.Insert_Database(string.Concat(new string[]
							{
								"UPDATE Online_Trains SET Late = '",
								dateTime5.ToString("HH:mm"),
								"',Exp_AT = '",
								dateTime3.ToString("HH:mm"),
								"', Exp_DT = '",
								dateTime4.ToString("HH:mm"),
								"'  WHERE Train_No = '",
								text,
								"' "
							}));
							Main.Flag_TADDBPrompt = true;
							this.DGV_OT.RefreshEdit();
							this.DGV_OT.Refresh();
							this.DGV_OT.Update();
						}
					}
				}
				catch
				{
				}
			}
		}

		// Token: 0x17000014 RID: 20
		// (get) Token: 0x060000D6 RID: 214 RVA: 0x0001B858 File Offset: 0x00019A58
		// (set) Token: 0x060000D7 RID: 215 RVA: 0x0001B85F File Offset: 0x00019A5F
		public static DateTime TP_FileAccesstime { get; set; }

		// Token: 0x060000D8 RID: 216 RVA: 0x0001B868 File Offset: 0x00019A68
		private void Btn_Play_Click_1(object sender, EventArgs e)
		{
			bool flag_AudioPlaying = Main.Flag_AudioPlaying;
			if (!flag_AudioPlaying)
			{
				Main.RepeatCount = (int)this.Num_AnnRepeat.Value;
				Main.Ann_StartDelay = 5;
				this.Chk_Adv_SelectedIndexChanged(null, null);
				int num = 0;
				for (int i = 0; i < this.DGV_OT.Rows.Count; i++)
				{
					bool flag = this.DGV_OT.Rows[i].Cells["OT_Ann"].Value.ToString() == "True";
					if (flag)
					{
						num++;
						this.DB.Insert_Database(string.Concat(new string[]
						{
							"UPDATE Online_Trains SET AN = '",
							this.DGV_OT.Rows[i].Cells["OT_Ann"].Value.ToString(),
							"' WHERE Train_No = '",
							this.DGV_OT.Rows[i].Cells["OT_TrainNo"].Value.ToString(),
							"' "
						}));
					}
				}
				this.DB.Update_Database("SELECT Train_No,Train_NameEng,Train_AD,Train_Status,Sch_AT,Sch_DT,Late,Exp_AT,Exp_DT,Sch_PF,AN,Div_City FROM Online_Trains");
				bool flag2 = num > 0 || Main.No_Adv > 0 || Main.No_Slg > 0;
				if (flag2)
				{
					Annaouncment annaouncment = new Annaouncment();
					Main.Flag_StartAnnounc = true;
					Main.Flag_PAAnn = false;
					this.Num_AnnRepeat.Enabled = false;
					Main.Ann_StartDelay = 5;
					this.Btn_Play.Image = Image.FromFile(Application.StartupPath + "\\Data\\Images\\Playing.png");
					Main.Repeat_Time = int.Parse(this.Num_AnnRepeat.Text);
					annaouncment.Start_Announcement();
					Main.Flag_AudioPlaying = true;
					Main.Flag_TogglePause = false;
					this.Btn_Play.Text = "PLAYING";
					this.SS_SysStatus.Text = "Announcment Playing...";
					this.SS_Lbl_AnnState.Text = string.Concat(new string[]
					{
						"Playing ",
						num.ToString(),
						" Trains, ",
						Main.No_Adv.ToString(),
						" Announcments & ",
						Main.No_Slg.ToString(),
						" Slogans"
					});
				}
				else
				{
					this.SS_Lbl_AnnState.Text = "No Train, Slogan or Advertisement Selected to play";
				}
			}
		}

		// Token: 0x060000D9 RID: 217 RVA: 0x0001BAD0 File Offset: 0x00019CD0
		private void Btn_Stop_Click_1(object sender, EventArgs e)
		{
			this.Num_AnnRepeat.Value = 1m;
			Main.Flag_StartAnnounc = false;
			Main.Replay_Time = 60;
			Main.Flag_AnnStarted = false;
			this.Num_AnnRepeat.Enabled = true;
			this.Num_AnnRepeat.Value = 1m;
			Main.CurrentMedia = Main.MediaCount + 1;
			this.Btn_Play.Image = Image.FromFile(Application.StartupPath + "\\Data\\Images\\Play.png");
			this.Btn_Pause.Image = Image.FromFile(Application.StartupPath + "\\Data\\Images\\Pause.png");
			this.Btn_Play.Text = "&PLAY";
			this.Btn_Play.ForeColor = Color.Black;
			bool flag_AudioPlaying = Main.Flag_AudioPlaying;
			if (flag_AudioPlaying)
			{
				Main.MPlayer.Ctlcontrols.stop();
				Main.Flag_TogglePause = false;
				Main.Flag_AudioPlaying = false;
				this.SS_SysStatus.Text = "Announcment Stopped...";
				this.SS_Lbl_AnnState.Text = "None";
			}
		}

		// Token: 0x060000DA RID: 218 RVA: 0x0001BBD8 File Offset: 0x00019DD8
		public void Uncheck_Ann()
		{
			for (int i = 0; i < Main.Online_TrainsGV.Rows.Count; i++)
			{
				bool flag = Main.Online_TrainsGV.Rows[i]["AN"].ToString() != "False";
				if (flag)
				{
					Main.Online_TrainsGV.Rows[i]["AN"] = "False";
					this.DGV_OT.Rows[i].Cells["OT_Ann"].Value = false;
					Class_Database db = this.DB;
					string str = "UPDATE Online_Trains SET AN = 'False' WHERE Train_No = '";
					object obj = Main.Online_TrainsGV.Rows[i]["Train_No"];
					db.Insert_Database(str + ((obj != null) ? obj.ToString() : null) + "'");
				}
			}
			try
			{
				Directory.Delete(Main.PATH_AUDIO + "\\WAVE\\TEMP", true);
				Directory.CreateDirectory(Main.PATH_AUDIO + "\\WAVE\\TEMP");
			}
			catch
			{
			}
		}

		// Token: 0x17000015 RID: 21
		// (get) Token: 0x060000DB RID: 219 RVA: 0x0001BD14 File Offset: 0x00019F14
		// (set) Token: 0x060000DC RID: 220 RVA: 0x0001BD1C File Offset: 0x00019F1C
		public int Language_Count { get; set; }

		// Token: 0x060000DD RID: 221 RVA: 0x0001BD28 File Offset: 0x00019F28
		private void Load_PlayList()
		{
			DataTable dataTable = new DataTable();
			string text = "";
			Main.MediaCount = 0;
			for (int i = 0; i < this.LB_PlayList.Items.Count; i++)
			{
				bool flag = this.LB_PlayList.Items[i].ToString().Contains("TRAIN");
				if (flag)
				{
					string str = this.LB_PlayList.Items[i].ToString().Substring(8, 5);
					Annaouncment.Ann_Table = this.DB.Read_Database("SELECT Train_No,Train_NameEng,Train_AD,Exp_AT,Exp_DT,Late,Train_Status,Sch_PF,Div_City From Online_Trains Where Train_No = '" + str + "'");
					Main.Flag_PAAnn = true;
					Annaouncment annaouncment = new Annaouncment();
					annaouncment.Train_Announce();
					Main.Flag_PAAnn = false;
				}
				else
				{
					bool flag2 = this.LB_PlayList.Items[i].ToString().Contains("#");
					if (flag2)
					{
						string[] array = this.LB_PlayList.Items[i].ToString().Split(new char[]
						{
							'#'
						});
						dataTable = new DataTable();
						dataTable = this.DB.Read_Database("SELECT Wave_FileLoc FROM PA_Details WHERE File_Name = '" + array[0].ToString() + "'");
						Main.PlayList[Main.MediaCount] = Main.PATH_AUDIO + "\\WAVE\\" + dataTable.Rows[0][0].ToString().Substring(0, dataTable.Rows[0][0].ToString().Length - 4) + "-E.wav";
						Main.MediaCount++;
						dataTable = this.DB.Read_Database("SELECT Wave_FileLoc FROM PA_Details WHERE File_Name = '" + array[1].ToString() + "'");
						bool flag3 = dataTable.Rows.Count > 0;
						if (flag3)
						{
							Main.PlayList[Main.MediaCount] = Main.PATH_AUDIO + "\\WAVE\\" + dataTable.Rows[0][0].ToString().Substring(0, dataTable.Rows[0][0].ToString().Length - 4) + "-E.wav";
							Main.MediaCount++;
						}
						else
						{
							bool flag4 = array[1].Length == 5 && array.Length == 2;
							if (flag4)
							{
								Main.PlayList[Main.MediaCount] = Main.PATH_AUDIO + "\\WAVE\\ENGLISH\\PATO.wav";
								Main.MediaCount++;
								int j = 0;
								while (j < 5)
								{
									Main.PlayList[Main.MediaCount] = Main.PATH_AUDIO + "\\WAVE\\ENGLISH\\COUNT\\" + array[1].Substring(j, 1) + ".wav";
									j++;
									Main.MediaCount++;
								}
							}
						}
						try
						{
							bool flag5 = array[2].Length == 5;
							if (flag5)
							{
								Main.PlayList[Main.MediaCount] = Main.PATH_AUDIO + "\\WAVE\\ENGLISH\\ETRN.wav";
								Main.MediaCount++;
								int k = 0;
								while (k < 5)
								{
									Main.PlayList[Main.MediaCount] = Main.PATH_AUDIO + "\\WAVE\\ENGLISH\\COUNT\\" + array[2].Substring(k, 1) + ".wav";
									k++;
									Main.MediaCount++;
								}
							}
						}
						catch
						{
						}
						dataTable = this.DB.Read_Database("SELECT Wave_FileLoc FROM PA_Details WHERE File_Name = '" + array[0].ToString() + "'");
						Main.PlayList[Main.MediaCount] = Main.PATH_AUDIO + "\\WAVE\\" + dataTable.Rows[0][0].ToString().Substring(0, dataTable.Rows[0][0].ToString().Length - 4) + "-H.wav";
						Main.MediaCount++;
						dataTable = this.DB.Read_Database("SELECT Wave_FileLoc FROM PA_Details WHERE File_Name = '" + array[1].ToString() + "'");
						bool flag6 = dataTable.Rows.Count > 0;
						if (flag6)
						{
							Main.PlayList[Main.MediaCount] = Main.PATH_AUDIO + "\\WAVE\\" + dataTable.Rows[0][0].ToString().Substring(0, dataTable.Rows[0][0].ToString().Length - 4) + "-H.wav";
							Main.MediaCount++;
						}
						else
						{
							bool flag7 = array[1].Length == 5 && array.Length == 2;
							if (flag7)
							{
								Main.PlayList[Main.MediaCount] = Main.PATH_AUDIO + "\\WAVE\\HINDI\\HTRN.wav";
								Main.MediaCount++;
								int l = 0;
								while (l < 5)
								{
									Main.PlayList[Main.MediaCount] = Main.PATH_AUDIO + "\\WAVE\\HINDI\\COUNT\\" + array[1].Substring(l, 1) + ".wav";
									l++;
									Main.MediaCount++;
								}
								Main.PlayList[Main.MediaCount] = Main.PATH_AUDIO + "\\WAVE\\HINDI\\PATO.wav";
								Main.MediaCount++;
							}
						}
						try
						{
							bool flag8 = array[2].Length == 5;
							if (flag8)
							{
								Main.PlayList[Main.MediaCount] = Main.PATH_AUDIO + "\\WAVE\\HINDI\\HTRN.wav";
								Main.MediaCount++;
								int m = 0;
								while (m < 5)
								{
									Main.PlayList[Main.MediaCount] = Main.PATH_AUDIO + "\\WAVE\\HINDI\\COUNT\\" + array[2].Substring(m, 1) + ".wav";
									m++;
									Main.MediaCount++;
								}
							}
						}
						catch
						{
						}
						dataTable = this.DB.Read_Database("SELECT Wave_FileLoc FROM PA_Details WHERE File_Name = '" + array[0].ToString() + "'");
						Main.PlayList[Main.MediaCount] = Main.PATH_AUDIO + "\\WAVE\\" + dataTable.Rows[0][0].ToString().Substring(0, dataTable.Rows[0][0].ToString().Length - 4) + "-R.wav";
						Main.MediaCount++;
						dataTable = this.DB.Read_Database("SELECT Wave_FileLoc FROM PA_Details WHERE File_Name = '" + array[1].ToString() + "'");
						bool flag9 = dataTable.Rows.Count > 0;
						if (flag9)
						{
							Main.PlayList[Main.MediaCount] = Main.PATH_AUDIO + "\\WAVE\\" + dataTable.Rows[0][0].ToString().Substring(0, dataTable.Rows[0][0].ToString().Length - 4) + "-R.wav";
							Main.MediaCount++;
						}
						else
						{
							bool flag10 = array[1].Length == 5 && array.Length == 2;
							if (flag10)
							{
								Main.PlayList[Main.MediaCount] = Main.PATH_AUDIO + "\\WAVE\\REGIONAL\\RTRN.wav";
								Main.MediaCount++;
								int n = 0;
								while (n < 5)
								{
									Main.PlayList[Main.MediaCount] = Main.PATH_AUDIO + "\\WAVE\\REGIONAL\\COUNT\\" + array[1].Substring(n, 1) + ".wav";
									n++;
									Main.MediaCount++;
								}
								Main.PlayList[Main.MediaCount] = Main.PATH_AUDIO + "\\WAVE\\REGIONAL\\PATO.wav";
								Main.MediaCount++;
							}
						}
						try
						{
							bool flag11 = array[2].Length == 5;
							if (flag11)
							{
								Main.PlayList[Main.MediaCount] = Main.PATH_AUDIO + "\\WAVE\\REGIONAL\\RTRN.wav";
								Main.MediaCount++;
								int num = 0;
								while (num < 5)
								{
									Main.PlayList[Main.MediaCount] = Main.PATH_AUDIO + "\\WAVE\\REGIONAL\\COUNT\\" + array[2].Substring(num, 1) + ".wav";
									num++;
									Main.MediaCount++;
								}
							}
						}
						catch
						{
						}
						this.DB.Insert_Database(string.Concat(new string[]
						{
							"UPDATE PA_Details SET PlayList_Index = '",
							(i + 1).ToString(),
							"' WHERE File_Name = '",
							array[0].ToString(),
							"'"
						}));
						bool flag12 = array.Length >= 2;
						if (flag12)
						{
							this.DB.Insert_Database(string.Concat(new string[]
							{
								"UPDATE PA_Details SET PlayList_Index = '",
								(i + 1).ToString(),
								"' WHERE File_Name = '",
								array[1].ToString(),
								"'"
							}));
						}
						bool flag13 = array.Length >= 3;
						if (flag13)
						{
							this.DB.Insert_Database(string.Concat(new string[]
							{
								"UPDATE PA_Details SET PlayList_Index = '",
								(i + 1).ToString(),
								"' WHERE File_Name = '",
								array[2].ToString(),
								"'"
							}));
						}
						text = text + "," + this.LB_PlayList.Items[i].ToString();
					}
					else
					{
						dataTable = new DataTable();
						dataTable = this.DB.Read_Database("SELECT Wave_FileLoc FROM PA_Details WHERE File_Name = '" + this.LB_PlayList.Items[i].ToString() + "'");
						this.DB.Insert_Database(string.Concat(new string[]
						{
							"UPDATE PA_Details SET PlayList_Index = '",
							(i + 1).ToString(),
							"' WHERE File_Name = '",
							this.LB_PlayList.Items[i].ToString(),
							"'"
						}));
						Main.PlayList[Main.MediaCount] = Main.PATH_AUDIO + "\\WAVE\\" + dataTable.Rows[0][0].ToString();
						Main.MediaCount++;
						text = text + "," + this.LB_PlayList.Items[i].ToString();
					}
				}
				bool flag14 = Main.MediaCount > 0 && !Main.Flag_AudioPlaying;
				if (flag14)
				{
					Misc_Functions misc_Functions = new Misc_Functions();
					misc_Functions.Write_Log("ANN", null, text);
					Main.Flag_StartAnnounc = true;
				}
			}
		}

		// Token: 0x060000DE RID: 222 RVA: 0x0001C818 File Offset: 0x0001AA18
		private void TSM_JP_Click(object sender, EventArgs e)
		{
			About about = new About();
			about.ShowDialog();
		}

		// Token: 0x060000DF RID: 223 RVA: 0x0001C834 File Offset: 0x0001AA34
		private void Btn_GridClr_Click_1(object sender, EventArgs e)
		{
			Main.Flag_DeleteTrain = true;
			this.DB.Insert_Database("DELETE * FROM Online_Trains WHERE  AN = 'False'");
			for (int i = Main.Online_TrainsGV.Rows.Count; i > 0; i--)
			{
				try
				{
					bool flag = Main.Online_TrainsGV.Rows[i]["AN"].ToString() == "False" || Main.Online_TrainsGV.Rows[i]["AN"].ToString() == "";
					if (flag)
					{
						Main.Online_TrainsGV.Rows.RemoveAt(i);
					}
				}
				catch
				{
				}
			}
			try
			{
				Main.Online_TrainsGV.AcceptChanges();
				this.DGV_OT.DataSource = Main.Online_TrainsGV;
				this.DGV_OT.RefreshEdit();
				this.TB_LoadTrain.Text = "";
			}
			catch
			{
			}
		}

		// Token: 0x060000E0 RID: 224 RVA: 0x000025C1 File Offset: 0x000007C1
		private void TB_LoadTrain_TextChanged(object sender, EventArgs e)
		{
		}

		// Token: 0x0400010A RID: 266
		private Class_Database DB = new Class_Database();

		// Token: 0x0400010B RID: 267
		private DataTable StnTable;

		// Token: 0x0400010C RID: 268
		public static bool Flag_AutoLoad;

		// Token: 0x0400010D RID: 269
		public static bool Flag_LoadNewTrains;

		// Token: 0x0400010E RID: 270
		public static bool Flag_AutoDelete;

		// Token: 0x0400010F RID: 271
		public static bool Flag_LoadCancel;

		// Token: 0x04000110 RID: 272
		public static bool Flag_TrainAdded;

		// Token: 0x04000111 RID: 273
		public static bool Flag_DeleteTrain;

		// Token: 0x04000112 RID: 274
		public static bool Flag_TrainDGV = false;

		// Token: 0x04000113 RID: 275
		public static bool Flag_Play = false;

		// Token: 0x04000114 RID: 276
		public static int AutoLoad_Interval;

		// Token: 0x04000115 RID: 277
		public static int AutoDelete_Interval;

		// Token: 0x04000116 RID: 278
		public static int AutoDeletePost_Interval;

		// Token: 0x04000117 RID: 279
		public static int AutoLoad_Time;

		// Token: 0x04000118 RID: 280
		public static int Next_LoadTime;

		// Token: 0x04000119 RID: 281
		public static int Grid_RefreshTime;

		// Token: 0x0400011A RID: 282
		public static string[] Selected_Adv;

		// Token: 0x0400011B RID: 283
		public static string[] Selected_Slogan;

		// Token: 0x0400011C RID: 284
		public static int No_Slg = 0;

		// Token: 0x0400011D RID: 285
		public static int No_Adv = 0;

		// Token: 0x0400011E RID: 286
		public static int Init_Time = 5;

		// Token: 0x0400011F RID: 287
		public static int No_PlayTime;

		// Token: 0x04000120 RID: 288
		public static bool Flag_IntegrationSlav;

		// Token: 0x04000121 RID: 289
		public static DataTable DGV_Messages;

		// Token: 0x04000122 RID: 290
		public static object[] SelectedMessages;

		// Token: 0x04000123 RID: 291
		public string Del_Trains;

		// Token: 0x04000124 RID: 292
		public static AxWindowsMediaPlayer MPlayer;

		// Token: 0x04000125 RID: 293
		public static string[] PF_Names;

		// Token: 0x04000126 RID: 294
		public static string[] City_Names;

		// Token: 0x04000127 RID: 295
		public static Main Main_App;

		// Token: 0x04000128 RID: 296
		public static int MediaCount = 0;

		// Token: 0x04000129 RID: 297
		public static int FalseCnt = 0;

		// Token: 0x0400012A RID: 298
		public static int RepeatCount;

		// Token: 0x0400012B RID: 299
		public static int PlayCount = 0;

		// Token: 0x0400012C RID: 300
		public static int Ann_StartDelay;

		// Token: 0x0400012D RID: 301
		public static int Replay_Time = 0;

		// Token: 0x0400012E RID: 302
		public static bool Flag_Pause;

		// Token: 0x0400012F RID: 303
		public static bool Flag_CoachAnn;

		// Token: 0x04000130 RID: 304
		public static bool Flag_PlayingTrain;

		// Token: 0x04000131 RID: 305
		public static bool Flag_AnnStart;

		// Token: 0x04000132 RID: 306
		public static bool Flag_AnnStarted;

		// Token: 0x04000133 RID: 307
		public static bool Flag_AnnStopped;

		// Token: 0x04000134 RID: 308
		public long Rate = 8000L;

		// Token: 0x04000135 RID: 309
		public bool waveRecordingImmediate;

		// Token: 0x04000136 RID: 310
		public int Channels = 1;

		// Token: 0x04000137 RID: 311
		public string WaveMidiFileName;

		// Token: 0x04000138 RID: 312
		public int Resolution = 16;

		// Token: 0x04000139 RID: 313
		private int Rec_Result;

		// Token: 0x0400013A RID: 314
		public DateTime Rec_Time;

		// Token: 0x0400013B RID: 315
		public Process SoundPlayer;

		// Token: 0x0400013C RID: 316
		public const int SL_NO = 0;

		// Token: 0x0400013D RID: 317
		public const int TRAIN_NO = 1;

		// Token: 0x0400013E RID: 318
		public const int TRAIN_NAME = 2;

		// Token: 0x0400013F RID: 319
		public const int TRAIN_AD = 3;

		// Token: 0x04000140 RID: 320
		public const int TRAIN_STATUS = 4;

		// Token: 0x04000141 RID: 321
		public const int TRAIN_SAT = 5;

		// Token: 0x04000142 RID: 322
		public const int TRAIN_SDT = 6;

		// Token: 0x04000143 RID: 323
		public const int TRAIN_LATE = 7;

		// Token: 0x04000144 RID: 324
		public const int TRAIN_EAT = 8;

		// Token: 0x04000145 RID: 325
		public const int TRAIN_EDT = 9;

		// Token: 0x04000146 RID: 326
		public const int TRAIN_PF = 10;

		// Token: 0x04000147 RID: 327
		public const int TRAIN_ANN = 11;

		// Token: 0x04000148 RID: 328
		public const int DIV_CITY = 12;

		// Token: 0x04000149 RID: 329
		public const int TRAIN_DELETE = 13;

		// Token: 0x0400014A RID: 330
		public static int Avl_PF;

		// Token: 0x0400014B RID: 331
		public static bool Flag_TADDBPrompt;

		// Token: 0x0400014C RID: 332
		public static bool Flag_CGDBPrompt;

		// Token: 0x0400014D RID: 333
		public static bool Flag_RefCGSPrompt;

		// Token: 0x0400014E RID: 334
		public static bool Flag_Update_DGVOT;

		// Token: 0x0400014F RID: 335
		public static bool Flag_TrainExsist;

		// Token: 0x04000150 RID: 336
		public static bool Flag_RefreshGrid;

		// Token: 0x04000151 RID: 337
		public static DataTable Online_TrainsGV;

		// Token: 0x04000152 RID: 338
		public static string[] ArrStatus = new string[]
		{
			"IS ARRIVING ON",
			"HAS ARRIVED ON",
			"WILL ARRIVE SHORTLY",
			"RUNNING RIGHT TIME",
			"RUNNING LATE",
			"INDEFINITE LATE",
			"CANCELLED",
			"PLATFORM CHANGED",
			"TERMINATED"
		};

		// Token: 0x04000153 RID: 339
		public static string[] DepStatus = new string[]
		{
			"IS READY TO LEAVE",
			"IS ON PLATFORM",
			"HAS LEFT",
			"RUNNING RIGHT TIME",
			"RESCHEDULED",
			"CANCELLED",
			"PLATFORM CHANGED",
			"DIVERTED"
		};

		// Token: 0x04000154 RID: 340
		public static string SelectedTrain;

		// Token: 0x04000155 RID: 341
		public static bool Flag_DatabaseInUse = false;

		// Token: 0x04000164 RID: 356
		public static double LogDays = -90.0;

		// Token: 0x04000167 RID: 359
		public static string[] PlayList;

		// Token: 0x04000168 RID: 360
		public static int CurrentMedia;

		// Token: 0x04000169 RID: 361
		public static int Repeat_Time;

		// Token: 0x0400016A RID: 362
		public static int Repeat_Play;

		// Token: 0x0400016B RID: 363
		public static bool Flag_AudioPlaying;

		// Token: 0x0400016C RID: 364
		public static bool Flag_PAAnn;

		// Token: 0x0400016D RID: 365
		public static bool Flag_StartAnnounc;

		// Token: 0x0400016E RID: 366
		public static bool Flag_TogglePause;

		// Token: 0x0400016F RID: 367
		public static bool Flag_StopPA;

		// Token: 0x04000170 RID: 368
		public static bool Flag_Mute;

		// Token: 0x04000171 RID: 369
		public static bool Flag_IgnoreEdit;

		// Token: 0x04000172 RID: 370
		public static Thread Voice_Thread;

		// Token: 0x04000173 RID: 371
		public static Thread AnnStart_Thread;

		// Token: 0x04000174 RID: 372
		public static SoundPlayer Player = new SoundPlayer();

		// Token: 0x04000175 RID: 373
		public static string[] Disp_Language = new string[3];

		// Token: 0x04000176 RID: 374
		private ListBox LB_PlayList;
	}
}
