using System.Collections.Generic;
using System.Data;
using IPIS.Models;

namespace IPIS.Repositories.Interfaces
{
    public interface IRoleRepository
    {
        void AddRole(string name, string description, List<string> permissions);
        void UpdateRole(long roleId, string name, string description, List<string> permissions);
        void DeleteRole(long roleId);
        DataTable GetAllRoles();
        Role GetRoleById(long roleId);
        Role GetRoleByName(string name);
        List<string> GetRolePermissions(long roleId);
        bool RoleExists(string name);
        bool IsRoleInUse(long roleId);
    }
} 