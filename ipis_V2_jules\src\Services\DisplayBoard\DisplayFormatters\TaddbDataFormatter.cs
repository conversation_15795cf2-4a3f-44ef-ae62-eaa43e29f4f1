using System;
using System.Collections.Generic;
using System.Linq;
using ipis_V2_jules.ApiClients; // For TrainDataErail
using ipis_V2_jules.DisplayFormatters; // For IDisplayDataFormatter, FormattedDisplayData, SimpleFont

// Assuming DisplayBoardConfig keys: "Lines", "CharsPerLine", "BoardId"
// Assuming PlatformInfo keys: "Platform", "ETA", "StatusMessage"

namespace ipis_V2_jules.Services.DisplayBoard.DisplayFormatters
{
    public class TaddbDataFormatter : IDisplayDataFormatter
    {
        // TADDB (Text and Dot Display Board) are assumed to be bitmap-based, similar to MLDBs.
        // This implementation will mirror MldbDataFormatter, using SimpleFont.
        // If TADDBs have specific bitmap requirements or different character encodings,
        // this class will need to be adjusted.

        public FormattedDisplayData FormatTrainData(TrainDataErail trainInfo, Dictionary<string, string> platformInfo, Dictionary<string, string> boardConfig)
        {
            Console.WriteLine($"TADDB Formatter: Formatting Train Data for TrainNo: {trainInfo?.TrainNo ?? "N/A"}");

            if (trainInfo == null)
            {
                return FormatMessage("NO TRAIN DATA", boardConfig);
            }

            // TADDBs can be multi-line (e.g., 2 to 6 lines)
            int lines = boardConfig.TryGetValue("Lines", out string linesStr) && int.TryParse(linesStr, out int l) ? l : 2; // Default to 2 lines
            int charsPerLine = boardConfig.TryGetValue("CharsPerLine", out string charsStr) && int.TryParse(charsStr, out int cpl) ? cpl : 20;
            byte boardId = boardConfig.TryGetValue("BoardId", out string idStr) && byte.TryParse(idStr, out byte id) ? id : (byte)0x01;

            var formattedData = new FormattedDisplayData();

            // Example formatting for TADDB:
            // Line 1: Train No & Name
            // Line 2: Platform & Expected Time (ETA/ETD)
            // Line 3: Status (e.g., ARRIVED, LATE, ON TIME)
            // (Adjust based on typical TADDB layouts and number of lines)

            string line1Text = $"{trainInfo.TrainNo} {trainInfo.TrainName}".ToUpper();

            string platformStr = platformInfo.GetValueOrDefault("Platform", "N/A") ?? "N/A";
            // Determine if it's arrival or departure based on context (not directly available in TrainDataErail for intermediate stations)
            // For simplicity, let's assume platformInfo provides "ExpectedTime" and "TimeType" (ETA/ETD)
            string timeType = platformInfo.GetValueOrDefault("TimeType", "ETA") ?? "ETA";
            string expectedTime = platformInfo.GetValueOrDefault("ExpectedTime", trainInfo.StartTime) ?? "N/A";
            string statusMsg = platformInfo.GetValueOrDefault("StatusMessage", "ON TIME") ?? "ON TIME";

            string line2Text = $"PF {platformStr} {timeType} {expectedTime}".ToUpper();
            string line3Text = statusMsg.ToUpper();
            // Add more lines if boardConfig.Lines supports it

            formattedData.Line1 = ConvertTextToBitmapBytes(line1Text, charsPerLine);
            Console.WriteLine($"TADDB Formatter: Line 1 Text: \"{line1Text}\", Bytes: {formattedData.Line1?.Length ?? 0}");

            if (lines >= 2)
            {
                formattedData.Line2 = ConvertTextToBitmapBytes(line2Text, charsPerLine);
                Console.WriteLine($"TADDB Formatter: Line 2 Text: \"{line2Text}\", Bytes: {formattedData.Line2?.Length ?? 0}");
            }
            else { formattedData.Line2 = null; }

            if (lines >= 3)
            {
                formattedData.Line3 = ConvertTextToBitmapBytes(line3Text, charsPerLine);
                Console.WriteLine($"TADDB Formatter: Line 3 Text: \"{line3Text}\", Bytes: {formattedData.Line3?.Length ?? 0}");
            }
            else { formattedData.Line3 = null; }

            // For TADDB, AdditionalHeaderBytes might be minimal or specific. Using BoardId as placeholder.
            formattedData.AdditionalHeaderBytes = new List<byte> { boardId };
            // formattedData.AdditionalHeaderBytes = null;

            return formattedData;
        }

        public FormattedDisplayData FormatMessage(string message, Dictionary<string, string> boardConfig)
        {
            Console.WriteLine($"TADDB Formatter: Formatting Message: \"{message.Substring(0, Math.Min(message.Length, 20))}...\"");

            int lines = boardConfig.TryGetValue("Lines", out string linesStr) && int.TryParse(linesStr, out int l) ? l : 1;
            int charsPerLine = boardConfig.TryGetValue("CharsPerLine", out string charsStr) && int.TryParse(charsStr, out int cpl) ? cpl : 20;
            byte boardId = boardConfig.TryGetValue("BoardId", out string idStr) && byte.TryParse(idStr, out byte id) ? id : (byte)0x01;

            var formattedData = new FormattedDisplayData();
            message = message.ToUpper();

            List<string> messageLines = new List<string>();
            for (int i = 0; i < message.Length; i += charsPerLine)
            {
                messageLines.Add(message.Substring(i, Math.Min(charsPerLine, message.Length - i)));
            }

            if (messageLines.Count > 0)
            {
                formattedData.Line1 = ConvertTextToBitmapBytes(messageLines[0], charsPerLine);
            }

            if (lines >= 2)
            {
                if (messageLines.Count > 1)
                    formattedData.Line2 = ConvertTextToBitmapBytes(messageLines[1], charsPerLine);
                else
                    formattedData.Line2 = ConvertTextToBitmapBytes("", charsPerLine); // Empty line
            } else { formattedData.Line2 = null; }


            if (lines >= 3)
            {
                 if (messageLines.Count > 2)
                    formattedData.Line3 = ConvertTextToBitmapBytes(messageLines[2], charsPerLine);
                else
                    formattedData.Line3 = ConvertTextToBitmapBytes("", charsPerLine); // Empty line
            } else { formattedData.Line3 = null; }

            // Fill remaining lines if board supports more
            for (int i = 3; i < lines; i++) // Example for up to 6 lines
            {
                if (i == 3 && formattedData.Line3 == null) formattedData.Line3 = ConvertTextToBitmapBytes("", charsPerLine);
                // For Line4, Line5, Line6, FormattedDisplayData would need more properties
                // Or Line3 could be a list of byte arrays for lines 3+
            }

            formattedData.AdditionalHeaderBytes = new List<byte> { boardId };
            // formattedData.AdditionalHeaderBytes = null;

            return formattedData;
        }

        private byte[] ConvertTextToBitmapBytes(string text, int charsPerLine)
        {
            if (text == null) text = "";
            text = text.ToUpper();

            List<byte> lineBitmapBytes = new List<byte>();

            for (int i = 0; i < charsPerLine; i++)
            {
                char c = (i < text.Length) ? text[i] : ' ';
                byte[] charBitmap = SimpleFont.GetCharBitmap(c);
                lineBitmapBytes.AddRange(charBitmap);
            }
            return lineBitmapBytes.ToArray();
        }
    }
}
