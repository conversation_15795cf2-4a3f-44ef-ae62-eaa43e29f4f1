using System;
using System.Drawing;
using System.Windows.Forms;
using IPIS.Models;
using IPIS.Services;
using IPIS.Utils;
using IPIS.Repositories;

namespace IPIS.Forms.Settings
{
    public partial class LanguageManagementControl : UserControl
    {
        private readonly LanguageService _languageService;
        private DataGridView dgvLanguages;
        private Button btnAddLanguage;
        private Button btnEditLanguage;
        private Button btnDeleteLanguage;
        private Button btnSetDefault;
        private Button btnToggleStatus;
        private Button btnRefresh;
        private Label lblLanguages;
        private StatusStrip statusStrip;
        private ToolStripStatusLabel statusLabel;

        public LanguageManagementControl()
        {
            _languageService = new LanguageService(new SQLiteLanguageRepository());
            InitializeComponent();
            LoadLanguagesAsync();
        }

        private void InitializeComponent()
        {
            this.Dock = DockStyle.Fill;
            // Title Label
            this.lblLanguages = new Label
            {
                AutoSize = true,
                Location = new Point(20, 20),
                Text = "Language Management",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.FromArgb(64, 64, 64)
            };

            // DataGridView
            this.dgvLanguages = new DataGridView
            {
                Location = new Point(20, 60),
                Size = new Size(940, 400),
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D,
                GridColor = Color.LightGray,
                RowHeadersVisible = false,
                Font = new Font("Segoe UI", 9F, FontStyle.Regular)
            };

            // Configure DataGridView columns
            this.dgvLanguages.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn { Name = "Id", HeaderText = "ID", Width = 50, Visible = false },
                new DataGridViewTextBoxColumn { Name = "Name", HeaderText = "Language Name", Width = 150 },
                new DataGridViewTextBoxColumn { Name = "Code", HeaderText = "Code", Width = 80 },
                new DataGridViewTextBoxColumn { Name = "NativeName", HeaderText = "Native Name", Width = 150 },
                new DataGridViewTextBoxColumn { Name = "WaveFolder", HeaderText = "Wave Folder", Width = 200 },
                new DataGridViewCheckBoxColumn { Name = "IsActive", HeaderText = "Active", Width = 60 },
                new DataGridViewCheckBoxColumn { Name = "IsDefault", HeaderText = "Default", Width = 60 }
            });

            // Buttons
            this.btnAddLanguage = new Button
            {
                Location = new Point(20, 480),
                Size = new Size(120, 35),
                Text = "Add Language"
            };
            ButtonStyler.ApplyStandardStyle(this.btnAddLanguage, "primary");
            this.btnAddLanguage.Click += new EventHandler(this.btnAddLanguage_Click);

            this.btnEditLanguage = new Button
            {
                Location = new Point(160, 480),
                Size = new Size(120, 35),
                Text = "Edit Language"
            };
            ButtonStyler.ApplyStandardStyle(this.btnEditLanguage, "warning");
            this.btnEditLanguage.Click += new EventHandler(this.btnEditLanguage_Click);

            this.btnDeleteLanguage = new Button
            {
                Location = new Point(300, 480),
                Size = new Size(120, 35),
                Text = "Delete Language"
            };
            ButtonStyler.ApplyStandardStyle(this.btnDeleteLanguage, "danger");
            this.btnDeleteLanguage.Click += new EventHandler(this.btnDeleteLanguage_Click);

            this.btnSetDefault = new Button
            {
                Location = new Point(440, 480),
                Size = new Size(120, 35),
                Text = "Set Default"
            };
            ButtonStyler.ApplyStandardStyle(this.btnSetDefault, "success");
            this.btnSetDefault.Click += new EventHandler(this.btnSetDefault_Click);

            this.btnToggleStatus = new Button
            {
                Location = new Point(580, 480),
                Size = new Size(120, 35),
                Text = "Toggle Status"
            };
            ButtonStyler.ApplyStandardStyle(this.btnToggleStatus, "secondary");
            this.btnToggleStatus.Click += new EventHandler(this.btnToggleStatus_Click);

            this.btnRefresh = new Button
            {
                Location = new Point(720, 480),
                Size = new Size(120, 35),
                Text = "Refresh"
            };
            ButtonStyler.ApplyStandardStyle(this.btnRefresh, "info");
            this.btnRefresh.Click += new EventHandler(this.btnRefresh_Click);

            // StatusStrip
            this.statusStrip = new StatusStrip();
            this.statusLabel = new ToolStripStatusLabel();
            this.statusStrip.Items.AddRange(new ToolStripItem[] {
                this.statusLabel
            });
            this.statusStrip.Location = new Point(0, 678);
            this.statusStrip.Name = "statusStrip";
            this.statusStrip.Size = new Size(1000, 22);
            this.statusStrip.TabIndex = 1;

            // Status Label
            this.statusLabel.Name = "statusLabel";
            this.statusLabel.Text = "Ready";

            // Add controls to UserControl
            this.Controls.AddRange(new Control[] {
                this.lblLanguages,
                this.dgvLanguages,
                this.btnAddLanguage,
                this.btnEditLanguage,
                this.btnDeleteLanguage,
                this.btnSetDefault,
                this.btnToggleStatus,
                this.btnRefresh,
                this.statusStrip
            });
        }

        private async void LoadLanguagesAsync()
        {
            try
            {
                statusLabel.Text = "Loading languages...";
                var languages = await _languageService.GetAllLanguagesAsync();
                
                dgvLanguages.Rows.Clear();
                foreach (var language in languages)
                {
                    dgvLanguages.Rows.Add(
                        language.Id,
                        language.Name,
                        language.Code,
                        language.NativeName,
                        language.WaveFolderPath,
                        language.IsActive,
                        language.IsDefault
                    );
                }
                
                statusLabel.Text = $"Loaded {languages.Count} languages";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading languages: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                statusLabel.Text = "Error loading languages";
            }
        }

        private void btnAddLanguage_Click(object sender, EventArgs e)
        {
            try
            {
                using (var languageForm = new LanguageAddEditForm(_languageService))
                {
                    if (languageForm.ShowDialog() == DialogResult.OK)
                    {
                        LoadLanguagesAsync();
                        statusLabel.Text = "Language added successfully";
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error adding language: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnEditLanguage_Click(object sender, EventArgs e)
        {
            try
            {
                if (dgvLanguages.SelectedRows.Count == 0)
                {
                    MessageBox.Show("Please select a language to edit.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                var selectedRow = dgvLanguages.SelectedRows[0];
                var languageId = Convert.ToInt32(selectedRow.Cells["Id"].Value);
                var language = _languageService.GetLanguageByIdAsync(languageId).Result;

                if (language != null)
                {
                    using (var languageForm = new LanguageAddEditForm(_languageService, language))
                    {
                        if (languageForm.ShowDialog() == DialogResult.OK)
                        {
                            LoadLanguagesAsync();
                            statusLabel.Text = "Language updated successfully";
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error editing language: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnDeleteLanguage_Click(object sender, EventArgs e)
        {
            try
            {
                if (dgvLanguages.SelectedRows.Count == 0)
                {
                    MessageBox.Show("Please select a language to delete.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                var selectedRow = dgvLanguages.SelectedRows[0];
                var languageName = selectedRow.Cells["Name"].Value.ToString();
                var languageId = Convert.ToInt32(selectedRow.Cells["Id"].Value);

                var result = MessageBox.Show(
                    $"Are you sure you want to delete the language '{languageName}'?",
                    "Confirm Delete",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    _languageService.DeleteLanguageAsync(languageId).Wait();
                    LoadLanguagesAsync();
                    statusLabel.Text = "Language deleted successfully";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error deleting language: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnSetDefault_Click(object sender, EventArgs e)
        {
            try
            {
                if (dgvLanguages.SelectedRows.Count == 0)
                {
                    MessageBox.Show("Please select a language to set as default.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                var selectedRow = dgvLanguages.SelectedRows[0];
                var languageName = selectedRow.Cells["Name"].Value.ToString();
                var languageId = Convert.ToInt32(selectedRow.Cells["Id"].Value);

                var result = MessageBox.Show(
                    $"Are you sure you want to set '{languageName}' as the default language?",
                    "Confirm Default",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    _languageService.SetDefaultLanguageAsync(languageId).Wait();
                    LoadLanguagesAsync();
                    statusLabel.Text = "Default language updated successfully";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error setting default language: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnToggleStatus_Click(object sender, EventArgs e)
        {
            try
            {
                if (dgvLanguages.SelectedRows.Count == 0)
                {
                    MessageBox.Show("Please select a language to toggle status.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                var selectedRow = dgvLanguages.SelectedRows[0];
                var languageName = selectedRow.Cells["Name"].Value.ToString();
                var languageId = Convert.ToInt32(selectedRow.Cells["Id"].Value);
                var currentStatus = Convert.ToBoolean(selectedRow.Cells["IsActive"].Value);

                var action = currentStatus ? "deactivate" : "activate";
                var result = MessageBox.Show(
                    $"Are you sure you want to {action} the language '{languageName}'?",
                    "Confirm Status Change",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    _languageService.ToggleLanguageStatusAsync(languageId).Wait();
                    LoadLanguagesAsync();
                    statusLabel.Text = $"Language {action}d successfully";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error toggling language status: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnRefresh_Click(object sender, EventArgs e)
        {
            LoadLanguagesAsync();
        }
    }
} 