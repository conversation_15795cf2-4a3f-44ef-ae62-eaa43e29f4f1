using System.Collections.Generic;
using System.IO.Ports; // For Parity, StopBits

namespace ipis_V2_jules.Models
{
    public class DisplayBoardConfig
    {
        public string BoardName { get; set; } = "DefaultBoard";
        public int BoardId { get; set; } // Primary Key, Network address of the board
        public string BoardType { get; set; } = "AGDB"; // e.g., "AGDB", "MLDB", "CGDB", "PDB", "TADDB"
        public string? Platform { get; set; } // Platform number or identifier this board is associated with

        // Serial Port Settings (if applicable)
        public string? PortName { get; set; } = "COM1";
        public int BaudRate { get; set; } = 9600;
        public Parity Parity { get; set; } = Parity.None;
        public int DataBits { get; set; } = 8;
        public StopBits StopBits { get; set; } = StopBits.One;

        // Display Properties
        public int Lines { get; set; } = 3; // Number of text lines
        public int CharsPerLine { get; set; } = 20; // Characters per line

        // For CGDB
        public int CoachSlots { get; set; } = 12;
        public int CoachSlotLength { get; set; } = 4;

        // For Bitmap-based boards (MLDB, PDB, TADDB)
        public int PixelWidth { get; set; } = 120; // Total pixel width of the display area
        public int PixelHeight { get; set; } = 24; // Total pixel height (e.g., 3 lines * 8px font height)

        // Communication settings
        public string? CommunicationProtocol { get; set; } // e.g., "SerialRS485", "TCP/IP"
        public string? IpAddress { get; set; } // For network boards
        public int NetworkPort { get; set; }   // For network boards

        // Formatter specific settings (can be extended)
        public Dictionary<string, string> FormatterSettings { get; set; }

        public DisplayBoardConfig()
        {
            BoardId = 0x01; // Default, should be set from DB
            FormatterSettings = new Dictionary<string, string>();
        }

        /// <summary>
        /// Converts essential board configuration properties to a dictionary
        /// as expected by the IDisplayDataFormatter interface.
        /// </summary>
        public Dictionary<string, string> ToDictionary()
        {
            var dict = new Dictionary<string, string>
            {
                { "BoardName", BoardName },
                { "BoardId", BoardId.ToString() },
                { "BoardType", BoardType },
                { "Platform", Platform ?? string.Empty },
                { "Lines", Lines.ToString() },
                { "CharsPerLine", CharsPerLine.ToString() },
                { "CoachSlots", CoachSlots.ToString() },
                { "CoachSlotLength", CoachSlotLength.ToString() },
                { "PixelWidth", PixelWidth.ToString() },
                { "PixelHeight", PixelHeight.ToString() }
                // Add any other relevant properties that formatters might need
            };

            // Merge formatter-specific settings
            foreach (var item in FormatterSettings)
            {
                dict[item.Key] = item.Value;
            }
            return dict;
        }
    }
}
