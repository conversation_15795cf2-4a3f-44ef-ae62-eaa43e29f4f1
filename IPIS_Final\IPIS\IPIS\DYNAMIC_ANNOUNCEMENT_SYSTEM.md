# Dynamic Announcement System

## Overview

The IPIS system now supports dynamic announcement templates and sequences that are loaded from the database instead of using hardcoded status options. This provides flexibility to add, modify, or remove announcement types without code changes.

## Key Features

### 1. Dynamic Status Options
- Status options in the Announcement Board are now loaded dynamically from the `AnnouncementTemplates` table
- Only active templates are shown as available status options
- Templates can be added, edited, or deactivated through the Announcement Management interface

### 2. Template-Based Audio Sequences
- Each announcement type (template) can have multiple sequences for different languages
- Sequences are composed of ordered items (audio files and placeholders)
- Audio queue generation now uses the configured sequences instead of hardcoded logic

### 3. Dynamic Placeholders
- Placeholders in sequences are replaced with actual train data at runtime
- Supported placeholders:
  - `TRAIN_NUMBER` - Train number (e.g., 12345)
  - `TRAIN_NAME` - Train name (e.g., Rajdhani Express)
  - `FROM_STATION` - Source station name
  - `TO_STATION` - Destination station name
  - `PLATFORM_NUMBER` - Platform number
  - `EXPECTED_TIME` - Expected arrival/departure time
  - `DELAY_TIME` - Delay time
  - `VIA_STATION_1` to `VIA_STATION_4` - Via station names

## Database Structure

### AnnouncementTemplates
```sql
CREATE TABLE AnnouncementTemplates (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    Name TEXT NOT NULL UNIQUE,           -- Status name (e.g., "RUNNING LATE")
    Description TEXT,                    -- Template description
    IsActive BOOLEAN NOT NULL DEFAULT 1, -- Whether this template is available
    CreatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt DATETIME
);
```

### AnnouncementSequences
```sql
CREATE TABLE AnnouncementSequences (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    TemplateId INTEGER NOT NULL,         -- Reference to AnnouncementTemplates
    LanguageId INTEGER NOT NULL,         -- Reference to Languages
    Name TEXT NOT NULL,                  -- Sequence name
    IsActive BOOLEAN NOT NULL DEFAULT 1,
    CreatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt DATETIME,
    FOREIGN KEY (TemplateId) REFERENCES AnnouncementTemplates(Id) ON DELETE CASCADE,
    FOREIGN KEY (LanguageId) REFERENCES Languages(Id) ON DELETE CASCADE,
    UNIQUE(TemplateId, LanguageId)       -- One sequence per template per language
);
```

### SequenceItems
```sql
CREATE TABLE SequenceItems (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    SequenceId INTEGER NOT NULL,         -- Reference to AnnouncementSequences
    OrderIndex INTEGER NOT NULL,         -- Order of items in sequence
    Type INTEGER NOT NULL,               -- 1 = AudioFile, 2 = Placeholder
    Content TEXT NOT NULL,               -- Audio file path or placeholder name
    Description TEXT,                    -- Item description
    IsActive BOOLEAN NOT NULL DEFAULT 1,
    CreatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt DATETIME,
    FOREIGN KEY (SequenceId) REFERENCES AnnouncementSequences(Id) ON DELETE CASCADE
);
```

## Usage Instructions

### 1. Creating Announcement Templates
1. Go to Settings > Announcement Management
2. Click "Add Template" in the Templates tab
3. Enter template name (this will appear as a status option)
4. Add description and set as active
5. Save the template

### 2. Creating Sequences
1. In the Sequences tab, click "Add Sequence"
2. Select the template and language
3. Enter sequence name
4. Save the sequence
5. Click "Manage Items" to add audio files and placeholders

### 3. Managing Sequence Items
1. Click "Add Audio File" to add audio files from the WAVE directory
2. Click "Add Placeholder" to add dynamic placeholders
3. Use "Move Up" and "Move Down" to reorder items
4. Use "Play Complete Sequence" to test the sequence
5. Save changes

### 4. Using in Announcement Board
1. The status dropdown will automatically show all active templates
2. Select a train and set its status to a template name
3. Check the "AN" column for trains you want to announce
4. Click "Play" to generate and play announcements using the configured sequences

## Example Sequence

For a "RUNNING LATE" template in English:
1. `SPL/TADA.wav` - Attention bell
2. `ENGLISH/STD/STD1.wav` - "May I have your attention please"
3. `TRAIN_NUMBER` - Placeholder for train number
4. `TRAIN_NAME` - Placeholder for train name
5. `FROM_STATION` - Placeholder for source station
6. `TO_STATION` - Placeholder for destination station
7. `ENGLISH/STD/STD9.wav` - "is reported running late by"
8. `DELAY_TIME` - Placeholder for delay time
9. `ENGLISH/STD/STD10.wav` - "inconvenience caused is deeply regretted"

## Benefits

1. **Flexibility**: Add new announcement types without code changes
2. **Multi-language Support**: Different sequences for different languages
3. **Maintainability**: Easy to modify announcement content
4. **Consistency**: All announcements follow the same structure
5. **Testability**: Test sequences before using them in production

## Migration from Static System

The system automatically falls back to the old static status options if:
- No templates are found in the database
- Template loading fails
- No sequences are configured for a template/language combination

This ensures backward compatibility while allowing gradual migration to the new dynamic system. 