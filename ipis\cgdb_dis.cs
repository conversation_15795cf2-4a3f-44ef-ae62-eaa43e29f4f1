// Decompiled with JetBrains decompiler
// Type: ipis.cgdb_dis
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using ipis.My;
using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Diagnostics;
using System.Text;
using System.Threading;

namespace ipis
{

public class cgdb_dis
{
  public static cgdb_dis.cgdb_dis_structure[,] cgdb_dis_brd = new cgdb_dis.cgdb_dis_structure[25, 27];
  public static byte[] no_of_cgdb = new byte[25];
  public static cgdb_dis.cgdb_train_s[] cgdb_brd_msg = new cgdb_dis.cgdb_train_s[256 /*0x0100*/];

  [DebuggerNonUserCode]
  public cgdb_dis()
  {
  }

  public static void cgdb_dis_brd_init()
  {
    int index1 = 0;
    while (index1 < 25)
    {
      int index2 = 0;
      while (index2 < 26)
      {
        cgdb_dis.cgdb_dis_brd[index1, index2].cgdb_addr = (byte) 0;
        cgdb_dis.cgdb_dis_brd[index1, index2].cgdb_name = string.Empty;
        cgdb_dis.cgdb_dis_brd[index1, index2].direction = string.Empty;
        cgdb_dis.cgdb_dis_brd[index1, index2].pdch_addr = (byte) 0;
        cgdb_dis.cgdb_dis_brd[index1, index2].pdch_name = string.Empty;
        cgdb_dis.cgdb_dis_brd[index1, index2].swithcing_time = (byte) 0;
        cgdb_dis.cgdb_dis_brd[index1, index2].video_type = (byte) 0;
        cgdb_dis.cgdb_dis_brd[index1, index2].multicast_addr = (byte) 0;
        cgdb_dis.cgdb_dis_brd[index1, index2].link_status = (byte) 2;
        checked { ++index2; }
      }
      cgdb_dis.no_of_cgdb[index1] = (byte) 0;
      checked { ++index1; }
    }
  }

  public static void cgdb_brd_msg_init()
  {
    int index = 0;
    while (index <= (int) byte.MaxValue)
    {
      cgdb_dis.cgdb_brd_msg[index].pfno = Conversions.ToString(0);
      cgdb_dis.cgdb_brd_msg[index].trainno = string.Empty;
      cgdb_dis.cgdb_brd_msg[index].status = string.Empty;
      cgdb_dis.cgdb_brd_msg[index].used = false;
      checked { ++index; }
    }
  }

  public static void cgdb_link_status()
  {
    byte[] numArray = new byte[51];
    byte index1 = 0;
    while ((int) index1 < frmMainFormIPIS.pfno_cnt)
    {
      byte index2 = 0;
      while ((uint) index2 < (uint) cgdb_dis.no_of_cgdb[(int) index1])
      {
        int pdchAddr1 = (int) cgdb_dis.cgdb_dis_brd[(int) index1, (int) index2].pdch_addr;
        int cgdbAddr1 = (int) cgdb_dis.cgdb_dis_brd[(int) index1, (int) index2].cgdb_addr;
        byte[] local1 = numArray;
        int num1 = 0;
        short num2 = checked ((short) num1);
        short local2 = num2;
        int num3 = (int) cgdb_api.cgdb_link_check((byte) pdchAddr1, (byte) cgdbAddr1, "", ref local1, ref local2);
        int num4 = (int) num2;
        int pdchAddr2 = (int) cgdb_dis.cgdb_dis_brd[(int) index1, (int) index2].pdch_addr;
        int cgdbAddr2 = (int) cgdb_dis.cgdb_dis_brd[(int) index1, (int) index2].cgdb_addr;
        byte[] local3 = numArray;
        num2 = checked ((short) num4);
        short local4 = num2;
        byte num5 = 0;
        byte local5 = num5;
        int num6 = (int) cgdb_api.cgdb_link_check_res_pkt((byte) pdchAddr2, (byte) cgdbAddr2, "", ref local3, ref local4, ref local5);
        num1 = (int) num2;
        if (num6 == 1)
          cgdb_dis.cgdb_dis_brd[(int) index1, (int) index2].link_status = (byte) 1;
        else
          cgdb_dis.cgdb_dis_brd[(int) index1, (int) index2].link_status = (byte) 0;
        if (frmMainFormIPIS.cgs_status | frmMainFormIPIS.display_status | frmMainFormIPIS.msg_pdb_status | frmMainFormIPIS.msg_mldb_status | frmMainFormIPIS.def_pkt_status)
        {
          frmMainFormIPIS.link_check_status = false;
          return;
        }
        checked { ++index2; }
      }
      checked { ++index1; }
    }
  }

  public static void cgdb_set_cfg()
  {
    byte[] numArray = new byte[51];
    byte index1 = 0;
    while ((int) index1 < frmMainFormIPIS.pfno_cnt)
    {
      byte index2 = 0;
      while ((uint) index2 < (uint) cgdb_dis.no_of_cgdb[(int) index1])
      {
        int pdchAddr1 = (int) cgdb_dis.cgdb_dis_brd[(int) index1, (int) index2].pdch_addr;
        int cgdbAddr1 = (int) cgdb_dis.cgdb_dis_brd[(int) index1, (int) index2].cgdb_addr;
        byte[] local1 = numArray;
        int num1 = 0;
        short num2 = checked ((short) num1);
        short local2 = num2;
        int num3 = (int) cgdb_api.cgdb_set_cfg_send_pkt((byte) pdchAddr1, (byte) cgdbAddr1, "", ref local1, ref local2);
        int num4 = (int) num2;
        int pdchAddr2 = (int) cgdb_dis.cgdb_dis_brd[(int) index1, (int) index2].pdch_addr;
        int cgdbAddr2 = (int) cgdb_dis.cgdb_dis_brd[(int) index1, (int) index2].cgdb_addr;
        byte[] local3 = numArray;
        num2 = checked ((short) num4);
        short local4 = num2;
        int num5 = (int) cgdb_api.cgdb_set_cfg_res_pkt((byte) pdchAddr2, (byte) cgdbAddr2, "", ref local3, ref local4);
        num1 = (int) num2;
        if (num5 == 1)
          cgdb_dis.cgdb_dis_brd[(int) index1, (int) index2].link_status = (byte) 1;
        else
          cgdb_dis.cgdb_dis_brd[(int) index1, (int) index2].link_status = (byte) 0;
        checked { ++index2; }
      }
      checked { ++index1; }
    }
  }

  public static byte cgdb_display_message(
    byte addr,
    string platform_no,
    string trainno,
    string train_status,
    string ar_time,
    string dp_time,
    byte[] pkt,
    byte length,
    string ad,
    string trainname,
    byte row_no)
  {
    string[] strArray = new string[27];
    byte pfnoInt = network_db_read.get_pfno_int(platform_no);
    if (pfnoInt != (byte) 0)
      checked { --pfnoInt; }
    byte pdchAddr = cgdb_dis.cgdb_dis_brd[(int) pfnoInt, 0].pdch_addr;
    byte swithcingTime = cgdb_dis.cgdb_dis_brd[(int) pfnoInt, 0].swithcing_time;
    byte videoType = cgdb_dis.cgdb_dis_brd[(int) pfnoInt, 0].video_type;
    byte index1 = 0;
    while (index1 < (byte) 26)
    {
      strArray[(int) index1] = frmMainFormIPIS.online_train_data[(int) row_no].cgs_array_values[(int) index1];
      checked { ++index1; }
    }
    Log_file.Log(string.Format("CGS STOP COMMAND SEND: CGS: {0} PLATFORM NO:{1} TRAIN NO: {2} TRAINSTATUS:{3} ARRIVAL TIME:{4} DEPARTURE TIME:{5}", addr, platform_no, trainno, train_status, ar_time, dp_time));
    byte num1 = 0;
    if (cgdb_api.cgdb_stop(pdchAddr, addr, platform_no) == (byte) 3 && frmMainFormIPIS.data_status)
    {
      Log_file.Log(string.Format("COM PORT FAIL"));
      int num2 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "COM PORT FAIL", "Msg Box", 0, 0, 0);
      num1 = (byte) 6;
    }
    else
    {
      Log_file.Log(string.Format("CGS DATA PACKET SEND: CGS: {0} PLATFORM NO:{1} TRAIN NO: {2} TRAINSTATUS:{3} ARRIVAL TIME:{4} DEPARTURE TIME:{5}", addr, platform_no, trainno, train_status, ar_time, dp_time));
      Log_file.Log(string.Format(" CGS Position from up to down"));
      byte index2 = 0;
      while (index2 < (byte) 26)
      {
        if (Operators.CompareString(strArray[(int) index2], "<!>", false) != 0)
          Log_file.Log(string.Format("CGDB{0}:{1}", checked ((int) index2 + 1), strArray[(int) index2]));
        checked { ++index2; }
      }
      if (cgdb_api.cgdb_msg(pdchAddr, addr, platform_no, pkt, length, swithcingTime, videoType) == (byte) 3 && frmMainFormIPIS.data_status)
      {
        Log_file.Log(string.Format("COM PORT FAIL"));
        int num3 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "COM PORT FAIL", "Msg Box", 0, 0, 0);
        num1 = (byte) 6;
      }
      else
      {
        Log_file.Train_Log(string.Format("{0},{1},{2},{3},{4},{5},{6},{7},{8},{9}", DateTime.Now.ToLongTimeString(), trainno, trainname, ad, ar_time, train_status, platform_no, "CGDB", addr, "SUCCESS"));
        Thread.Sleep(20);
        Log_file.Log(string.Format("CGS START COMMAND SEND: CGS: {0} PLATFORM NO:{1} TRAIN NO: {2} TRAINSTATUS:{3} ARRIVAL TIME:{4} DEPARTURE TIME:{5}", addr, platform_no, trainno, train_status, ar_time, dp_time));
        if (cgdb_api.cgdb_start(pdchAddr, addr, platform_no) == (byte) 3 && frmMainFormIPIS.data_status)
        {
          Log_file.Log(string.Format("COM PORT FAIL"));
          int num4 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "COM PORT FAIL", "Msg Box", 0, 0, 0);
          num1 = (byte) 6;
        }
        else
        {
          cgdb_dis.cgdb_brd_msg[(int) addr].pfno = platform_no;
          cgdb_dis.cgdb_brd_msg[(int) addr].status = train_status;
          cgdb_dis.cgdb_brd_msg[(int) addr].trainno = trainno;
          cgdb_dis.cgdb_brd_msg[(int) addr].used = true;
          int index3 = 0;
          string empty = string.Empty;
          while (index3 < 26)
          {
            empty += strArray[index3];
            checked { ++index3; }
          }
          if (Operators.CompareString(ad, "A", false) == 0)
            Log_file.cgs_Log(string.Format("{0},{1},{2},{3},{4},{5},{6},{7},{8},{9},{10}", DateTime.Now.ToLongTimeString(), trainno, trainname, ad, ar_time, train_status, platform_no, "UP", addr, "SUCCESS", empty));
          else
            Log_file.cgs_Log(string.Format("{0},{1},{2},{3},{4},{5},{6},{7},{8},{9},{10}", DateTime.Now.ToLongTimeString(), trainno, trainname, ad, ar_time, train_status, platform_no, "DOWN", addr, "SUCCESS", empty));
        }
      }
    }
    return num1;
  }

  public byte cgdb_delete_message(int row_no, byte addr)
  {
    byte pfnoInt = network_db_read.get_pfno_int(frmMainFormIPIS.online_train_data[row_no].pfno);
    if (pfnoInt != (byte) 0)
      checked { --pfnoInt; }
    if (addr == (byte) 0)
      addr = cgdb_dis.cgdb_dis_brd[(int) pfnoInt, 0].multicast_addr;
    byte pdchAddr = cgdb_dis.cgdb_dis_brd[(int) pfnoInt, 0].pdch_addr;
    byte swithcingTime = cgdb_dis.cgdb_dis_brd[(int) pfnoInt, 0].swithcing_time;
    byte videoType = cgdb_dis.cgdb_dis_brd[(int) pfnoInt, 0].video_type;
    byte num1 = 0;
    if (Operators.CompareString(cgdb_dis.cgdb_brd_msg[(int) addr].trainno, frmMainFormIPIS.online_train_data[row_no].train_no, false) == 0 & Operators.CompareString(cgdb_dis.cgdb_brd_msg[(int) addr].pfno, frmMainFormIPIS.online_train_data[row_no].pfno, false) == 0 & cgdb_dis.cgdb_brd_msg[(int) addr].used)
    {
      Log_file.Log(string.Format("CGS STOP COMMAND SEND: CGS: {0} PLATFORM NO:{1} TRAIN NO: {2} TRAINSTATUS:{3} ARRIVAL TIME:{4} DEPARTURE TIME:{5}", addr, frmMainFormIPIS.online_train_data[row_no].pfno, frmMainFormIPIS.online_train_data[row_no].train_no, frmMainFormIPIS.online_train_data[row_no].train_status, frmMainFormIPIS.online_train_data[row_no].exp_arr_time, frmMainFormIPIS.online_train_data[row_no].exp_dep_time));
      if (cgdb_api.cgdb_stop(pdchAddr, addr, frmMainFormIPIS.online_train_data[row_no].pfno) == (byte) 3 && frmMainFormIPIS.data_status)
      {
        Log_file.Log(string.Format("COM PORT FAIL"));
        int num2 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "COM PORT FAIL", "Msg Box", 0, 0, 0);
        num1 = (byte) 6;
        goto label_9;
      }
      Thread.Sleep(20);
      cgdb_dis.cgdb_brd_msg[(int) addr].pfno = string.Empty;
      cgdb_dis.cgdb_brd_msg[(int) addr].status = string.Empty;
      cgdb_dis.cgdb_brd_msg[(int) addr].trainno = string.Empty;
      cgdb_dis.cgdb_brd_msg[(int) addr].used = false;
    }
label_9:
    return num1;
  }

  public void cgdb_dis_msg()
  {
    string str1 = string.Empty;
    string str2 = string.Empty;
    string str3 = string.Empty;
    string str4 = string.Empty;
    string[] strArray = new string[27];
    byte[] agdb_msg = new byte[240 /*0xF0*/];
    string empty = string.Empty;
    byte[] numArray1 = new byte[4];
    byte[] numArray2 = new byte[4];
    byte[] pkt = new byte[201];
    try
    {
      int row_no = 0;
      while (row_no < frmMainFormIPIS.online_train_cnt)
      {
        if (frmMainFormIPIS.online_train_data[row_no].cgs_checked)
        {
          string pfno = frmMainFormIPIS.online_train_data[row_no].pfno;
          string str5 = Strings.Trim(frmMainFormIPIS.online_train_data[row_no].train_no);
          string trainName = frmMainFormIPIS.online_train_data[row_no].train_name;
          string ad = frmMainFormIPIS.online_train_data[row_no].AD;
          string expArrTime = frmMainFormIPIS.online_train_data[row_no].exp_arr_time;
          string expDepTime = frmMainFormIPIS.online_train_data[row_no].exp_dep_time;
          string trainStatus = frmMainFormIPIS.online_train_data[row_no].train_status;
          str1 = frmMainFormIPIS.online_train_data[row_no].train_name_hin;
          str2 = frmMainFormIPIS.online_train_data[row_no].train_name_reg;
          str3 = frmMainFormIPIS.online_train_data[row_no].late;
          str4 = frmMainFormIPIS.online_train_data[row_no].sch_arr_time;
          string arrivalordeparture_time = Conversions.ToString(Interaction.IIf(Operators.CompareString(ad, "A", false) == 0, (object) expArrTime, (object) expDepTime));
          if (Operators.CompareString(str5, " ", false) == 0)
          {
            if (basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Train NO is not selected\r\nTo Proceed with other selected trains (Y/N)", "Msg Box", 4, 0, 0) == (short) 7)
            {
              while (row_no < frmMainFormIPIS.online_train_cnt)
              {
                frmMainFormIPIS.online_train_data[row_no].cgs_checked = false;
                checked { ++row_no; }
              }
              online_trains.update_dgv();
              int index = 0;
              while (index < frmMainFormIPIS.train_cnt)
              {
                MyProject.Forms.frmMainFormIPIS.dgvTrainno.Items.Add((object) frmMainFormIPIS.train_details[index].train_no);
                checked { ++index; }
              }
              return;
            }
            frmMainFormIPIS.online_train_data[row_no].cgs_checked = false;
            online_trains.update_dgv();
            int index1 = 0;
            while (index1 < frmMainFormIPIS.train_cnt)
            {
              MyProject.Forms.frmMainFormIPIS.dgvTrainno.Items.Add((object) frmMainFormIPIS.train_details[index1].train_no);
              checked { ++index1; }
            }
            goto label_79;
          }
          if (Operators.CompareString(ad, " ", false) == 0)
          {
            if (basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Arrival or Departure is not selected\r\nTo Proceed with other selected trains (Y/N)", "Msg Box", 4, 0, 0) == (short) 7)
            {
              while (row_no < frmMainFormIPIS.online_train_cnt)
              {
                frmMainFormIPIS.online_train_data[row_no].cgs_checked = false;
                checked { ++row_no; }
              }
              online_trains.update_dgv();
              int index = 0;
              while (index < frmMainFormIPIS.train_cnt)
              {
                MyProject.Forms.frmMainFormIPIS.dgvTrainno.Items.Add((object) frmMainFormIPIS.train_details[index].train_no);
                checked { ++index; }
              }
              return;
            }
            frmMainFormIPIS.online_train_data[row_no].cgs_checked = false;
            online_trains.update_dgv();
            int index2 = 0;
            while (index2 < frmMainFormIPIS.train_cnt)
            {
              MyProject.Forms.frmMainFormIPIS.dgvTrainno.Items.Add((object) frmMainFormIPIS.train_details[index2].train_no);
              checked { ++index2; }
            }
            goto label_79;
          }
          if (Operators.CompareString(trainStatus, " ", false) == 0)
          {
            if (basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Train Status is not selected\r\nTo Proceed with other selected trains (Y/N)", "Msg Box", 4, 0, 0) == (short) 7)
            {
              while (row_no < frmMainFormIPIS.online_train_cnt)
              {
                frmMainFormIPIS.online_train_data[row_no].cgs_checked = false;
                checked { ++row_no; }
              }
              online_trains.update_dgv();
              return;
            }
            frmMainFormIPIS.online_train_data[row_no].cgs_checked = false;
            online_trains.update_dgv();
            goto label_79;
          }
          if (Operators.CompareString(pfno, "", false) == 0)
          {
            if (basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Platform No is not selected\r\nTo Proceed with other selected trains (Y/N)", "Msg Box", 4, 0, 0) == (short) 7)
            {
              while (row_no < frmMainFormIPIS.online_train_cnt)
              {
                frmMainFormIPIS.online_train_data[row_no].cgs_checked = false;
                checked { ++row_no; }
              }
              online_trains.update_dgv();
              return;
            }
            frmMainFormIPIS.online_train_data[row_no].cgs_checked = false;
            online_trains.update_dgv();
            goto label_79;
          }
          if (frmMainFormIPIS.online_train_data[row_no].cgs_array_modified)
          {
            int index = 0;
            while (index < 240 /*0xF0*/)
            {
              agdb_msg[index] = (byte) 0;
              checked { ++index; }
            }
            agdb_byte_construct.train_inf_data_bytes(str5, trainName, arrivalordeparture_time, ad, pfno, ref agdb_msg);
            if (taddb_msg.agdb_display_message(row_no, agdb_msg) == (byte) 6)
            {
              frmMainFormIPIS.red_Cgdb_Value = true;
              goto label_83;
            }
            if (taddb_msg.agdb_com_display_message(checked ((byte) row_no), agdb_msg) == (byte) 6)
            {
              frmMainFormIPIS.red_Cgdb_Value = true;
              goto label_83;
            }
          }
          byte index3 = checked ((byte) ((int) network_db_read.get_pfno_int(pfno) - 1));
          int index4 = 0;
          while (index4 < frmMainFormIPIS.pfno_cnt)
          {
            byte multicastAddr = cgdb_dis.cgdb_dis_brd[index4, 0].multicast_addr;
            if (Operators.CompareString(str5, cgdb_dis.cgdb_brd_msg[(int) multicastAddr].trainno, false) == 0 & Operators.CompareString(pfno, cgdb_dis.cgdb_brd_msg[(int) multicastAddr].pfno, false) != 0)
            {
              if (this.cgdb_delete_message(row_no, multicastAddr) == (byte) 6)
              {
                frmMainFormIPIS.red_Cgdb_Value = true;
                goto label_83;
              }
              break;
            }
            checked { ++index4; }
          }
          if (cgdb_dis.no_of_cgdb[(int) index3] != (byte) 0)
          {
            byte multicastAddr = cgdb_dis.cgdb_dis_brd[(int) index3, 0].multicast_addr;
            if (cgdb_dis.cgdb_brd_msg[(int) multicastAddr].used & Operators.CompareString(cgdb_dis.cgdb_brd_msg[(int) multicastAddr].trainno, str5, false) == 0)
            {
              if (Operators.CompareString(trainStatus, cgdb_dis.cgdb_brd_msg[(int) multicastAddr].status, false) != 0 & (Operators.CompareString(trainStatus, "RESCHEDULED", false) == 0 | Operators.CompareString(trainStatus, "INDEFINITE LATE", false) == 0 | Operators.CompareString(trainStatus, "CANCELLED", false) == 0 | Operators.CompareString(trainStatus, "DIVERTED ROUTE", false) == 0 | Operators.CompareString(trainStatus, "TERMINATED", false) == 0 | Operators.CompareString(trainStatus, "SCHEDULED DEPARTURE", false) == 0 | Operators.CompareString(trainStatus, "HAS LEFT", false) == 0))
              {
                if (this.cgdb_delete_message(row_no, multicastAddr) == (byte) 6)
                {
                  frmMainFormIPIS.red_Cgdb_Value = true;
                  goto label_83;
                }
              }
              else
              {
                if ((Operators.CompareString(cgdb_dis.cgdb_brd_msg[(int) multicastAddr].pfno, pfno, false) == 0 | Operators.CompareString(cgdb_dis.cgdb_brd_msg[(int) multicastAddr].status, trainStatus, false) == 0) & !frmMainFormIPIS.online_train_data[row_no].cgs_array_modified || !((Operators.CompareString(cgdb_dis.cgdb_brd_msg[(int) multicastAddr].pfno, pfno, false) == 0 | Operators.CompareString(cgdb_dis.cgdb_brd_msg[(int) multicastAddr].status, trainStatus, false) == 0) & frmMainFormIPIS.online_train_data[row_no].cgs_array_modified) && Operators.CompareString(trainStatus, "EXPECTED SHORTLY", false) == 0 | Operators.CompareString(trainStatus, "ARRIVING ON", false) == 0 | Operators.CompareString(trainStatus, "ARRIVED ON", false) == 0 | Operators.CompareString(trainStatus, "READY TO LEAVE", false) == 0 | Operators.CompareString(trainStatus, "ON PLATFORM", false) == 0)
                  goto label_79;
                goto label_58;
              }
            }
label_58:
            byte[] bytes1 = new ASCIIEncoding().GetBytes(frmMainFormIPIS.online_train_data[row_no].train_no);
            int index5 = 0;
            byte length = 0;
            while (index5 < 5)
            {
              if (index5 < bytes1.Length)
              {
                pkt[(int) length] = bytes1[index5];
                checked { ++length; }
              }
              else
              {
                pkt[(int) length] = (byte) 0;
                checked { ++length; }
              }
              checked { ++index5; }
            }
            pkt[(int) length] = cgdb_dis.no_of_cgdb[(int) index3];
            checked { ++length; }
            int index6 = 0;
            int index7 = 0;
            while (index6 < (int) cgdb_dis.no_of_cgdb[(int) index3])
            {
              string cgsArrayValue = frmMainFormIPIS.online_train_data[row_no].cgs_array_values[index7];
              if (!(Operators.CompareString(cgsArrayValue, "", false) == 0 | cgsArrayValue == null))
              {
                pkt[(int) length] = cgdb_dis.cgdb_dis_brd[(int) index3, index6].cgdb_addr;
                checked { ++length; }
                byte[] bytes2 = new ASCIIEncoding().GetBytes(cgsArrayValue);
                byte index8 = 0;
                while (index8 < (byte) 4)
                {
                  if ((int) index8 < bytes2.Length)
                  {
                    pkt[(int) length] = bytes2[(int) index8];
                    checked { ++length; }
                  }
                  else
                  {
                    pkt[(int) length] = (byte) 0;
                    checked { ++length; }
                  }
                  checked { ++index8; }
                }
                checked { ++index6; }
              }
              checked { ++index7; }
            }
            if ((Operators.CompareString(trainStatus, "EXPECTED SHORTLY", false) == 0 | Operators.CompareString(trainStatus, "ARRIVING ON", false) == 0 | Operators.CompareString(trainStatus, "ARRIVED ON", false) == 0 | Operators.CompareString(trainStatus, "READY TO LEAVE", false) == 0 | Operators.CompareString(trainStatus, "ON PLATFORM", false) == 0) & (!cgdb_dis.cgdb_brd_msg[(int) multicastAddr].used | frmMainFormIPIS.online_train_data[row_no].cgs_array_modified) && cgdb_dis.cgdb_display_message(multicastAddr, pfno, str5, trainStatus, expArrTime, expDepTime, pkt, length, ad, trainName, checked ((byte) row_no)) == (byte) 6)
            {
              frmMainFormIPIS.red_Cgdb_Value = true;
              goto label_83;
            }
            frmMainFormIPIS.online_train_data[row_no].cgs_array_modified = false;
          }
          else
            goto label_79;
        }
label_79:
        checked { ++row_no; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
label_83:;
  }

  public struct cgdb_dis_structure
  {
    public byte cgdb_addr;
    public string cgdb_name;
    public byte pdch_addr;
    public string pdch_name;
    public byte swithcing_time;
    public byte video_type;
    public string direction;
    public byte multicast_addr;
    public byte link_status;
  }

  public struct cgdb_train_s
  {
    public string trainno;
    public string pfno;
    public string status;
    public bool used;
  }
}
}