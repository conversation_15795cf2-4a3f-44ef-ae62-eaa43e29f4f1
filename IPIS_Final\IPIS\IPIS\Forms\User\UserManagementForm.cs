using System;
using System.Collections.Generic;
using System.Windows.Forms;
using IPIS.Utils;
using IPIS.Services;
using IPIS.Repositories;

namespace IPIS.Forms.User
{
    public partial class UserManagementForm : Form
    {
        private readonly UserService userService;

        public UserManagementForm()
        {
            InitializeComponent();
            userService = new UserService(new SQLiteUserRepository());
            userList.DataBindingComplete += userList_DataBindingComplete;
            LoadUsers();
            LoadRoles();
        }

        private void InitializeComponent()
        {
            this.splitContainer = new SplitContainer();
            this.userList = new DataGridView();
            this.userGroup = new GroupBox();
            this.usernameLabel = new Label();
            this.usernameTextBox = new TextBox();
            this.passwordLabel = new Label();
            this.passwordTextBox = new TextBox();
            this.confirmPasswordLabel = new Label();
            this.confirmPasswordTextBox = new TextBox();
            this.roleLabel = new Label();
            this.roleComboBox = new ComboBox();
            this.addUserButton = new Button();
            this.editUserButton = new Button();
            this.refreshButton = new Button();
            this.clearButton = new Button();
            this.statusStrip = new StatusStrip();
            this.statusLabel = new ToolStripStatusLabel();

            // UserManagementForm
            this.ClientSize = new System.Drawing.Size(1024, 768);
            this.Name = "UserManagementForm";
            this.Text = "User Management";
            this.WindowState = FormWindowState.Maximized;

            // SplitContainer
            this.splitContainer.Dock = DockStyle.Fill;
            this.splitContainer.Name = "splitContainer";
            this.splitContainer.Orientation = Orientation.Vertical;
            this.splitContainer.SplitterDistance = 250;

            // User List (DataGridView)
            this.userList.Dock = DockStyle.Fill;
            this.userList.Name = "userList";
            this.userList.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            this.userList.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            this.userList.MultiSelect = false;
            this.userList.AllowUserToAddRows = false;
            this.userList.AllowUserToDeleteRows = false;
            this.userList.ReadOnly = true;
            this.userList.Columns.Add("Username", "Username");
            this.userList.Columns.Add("Role", "Role");
            this.userList.Columns.Add("Last Login", "Last Login");
            this.userList.Columns.Add("Status", "Status");
            this.userList.Columns.Add("Edit", "Edit");
            this.userList.Columns.Add("Delete", "Delete");
            this.userList.CellClick += new DataGridViewCellEventHandler(userList_CellClick);
            this.userList.CellPainting += userList_CellPainting;

            // User Group
            this.userGroup.Dock = DockStyle.Fill;
            this.userGroup.Name = "userGroup";
            this.userGroup.Text = "Add/Edit User";

            // Username Label
            this.usernameLabel.AutoSize = true;
            this.usernameLabel.Location = new System.Drawing.Point(20, 30);
            this.usernameLabel.Name = "usernameLabel";
            this.usernameLabel.Size = new System.Drawing.Size(60, 15);
            this.usernameLabel.Text = "Username";

            // Username TextBox
            this.usernameTextBox.Location = new System.Drawing.Point(120, 27);
            this.usernameTextBox.Name = "usernameTextBox";
            this.usernameTextBox.Size = new System.Drawing.Size(200, 23);

            // Password Label
            this.passwordLabel.AutoSize = true;
            this.passwordLabel.Location = new System.Drawing.Point(20, 70);
            this.passwordLabel.Name = "passwordLabel";
            this.passwordLabel.Size = new System.Drawing.Size(57, 15);
            this.passwordLabel.Text = "Password";

            // Password TextBox
            this.passwordTextBox.Location = new System.Drawing.Point(120, 67);
            this.passwordTextBox.Name = "passwordTextBox";
            this.passwordTextBox.Size = new System.Drawing.Size(200, 23);
            this.passwordTextBox.PasswordChar = '*';

            // Confirm Password Label
            this.confirmPasswordLabel.AutoSize = true;
            this.confirmPasswordLabel.Location = new System.Drawing.Point(20, 110);
            this.confirmPasswordLabel.Name = "confirmPasswordLabel";
            this.confirmPasswordLabel.Size = new System.Drawing.Size(100, 15);
            this.confirmPasswordLabel.Text = "Confirm Password";

            // Confirm Password TextBox
            this.confirmPasswordTextBox.Location = new System.Drawing.Point(120, 107);
            this.confirmPasswordTextBox.Name = "confirmPasswordTextBox";
            this.confirmPasswordTextBox.Size = new System.Drawing.Size(200, 23);
            this.confirmPasswordTextBox.PasswordChar = '*';

            // Role Label
            this.roleLabel.AutoSize = true;
            this.roleLabel.Location = new System.Drawing.Point(20, 150);
            this.roleLabel.Name = "roleLabel";
            this.roleLabel.Size = new System.Drawing.Size(30, 15);
            this.roleLabel.Text = "Role";

            // Role ComboBox
            this.roleComboBox.Location = new System.Drawing.Point(120, 147);
            this.roleComboBox.Name = "roleComboBox";
            this.roleComboBox.Size = new System.Drawing.Size(200, 23);
            this.roleComboBox.DropDownStyle = ComboBoxStyle.DropDownList;

            // Add User Button
            this.addUserButton.Location = new System.Drawing.Point(120, 190);
            this.addUserButton.Name = "addUserButton";
            this.addUserButton.Size = new System.Drawing.Size(200, 30);
            this.addUserButton.Text = "Add User";
            ButtonStyler.ApplyStandardStyle(this.addUserButton, "primary");
            this.addUserButton.Click += new EventHandler(this.addUserButton_Click);

            // Edit User Button
            this.editUserButton.Location = new System.Drawing.Point(120, 230);
            this.editUserButton.Name = "editUserButton";
            this.editUserButton.Size = new System.Drawing.Size(200, 30);
            this.editUserButton.Text = "Edit User";
            ButtonStyler.ApplyStandardStyle(this.editUserButton, "secondary");
            this.editUserButton.Click += new EventHandler(this.editUserButton_Click);

            // Refresh Button
            this.refreshButton.Location = new System.Drawing.Point(120, 270);
            this.refreshButton.Name = "refreshButton";
            this.refreshButton.Size = new System.Drawing.Size(200, 30);
            this.refreshButton.Text = "Refresh";
            ButtonStyler.ApplyStandardStyle(this.refreshButton, "info");
            this.refreshButton.Click += new EventHandler(this.refreshButton_Click);

            // Clear Button
            this.clearButton.Location = new System.Drawing.Point(120, 310);
            this.clearButton.Name = "clearButton";
            this.clearButton.Size = new System.Drawing.Size(200, 30);
            this.clearButton.Text = "Clear";
            ButtonStyler.ApplyStandardStyle(this.clearButton, "info");
            this.clearButton.Click += new EventHandler(this.clearButton_Click);

            // Add controls to user group
            this.userGroup.Controls.AddRange(new Control[] {
                this.usernameLabel,
                this.usernameTextBox,
                this.passwordLabel,
                this.passwordTextBox,
                this.confirmPasswordLabel,
                this.confirmPasswordTextBox,
                this.roleLabel,
                this.roleComboBox,
                this.addUserButton,
                this.editUserButton,
                this.refreshButton,
                this.clearButton
            });

            // StatusStrip
            this.statusStrip.Items.AddRange(new ToolStripItem[] {
                this.statusLabel
            });
            this.statusStrip.Location = new System.Drawing.Point(0, 746);
            this.statusStrip.Name = "statusStrip";
            this.statusStrip.Size = new System.Drawing.Size(1024, 22);
            this.statusStrip.TabIndex = 2;

            // Status Label
            this.statusLabel.Name = "statusLabel";
            this.statusLabel.Text = "Ready";

            // Add controls to form
            this.splitContainer.Panel1.Controls.Add(this.userList);
            this.splitContainer.Panel2.Controls.Add(this.userGroup);
            this.Controls.AddRange(new Control[] {
                this.splitContainer,
                this.statusStrip
            });
        }

        private SplitContainer splitContainer;
        private DataGridView userList;
        private GroupBox userGroup;
        private Label usernameLabel;
        private TextBox usernameTextBox;
        private Label passwordLabel;
        private TextBox passwordTextBox;
        private Label confirmPasswordLabel;
        private TextBox confirmPasswordTextBox;
        private Label roleLabel;
        private ComboBox roleComboBox;
        private Button addUserButton;
        private Button editUserButton;
        private Button refreshButton;
        private Button clearButton;
        private StatusStrip statusStrip;
        private ToolStripStatusLabel statusLabel;

        private void userList_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex < 0) return;

            if (e.ColumnIndex == userList.Columns["Edit"].Index)
            {
                DataGridViewRow row = userList.Rows[e.RowIndex];
                string username = row.Cells["Username"].Value.ToString();
                string role = row.Cells["Role"].Value.ToString();
                
                // Prevent editing Administrator users
                if (role.Equals("Administrator", StringComparison.OrdinalIgnoreCase))
                {
                    MessageBox.Show("Administrator users cannot be edited.", "Access Denied", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }
                
                usernameTextBox.Text = username;
                roleComboBox.SelectedItem = role;
            }
            else if (e.ColumnIndex == userList.Columns["Delete"].Index)
            {
                DataGridViewRow row = userList.Rows[e.RowIndex];
                string username = row.Cells["Username"].Value.ToString();
                string role = row.Cells["Role"].Value.ToString();
                
                // Prevent deleting Administrator users
                if (role.Equals("Administrator", StringComparison.OrdinalIgnoreCase))
                {
                    MessageBox.Show("Administrator users cannot be deleted.", "Access Denied", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }
                
                long userId = Convert.ToInt64(row.Cells["Id"].Value);
                var result = MessageBox.Show("Are you sure you want to delete this user?", "Confirm Delete", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);
                if (result == DialogResult.Yes)
                {
                    try
                    {
                        userService.DeleteUser(userId);
                        LoadUsers();
                        ClearInputs();
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show("Error deleting user: " + ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private void userList_CellPainting(object sender, DataGridViewCellPaintingEventArgs e)
        {
            if ((e.ColumnIndex == userList.Columns["Edit"].Index || e.ColumnIndex == userList.Columns["Delete"].Index) && e.RowIndex >= 0)
            {
                // Check if this is an Administrator user
                string role = userList.Rows[e.RowIndex].Cells["Role"].Value?.ToString() ?? "";
                if (role.Equals("Administrator", StringComparison.OrdinalIgnoreCase))
                {
                    // Hide buttons for Administrator users
                    e.PaintBackground(e.ClipBounds, true);
                    e.Handled = true;
                    return;
                }
                
                e.PaintBackground(e.ClipBounds, true);
                e.PaintContent(e.ClipBounds);

                var buttonRect = e.CellBounds;
                buttonRect.Inflate(-6, -6);
                System.Drawing.Color buttonColor = e.ColumnIndex == userList.Columns["Edit"].Index ? System.Drawing.Color.FromArgb(0, 123, 255) : System.Drawing.Color.FromArgb(220, 53, 69);
                System.Drawing.Color borderColor = e.ColumnIndex == userList.Columns["Edit"].Index ? System.Drawing.Color.FromArgb(0, 80, 200) : System.Drawing.Color.FromArgb(200, 35, 51);
                string buttonText = e.ColumnIndex == userList.Columns["Edit"].Index ? "Edit" : "Delete";
                using (var brush = new System.Drawing.SolidBrush(buttonColor))
                using (var pen = new System.Drawing.Pen(borderColor))
                using (var format = new System.Drawing.StringFormat { Alignment = System.Drawing.StringAlignment.Center, LineAlignment = System.Drawing.StringAlignment.Center })
                {
                    e.Graphics.FillRectangle(brush, buttonRect);
                    e.Graphics.DrawRectangle(pen, buttonRect);
                    e.Graphics.DrawString(buttonText, e.CellStyle.Font, System.Drawing.Brushes.White, buttonRect, format);
                }
                e.Handled = true;
            }
        }

        private void userList_DataBindingComplete(object sender, DataGridViewBindingCompleteEventArgs e)
        {
            // Hide Id column if it exists
            if (userList.Columns.Contains("Id"))
            {
                userList.Columns["Id"].Visible = false;
            }

            // Add Edit button column if it doesn't exist
            if (!userList.Columns.Contains("Edit"))
            {
                DataGridViewButtonColumn editColumn = new DataGridViewButtonColumn();
                editColumn.Name = "Edit";
                editColumn.HeaderText = "Edit";
                editColumn.Text = "Edit";
                editColumn.UseColumnTextForButtonValue = true;
                userList.Columns.Add(editColumn);
            }

            // Add Delete button column if it doesn't exist
            if (!userList.Columns.Contains("Delete"))
            {
                DataGridViewButtonColumn deleteColumn = new DataGridViewButtonColumn();
                deleteColumn.Name = "Delete";
                deleteColumn.HeaderText = "Delete";
                deleteColumn.Text = "Delete";
                deleteColumn.UseColumnTextForButtonValue = true;
                userList.Columns.Add(deleteColumn);
            }

            // Set column order for Edit and Delete
            if (userList.Columns.Contains("Edit"))
            {
                userList.Columns["Edit"].DisplayIndex = userList.Columns.Count - 2;
            }
            if (userList.Columns.Contains("Delete"))
            {
                userList.Columns["Delete"].DisplayIndex = userList.Columns.Count - 1;
            }
        }

        private void LoadUsers()
        {
            try
            {
                userList.Columns.Clear(); // This will remove all columns, including Edit/Delete
                userList.DataSource = userService.GetAllUsers();
            }
            catch (Exception ex)
            {
                MessageBox.Show("Error loading users: " + ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadRoles()
        {
            try
            {
                roleComboBox.Items.Clear();
                // Load roles from database (excluding Administrator for new users)
                var roleService = new RoleService(new SQLiteRoleRepository());
                var rolesTable = roleService.GetAllRoles();
                
                foreach (System.Data.DataRow row in rolesTable.Rows)
                {
                    string roleName = row["Name"].ToString();
                    // Don't add Administrator role to the dropdown for new users
                    if (!roleName.Equals("Administrator", StringComparison.OrdinalIgnoreCase))
                    {
                        roleComboBox.Items.Add(roleName);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("Error loading roles: " + ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void addUserButton_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(usernameTextBox.Text) || string.IsNullOrWhiteSpace(passwordTextBox.Text) || string.IsNullOrWhiteSpace(confirmPasswordTextBox.Text) || roleComboBox.SelectedItem == null)
            {
                MessageBox.Show("Please fill in all fields.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            if (passwordTextBox.Text != confirmPasswordTextBox.Text)
            {
                MessageBox.Show("Passwords do not match.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            // Prevent creating Administrator users
            if (roleComboBox.SelectedItem.ToString().Equals("Administrator", StringComparison.OrdinalIgnoreCase))
            {
                MessageBox.Show("Administrator users cannot be created through this interface.", "Access Denied", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                userService.AddUser(
                    usernameTextBox.Text,
                    passwordTextBox.Text,
                    roleComboBox.SelectedItem.ToString()
                );

                LoadUsers();
                ClearInputs();
                MessageBox.Show("User added successfully.", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show("Error adding user: " + ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void editUserButton_Click(object sender, EventArgs e)
        {
            if (userList.SelectedRows.Count == 0)
            {
                MessageBox.Show("Please select a user to edit.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            DataGridViewRow row = userList.SelectedRows[0];
            long userId = Convert.ToInt64(row.Cells["Id"].Value);
            string currentRole = row.Cells["Role"].Value.ToString();

            // Prevent editing Administrator users
            if (currentRole.Equals("Administrator", StringComparison.OrdinalIgnoreCase))
            {
                MessageBox.Show("Administrator users cannot be edited.", "Access Denied", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (string.IsNullOrWhiteSpace(usernameTextBox.Text) || string.IsNullOrWhiteSpace(passwordTextBox.Text) || string.IsNullOrWhiteSpace(confirmPasswordTextBox.Text) || roleComboBox.SelectedItem == null)
            {
                MessageBox.Show("Please fill in all fields.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            if (passwordTextBox.Text != confirmPasswordTextBox.Text)
            {
                MessageBox.Show("Passwords do not match.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            // Prevent changing users to Administrator role
            if (roleComboBox.SelectedItem.ToString().Equals("Administrator", StringComparison.OrdinalIgnoreCase))
            {
                MessageBox.Show("Users cannot be changed to Administrator role through this interface.", "Access Denied", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                userService.UpdateUser(
                    userId,
                    usernameTextBox.Text,
                    passwordTextBox.Text,
                    roleComboBox.SelectedItem.ToString()
                );

                LoadUsers();
                ClearInputs();
                MessageBox.Show("User updated successfully.", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show("Error updating user: " + ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void refreshButton_Click(object sender, EventArgs e)
        {
            LoadUsers();
        }

        private void clearButton_Click(object sender, EventArgs e)
        {
            ClearInputs();
        }

        private void ClearInputs()
        {
            usernameTextBox.Clear();
            passwordTextBox.Clear();
            confirmPasswordTextBox.Clear();
            roleComboBox.SelectedIndex = -1;
        }
    }
} 