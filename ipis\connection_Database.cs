// Decompiled with JetBrains decompiler
// Type: ipis.connection_Database
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using System.Data.OleDb;
using System.Diagnostics;

namespace ipis
{

public class connection_Database
{
  public static OleDbConnection con1 = new OleDbConnection("Provider=Microsoft.Jet.OLEDB.4.0;Data Source=C:\\IPIS\\Database\\train_db.mdb;Jet OLEDB:Database Password=password");
  public static OleDbConnection con2 = new OleDbConnection("Provider=Microsoft.Jet.OLEDB.4.0;Data Source=C:\\IPIS\\Database\\network.mdb;Jet OLEDB:Database password=password");
  public static OleDbConnection con3 = new OleDbConnection("Provider=Microsoft.Jet.OLEDB.4.0;Data Source=C:\\IPIS\\Database\\StationDetails.mdb;Jet OLEDB:Database password=password");
  public static OleDbConnection con4 = new OleDbConnection("Provider=Microsoft.Jet.OLEDB.4.0;Data Source=C:\\IPIS\\Database\\message_db.mdb;Jet OLEDB:Database password=password");
  public static OleDbConnection con5 = new OleDbConnection("Provider=Microsoft.Jet.OLEDB.4.0;Data Source=C:\\IPIS\\Database\\logindb.mdb;Jet OLEDB:Database password=password");
  public static OleDbConnection con6 = new OleDbConnection("Provider=Microsoft.Jet.OLEDB.4.0;Data Source=C:\\IPIS\\Database\\font.mdb;Jet OLEDB:Database password=password");
  public static OleDbConnection con7 = new OleDbConnection("Provider=Microsoft.Jet.OLEDB.4.0;Data Source=C:\\IPIS\\Database\\video_train_db.mdb;Jet OLEDB:Database password=password");
  public static OleDbConnection con8 = new OleDbConnection("Provider=Microsoft.Jet.OLEDB.4.0;Data Source=C:\\IPIS\\Database\\voice_spl_msg_db.mdb;Jet OLEDB:Database password=password");
  public static OleDbConnection con9 = new OleDbConnection("Provider=Microsoft.Jet.OLEDB.4.0;Data Source=C:\\IPIS\\Database\\languages.mdb;Jet OLEDB:Database password=password");
  public static OleDbConnection con10 = new OleDbConnection("Provider=Microsoft.Jet.OLEDB.4.0;Data Source=C:\\IPIS\\Database\\platformno_db.mdb;Jet OLEDB:Database password=password");
  public static OleDbConnection con11 = new OleDbConnection("Provider=Microsoft.Jet.OLEDB.4.0;Data Source=C:\\IPIS\\Database\\StationCode_db.mdb;Jet OLEDB:Database password=password");
  public static OleDbConnection con12 = new OleDbConnection("Provider=Microsoft.Jet.OLEDB.4.0;Data Source=C:\\IPIS\\Database\\video_db.mdb;Jet OLEDB:Database password=password");
  public static OleDbConnection con13 = new OleDbConnection("Provider=Microsoft.Jet.OLEDB.4.0;Data Source=C:\\IPIS\\Database\\shared_files_db.mdb");
  public static OleDbConnection con14 = new OleDbConnection("Provider=Microsoft.Jet.OLEDB.4.0;Data Source=C:\\IPIS\\Database\\live_db.mdb");
  public static OleDbConnection con15 = new OleDbConnection("Provider=Microsoft.Jet.OLEDB.4.0;Data Source=C:\\IPIS\\Database\\admin_db.mdb;Jet OLEDB:Database password=password");

  [DebuggerNonUserCode]
  public connection_Database()
  {
  }
}

}