// Decompiled with JetBrains decompiler
// Type: ipis.frmTrainStatusMsg
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using ipis.My;
using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

namespace ipis
{

[DesignerGenerated]
public class frmTrainStatusMsg : Form
{
  private static List<WeakReference> __ENCList = new List<WeakReference>();
  private IContainer components;
  [AccessedThroughProperty("btnExit")]
  private Button _btnExit;
  [AccessedThroughProperty("btnOk")]
  private Button _btnOk;
  [AccessedThroughProperty("txtHindiStatusMsg")]
  private TextBox _txtHindiStatusMsg;
  [AccessedThroughProperty("txtRegStatusMsg")]
  private TextBox _txtRegStatusMsg;
  [AccessedThroughProperty("cmbEngStatusMsg")]
  private ComboBox _cmbEngStatusMsg;
  [AccessedThroughProperty("Label3")]
  private Label _Label3;
  [AccessedThroughProperty("Label2")]
  private Label _Label2;
  [AccessedThroughProperty("Label1")]
  private Label _Label1;
  [AccessedThroughProperty("Label4")]
  private Label _Label4;
  [AccessedThroughProperty("cmbEffect1")]
  private ComboBox _cmbEffect1;
  [AccessedThroughProperty("txtlangName")]
  private TextBox _txtlangName;
  private byte effect;

  [DebuggerNonUserCode]
  static frmTrainStatusMsg()
  {
  }

  [DebuggerNonUserCode]
  public frmTrainStatusMsg()
  {
    this.Load += new EventHandler(this.frmTrainStatusMsg_Load);
    frmTrainStatusMsg.__ENCAddToList((object) this);
    this.InitializeComponent();
  }

  [DebuggerNonUserCode]
  private static void __ENCAddToList(object value)
  {
    lock (frmTrainStatusMsg.__ENCList)
    {
      if (frmTrainStatusMsg.__ENCList.Count == frmTrainStatusMsg.__ENCList.Capacity)
      {
        int index1 = 0;
        int num = checked (frmTrainStatusMsg.__ENCList.Count - 1);
        int index2 = 0;
        while (index2 <= num)
        {
          if (frmTrainStatusMsg.__ENCList[index2].IsAlive)
          {
            if (index2 != index1)
              frmTrainStatusMsg.__ENCList[index1] = frmTrainStatusMsg.__ENCList[index2];
            checked { ++index1; }
          }
          checked { ++index2; }
        }
        frmTrainStatusMsg.__ENCList.RemoveRange(index1, checked (frmTrainStatusMsg.__ENCList.Count - index1));
        frmTrainStatusMsg.__ENCList.Capacity = frmTrainStatusMsg.__ENCList.Count;
      }
      frmTrainStatusMsg.__ENCList.Add(new WeakReference(RuntimeHelpers.GetObjectValue(value)));
    }
  }

  [DebuggerNonUserCode]
  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  [DebuggerStepThrough]
  private void InitializeComponent()
  {
    this.btnExit = new Button();
    this.btnOk = new Button();
    this.txtHindiStatusMsg = new TextBox();
    this.txtRegStatusMsg = new TextBox();
    this.cmbEngStatusMsg = new ComboBox();
    this.Label3 = new Label();
    this.Label2 = new Label();
    this.Label1 = new Label();
    this.Label4 = new Label();
    this.cmbEffect1 = new ComboBox();
    this.txtlangName = new TextBox();
    this.SuspendLayout();
    this.btnExit.BackColor = Color.SeaShell;
    this.btnExit.DialogResult = DialogResult.Cancel;
    this.btnExit.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnExit1 = this.btnExit;
    Point point1 = new Point(277, 228);
    Point point2 = point1;
    btnExit1.Location = point2;
    this.btnExit.Name = "btnExit";
    Button btnExit2 = this.btnExit;
    Size size1 = new Size(60, 25);
    Size size2 = size1;
    btnExit2.Size = size2;
    this.btnExit.TabIndex = 7;
    this.btnExit.Text = "Exit";
    this.btnExit.UseVisualStyleBackColor = false;
    this.btnOk.BackColor = Color.SeaShell;
    this.btnOk.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnOk1 = this.btnOk;
    point1 = new Point(197, 228);
    Point point3 = point1;
    btnOk1.Location = point3;
    this.btnOk.Name = "btnOk";
    Button btnOk2 = this.btnOk;
    size1 = new Size(60, 25);
    Size size3 = size1;
    btnOk2.Size = size3;
    this.btnOk.TabIndex = 6;
    this.btnOk.Text = "Ok";
    this.btnOk.UseVisualStyleBackColor = false;
    this.txtHindiStatusMsg.Font = new Font("Microsoft Sans Serif", 14.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtHindiStatusMsg1 = this.txtHindiStatusMsg;
    point1 = new Point(191, 128 /*0x80*/);
    Point point4 = point1;
    txtHindiStatusMsg1.Location = point4;
    this.txtHindiStatusMsg.MaxLength = 30;
    this.txtHindiStatusMsg.Name = "txtHindiStatusMsg";
    TextBox txtHindiStatusMsg2 = this.txtHindiStatusMsg;
    size1 = new Size(201, 29);
    Size size4 = size1;
    txtHindiStatusMsg2.Size = size4;
    this.txtHindiStatusMsg.TabIndex = 4;
    this.txtRegStatusMsg.Font = new Font("Microsoft Sans Serif", 14.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtRegStatusMsg1 = this.txtRegStatusMsg;
    point1 = new Point(191, 80 /*0x50*/);
    Point point5 = point1;
    txtRegStatusMsg1.Location = point5;
    this.txtRegStatusMsg.MaxLength = 30;
    this.txtRegStatusMsg.Name = "txtRegStatusMsg";
    TextBox txtRegStatusMsg2 = this.txtRegStatusMsg;
    size1 = new Size(201, 29);
    Size size5 = size1;
    txtRegStatusMsg2.Size = size5;
    this.txtRegStatusMsg.TabIndex = 2;
    this.cmbEngStatusMsg.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.cmbEngStatusMsg.FormattingEnabled = true;
    this.cmbEngStatusMsg.Items.AddRange(new object[7]
    {
      (object) "ARRIVED ON",
      (object) "CANCELLED",
      (object) "INDEFINITE LATE",
      (object) "HAS LEFT",
      (object) "RESCHEDULED",
      (object) "DIVERTED ROUTE",
      (object) "TERMINATED"
    });
    ComboBox cmbEngStatusMsg1 = this.cmbEngStatusMsg;
    point1 = new Point(191, 18);
    Point point6 = point1;
    cmbEngStatusMsg1.Location = point6;
    this.cmbEngStatusMsg.Name = "cmbEngStatusMsg";
    ComboBox cmbEngStatusMsg2 = this.cmbEngStatusMsg;
    size1 = new Size(201, 24);
    Size size6 = size1;
    cmbEngStatusMsg2.Size = size6;
    this.cmbEngStatusMsg.TabIndex = 1;
    this.Label3.AutoSize = true;
    this.Label3.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label label3_1 = this.Label3;
    point1 = new Point(128 /*0x80*/, 129);
    Point point7 = point1;
    label3_1.Location = point7;
    this.Label3.Name = "Label3";
    Label label3_2 = this.Label3;
    size1 = new Size(49, 16 /*0x10*/);
    Size size7 = size1;
    label3_2.Size = size7;
    this.Label3.TabIndex = 11;
    this.Label3.Text = "HINDI";
    this.Label2.AutoSize = true;
    this.Label2.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label label2_1 = this.Label2;
    point1 = new Point(7, 80 /*0x50*/);
    Point point8 = point1;
    label2_1.Location = point8;
    this.Label2.Name = "Label2";
    Label label2_2 = this.Label2;
    size1 = new Size(170, 16 /*0x10*/);
    Size size8 = size1;
    label2_2.Size = size8;
    this.Label2.TabIndex = 10;
    this.Label2.Text = "REGIONAL LANGUAGE";
    this.Label1.AutoSize = true;
    this.Label1.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label label1_1 = this.Label1;
    point1 = new Point(101, 23);
    Point point9 = point1;
    label1_1.Location = point9;
    this.Label1.Name = "Label1";
    Label label1_2 = this.Label1;
    size1 = new Size(73, 16 /*0x10*/);
    Size size9 = size1;
    label1_2.Size = size9;
    this.Label1.TabIndex = 9;
    this.Label1.Text = "ENGLISH";
    this.Label4.AutoSize = true;
    this.Label4.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label label4_1 = this.Label4;
    point1 = new Point(130, 177);
    Point point10 = point1;
    label4_1.Location = point10;
    this.Label4.Name = "Label4";
    Label label4_2 = this.Label4;
    size1 = new Size(47, 16 /*0x10*/);
    Size size10 = size1;
    label4_2.Size = size10;
    this.Label4.TabIndex = 13;
    this.Label4.Text = "Effect";
    this.cmbEffect1.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.cmbEffect1.FormattingEnabled = true;
    this.cmbEffect1.Items.AddRange(new object[9]
    {
      (object) "Curtain Left to Right",
      (object) "Curtain Top to Bottom",
      (object) "Curtain Bottom to Top",
      (object) "Typing Left to Right",
      (object) "Running Right to Left",
      (object) "Running Top to Bottom",
      (object) "Running Bottom to Top",
      (object) "Flashing",
      (object) "Normal"
    });
    ComboBox cmbEffect1_1 = this.cmbEffect1;
    point1 = new Point(191, 176 /*0xB0*/);
    Point point11 = point1;
    cmbEffect1_1.Location = point11;
    this.cmbEffect1.Name = "cmbEffect1";
    ComboBox cmbEffect1_2 = this.cmbEffect1;
    size1 = new Size(138, 24);
    Size size11 = size1;
    cmbEffect1_2.Size = size11;
    this.cmbEffect1.TabIndex = 5;
    this.cmbEffect1.Text = "Normal";
    TextBox txtlangName1 = this.txtlangName;
    point1 = new Point(418, 80 /*0x50*/);
    Point point12 = point1;
    txtlangName1.Location = point12;
    this.txtlangName.Name = "txtlangName";
    TextBox txtlangName2 = this.txtlangName;
    size1 = new Size(87, 20);
    Size size12 = size1;
    txtlangName2.Size = size12;
    this.txtlangName.TabIndex = 3;
    this.AcceptButton = (IButtonControl) this.btnOk;
    this.AutoScaleDimensions = new SizeF(7f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.BackColor = Color.PowderBlue;
    this.CancelButton = (IButtonControl) this.btnExit;
    size1 = new Size(544, 274);
    this.ClientSize = size1;
    this.Controls.Add((Control) this.txtlangName);
    this.Controls.Add((Control) this.cmbEffect1);
    this.Controls.Add((Control) this.Label4);
    this.Controls.Add((Control) this.btnExit);
    this.Controls.Add((Control) this.btnOk);
    this.Controls.Add((Control) this.txtHindiStatusMsg);
    this.Controls.Add((Control) this.txtRegStatusMsg);
    this.Controls.Add((Control) this.cmbEngStatusMsg);
    this.Controls.Add((Control) this.Label3);
    this.Controls.Add((Control) this.Label2);
    this.Controls.Add((Control) this.Label1);
    this.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.FormBorderStyle = FormBorderStyle.FixedToolWindow;
    this.Name = "frmTrainStatusMsg";
    this.Text = "Train Status Messages";
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  internal virtual Button btnExit
  {
    [DebuggerNonUserCode] get { return this._btnExit; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnExit_Click);
      if (this._btnExit != null)
        this._btnExit.Click -= eventHandler;
      this._btnExit = value;
      if (this._btnExit == null)
        return;
      this._btnExit.Click += eventHandler;
    }
  }

  internal virtual Button btnOk
  {
    [DebuggerNonUserCode] get { return this._btnOk; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnOk_Click);
      if (this._btnOk != null)
        this._btnOk.Click -= eventHandler;
      this._btnOk = value;
      if (this._btnOk == null)
        return;
      this._btnOk.Click += eventHandler;
    }
  }

  internal virtual TextBox txtHindiStatusMsg
  {
    [DebuggerNonUserCode] get { return this._txtHindiStatusMsg; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtHindiStatusMsg = value;
    }
  }

  internal virtual TextBox txtRegStatusMsg
  {
    [DebuggerNonUserCode] get { return this._txtRegStatusMsg; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtRegStatusMsg = value;
    }
  }

  internal virtual ComboBox cmbEngStatusMsg
  {
    [DebuggerNonUserCode] get { return this._cmbEngStatusMsg; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.cmbEngStatusMsg_SelectedIndexChanged);
      if (this._cmbEngStatusMsg != null)
        this._cmbEngStatusMsg.SelectedIndexChanged -= eventHandler;
      this._cmbEngStatusMsg = value;
      if (this._cmbEngStatusMsg == null)
        return;
      this._cmbEngStatusMsg.SelectedIndexChanged += eventHandler;
    }
  }

  internal virtual Label Label3
  {
    [DebuggerNonUserCode] get { return this._Label3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Label3 = value; }
  }

  internal virtual Label Label2
  {
    [DebuggerNonUserCode] get { return this._Label2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Label2 = value; }
  }

  internal virtual Label Label1
  {
    [DebuggerNonUserCode] get { return this._Label1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Label1 = value; }
  }

  internal virtual Label Label4
  {
    [DebuggerNonUserCode] get { return this._Label4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Label4 = value; }
  }

  internal virtual ComboBox cmbEffect1
  {
    [DebuggerNonUserCode] get { return this._cmbEffect1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.cmbEffect1_SelectedIndexChanged);
      if (this._cmbEffect1 != null)
        this._cmbEffect1.SelectedIndexChanged -= eventHandler;
      this._cmbEffect1 = value;
      if (this._cmbEffect1 == null)
        return;
      this._cmbEffect1.SelectedIndexChanged += eventHandler;
    }
  }

  internal virtual TextBox txtlangName
  {
    [DebuggerNonUserCode] get { return this._txtlangName; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.txtlangName_TextChanged);
      if (this._txtlangName != null)
        this._txtlangName.TextChanged -= eventHandler;
      this._txtlangName = value;
      if (this._txtlangName == null)
        return;
      this._txtlangName.TextChanged += eventHandler;
    }
  }

  private void btnExit_Click(object sender, EventArgs e)
{
  this.Close();
}

  private void btnOk_Click(object sender, EventArgs e)
  {
    this.lang_status_update();
    int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Message updated/added successfully", "Msg Box", 0, 0, 0);
    try
    {
      string str = "Z:\\Database\\message_db.mdb";
      string sourceFileName = "C:\\IPIS\\Database\\message_db.mdb";
      if (!File.Exists(str))
        File.Create(str);
      bool overwrite = true;
      MyProject.Computer.FileSystem.CopyFile(sourceFileName, str, overwrite);
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
  }

  private void cmbEngStatusMsg_SelectedIndexChanged(object sender, EventArgs e)
  {
    string reg_lang_status_msg = "";
    string hindi_status_msg = "";
    string reg_lang = "";
    string msg_effect = "";
    try
    {
      network_db_read.get_language_details();
      this.lang_status(this.cmbEngStatusMsg.Text, ref reg_lang_status_msg, ref hindi_status_msg, ref msg_effect, ref reg_lang);
      reg_lang = frmMainFormIPIS.language_selection.regional_language_name;
      if (!frmMainFormIPIS.language_selection.regional_language_selected)
      {
        this.txtRegStatusMsg.Text = string.Empty;
        this.txtlangName.Text = string.Empty;
      }
      else
      {
        if (Operators.CompareString(reg_lang, "Telugu", false) == 0)
          this.txtRegStatusMsg.Font = new Font("Gautami", 8.25f, FontStyle.Regular, GraphicsUnit.Point);
        else if (Operators.CompareString(reg_lang, "Oriya", false) == 0)
          this.txtRegStatusMsg.Font = new Font("Maan Normal Odia Akhayara", 8.25f, FontStyle.Regular, GraphicsUnit.Point);
        this.txtlangName.Text = frmMainFormIPIS.language_selection.regional_language_name;
        this.txtRegStatusMsg.Text = Operators.CompareString(Strings.Trim(reg_lang), Strings.Trim(frmMainFormIPIS.language_selection.regional_language_name), false) != 0 ? string.Empty : reg_lang_status_msg;
      }
      this.txtHindiStatusMsg.Text = hindi_status_msg;
      this.cmbEffect1.Text = msg_effect;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  public void lang_status(
    string english_status_msg,
    ref string reg_lang_status_msg,
    ref string hindi_status_msg,
    ref string msg_effect,
    ref string reg_lang)
  {
    network_db_read.get_train_status_info(ref reg_lang_status_msg, ref hindi_status_msg, ref msg_effect, english_status_msg, ref reg_lang);
  }

  public void lang_status_update()
  {
    network_db_read.set_train_status(this.txtRegStatusMsg.Text, this.txtHindiStatusMsg.Text, this.effect, this.cmbEngStatusMsg.Text);
  }

  private void cmbEffect1_SelectedIndexChanged(object sender, EventArgs e)
  {
    if (Operators.CompareString(this.cmbEffect1.Text, "Curtain Left to Right", false) == 0)
      this.effect = (byte) 1;
    else if (Operators.CompareString(this.cmbEffect1.Text, "Curtain Top to Bottom", false) == 0)
      this.effect = (byte) 2;
    else if (Operators.CompareString(this.cmbEffect1.Text, "Curtain Bottom to Top", false) == 0)
      this.effect = (byte) 3;
    else if (Operators.CompareString(this.cmbEffect1.Text, "Typing Left to Right", false) == 0)
      this.effect = (byte) 4;
    else if (Operators.CompareString(this.cmbEffect1.Text, "Running Right to Left", false) == 0)
      this.effect = (byte) 5;
    else if (Operators.CompareString(this.cmbEffect1.Text, "Running Top to Bottom", false) == 0)
      this.effect = (byte) 6;
    else if (Operators.CompareString(this.cmbEffect1.Text, "Running Bottom to Top", false) == 0)
      this.effect = (byte) 7;
    else if (Operators.CompareString(this.cmbEffect1.Text, "Flashing", false) == 0)
    {
      this.effect = (byte) 8;
    }
    else
    {
      if (Operators.CompareString(this.cmbEffect1.Text, "Normal", false) != 0)
        return;
      this.effect = (byte) 9;
    }
  }

  private void frmTrainStatusMsg_Load(object sender, EventArgs e)
  {
    if (!frmMainFormIPIS.language_selection.regional_language_selected)
    {
      this.txtRegStatusMsg.Enabled = false;
      this.txtlangName.Enabled = false;
      this.txtlangName.Text = string.Empty;
    }
    else
    {
      this.txtRegStatusMsg.Enabled = true;
      this.txtlangName.Enabled = true;
      this.txtlangName.Text = frmMainFormIPIS.language_selection.regional_language_name;
      if (Operators.CompareString(this.txtlangName.Text, "Oriya", false) == 0)
        this.txtRegStatusMsg.Font = new Font("Maan Normal Odia Akhayara", 8.25f, FontStyle.Regular, GraphicsUnit.Point);
      else if (Operators.CompareString(this.txtlangName.Text, "Telugu", false) == 0)
        this.txtRegStatusMsg.Font = new Font("Gautami", 8.25f, FontStyle.Regular, GraphicsUnit.Point);
    }
  }

  private void txtlangName_TextChanged(object sender, EventArgs e)
  {
    if (Operators.CompareString(this.txtlangName.Text, "Oriya", false) == 0)
    {
      this.txtRegStatusMsg.Font = new Font("Maan Normal Odia Akhayara", 8.25f, FontStyle.Regular, GraphicsUnit.Point);
    }
    else
    {
      if (Operators.CompareString(this.txtlangName.Text, "Telugu", false) != 0)
        return;
      this.txtRegStatusMsg.Font = new Font("Gautami", 8.25f, FontStyle.Regular, GraphicsUnit.Point);
    }
  }
}

}