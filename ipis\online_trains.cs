// Decompiled with JetBrains decompiler
// Type: ipis.online_trains
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using ipis.My;
using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.IO.Ports;
using System.Text;
using System.Threading;
using System.Windows.Forms;

namespace ipis
{

public class online_trains
{
  public static int temp_cnt = 0;
  public static string present_time;

  [DebuggerNonUserCode]
  public online_trains()
  {
  }

  public static string ReceiveSerialData()
  {
    string serialData = "";
    SerialPort serialPort = (SerialPort) null;
    try
    {
      serialPort = MyProject.Computer.Ports.OpenSerialPort("COM3");
      while (true)
      {
        do
          ;
        while (!serialPort.IsOpen);
        string str = serialPort.ReadLine();
        if (str != null)
          serialData = "{serialData}{str}\r\n";
        else
          break;
      }
    }
    catch (TimeoutException ex)
    {
      ProjectData.SetProjectError((Exception) ex);
      serialData = "Error: Serial Port read timed out.";
      ProjectData.ClearProjectError();
    }
    finally
    {
      if (serialPort != null) serialPort.Close();
    }
    return serialData;
  }

  public static void DialModem()
  {
    using (SerialPort serialPort = MyProject.Computer.Ports.OpenSerialPort("COM3", 9600))
    {
      serialPort.DtrEnable = true;
      serialPort.Write("ATDT 555-0100\r\n");
    }
  }

  public static void SendSerialData(string data)
  {
    using (SerialPort serialPort = MyProject.Computer.Ports.OpenSerialPort("COM3"))
      serialPort.WriteLine(data);
  }

  public static void GetSerialPortNames()
  {
    ListBox listBox = new ListBox();
    try
    {
      foreach (string serialPortName in MyProject.Computer.Ports.SerialPortNames)
        listBox.Items.Add((object) serialPortName);
    }
    finally
    {
      IEnumerator<string> enumerator = null;
      if (enumerator != null) enumerator.Dispose();
    }
  }

  public static void update_dgv()
  {
    int rowIndex = 0;
    int num1 = 0;
    try
    {
      MyProject.Forms.frmMainFormIPIS.dgv.Rows.Clear();
      num1 = 0;
      while (rowIndex < frmMainFormIPIS.online_train_cnt)
      {
        int index = 0;
        int num2 = 0;
        while (index < frmMainFormIPIS.pfno_cnt)
        {
          if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.online_train_data[rowIndex].pfno, Strings.Trim(frmMainFormIPIS.platform_nos[index]), false) == 0)
          {
            num2 = 1;
            break;
          }
          checked { ++index; }
        }
        string reg_auto = "";
        string comport = "";
        network_db_read.get_Auto(ref reg_auto);
        network_db_read.get_ComPort(ref comport);
        if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(reg_auto, "Automatic", false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(reg_auto, "Manual", false) == 0)
        {
          SerialPort serialPort = new SerialPort(comport);
          if (serialPort.IsOpen)
            serialPort.Close();
          else
            serialPort.Open();
          string str1 = DateAndTime.Now.ToString("dd-MM-yyyy HH:mm");
          string str2 = str1.Substring(0, 10).Replace("-", "");
          string str3 = str1.Substring(11, 5).Replace(":", "");
          DataSet ds = new DataSet();
          network_db_read.get_config(ref ds);
          string str4 = ds.Tables[0].Rows[0]["basestn"].ToString().Trim();
          int int16 = (int) Convert.ToInt16(ds.Tables[0].Rows[0]["bufferTime"].ToString().Trim());
          bool boolean = Convert.ToBoolean(ds.Tables[0].Rows[0]["debug"].ToString());
          if (str4.Length == 2)
            str4 += "  ";
          else if (str4.Length == 3)
            str4 += " ";
          string s = str3 + "Q0040023";
          string str5 = frmMainFormIPIS.online_train_data[rowIndex].train_no + str2 + str4 + "      ";
          if (boolean)
          {
            int num3 = (int) MessageBox.Show(str5);
          }
          List<byte> byteList = new List<byte>();
          byteList.Add((byte) 1);
          byteList.AddRange((IEnumerable<byte>) Encoding.ASCII.GetBytes(s));
          byteList.Add((byte) 2);
          byteList.AddRange((IEnumerable<byte>) Encoding.ASCII.GetBytes(str5));
          byteList.Add((byte) 3);
          byte[] array = byteList.ToArray();
          serialPort.Write(array, 0, array.Length);
          Thread.Sleep(1500);
          byte[] numArray = new byte[checked (69 + 1)];
          serialPort.Read(numArray, 0, 69);
          string text1 = Encoding.ASCII.GetString(numArray);
          string text2 = text1;
          if (boolean)
          {
            int num4 = (int) MessageBox.Show(text1);
          }
          text2.Substring(0, 1);
          text2.Substring(2, 4);
          text2.Substring(6, 1);
          text2.Substring(7, 3);
          text2.Substring(10, 4);
          text2.Substring(14, 1);
          text2.Substring(15, 5);
          text2.Substring(20, 7);
          text2.Substring(27, 1);
          text2.Substring(28, 4);
          string str6 = text2.Substring(32 /*0x20*/, 4);
          string str7 = text2.Substring(36, 4);
          text2.Substring(40, 4);
          string Left1 = text2.Substring(44, 4);
          text2.Substring(48 /*0x30*/, 4);
          string str8 = text2.Substring(52, 4);
          text2.Substring(56, 4);
          string Left2 = text2.Substring(60, 1);
          string Left3 = text2.Substring(61, 1);
          text2.Substring(62, 4);
          text2.Substring(66, 3);
          text2.Substring(69, 1);
          if (boolean)
          {
            int num5 = (int) MessageBox.Show(text2);
          }
          frmMainFormIPIS.online_train_data[rowIndex].sch_arr_time = str6.Insert(2, ":");
          frmMainFormIPIS.online_train_data[rowIndex].sch_dep_time = str7.Insert(2, ":");
          frmMainFormIPIS.online_train_data[rowIndex].exp_arr_time = Left1.Insert(2, ":");
          frmMainFormIPIS.online_train_data[rowIndex].exp_dep_time = str7.Insert(2, ":");
          frmMainFormIPIS.online_train_data[rowIndex].late = str8.Insert(2, ":");
          serialPort.Close();
          if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Left2, "1", false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Left2, "2", false) == 0)
            frmMainFormIPIS.online_train_data[rowIndex].AD = "A";
          else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Left2, "3", false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Left2, "4", false) == 0)
            frmMainFormIPIS.online_train_data[rowIndex].AD = "D";
          frmMainFormIPIS.online_train_data[rowIndex].announce_checked = true;
          if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Left3, "0", false) == 0)
          {
            if ((int) Convert.ToInt16(str8) < int16)
            {
              if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.online_train_data[rowIndex].AD, "A", false) == 0)
                frmMainFormIPIS.online_train_data[rowIndex].train_status = "RUNNING ON TIME";
              else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.online_train_data[rowIndex].AD, "D", false) == 0)
                frmMainFormIPIS.online_train_data[rowIndex].train_status = "READY TO LEAVE";
            }
            else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.online_train_data[rowIndex].AD, "A", false) == 0)
              frmMainFormIPIS.online_train_data[rowIndex].train_status = "RUNNING LATE";
            else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.online_train_data[rowIndex].AD, "D", false) == 0)
              frmMainFormIPIS.online_train_data[rowIndex].train_status = "RESCHEDULED";
            if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Left1, "0000", false) == 0 & Convert.ToInt16(str7) > (short) 0 & Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Left2, "1", false) == 0)
            {
              frmMainFormIPIS.online_train_data[rowIndex].AD = "D";
              frmMainFormIPIS.online_train_data[rowIndex].train_status = "READY TO LEAVE";
            }
          }
          else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Left3, "1", false) == 0)
          {
            if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.online_train_data[rowIndex].AD, "A", false) == 0)
              frmMainFormIPIS.online_train_data[rowIndex].train_status = "INDEFINITE LATE";
            else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.online_train_data[rowIndex].AD, "D", false) == 0)
              frmMainFormIPIS.online_train_data[rowIndex].train_status = "RESCHEDULED";
          }
          else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Left3, "2", false) == 0)
          {
            if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.online_train_data[rowIndex].AD, "A", false) == 0)
            {
              frmMainFormIPIS.online_train_data[rowIndex].train_status = "Train does not run on this day";
              frmMainFormIPIS.online_train_data[rowIndex].announce_checked = false;
            }
            else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.online_train_data[rowIndex].AD, "D", false) == 0)
            {
              frmMainFormIPIS.online_train_data[rowIndex].train_status = "Train does not run on this day";
              frmMainFormIPIS.online_train_data[rowIndex].announce_checked = false;
            }
          }
          else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Left3, "3", false) == 0)
          {
            if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.online_train_data[rowIndex].AD, "A", false) == 0)
              frmMainFormIPIS.online_train_data[rowIndex].train_status = "CANCELLED";
            else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.online_train_data[rowIndex].AD, "D", false) == 0)
              frmMainFormIPIS.online_train_data[rowIndex].train_status = "TERMINATED";
          }
          else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Left3, "4", false) == 0)
          {
            if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.online_train_data[rowIndex].AD, "A", false) == 0)
              frmMainFormIPIS.online_train_data[rowIndex].train_status = "INDEFINITE LATE";
            else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.online_train_data[rowIndex].AD, "D", false) == 0)
              frmMainFormIPIS.online_train_data[rowIndex].train_status = "RESCHEDULED";
          }
          else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Left3, "5", false) == 0)
          {
            if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.online_train_data[rowIndex].AD, "A", false) == 0)
              frmMainFormIPIS.online_train_data[rowIndex].train_status = "INDEFINITE LATE";
            else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.online_train_data[rowIndex].AD, "D", false) == 0)
              frmMainFormIPIS.online_train_data[rowIndex].train_status = "DIVERTED ROUTE";
          }
          if (num2 == 0)
          {
            MyProject.Forms.frmMainFormIPIS.dgv.Rows.Add((object) checked (rowIndex + 1), (object) frmMainFormIPIS.online_train_data[rowIndex].train_no, (object) frmMainFormIPIS.online_train_data[rowIndex].train_name, (object) frmMainFormIPIS.online_train_data[rowIndex].sch_arr_time, (object) frmMainFormIPIS.online_train_data[rowIndex].sch_dep_time, (object) frmMainFormIPIS.online_train_data[rowIndex].AD, (object) frmMainFormIPIS.online_train_data[rowIndex].train_status, (object) frmMainFormIPIS.online_train_data[rowIndex].late, (object) frmMainFormIPIS.online_train_data[rowIndex].exp_arr_time, (object) frmMainFormIPIS.online_train_data[rowIndex].exp_dep_time, (object) "", (object) frmMainFormIPIS.online_train_data[rowIndex].display_checked, (object) "", (object) frmMainFormIPIS.online_train_data[rowIndex].cgs_checked, (object) frmMainFormIPIS.online_train_data[rowIndex].announce_checked);
            frmMainFormIPIS.online_train_data[rowIndex].pfno = "";
          }
          else
            MyProject.Forms.frmMainFormIPIS.dgv.Rows.Add((object) checked (rowIndex + 1), (object) frmMainFormIPIS.online_train_data[rowIndex].train_no, (object) frmMainFormIPIS.online_train_data[rowIndex].train_name, (object) frmMainFormIPIS.online_train_data[rowIndex].sch_arr_time, (object) frmMainFormIPIS.online_train_data[rowIndex].sch_dep_time, (object) frmMainFormIPIS.online_train_data[rowIndex].AD, (object) frmMainFormIPIS.online_train_data[rowIndex].train_status, (object) frmMainFormIPIS.online_train_data[rowIndex].late, (object) frmMainFormIPIS.online_train_data[rowIndex].exp_arr_time, (object) frmMainFormIPIS.online_train_data[rowIndex].exp_dep_time, (object) Strings.Trim(frmMainFormIPIS.online_train_data[rowIndex].pfno), (object) frmMainFormIPIS.online_train_data[rowIndex].display_checked, (object) "", (object) frmMainFormIPIS.online_train_data[rowIndex].cgs_checked, (object) frmMainFormIPIS.online_train_data[rowIndex].announce_checked);
          frmMainFormIPIS.online_train_data[rowIndex].sno = checked (rowIndex + 1);
          MyProject.Forms.frmMainFormIPIS.dgv[5, rowIndex].Value = (object) frmMainFormIPIS.online_train_data[rowIndex].AD;
          if (Microsoft.VisualBasic.CompilerServices.Operators.ConditionalCompareObjectEqual(MyProject.Forms.frmMainFormIPIS.dgv[5, rowIndex].Value, (object) "A", false))
          {
            DataGridViewComboBoxCell viewComboBoxCell = new DataGridViewComboBoxCell();
            viewComboBoxCell.Items.Clear();
            viewComboBoxCell.Items.AddRange((object) "RUNNING ON TIME", (object) "ARRIVING ON", (object) "ARRIVED ON", (object) "EXPECTED SHORTLY", (object) "RUNNING LATE", (object) "INDEFINITE LATE", (object) "CANCELLED", (object) "PLATFORM CHANGE");
            MyProject.Forms.frmMainFormIPIS.dgv[6, rowIndex] = (DataGridViewCell) viewComboBoxCell;
            MyProject.Forms.frmMainFormIPIS.dgv[6, rowIndex].Value = (object) frmMainFormIPIS.online_train_data[rowIndex].train_status;
          }
          else if (Microsoft.VisualBasic.CompilerServices.Operators.ConditionalCompareObjectEqual(MyProject.Forms.frmMainFormIPIS.dgv[5, rowIndex].Value, (object) "D", false))
          {
            DataGridViewComboBoxCell viewComboBoxCell = new DataGridViewComboBoxCell();
            viewComboBoxCell.Items.Clear();
            viewComboBoxCell.Items.AddRange((object) "READY TO LEAVE", (object) "ON PLATFORM", (object) "HAS LEFT", (object) "SCHEDULED DEPARTURE", (object) "RESCHEDULED", (object) "DIVERTED ROUTE", (object) "TERMINATED", (object) "REGULATED", (object) "PLATFORM CHANGE", (object) "LEAVE SHORTLY");
            MyProject.Forms.frmMainFormIPIS.dgv[6, rowIndex] = (DataGridViewCell) viewComboBoxCell;
            MyProject.Forms.frmMainFormIPIS.dgv[6, rowIndex].Value = (object) frmMainFormIPIS.online_train_data[rowIndex].train_status;
          }
          MyProject.Forms.frmMainFormIPIS.dgv[13, rowIndex].ReadOnly = !(Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.online_train_data[rowIndex].train_status, "ARRIVED ON", false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.online_train_data[rowIndex].train_status, "ARRIVING ON", false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.online_train_data[rowIndex].train_status, "EXPECTED SHORTLY", false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.online_train_data[rowIndex].train_status, "READY TO LEAVE", false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.online_train_data[rowIndex].train_status, "ON PLATFORM", false) == 0);
        }
        else
        {
          frmMainFormIPIS.online_train_data[rowIndex].announce_checked = false;
          if (num2 == 0)
          {
            MyProject.Forms.frmMainFormIPIS.dgv.Rows.Add((object) checked (rowIndex + 1), (object) frmMainFormIPIS.online_train_data[rowIndex].train_no, (object) frmMainFormIPIS.online_train_data[rowIndex].train_name, (object) frmMainFormIPIS.online_train_data[rowIndex].sch_arr_time, (object) frmMainFormIPIS.online_train_data[rowIndex].sch_dep_time, (object) frmMainFormIPIS.online_train_data[rowIndex].AD, (object) frmMainFormIPIS.online_train_data[rowIndex].train_status, (object) frmMainFormIPIS.online_train_data[rowIndex].late, (object) frmMainFormIPIS.online_train_data[rowIndex].exp_arr_time, (object) frmMainFormIPIS.online_train_data[rowIndex].exp_dep_time, (object) "", (object) frmMainFormIPIS.online_train_data[rowIndex].display_checked, (object) "", (object) frmMainFormIPIS.online_train_data[rowIndex].cgs_checked, (object) frmMainFormIPIS.online_train_data[rowIndex].announce_checked);
            frmMainFormIPIS.online_train_data[rowIndex].pfno = "";
          }
          else
            MyProject.Forms.frmMainFormIPIS.dgv.Rows.Add((object) checked (rowIndex + 1), (object) frmMainFormIPIS.online_train_data[rowIndex].train_no, (object) frmMainFormIPIS.online_train_data[rowIndex].train_name, (object) frmMainFormIPIS.online_train_data[rowIndex].sch_arr_time, (object) frmMainFormIPIS.online_train_data[rowIndex].sch_dep_time, (object) frmMainFormIPIS.online_train_data[rowIndex].AD, (object) frmMainFormIPIS.online_train_data[rowIndex].train_status, (object) frmMainFormIPIS.online_train_data[rowIndex].late, (object) frmMainFormIPIS.online_train_data[rowIndex].exp_arr_time, (object) frmMainFormIPIS.online_train_data[rowIndex].exp_dep_time, (object) Strings.Trim(frmMainFormIPIS.online_train_data[rowIndex].pfno), (object) frmMainFormIPIS.online_train_data[rowIndex].display_checked, (object) "", (object) frmMainFormIPIS.online_train_data[rowIndex].cgs_checked, (object) frmMainFormIPIS.online_train_data[rowIndex].announce_checked);
          frmMainFormIPIS.online_train_data[rowIndex].sno = checked (rowIndex + 1);
          MyProject.Forms.frmMainFormIPIS.dgv[5, rowIndex].Value = (object) frmMainFormIPIS.online_train_data[rowIndex].AD;
          if (Microsoft.VisualBasic.CompilerServices.Operators.ConditionalCompareObjectEqual(MyProject.Forms.frmMainFormIPIS.dgv[5, rowIndex].Value, (object) "A", false))
          {
            DataGridViewComboBoxCell viewComboBoxCell = new DataGridViewComboBoxCell();
            viewComboBoxCell.Items.Clear();
            viewComboBoxCell.Items.AddRange((object) "RUNNING ON TIME", (object) "ARRIVING ON", (object) "ARRIVED ON", (object) "EXPECTED SHORTLY", (object) "RUNNING LATE", (object) "INDEFINITE LATE", (object) "CANCELLED", (object) "PLATFORM CHANGE");
            MyProject.Forms.frmMainFormIPIS.dgv[6, rowIndex] = (DataGridViewCell) viewComboBoxCell;
            MyProject.Forms.frmMainFormIPIS.dgv[6, rowIndex].Value = (object) frmMainFormIPIS.online_train_data[rowIndex].train_status;
          }
          else if (Microsoft.VisualBasic.CompilerServices.Operators.ConditionalCompareObjectEqual(MyProject.Forms.frmMainFormIPIS.dgv[5, rowIndex].Value, (object) "D", false))
          {
            DataGridViewComboBoxCell viewComboBoxCell = new DataGridViewComboBoxCell();
            viewComboBoxCell.Items.Clear();
            viewComboBoxCell.Items.AddRange((object) "READY TO LEAVE", (object) "ON PLATFORM", (object) "HAS LEFT", (object) "SCHEDULED DEPARTURE", (object) "RESCHEDULED", (object) "DIVERTED ROUTE", (object) "TERMINATED", (object) "REGULATED", (object) "PLATFORM CHANGE", (object) "LEAVE SHORTLY");
            MyProject.Forms.frmMainFormIPIS.dgv[6, rowIndex] = (DataGridViewCell) viewComboBoxCell;
            MyProject.Forms.frmMainFormIPIS.dgv[6, rowIndex].Value = (object) frmMainFormIPIS.online_train_data[rowIndex].train_status;
          }
          MyProject.Forms.frmMainFormIPIS.dgv[13, rowIndex].ReadOnly = !(Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.online_train_data[rowIndex].train_status, "ARRIVED ON", false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.online_train_data[rowIndex].train_status, "ARRIVING ON", false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.online_train_data[rowIndex].train_status, "EXPECTED SHORTLY", false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.online_train_data[rowIndex].train_status, "READY TO LEAVE", false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.online_train_data[rowIndex].train_status, "ON PLATFORM", false) == 0);
        }
        checked { ++rowIndex; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num6 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (frmMainFormIPIS.online_update)
      return;
    MyProject.Forms.frmMainFormIPIS.update_online_trains();
  }

  private static void get_online_trains_temp_info_read(int i)
  {
    try
    {
      frmMainFormIPIS.online_train_data_temp[frmMainFormIPIS.online_train_temp_cnt].AD = string.Empty;
      frmMainFormIPIS.online_train_data_temp[frmMainFormIPIS.online_train_temp_cnt].announce_checked = false;
      frmMainFormIPIS.online_train_data_temp[frmMainFormIPIS.online_train_temp_cnt].cgs_array_values = frmMainFormIPIS.train_details[i].cgs_inf;
      frmMainFormIPIS.online_train_data_temp[frmMainFormIPIS.online_train_temp_cnt].cgs_array_modified = false;
      frmMainFormIPIS.online_train_data_temp[frmMainFormIPIS.online_train_temp_cnt].cgs_checked = false;
      frmMainFormIPIS.online_train_data_temp[frmMainFormIPIS.online_train_temp_cnt].display_checked = false;
      frmMainFormIPIS.online_train_data_temp[frmMainFormIPIS.online_train_temp_cnt].exp_arr_time = frmMainFormIPIS.train_details[i].arr_time;
      frmMainFormIPIS.online_train_data_temp[frmMainFormIPIS.online_train_temp_cnt].exp_dep_time = frmMainFormIPIS.train_details[i].dep_time;
      frmMainFormIPIS.online_train_data_temp[frmMainFormIPIS.online_train_temp_cnt].late = "00:00";
      frmMainFormIPIS.online_train_data_temp[frmMainFormIPIS.online_train_temp_cnt].pfno = frmMainFormIPIS.train_details[i].pf_no;
      frmMainFormIPIS.online_train_data_temp[frmMainFormIPIS.online_train_temp_cnt].sch_arr_time = frmMainFormIPIS.train_details[i].arr_time;
      frmMainFormIPIS.online_train_data_temp[frmMainFormIPIS.online_train_temp_cnt].sch_dep_time = frmMainFormIPIS.train_details[i].dep_time;
      frmMainFormIPIS.online_train_data_temp[frmMainFormIPIS.online_train_temp_cnt].sno = checked (frmMainFormIPIS.online_train_cnt + 1);
      frmMainFormIPIS.online_train_data_temp[frmMainFormIPIS.online_train_temp_cnt].station_pos = frmMainFormIPIS.train_details[i].station_pos;
      frmMainFormIPIS.online_train_data_temp[frmMainFormIPIS.online_train_temp_cnt].train_name = frmMainFormIPIS.train_details[i].train_name;
      frmMainFormIPIS.online_train_data_temp[frmMainFormIPIS.online_train_temp_cnt].train_name_hin = frmMainFormIPIS.train_details[i].train_name_hin;
      frmMainFormIPIS.online_train_data_temp[frmMainFormIPIS.online_train_temp_cnt].train_name_reg = frmMainFormIPIS.train_details[i].train_name_reg;
      frmMainFormIPIS.online_train_data_temp[frmMainFormIPIS.online_train_temp_cnt].train_no = frmMainFormIPIS.train_details[i].train_no;
      frmMainFormIPIS.online_train_data_temp[frmMainFormIPIS.online_train_temp_cnt].train_status = string.Empty;
      checked { ++frmMainFormIPIS.online_train_temp_cnt; }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private static void compare_online_data()
  {
    int num = 0;
    int[] numArray = new int[601];
    frmMainFormIPIS.online_train_details[] onlineTrainDetailsArray = new frmMainFormIPIS.online_train_details[601];
    object obj = (object) false;
    int index1 = 0;
    while (index1 < frmMainFormIPIS.online_train_temp_cnt)
    {
      int index2 = 0;
      bool flag = false;
      while (index2 < frmMainFormIPIS.online_train_cnt)
      {
        if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Strings.Trim(frmMainFormIPIS.online_train_data[index2].train_no), frmMainFormIPIS.online_train_data_temp[index1].train_no, false) == 0)
        {
          frmMainFormIPIS.online_train_data_temp[index1] = frmMainFormIPIS.online_train_data[index2];
          flag = true;
          break;
        }
        checked { ++index2; }
      }
      if (!flag)
      {
        frmMainFormIPIS.online_train_data[frmMainFormIPIS.online_train_cnt] = frmMainFormIPIS.online_train_data_temp[index1];
        checked { ++frmMainFormIPIS.online_train_cnt; }
      }
      checked { ++index1; }
    }
    int index3 = 0;
    int index4 = 0;
    num = 0;
    int index5 = 0;
    while (index3 < frmMainFormIPIS.online_train_cnt)
    {
      int index6 = 0;
      bool flag = false;
      while (index6 < frmMainFormIPIS.online_train_temp_cnt)
      {
        if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Strings.Trim(frmMainFormIPIS.online_train_data[index3].train_no), frmMainFormIPIS.online_train_data_temp[index6].train_no, false) == 0)
        {
          onlineTrainDetailsArray[index4] = frmMainFormIPIS.online_train_data[index3];
          checked { ++index4; }
          numArray[index5] = index6;
          checked { ++index5; }
          flag = true;
          break;
        }
        checked { ++index6; }
      }
      if (!flag & (frmMainFormIPIS.online_train_data[index3].cgs_checked | frmMainFormIPIS.online_train_data[index3].display_checked))
      {
        onlineTrainDetailsArray[index4] = frmMainFormIPIS.online_train_data[index3];
        checked { ++index4; }
      }
      checked { ++index3; }
    }
    frmMainFormIPIS.online_train_cnt = 0;
    int index7 = 0;
    while (index7 < index4)
    {
      frmMainFormIPIS.online_train_data[index7] = onlineTrainDetailsArray[index7];
      checked { ++index7; }
      checked { ++frmMainFormIPIS.online_train_cnt; }
    }
    int index8 = 0;
    num = 0;
    while (index8 < frmMainFormIPIS.online_train_temp_cnt)
    {
      int index9 = 0;
      while (index9 < index5)
      {
        if (numArray[index9] != index8)
          checked { ++index9; }
        else
          goto label_32;
      }
      frmMainFormIPIS.online_train_data[frmMainFormIPIS.online_train_cnt] = frmMainFormIPIS.online_train_data_temp[index8];
      if (index4 == 0 | frmMainFormIPIS.online_train_temp_cnt == 0)
        frmMainFormIPIS.online_train_cnt = 0;
      checked { ++frmMainFormIPIS.online_train_cnt; }
label_32:
      checked { ++index8; }
    }
  }

  public static void next_online_train_details()
  {
    int num1 = 0;
    num1 = 0;
    try
    {
      frmMainFormIPIS.online_train_temp_cnt = 0;
      frmMainFormIPIS.online_train_data_temp_init();
      num1 = 0;
      string[] strArray = new string[12];
      int[] numArray = new int[601];
      string empty1 = string.Empty;
      string empty2 = string.Empty;
      string empty3 = string.Empty;
      string empty4 = string.Empty;
      string str1 = string.Empty;
      string empty5 = string.Empty;
      int index1 = 0;
      while (index1 < 600)
      {
        numArray[index1] = 0;
        checked { ++index1; }
      }
      string str2 = Conversions.ToString(Conversions.ToDouble(Conversions.ToString((int) DateTime.Today.DayOfWeek)) + 0.0);
      if (Conversions.ToDouble(str2) == 0.0)
      {
        str2 = "Sunday";
        str1 = "Monday";
      }
      else if (Conversions.ToDouble(str2) == 1.0)
      {
        str2 = "Monday";
        str1 = "Tuesday";
      }
      else if (Conversions.ToDouble(str2) == 2.0)
      {
        str2 = "Tuesday";
        str1 = "Wednesday";
      }
      else if (Conversions.ToDouble(str2) == 3.0)
      {
        str2 = "Wednesday";
        str1 = "Thursday";
      }
      else if (Conversions.ToDouble(str2) == 4.0)
      {
        str2 = "Thursday";
        str1 = "Friday";
      }
      else if (Conversions.ToDouble(str2) == 5.0)
      {
        str2 = "Friday";
        str1 = "Saturday";
      }
      else if (Conversions.ToDouble(str2) == 6.0)
      {
        str2 = "Saturday";
        str1 = "Sunday";
      }
      DateTime today = DateTime.Today;
      DateTime dateTime = today.AddDays(1.0);
      DateTime Expression1 = today;
      DateTime Expression2 = dateTime;
      string Right1 = "23:59";
      string Right2 = "01:00";
      DateTime timeOfDay = DateAndTime.TimeOfDay;
      network_db_read.get_train_timings(ref empty1, ref empty2, ref empty3, ref empty4);
      string Right3;
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(empty4, "from", false) == 0)
      {
        online_trains.present_time = empty1;
        Right3 = empty2;
      }
      else
      {
        online_trains.present_time = Strings.Format((object) timeOfDay, "HH:mm");
        Right3 = Strings.Format((object) Conversions.ToDate(Strings.Format((object) Conversions.ToDate(online_trains.present_time), "HH:mm")).AddMinutes(Conversions.ToDouble(empty3)), "HH:mm");
      }
      int i1 = 0;
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(online_trains.present_time, Right3, false) <= 0)
      {
        while (i1 < frmMainFormIPIS.train_cnt)
        {
          if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.train_details[i1].train_no, "12676", false) == 0)
          {
            int num2 = (int) MessageBox.Show("12676");
          }
          if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.train_details[i1].arr_time, online_trains.present_time, false) >= 0 & Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.train_details[i1].arr_time, Right3, false) <= 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.train_details[i1].dep_time, online_trains.present_time, false) >= 0 & Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.train_details[i1].dep_time, Right3, false) <= 0)
          {
            if (frmMainFormIPIS.train_details[i1].daily == 1)
              online_trains.get_online_trains_temp_info_read(i1);
            else if (frmMainFormIPIS.train_details[i1].specificdays == 1)
            {
              int index2 = 0;
              while (index2 < 7)
              {
                if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Strings.Trim(frmMainFormIPIS.train_details[i1].days[index2]), Strings.Trim(str2), false) == 0)
                {
                  online_trains.get_online_trains_temp_info_read(i1);
                  break;
                }
                checked { ++index2; }
              }
            }
            else if (frmMainFormIPIS.train_details[i1].period == 1)
            {
              if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Strings.Format((object) frmMainFormIPIS.train_details[i1].fromdt, "MM/dd/yyyy"), Strings.Format((object) Expression1, "MM/dd/yyyy"), false) <= 0 & Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Strings.Format((object) frmMainFormIPIS.train_details[i1].todt, "MM/dd/yyyy"), Strings.Format((object) Expression1, "MM/dd/yyyy"), false) >= 0)
                online_trains.get_online_trains_temp_info_read(i1);
            }
            else if (frmMainFormIPIS.train_details[i1].specificdate == 1)
            {
              int index3 = 0;
              while (index3 < 10)
              {
                if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Strings.Format((object) frmMainFormIPIS.train_details[i1].specificdates[index3], "MM/dd/yyyy"), Strings.Format((object) Expression1, "MM/dd/yyyy"), false) == 0)
                {
                  online_trains.get_online_trains_temp_info_read(i1);
                  break;
                }
                checked { ++index3; }
              }
            }
          }
          checked { ++i1; }
        }
      }
      else
      {
        int i2 = 0;
        while (i2 < frmMainFormIPIS.train_cnt)
        {
          if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.train_details[i2].arr_time, online_trains.present_time, false) >= 0 & Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.train_details[i2].arr_time, Right1, false) <= 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.train_details[i2].dep_time, online_trains.present_time, false) >= 0 & Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.train_details[i2].dep_time, Right1, false) <= 0)
          {
            if (frmMainFormIPIS.train_details[i2].daily == 1)
              online_trains.get_online_trains_temp_info_read(i2);
            else if (frmMainFormIPIS.train_details[i2].specificdays == 1)
            {
              int index4 = 0;
              while (index4 < 7)
              {
                if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Strings.Trim(frmMainFormIPIS.train_details[i2].days[index4]), Strings.Trim(str2), false) == 0)
                {
                  online_trains.get_online_trains_temp_info_read(i2);
                  break;
                }
                checked { ++index4; }
              }
            }
            else if (frmMainFormIPIS.train_details[i2].period == 1)
            {
              if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Strings.Format((object) frmMainFormIPIS.train_details[i2].fromdt, "MM/dd/yyyy"), Strings.Format((object) Expression1, "MM/dd/yyyy"), false) <= 0 & Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Strings.Format((object) frmMainFormIPIS.train_details[i2].todt, "MM/dd/yyyy"), Strings.Format((object) Expression1, "MM/dd/yyyy"), false) >= 0)
                online_trains.get_online_trains_temp_info_read(i2);
            }
            else if (frmMainFormIPIS.train_details[i2].specificdate == 1)
            {
              int index5 = 0;
              while (index5 < 10)
              {
                if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Strings.Format((object) frmMainFormIPIS.train_details[i2].specificdates[index5], "MM/dd/yyyy"), Strings.Format((object) Expression1, "MM/dd/yyyy"), false) == 0)
                {
                  online_trains.get_online_trains_temp_info_read(i2);
                  break;
                }
                checked { ++index5; }
              }
            }
          }
          checked { ++i2; }
        }
        int i3 = 0;
        while (i3 < frmMainFormIPIS.train_cnt)
        {
          int index6 = 0;
          while (index6 < online_trains.temp_cnt)
          {
            if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.online_train_data_temp[index6].train_no, frmMainFormIPIS.train_details[i3].train_no, false) != 0)
              checked { ++index6; }
            else
              goto label_98;
          }
          if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.train_details[i3].arr_time, Right2, false) >= 0 & Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.train_details[i3].arr_time, Right3, false) <= 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.train_details[i3].dep_time, Right2, false) >= 0 & Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.train_details[i3].dep_time, Right3, false) <= 0)
          {
            if (frmMainFormIPIS.train_details[i3].daily == 1)
              online_trains.get_online_trains_temp_info_read(i3);
            else if (frmMainFormIPIS.train_details[i3].specificdays == 1)
            {
              int index7 = 0;
              while (index7 < 7)
              {
                if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Strings.Trim(frmMainFormIPIS.train_details[i3].days[index7]), Strings.Trim(str1), false) == 0)
                {
                  online_trains.get_online_trains_temp_info_read(i3);
                  break;
                }
                checked { ++index7; }
              }
            }
            else if (frmMainFormIPIS.train_details[i3].period == 1)
            {
              if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Strings.Format((object) frmMainFormIPIS.train_details[i3].fromdt, "MM/dd/yyyy"), Strings.Format((object) Expression2, "MM/dd/yyyy"), false) <= 0 & Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Strings.Format((object) frmMainFormIPIS.train_details[i3].todt, "MM/dd/yyyy"), Strings.Format((object) Expression2, "MM/dd/yyyy"), false) >= 0)
                online_trains.get_online_trains_temp_info_read(i3);
            }
            else if (frmMainFormIPIS.train_details[i3].specificdate == 1)
            {
              int index8 = 0;
              while (index8 < 10)
              {
                if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Strings.Format((object) frmMainFormIPIS.train_details[i3].specificdates[index8], "MM/dd/yyyy"), Strings.Format((object) Expression2, "MM/dd/yyyy"), false) == 0)
                {
                  online_trains.get_online_trains_temp_info_read(i3);
                  break;
                }
                checked { ++index8; }
              }
            }
          }
label_98:
          checked { ++i3; }
        }
      }
      online_trains.sort_online_temp_data();
      online_trains.compare_online_data();
      online_trains.update_dgv();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num3 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private static void sort_online_temp_data()
  {
    int num1 = 0;
    int num2 = 0;
    frmMainFormIPIS.online_train_details[] onlineTrainDetailsArray = new frmMainFormIPIS.online_train_details[601];
    try
    {
      string Right = Strings.Format((object) DateAndTime.TimeOfDay, "HH:mm");
      num1 = 0;
      int index1 = 0;
      while (index1 < frmMainFormIPIS.online_train_temp_cnt)
      {
        if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.online_train_data_temp[index1].exp_arr_time, frmMainFormIPIS.online_train_data_temp[index1].exp_dep_time, false) <= 0)
        {
          if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.online_train_data_temp[index1].exp_arr_time, Right, false) >= 0)
          {
            onlineTrainDetailsArray[index1] = frmMainFormIPIS.online_train_data_temp[index1];
            onlineTrainDetailsArray[index1].final_time = frmMainFormIPIS.online_train_data_temp[index1].exp_arr_time;
          }
          else
          {
            onlineTrainDetailsArray[index1] = frmMainFormIPIS.online_train_data_temp[index1];
            onlineTrainDetailsArray[index1].final_time = frmMainFormIPIS.online_train_data_temp[index1].exp_dep_time;
          }
        }
        else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.online_train_data_temp[index1].exp_dep_time, Right, false) >= 0)
        {
          onlineTrainDetailsArray[index1] = frmMainFormIPIS.online_train_data_temp[index1];
          onlineTrainDetailsArray[index1].final_time = frmMainFormIPIS.online_train_data_temp[index1].exp_dep_time;
        }
        else
        {
          onlineTrainDetailsArray[index1] = frmMainFormIPIS.online_train_data_temp[index1];
          onlineTrainDetailsArray[index1].final_time = frmMainFormIPIS.online_train_data_temp[index1].exp_arr_time;
        }
        checked { ++index1; }
      }
      int index2 = 0;
      num2 = 0;
      while (index2 < index1)
      {
        int index3 = checked (index2 + 1);
        while (index3 < index1)
        {
          if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(onlineTrainDetailsArray[index2].final_time, onlineTrainDetailsArray[index3].final_time, false) <= 0)
          {
            checked { ++index3; }
          }
          else
          {
            frmMainFormIPIS.online_train_details onlineTrainDetails = onlineTrainDetailsArray[index2];
            onlineTrainDetailsArray[index2] = onlineTrainDetailsArray[index3];
            onlineTrainDetailsArray[index3] = onlineTrainDetails;
            checked { ++index3; }
          }
        }
        checked { ++index2; }
      }
      frmMainFormIPIS.online_train_data_temp = onlineTrainDetailsArray;
      frmMainFormIPIS.online_train_temp_cnt = index1;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num3 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }
}

}