using System;
using Microsoft.VisualBasic;

namespace ipis
{
    public static class taddb_msg
    {
        // Static arrays for different message types
        public static agdb_msg_structure[] agdb_msg = new agdb_msg_structure[255];
        public static agdb_msg_structure[] agdb_com_msg = new agdb_msg_structure[255];
        public static pdb_msg_structure[] pdb_msg = new pdb_msg_structure[255];
        public static mldb_msg_structure[] mldb_msg = new mldb_msg_structure[255];
        public static agdb_dis_structure[,] agdb_dis_brd = new agdb_dis_structure[255, 255];
        public static mldb_dis_brd_struct mldb_dis_brd = new mldb_dis_brd_struct(true);
        public static pdb_dis_structure[,] pdb_dis_brd = new pdb_dis_structure[255, 255];
        public static agdb_dis_structure[,] agdb_com_dis_brd = new agdb_dis_structure[255, 255];
        public static byte[] no_of_pdbs_pfno = new byte[255];
        public static byte[] no_of_agdbs_pfno = new byte[255];
        public static byte[] no_of_com_agdbs = new byte[255];
        public static byte def_mldb_addr = 0;
        public static string def_mldb_name = "";
        public static byte no_of_mldbs = 0;
        public static bool[] pdb_pf_status = new bool[255];
        public static byte[] pdb_default_display_msg = new byte[1000];
        public static byte[] mldb_default_display_msg = new byte[1000];



        // Initialize static arrays
        static taddb_msg()
        {
            for (int i = 0; i < 255; i++)
            {
                agdb_msg[i] = new agdb_msg_structure();
                agdb_com_msg[i] = new agdb_msg_structure();
                pdb_msg[i] = new pdb_msg_structure();
                mldb_msg[i] = new mldb_msg_structure();
                no_of_pdbs_pfno[i] = 0;
                no_of_agdbs_pfno[i] = 0;
                no_of_com_agdbs[i] = 0;

                for (int j = 0; j < 255; j++)
                {
                    agdb_dis_brd[i, j] = new agdb_dis_structure();
                    pdb_dis_brd[i, j] = new pdb_dis_structure();
                    agdb_com_dis_brd[i, j] = new agdb_dis_structure();
                }
            }
        }

        // Method to handle AGDB display messages
        public static byte agdb_display_message(int row_no, byte[] agdb_msg_data)
        {
            try
            {
                // Basic implementation - just return success
                // In a real implementation, this would process the message data
                // and send it to the appropriate display boards
                return 0; // Success
            }
            catch (Exception)
            {
                return 6; // Error code for failure
            }
        }

        // Method to handle AGDB communication display messages
        public static byte agdb_com_display_message(byte row_no, byte[] agdb_msg_data)
        {
            try
            {
                // Basic implementation - just return success
                // In a real implementation, this would process the message data
                // and send it to communication displays
                return 0; // Success
            }
            catch (Exception)
            {
                return 6; // Error code for failure
            }
        }

        // Structure definitions
        public struct agdb_msg_structure
        {
            public int no_of_msgs;
            public train_msg_structure[] tr_msg;

            public agdb_msg_structure(bool initialize)
            {
                no_of_msgs = 0;
                tr_msg = new train_msg_structure[255];
                for (int i = 0; i < 255; i++)
                {
                    tr_msg[i] = new train_msg_structure();
                }
            }
        }

        public struct pdb_msg_structure
        {
            public int no_of_msgs;
            public train_msg_structure[] tr_msg;

            public pdb_msg_structure(bool initialize)
            {
                no_of_msgs = 0;
                tr_msg = new train_msg_structure[255];
                for (int i = 0; i < 255; i++)
                {
                    tr_msg[i] = new train_msg_structure();
                }
            }
        }

        public struct mldb_msg_structure
        {
            public int no_of_msgs;
            public train_msg_structure[] tr_msg;

            public mldb_msg_structure(bool initialize)
            {
                no_of_msgs = 0;
                tr_msg = new train_msg_structure[255];
                for (int i = 0; i < 255; i++)
                {
                    tr_msg[i] = new train_msg_structure();
                }
            }
        }

        public struct train_msg_structure
        {
            public bool used;
            public string ar_time;
            public string dp_time;
            public int effect;
            public string platform_no;
            public string status;
            public string train_no;
            public int def_msg_no;

            public train_msg_structure(bool initialize)
            {
                used = false;
                ar_time = "";
                dp_time = "";
                effect = 0;
                platform_no = "";
                status = "";
                train_no = "";
                def_msg_no = 0;
            }
        }

        public struct agdb_dis_structure
        {
            public byte agdb_addr;
            public string agdb_name;
            public byte pdch_addr;
            public string pdch_name;
            public byte swithcing_time;
            public byte video_type;
            public string direction;
            public byte multicast_addr;
            public byte link_status;
            public bool shared_platform;
            public string shared_platform_no;

            public agdb_dis_structure(bool initialize)
            {
                agdb_addr = 0;
                agdb_name = "";
                pdch_addr = 0;
                pdch_name = "";
                swithcing_time = 0;
                video_type = 0;
                direction = "";
                multicast_addr = 0;
                link_status = 0;
                shared_platform = false;
                shared_platform_no = "";
            }
        }

        public struct mldb_dis_structure
        {
            public byte mldb_addr;
            public string mldb_name;
            public byte pdch_addr;
            public string pdch_name;
            public byte swithcing_time;
            public byte video_type;
            public string direction;
            public byte multicast_addr;
            public byte link_status;
            public byte effect_speed;
            public byte no_of_lines;
            public string mldb_type;
            public byte[] effect;

            public mldb_dis_structure(bool initialize)
            {
                mldb_addr = 0;
                mldb_name = "";
                pdch_addr = 0;
                pdch_name = "";
                swithcing_time = 0;
                video_type = 0;
                direction = "";
                multicast_addr = 0;
                link_status = 0;
                effect_speed = 0;
                no_of_lines = 0;
                mldb_type = "";
                effect = new byte[5];
            }
        }

        public struct pdb_dis_structure
        {
            public byte pdb_addr;
            public string pdb_name;
            public byte pdch_addr;
            public string pdch_name;
            public byte swithcing_time;
            public byte video_type;
            public string direction;
            public byte multicast_addr;
            public byte link_status;
            public bool shared_platform;
            public string shared_platform_no;
            public byte effect;
            public byte effect_speed;

            public pdb_dis_structure(bool initialize)
            {
                pdb_addr = 0;
                pdb_name = "";
                pdch_addr = 0;
                pdch_name = "";
                swithcing_time = 0;
                video_type = 0;
                direction = "";
                multicast_addr = 0;
                link_status = 0;
                shared_platform = false;
                shared_platform_no = "";
                effect = 0;
                effect_speed = 0;
            }
        }

        public struct mldb_dis_brd_struct
        {
            public byte no_of_mldbs;
            public mldb_dis_structure[] mdlb;

            public mldb_dis_brd_struct(bool initialize)
            {
                no_of_mldbs = 0;
                mdlb = new mldb_dis_structure[255];
                for (int i = 0; i < 255; i++)
                {
                    mdlb[i] = new mldb_dis_structure(true);
                }
            }
        }
    }
}