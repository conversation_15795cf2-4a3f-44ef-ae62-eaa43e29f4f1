using System;
using System.Runtime.InteropServices;

namespace IPIS.Utils
{
    public static class VolumeControl
    {
        [DllImport("winmm.dll")]
        private static extern int waveOutSetVolume(IntPtr hwo, uint dwVolume);

        [DllImport("winmm.dll")]
        private static extern int waveOutGetVolume(IntPtr hwo, out uint dwVolume);

        /// <summary>
        /// Sets the system volume (0-100)
        /// </summary>
        /// <param name="volume">Volume level from 0 to 100</param>
        public static void SetSystemVolume(int volume)
        {
            if (volume < 0) volume = 0;
            if (volume > 100) volume = 100;

            // Convert percentage to the format expected by Windows API
            uint volumeLevel = (uint)((volume * 65535) / 100);
            uint stereoVolume = (volumeLevel << 16) | volumeLevel; // Set both left and right channels

            waveOutSetVolume(IntPtr.Zero, stereoVolume);
        }

        /// <summary>
        /// Gets the current system volume (0-100)
        /// </summary>
        /// <returns>Current volume level from 0 to 100</returns>
        public static int GetSystemVolume()
        {
            uint volumeLevel;
            waveOutGetVolume(IntPtr.Zero, out volumeLevel);

            // Extract the volume from the left channel (lower 16 bits)
            uint leftVolume = volumeLevel & 0xFFFF;
            
            // Convert to percentage
            return (int)((leftVolume * 100) / 65535);
        }

        /// <summary>
        /// Mutes the system audio
        /// </summary>
        public static void Mute()
        {
            SetSystemVolume(0);
        }

        /// <summary>
        /// Unmutes the system audio and sets to a default volume
        /// </summary>
        public static void Unmute()
        {
            SetSystemVolume(50); // Default to 50%
        }
    }
} 