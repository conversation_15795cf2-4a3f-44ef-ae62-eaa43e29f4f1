using System;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using IPIS.Models;
using IPIS.Repositories;
using IPIS.Services;

namespace IPIS.Forms.Settings.Announcement
{
    public partial class SequenceForm : Form
    {
        private readonly SQLiteAnnouncementSequenceRepository _sequenceRepository;
        private readonly AnnouncementTemplateService _templateService;
        private readonly LanguageService _languageService;
        private AnnouncementSequence _sequence;
        private bool _isEditMode;

        // Controls
        private TextBox txtName;
        private ComboBox cboTemplate;
        private ComboBox cboLanguage;
        private CheckBox chkIsActive;
        private Button btnSave;
        private Button btnCancel;
        private Label lblName;
        private Label lblTemplate;
        private Label lblLanguage;

        public SequenceForm()
        {
            _sequenceRepository = new SQLiteAnnouncementSequenceRepository();
            _templateService = new AnnouncementTemplateService(new SQLiteAnnouncementTemplateRepository());
            _languageService = new LanguageService(new SQLiteLanguageRepository());
            _sequence = new AnnouncementSequence();
            _isEditMode = false;
            InitializeComponent();
            LoadComboBoxesAsync();
        }

        public SequenceForm(AnnouncementSequence sequence)
        {
            _sequenceRepository = new SQLiteAnnouncementSequenceRepository();
            _templateService = new AnnouncementTemplateService(new SQLiteAnnouncementTemplateRepository());
            _languageService = new LanguageService(new SQLiteLanguageRepository());
            _sequence = sequence;
            _isEditMode = true;
            InitializeComponent();
            LoadComboBoxesAsync();
        }

        private void InitializeComponent()
        {
            this.txtName = new TextBox();
            this.cboTemplate = new ComboBox();
            this.cboLanguage = new ComboBox();
            this.chkIsActive = new CheckBox();
            this.btnSave = new Button();
            this.btnCancel = new Button();
            this.lblName = new Label();
            this.lblTemplate = new Label();
            this.lblLanguage = new Label();

            // Form
            this.ClientSize = new Size(500, 300);
            this.Name = "SequenceForm";
            this.Text = _isEditMode ? "Edit Sequence" : "Add Sequence";
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            // Name Label
            this.lblName.AutoSize = true;
            this.lblName.Location = new Point(20, 20);
            this.lblName.Size = new Size(80, 15);
            this.lblName.Text = "Name:";
            this.lblName.Font = new Font("Segoe UI", 9F, FontStyle.Regular);

            // Name TextBox
            this.txtName.Location = new Point(120, 17);
            this.txtName.Size = new Size(350, 23);
            this.txtName.Font = new Font("Segoe UI", 9F, FontStyle.Regular);

            // Template Label
            this.lblTemplate.AutoSize = true;
            this.lblTemplate.Location = new Point(20, 60);
            this.lblTemplate.Size = new Size(80, 15);
            this.lblTemplate.Text = "Template:";
            this.lblTemplate.Font = new Font("Segoe UI", 9F, FontStyle.Regular);

            // Template ComboBox
            this.cboTemplate.Location = new Point(120, 57);
            this.cboTemplate.Size = new Size(350, 23);
            this.cboTemplate.DropDownStyle = ComboBoxStyle.DropDownList;
            this.cboTemplate.Font = new Font("Segoe UI", 9F, FontStyle.Regular);

            // Language Label
            this.lblLanguage.AutoSize = true;
            this.lblLanguage.Location = new Point(20, 100);
            this.lblLanguage.Size = new Size(80, 15);
            this.lblLanguage.Text = "Language:";
            this.lblLanguage.Font = new Font("Segoe UI", 9F, FontStyle.Regular);

            // Language ComboBox
            this.cboLanguage.Location = new Point(120, 97);
            this.cboLanguage.Size = new Size(350, 23);
            this.cboLanguage.DropDownStyle = ComboBoxStyle.DropDownList;
            this.cboLanguage.Font = new Font("Segoe UI", 9F, FontStyle.Regular);

            // Active CheckBox
            this.chkIsActive.AutoSize = true;
            this.chkIsActive.Location = new Point(120, 140);
            this.chkIsActive.Size = new Size(100, 19);
            this.chkIsActive.Text = "Active";
            this.chkIsActive.Font = new Font("Segoe UI", 9F, FontStyle.Regular);

            // Save Button
            this.btnSave.Location = new Point(280, 220);
            this.btnSave.Size = new Size(90, 30);
            this.btnSave.Text = "Save";
            this.btnSave.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
            this.btnSave.BackColor = Color.FromArgb(0, 122, 204);
            this.btnSave.ForeColor = Color.White;
            this.btnSave.FlatStyle = FlatStyle.Flat;
            this.btnSave.Click += new EventHandler(this.btnSave_Click);

            // Cancel Button
            this.btnCancel.Location = new Point(380, 220);
            this.btnCancel.Size = new Size(90, 30);
            this.btnCancel.Text = "Cancel";
            this.btnCancel.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
            this.btnCancel.BackColor = Color.FromArgb(108, 117, 125);
            this.btnCancel.ForeColor = Color.White;
            this.btnCancel.FlatStyle = FlatStyle.Flat;
            this.btnCancel.Click += new EventHandler(this.btnCancel_Click);

            // Add controls to form
            this.Controls.AddRange(new Control[] {
                this.lblName,
                this.txtName,
                this.lblTemplate,
                this.cboTemplate,
                this.lblLanguage,
                this.cboLanguage,
                this.chkIsActive,
                this.btnSave,
                this.btnCancel
            });
        }

        private async void LoadComboBoxesAsync()
        {
            try
            {
                // Load templates
                var templates = await _templateService.GetActiveTemplatesAsync();
                cboTemplate.Items.Clear();
                foreach (var template in templates)
                {
                    cboTemplate.Items.Add(new ComboBoxItem { Id = template.Id, Text = template.Name });
                }

                // Load languages
                var languages = await _languageService.GetAllLanguagesAsync();
                cboLanguage.Items.Clear();
                foreach (var language in languages.Where(l => l.IsActive))
                {
                    cboLanguage.Items.Add(new ComboBoxItem { Id = language.Id, Text = language.Name });
                }

                // Load sequence data if in edit mode
                if (_isEditMode)
                {
                    LoadSequenceData();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading data: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadSequenceData()
        {
            txtName.Text = _sequence.Name;
            chkIsActive.Checked = _sequence.IsActive;

            // Select template
            for (int i = 0; i < cboTemplate.Items.Count; i++)
            {
                var item = cboTemplate.Items[i] as ComboBoxItem;
                if (item?.Id == _sequence.TemplateId)
                {
                    cboTemplate.SelectedIndex = i;
                    break;
                }
            }

            // Select language
            for (int i = 0; i < cboLanguage.Items.Count; i++)
            {
                var item = cboLanguage.Items[i] as ComboBoxItem;
                if (item?.Id == _sequence.LanguageId)
                {
                    cboLanguage.SelectedIndex = i;
                    break;
                }
            }
        }

        private async void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                // Validate input
                if (string.IsNullOrWhiteSpace(txtName.Text))
                {
                    MessageBox.Show("Sequence name is required.", "Validation Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtName.Focus();
                    return;
                }

                if (cboTemplate.SelectedItem == null)
                {
                    MessageBox.Show("Please select a template.", "Validation Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    cboTemplate.Focus();
                    return;
                }

                if (cboLanguage.SelectedItem == null)
                {
                    MessageBox.Show("Please select a language.", "Validation Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    cboLanguage.Focus();
                    return;
                }

                // Update sequence object
                _sequence.Name = txtName.Text.Trim();
                _sequence.TemplateId = (cboTemplate.SelectedItem as ComboBoxItem).Id;
                _sequence.LanguageId = (cboLanguage.SelectedItem as ComboBoxItem).Id;
                _sequence.IsActive = chkIsActive.Checked;

                bool success;
                if (_isEditMode)
                {
                    success = await _sequenceRepository.UpdateSequenceAsync(_sequence);
                }
                else
                {
                    await _sequenceRepository.AddSequenceAsync(_sequence);
                    success = true;
                }

                if (success)
                {
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    MessageBox.Show("Failed to save sequence.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving sequence: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        // Helper class for ComboBox items
        private class ComboBoxItem
        {
            public int Id { get; set; }
            public string Text { get; set; }

            public override string ToString()
            {
                return Text;
            }
        }
    }
} 