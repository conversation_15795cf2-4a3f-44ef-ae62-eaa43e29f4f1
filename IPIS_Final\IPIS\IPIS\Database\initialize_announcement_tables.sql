-- Initialize Announcement Management Tables
-- Run this script to create the necessary tables for announcement management

-- Table: AnnouncementTemplates
CREATE TABLE IF NOT EXISTS AnnouncementTemplates (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    Name TEXT NOT NULL UNIQUE,
    Description TEXT,
    ArrivalDeparture TEXT NOT NULL DEFAULT 'A', -- A for Arrival, D for Departure
    IsActive BOOLEAN NOT NULL DEFAULT 1,
    CreatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt DATETIME
);

-- Table: AnnouncementSequences
CREATE TABLE IF NOT EXISTS AnnouncementSequences (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    TemplateId INTEGER NOT NULL,
    LanguageId INTEGER NOT NULL,
    Name TEXT NOT NULL,
    IsActive BOOLEAN NOT NULL DEFAULT 1,
    CreatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt DATETIME,
    FOREIG<PERSON> KEY (TemplateId) REFERENCES AnnouncementTemplates(Id) ON DELETE CASCADE,
    FOREIGN KEY (LanguageId) REFERENCES Languages(Id) ON DELETE CASCADE,
    UNIQUE(TemplateId, LanguageId)
);

-- Table: SequenceItems
CREATE TABLE IF NOT EXISTS SequenceItems (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    SequenceId INTEGER NOT NULL,
    OrderIndex INTEGER NOT NULL,
    Type INTEGER NOT NULL, -- 1 = AudioFile, 2 = Placeholder
    Content TEXT NOT NULL, -- Audio file path or placeholder name
    Description TEXT,
    IsActive BOOLEAN NOT NULL DEFAULT 1,
    CreatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt DATETIME,
    FOREIGN KEY (SequenceId) REFERENCES AnnouncementSequences(Id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_announcement_templates_active ON AnnouncementTemplates(IsActive);
CREATE INDEX IF NOT EXISTS idx_announcement_templates_ad ON AnnouncementTemplates(ArrivalDeparture);
CREATE INDEX IF NOT EXISTS idx_announcement_sequences_template ON AnnouncementSequences(TemplateId);
CREATE INDEX IF NOT EXISTS idx_announcement_sequences_language ON AnnouncementSequences(LanguageId);
CREATE INDEX IF NOT EXISTS idx_announcement_sequences_active ON AnnouncementSequences(IsActive);
CREATE INDEX IF NOT EXISTS idx_sequence_items_sequence ON SequenceItems(SequenceId);
CREATE INDEX IF NOT EXISTS idx_sequence_items_order ON SequenceItems(SequenceId, OrderIndex);
CREATE INDEX IF NOT EXISTS idx_sequence_items_active ON SequenceItems(IsActive);

-- Insert default announcement templates
INSERT OR IGNORE INTO AnnouncementTemplates (Name, Description, ArrivalDeparture) VALUES
('IS ARRIVING ON', 'Train is arriving on platform', 'A'),
('HAS ARRIVED ON', 'Train has arrived on platform', 'A'),
('WILL ARRIVE SHORTLY', 'Train will arrive shortly on platform', 'A'),
('RUNNING RIGHT TIME', 'Train is running on time', 'A'),
('RUNNING LATE', 'Train is running late', 'A'),
('INDEFINITE LATE', 'Train is indefinitely late', 'A'),
('CANCELLED', 'Train has been cancelled', 'A'),
('PLATFORM CHANGED', 'Platform has been changed', 'A'),
('TERMINATED', 'Train has been terminated', 'A'),
('DEPARTING', 'Train is departing from platform', 'D'),
('WILL DEPART SHORTLY', 'Train will depart shortly from platform', 'D'),
('HAS DEPARTED', 'Train has departed from platform', 'D'),
('DEPARTURE DELAYED', 'Train departure is delayed', 'D');

-- Insert default sequence for RUNNING RIGHT TIME (English)
INSERT OR IGNORE INTO AnnouncementSequences (TemplateId, LanguageId, Name) 
SELECT t.Id, l.Id, 'RUNNING RIGHT TIME - English'
FROM AnnouncementTemplates t, Languages l 
WHERE t.Name = 'RUNNING RIGHT TIME' AND l.Code = 'EN';

-- Insert default sequence for RUNNING RIGHT TIME (Hindi)
INSERT OR IGNORE INTO AnnouncementSequences (TemplateId, LanguageId, Name) 
SELECT t.Id, l.Id, 'RUNNING RIGHT TIME - Hindi'
FROM AnnouncementTemplates t, Languages l 
WHERE t.Name = 'RUNNING RIGHT TIME' AND l.Code = 'HI';

-- Insert sample sequence items for RUNNING RIGHT TIME (English)
INSERT OR IGNORE INTO SequenceItems (SequenceId, OrderIndex, Type, Content, Description)
SELECT s.Id, 1, 1, 'SPL/TADA.wav', 'Attention bell'
FROM AnnouncementSequences s
INNER JOIN AnnouncementTemplates t ON s.TemplateId = t.Id
INNER JOIN Languages l ON s.LanguageId = l.Id
WHERE t.Name = 'RUNNING RIGHT TIME' AND l.Code = 'EN';

INSERT OR IGNORE INTO SequenceItems (SequenceId, OrderIndex, Type, Content, Description)
SELECT s.Id, 2, 1, 'ENGLISH/STD/STD1.wav', 'May I have your attention please'
FROM AnnouncementSequences s
INNER JOIN AnnouncementTemplates t ON s.TemplateId = t.Id
INNER JOIN Languages l ON s.LanguageId = l.Id
WHERE t.Name = 'RUNNING RIGHT TIME' AND l.Code = 'EN';

INSERT OR IGNORE INTO SequenceItems (SequenceId, OrderIndex, Type, Content, Description)
SELECT s.Id, 3, 2, 'TRAIN_NUMBER', 'Train number'
FROM AnnouncementSequences s
INNER JOIN AnnouncementTemplates t ON s.TemplateId = t.Id
INNER JOIN Languages l ON s.LanguageId = l.Id
WHERE t.Name = 'RUNNING RIGHT TIME' AND l.Code = 'EN';

INSERT OR IGNORE INTO SequenceItems (SequenceId, OrderIndex, Type, Content, Description)
SELECT s.Id, 4, 2, 'TRAIN_NAME', 'Train name'
FROM AnnouncementSequences s
INNER JOIN AnnouncementTemplates t ON s.TemplateId = t.Id
INNER JOIN Languages l ON s.LanguageId = l.Id
WHERE t.Name = 'RUNNING RIGHT TIME' AND l.Code = 'EN';

INSERT OR IGNORE INTO SequenceItems (SequenceId, OrderIndex, Type, Content, Description)
SELECT s.Id, 5, 1, 'ENGLISH/STD/STD2.wav', 'Is running on time'
FROM AnnouncementSequences s
INNER JOIN AnnouncementTemplates t ON s.TemplateId = t.Id
INNER JOIN Languages l ON s.LanguageId = l.Id
WHERE t.Name = 'RUNNING RIGHT TIME' AND l.Code = 'EN';

-- Insert sample sequence items for RUNNING RIGHT TIME (Hindi)
INSERT OR IGNORE INTO SequenceItems (SequenceId, OrderIndex, Type, Content, Description)
SELECT s.Id, 1, 1, 'SPL/TADA.wav', 'Attention bell'
FROM AnnouncementSequences s
INNER JOIN AnnouncementTemplates t ON s.TemplateId = t.Id
INNER JOIN Languages l ON s.LanguageId = l.Id
WHERE t.Name = 'RUNNING RIGHT TIME' AND l.Code = 'HI';

INSERT OR IGNORE INTO SequenceItems (SequenceId, OrderIndex, Type, Content, Description)
SELECT s.Id, 2, 1, 'HINDI/STD/STD1.wav', 'Kirpya dhayn diji'
FROM AnnouncementSequences s
INNER JOIN AnnouncementTemplates t ON s.TemplateId = t.Id
INNER JOIN Languages l ON s.LanguageId = l.Id
WHERE t.Name = 'RUNNING RIGHT TIME' AND l.Code = 'HI';

INSERT OR IGNORE INTO SequenceItems (SequenceId, OrderIndex, Type, Content, Description)
SELECT s.Id, 3, 2, 'TRAIN_NUMBER', 'Train number'
FROM AnnouncementSequences s
INNER JOIN AnnouncementTemplates t ON s.TemplateId = t.Id
INNER JOIN Languages l ON s.LanguageId = l.Id
WHERE t.Name = 'RUNNING RIGHT TIME' AND l.Code = 'HI';

INSERT OR IGNORE INTO SequenceItems (SequenceId, OrderIndex, Type, Content, Description)
SELECT s.Id, 4, 2, 'TRAIN_NAME', 'Train name'
FROM AnnouncementSequences s
INNER JOIN AnnouncementTemplates t ON s.TemplateId = t.Id
INNER JOIN Languages l ON s.LanguageId = l.Id
WHERE t.Name = 'RUNNING RIGHT TIME' AND l.Code = 'HI';

INSERT OR IGNORE INTO SequenceItems (SequenceId, OrderIndex, Type, Content, Description)
SELECT s.Id, 5, 1, 'HINDI/STD/STD2.wav', 'Is running on time (Hindi)'
FROM AnnouncementSequences s
INNER JOIN AnnouncementTemplates t ON s.TemplateId = t.Id
INNER JOIN Languages l ON s.LanguageId = l.Id
WHERE t.Name = 'RUNNING RIGHT TIME' AND l.Code = 'HI';

PRAGMA foreign_keys = ON; 