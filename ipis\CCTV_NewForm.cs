// Decompiled with JetBrains decompiler
// Type: ipis.CCTV_NewForm
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;
using ScrollingTextControl;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Runtime.CompilerServices;
using System.Text;
using System.Windows.Forms;

namespace ipis
{

[DesignerGenerated]
public class CCTV_NewForm : frmCCTV
{
  private static List<WeakReference> __ENCList = new List<WeakReference>();
  private IContainer components;
  [AccessedThroughProperty("Timer1")]
  private Timer _Timer1;
  [AccessedThroughProperty("DataGridView1")]
  private DataGridView _DataGridView1;
  [AccessedThroughProperty("IPISDAILYTRAINSBindingSource")]
  private BindingSource _IPISDAILYTRAINSBindingSource;
  [AccessedThroughProperty("CCTV_Header_lbl")]
  private Label _CCTV_Header_lbl;
  [AccessedThroughProperty("IPISDAILYTRAINSBindingSource1")]
  private BindingSource _IPISDAILYTRAINSBindingSource1;
  [AccessedThroughProperty("Timer2")]
  private Timer _Timer2;
  [AccessedThroughProperty("DataGridView3")]
  private DataGridView _DataGridView3;
  [AccessedThroughProperty("DataGridView2")]
  private DataGridView _DataGridView2;
  [AccessedThroughProperty("LblCCTV_Header")]
  private Label _LblCCTV_Header;
  [AccessedThroughProperty("lblHeaderText")]
  private Label _lblHeaderText;
  [AccessedThroughProperty("lblHeaderTime")]
  private Label _lblHeaderTime;
  [AccessedThroughProperty("Timer3")]
  private Timer _Timer3;
  [AccessedThroughProperty("scrolling_timer")]
  private Timer _scrolling_timer;
  public int Fontforall;
  public string CCTVMessage;
  public DataSet CCTVData;
  public DataSet TrainCoachs;
  public string CCTVHeading;
  private int row_cnt;
  private int scroll_cnt;
  private int cnt_rows;
  private bool eng_found;
  private bool reg_found;
  private bool hin_found;
  private int no_rows;
  private bool video_data_entered;
  private string scrolledString;
  private string[] myStrings;
  private int position;
  private string[] FillGridLine;
  private string[] CloseAppLine;
  private string[] VoiceAnnLine;
  private string CCTVStatusPath;
  private StringBuilder CurrentCoaches;
  private int glbCurrentRow;
  public int CCTVFormLeft;
  private int TimerInterval;
  private string dat;
  private string tim;
  private int LangID;
  private int[] LangFkeys;
  private ScrollingText MyScrollControl;
  private Label TrainDetailsScrollControl;
  private Label CoachsScrollControl;
  private Label TrainInfo;
  private string[] TrainArray;
  private int trainFkey;
  private int TimerIntervel2;
  private int TimerIntervel3;
  private int lastrow;
  private int TInt;
  private int A;
  private int B;
  private int C;
  private int D;
  private int F;
  private int lang_id;
  private int tm_tick;
  private bool start_display;
  private int start_no;
  private int count;
  private int q;
  private int Gbl;
  private int dc;
  private int TI;
  private int coach_count;
  private int cc;

  [DebuggerNonUserCode]
  static CCTV_NewForm()
  {
  }

  public CCTV_NewForm()
  {
    this.Load += new EventHandler(this.CCTV_NewForm_Load);
    CCTV_NewForm.__ENCAddToList((object) this);
    this.Fontforall = 1;
    this.CCTVMessage = "";
    this.CCTVData = new DataSet();
    this.TrainCoachs = new DataSet();
    this.CCTVHeading = "";
    this.row_cnt = 0;
    this.scroll_cnt = 0;
    this.cnt_rows = 0;
    this.eng_found = true;
    this.reg_found = false;
    this.hin_found = true;
    this.no_rows = 0;
    this.video_data_entered = false;
    this.scrolledString = this.CCTVMessage;
    this.myStrings = new string[checked (this.scrolledString.Length - 1 + 1)];
    this.position = -1;
    this.CCTVStatusPath = "c:\\IPIS\\shared_info\\CCTVStatus.txt";
    this.CurrentCoaches = new StringBuilder();
    this.glbCurrentRow = 1;
    this.TimerInterval = 0;
    this.LangID = 0;
    this.LangFkeys = new int[3]{ 0, 0, 0 };
    this.MyScrollControl = new ScrollingText();
    this.TrainDetailsScrollControl = new Label();
    this.CoachsScrollControl = new Label();
    this.TrainInfo = new Label();
    this.TrainArray = new string[5];
    this.trainFkey = 0;
    this.TimerIntervel2 = 0;
    this.TimerIntervel3 = 0;
    this.lastrow = 0;
    this.TInt = 0;
    this.A = 0;
    this.B = 0;
    this.C = 0;
    this.D = 0;
    this.F = 0;
    this.lang_id = 0;
    this.tm_tick = 0;
    this.start_display = false;
    this.start_no = 0;
    this.count = 0;
    this.q = 0;
    this.Gbl = 0;
    this.dc = 0;
    this.TI = 0;
    this.coach_count = 0;
    this.cc = 0;
    this.InitializeComponent();
  }

  [DebuggerNonUserCode]
  private static void __ENCAddToList(object value)
  {
    lock (CCTV_NewForm.__ENCList)
    {
      if (CCTV_NewForm.__ENCList.Count == CCTV_NewForm.__ENCList.Capacity)
      {
        int index1 = 0;
        int num = checked (CCTV_NewForm.__ENCList.Count - 1);
        int index2 = 0;
        while (index2 <= num)
        {
          if (CCTV_NewForm.__ENCList[index2].IsAlive)
          {
            if (index2 != index1)
              CCTV_NewForm.__ENCList[index1] = CCTV_NewForm.__ENCList[index2];
            checked { ++index1; }
          }
          checked { ++index2; }
        }
        CCTV_NewForm.__ENCList.RemoveRange(index1, checked (CCTV_NewForm.__ENCList.Count - index1));
        CCTV_NewForm.__ENCList.Capacity = CCTV_NewForm.__ENCList.Count;
      }
      CCTV_NewForm.__ENCList.Add(new WeakReference(RuntimeHelpers.GetObjectValue(value)));
    }
  }

  [DebuggerStepThrough]
  private void InitializeComponent()
  {
    this.components = (IContainer) new System.ComponentModel.Container();
    DataGridViewCellStyle gridViewCellStyle1 = new DataGridViewCellStyle();
    DataGridViewCellStyle gridViewCellStyle2 = new DataGridViewCellStyle();
    DataGridViewCellStyle gridViewCellStyle3 = new DataGridViewCellStyle();
    DataGridViewCellStyle gridViewCellStyle4 = new DataGridViewCellStyle();
    DataGridViewCellStyle gridViewCellStyle5 = new DataGridViewCellStyle();
    this.Timer1 = new Timer(this.components);
    this.DataGridView1 = new DataGridView();
    this.IPISDAILYTRAINSBindingSource = new BindingSource(this.components);
    this.CCTV_Header_lbl = new Label();
    this.IPISDAILYTRAINSBindingSource1 = new BindingSource(this.components);
    this.Timer2 = new Timer(this.components);
    this.DataGridView3 = new DataGridView();
    this.DataGridView2 = new DataGridView();
    this.LblCCTV_Header = new Label();
    this.lblHeaderText = new Label();
    this.lblHeaderTime = new Label();
    this.Timer3 = new Timer(this.components);
    this.scrolling_timer = new Timer(this.components);
    ((ISupportInitialize) this.DataGridView1).BeginInit();
    ((ISupportInitialize) this.IPISDAILYTRAINSBindingSource).BeginInit();
    ((ISupportInitialize) this.IPISDAILYTRAINSBindingSource1).BeginInit();
    ((ISupportInitialize) this.DataGridView3).BeginInit();
    ((ISupportInitialize) this.DataGridView2).BeginInit();
    this.SuspendLayout();
    this.Timer1.Interval = 200;
    gridViewCellStyle1.BackColor = Color.White;
    this.DataGridView1.AlternatingRowsDefaultCellStyle = gridViewCellStyle1;
    this.DataGridView1.BackgroundColor = Color.FromArgb(192 /*0xC0*/, (int) byte.MaxValue, (int) byte.MaxValue);
    this.DataGridView1.BorderStyle = BorderStyle.None;
    this.DataGridView1.CellBorderStyle = DataGridViewCellBorderStyle.None;
    this.DataGridView1.ClipboardCopyMode = DataGridViewClipboardCopyMode.Disable;
    this.DataGridView1.ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.None;
    gridViewCellStyle2.Alignment = DataGridViewContentAlignment.MiddleLeft;
    gridViewCellStyle2.BackColor = SystemColors.Control;
    gridViewCellStyle2.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
    gridViewCellStyle2.ForeColor = SystemColors.WindowText;
    gridViewCellStyle2.SelectionBackColor = SystemColors.Highlight;
    gridViewCellStyle2.SelectionForeColor = SystemColors.ControlDark;
    gridViewCellStyle2.WrapMode = DataGridViewTriState.True;
    this.DataGridView1.ColumnHeadersDefaultCellStyle = gridViewCellStyle2;
    this.DataGridView1.ColumnHeadersHeight = 20;
    this.DataGridView1.ColumnHeadersVisible = false;
    gridViewCellStyle3.Alignment = DataGridViewContentAlignment.MiddleCenter;
    gridViewCellStyle3.BackColor = SystemColors.Window;
    gridViewCellStyle3.Font = new Font("Verdana", 18f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    gridViewCellStyle3.ForeColor = Color.FromArgb(224 /*0xE0*/, 224 /*0xE0*/, 224 /*0xE0*/);
    gridViewCellStyle3.SelectionBackColor = Color.FromArgb(224 /*0xE0*/, 224 /*0xE0*/, 224 /*0xE0*/);
    gridViewCellStyle3.SelectionForeColor = SystemColors.HighlightText;
    gridViewCellStyle3.WrapMode = DataGridViewTriState.False;
    this.DataGridView1.DefaultCellStyle = gridViewCellStyle3;
    this.DataGridView1.GridColor = Color.FromArgb(128 /*0x80*/, (int) byte.MaxValue, (int) byte.MaxValue);
    DataGridView dataGridView1_1 = this.DataGridView1;
    Point point1 = new Point(2, 105);
    Point point2 = point1;
    dataGridView1_1.Location = point2;
    this.DataGridView1.Name = "DataGridView1";
    this.DataGridView1.RowHeadersBorderStyle = DataGridViewHeaderBorderStyle.None;
    gridViewCellStyle4.Alignment = DataGridViewContentAlignment.MiddleLeft;
    gridViewCellStyle4.BackColor = SystemColors.Control;
    gridViewCellStyle4.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
    gridViewCellStyle4.ForeColor = SystemColors.WindowText;
    gridViewCellStyle4.SelectionBackColor = SystemColors.Highlight;
    gridViewCellStyle4.SelectionForeColor = SystemColors.HighlightText;
    gridViewCellStyle4.WrapMode = DataGridViewTriState.True;
    this.DataGridView1.RowHeadersDefaultCellStyle = gridViewCellStyle4;
    this.DataGridView1.RowHeadersVisible = false;
    this.DataGridView1.RowHeadersWidthSizeMode = DataGridViewRowHeadersWidthSizeMode.AutoSizeToFirstHeader;
    gridViewCellStyle5.BackColor = SystemColors.Control;
    gridViewCellStyle5.Font = new Font("Microsoft Sans Serif", 12f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    gridViewCellStyle5.ForeColor = Color.Black;
    this.DataGridView1.RowsDefaultCellStyle = gridViewCellStyle5;
    this.DataGridView1.RowTemplate.Height = 50;
    this.DataGridView1.ScrollBars = ScrollBars.None;
    this.DataGridView1.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
    DataGridView dataGridView1_2 = this.DataGridView1;
    Size size1 = new Size(1010, 446);
    Size size2 = size1;
    dataGridView1_2.Size = size2;
    this.DataGridView1.StandardTab = true;
    this.DataGridView1.TabIndex = 0;
    this.IPISDAILYTRAINSBindingSource.DataMember = "tbl_Daily_Trains";
    this.CCTV_Header_lbl.BackColor = Color.FromArgb(0, 0, 64 /*0x40*/);
    this.CCTV_Header_lbl.BorderStyle = BorderStyle.Fixed3D;
    this.CCTV_Header_lbl.FlatStyle = FlatStyle.System;
    Label cctvHeaderLbl1 = this.CCTV_Header_lbl;
    point1 = new Point(2, 61);
    Point point3 = point1;
    cctvHeaderLbl1.Location = point3;
    this.CCTV_Header_lbl.Name = "CCTV_Header_lbl";
    Label cctvHeaderLbl2 = this.CCTV_Header_lbl;
    size1 = new Size(1010, 49);
    Size size3 = size1;
    cctvHeaderLbl2.Size = size3;
    this.CCTV_Header_lbl.TabIndex = 1;
    this.CCTV_Header_lbl.Visible = false;
    this.IPISDAILYTRAINSBindingSource1.DataMember = "tbl_Daily_Trains";
    this.DataGridView3.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
    DataGridView dataGridView3_1 = this.DataGridView3;
    point1 = new Point(2, 134);
    Point point4 = point1;
    dataGridView3_1.Location = point4;
    this.DataGridView3.Name = "DataGridView3";
    DataGridView dataGridView3_2 = this.DataGridView3;
    size1 = new Size(1009, 237);
    Size size4 = size1;
    dataGridView3_2.Size = size4;
    this.DataGridView3.TabIndex = 3;
    this.DataGridView3.Visible = false;
    this.DataGridView2.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
    DataGridView dataGridView2_1 = this.DataGridView2;
    point1 = new Point(2, 510);
    Point point5 = point1;
    dataGridView2_1.Location = point5;
    this.DataGridView2.Name = "DataGridView2";
    DataGridView dataGridView2_2 = this.DataGridView2;
    size1 = new Size(992, 154);
    Size size5 = size1;
    dataGridView2_2.Size = size5;
    this.DataGridView2.TabIndex = 5;
    this.DataGridView2.Visible = false;
    this.LblCCTV_Header.BackColor = Color.MediumTurquoise;
    this.LblCCTV_Header.BorderStyle = BorderStyle.Fixed3D;
    this.LblCCTV_Header.FlatStyle = FlatStyle.System;
    this.LblCCTV_Header.ForeColor = SystemColors.ControlText;
    Label lblCctvHeader1 = this.LblCCTV_Header;
    point1 = new Point(2, 0);
    Point point6 = point1;
    lblCctvHeader1.Location = point6;
    this.LblCCTV_Header.Name = "LblCCTV_Header";
    Label lblCctvHeader2 = this.LblCCTV_Header;
    size1 = new Size(184, 45);
    Size size6 = size1;
    lblCctvHeader2.Size = size6;
    this.LblCCTV_Header.TabIndex = 6;
    this.lblHeaderText.BackColor = Color.MediumTurquoise;
    this.lblHeaderText.ForeColor = Color.DarkBlue;
    Label lblHeaderText1 = this.lblHeaderText;
    point1 = new Point(182, 0);
    Point point7 = point1;
    lblHeaderText1.Location = point7;
    this.lblHeaderText.Name = "lblHeaderText";
    Label lblHeaderText2 = this.lblHeaderText;
    size1 = new Size(657, 45);
    Size size7 = size1;
    lblHeaderText2.Size = size7;
    this.lblHeaderText.TabIndex = 7;
    this.lblHeaderTime.BackColor = Color.MediumTurquoise;
    this.lblHeaderTime.ForeColor = SystemColors.ControlText;
    Label lblHeaderTime1 = this.lblHeaderTime;
    point1 = new Point(835, 0);
    Point point8 = point1;
    lblHeaderTime1.Location = point8;
    this.lblHeaderTime.Name = "lblHeaderTime";
    Label lblHeaderTime2 = this.lblHeaderTime;
    size1 = new Size(176 /*0xB0*/, 45);
    Size size8 = size1;
    lblHeaderTime2.Size = size8;
    this.lblHeaderTime.TabIndex = 8;
    this.scrolling_timer.Interval = 300;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.BackColor = Color.FromArgb(192 /*0xC0*/, (int) byte.MaxValue, (int) byte.MaxValue);
    size1 = new Size(1024 /*0x0400*/, 704);
    this.ClientSize = size1;
    this.Controls.Add((Control) this.lblHeaderTime);
    this.Controls.Add((Control) this.lblHeaderText);
    this.Controls.Add((Control) this.LblCCTV_Header);
    this.Controls.Add((Control) this.DataGridView2);
    this.Controls.Add((Control) this.DataGridView3);
    this.Controls.Add((Control) this.CCTV_Header_lbl);
    this.Controls.Add((Control) this.DataGridView1);
    this.FormBorderStyle = FormBorderStyle.None;
    this.Name = "CCTV_NewForm";
    this.Text = "CCTV";
    ((ISupportInitialize) this.DataGridView1).EndInit();
    ((ISupportInitialize) this.IPISDAILYTRAINSBindingSource).EndInit();
    ((ISupportInitialize) this.IPISDAILYTRAINSBindingSource1).EndInit();
    ((ISupportInitialize) this.DataGridView3).EndInit();
    ((ISupportInitialize) this.DataGridView2).EndInit();
    this.ResumeLayout(false);
  }

  internal virtual Timer Timer1
  {
    [DebuggerNonUserCode] get { return this._Timer1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.Timer1_Tick);
      if (this._Timer1 != null)
        this._Timer1.Tick -= eventHandler;
      this._Timer1 = value;
      if (this._Timer1 == null)
        return;
      this._Timer1.Tick += eventHandler;
    }
  }

  internal virtual DataGridView DataGridView1
  {
    [DebuggerNonUserCode] get { return this._DataGridView1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._DataGridView1 = value;
    }
  }

  internal virtual BindingSource IPISDAILYTRAINSBindingSource
  {
    [DebuggerNonUserCode] get { return this._IPISDAILYTRAINSBindingSource; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._IPISDAILYTRAINSBindingSource = value;
    }
  }

  internal virtual Label CCTV_Header_lbl
  {
    [DebuggerNonUserCode] get { return this._CCTV_Header_lbl; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._CCTV_Header_lbl = value;
    }
  }

  internal virtual BindingSource IPISDAILYTRAINSBindingSource1
  {
    [DebuggerNonUserCode] get { return this._IPISDAILYTRAINSBindingSource1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._IPISDAILYTRAINSBindingSource1 = value;
    }
  }

  internal virtual Timer Timer2
  {
    [DebuggerNonUserCode] get { return this._Timer2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Timer2 = value; }
  }

  internal virtual DataGridView DataGridView3
  {
    [DebuggerNonUserCode] get { return this._DataGridView3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._DataGridView3 = value;
    }
  }

  internal virtual DataGridView DataGridView2
  {
    [DebuggerNonUserCode] get { return this._DataGridView2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._DataGridView2 = value;
    }
  }

  internal virtual Label LblCCTV_Header
  {
    [DebuggerNonUserCode] get { return this._LblCCTV_Header; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._LblCCTV_Header = value;
    }
  }

  internal virtual Label lblHeaderText
  {
    [DebuggerNonUserCode] get { return this._lblHeaderText; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblHeaderText = value;
    }
  }

  internal virtual Label lblHeaderTime
  {
    [DebuggerNonUserCode] get { return this._lblHeaderTime; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblHeaderTime = value;
    }
  }

  internal virtual Timer Timer3
  {
    [DebuggerNonUserCode] get { return this._Timer3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.Timer3_Tick);
      if (this._Timer3 != null)
        this._Timer3.Tick -= eventHandler;
      this._Timer3 = value;
      if (this._Timer3 == null)
        return;
      this._Timer3.Tick += eventHandler;
    }
  }

  internal virtual Timer scrolling_timer
  {
    [DebuggerNonUserCode] get { return this._scrolling_timer; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.scrolling_timer_Tick);
      if (this._scrolling_timer != null)
        this._scrolling_timer.Tick -= eventHandler;
      this._scrolling_timer = value;
      if (this._scrolling_timer == null)
        return;
      this._scrolling_timer.Tick += eventHandler;
    }
  }

  public void SetCCTVFormLeft(int FormLeft)
  {
    this.CCTVFormLeft = FormLeft;
  }

  public void DrawTittle()
  {
    Label[] labelArray = new Label[6];
    int index1 = 0;
    do
    {
      labelArray[index1] = new Label();
      labelArray[index1].Top = 50;
      checked { ++index1; }
    }
    while (index1 <= 5);
    labelArray[0].Text = "TrainNo";
    labelArray[1].Text = "Train Description";
    labelArray[2].Text = "Status";
    labelArray[3].Text = "Exp Arr.Time";
    labelArray[4].Text = "Exp Dep.Time";
    labelArray[5].Text = "PF No";
    labelArray[0].Left = 5;
    labelArray[1].Left = 150;
    labelArray[2].Left = 600;
    labelArray[3].Left = 450;
    labelArray[4].Left = 670;
    labelArray[5].Left = 900;
    labelArray[2].Visible = false;
    int index2 = 0;
    do
    {
      labelArray[index2].AutoSize = true;
      labelArray[index2].ForeColor = Color.Red;
      labelArray[index2].Font = (Font) this.allfont();
      this.Controls.Add((Control) labelArray[index2]);
      checked { ++index2; }
    }
    while (index2 <= 5);
  }

  public void DrawLine()
  {
    Graphics.FromImage((Image) new Bitmap(10, 10));
    Pen pen = new Pen(Color.Blue, 3f);
    this.CreateGraphics().DrawLine(pen, 0, 0, 10, 10);
  }

  private void ClearTextFile()
  {
    try
    {
      if (!File.Exists(this.CCTVStatusPath))
        return;
      new FileStream(this.CCTVStatusPath, FileMode.Truncate).Close();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
  }

  private void AppendTexttoFile(string FillGrid, string CloseApp, string VoiceAnn)
  {
    try
    {
      if (!File.Exists(this.CCTVStatusPath))
        return;
      FileStream fileStream = new FileStream(this.CCTVStatusPath, FileMode.Truncate, FileAccess.Write);
      StreamWriter streamWriter = new StreamWriter((Stream) fileStream);
      streamWriter.WriteLine("FillGrid#{FillGrid}\r\nCloseApp#{CloseApp}\r\nVoiveAnn#{VoiceAnn}\r\n");
      streamWriter.Close();
      fileStream.Close();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
  }

  private void ReadTextFile()
  {
    try
    {
      TextReader textReader = (TextReader) new StreamReader(this.CCTVStatusPath);
      string[] strArray = new string[checked (15 - 1 + 1)];
      int index = 1;
      string Left = textReader.ReadLine();
      strArray[0] = Left;
      while (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Left, "", false) != 0)
      {
        Left = textReader.ReadLine();
        strArray[index] = Left;
        this.FillGridLine = strArray[0].Split('#');
        this.CloseAppLine = strArray[1].Split('#');
        checked { ++index; }
      }
      this.VoiceAnnLine = strArray[2].Split('#');
      textReader.Close();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
  }

  private void CCTV_NewForm_Load(object sender, EventArgs e)
  {
    this.ClearTextFile();
    this.AppendTexttoFile("False", "False", "False");
    this.ReadTextFile();
    this.scrolledString = this.CCTVMessage;
    this.myStrings = new string[checked (this.scrolledString.Length - 1 + 1)];
    this.fillGrid2();
    this.IniCCTVSystem();
    this.dat = DateTime.Now.ToShortDateString().ToString();
    this.tim = Conversions.ToString(DateTime.Now.Hour) + ":" + Conversions.ToString(DateTime.Now.Minute) + ":" + Conversions.ToString(DateTime.Now.Second);
    this.LblCCTV_Header.Text = this.dat;
    this.lblHeaderText.Text = this.CCTVHeading;
    this.LblCCTV_Header.TextAlign = ContentAlignment.MiddleLeft;
    this.lblHeaderText.TextAlign = ContentAlignment.MiddleCenter;
    this.lblHeaderTime.Text = this.tim;
    this.lblHeaderTime.TextAlign = ContentAlignment.MiddleRight;
    this.LblCCTV_Header.Font = (Font) this.allfont();
    this.lblHeaderText.Font = (Font) this.allfont();
    this.lblHeaderTime.Font = (Font) this.allfont();
    this.DataGridView1.Width = this.Width;
    this.DrawTittle();
    this.DrawLine();
    this.LblCCTV_Header.Visible = true;
    this.DataGridView1.Columns.Add("TrainNo", "Train No");
    this.DataGridView1.Columns.Add("TrainName", "Train Name");
    this.DataGridView1.Columns.Add("ArrTime", "Arr Time");
    this.DataGridView1.Columns.Add("DeptTime", "Dept Time");
    this.DataGridView1.Columns.Add("PFNo", "PF No");
    this.DataGridView1.Columns[0].Width = 130;
    this.DataGridView1.Columns[1].Width = 370;
    this.DataGridView1.Columns[2].Width = 200;
    this.DataGridView1.Columns[3].Width = 205;
    this.DataGridView1.Columns[4].Width = 100;
    ((Control) this.MyScrollControl).Name = "MyScrollControl";
    this.MyScrollControl.ScrollText = this.CCTVMessage;
    ((Control) this.MyScrollControl).Text = this.CCTVMessage;
    ((Control) this.MyScrollControl).Font = (Font) this.allfont();
    ((Control) this.MyScrollControl).Left = 10;
    ((Control) this.MyScrollControl).Top = 690;
    ((Control) this.MyScrollControl).Width = 1000;
    ((Control) this.MyScrollControl).Height = checked (((Control) this.MyScrollControl).Height + 25);
    ((Control) this.MyScrollControl).BackColor = Color.Yellow;
    ((Control) this.MyScrollControl).ForeColor = Color.Black;
    this.CoachsScrollControl.Text = "Coach";
    this.CoachsScrollControl.Name = "CoachsScrollControl";
    this.CoachsScrollControl.Font = (Font) this.allfont();
    this.CoachsScrollControl.ForeColor = Color.Blue;
    this.CoachsScrollControl.Left = 10;
    this.CoachsScrollControl.Top = 650;
    this.CoachsScrollControl.Width = 1000;
    checked { this.CoachsScrollControl.Height += 15; }
    this.CoachsScrollControl.BackColor = Color.MistyRose;
    this.TrainDetailsScrollControl.Name = " TrainDetailsScrollControl";
    this.TrainDetailsScrollControl.Text = "TrainDetails";
    this.TrainDetailsScrollControl.Font = (Font) this.allfont();
    this.TrainDetailsScrollControl.ForeColor = Color.Blue;
    this.TrainDetailsScrollControl.Left = 10;
    this.TrainDetailsScrollControl.Top = 610;
    this.TrainDetailsScrollControl.Width = 1000;
    checked { this.TrainDetailsScrollControl.Height += 15; }
    this.TrainDetailsScrollControl.BackColor = Color.MistyRose;
    this.TrainInfo.Name = "traininfo ";
    this.TrainInfo.Text = "";
    this.TrainInfo.Font = (Font) this.allfont();
    this.TrainInfo.ForeColor = Color.Blue;
    this.TrainInfo.Left = 10;
    this.TrainInfo.Top = 570;
    this.TrainInfo.Width = 1000;
    checked { this.TrainInfo.Height += 15; }
    this.TrainInfo.BackColor = Color.Wheat;
    this.TrainInfo.ForeColor = Color.Red;
    this.Controls.Add((Control) this.TrainInfo);
    this.Controls.Add((Control) this.TrainDetailsScrollControl);
    this.Controls.Add((Control) this.CoachsScrollControl);
    this.Controls.Add((Control) this.MyScrollControl);
    this.LblCCTV_Header.Font = (Font) this.allfont();
    this.lblHeaderText.Font = (Font) this.allfont();
    this.lblHeaderTime.Font = (Font) this.allfont();
    this.Timer1.Interval = 100;
    this.Timer1.Enabled = true;
    Color color = new Color();
    DataGridViewCellStyle gridViewCellStyle = new DataGridViewCellStyle();
    this.DataGridView1.Enabled = false;
    if (Screen.AllScreens.Length > 1)
    {
      this.Location = new Point(Screen.PrimaryScreen.WorkingArea.Width, 0);
      this.WindowState = FormWindowState.Maximized;
    }
    this.DataGridView1.Width = 1000;
    this.no_rows = 0;
    int index = 0;
    while (index < frmMainFormIPIS.online_train_cnt)
    {
      if (frmMainFormIPIS.online_train_data[index].display_checked)
        checked { ++this.no_rows; }
      checked { ++index; }
    }
    this.tm_tick = this.no_rows;
    this.Timer1.Start();
    this.Timer3.Start();
    this.scrolling_timer.Start();
  }

  public object allfont()
  {
    return (object) new Font(FontFamily.GenericSansSerif, (float) this.Fontforall, FontStyle.Bold);
  }

  public object all_reg_lang_font()
  {
    return (object) new Font(frmMainFormIPIS.reg_font_name, 20f, FontStyle.Bold);
  }

  public object all_hin_lang_font()
  {
    return (object) new Font(frmMainFormIPIS.hindi_font_name, 25f, FontStyle.Bold);
  }

  private void setCoaches(string TrainNo, string TrainName)
  {
    string empty1 = string.Empty;
    string[] strArray1 = new string[3];
    string empty2 = string.Empty;
    string str1 = string.Empty;
    int num1 = checked (this.DataGridView3.Rows.Count - 1);
    int index1 = 0;
    while (index1 <= num1)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.ConditionalCompareObjectEqual(this.DataGridView3.Rows[index1].Cells[1].Value, (object) TrainNo, false))
      {
        this.DataGridView3.Rows[index1].Cells[2].Value = (object) Strings.Replace(Conversions.ToString(this.DataGridView3.Rows[index1].Cells[2].Value), "@", "");
        empty1 = Conversions.ToString(Microsoft.VisualBasic.CompilerServices.Operators.ConcatenateObject((object) empty1, this.DataGridView3.Rows[index1].Cells[2].Value));
        break;
      }
      checked { ++index1; }
    }
    int num2 = 0;
    string str2 = string.Empty;
    string[] strArray2 = empty1.Split(' ');
    int index2 = 0;
    while (index2 < strArray2.Length)
    {
      if (index2 < 14)
      {
        str2 = str2 + strArray2[index2] + " ";
        checked { num2 += str2.Length; }
      }
      else
        str1 = str1 + strArray2[index2] + " ";
      checked { ++index2; }
    }
    if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Strings.Trim(str2), string.Empty, false) == 0)
      this.TrainInfo.Text = TrainNo + Strings.Space(4) + TrainName;
    else
      this.TrainInfo.Text = TrainNo + Strings.Space(4) + TrainName + Strings.Space(10) + "COACH POSITION";
    this.TrainDetailsScrollControl.Text = str2;
    this.CoachsScrollControl.Text = str1;
  }

  private void Timer1_Tick(object sender, EventArgs e)
  {
    try
    {
      this.no_rows = 0;
      int index = 0;
      while (index < frmMainFormIPIS.online_train_cnt)
      {
        if (frmMainFormIPIS.online_train_data[index].display_checked)
          checked { ++this.no_rows; }
        checked { ++index; }
      }
      this.timer_set();
      checked { ++this.TimerInterval; }
      this.ReadTextFile();
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(this.FillGridLine[1], "True", false) == 0)
      {
        frmMainFormIPIS.SenddataToSCCTV();
        this.fillGrid2();
        this.Refresh();
        this.ClearTextFile();
        this.AppendTexttoFile("False", "False", "False");
      }
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(this.CloseAppLine[1], "True", false) != 0)
        return;
      this.Close();
      Application.Exit();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
  }

  public void timer_set()
  {
    if (this.tm_tick == this.no_rows)
    {
      this.start_display = true;
      this.tm_tick = 0;
      this.Timer1.Interval = 25000;
      this.cc = this.count;
      network_db_read.get_language_details();
      this.reg_found = frmMainFormIPIS.language_selection.regional_language_selected;
      if (this.LangID == 0)
      {
        if (this.eng_found)
        {
          this.DataGridView1.Rows.Clear();
          this.DisplayTrainsOnGrid();
          this.start_no = 0;
          this.display_coach_position(0);
          if (this.hin_found)
            this.LangID = 1;
          else if (this.reg_found)
            this.LangID = 2;
        }
        else if (this.hin_found)
        {
          this.DataGridView1.Rows.Clear();
          this.DisplayTrainsOnGrid();
          this.start_no = 1;
          this.display_coach_position(0);
          if (this.eng_found)
            this.LangID = 2;
          else if (this.reg_found)
            this.LangID = 0;
        }
        else if (this.reg_found)
        {
          this.DataGridView1.Rows.Clear();
          this.DisplayTrainsOnGrid();
          this.start_no = 2;
          this.display_coach_position(0);
          if (this.eng_found)
            this.LangID = 0;
          else if (this.hin_found)
            this.LangID = 1;
        }
      }
      else if (this.LangID == 1)
      {
        if (this.hin_found)
        {
          this.DataGridView1.Rows.Clear();
          this.DisplayTrainsOnGrid();
          this.start_no = 1;
          this.display_coach_position(0);
          if (this.reg_found)
            this.LangID = 2;
          else if (this.eng_found)
            this.LangID = 0;
        }
        else if (this.reg_found)
        {
          this.DataGridView1.Rows.Clear();
          this.DisplayTrainsOnGrid();
          this.start_no = 2;
          this.display_coach_position(0);
          if (this.eng_found)
            this.LangID = 0;
          else if (this.hin_found)
            this.LangID = 1;
        }
        else if (this.eng_found)
        {
          this.DataGridView1.Rows.Clear();
          this.DisplayTrainsOnGrid();
          this.start_no = 0;
          this.display_coach_position(0);
          if (this.hin_found)
            this.LangID = 1;
          else if (this.reg_found)
            this.LangID = 2;
        }
      }
      else if (this.LangID == 2)
      {
        if (this.reg_found)
        {
          this.DataGridView1.Rows.Clear();
          this.DisplayTrainsOnGrid();
          this.start_no = 2;
          this.display_coach_position(0);
          if (this.eng_found)
            this.LangID = 0;
          else if (this.hin_found)
            this.LangID = 1;
        }
        else if (this.eng_found)
        {
          this.DataGridView1.Rows.Clear();
          this.DisplayTrainsOnGrid();
          this.start_no = 0;
          this.display_coach_position(0);
          if (this.hin_found)
            this.LangID = 1;
          else if (this.reg_found)
            this.LangID = 2;
        }
        else if (this.hin_found)
        {
          this.DataGridView1.Rows.Clear();
          this.DisplayTrainsOnGrid();
          this.start_no = 1;
          this.display_coach_position(0);
          if (this.reg_found)
            this.LangID = 2;
          else if (this.eng_found)
            this.LangID = 0;
        }
      }
      this.Timer1.Interval = checked ((int) Math.Round(unchecked (25000.0 / (double) this.no_rows)));
      checked { this.dc += this.cc; }
    }
    else
    {
      if (this.tm_tick < this.DataGridView1.Rows.Count)
      {
        if (this.start_display)
          this.display_coach_position(this.tm_tick);
        this.row_cnt = 0;
      }
      if (this.tm_tick == this.no_rows)
        this.Timer1.Interval = 25000;
      checked { ++this.tm_tick; }
      this.Timer1.Interval = checked ((int) Math.Round(unchecked (25000.0 / (double) this.no_rows)));
    }
  }

  public void IniCCTVSystem()
  {
    try
    {
      if (Conversions.ToDouble(this.CCTVHeading) == 70.0)
        return;
      int num = checked ((int) Math.Round(unchecked (70.0 - Conversions.ToDouble(this.CCTVHeading))));
      this.CCTVHeading = Strings.Space(checked ((int) Math.Round(unchecked ((double) num / 2.0)))) + this.CCTVHeading + Strings.Space(checked ((int) Math.Round(unchecked ((double) num / 2.0))));
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
  }

  public object fillGrid2()
  {
    DataSet dataSet = new DataSet();
    DataSet cctvData = this.CCTVData;
    if (cctvData.Tables.Count > 0)
    {
      this.DataGridView2.DataSource = (object) cctvData.Tables[0];
      this.DataGridView3.DataSource = (object) cctvData.Tables[1];
    }
    return (object) null;
  }

  public void DisplayTrainsOnGrid()
  {
    try
    {
      int rowIndex = 0;
      int num1 = 0;
      string empty1 = string.Empty;
      string empty2 = string.Empty;
      string empty3 = string.Empty;
      string empty4 = string.Empty;
      string empty5 = string.Empty;
      string empty6 = string.Empty;
      this.dc = 0;
      network_db_read.get_language_details();
      if (this.video_data_entered)
        return;
      this.video_data_entered = true;
      this.DataGridView1.Rows.Clear();
      int count = this.count;
      int num2 = checked (this.DataGridView2.Rows.Count - 2);
      this.q = count;
      while (this.q <= num2)
      {
        this.TrainArray[0] = Conversions.ToString(this.DataGridView2[0, this.Gbl].Value);
        this.TrainArray[0] = Conversions.ToString(this.DataGridView2[1, this.Gbl].Value);
        this.TrainArray[1] = Conversions.ToString(this.DataGridView2[checked (this.LangID + 2), this.Gbl].Value);
        this.TrainArray[2] = Conversions.ToString(this.DataGridView2[5, this.Gbl].Value);
        this.TrainArray[3] = Conversions.ToString(this.DataGridView2[6, this.Gbl].Value);
        this.TrainArray[4] = Conversions.ToString(this.DataGridView2[7, this.Gbl].Value);
        this.DataGridView1.RowsDefaultCellStyle.Font = (Font) this.allfont();
        if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(this.TrainArray[2], "Arrived", false) == 0)
        {
          string str = "";
          network_db_read.get_train_status_info(ref empty1, ref empty2, ref str, "ARRIVED ON", ref empty6);
          if (this.LangID == 2)
          {
            if (frmMainFormIPIS.language_selection.regional_language_selected)
            {
              this.TrainArray[2] = empty1;
              this.DataGridView1.Rows[this.q].Cells[2].Style.Font = (Font) this.all_reg_lang_font();
            }
            else
              this.TrainArray[2] = string.Empty;
          }
          else if (this.LangID == 1)
          {
            this.TrainArray[2] = empty2;
            this.DataGridView1.Rows[this.q].Cells[2].Style.Font = (Font) this.all_hin_lang_font();
          }
        }
        else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(this.TrainArray[2], "Cancelled", false) == 0)
        {
          string str = "";
          network_db_read.get_train_status_info(ref empty1, ref empty2, ref str, "CANCELLED", ref empty6);
          if (this.LangID == 2)
          {
            if (frmMainFormIPIS.language_selection.regional_language_selected)
            {
              this.TrainArray[2] = empty1;
              this.DataGridView1.Rows[this.q].Cells[2].Style.Font = (Font) this.all_reg_lang_font();
            }
            else
              this.TrainArray[2] = string.Empty;
          }
          else if (this.LangID == 1)
          {
            this.TrainArray[2] = empty2;
            this.DataGridView1.Rows[this.q].Cells[2].Style.Font = (Font) this.all_hin_lang_font();
          }
        }
        else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(this.TrainArray[2], "Has Left", false) == 0)
        {
          string str = "";
          network_db_read.get_train_status_info(ref empty1, ref empty2, ref str, "HAS LEFT", ref empty6);
          if (this.LangID == 2)
          {
            if (frmMainFormIPIS.language_selection.regional_language_selected)
            {
              this.TrainArray[2] = empty1;
              this.DataGridView1.Rows[this.q].Cells[2].Style.Font = (Font) this.all_reg_lang_font();
            }
            else
              this.TrainArray[2] = string.Empty;
          }
          else if (this.LangID == 1)
          {
            this.TrainArray[2] = empty2;
            this.DataGridView1.Rows[this.q].Cells[2].Style.Font = (Font) this.all_hin_lang_font();
          }
        }
        else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(this.TrainArray[2], "Indef.Late", false) == 0)
        {
          string str = "";
          network_db_read.get_train_status_info(ref empty1, ref empty2, ref str, "INDEFINITE LATE", ref empty6);
          if (this.LangID == 2)
          {
            if (frmMainFormIPIS.language_selection.regional_language_selected)
            {
              this.TrainArray[2] = empty1;
              this.DataGridView1.Rows[this.q].Cells[2].Style.Font = (Font) this.all_reg_lang_font();
            }
            else
              this.TrainArray[2] = string.Empty;
          }
          else if (this.LangID == 1)
          {
            this.TrainArray[2] = empty2;
            this.DataGridView1.Rows[this.q].Cells[2].Style.Font = (Font) this.all_hin_lang_font();
          }
        }
        else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(this.TrainArray[2], "Rescheduled", false) == 0)
        {
          string str = "";
          network_db_read.get_train_status_info(ref empty1, ref empty2, ref str, "RESCHEDULED", ref empty6);
          if (this.LangID == 2)
          {
            if (frmMainFormIPIS.language_selection.regional_language_selected)
            {
              this.TrainArray[2] = empty1;
              this.DataGridView1.Rows[this.q].Cells[2].Style.Font = (Font) this.all_reg_lang_font();
            }
            else
              this.TrainArray[2] = string.Empty;
          }
          else if (this.LangID == 1)
          {
            this.TrainArray[2] = empty2;
            this.DataGridView1.Rows[this.q].Cells[2].Style.Font = (Font) this.all_hin_lang_font();
          }
        }
        else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(this.TrainArray[2], "Terminate At", false) == 0)
        {
          string str = "";
          network_db_read.get_train_status_info(ref empty1, ref empty2, ref str, "TERMINATED", ref empty6);
          network_db_read.get_name_station(this.TrainArray[0], ref empty3, ref empty5, ref empty4);
          this.TrainArray[3] = empty3;
          if (this.LangID == 2)
          {
            if (frmMainFormIPIS.language_selection.regional_language_selected)
            {
              this.TrainArray[2] = empty1;
              this.DataGridView1.Rows[this.q].Cells[2].Style.Font = (Font) this.all_reg_lang_font();
            }
            else
              this.TrainArray[2] = string.Empty;
            this.TrainArray[3] = empty4;
            this.DataGridView1.Rows[this.q].Cells[3].Style.Font = (Font) this.all_reg_lang_font();
          }
          else if (this.LangID == 1)
          {
            this.TrainArray[2] = empty2;
            this.DataGridView1.Rows[this.q].Cells[2].Style.Font = (Font) this.all_hin_lang_font();
            this.TrainArray[3] = empty5;
            this.DataGridView1.Rows[this.q].Cells[3].Style.Font = (Font) this.all_hin_lang_font();
          }
        }
        else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(this.TrainArray[2], "Diverted Via", false) == 0)
        {
          string str = "";
          network_db_read.get_train_status_info(ref empty1, ref empty2, ref str, "DIVERTED ROUTE", ref empty6);
          network_db_read.get_name_station(this.TrainArray[0], ref empty3, ref empty5, ref empty4);
          this.TrainArray[3] = empty3;
          if (this.LangID == 2)
          {
            if (frmMainFormIPIS.language_selection.regional_language_selected)
            {
              this.TrainArray[2] = empty1;
              this.DataGridView1.Rows[this.q].Cells[2].Style.Font = (Font) this.all_reg_lang_font();
            }
            else
              this.TrainArray[2] = string.Empty;
            this.TrainArray[3] = empty4;
            this.DataGridView1.Rows[this.q].Cells[3].Style.Font = (Font) this.all_reg_lang_font();
          }
          else if (this.LangID == 1)
          {
            this.TrainArray[2] = empty2;
            this.DataGridView1.Rows[this.q].Cells[2].Style.Font = (Font) this.all_hin_lang_font();
            this.TrainArray[3] = empty5;
            this.DataGridView1.Rows[this.q].Cells[3].Style.Font = (Font) this.all_hin_lang_font();
          }
        }
        if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(this.TrainArray[2], "00:00", false) == 0)
          this.TrainArray[2] = " ----";
        if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(this.TrainArray[3], "00:00", false) == 0)
          this.TrainArray[3] = " ----";
        if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(this.TrainArray[4], "0", false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(this.TrainArray[4], "00", false) == 0)
          this.TrainArray[4] = "--";
        num1 = 0;
        int num3 = 0;
        do
        {
          this.DataGridView1.Rows.Add();
          checked { ++num3; }
        }
        while (num3 <= 9);
        this.DataGridView1.Rows.Insert(rowIndex, (object[]) this.TrainArray);
        if (this.LangID == 2)
        {
          this.DataGridView1.Rows[this.q].Cells[1].Style.Font = (Font) this.all_reg_lang_font();
          if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(empty1, "", false) != 0)
            this.DataGridView1.Rows[this.q].Cells[2].Style.Font = (Font) this.all_reg_lang_font();
          if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(empty4, "", false) != 0)
            this.DataGridView1.Rows[this.q].Cells[3].Style.Font = (Font) this.all_reg_lang_font();
        }
        if (this.LangID == 1)
        {
          this.DataGridView1.Rows[this.q].Cells[1].Style.Font = (Font) this.all_hin_lang_font();
          if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(empty2, "", false) != 0)
            this.DataGridView1.Rows[this.q].Cells[2].Style.Font = (Font) this.all_hin_lang_font();
          if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(empty5, "", false) != 0)
            this.DataGridView1.Rows[this.q].Cells[3].Style.Font = (Font) this.all_hin_lang_font();
        }
        this.DataGridView1.Rows.Insert(10, (object[]) this.TrainArray);
        this.DataGridView1.Rows[0].Selected = false;
        this.DataGridView1.Rows[6].Selected = false;
        int index = 5;
        do
        {
          this.DataGridView1.Rows[index].Selected = false;
          checked { ++index; }
        }
        while (index <= 9);
        checked { ++rowIndex; }
        checked { ++this.dc; }
        if (this.Gbl == checked (this.DataGridView2.Rows.Count - 2))
        {
          if (!frmMainFormIPIS.language_selection.regional_language_selected)
          {
            if (this.LangID == 1)
            {
              this.Gbl = 0;
              this.count = 0;
              break;
            }
            this.Gbl = this.count;
            break;
          }
          if (this.LangID == 2)
          {
            this.Gbl = 0;
            this.count = 0;
          }
          else
            this.Gbl = this.count;
          break;
        }
        if (this.Gbl == 8 | this.Gbl == 16 /*0x10*/ | this.Gbl == 24 | this.Gbl == 32 /*0x20*/ | this.Gbl == 40 | this.Gbl == 48 /*0x30*/)
        {
          if (!frmMainFormIPIS.language_selection.regional_language_selected)
          {
            if (this.LangID == 1)
            {
              this.count = checked (this.Gbl + 1);
              this.Gbl = this.count;
              break;
            }
            this.Gbl = this.count;
            break;
          }
          if (this.LangID == 2)
          {
            this.count = checked (this.Gbl + 1);
            this.Gbl = this.count;
          }
          else
            this.Gbl = this.count;
          break;
        }
        checked { ++this.Gbl; }
        checked { ++this.q; }
      }
      this.video_data_entered = false;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      this.video_data_entered = false;
      ProjectData.ClearProjectError();
    }
  }

  private void display_coach_position(int row_cnt)
  {
    if (this.DataGridView1.Rows.Count == 0)
      return;
    string TrainName = Conversions.ToString(this.DataGridView1.Rows[row_cnt].Cells[1].Value);
    this.setCoaches(Conversions.ToString(this.DataGridView1.Rows[row_cnt].Cells[0].Value), TrainName);
    checked { ++this.cc; }
  }

  private void Timer3_Tick(object sender, EventArgs e)
  {
    DateTime now1;
    int num1 = 0;
    if (DateTime.Now.Second < 10)
    {
      now1 = DateTime.Now;
      if (now1.Minute < 10)
      {
        num1 = 1;
        goto label_4;
      }
    }
    num1 = 0;
label_4:
    if (num1 != 0)
    {
      this.tim = "{Conversions.ToString(DateTime.Now.Hour)}:0{Conversions.ToString(DateTime.Now.Minute)}:0{Conversions.ToString(DateTime.Now.Second)}";
    }
    else
    {
      DateTime now2 = DateTime.Now;
      DateTime now3;
      int num2 = 0;
      if (now2.Second >= 10)
      {
        now3 = DateTime.Now;
        if (now3.Minute < 10)
        {
          num2 = 1;
          goto label_10;
        }
      }
      num2 = 0;
label_10:
      if (num2 != 0)
      {
        string[] strArray1 = new string[5]
        {
          Conversions.ToString(DateTime.Now.Hour),
          ":0",
          null,
          null,
          null
        };
        string[] strArray2 = strArray1;
        now1 = DateTime.Now;
        string str = Conversions.ToString(now1.Minute);
        strArray2[2] = str;
        strArray1[3] = ":";
        strArray1[4] = Conversions.ToString(DateTime.Now.Second);
        this.tim = string.Concat(strArray1);
      }
      else
      {
        now2 = DateTime.Now;
        int num3;
        if (now2.Second < 10)
        {
          now3 = DateTime.Now;
          if (now3.Minute >= 10)
          {
            num3 = 1;
            goto label_16;
          }
        }
        num3 = 0;
label_16:
        if (num3 != 0)
        {
          string[] strArray3 = new string[5]
          {
            Conversions.ToString(DateTime.Now.Hour),
            ":",
            null,
            null,
            null
          };
          string[] strArray4 = strArray3;
          now1 = DateTime.Now;
          string str = Conversions.ToString(now1.Minute);
          strArray4[2] = str;
          strArray3[3] = ":0";
          strArray3[4] = Conversions.ToString(DateTime.Now.Second);
          this.tim = string.Concat(strArray3);
        }
        else
        {
          string[] strArray5 = new string[5]
          {
            Conversions.ToString(DateTime.Now.Hour),
            ":",
            null,
            null,
            null
          };
          string[] strArray6 = strArray5;
          now3 = DateTime.Now;
          string str1 = Conversions.ToString(now3.Minute);
          strArray6[2] = str1;
          strArray5[3] = ":";
          string[] strArray7 = strArray5;
          now2 = DateTime.Now;
          string str2 = Conversions.ToString(now2.Second);
          strArray7[4] = str2;
          this.tim = string.Concat(strArray5);
        }
      }
    }
    this.LblCCTV_Header.Text = this.dat;
    this.lblHeaderText.Text = this.CCTVHeading;
    this.LblCCTV_Header.TextAlign = ContentAlignment.MiddleLeft;
    this.lblHeaderText.TextAlign = ContentAlignment.MiddleCenter;
    this.lblHeaderTime.Text = this.tim;
    this.lblHeaderTime.TextAlign = ContentAlignment.MiddleRight;
  }

  private void scrolling_timer_Tick(object sender, EventArgs e)
  {
    if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(((Control) this.MyScrollControl).Text, "", false) != 0)
      ((Control) this.MyScrollControl).Text = Strings.Right(((Control) this.MyScrollControl).Text, checked (((Control) this.MyScrollControl).Text.Length - 1));
    else
      ((Control) this.MyScrollControl).Text = this.CCTVMessage;
  }
}

}