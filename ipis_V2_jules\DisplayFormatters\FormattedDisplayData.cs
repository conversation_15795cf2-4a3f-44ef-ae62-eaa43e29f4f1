using System;
using System.Collections.Generic;

namespace ipis_V2_jules.DisplayFormatters
{
    public class FormattedDisplayData
    {
        public byte[]? Line1 { get; set; }
        public byte[]? Line2 { get; set; }
        public byte[]? Line3 { get; set; } // For boards that support 3 lines or more
        public List<byte>? AdditionalHeaderBytes { get; set; } // For effect codes, packet types etc.

        public FormattedDisplayData()
        {
            AdditionalHeaderBytes = new List<byte>();
        }
    }
}
