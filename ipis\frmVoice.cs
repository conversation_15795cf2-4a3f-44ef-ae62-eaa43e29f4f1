// Decompiled with JetBrains decompiler
// Type: ipis.frmVoice
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

namespace ipis
{

[DesignerGenerated]
public class frmVoice : Form
{
  private static List<WeakReference> __ENCList = new List<WeakReference>();
  private IContainer components;
  [AccessedThroughProperty("btnExit")]
  private Button _btnExit;
  [AccessedThroughProperty("btnSave")]
  private Button _btnSave;
  [AccessedThroughProperty("Label1")]
  private Label _Label1;
  [AccessedThroughProperty("Label2")]
  private Label _Label2;
  [AccessedThroughProperty("btnAdd")]
  private Button _btnAdd;
  [AccessedThroughProperty("btnAddStationVoice")]
  private Button _btnAddStationVoice;
  [AccessedThroughProperty("Label4")]
  private Label _Label4;
  [AccessedThroughProperty("event_addTrainVoice")]
  private frmAddTrainNameVoice _event_addTrainVoice;
  [AccessedThroughProperty("event_deleteTrainVoice")]
  private frmDeleteTrainNameVoice _event_deleteTrainVoice;
  [AccessedThroughProperty("event_addStationNameVoice")]
  private frmStationNameVoice _event_addStationNameVoice;
  [AccessedThroughProperty("event_languages")]
  private frmLanguage _event_languages;
  private StreamWriter sw;

  [DebuggerNonUserCode]
  static frmVoice()
  {
  }

  [DebuggerNonUserCode]
  public frmVoice()
  {
    frmVoice.__ENCAddToList((object) this);
    this.InitializeComponent();
  }

  [DebuggerNonUserCode]
  private static void __ENCAddToList(object value)
  {
    lock (frmVoice.__ENCList)
    {
      if (frmVoice.__ENCList.Count == frmVoice.__ENCList.Capacity)
      {
        int index1 = 0;
        int num = checked (frmVoice.__ENCList.Count - 1);
        int index2 = 0;
        while (index2 <= num)
        {
          if (frmVoice.__ENCList[index2].IsAlive)
          {
            if (index2 != index1)
              frmVoice.__ENCList[index1] = frmVoice.__ENCList[index2];
            checked { ++index1; }
          }
          checked { ++index2; }
        }
        frmVoice.__ENCList.RemoveRange(index1, checked (frmVoice.__ENCList.Count - index1));
        frmVoice.__ENCList.Capacity = frmVoice.__ENCList.Count;
      }
      frmVoice.__ENCList.Add(new WeakReference(RuntimeHelpers.GetObjectValue(value)));
    }
  }

  [DebuggerNonUserCode]
  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  [DebuggerStepThrough]
  private void InitializeComponent()
  {
    this.btnExit = new Button();
    this.btnSave = new Button();
    this.Label1 = new Label();
    this.Label2 = new Label();
    this.btnAdd = new Button();
    this.btnAddStationVoice = new Button();
    this.Label4 = new Label();
    this.SuspendLayout();
    this.btnExit.BackColor = Color.SeaShell;
    this.btnExit.DialogResult = DialogResult.Cancel;
    this.btnExit.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.btnExit.ForeColor = SystemColors.ControlText;
    Button btnExit1 = this.btnExit;
    Point point1 = new Point(175, 230);
    Point point2 = point1;
    btnExit1.Location = point2;
    this.btnExit.Name = "btnExit";
    Button btnExit2 = this.btnExit;
    Size size1 = new Size(60, 25);
    Size size2 = size1;
    btnExit2.Size = size2;
    this.btnExit.TabIndex = 7;
    this.btnExit.Text = "Exit";
    this.btnExit.UseVisualStyleBackColor = false;
    this.btnSave.BackColor = SystemColors.ButtonFace;
    this.btnSave.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.btnSave.ForeColor = SystemColors.ControlText;
    Button btnSave1 = this.btnSave;
    point1 = new Point(260, 21);
    Point point3 = point1;
    btnSave1.Location = point3;
    this.btnSave.Name = "btnSave";
    Button btnSave2 = this.btnSave;
    size1 = new Size(75, 25);
    Size size3 = size1;
    btnSave2.Size = size3;
    this.btnSave.TabIndex = 6;
    this.btnSave.Text = "Create";
    this.btnSave.UseVisualStyleBackColor = false;
    this.Label1.AutoSize = true;
    this.Label1.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label label1_1 = this.Label1;
    point1 = new Point(87, 25);
    Point point4 = point1;
    label1_1.Location = point4;
    this.Label1.Name = "Label1";
    Label label1_2 = this.Label1;
    size1 = new Size(128 /*0x80*/, 16 /*0x10*/);
    Size size4 = size1;
    label1_2.Size = size4;
    this.Label1.TabIndex = 8;
    this.Label1.Text = "Create Voice File";
    this.Label2.AutoSize = true;
    this.Label2.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label label2_1 = this.Label2;
    point1 = new Point(24, 74);
    Point point5 = point1;
    label2_1.Location = point5;
    this.Label2.Name = "Label2";
    Label label2_2 = this.Label2;
    size1 = new Size(191, 16 /*0x10*/);
    Size size5 = size1;
    label2_2.Size = size5;
    this.Label2.TabIndex = 10;
    this.Label2.Text = "Add TrainName Voice File";
    this.btnAdd.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnAdd1 = this.btnAdd;
    point1 = new Point(260, 71);
    Point point6 = point1;
    btnAdd1.Location = point6;
    this.btnAdd.Name = "btnAdd";
    Button btnAdd2 = this.btnAdd;
    size1 = new Size(128 /*0x80*/, 23);
    Size size6 = size1;
    btnAdd2.Size = size6;
    this.btnAdd.TabIndex = 11;
    this.btnAdd.Text = "Add Train Voice";
    this.btnAdd.UseVisualStyleBackColor = true;
    this.btnAddStationVoice.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnAddStationVoice1 = this.btnAddStationVoice;
    point1 = new Point(260, 142);
    Point point7 = point1;
    btnAddStationVoice1.Location = point7;
    this.btnAddStationVoice.Name = "btnAddStationVoice";
    Button btnAddStationVoice2 = this.btnAddStationVoice;
    size1 = new Size(146, 23);
    Size size7 = size1;
    btnAddStationVoice2.Size = size7;
    this.btnAddStationVoice.TabIndex = 15;
    this.btnAddStationVoice.Text = "Add Station Voice";
    this.btnAddStationVoice.UseVisualStyleBackColor = true;
    this.Label4.AutoSize = true;
    this.Label4.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label label4_1 = this.Label4;
    point1 = new Point(12, 149);
    Point point8 = point1;
    label4_1.Location = point8;
    this.Label4.Name = "Label4";
    Label label4_2 = this.Label4;
    size1 = new Size(203, 16 /*0x10*/);
    Size size8 = size1;
    label4_2.Size = size8;
    this.Label4.TabIndex = 14;
    this.Label4.Text = "Add StationName Voice File";
    this.AcceptButton = (IButtonControl) this.btnSave;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.BackColor = Color.PowderBlue;
    this.CancelButton = (IButtonControl) this.btnExit;
    size1 = new Size(432, 267);
    this.ClientSize = size1;
    this.Controls.Add((Control) this.btnAddStationVoice);
    this.Controls.Add((Control) this.Label4);
    this.Controls.Add((Control) this.btnAdd);
    this.Controls.Add((Control) this.Label2);
    this.Controls.Add((Control) this.Label1);
    this.Controls.Add((Control) this.btnExit);
    this.Controls.Add((Control) this.btnSave);
    this.FormBorderStyle = FormBorderStyle.FixedToolWindow;
    this.Name = "frmVoice";
    this.Text = "Train Name Voice";
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  internal virtual Button btnExit
  {
    [DebuggerNonUserCode] get { return this._btnExit; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnExit_Click);
      if (this._btnExit != null)
        this._btnExit.Click -= eventHandler;
      this._btnExit = value;
      if (this._btnExit == null)
        return;
      this._btnExit.Click += eventHandler;
    }
  }

  internal virtual Button btnSave
  {
    [DebuggerNonUserCode] get { return this._btnSave; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnSave_Click);
      if (this._btnSave != null)
        this._btnSave.Click -= eventHandler;
      this._btnSave = value;
      if (this._btnSave == null)
        return;
      this._btnSave.Click += eventHandler;
    }
  }

  internal virtual Label Label1
  {
    [DebuggerNonUserCode] get { return this._Label1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Label1 = value; }
  }

  internal virtual Label Label2
  {
    [DebuggerNonUserCode] get { return this._Label2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Label2 = value; }
  }

  internal virtual Button btnAdd
  {
    [DebuggerNonUserCode] get { return this._btnAdd; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnAdd_Click);
      if (this._btnAdd != null)
        this._btnAdd.Click -= eventHandler;
      this._btnAdd = value;
      if (this._btnAdd == null)
        return;
      this._btnAdd.Click += eventHandler;
    }
  }

  internal virtual Button btnAddStationVoice
  {
    [DebuggerNonUserCode] get { return this._btnAddStationVoice; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnAddStationVoice_Click);
      if (this._btnAddStationVoice != null)
        this._btnAddStationVoice.Click -= eventHandler;
      this._btnAddStationVoice = value;
      if (this._btnAddStationVoice == null)
        return;
      this._btnAddStationVoice.Click += eventHandler;
    }
  }

  internal virtual Label Label4
  {
    [DebuggerNonUserCode] get { return this._Label4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Label4 = value; }
  }

  protected virtual frmAddTrainNameVoice event_addTrainVoice
  {
    [DebuggerNonUserCode] get { return this._event_addTrainVoice; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._event_addTrainVoice = value;
    }
  }

  protected virtual frmDeleteTrainNameVoice event_deleteTrainVoice
  {
    [DebuggerNonUserCode] get { return this._event_deleteTrainVoice; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._event_deleteTrainVoice = value;
    }
  }

  protected virtual frmStationNameVoice event_addStationNameVoice
  {
    [DebuggerNonUserCode] get { return this._event_addStationNameVoice; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._event_addStationNameVoice = value;
    }
  }

  protected virtual frmLanguage event_languages
  {
    [DebuggerNonUserCode] get { return this._event_languages; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._event_languages = value;
    }
  }

  public void CreatPlayList(string audioFilesString)
  {
    try
    {
      if (audioFilesString == null)
        return;
      ArrayList arrayList = new ArrayList((ICollection) audioFilesString.Split(','));
      int num = checked (arrayList.Count - 1);
      int index = 0;
      while (index <= num)
      {
        if (Operators.CompareString(Strings.Trim(Conversions.ToString(arrayList[index])), "", false) != 0)
          Conversions.ToString(Operators.ConcatenateObject((object) "<media src=\"", Operators.AddObject(arrayList[index], (object) "\"/>")));
        checked { ++index; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void update_playlist_trainname(
    string file_name,
    string train_name,
    string train_name_path)
  {
    StreamReader streamReader = new StreamReader(file_name);
    string end = streamReader.ReadToEnd();
    try
    {
      streamReader.Close();
      string contents = end.Replace("</sounds>", " ");
      File.WriteAllText(file_name, contents);
      this.sw = File.AppendText(file_name);
      this.train_name_data(train_name, train_name_path);
      this.sw.WriteLine("</sounds>");
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Train name voice  added", "Msg Box", 0, 0, 0);
      this.sw.Close();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void Create_PlayList(
    string FileName,
    string trainno_voice_path,
    string trainno_path,
    string train_status_name,
    string train_status_path,
    string train_name,
    string train_name_path,
    string kind_attention_path,
    string time_name,
    string time_path,
    string number_name,
    string number_path,
    string reg_time_hours_name,
    string reg_time_hours_path,
    string reg_number_name,
    string reg_number_path,
    string alphabet_name,
    string alphabet_path)
  {
    FileStream fileStream = new FileStream(FileName, FileMode.Create, FileAccess.Write, FileShare.None);
    this.sw = new StreamWriter((Stream) fileStream);
    try
    {
      this.sw.WriteLine("<?xml version=\"1.0\" encoding=\"utf-8\" ?>");
      this.sw.WriteLine("<sounds>");
      this.kind_attention(kind_attention_path);
      this.train_no_voice_data(this.Name, trainno_voice_path);
      this.train_no_data(trainno_path);
      this.train_status_data(train_status_name, train_status_path);
      this.time_name_data(time_name, time_path);
      this.number_name_data(number_name, number_path);
      this.hours_data(reg_time_hours_name, reg_time_hours_path);
      if (Operators.CompareString(reg_number_name, "", false) != 0)
        this.reg_number_name_data(reg_number_name, reg_number_path);
      this.alphabet_data(alphabet_name, alphabet_path);
      this.sw.WriteLine("</sounds>");
      FileName += " Successfully created.";
      int num = (int) MessageBox.Show(FileName, "Create Playlist");
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      int num = (int) MessageBox.Show(ex.Message, "Create Playlist: Error");
      ProjectData.ClearProjectError();
    }
    finally
    {
      this.sw.Close();
      fileStream.Close();
    }
  }

  private void alphabet_data(string alphabet_name, string alphabet_path)
  {
    ArrayList arrayList1 = new ArrayList((ICollection) alphabet_name.Split(','));
    ArrayList arrayList2 = new ArrayList((ICollection) alphabet_path.Split(','));
    try
    {
      int num = checked (arrayList1.Count - 1);
      int index = 0;
      while (index <= num)
      {
        if (Operators.CompareString(Strings.Trim(Conversions.ToString(alphabet_name[index])), "", false) != 0)
        {
          this.sw.WriteLine("\t<alpha_name>");
          this.sw.WriteLine(Operators.ConcatenateObject(Operators.ConcatenateObject((object) "\t\t<label>", arrayList1[index]), (object) "</label>"));
          this.sw.WriteLine(Operators.ConcatenateObject(Operators.ConcatenateObject((object) "\t\t<data>", arrayList2[index]), (object) "</data>"));
          this.sw.WriteLine("\t</alpha_name>");
        }
        checked { ++index; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void reg_number_name_data(string number_name, string number_path)
  {
    ArrayList arrayList1 = new ArrayList((ICollection) number_name.Split(','));
    ArrayList arrayList2 = new ArrayList((ICollection) number_path.Split(','));
    try
    {
      int num = checked (arrayList1.Count - 1);
      int index = 0;
      while (index <= num)
      {
        if (Operators.CompareString(Strings.Trim(Conversions.ToString(number_name[index])), "", false) != 0)
        {
          this.sw.WriteLine("\t<reg_Number_name>");
          this.sw.WriteLine(Operators.ConcatenateObject(Operators.ConcatenateObject((object) "\t\t<label>", arrayList1[index]), (object) "</label>"));
          this.sw.WriteLine(Operators.ConcatenateObject(Operators.ConcatenateObject((object) "\t\t<data>", arrayList2[index]), (object) "</data>"));
          this.sw.WriteLine("\t</reg_Number_name>");
        }
        checked { ++index; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void train_no_data(string path)
  {
    int num1 = 0;
    try
    {
      if (!(num1 >= 0 & path != null))
        return;
      ArrayList arrayList = new ArrayList((ICollection) path.Split(','));
      int num2 = checked (arrayList.Count - 1);
      int index = 0;
      while (index <= num2)
      {
        if (Operators.CompareString(Strings.Trim(Conversions.ToString(arrayList[index])), "", false) != 0)
        {
          this.sw.WriteLine("\t<Trainno>");
          this.sw.WriteLine("\t\t<label>{Conversions.ToString(num1)}</label>");
          this.sw.WriteLine(Operators.ConcatenateObject(Operators.ConcatenateObject((object) "\t\t <data>", arrayList[index]), (object) "</data>"));
          this.sw.WriteLine("\t</Trainno>");
        }
        checked { ++num1; }
        checked { ++index; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num3 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void time_name_data(string train_time_name, string train_time_path)
  {
    ArrayList arrayList1 = new ArrayList((ICollection) train_time_name.Split(','));
    ArrayList arrayList2 = new ArrayList((ICollection) train_time_path.Split(','));
    try
    {
      int num = checked (arrayList1.Count - 1);
      int index = 0;
      while (index <= num)
      {
        if (Operators.CompareString(Strings.Trim(Conversions.ToString(arrayList1[index])), "", false) != 0)
        {
          this.sw.WriteLine("\t<Time_name>");
          this.sw.WriteLine(Operators.ConcatenateObject(Operators.ConcatenateObject((object) "\t\t<label>", arrayList1[index]), (object) "</label>"));
          this.sw.WriteLine(Operators.ConcatenateObject(Operators.ConcatenateObject((object) "\t\t<data>", arrayList2[index]), (object) "</data>"));
          this.sw.WriteLine("\t</Time_name>");
        }
        checked { ++index; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void number_name_data(string train_number_name, string train_number_path)
  {
    ArrayList arrayList1 = new ArrayList((ICollection) train_number_name.Split(','));
    ArrayList arrayList2 = new ArrayList((ICollection) train_number_path.Split(','));
    try
    {
      int num = checked (arrayList1.Count - 1);
      int index = 0;
      while (index <= num)
      {
        if (Operators.CompareString(Strings.Trim(Conversions.ToString(arrayList1[index])), "", false) != 0)
        {
          this.sw.WriteLine("\t<Number_name>");
          this.sw.WriteLine(Operators.ConcatenateObject(Operators.ConcatenateObject((object) "\t\t<label>", arrayList1[index]), (object) "</label>"));
          this.sw.WriteLine(Operators.ConcatenateObject(Operators.ConcatenateObject((object) "\t\t<data>", arrayList2[index]), (object) "</data>"));
          this.sw.WriteLine("\t</Number_name>");
        }
        checked { ++index; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void train_status_data(string train_status_name, string train_status_path)
  {
    ArrayList arrayList1 = new ArrayList((ICollection) train_status_name.Split(','));
    ArrayList arrayList2 = new ArrayList((ICollection) train_status_path.Split(','));
    try
    {
      int num = checked (arrayList1.Count - 1);
      int index = 0;
      while (index <= num)
      {
        if (Operators.CompareString(Strings.Trim(Conversions.ToString(arrayList1[index])), "", false) != 0)
        {
          this.sw.WriteLine("\t<Trainstatus>");
          this.sw.WriteLine(Operators.ConcatenateObject(Operators.ConcatenateObject((object) "\t\t<label>", arrayList1[index]), (object) "</label>"));
          this.sw.WriteLine(Operators.ConcatenateObject(Operators.ConcatenateObject((object) "\t\t<data>", arrayList2[index]), (object) "</data>"));
          this.sw.WriteLine("\t</Trainstatus>");
        }
        checked { ++index; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void train_name_data(string name, string path)
  {
    this.sw.WriteLine("\t<Trainname>");
    this.sw.WriteLine("\t\t<label>{name}</label>");
    this.sw.WriteLine("\t\t<data>{path}</data>");
    this.sw.WriteLine("\t</Trainname>");
  }

  private void train_no_voice_data(string name, string path)
  {
    this.sw.WriteLine("\t<Trainnovoice>");
    this.sw.WriteLine("\t\t <label>trainno</label>");
    this.sw.WriteLine("\t\t <data>{path}</data>");
    this.sw.WriteLine("\t</Trainnovoice>");
  }

  private void hours_data(string reg_time_hours_name, string reg_time_hours_path)
  {
    ArrayList arrayList1 = new ArrayList((ICollection) reg_time_hours_name.Split(','));
    ArrayList arrayList2 = new ArrayList((ICollection) reg_time_hours_path.Split(','));
    try
    {
      int num = checked (arrayList1.Count - 1);
      int index = 0;
      while (index <= num)
      {
        if (Operators.CompareString(Strings.Trim(Conversions.ToString(arrayList1[index])), "", false) != 0)
        {
          this.sw.WriteLine("\t<TrainHour>");
          this.sw.WriteLine(Operators.ConcatenateObject(Operators.ConcatenateObject((object) "\t\t<label>", arrayList1[index]), (object) "</label>"));
          this.sw.WriteLine(Operators.ConcatenateObject(Operators.ConcatenateObject((object) "\t\t<data>", arrayList2[index]), (object) "</data>"));
          this.sw.WriteLine("\t</TrainHour>");
        }
        checked { ++index; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void kind_attention(string path)
  {
    this.sw.WriteLine("\t<Kind_attn>");
    this.sw.WriteLine("\t\t <label>Kind_attn</label>");
    this.sw.WriteLine("\t\t <data>{path}</data>");
    this.sw.WriteLine("\t</Kind_attn>");
  }

  private void btnSave_Click(object sender, EventArgs e)
  {
    string FileName1 = "C:\\IPIS\\voice\\voicexml.xml";
    string FileName2 = "C:\\IPIS\\voice\\regional\\reg_voicexml.xml";
    string FileName3 = "C:\\IPIS\\voice\\hindi\\hin_voicexml.xml";
    string FileName4 = "C:\\IPIS\\voice\\regional\\oriya\\oriya_voicexml.xml";
    string FileName5 = "C:\\IPIS\\voice\\regional\\Marathi\\marathi_voicexml.xml";
    string FileName6 = "C:\\IPIS\\voice\\regional\\Chattisgarh\\Chattisgarh_voicexml.xml";
    string FileName7 = "C:\\IPIS\\voice\\regional\\Bengali\\Bengali_voicexml.xml";
    string empty1 = string.Empty;
    string empty2 = string.Empty;
    string empty3 = string.Empty;
    string empty4 = string.Empty;
    string empty5 = string.Empty;
    string empty6 = string.Empty;
    string empty7 = string.Empty;
    string empty8 = string.Empty;
    string empty9 = string.Empty;
    string empty10 = string.Empty;
    string empty11 = string.Empty;
    string empty12 = string.Empty;
    string empty13 = string.Empty;
    string empty14 = string.Empty;
    string empty15 = string.Empty;
    string empty16 = string.Empty;
    string empty17 = string.Empty;
    string empty18 = string.Empty;
    string empty19 = string.Empty;
    string empty20 = string.Empty;
    string empty21 = string.Empty;
    string empty22 = string.Empty;
    string empty23 = string.Empty;
    string empty24 = string.Empty;
    string empty25 = string.Empty;
    string empty26 = string.Empty;
    string empty27 = string.Empty;
    string empty28 = string.Empty;
    string empty29 = string.Empty;
    string empty30 = string.Empty;
    string empty31 = string.Empty;
    string empty32 = string.Empty;
    try
    {
      string kind_attention_path1 = "c:\\IPIS\\voice\\kind_att_passengers.wav";
      string trainno_voice_path1 = "c:\\IPIS\\voice\\trainno_voice.wav";
      string trainno_path1 = "c:\\IPIS\\voice\\no_zero.wav,c:\\IPIS\\voice\\no_one.wav,c:\\IPIS\\voice\\no_two.wav,c:\\IPIS\\voice\\no_three.wav,c:\\IPIS\\voice\\no_four.wav,c:\\IPIS\\voice\\no_five.wav,c:\\IPIS\\voice\\no_six.wav,c:\\IPIS\\voice\\no_seven.wav,c:\\IPIS\\voice\\no_eight.wav,c:\\IPIS\\voice\\no_nine.wav";
      string train_status_name1 = "ARRIVING ON,ARRIVED ON,RUNNING ON TIME,EXPECTED TO ARRIVE AT,SCHEDULED TO ARRIVE,RUNNING LATE,ON PFNO,INDEFINITE LATE,READY TO LEAVE,WILL LEAVE FROM,SCHEDULED DEPARTURE,COMFORTABLE WISH,RESCHEDULED,TODAY LEAVE,FROM PFNO,IS SCHEDULED LEAVE,DIVERTED ROUTE,INCONVENIENCE,PLATFORM CHANGE,THIS,TRAIN,TODAY,THIS_TRAIN_WILL,AND_REACH,COME_ON,FROM,INSTEAD_OF,IS_CHANGED,IS_DIVERTED,NOW_THIS_TRAIN_WILL_ARRIVE_ON,OF,TO,TRAVEL_VIA,VIA,WILL_BE_TERMINATED_AT,PLATFORM_NO,CANCELLED,THIS_TRAIN,IS,EXPECTED SHORTLY,HAS LEFT";
      string train_status_path1 = "C:\\IPIS\\voice\\is_comming_on_pfno.wav,C:\\IPIS\\voice\\is_arrived_on_pfno.wav,C:\\IPIS\\voice\\is_running_ontime.wav,C:\\IPIS\\voice\\is_exp_to_arrive_at.wav,C:\\IPIS\\voice\\is_scheduled_to_arrive_at.wav,C:\\IPIS\\voice\\is_running_late_by.wav,C:\\IPIS\\voice\\on_pfno.wav,C:\\IPIS\\voice\\running_late_indefinetely.wav,C:\\IPIS\\voice\\ready_to_leave_from_pfno.wav,C:\\IPIS\\voice\\will_leave_from_pfno.wav,C:\\IPIS\\voice\\at_sch_dep_time_of.wav,C:\\IPIS\\voice\\wish_comfortable_happy_journey.wav,C:\\IPIS\\voice\\has_resheduled.wav,C:\\IPIS\\voice\\today_we_leave_at.wav,C:\\IPIS\\voice\\from_pfno.wav,C:\\IPIS\\voice\\is_sch_leave_at.wav,C:\\IPIS\\voice\\is_runn_on_diverted_route.wav,C:\\IPIS\\voice\\con_pass_deeply_regretted.wav,c:\\IPIS\\voice\\pf_change.wav,C:\\IPIS\\voice\\this.wav,c:\\IPIS\\voice\\train.wav,c:\\IPIS\\voice\\today.wav,c:\\IPIS\\voice\\this_train_will.wav,c:\\IPIS\\voice\\and_reach.wav,c:\\IPIS\\voice\\come_on.wav,c:\\IPIS\\voice\\from.wav,c:\\IPIS\\voice\\instead_of.wav,c:\\IPIS\\voice\\is_changed.wav,c:\\IPIS\\voice\\is_diverted.wav,c:\\IPIS\\voice\\now_this_train_will_arrive_on.wav,c:\\IPIS\\voice\\of.wav,c:\\IPIS\\voice\\to.wav,c:\\IPIS\\voice\\travel_via.wav,c:\\IPIS\\voice\\via.wav,c:\\IPIS\\voice\\will_be_terminated_at.wav,c:\\IPIS\\voice\\pfno.wav,c:\\IPIS\\voice\\has_been_cancelled_today.wav,c:\\IPIS\\voice\\this_train.wav,c:\\IPIS\\voice\\is.wav,c:\\IPIS\\voice\\is_exp_to_arrive_shortly_on_pfno.wav,c:\\IPIS\\voice\\has_left.wav";
      string time_name1 = "AM,PM,HOURS,MINS,1_HOUR";
      string time_path1 = "C:\\IPIS\\voice\\time_AM.wav,C:\\IPIS\\voice\\time_PM.wav,C:\\IPIS\\voice\\time_hours.wav,C:\\IPIS\\voice\\time_min.wav,C:\\IPIS\\voice\\1_hour.wav";
      string number_name1 = "0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59";
      string number_path1 = "c:\\IPIS\\voice\\no_zero.wav,c:\\IPIS\\voice\\no_one.wav,c:\\IPIS\\voice\\no_two.wav,c:\\IPIS\\voice\\no_three.wav,c:\\IPIS\\voice\\no_four.wav,c:\\IPIS\\voice\\no_five.wav,c:\\IPIS\\voice\\no_six.wav,c:\\IPIS\\voice\\no_seven.wav,c:\\IPIS\\voice\\no_eight.wav,c:\\IPIS\\voice\\no_nine.wav,C:\\IPIS\\voice\\no_10.wav,C:\\IPIS\\voice\\no_11.wav,C:\\IPIS\\voice\\no_12.wav,C:\\IPIS\\voice\\no_13.wav,C:\\IPIS\\voice\\no_14.wav,C:\\IPIS\\voice\\no_15.wav,C:\\IPIS\\voice\\no_16.wav,C:\\IPIS\\voice\\no_17.wav,C:\\IPIS\\voice\\no_18.wav,C:\\IPIS\\voice\\no_19.wav,C:\\IPIS\\voice\\no_20.wav,C:\\IPIS\\voice\\no_21.wav,C:\\IPIS\\voice\\no_22.wav,C:\\IPIS\\voice\\no_23.wav,C:\\IPIS\\voice\\no_24.wav,C:\\IPIS\\voice\\no_25.wav,C:\\IPIS\\voice\\no_26.wav,C:\\IPIS\\voice\\no_27.wav,C:\\IPIS\\voice\\no_28.wav,C:\\IPIS\\voice\\no_29.wav,C:\\IPIS\\voice\\no_30.wav,C:\\IPIS\\voice\\no_31.wav,C:\\IPIS\\voice\\no_32.wav,C:\\IPIS\\voice\\no_33.wav,C:\\IPIS\\voice\\no_34.wav,C:\\IPIS\\voice\\no_35.wav,C:\\IPIS\\voice\\no_36.wav,C:\\IPIS\\voice\\no_37.wav,C:\\IPIS\\voice\\no_38.wav,C:\\IPIS\\voice\\no_39.wav,C:\\IPIS\\voice\\no_40.wav,C:\\IPIS\\voice\\no_41.wav,C:\\IPIS\\voice\\no_42.wav,C:\\IPIS\\voice\\no_43.wav,C:\\IPIS\\voice\\no_44.wav,C:\\IPIS\\voice\\no_45.wav,C:\\IPIS\\voice\\no_46.wav,C:\\IPIS\\voice\\no_47.wav,C:\\IPIS\\voice\\no_48.wav,C:\\IPIS\\voice\\no_49.wav,C:\\IPIS\\voice\\no_50.wav,C:\\IPIS\\voice\\no_51.wav,C:\\IPIS\\voice\\no_52.wav,C:\\IPIS\\voice\\no_53.wav,C:\\IPIS\\voice\\no_54.wav,C:\\IPIS\\voice\\no_55.wav,C:\\IPIS\\voice\\no_56.wav,C:\\IPIS\\voice\\no_57.wav,C:\\IPIS\\voice\\no_58.wav,C:\\IPIS\\voice\\no_59.wav";
      string alphabet_name = "A,B,C,D,E,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T,U,V,W,X,Y,Z";
      string alphabet_path = "C:\\IPIS\\Voice\\alphabet\\A.wav,C:\\IPIS\\Voice\\alphabet\\B.wav,C:\\IPIS\\Voice\\alphabet\\C.wav,C:\\IPIS\\Voice\\alphabet\\D.wav,C:\\IPIS\\Voice\\alphabet\\E.wav,C:\\IPIS\\Voice\\alphabet\\F.wav,C:\\IPIS\\Voice\\alphabet\\G.wav,C:\\IPIS\\Voice\\alphabet\\H.wav,C:\\IPIS\\Voice\\alphabet\\I.wav,C:\\IPIS\\Voice\\alphabet\\J.wav,C:\\IPIS\\Voice\\alphabet\\K.wav,C:\\IPIS\\Voice\\alphabet\\L.wav,C:\\IPIS\\Voice\\alphabet\\M.wav,C:\\IPIS\\Voice\\alphabet\\N.wav,C:\\IPIS\\Voice\\alphabet\\O.wav,C:\\IPIS\\Voice\\alphabet\\P.wav,C:\\IPIS\\Voice\\alphabet\\Q.wav,C:\\IPIS\\Voice\\alphabet\\R.wav,C:\\IPIS\\Voice\\alphabet\\S.wav,C:\\IPIS\\Voice\\alphabet\\T.wav,C:\\IPIS\\Voice\\alphabet\\U.wav,C:\\IPIS\\Voice\\alphabet\\V.wav,C:\\IPIS\\Voice\\alphabet\\W.wav,C:\\IPIS\\Voice\\alphabet\\X.wav,C:\\IPIS\\Voice\\alphabet\\Y.wav,C:\\IPIS\\Voice\\alphabet\\Z.wav";
      string kind_attention_path2 = "C:\\IPIS\\voice\\regional\\tel_kind_attn.wav";
      string trainno_voice_path2 = "C:\\IPIS\\voice\\regional\\tel_trainno.wav";
      string trainno_path2 = "C:\\IPIS\\voice\\regional\\tel_no_0.wav,C:\\IPIS\\voice\\regional\\tel_no_1.wav,C:\\IPIS\\voice\\regional\\tel_no_2.wav,C:\\IPIS\\voice\\regional\\tel_no_3.wav,C:\\IPIS\\voice\\regional\\tel_no_4.wav,C:\\IPIS\\voice\\regional\\tel_no_5.wav,C:\\IPIS\\voice\\regional\\tel_no_6.wav,C:\\IPIS\\voice\\regional\\tel_no_7.wav,C:\\IPIS\\voice\\regional\\tel_no_8.wav,C:\\IPIS\\voice\\regional\\tel_no_9.wav";
      string train_status_name2 = "HAS LEFT,ARRIVING ON,ARRIVED ON,RUNNING ON TIME,EXPECTED TO ARRIVE AT,SHEDULED TO ARRIVE,RUNNING LATE,INCONVENIENCE,INDEFINITE LATE,READY TO LEAVE,VARIOUS REASONS,CANCELLED,SCHEDULED RIGHT TIME,LEAVE FROM,MEANS,TO DEPART,TO GO,VISIT ENQUIRY,READY TO LEAVE,NAKU,NO_PLATFORM,PLATFORM_NO,AFTER SOMETIME,NUNDI,RANUNNADI,ROUTE,YOKKA,MARCHADAMAINADI,BAYALUDERUNU,BAYALUDERI,NADI";
      string train_status_path2 = "C:\\IPIS\\voice\\regional\\hasleft.wav,C:\\IPIS\\voice\\regional\\tel_coming_on_pfno.wav,C:\\IPIS\\voice\\regional\\tel_arrived_on.wav,C:\\IPIS\\voice\\regional\\tel_running_on_time.wav,C:\\IPIS\\voice\\regional\\tel_to_arrive.wav,C:\\IPIS\\voice\\regional\\tel_after_sometime.wav,C:\\IPIS\\voice\\regional\\tel_running_late.wav,C:\\IPIS\\voice\\regional\\tel_regretted_inconvenience.wav,C:\\IPIS\\voice\\regional\\tel_indefinite_late.wav,C:\\IPIS\\voice\\regional\\tel_is_ready_to_depart_from_pfno.wav,C:\\IPIS\\voice\\regional\\tel_various_reasons.wav,C:\\IPIS\\voice\\regional\\tel_cancelled.wav,C:\\IPIS\\voice\\regional\\tel_departed_right_time.wav,C:\\IPIS\\voice\\regional\\tel_leave_from_pfno.wav ,C:\\IPIS\\voice\\regional\\tel_means.wav,C:\\IPIS\\voice\\regional\\tel_to_be_departed.wav,C:\\IPIS\\voice\\regional\\tel_to_depart.wav,C:\\IPIS\\voice\\regional\\tel_visit_enquiry.wav,C:\\IPIS\\voice\\regional\\tel_ready_to_leave.wav,C:\\IPIS\\voice\\regional\\tel_naku.wav,C:\\IPIS\\voice\\regional\\tel_no_pf.wav,C:\\IPIS\\voice\\regional\\pfno.wav,C:\\IPIS\\voice\\regional\\tel_after_sometime.wav,C:\\IPIS\\voice\\regional\\tel_nundi.wav,C:\\IPIS\\voice\\regional\\tel_ranunnadi.wav,C:\\IPIS\\voice\\regional\\tel_route.wav,C:\\IPIS\\voice\\regional\\tel_yokka.wav,C:\\IPIS\\voice\\regional\\tel_marchadamainadi.wav,C:\\IPIS\\voice\\regional\\tel_bayaluderunu.wav,C:\\IPIS\\voice\\regional\\tel_bayaluderi.wav,C:\\IPIS\\voice\\regional\\tel_nadi.wav";
      string time_name2 = "AM,PM,HOURS,MINS,HOUR,NIGHT";
      string time_path2 = "C:\\IPIS\\voice\\regional\\tel_mor.wav,C:\\IPIS\\voice\\regional\\tel_evening.wav,C:\\IPIS\\voice\\regional\\tel_hours.wav,C:\\IPIS\\voice\\regional\\tel_mins.wav,C:\\IPIS\\voice\\regional\\tel_hour.wav,C:\\IPIS\\voice\\regional\\tel_night.wav";
      string number_name2 = "1,2,3,4,5,6,7,8,9,10,11,12";
      string number_path2 = "c:\\IPIS\\voice\\regional\\tel_pfno_1.wav,c:\\IPIS\\voice\\regional\\tel_pfno_2.wav,c:\\IPIS\\voice\\regional\\tel_pfno_3.wav,c:\\IPIS\\voice\\regional\\tel_pfno_4.wav,c:\\IPIS\\voice\\regional\\tel_pfno_5.wav,c:\\IPIS\\voice\\regional\\tel_pfno_6.wav,c:\\IPIS\\voice\\regional\\tel_pfno_7.wav,c:\\IPIS\\voice\\regional\\tel_pfno_8.wav,c:\\IPIS\\voice\\regional\\tel_pfno_9.wav,C:\\IPIS\\voice\\regional\\tel_pfno_10.wav,C:\\IPIS\\voice\\regional\\tel_pfno_11.wav,C:\\IPIS\\voice\\regional\\tel_pfno_12.wav";
      string reg_time_hours_name = "1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24";
      string reg_time_hours_path = "c:\\IPIS\\voice\\regional\\tel_1_hour.wav,c:\\IPIS\\voice\\regional\\tel_2_hour.wav,c:\\IPIS\\voice\\regional\\tel_3_hour.wav,c:\\IPIS\\voice\\regional\\tel_4_hour.wav,c:\\IPIS\\voice\\regional\\tel_5_hour.wav,c:\\IPIS\\voice\\regional\\tel_6_hour.wav,c:\\IPIS\\voice\\regional\\tel_7_hour.wav,c:\\IPIS\\voice\\regional\\tel_8_hour.wav,c:\\IPIS\\voice\\regional\\tel_9_hour.wav,c:\\IPIS\\voice\\regional\\tel_10_hour.wav,C:\\IPIS\\voice\\regional\\tel_11_hour.wav,C:\\IPIS\\voice\\regional\\tel_12_hour.wav,C:\\IPIS\\voice\\regional\\tel_13_hour.wav,C:\\IPIS\\voice\\regional\\tel_14_hour.wav,C:\\IPIS\\voice\\regional\\tel_15_hour.wav,C:\\IPIS\\voice\\regional\\tel_16_hour.wav,C:\\IPIS\\voice\\regional\\tel_17_hour.wav,C:\\IPIS\\voice\\regional\\tel_18_hour.wav,C:\\IPIS\\voice\\regional\\tel_19_hour.wav,C:\\IPIS\\voice\\regional\\tel_20_hour.wav,C:\\IPIS\\voice\\regional\\tel_21_hour.wav,C:\\IPIS\\voice\\regional\\tel_22_hour.wav,C:\\IPIS\\voice\\regional\\tel_23_hour.wav,C:\\IPIS\\voice\\regional\\tel_24_hour.wav";
      string reg_number_name = "1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59";
      string reg_number_path = "c:\\IPIS\\voice\\regional\\tel_no_1.wav,c:\\IPIS\\voice\\regional\\tel_no_2.wav,c:\\IPIS\\voice\\regional\\tel_no_3.wav,c:\\IPIS\\voice\\regional\\tel_no_4.wav,c:\\IPIS\\voice\\regional\\tel_no_5.wav,c:\\IPIS\\voice\\regional\\tel_no_6.wav,c:\\IPIS\\voice\\regional\\tel_no_7.wav,c:\\IPIS\\voice\\regional\\tel_no_8.wav,c:\\IPIS\\voice\\regional\\tel_no_9.wav,c:\\IPIS\\voice\\regional\\tel_no_10.wav,c:\\IPIS\\voice\\regional\\tel_no_11.wav,c:\\IPIS\\voice\\regional\\tel_no_12.wav,c:\\IPIS\\voice\\regional\\tel_no_13.wav,c:\\IPIS\\voice\\regional\\tel_no_14.wav,c:\\IPIS\\voice\\regional\\tel_no_15.wav,c:\\IPIS\\voice\\regional\\tel_no_16.wav,c:\\IPIS\\voice\\regional\\tel_no_17.wav,c:\\IPIS\\voice\\regional\\tel_no_18.wav,c:\\IPIS\\voice\\regional\\tel_no_19.wav,c:\\IPIS\\voice\\regional\\tel_no_20.wav,c:\\IPIS\\voice\\regional\\tel_no_21.wav,c:\\IPIS\\voice\\regional\\tel_no_22.wav,c:\\IPIS\\voice\\regional\\tel_no_23.wav,c:\\IPIS\\voice\\regional\\tel_no_24.wav,c:\\IPIS\\voice\\regional\\tel_no_25.wav,c:\\IPIS\\voice\\regional\\tel_no_26.wav,c:\\IPIS\\voice\\regional\\tel_no_27.wav,c:\\IPIS\\voice\\regional\\tel_no_28.wav,c:\\IPIS\\voice\\regional\\tel_no_29.wav,c:\\IPIS\\voice\\regional\\tel_no_30.wav,c:\\IPIS\\voice\\regional\\tel_no_31.wav,c:\\IPIS\\voice\\regional\\tel_no_32.wav,c:\\IPIS\\voice\\regional\\tel_no_33.wav,c:\\IPIS\\voice\\regional\\tel_no_34.wav,c:\\IPIS\\voice\\regional\\tel_no_35.wav,c:\\IPIS\\voice\\regional\\tel_no_36.wav,c:\\IPIS\\voice\\regional\\tel_no_37.wav,c:\\IPIS\\voice\\regional\\tel_no_38.wav,c:\\IPIS\\voice\\regional\\tel_no_39.wav,c:\\IPIS\\voice\\regional\\tel_no_40.wav,c:\\IPIS\\voice\\regional\\tel_no_41.wav,c:\\IPIS\\voice\\regional\\tel_no_42.wav,c:\\IPIS\\voice\\regional\\tel_no_43.wav,c:\\IPIS\\voice\\regional\\tel_no_44.wav,c:\\IPIS\\voice\\regional\\tel_no_45.wav,c:\\IPIS\\voice\\regional\\tel_no_46.wav,c:\\IPIS\\voice\\regional\\tel_no_47.wav,c:\\IPIS\\voice\\regional\\tel_no_48.wav,c:\\IPIS\\voice\\regional\\tel_no_49.wav,c:\\IPIS\\voice\\regional\\tel_no_50.wav,c:\\IPIS\\voice\\regional\\tel_no_51.wav,c:\\IPIS\\voice\\regional\\tel_no_52.wav,c:\\IPIS\\voice\\regional\\tel_no_53.wav,c:\\IPIS\\voice\\regional\\tel_no_54.wav,c:\\IPIS\\voice\\regional\\tel_no_55.wav,c:\\IPIS\\voice\\regional\\tel_no_56.wav,c:\\IPIS\\voice\\regional\\tel_no_57.wav,c:\\IPIS\\voice\\regional\\tel_no_58.wav,c:\\IPIS\\voice\\regional\\tel_no_59.wav";
      string kind_attention_path3 = "C:\\IPIS\\voice\\hindi\\hin_kind_attn.wav";
      string trainno_voice_path3 = "c:\\IPIS\\voice\\hindi\\hin_trainno.wav";
      string trainno_path3 = "c:\\IPIS\\voice\\hindi\\0.wav,c:\\IPIS\\voice\\hindi\\1.wav,c:\\IPIS\\voice\\hindi\\2.wav,c:\\IPIS\\voice\\hindi\\3.wav,c:\\IPIS\\voice\\hindi\\4.wav,c:\\IPIS\\voice\\hindi\\5.wav,c:\\IPIS\\voice\\hindi\\6.wav,c:\\IPIS\\voice\\hindi\\7.wav,c:\\IPIS\\voice\\hindi\\8.wav,c:\\IPIS\\voice\\hindi\\9.wav";
      string train_status_name3 = "ARRIVING ON,ARRIVED ON,AFTER_SOMETIME,CANCELLED,PLATFORM_CHANGE,CHANGE_TIME,CHANGE_WAY,COME,INDEFINITE LATE,LEAVE TIME,MIGHT COME,PLATFORM NO,READY TO LEAVE,INCONVENIENCE,RUNNING LATE,RUNNING ON TIME,SCHEDULED TIME,SCHEDULE_TIME_2,WILL ARRIVE,WILL LEAVE,COMFORTABLE WISH,NOW_THIS_TRAIN,PER,THIS_TRAIN,SCHEDULED DEPARTURE,TODAY_THIS_TRAIN,TRAIN_ARRIVING,IS_THERE,SE,RAVANA,HOGI,THAK,JAYEGI,EXPECTED SHORTLY,NAJAAKAR,HAS LEFT";
      string train_status_path3 = "C:\\IPIS\\voice\\hindi\\arriving_on.wav,C:\\IPIS\\voice\\hindi\\arrivedon.wav,C:\\IPIS\\voice\\hindi\\after_sometime.wav,C:\\IPIS\\voice\\hindi\\cancelled.wav,C:\\IPIS\\voice\\hindi\\change_pfno_time.wav,C:\\IPIS\\voice\\hindi\\change_time.wav,C:\\IPIS\\voice\\hindi\\change_way.wav,C:\\IPIS\\voice\\hindi\\come.wav,C:\\IPIS\\voice\\hindi\\indefinite_late.wav,C:\\IPIS\\voice\\hindi\\leave_time.wav,C:\\IPIS\\voice\\hindi\\might_come.wav,C:\\IPIS\\voice\\hindi\\platform_no.wav,C:\\IPIS\\voice\\hindi\\ready_to_leave.wav,C:\\IPIS\\voice\\hindi\\regretted_inconvenience.wav,C:\\IPIS\\voice\\hindi\\running_late.wav,C:\\IPIS\\voice\\hindi\\running_on_time.wav,C:\\IPIS\\voice\\hindi\\scheduled_time.wav,C:\\IPIS\\voice\\hindi\\scheduled_time_2.wav,C:\\IPIS\\voice\\hindi\\will_arrive.wav,C:\\IPIS\\voice\\hindi\\will_leave.wav,C:\\IPIS\\voice\\hindi\\wishes.wav,C:\\IPIS\\voice\\hindi\\now_this_train.wav,C:\\IPIS\\voice\\hindi\\per.wav,C:\\IPIS\\voice\\hindi\\this_train.wav,C:\\IPIS\\voice\\hindi\\scheduled_departure.wav,C:\\IPIS\\voice\\hindi\\today_this_train.wav,C:\\IPIS\\voice\\hindi\\train_is_arriving.wav,C:\\IPIS\\voice\\hindi\\hin_kadihai.wav,C:\\IPIS\\voice\\hindi\\hin_se.wav,C:\\IPIS\\voice\\hindi\\ravana.wav,C:\\IPIS\\voice\\hindi\\hogi.wav,C:\\IPIS\\voice\\hindi\\thak.wav,C:\\IPIS\\voice\\hindi\\jayegi.wav,C:\\IPIS\\voice\\hindi\\expected_shortly.wav,C:\\IPIS\\voice\\hindi\\najaakar.wav,C:\\IPIS\\voice\\hindi\\hasleft.wav";
      string time_name3 = "HOUR,MIN,HOURS,MINUTE,BAJE";
      string time_path3 = "C:\\IPIS\\voice\\hindi\\hour.wav,C:\\IPIS\\voice\\hindi\\min.wav,C:\\IPIS\\voice\\hindi\\hours.wav,C:\\IPIS\\voice\\hindi\\minute.wav,C:\\IPIS\\voice\\hindi\\hn_baje.wav";
      string number_name3 = "0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59";
      string number_path3 = "c:\\IPIS\\voice\\hindi\\0.wav,c:\\IPIS\\voice\\hindi\\1.wav,c:\\IPIS\\voice\\hindi\\2.wav,c:\\IPIS\\voice\\hindi\\3.wav,c:\\IPIS\\voice\\hindi\\4.wav,c:\\IPIS\\voice\\hindi\\5.wav,c:\\IPIS\\voice\\hindi\\6.wav,c:\\IPIS\\voice\\hindi\\7.wav,c:\\IPIS\\voice\\hindi\\8.wav,c:\\IPIS\\voice\\hindi\\9.wav,C:\\IPIS\\voice\\hindi\\10.wav,C:\\IPIS\\voice\\hindi\\11.wav,C:\\IPIS\\voice\\hindi\\12.wav,C:\\IPIS\\voice\\hindi\\13.wav,C:\\IPIS\\voice\\hindi\\14.wav,C:\\IPIS\\voice\\hindi\\15.wav,C:\\IPIS\\voice\\hindi\\16.wav,C:\\IPIS\\voice\\hindi\\17.wav,C:\\IPIS\\voice\\hindi\\18.wav,C:\\IPIS\\voice\\hindi\\19.wav,C:\\IPIS\\voice\\hindi\\20.wav,C:\\IPIS\\voice\\hindi\\21.wav,C:\\IPIS\\voice\\hindi\\22.wav,C:\\IPIS\\voice\\hindi\\23.wav,C:\\IPIS\\voice\\hindi\\24.wav,C:\\IPIS\\voice\\hindi\\25.wav,C:\\IPIS\\voice\\hindi\\26.wav,C:\\IPIS\\voice\\hindi\\27.wav,C:\\IPIS\\voice\\hindi\\28.wav,C:\\IPIS\\voice\\hindi\\29.wav,C:\\IPIS\\voice\\hindi\\30.wav,C:\\IPIS\\voice\\hindi\\31.wav,C:\\IPIS\\voice\\hindi\\32.wav,C:\\IPIS\\voice\\hindi\\33.wav,C:\\IPIS\\voice\\hindi\\34.wav,C:\\IPIS\\voice\\hindi\\35.wav,C:\\IPIS\\voice\\hindi\\36.wav,C:\\IPIS\\voice\\hindi\\37.wav,C:\\IPIS\\voice\\hindi\\38.wav,C:\\IPIS\\voice\\hindi\\39.wav,C:\\IPIS\\voice\\hindi\\40.wav,C:\\IPIS\\voice\\hindi\\41.wav,C:\\IPIS\\voice\\hindi\\42.wav,C:\\IPIS\\voice\\hindi\\43.wav,C:\\IPIS\\voice\\hindi\\44.wav,C:\\IPIS\\voice\\hindi\\45.wav,C:\\IPIS\\voice\\hindi\\46.wav,C:\\IPIS\\voice\\hindi\\47.wav,C:\\IPIS\\voice\\hindi\\48.wav,C:\\IPIS\\voice\\hindi\\49.wav,C:\\IPIS\\voice\\hindi\\50.wav,C:\\IPIS\\voice\\hindi\\51.wav,C:\\IPIS\\voice\\hindi\\52.wav,C:\\IPIS\\voice\\hindi\\53.wav,C:\\IPIS\\voice\\hindi\\54.wav,C:\\IPIS\\voice\\hindi\\55.wav,C:\\IPIS\\voice\\hindi\\56.wav,C:\\IPIS\\voice\\hindi\\57.wav,C:\\IPIS\\voice\\hindi\\58.wav,C:\\IPIS\\voice\\hindi\\59.wav";
      string kind_attention_path4 = "C:\\IPIS\\voice\\regional\\oriya\\oriya_kind_attn.wav";
      string trainno_voice_path4 = "C:\\IPIS\\voice\\regional\\oriya\\oriya_trainno.wav";
      string trainno_path4 = "C:\\IPIS\\voice\\regional\\oriya\\0.wav,c:\\IPIS\\voice\\regional\\oriya\\1.wav,c:\\IPIS\\voice\\regional\\oriya\\2.wav,c:\\IPIS\\voice\\regional\\oriya\\3.wav,C:\\IPIS\\voice\\regional\\oriya\\4.wav,c:\\IPIS\\voice\\regional\\oriya\\5.wav,c:\\IPIS\\voice\\regional\\oriya\\6.wav,c:\\IPIS\\voice\\regional\\oriya\\7.wav,c:\\IPIS\\voice\\regional\\oriya\\8.wav,c:\\IPIS\\voice\\regional\\oriya\\9.wav";
      string time_name4 = "HOUR,MIN";
      string time_path4 = "C:\\IPIS\\voice\\regional\\oriya\\hour.wav,C:\\IPIS\\voice\\regional\\oriya\\min.wav";
      string train_status_name4 = "HAS LEFT,ARRIVING ON,ARRIVED ON,AFTER_SOMETIME,CANCELLED,PLATFORM_CHANGE,CHANGE_TIME,CHANGE_WAY,COME,INDEFINITE LATE,LEAVE TIME,MIGHT COME,PLATFORM NO,READY TO LEAVE,INCONVENIENCE,RUNNING LATE,RUNNING ON TIME,SCHEDULED TIME,SCHEDULE_TIME_2,WILL ARRIVE,WILL LEAVE,COMFORTABLE WISH,NOW_THIS_TRAIN,PER,THIS_TRAIN,SCHEDULED DEPARTURE,TODAY_THIS_TRAIN,TRAIN_ARRIVING,IS_THERE,SE,RAVANA,HOGI,THAK,JAYEGI,TRAIN_CANCELLED,ON,TODAY,THE,GOING_FROM,NO_JAYEGI";
      string train_status_path4 = "C:\\IPIS\\voice\\regional\\oriya\\hasleft.wav,C:\\IPIS\\voice\\regional\\oriya\\arriving_on.wav,C:\\IPIS\\voice\\regional\\oriya\\arrivedon.wav,C:\\IPIS\\voice\\regional\\oriya\\after_sometime.wav,C:\\IPIS\\voice\\regional\\oriya\\cancelled.wav,C:\\IPIS\\voice\\regional\\oriya\\change_pfno_time.wav,C:\\IPIS\\voice\\regional\\oriya\\change_time.wav,C:\\IPIS\\voice\\regional\\oriya\\change_way.wav,C:\\IPIS\\voice\\regional\\oriya\\come.wav,C:\\IPIS\\voice\\regional\\oriya\\indefinite_late.wav,C:\\IPIS\\voice\\regional\\oriya\\leave_time.wav,C:\\IPIS\\voice\\regional\\oriya\\might_come.wav,C:\\IPIS\\voice\\regional\\oriya\\platform_no.wav,C:\\IPIS\\voice\\regional\\oriya\\ready_to_leave.wav,C:\\IPIS\\voice\\regional\\oriya\\regretted_inconvenience.wav,C:\\IPIS\\voice\\regional\\oriya\\running_late.wav,C:\\IPIS\\voice\\regional\\oriya\\running_on_time.wav,C:\\IPIS\\voice\\regional\\oriya\\scheduled_time.wav,C:\\IPIS\\voice\\regional\\oriya\\scheduled_time_2.wav,C:\\IPIS\\voice\\regional\\oriya\\will_arrive.wav,C:\\IPIS\\voice\\regional\\oriya\\will_leave.wav,C:\\IPIS\\voice\\regional\\oriya\\wishes.wav,C:\\IPIS\\voice\\regional\\oriya\\now_this_train.wav,C:\\IPIS\\voice\\regional\\oriya\\per.wav,C:\\IPIS\\voice\\regional\\oriya\\this_train.wav,C:\\IPIS\\voice\\regional\\oriya\\scheduled_departure.wav,C:\\IPIS\\voice\\regional\\oriya\\today_this_train.wav,C:\\IPIS\\voice\\regional\\oriya\\train_is_arriving.wav,C:\\IPIS\\voice\\regional\\oriya\\hin_kadihai.wav,C:\\IPIS\\voice\\regional\\oriya\\hin_se.wav,C:\\IPIS\\voice\\regional\\oriya\\ravana.wav,C:\\IPIS\\voice\\regional\\oriya\\hogi.wav,C:\\IPIS\\voice\\regional\\oriya\\thak.wav,C:\\IPIS\\voice\\regional\\oriya\\jayegi.wav,C:\\IPIS\\voice\\regional\\oriya\\train_cancelled.wav,C:\\IPIS\\voice\\regional\\oriya\\on.wav,C:\\IPIS\\voice\\regional\\oriya\\Today.wav,C:\\IPIS\\voice\\regional\\oriya\\The.wav,C:\\IPIS\\voice\\regional\\oriya\\going_from.wav,C:\\IPIS\\voice\\regional\\oriya\\no_jayegi.wav";
      string number_name4 = "0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59";
      string number_path4 = "c:\\IPIS\\voice\\regional\\oriya\\0.wav,c:\\IPIS\\voice\\regional\\oriya\\1.wav,c:\\IPIS\\voice\\regional\\oriya\\2.wav,c:\\IPIS\\voice\\regional\\oriya\\3.wav,c:\\IPIS\\voice\\regional\\oriya\\4.wav,c:\\IPIS\\voice\\regional\\oriya\\5.wav,c:\\IPIS\\voice\\regional\\oriya\\6.wav,c:\\IPIS\\voice\\regional\\oriya\\7.wav,c:\\IPIS\\voice\\regional\\oriya\\8.wav,c:\\IPIS\\voice\\regional\\oriya\\9.wav,C:\\IPIS\\voice\\regional\\oriya\\10.wav,C:\\IPIS\\voice\\regional\\oriya\\11.wav,C:\\IPIS\\voice\\regional\\oriya\\12.wav,C:\\IPIS\\voice\\regional\\oriya\\13.wav,C:\\IPIS\\voice\\regional\\oriya\\14.wav,C:\\IPIS\\voice\\regional\\oriya\\15.wav,C:\\IPIS\\voice\\regional\\oriya\\16.wav,C:\\IPIS\\voice\\regional\\oriya\\17.wav,C:\\IPIS\\voice\\regional\\oriya\\18.wav,C:\\IPIS\\voice\\regional\\oriya\\19.wav,C:\\IPIS\\voice\\regional\\oriya\\20.wav,C:\\IPIS\\voice\\regional\\oriya\\21.wav,C:\\IPIS\\voice\\regional\\oriya\\22.wav,C:\\IPIS\\voice\\regional\\oriya\\23.wav,c:\\IPIS\\voice\\regional\\oriya\\24.wav,c:\\IPIS\\voice\\regional\\oriya\\25.wav,c:\\IPIS\\voice\\regional\\oriya\\26.wav,c:\\IPIS\\voice\\regional\\oriya\\27.wav,c:\\IPIS\\voice\\regional\\oriya\\28.wav,c:\\IPIS\\voice\\regional\\oriya\\29.wav,c:\\IPIS\\voice\\regional\\oriya\\30.wav,c:\\IPIS\\voice\\regional\\oriya\\31.wav,c:\\IPIS\\voice\\regional\\oriya\\32.wav,c:\\IPIS\\voice\\regional\\oriya\\33.wav,C:\\IPIS\\voice\\regional\\oriya\\34.wav,C:\\IPIS\\voice\\regional\\oriya\\35.wav,C:\\IPIS\\voice\\regional\\oriya\\36.wav,C:\\IPIS\\voice\\regional\\oriya\\37.wav,C:\\IPIS\\voice\\regional\\oriya\\38.wav,C:\\IPIS\\voice\\regional\\oriya\\39.wav,C:\\IPIS\\voice\\regional\\oriya\\40.wav,C:\\IPIS\\voice\\regional\\oriya\\41.wav,C:\\IPIS\\voice\\regional\\oriya\\42.wav,C:\\IPIS\\voice\\regional\\oriya\\43.wav,C:\\IPIS\\voice\\regional\\oriya\\44.wav,C:\\IPIS\\voice\\regional\\oriya\\45.wav,C:\\IPIS\\voice\\regional\\oriya\\46.wav,C:\\IPIS\\voice\\regional\\oriya\\47.wav,c:\\IPIS\\voice\\regional\\oriya\\48.wav,c:\\IPIS\\voice\\regional\\oriya\\49.wav,c:\\IPIS\\voice\\regional\\oriya\\50.wav,c:\\IPIS\\voice\\regional\\oriya\\51.wav,c:\\IPIS\\voice\\regional\\oriya\\52.wav,c:\\IPIS\\voice\\regional\\oriya\\53.wav,c:\\IPIS\\voice\\regional\\oriya\\54.wav,c:\\IPIS\\voice\\regional\\oriya\\55.wav,c:\\IPIS\\voice\\regional\\oriya\\56.wav,c:\\IPIS\\voice\\regional\\oriya\\57.wav,C:\\IPIS\\voice\\regional\\oriya\\58.wav,C:\\IPIS\\voice\\regional\\oriya\\59.wav";
      string kind_attention_path5 = "C:\\IPIS\\voice\\regional\\Marathi\\Marathi_kind_attn.wav";
      string trainno_voice_path5 = "C:\\IPIS\\voice\\regional\\Marathi\\Marathi_trainno.wav";
      string trainno_path5 = "C:\\IPIS\\voice\\regional\\Marathi\\0.wav,c:\\IPIS\\voice\\regional\\Marathi\\1.wav,c:\\IPIS\\voice\\regional\\Marathi\\2.wav,c:\\IPIS\\voice\\regional\\Marathi\\3.wav,C:\\IPIS\\voice\\regional\\Marathi\\4.wav,c:\\IPIS\\voice\\regional\\Marathi\\5.wav,c:\\IPIS\\voice\\regional\\Marathi\\6.wav,c:\\IPIS\\voice\\regional\\Marathi\\7.wav,c:\\IPIS\\voice\\regional\\Marathi\\8.wav,c:\\IPIS\\voice\\regional\\Marathi\\9.wav";
      string time_name5 = "HOUR,MIN";
      string time_path5 = "C:\\IPIS\\voice\\regional\\Marathi\\hour.wav,C:\\IPIS\\voice\\regional\\Marathi\\min.wav";
      string train_status_name5 = "HAS LEFT,ARRIVING ON,ARRIVED ON,AFTER_SOMETIME,CANCELLED,PLATFORM_CHANGE,CHANGE_TIME,CHANGE_WAY,COME,INDEFINITE LATE,LEAVE TIME,MIGHT COME,PLATFORM NO,READY TO LEAVE,INCONVENIENCE,RUNNING LATE,RUNNING ON TIME,SCHEDULED TIME,SCHEDULE_TIME_2,WILL ARRIVE,WILL LEAVE,COMFORTABLE WISH,NOW_THIS_TRAIN,PER,THIS_TRAIN,SCHEDULED DEPARTURE,TODAY_THIS_TRAIN,TRAIN_ARRIVING,IS_THERE,SE,RAVANA,HOGI,THAK,JAYEGI,TRAIN_CANCELLED,ON,TODAY,THE,GOING_FROM,NO_JAYEGI,RUNNING_TIME,ALREADY_ANNOUNCE,EXPECTED SHORTLY,VAROON,REGULATED,TERMINATED";
      string train_status_path5 = "C:\\IPIS\\voice\\regional\\Marathi\\hasleft.wav,C:\\IPIS\\voice\\regional\\Marathi\\arriving_on.wav,C:\\IPIS\\voice\\regional\\Marathi\\arrivedon.wav,C:\\IPIS\\voice\\regional\\Marathi\\after_sometime.wav,C:\\IPIS\\voice\\regional\\Marathi\\cancelled.wav,C:\\IPIS\\voice\\regional\\Marathi\\change_pfno_time.wav,C:\\IPIS\\voice\\regional\\Marathi\\change_time.wav,C:\\IPIS\\voice\\regional\\Marathi\\change_way.wav,C:\\IPIS\\voice\\regional\\Marathi\\come.wav,C:\\IPIS\\voice\\regional\\Marathi\\indefinite_late.wav,C:\\IPIS\\voice\\regional\\Marathi\\leave_time.wav,C:\\IPIS\\voice\\regional\\Marathi\\might_come.wav,C:\\IPIS\\voice\\regional\\Marathi\\platform_no.wav,C:\\IPIS\\voice\\regional\\Marathi\\ready_to_leave.wav,C:\\IPIS\\voice\\regional\\Marathi\\regretted_inconvenience.wav,C:\\IPIS\\voice\\regional\\Marathi\\running_late.wav,C:\\IPIS\\voice\\regional\\Marathi\\running_on_time.wav,C:\\IPIS\\voice\\regional\\Marathi\\scheduled_time.wav,C:\\IPIS\\voice\\regional\\Marathi\\scheduled_time_2.wav,C:\\IPIS\\voice\\regional\\Marathi\\will_arrive.wav,C:\\IPIS\\voice\\regional\\Marathi\\will_leave.wav,C:\\IPIS\\voice\\regional\\Marathi\\wishes.wav,C:\\IPIS\\voice\\regional\\Marathi\\now_this_train.wav,C:\\IPIS\\voice\\regional\\Marathi\\per.wav,C:\\IPIS\\voice\\regional\\Marathi\\this_train.wav,C:\\IPIS\\voice\\regional\\Marathi\\scheduled_departure.wav,C:\\IPIS\\voice\\regional\\Marathi\\today_this_train.wav,C:\\IPIS\\voice\\regional\\Marathi\\train_is_arriving.wav,C:\\IPIS\\voice\\regional\\Marathi\\hin_kadihai.wav,C:\\IPIS\\voice\\regional\\Marathi\\hin_se.wav,C:\\IPIS\\voice\\regional\\Marathi\\ravana.wav,C:\\IPIS\\voice\\regional\\Marathi\\hogi.wav,C:\\IPIS\\voice\\regional\\Marathi\\thak.wav,C:\\IPIS\\voice\\regional\\Marathi\\jayegi.wav,C:\\IPIS\\voice\\regional\\Marathi\\train_cancelled.wav,C:\\IPIS\\voice\\regional\\Marathi\\on.wav,C:\\IPIS\\voice\\regional\\Marathi\\Today.wav,C:\\IPIS\\voice\\regional\\Marathi\\The.wav,C:\\IPIS\\voice\\regional\\Marathi\\going_from.wav,C:\\IPIS\\voice\\regional\\Marathi\\no_jayegi.wav,C:\\IPIS\\voice\\regional\\Marathi\\running_time.wav,C:\\IPIS\\voice\\regional\\Marathi\\already_announce.wav,C:\\IPIS\\voice\\regional\\Marathi\\expected_shortly.wav,C:\\IPIS\\voice\\regional\\Marathi\\varoon.wav,C:\\IPIS\\voice\\regional\\Marathi\\regulated.wav,C:\\IPIS\\voice\\regional\\Marathi\\terminated.wav";
      string number_name5 = "0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59";
      string number_path5 = "c:\\IPIS\\voice\\regional\\Marathi\\0.wav,c:\\IPIS\\voice\\regional\\Marathi\\1.wav,c:\\IPIS\\voice\\regional\\Marathi\\2.wav,c:\\IPIS\\voice\\regional\\Marathi\\3.wav,c:\\IPIS\\voice\\regional\\Marathi\\4.wav,c:\\IPIS\\voice\\regional\\Marathi\\5.wav,c:\\IPIS\\voice\\regional\\Marathi\\6.wav,c:\\IPIS\\voice\\regional\\Marathi\\7.wav,c:\\IPIS\\voice\\regional\\Marathi\\8.wav,c:\\IPIS\\voice\\regional\\Marathi\\9.wav,C:\\IPIS\\voice\\regional\\Marathi\\10.wav,C:\\IPIS\\voice\\regional\\Marathi\\11.wav,C:\\IPIS\\voice\\regional\\Marathi\\12.wav,C:\\IPIS\\voice\\regional\\Marathi\\13.wav,C:\\IPIS\\voice\\regional\\Marathi\\14.wav,C:\\IPIS\\voice\\regional\\Marathi\\15.wav,C:\\IPIS\\voice\\regional\\Marathi\\16.wav,C:\\IPIS\\voice\\regional\\Marathi\\17.wav,C:\\IPIS\\voice\\regional\\Marathi\\18.wav,C:\\IPIS\\voice\\regional\\Marathi\\19.wav,C:\\IPIS\\voice\\regional\\Marathi\\20.wav,C:\\IPIS\\voice\\regional\\Marathi\\21.wav,C:\\IPIS\\voice\\regional\\Marathi\\22.wav,C:\\IPIS\\voice\\regional\\Marathi\\23.wav,C:\\IPIS\\voice\\regional\\Marathi\\24.wav,C:\\IPIS\\voice\\regional\\Marathi\\25.wav,C:\\IPIS\\voice\\regional\\Marathi\\26.wav,C:\\IPIS\\voice\\regional\\Marathi\\27.wav,C:\\IPIS\\voice\\regional\\Marathi\\28.wav,C:\\IPIS\\voice\\regional\\Marathi\\29.wav,C:\\IPIS\\voice\\regional\\Marathi\\30.wav,C:\\IPIS\\voice\\regional\\Marathi\\31.wav,C:\\IPIS\\voice\\regional\\Marathi\\32.wav,C:\\IPIS\\voice\\regional\\Marathi\\33.wav,C:\\IPIS\\voice\\regional\\Marathi\\34.wav,C:\\IPIS\\voice\\regional\\Marathi\\35.wav,C:\\IPIS\\voice\\regional\\Marathi\\36.wav,C:\\IPIS\\voice\\regional\\Marathi\\37.wav,C:\\IPIS\\voice\\regional\\Marathi\\38.wav,C:\\IPIS\\voice\\regional\\Marathi\\39.wav,C:\\IPIS\\voice\\regional\\Marathi\\40.wav,C:\\IPIS\\voice\\regional\\Marathi\\41.wav,C:\\IPIS\\voice\\regional\\Marathi\\42.wav,C:\\IPIS\\voice\\regional\\Marathi\\43.wav,C:\\IPIS\\voice\\regional\\Marathi\\44.wav,C:\\IPIS\\voice\\regional\\Marathi\\45.wav,C:\\IPIS\\voice\\regional\\Marathi\\46.wav,C:\\IPIS\\voice\\regional\\Marathi\\47.wav,C:\\IPIS\\voice\\regional\\Marathi\\48.wav,C:\\IPIS\\voice\\regional\\Marathi\\49.wav,C:\\IPIS\\voice\\regional\\Marathi\\50.wav,C:\\IPIS\\voice\\regional\\Marathi\\51.wav,C:\\IPIS\\voice\\regional\\Marathi\\52.wav,C:\\IPIS\\voice\\regional\\Marathi\\53.wav,C:\\IPIS\\voice\\regional\\Marathi\\54.wav,C:\\IPIS\\voice\\regional\\Marathi\\55.wav,C:\\IPIS\\voice\\regional\\Marathi\\56.wav,C:\\IPIS\\voice\\regional\\Marathi\\57.wav,C:\\IPIS\\voice\\regional\\Marathi\\58.wav,C:\\IPIS\\voice\\regional\\Marathi\\59.wav";
      string kind_attention_path6 = "C:\\IPIS\\voice\\regional\\Bengali\\kind_attn.wav";
      string trainno_voice_path6 = "c:\\IPIS\\voice\\regional\\Bengali\\trainno.wav";
      string trainno_path6 = "c:\\IPIS\\voice\\regional\\Bengali\\0.wav,c:\\IPIS\\voice\\regional\\Bengali\\1.wav,c:\\IPIS\\voice\\regional\\Bengali\\2.wav,c:\\IPIS\\voice\\regional\\Bengali\\3.wav,c:\\IPIS\\voice\\regional\\Bengali\\4.wav,c:\\IPIS\\voice\\regional\\Bengali\\5.wav,c:\\IPIS\\voice\\regional\\Bengali\\6.wav,c:\\IPIS\\voice\\regional\\Bengali\\7.wav,c:\\IPIS\\voice\\regional\\Bengali\\8.wav,c:\\IPIS\\voice\\regional\\Bengali\\9.wav";
      string train_status_name6 = "HAS LEFT,ARRIVING ON,ARRIVED ON,AFTER_SOMETIME,CANCELLED,PLATFORM_CHANGE,CHANGE_TIME,CHANGE_WAY,COME,INDEFINITE LATE,LEAVE TIME,MIGHT COME,PLATFORM NO,READY TO LEAVE,INCONVENIENCE,RUNNING LATE,RUNNING ON TIME,SCHEDULED TIME,SCHEDULE_TIME_2,WILL ARRIVE,WILL LEAVE,COMFORTABLE WISH,NOW_THIS_TRAIN,PER,THIS_TRAIN,SCHEDULED DEPARTURE,TODAY_THIS_TRAIN,TRAIN_ARRIVING,IS_THERE,SE,RAVANA,HOGI,THAK,JAYEGI,NO_PLATFORM,EXPECTED SHORTLY,WILL_COME,ON PLATFORM,TO DEPART,FROM,TO,HAS LEFT,TERMINATED,TERMINATED2";
      string train_status_path6 = "C:\\IPIS\\voice\\regional\\Bengali\\hasleft.wav,C:\\IPIS\\voice\\regional\\Bengali\\arriving_on.wav,C:\\IPIS\\voice\\regional\\Bengali\\arrivedon.wav,C:\\IPIS\\voice\\regional\\Bengali\\after_sometime.wav,C:\\IPIS\\voice\\regional\\Bengali\\cancelled.wav,C:\\IPIS\\voice\\regional\\Bengali\\change_pfno_time.wav,C:\\IPIS\\voice\\regional\\Bengali\\change_time.wav,C:\\IPIS\\voice\\regional\\Bengali\\change_way.wav,C:\\IPIS\\voice\\regional\\Bengali\\come.wav,C:\\IPIS\\voice\\regional\\Bengali\\indefinite_late.wav,C:\\IPIS\\voice\\regional\\Bengali\\leave_time.wav,C:\\IPIS\\voice\\regional\\Bengali\\might_come.wav,C:\\IPIS\\voice\\regional\\Bengali\\platform_no.wav,C:\\IPIS\\voice\\regional\\Bengali\\ready_to_leave.wav,C:\\IPIS\\voice\\regional\\Bengali\\regretted_inconvenience.wav,C:\\IPIS\\voice\\regional\\Bengali\\running_late.wav,C:\\IPIS\\voice\\regional\\Bengali\\running_on_time.wav,C:\\IPIS\\voice\\regional\\Bengali\\scheduled_time.wav,C:\\IPIS\\voice\\regional\\Bengali\\scheduled_time_2.wav,C:\\IPIS\\voice\\regional\\Bengali\\will_arrive.wav,C:\\IPIS\\voice\\regional\\Bengali\\will_leave.wav,C:\\IPIS\\voice\\regional\\Bengali\\wishes.wav,C:\\IPIS\\voice\\regional\\Bengali\\now_this_train.wav,C:\\IPIS\\voice\\regional\\Bengali\\per.wav,C:\\IPIS\\voice\\regional\\Bengali\\this_train.wav,C:\\IPIS\\voice\\regional\\Bengali\\scheduled_departure.wav,C:\\IPIS\\voice\\regional\\Bengali\\today_this_train.wav,C:\\IPIS\\voice\\regional\\Bengali\\train_is_arriving.wav,C:\\IPIS\\voice\\regional\\Bengali\\hin_kadihai.wav,C:\\IPIS\\voice\\regional\\Bengali\\hin_se.wav,C:\\IPIS\\voice\\regional\\Bengali\\ravana.wav,C:\\IPIS\\voice\\regional\\Bengali\\hogi.wav,C:\\IPIS\\voice\\regional\\Bengali\\thak.wav,C:\\IPIS\\voice\\regional\\Bengali\\jayegi.wav,C:\\IPIS\\voice\\regional\\Bengali\\no_platform.wav,C:\\IPIS\\voice\\regional\\Bengali\\expected_shortly.wav,C:\\IPIS\\voice\\regional\\Bengali\\asbe.wav,C:\\IPIS\\voice\\regional\\Bengali\\on_platform_no.wav,C:\\IPIS\\voice\\regional\\Bengali\\to_depart.wav,C:\\IPIS\\voice\\regional\\Bengali\\from.wav,C:\\IPIS\\voice\\regional\\Bengali\\to.wav,C:\\IPIS\\voice\\regional\\Bengali\\has_left.wav,C:\\IPIS\\voice\\regional\\Bengali\\terminated2.wav,C:\\IPIS\\voice\\regional\\Bengali\\terminated.wav";
      string time_name6 = "HOUR,MIN,HOURS,MINUTE,BAJE";
      string time_path6 = "C:\\IPIS\\voice\\regional\\Bengali\\hour.wav,C:\\IPIS\\voice\\regional\\Bengali\\min.wav,C:\\IPIS\\voice\\regional\\Bengali\\hours.wav,C:\\IPIS\\voice\\regional\\Bengali\\minute.wav,C:\\IPIS\\voice\\regional\\Bengali\\hn_baje.wav";
      string number_name6 = "0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59";
      string number_path6 = "c:\\IPIS\\voice\\regional\\Bengali\\0.wav,c:\\IPIS\\voice\\regional\\Bengali\\1.wav,c:\\IPIS\\voice\\regional\\Bengali\\2.wav,c:\\IPIS\\voice\\regional\\Bengali\\3.wav,c:\\IPIS\\voice\\regional\\Bengali\\4.wav,c:\\IPIS\\voice\\regional\\Bengali\\5.wav,c:\\IPIS\\voice\\regional\\Bengali\\6.wav,c:\\IPIS\\voice\\regional\\Bengali\\7.wav,c:\\IPIS\\voice\\regional\\Bengali\\8.wav,c:\\IPIS\\voice\\regional\\Bengali\\9.wav,C:\\IPIS\\voice\\regional\\Bengali\\10.wav,C:\\IPIS\\voice\\regional\\Bengali\\11.wav,C:\\IPIS\\voice\\regional\\Bengali\\12.wav,C:\\IPIS\\voice\\regional\\Bengali\\13.wav,C:\\IPIS\\voice\\regional\\Bengali\\14.wav,C:\\IPIS\\voice\\regional\\Bengali\\15.wav,C:\\IPIS\\voice\\regional\\Bengali\\16.wav,C:\\IPIS\\voice\\regional\\Bengali\\17.wav,C:\\IPIS\\voice\\regional\\Bengali\\18.wav,C:\\IPIS\\voice\\regional\\Bengali\\19.wav,C:\\IPIS\\voice\\regional\\Bengali\\20.wav,C:\\IPIS\\voice\\regional\\Bengali\\21.wav,C:\\IPIS\\voice\\regional\\Bengali\\22.wav,C:\\IPIS\\voice\\regional\\Bengali\\23.wav,C:\\IPIS\\voice\\regional\\Bengali\\24.wav,C:\\IPIS\\voice\\regional\\Bengali\\25.wav,C:\\IPIS\\voice\\regional\\Bengali\\26.wav,C:\\IPIS\\voice\\regional\\Bengali\\27.wav,C:\\IPIS\\voice\\regional\\Bengali\\28.wav,C:\\IPIS\\voice\\regional\\Bengali\\29.wav,C:\\IPIS\\voice\\regional\\Bengali\\30.wav,C:\\IPIS\\voice\\regional\\Bengali\\31.wav,C:\\IPIS\\voice\\regional\\Bengali\\32.wav,C:\\IPIS\\voice\\regional\\Bengali\\33.wav,C:\\IPIS\\voice\\regional\\Bengali\\34.wav,C:\\IPIS\\voice\\regional\\Bengali\\35.wav,C:\\IPIS\\voice\\regional\\Bengali\\36.wav,C:\\IPIS\\voice\\regional\\Bengali\\37.wav,C:\\IPIS\\voice\\regional\\Bengali\\38.wav,C:\\IPIS\\voice\\regional\\Bengali\\39.wav,C:\\IPIS\\voice\\regional\\Bengali\\40.wav,C:\\IPIS\\voice\\regional\\Bengali\\41.wav,C:\\IPIS\\voice\\regional\\Bengali\\42.wav,C:\\IPIS\\voice\\regional\\Bengali\\43.wav,C:\\IPIS\\voice\\regional\\Bengali\\44.wav,C:\\IPIS\\voice\\regional\\Bengali\\45.wav,C:\\IPIS\\voice\\regional\\Bengali\\46.wav,C:\\IPIS\\voice\\regional\\Bengali\\47.wav,C:\\IPIS\\voice\\regional\\Bengali\\48.wav,C:\\IPIS\\voice\\regional\\Bengali\\49.wav,C:\\IPIS\\voice\\regional\\Bengali\\50.wav,C:\\IPIS\\voice\\regional\\Bengali\\51.wav,C:\\IPIS\\voice\\regional\\Bengali\\52.wav,C:\\IPIS\\voice\\regional\\Bengali\\53.wav,C:\\IPIS\\voice\\regional\\Bengali\\54.wav,C:\\IPIS\\voice\\regional\\Bengali\\55.wav,C:\\IPIS\\voice\\regional\\Bengali\\56.wav,C:\\IPIS\\voice\\regional\\Bengali\\57.wav,C:\\IPIS\\voice\\regional\\Bengali\\58.wav,C:\\IPIS\\voice\\regional\\Bengali\\59.wav";
      string kind_attention_path7 = "C:\\IPIS\\voice\\regional\\Chattisgarh\\hin_kind_attn.wav";
      string trainno_voice_path7 = "c:\\IPIS\\voice\\regional\\Chattisgarh\\hin_trainno.wav";
      string trainno_path7 = "c:\\IPIS\\voice\\regional\\Chattisgarh\\0.wav,c:\\IPIS\\voice\\regional\\Chattisgarh\\1.wav,c:\\IPIS\\voice\\regional\\Chattisgarh\\2.wav,c:\\IPIS\\voice\\regional\\Chattisgarh\\3.wav,c:\\IPIS\\voice\\regional\\Chattisgarh\\4.wav,c:\\IPIS\\voice\\regional\\Chattisgarh\\5.wav,c:\\IPIS\\voice\\regional\\Chattisgarh\\6.wav,c:\\IPIS\\voice\\regional\\Chattisgarh\\7.wav,c:\\IPIS\\voice\\regional\\Chattisgarh\\8.wav,c:\\IPIS\\voice\\regional\\Chattisgarh\\9.wav";
      string train_status_name7 = "HAS LEFT,ARRIVING ON,ARRIVED ON,AFTER_SOMETIME,CANCELLED,PLATFORM_CHANGE,CHANGE_TIME,CHANGE_WAY,COME,INDEFINITE LATE,LEAVE TIME,MIGHT COME,PLATFORM NO,READY TO LEAVE,INCONVENIENCE,RUNNING LATE,RUNNING ON TIME,SCHEDULED TIME,SCHEDULE_TIME_2,WILL ARRIVE,WILL LEAVE,COMFORTABLE WISH,NOW_THIS_TRAIN,PER,THIS_TRAIN,SCHEDULED DEPARTURE,TODAY_THIS_TRAIN,TRAIN_ARRIVING,IS_THERE,SE,RAVANA,HOGI,THAK,JAYEGI";
      string train_status_path7 = "C:\\IPIS\\voice\\regional\\Chattisgarh\\hasleft.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\arriving_on.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\arrivedon.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\after_sometime.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\cancelled.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\change_pfno_time.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\change_time.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\change_way.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\come.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\indefinite_late.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\leave_time.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\might_come.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\platform_no.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\ready_to_leave.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\regretted_inconvenience.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\running_late.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\running_on_time.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\scheduled_time.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\scheduled_time_2.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\will_arrive.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\will_leave.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\wishes.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\now_this_train.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\per.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\this_train.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\scheduled_departure.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\today_this_train.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\train_is_arriving.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\hin_kadihai.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\hin_se.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\ravana.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\hogi.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\thak.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\jayegi.wav";
      string time_name7 = "HOUR,MIN,HOURS,MINUTE,BAJE";
      string time_path7 = "C:\\IPIS\\voice\\regional\\Chattisgarh\\hour.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\min.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\hours.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\minute.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\hn_baje.wav";
      string number_name7 = "0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59";
      string number_path7 = "c:\\IPIS\\voice\\regional\\Chattisgarh\\0.wav,c:\\IPIS\\voice\\regional\\Chattisgarh\\1.wav,c:\\IPIS\\voice\\regional\\Chattisgarh\\2.wav,c:\\IPIS\\voice\\regional\\Chattisgarh\\3.wav,c:\\IPIS\\voice\\regional\\Chattisgarh\\4.wav,c:\\IPIS\\voice\\regional\\Chattisgarh\\5.wav,c:\\IPIS\\voice\\regional\\Chattisgarh\\6.wav,c:\\IPIS\\voice\\regional\\Chattisgarh\\7.wav,c:\\IPIS\\voice\\regional\\Chattisgarh\\8.wav,c:\\IPIS\\voice\\regional\\Chattisgarh\\9.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\10.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\11.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\12.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\13.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\14.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\15.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\16.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\17.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\18.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\19.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\20.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\21.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\22.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\23.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\24.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\25.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\26.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\27.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\28.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\29.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\30.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\31.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\32.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\33.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\34.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\35.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\36.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\37.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\38.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\39.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\40.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\41.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\42.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\43.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\44.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\45.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\46.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\47.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\48.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\49.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\50.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\51.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\52.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\53.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\54.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\55.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\56.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\57.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\58.wav,C:\\IPIS\\voice\\regional\\Chattisgarh\\59.wav";
      this.Create_PlayList(FileName1, trainno_voice_path1, trainno_path1, train_status_name1, train_status_path1, empty1, empty2, kind_attention_path1, time_name1, time_path1, number_name1, number_path1, "", "", "", "", alphabet_name, alphabet_path);
      this.Create_PlayList(FileName3, trainno_voice_path3, trainno_path3, train_status_name3, train_status_path3, empty1, empty2, kind_attention_path3, time_name3, time_path3, number_name3, number_path3, "", "", "", "", alphabet_name, alphabet_path);
      this.Create_PlayList(FileName2, trainno_voice_path2, trainno_path2, train_status_name2, train_status_path2, empty1, empty2, kind_attention_path2, time_name2, time_path2, number_name2, number_path2, reg_time_hours_name, reg_time_hours_path, reg_number_name, reg_number_path, alphabet_name, alphabet_path);
      this.Create_PlayList(FileName4, trainno_voice_path4, trainno_path4, train_status_name4, train_status_path4, empty1, empty2, kind_attention_path4, time_name4, time_path4, number_name4, number_path4, empty14, empty15, empty16, empty17, alphabet_name, alphabet_path);
      this.Create_PlayList(FileName5, trainno_voice_path5, trainno_path5, train_status_name5, train_status_path5, empty1, empty2, kind_attention_path5, time_name5, time_path5, number_name5, number_path5, empty29, empty30, empty31, empty32, alphabet_name, alphabet_path);
      this.Create_PlayList(FileName6, trainno_voice_path7, trainno_path7, train_status_name7, train_status_path7, empty1, empty2, kind_attention_path7, time_name7, time_path7, number_name7, number_path7, "", "", "", "", alphabet_name, alphabet_path);
      this.Create_PlayList(FileName7, trainno_voice_path6, trainno_path6, train_status_name6, train_status_path6, empty1, empty2, kind_attention_path6, time_name6, time_path6, number_name6, number_path6, "", "", "", "", alphabet_name, alphabet_path);
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void btnExit_Click(object sender, EventArgs e)
{
  this.Close();
}

  private void btnAdd_Click(object sender, EventArgs e)
  {
    if (!Information.IsNothing((object) this.event_addTrainVoice))
    {
      if (!this.event_addTrainVoice.IsDisposed)
      {
        this.event_addTrainVoice.BringToFront();
      }
      else
      {
        this.event_addTrainVoice = new frmAddTrainNameVoice();
        this.event_addTrainVoice.Show();
      }
    }
    else
    {
      this.event_addTrainVoice = new frmAddTrainNameVoice();
      this.event_addTrainVoice.Show();
    }
  }

  private void btnAddStationVoice_Click(object sender, EventArgs e)
  {
    if (!Information.IsNothing((object) this.event_addStationNameVoice))
    {
      if (!this.event_addStationNameVoice.IsDisposed)
      {
        this.event_addStationNameVoice.BringToFront();
      }
      else
      {
        this.event_addStationNameVoice = new frmStationNameVoice();
        this.event_addStationNameVoice.Show();
      }
    }
    else
    {
      this.event_addStationNameVoice = new frmStationNameVoice();
      this.event_addStationNameVoice.Show();
    }
  }
}

}