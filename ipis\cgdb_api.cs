// Decompiled with JetBrains decompiler
// Type: ipis.cgdb_api
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using System.Diagnostics;
using System.Threading;

namespace ipis
{

public class cgdb_api
{
  private static byte[] pkt_buf = new byte[201];
  private static byte[] rxbuf = new byte[2001];

  [DebuggerNonUserCode]
  public cgdb_api()
  {
  }

  public static byte cgdb_link_check(
    byte pdch_addr,
    byte cgdb_addr,
    string platform_no,
    ref byte[] cgdb_pkt,
    ref short length)
  {
    cgdb_api.pkt_buf[0] = (byte) 170;
    cgdb_api.pkt_buf[1] = (byte) 204;
    cgdb_api.pkt_buf[2] = (byte) 0;
    cgdb_api.pkt_buf[3] = (byte) 10;
    cgdb_api.pkt_buf[4] = frmMainFormIPIS.ccu_addr;
    cgdb_api.pkt_buf[5] = (byte) 0;
    cgdb_api.pkt_buf[6] = pdch_addr;
    cgdb_api.pkt_buf[7] = cgdb_addr;
    cgdb_api.pkt_buf[8] = (byte) 0;
    cgdb_api.pkt_buf[9] = (byte) 128 /*0x80*/;
    ushort length1 = checked ((ushort) ((int) unchecked ((ushort) ((int) (ushort) (0U << 8) | 10)) + 2));
    length = checked ((short) length1);
    int index = 0;
    while (index < (int) length1)
    {
      cgdb_pkt[index] = cgdb_api.pkt_buf[index];
      checked { ++index; }
    }
    Checksum.prepare_checksum(ref cgdb_api.pkt_buf, length1);
    if (RS232.Serial_Write(ref cgdb_api.pkt_buf, (int) length1) == 1)
      return 1;
    Log_file.Log(string.Format("Write to COM port failed, may be COM port deos't exit. Please check the COM PORT settings"));
    return 3;
  }

  public static byte cgdb_link_check_res_pkt(
    byte pdch_addr,
    byte cgdb_addr,
    string platform_no,
    ref byte[] cgdb_pkt,
    ref short length,
    ref byte cgdb_Sys_Cfg)
  {
    if (RS232.Serial_Read(ref cgdb_api.rxbuf) == (byte) 1)
    {
      ushort Pkt_length = checked ((ushort) unchecked ((int) (ushort) ((uint) (ushort) cgdb_api.rxbuf[0] << 8) + (int) cgdb_api.rxbuf[1]));
      length = checked ((short) ((int) Pkt_length + 2));
      byte index = 0;
      cgdb_pkt[0] = (byte) 170;
      cgdb_pkt[1] = (byte) 204;
      while ((uint) index < (uint) Pkt_length)
      {
        cgdb_pkt[checked ((int) index + 2)] = cgdb_api.rxbuf[(int) index];
        checked { ++index; }
      }
      byte num = 0;
      if ((int) cgdb_api.rxbuf[2] != (int) pdch_addr & (int) cgdb_api.rxbuf[3] != (int) cgdb_addr)
      {
        Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr}LINK CHECK RESPONSE PACKET: IN CORRECT SOURCE ADDRESS");
        num = (byte) 1;
      }
      if (Pkt_length != (ushort) 12)
      {
        Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr}LINK CHECK RESPONSE PACKET: IN VALID DATA LENGTH");
        num = (byte) 1;
      }
      if (cgdb_api.rxbuf[7] != (byte) 192 /*0xC0*/)
      {
        Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr}LINK CHECK RESPONSE PACKET: IN-VALID RESPONSE PACKET FUNCTION CODE");
        num = (byte) 1;
      }
      switch (cgdb_api.rxbuf[8])
      {
        case 0:
          Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr}LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully ");
          break;
        case 2:
          Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr}LINK CHECK COMMAND PACKET STATUS: CRC FAIL");
          num = (byte) 1;
          break;
        case 6:
          Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr}LINK CHECK COMMAND PACKET STATUS: IN VALID FUNCTION CODE");
          num = (byte) 1;
          break;
        case 35:
          Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr}LINK CHECK COMMAND PACKET STATUS: IN VALID DATA LENGTH");
          num = (byte) 1;
          break;
      }
      cgdb_Sys_Cfg = cgdb_api.rxbuf[9];
      if (Checksum.Checksum_Calc(ref cgdb_api.rxbuf, Pkt_length) == 0)
      {
        Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr}LINK CHECK RESPONSE PACKET: CHECKSUM FAILED ");
        return 9;
      }
      if (num != (byte) 0)
      {
        Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr}LINK CHECK COMMAND IS UNSUCCESSFULL");
        return 0;
      }
      Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr}LINK CHECK COMMAND IS SUCCESSFUL");
      return 1;
    }
    Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} LINK FAILURE or ADDRESSED CGDB DOESn't EXIST");
    return 2;
  }

  public static byte cgdb_default_msg(
    byte pdch_addr,
    byte cgdb_addr,
    byte[] station_name,
    byte[] region_name,
    string platform_no,
    byte cgdb_sw_dly,
    byte video_type)
  {
    cgdb_api.pkt_buf[0] = (byte) 170;
    cgdb_api.pkt_buf[1] = (byte) 204;
    cgdb_api.pkt_buf[2] = (byte) 0;
    cgdb_api.pkt_buf[3] = (byte) 22;
    cgdb_api.pkt_buf[4] = frmMainFormIPIS.ccu_addr;
    cgdb_api.pkt_buf[5] = (byte) 0;
    cgdb_api.pkt_buf[6] = pdch_addr;
    cgdb_api.pkt_buf[7] = cgdb_addr;
    cgdb_api.pkt_buf[8] = (byte) 0;
    cgdb_api.pkt_buf[9] = (byte) 129;
    cgdb_api.pkt_buf[10] = (byte) 1;
    cgdb_api.pkt_buf[11] = cgdb_sw_dly;
    cgdb_api.pkt_buf[12] = (byte) 0;
    cgdb_api.pkt_buf[13] = video_type;
    cgdb_api.pkt_buf[14] = station_name[0];
    cgdb_api.pkt_buf[15] = station_name[1];
    cgdb_api.pkt_buf[16 /*0x10*/] = station_name[2];
    cgdb_api.pkt_buf[17] = station_name[3];
    cgdb_api.pkt_buf[18] = region_name[0];
    cgdb_api.pkt_buf[19] = region_name[1];
    cgdb_api.pkt_buf[20] = region_name[2];
    cgdb_api.pkt_buf[21] = region_name[3];
    ushort length = 24;
    Checksum.prepare_checksum(ref cgdb_api.pkt_buf, length);
    if (RS232.Serial_Write(ref cgdb_api.pkt_buf, (int) length) != 1)
    {
      Log_file.Log(string.Format("Write to COM port failed, may be COM port deos't exit. Please check the COM PORT settings"));
      return 3;
    }
    Thread.Sleep(100);
    byte num = 0;
    if (RS232.Serial_Read(ref cgdb_api.rxbuf) == (byte) 1)
    {
      ushort Pkt_length = checked ((ushort) unchecked ((int) (ushort) ((uint) (ushort) cgdb_api.rxbuf[0] << 8) + (int) cgdb_api.rxbuf[1]));
      if (Checksum.Checksum_Calc(ref cgdb_api.rxbuf, Pkt_length) == 0)
      {
        Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} DEFAULT DATA RESPONSE PACKET: CHECKSUM FAILED ");
        num = (byte) 1;
      }
      if ((int) cgdb_api.rxbuf[2] != (int) pdch_addr & (int) cgdb_api.rxbuf[3] != (int) cgdb_addr)
      {
        Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} DEFAULT DATA RESPONSE PACKET: IN CORRECT SOURCE ADDRESS");
        num = (byte) 1;
      }
      if (Pkt_length != (ushort) 11)
      {
        Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} DEFAULT DATA RESPONSE PACKET: IN VALID DATA LENGTH");
        num = (byte) 1;
      }
      if (cgdb_api.rxbuf[7] != (byte) 193)
      {
        Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} DEFAULT DATA RESPONSE PACKET: IN-VALID RESPONSE PACKET FUNCTION CODE");
        num = (byte) 1;
      }
      switch (cgdb_api.rxbuf[8])
      {
        case 0:
          Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} DEFAULT DATA PACKET STATUS: Packet Received and proceed successfully ");
          break;
        case 2:
          Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} DEFAULT DATA PACKET STATUS: CRC FAIL");
          num = (byte) 1;
          break;
        case 6:
          Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} DEFAULT DATA PACKET STATUS: IN VALID FUNCTION CODE");
          num = (byte) 1;
          break;
        case 35:
          Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} DEFAULT DATA PACKET STATUS: IN VALID DATA LENGTH");
          num = (byte) 1;
          break;
      }
      if (num != (byte) 0)
      {
        Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} DEFAULT DATA PACKET IS UNSUCCESSFULL");
        return 0;
      }
      Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} DEFAULT DATA PACKET IS SUCCESSFUL");
      return 1;
    }
    Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} LINK FAILURE or ADDRESSED CGDB DOESn't EXIST");
    return 2;
  }

  public static byte cgdb_msg(
    byte pdch_addr,
    byte cgdb_addr,
    string platform_no,
    byte[] buf,
    byte length,
    byte cgdb_sw_dly,
    byte video_type)
  {
    cgdb_api.pkt_buf[0] = (byte) 170;
    cgdb_api.pkt_buf[1] = (byte) 204;
    cgdb_api.pkt_buf[4] = frmMainFormIPIS.ccu_addr;
    cgdb_api.pkt_buf[5] = (byte) 0;
    cgdb_api.pkt_buf[6] = pdch_addr;
    cgdb_api.pkt_buf[7] = cgdb_addr;
    cgdb_api.pkt_buf[8] = (byte) 0;
    cgdb_api.pkt_buf[9] = (byte) 129;
    cgdb_api.pkt_buf[10] = (byte) 0;
    cgdb_api.pkt_buf[11] = cgdb_sw_dly;
    cgdb_api.pkt_buf[12] = (byte) 0;
    cgdb_api.pkt_buf[13] = video_type;
    byte index = 0;
    while ((uint) index < (uint) length)
    {
      cgdb_api.pkt_buf[checked ((int) index + 14)] = buf[(int) index];
      checked { ++index; }
    }
    ushort num1 = (ushort) checked ((byte) unchecked ((int) length + 14));
    cgdb_api.pkt_buf[3] = checked ((byte) ((int) num1 & (int) byte.MaxValue));
    cgdb_api.pkt_buf[2] = checked ((byte) (((int) num1 & 65280) >> 8));
    ushort length1 = checked ((ushort) ((int) num1 + 2));
    Checksum.prepare_checksum(ref cgdb_api.pkt_buf, length1);
    if (RS232.Serial_Write(ref cgdb_api.pkt_buf, (int) length1) != 1)
    {
      Log_file.Log(string.Format("Write to COM port failed, may be COM port deos't exit. Please check the COM PORT settings"));
      return 3;
    }
    Thread.Sleep(100);
    byte num2 = 0;
    if (RS232.Serial_Read(ref cgdb_api.rxbuf) == (byte) 1)
    {
      ushort Pkt_length = checked ((ushort) unchecked ((int) (ushort) ((uint) (ushort) cgdb_api.rxbuf[0] << 8) + (int) cgdb_api.rxbuf[1]));
      if (Checksum.Checksum_Calc(ref cgdb_api.rxbuf, Pkt_length) == 0)
      {
        Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} DATA RESPONSE PACKET: CHECKSUM FAILED ");
        num2 = (byte) 1;
      }
      if ((int) cgdb_api.rxbuf[2] != (int) pdch_addr & (int) cgdb_api.rxbuf[3] != (int) cgdb_addr)
      {
        Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} DATA RESPONSE PACKET: IN CORRECT SOURCE ADDRESS");
        num2 = (byte) 1;
      }
      if (Pkt_length != (ushort) 11)
      {
        Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} DATA RESPONSE PACKET: IN VALID DATA LENGTH");
        num2 = (byte) 1;
      }
      if (cgdb_api.rxbuf[7] != (byte) 193)
      {
        Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} DATA RESPONSE PACKET: IN-VALID RESPONSE PACKET FUNCTION CODE");
        num2 = (byte) 1;
      }
      switch (cgdb_api.rxbuf[8])
      {
        case 0:
          Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} DATA PACKET STATUS: Packet Received and proceed successfully ");
          break;
        case 2:
          Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} DATA PACKET STATUS: CRC FAIL");
          num2 = (byte) 1;
          break;
        case 6:
          Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} DATA PACKET STATUS: IN VALID FUNCTION CODE");
          num2 = (byte) 1;
          break;
        case 35:
          Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} DATA PACKET STATUS: IN VALID DATA LENGTH");
          num2 = (byte) 1;
          break;
      }
      if (num2 != (byte) 0)
      {
        Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} DATA PACKET IS UNSUCCESSFULL");
        return 0;
      }
      Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} DATA PACKET IS SUCCESSFUL");
      return 1;
    }
    Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} LINK FAILURE or ADDRESSED CGDB DOESn't EXIST");
    return 2;
  }

  public static byte cgdb_stop(byte pdch_addr, byte cgdb_addr, string platform_no)
  {
    cgdb_api.pkt_buf[0] = (byte) 170;
    cgdb_api.pkt_buf[1] = (byte) 204;
    cgdb_api.pkt_buf[2] = (byte) 0;
    cgdb_api.pkt_buf[3] = (byte) 10;
    cgdb_api.pkt_buf[4] = frmMainFormIPIS.ccu_addr;
    cgdb_api.pkt_buf[5] = (byte) 0;
    cgdb_api.pkt_buf[6] = pdch_addr;
    cgdb_api.pkt_buf[7] = cgdb_addr;
    cgdb_api.pkt_buf[8] = (byte) 0;
    cgdb_api.pkt_buf[9] = (byte) 130;
    ushort length = checked ((ushort) ((int) unchecked ((ushort) ((int) (ushort) (0U << 8) | 10)) + 2));
    Checksum.prepare_checksum(ref cgdb_api.pkt_buf, length);
    if (RS232.Serial_Write(ref cgdb_api.pkt_buf, (int) length) != 1)
    {
      Log_file.Log(string.Format("Write to COM port failed, may be COM port deos't exit. Please check the COM PORT settings"));
      return 3;
    }
    Log_file.Log(string.Format("STOP command to Platform No (0) CGS send successfully", (object) platform_no));
    int num = 0;
    if (RS232.Serial_Read(ref cgdb_api.rxbuf) == (byte) 1)
    {
      ushort Pkt_length = checked ((ushort) unchecked ((int) (ushort) ((uint) (ushort) cgdb_api.rxbuf[0] << 8) + (int) cgdb_api.rxbuf[1]));
      if (Checksum.Checksum_Calc(ref cgdb_api.rxbuf, Pkt_length) == 0)
      {
        Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} DATA RESPONSE PACKET: CHECKSUM FAILED ");
        num = 1;
      }
      if ((int) cgdb_api.rxbuf[2] != (int) pdch_addr & (int) cgdb_api.rxbuf[3] != (int) cgdb_addr)
      {
        Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} DATA RESPONSE PACKET: IN CORRECT SOURCE ADDRESS");
        num = 1;
      }
      if (Pkt_length != (ushort) 11)
      {
        Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} DATA RESPONSE PACKET: IN VALID DATA LENGTH");
        num = 1;
      }
      if (cgdb_api.rxbuf[7] != (byte) 194)
      {
        Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} DATA RESPONSE PACKET: IN-VALID RESPONSE PACKET FUNCTION CODE");
        num = 1;
      }
      switch (cgdb_api.rxbuf[8])
      {
        case 0:
          Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} DATA PACKET STATUS: Packet Received and proceed successfully ");
          break;
        case 2:
          Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} DATA PACKET STATUS: CRC FAIL");
          num = 1;
          break;
        case 6:
          Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} DATA PACKET STATUS: IN VALID FUNCTION CODE");
          num = 1;
          break;
        case 35:
          Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} DATA PACKET STATUS: IN VALID DATA LENGTH");
          num = 1;
          break;
      }
      if (num != 0)
      {
        Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} DATA PACKET IS UNSUCCESSFULL");
        return 0;
      }
      Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} DATA PACKET IS SUCCESSFUL");
      return 1;
    }
    Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} LINK FAILURE or ADDRESSED CGDB DOESn't EXIST");
    return 2;
  }

  public static byte cgdb_start(byte pdch_addr, byte cgdb_addr, string platform_no)
  {
    cgdb_api.pkt_buf[0] = (byte) 170;
    cgdb_api.pkt_buf[1] = (byte) 204;
    cgdb_api.pkt_buf[2] = (byte) 0;
    cgdb_api.pkt_buf[3] = (byte) 10;
    cgdb_api.pkt_buf[4] = frmMainFormIPIS.ccu_addr;
    cgdb_api.pkt_buf[5] = (byte) 0;
    cgdb_api.pkt_buf[6] = pdch_addr;
    cgdb_api.pkt_buf[7] = cgdb_addr;
    cgdb_api.pkt_buf[8] = (byte) 0;
    cgdb_api.pkt_buf[9] = (byte) 131;
    ushort length = checked ((ushort) ((int) unchecked ((ushort) ((int) (ushort) (0U << 8) | 10)) + 2));
    Checksum.prepare_checksum(ref cgdb_api.pkt_buf, length);
    if (RS232.Serial_Write(ref cgdb_api.pkt_buf, (int) length) != 1)
    {
      Log_file.Log(string.Format("Write to COM port failed, may be COM port deos't exit. Please check the COM PORT settings"));
      return 3;
    }
    Log_file.Log(string.Format("START command to Platform No (0) CGS send successfully", (object) platform_no));
    int num = 0;
    if (RS232.Serial_Read(ref cgdb_api.rxbuf) == (byte) 1)
    {
      ushort Pkt_length = checked ((ushort) unchecked ((int) (ushort) ((uint) (ushort) cgdb_api.rxbuf[0] << 8) + (int) cgdb_api.rxbuf[1]));
      if (Checksum.Checksum_Calc(ref cgdb_api.rxbuf, Pkt_length) == 0)
      {
        Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} DATA RESPONSE PACKET: CHECKSUM FAILED ");
        num = 1;
      }
      if ((int) cgdb_api.rxbuf[2] != (int) pdch_addr & (int) cgdb_api.rxbuf[3] != (int) cgdb_addr)
      {
        Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} DATA RESPONSE PACKET: IN CORRECT SOURCE ADDRESS");
        num = 1;
      }
      if (Pkt_length != (ushort) 11)
      {
        Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} DATA RESPONSE PACKET: IN VALID DATA LENGTH");
        num = 1;
      }
      if (cgdb_api.rxbuf[7] != (byte) 195)
      {
        Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} DATA RESPONSE PACKET: IN-VALID RESPONSE PACKET FUNCTION CODE");
        num = 1;
      }
      switch (cgdb_api.rxbuf[8])
      {
        case 0:
          Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} DATA PACKET STATUS: Packet Received and proceed successfully ");
          break;
        case 2:
          Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} DATA PACKET STATUS: CRC FAIL");
          num = 1;
          break;
        case 6:
          Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} DATA PACKET STATUS: IN VALID FUNCTION CODE");
          num = 1;
          break;
        case 35:
          Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} DATA PACKET STATUS: IN VALID DATA LENGTH");
          num = 1;
          break;
      }
      if (num != 0)
      {
        Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} DATA PACKET IS UNSUCCESSFULL");
        return 0;
      }
      Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} DATA PACKET IS SUCCESSFUL");
      return 1;
    }
    Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} LINK FAILURE or ADDRESSED CGDB DOESn't EXIST");
    return 2;
  }

  public static byte cgdb_set_cfg_send_pkt(
    byte pdch_addr,
    byte cgdb_addr,
    string platform_no,
    ref byte[] cgdb_pkt,
    ref short length)
  {
    int index1 = 0;
    while (index1 < 50)
    {
      cgdb_api.pkt_buf[index1] = (byte) 0;
      checked { ++index1; }
    }
    cgdb_api.pkt_buf[0] = (byte) 170;
    cgdb_api.pkt_buf[1] = (byte) 204;
    cgdb_api.pkt_buf[2] = (byte) 0;
    cgdb_api.pkt_buf[3] = (byte) 12;
    cgdb_api.pkt_buf[4] = frmMainFormIPIS.ccu_addr;
    cgdb_api.pkt_buf[5] = (byte) 0;
    cgdb_api.pkt_buf[6] = pdch_addr;
    cgdb_api.pkt_buf[7] = cgdb_addr;
    cgdb_api.pkt_buf[8] = (byte) 0;
    cgdb_api.pkt_buf[9] = (byte) 132;
    cgdb_api.pkt_buf[10] = frmMainFormIPIS.intensity;
    cgdb_api.pkt_buf[11] = (byte) 0;
    ushort length1 = checked ((ushort) ((int) unchecked ((ushort) ((int) (ushort) (0U << 8) | 12)) + 2));
    length = checked ((short) length1);
    int index2 = 0;
    while (index2 < (int) length1)
    {
      cgdb_pkt[index2] = cgdb_api.pkt_buf[index2];
      checked { ++index2; }
    }
    Checksum.prepare_checksum(ref cgdb_api.pkt_buf, length1);
    if (RS232.Serial_Write(ref cgdb_api.pkt_buf, (int) length1) == 1)
      return 1;
    Log_file.Log(string.Format("Write to COM port failed, may be COM port deos't exit. Please check the COM PORT settings"));
    return 3;
  }

  public static byte cgdb_set_cfg_res_pkt(
    byte pdch_addr,
    byte cgdb_addr,
    string platform_no,
    ref byte[] cgdb_pkt,
    ref short length)
  {
    if (RS232.Serial_Read(ref cgdb_api.rxbuf) == (byte) 1)
    {
      ushort Pkt_length = checked ((ushort) unchecked ((int) (ushort) ((uint) (ushort) cgdb_api.rxbuf[0] << 8) + (int) cgdb_api.rxbuf[1]));
      length = checked ((short) ((int) Pkt_length + 2));
      byte index = 0;
      cgdb_pkt[0] = (byte) 170;
      cgdb_pkt[1] = (byte) 204;
      while ((uint) index < (uint) Pkt_length)
      {
        cgdb_pkt[checked ((int) index + 2)] = cgdb_api.rxbuf[(int) index];
        checked { ++index; }
      }
      byte num = 0;
      if ((int) cgdb_api.rxbuf[2] != (int) pdch_addr & (int) cgdb_api.rxbuf[3] != (int) cgdb_addr)
      {
        Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} SET CONFIGURATION RESPONSE PACKET: IN CORRECT SOURCE ADDRESS");
        num = (byte) 1;
      }
      if (Pkt_length != (ushort) 11)
      {
        Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} SET CONFIGURATION RESPONSE PACKET: IN VALID DATA LENGTH");
        num = (byte) 1;
      }
      if (cgdb_api.rxbuf[7] != (byte) 196)
      {
        Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} SET CONFIGURATION RESPONSE PACKET: IN VALID FUNCTION CODE");
        num = (byte) 1;
      }
      switch (cgdb_api.rxbuf[8])
      {
        case 0:
          Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully");
          break;
        case 2:
          Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} SET CONFIGURATION COMMAND PACKET: CRC FAIL");
          num = (byte) 1;
          break;
        case 6:
          Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} SET CONFIGURATION COMMAND PACKET: IN VALID FUNCTION CODE");
          num = (byte) 1;
          break;
        case 35:
          Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} SET CONFIGURATION COMMAND PACKET: IN VALID DATA LENGTH");
          num = (byte) 1;
          break;
      }
      if (Checksum.Checksum_Calc(ref cgdb_api.rxbuf, Pkt_length) == 0)
      {
        Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr}LINK CHECK RESPONSE PACKET: CHECKSUM FAILED ");
        return 9;
      }
      if (num != (byte) 0)
      {
        Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} SET CONFIGURATION COMMAND IS UNSUCCESSFUL");
        return 0;
      }
      Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} SET CONFIGURATION COMMAND IS SUCCESSFUL");
      return 1;
    }
    Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} LINK FAILURE or ADDRESSED CGDB DOESn't EXIST");
    return 2;
  }

  public static byte cgdb_get_cfg_send_pkt(
    byte pdch_addr,
    byte cgdb_addr,
    string platform_no,
    ref byte[] cgdb_pkt,
    ref short length)
  {
    cgdb_api.pkt_buf[0] = (byte) 170;
    cgdb_api.pkt_buf[1] = (byte) 204;
    cgdb_api.pkt_buf[2] = (byte) 0;
    cgdb_api.pkt_buf[3] = (byte) 10;
    cgdb_api.pkt_buf[4] = frmMainFormIPIS.ccu_addr;
    cgdb_api.pkt_buf[5] = (byte) 0;
    cgdb_api.pkt_buf[6] = pdch_addr;
    cgdb_api.pkt_buf[7] = cgdb_addr;
    cgdb_api.pkt_buf[8] = (byte) 0;
    cgdb_api.pkt_buf[9] = (byte) 133;
    ushort length1 = checked ((ushort) ((int) unchecked ((ushort) ((int) (ushort) (0U << 8) | 10)) + 2));
    Checksum.prepare_checksum(ref cgdb_api.pkt_buf, length1);
    length = checked ((short) length1);
    int index = 0;
    while (index < (int) length1)
    {
      cgdb_pkt[index] = cgdb_api.pkt_buf[index];
      checked { ++index; }
    }
    if (RS232.Serial_Write(ref cgdb_api.pkt_buf, (int) length1) == 1)
      return 1;
    Log_file.Log(string.Format("Write to COM port failed, may be COM port deos't exit. Please check the COM PORT settings"));
    return 3;
  }

  public static byte cgdb_get_cfg_res_pkt(
    byte pdch_addr,
    byte cgdb_addr,
    string platform_no,
    ref byte[] cgdb_pkt,
    ref short length,
    ref byte cgdb_intensity,
    ref byte cgdb_Sys_Cfg)
  {
    if (RS232.Serial_Read(ref cgdb_api.rxbuf) == (byte) 1)
    {
      ushort Pkt_length = checked ((ushort) unchecked ((int) (ushort) ((uint) (ushort) cgdb_api.rxbuf[0] << 8) + (int) cgdb_api.rxbuf[1]));
      length = checked ((short) ((int) Pkt_length + 2));
      byte index = 0;
      cgdb_pkt[0] = (byte) 170;
      cgdb_pkt[1] = (byte) 204;
      while ((uint) index < (uint) Pkt_length)
      {
        cgdb_pkt[checked ((int) index + 2)] = cgdb_api.rxbuf[(int) index];
        checked { ++index; }
      }
      byte num = 0;
      if ((int) cgdb_api.rxbuf[2] != (int) pdch_addr & (int) cgdb_api.rxbuf[3] != (int) cgdb_addr)
      {
        Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} GET CONFIGURATION RESPONSE PACKET: IN CORRECT SOURCE ADDRESS");
        num = (byte) 1;
      }
      if (Pkt_length != (ushort) 14)
      {
        Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} GET CONFIGURATION RESPONSE PACKET: IN VALID DATA LENGTH");
        num = (byte) 1;
      }
      if (cgdb_api.rxbuf[7] != (byte) 197)
      {
        Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} GET CONFIGURATION RESPONSE PACKET: IN VALID FUNCTION CODE");
        num = (byte) 1;
      }
      switch (cgdb_api.rxbuf[8])
      {
        case 0:
          Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} GET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully");
          break;
        case 2:
          Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} GET CONFIGURATION COMMAND PACKET: CRC FAIL");
          num = (byte) 1;
          break;
        case 6:
          Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} GET CONFIGURATION COMMAND PACKET: IN VALID FUNCTION CODE");
          num = (byte) 1;
          break;
        case 35:
          Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} GET CONFIGURATION COMMAND PACKET: IN VALID DATA LENGTH");
          num = (byte) 1;
          break;
      }
      cgdb_Sys_Cfg = cgdb_api.rxbuf[9];
      cgdb_intensity = cgdb_api.rxbuf[10];
      if (Checksum.Checksum_Calc(ref cgdb_api.rxbuf, Pkt_length) == 0)
      {
        Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr}LINK CHECK RESPONSE PACKET: CHECKSUM FAILED ");
        return 9;
      }
      if (num != (byte) 0)
      {
        Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} GET CONFIGURATION COMMAND IS UNSUCCESSFUL");
        return 0;
      }
      Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} GET CONFIGURATION COMMAND IS SUCCESSFUL");
      return 1;
    }
    Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} LINK FAILURE or ADDRESSED CGDB DOESn't EXIST");
    return 2;
  }

  public static byte cgdb_soft_reset(
    byte pdch_addr,
    byte cgdb_addr,
    string platform_no,
    ref byte[] cgdb_pkt,
    ref short length)
  {
    cgdb_api.pkt_buf[0] = (byte) 170;
    cgdb_api.pkt_buf[1] = (byte) 204;
    cgdb_api.pkt_buf[2] = (byte) 0;
    cgdb_api.pkt_buf[3] = (byte) 10;
    cgdb_api.pkt_buf[4] = frmMainFormIPIS.ccu_addr;
    cgdb_api.pkt_buf[5] = (byte) 0;
    cgdb_api.pkt_buf[6] = pdch_addr;
    cgdb_api.pkt_buf[7] = cgdb_addr;
    cgdb_api.pkt_buf[8] = (byte) 0;
    cgdb_api.pkt_buf[9] = (byte) 134;
    ushort length1 = checked ((ushort) ((int) unchecked ((ushort) ((int) (ushort) (0U << 8) | 10)) + 2));
    length = checked ((short) length1);
    int index = 0;
    while (index < (int) length1)
    {
      cgdb_pkt[index] = cgdb_api.pkt_buf[index];
      checked { ++index; }
    }
    Checksum.prepare_checksum(ref cgdb_api.pkt_buf, length1);
    if (RS232.Serial_Write(ref cgdb_api.pkt_buf, (int) length1) != 1)
    {
      Log_file.Log(string.Format("Write to COM port failed, may be COM port deos't exit. Please check the COM PORT settings"));
      return 3;
    }
    Thread.Sleep(10);
    Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} SOFT RESET COMMAND IS SUCCESSFUL");
    return 1;
  }

  public static byte cgdb_clr_reset(
    byte pdch_addr,
    byte cgdb_addr,
    string platform_no,
    ref byte[] cgdb_pkt,
    ref short length)
  {
    cgdb_api.pkt_buf[0] = (byte) 170;
    cgdb_api.pkt_buf[1] = (byte) 204;
    cgdb_api.pkt_buf[2] = (byte) 0;
    cgdb_api.pkt_buf[3] = (byte) 10;
    cgdb_api.pkt_buf[4] = frmMainFormIPIS.ccu_addr;
    cgdb_api.pkt_buf[5] = (byte) 0;
    cgdb_api.pkt_buf[6] = pdch_addr;
    cgdb_api.pkt_buf[7] = cgdb_addr;
    cgdb_api.pkt_buf[8] = (byte) 0;
    cgdb_api.pkt_buf[9] = (byte) 135;
    ushort length1 = checked ((ushort) ((int) unchecked ((ushort) ((int) (ushort) (0U << 8) | 10)) + 2));
    length = checked ((short) length1);
    int index = 0;
    while (index < (int) length1)
    {
      cgdb_pkt[index] = cgdb_api.pkt_buf[index];
      checked { ++index; }
    }
    Checksum.prepare_checksum(ref cgdb_api.pkt_buf, length1);
    if (RS232.Serial_Write(ref cgdb_api.pkt_buf, (int) length1) == 1)
      return 1;
    Log_file.Log(string.Format("Write to COM port failed, may be COM port deos't exit. Please check the COM PORT settings"));
    return 3;
  }

  public static byte cgdb_clr_reset_res_pkt(
    byte pdch_addr,
    byte cgdb_addr,
    string platform_no,
    ref byte[] cgdb_pkt,
    ref short length)
  {
    if (RS232.Serial_Read(ref cgdb_api.rxbuf) == (byte) 1)
    {
      ushort Pkt_length = checked ((ushort) unchecked ((int) (ushort) ((uint) (ushort) cgdb_api.rxbuf[0] << 8) + (int) cgdb_api.rxbuf[1]));
      length = checked ((short) ((int) Pkt_length + 2));
      byte index = 0;
      cgdb_pkt[0] = (byte) 170;
      cgdb_pkt[1] = (byte) 204;
      while ((uint) index < (uint) Pkt_length)
      {
        cgdb_pkt[checked ((int) index + 2)] = cgdb_api.rxbuf[(int) index];
        checked { ++index; }
      }
      byte num = 0;
      if ((int) cgdb_api.rxbuf[2] != (int) pdch_addr & (int) cgdb_api.rxbuf[3] != (int) cgdb_addr)
      {
        Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} CLEAR RESET RESPONSE PACKET: IN CORRECT SOURCE ADDRESS");
        num = (byte) 1;
      }
      if (Pkt_length != (ushort) 11)
      {
        Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} CLEAR RESET RESPONSE PACKET: IN VALID DATA LENGTH");
        num = (byte) 1;
      }
      if (cgdb_api.rxbuf[7] != (byte) 199)
      {
        Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} CLEAR RESET RESPONSE PACKET: IN VALID FUNCTION CODE");
        num = (byte) 1;
      }
      switch (cgdb_api.rxbuf[8])
      {
        case 0:
          Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} CLEAR RESET COMMAND PACKET: Packet Received and Processed Successfully");
          break;
        case 2:
          Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} CLEAR RESET COMMAND PACKET: CRC FAIL");
          num = (byte) 1;
          break;
        case 6:
          Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} CLEAR RESET COMMAND PACKET: IN VALID FUNCTION CODE");
          num = (byte) 1;
          break;
        case 35:
          Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} CLEAR RESET COMMAND PACKET: IN VALID DATA LENGTH");
          num = (byte) 1;
          break;
      }
      if (Checksum.Checksum_Calc(ref cgdb_api.rxbuf, Pkt_length) == 0)
      {
        Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr}LINK CHECK RESPONSE PACKET: CHECKSUM FAILED ");
        return 9;
      }
      if (num != (byte) 0)
      {
        Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} CLEAT RESET COMMAND IS UNSUCCESSFUL");
        return 0;
      }
      Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} CLEAR RESET COMMAND IS SUCCESSFUL");
      return 1;
    }
    Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} LINK FAILURE or ADDRESSED CGDB DOESn't EXIST");
    return 2;
  }

  public static byte cgdb_pre_cmd_status(
    byte pdch_addr,
    byte cgdb_addr,
    string platform_no,
    ref byte[] cgdb_pkt,
    ref short length)
  {
    cgdb_api.pkt_buf[0] = (byte) 170;
    cgdb_api.pkt_buf[1] = (byte) 204;
    cgdb_api.pkt_buf[2] = (byte) 0;
    cgdb_api.pkt_buf[3] = (byte) 10;
    cgdb_api.pkt_buf[4] = frmMainFormIPIS.ccu_addr;
    cgdb_api.pkt_buf[5] = (byte) 0;
    cgdb_api.pkt_buf[6] = pdch_addr;
    cgdb_api.pkt_buf[7] = cgdb_addr;
    cgdb_api.pkt_buf[8] = (byte) 0;
    cgdb_api.pkt_buf[9] = (byte) 136;
    ushort length1 = checked ((ushort) ((int) unchecked ((ushort) ((int) (ushort) (0U << 8) | 10)) + 2));
    length = checked ((short) length1);
    int index = 0;
    while (index < (int) length1)
    {
      cgdb_pkt[index] = cgdb_api.pkt_buf[index];
      checked { ++index; }
    }
    Checksum.prepare_checksum(ref cgdb_api.pkt_buf, length1);
    if (RS232.Serial_Write(ref cgdb_api.pkt_buf, (int) length1) == 1)
      return 1;
    Log_file.Log(string.Format("Write to COM port failed, may be COM port deos't exit. Please check the COM PORT settings"));
    return 3;
  }

  public static byte cgdb_pre_cmd_res_pkt(
    byte pdch_addr,
    byte cgdb_addr,
    string platform_no,
    ref byte pre_cmd_status,
    ref byte pre_cmd_serial_no,
    ref byte pre_cmd_fc,
    ref byte[] cgdb_pkt,
    ref short length)
  {
    if (RS232.Serial_Read(ref cgdb_api.rxbuf) == (byte) 1)
    {
      ushort Pkt_length = checked ((ushort) unchecked ((int) (ushort) ((uint) (ushort) cgdb_api.rxbuf[0] << 8) + (int) cgdb_api.rxbuf[1]));
      length = checked ((short) ((int) Pkt_length + 2));
      byte index = 0;
      cgdb_pkt[0] = (byte) 170;
      cgdb_pkt[1] = (byte) 204;
      while ((uint) index < (uint) Pkt_length)
      {
        cgdb_pkt[checked ((int) index + 2)] = cgdb_api.rxbuf[(int) index];
        checked { ++index; }
      }
      byte num = 0;
      if ((int) cgdb_api.rxbuf[2] != (int) pdch_addr & (int) cgdb_api.rxbuf[3] != (int) cgdb_addr)
      {
        Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} PREVIOUS COMMAND RESPONSE PACKET: IN CORRECT SOURCE ADDRESS");
        num = (byte) 1;
      }
      if (Pkt_length != (ushort) 14)
      {
        Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} PREVIOUS COMMAND RESPONSE PACKET: IN VALID DATA LENGTH");
        num = (byte) 1;
      }
      if (cgdb_api.rxbuf[7] != (byte) 200)
      {
        Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} PREVIOUS COMMAND RESPONSE PACKET: IN VALID FUNCTION CODE");
        num = (byte) 1;
      }
      switch (cgdb_api.rxbuf[8])
      {
        case 0:
          Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} PREVIOUS COMMAND PACKET: Packet Received and Processed Successfully");
          break;
        case 2:
          Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} PREVIOUS COMMAND PACKET: CRC FAIL");
          num = (byte) 1;
          break;
        case 6:
          Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} PREVIOUS COMMAND PACKET: IN VALID FUNCTION CODE");
          num = (byte) 1;
          break;
        case 35:
          Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} PREVIOUS COMMAND PACKET: IN VALID DATA LENGTH");
          num = (byte) 1;
          break;
      }
      pre_cmd_status = cgdb_api.rxbuf[9];
      pre_cmd_serial_no = cgdb_api.rxbuf[10];
      pre_cmd_fc = cgdb_api.rxbuf[11];
      if (Checksum.Checksum_Calc(ref cgdb_api.rxbuf, Pkt_length) == 0)
      {
        Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr}LINK CHECK RESPONSE PACKET: CHECKSUM FAILED ");
        return 9;
      }
      if (num != (byte) 0)
      {
        Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} PREVIOUS COMMAND IS UNSUCCESSFUL");
        return 0;
      }
      Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} PREVIOUS COMMAND IS SUCCESSFUL");
      return 1;
    }
    Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} LINK FAILURE or ADDRESSED CGDB DOESn't EXIST");
    return 2;
  }

  public static byte cgdb_diag_cmd(
    byte pdch_addr,
    byte cgdb_addr,
    string platform_no,
    ref byte[] cgdb_pkt,
    ref short length)
  {
    cgdb_api.pkt_buf[0] = (byte) 170;
    cgdb_api.pkt_buf[1] = (byte) 204;
    cgdb_api.pkt_buf[2] = (byte) 0;
    cgdb_api.pkt_buf[3] = (byte) 10;
    cgdb_api.pkt_buf[4] = frmMainFormIPIS.ccu_addr;
    cgdb_api.pkt_buf[5] = (byte) 0;
    cgdb_api.pkt_buf[6] = pdch_addr;
    cgdb_api.pkt_buf[7] = cgdb_addr;
    cgdb_api.pkt_buf[8] = (byte) 0;
    cgdb_api.pkt_buf[9] = (byte) 138;
    ushort length1 = checked ((ushort) ((int) unchecked ((ushort) ((int) (ushort) (0U << 8) | 10)) + 2));
    length = checked ((short) length1);
    int index = 0;
    while (index < (int) length1)
    {
      cgdb_pkt[index] = cgdb_api.pkt_buf[index];
      checked { ++index; }
    }
    Checksum.prepare_checksum(ref cgdb_api.pkt_buf, length1);
    if (RS232.Serial_Write(ref cgdb_api.pkt_buf, (int) length1) == 1)
      return 1;
    Log_file.Log(string.Format("Write to COM port failed, may be COM port deos't exit. Please check the COM PORT settings"));
    return 3;
  }

  public static byte cgdb_diag_cmd_res_pkt(
    byte pdch_addr,
    byte cgdb_addr,
    string platform_no,
    ref byte[] cgdb_pkt,
    ref short length,
    ref byte[] manu)
  {
    if (RS232.Serial_Read(ref cgdb_api.rxbuf) == (byte) 1)
    {
      ushort Pkt_length = checked ((ushort) unchecked ((int) (ushort) ((uint) (ushort) cgdb_api.rxbuf[0] << 8) + (int) cgdb_api.rxbuf[1]));
      length = checked ((short) ((int) Pkt_length + 2));
      byte index = 0;
      cgdb_pkt[0] = (byte) 170;
      cgdb_pkt[1] = (byte) 204;
      while ((uint) index < (uint) Pkt_length)
      {
        cgdb_pkt[checked ((int) index + 2)] = cgdb_api.rxbuf[(int) index];
        checked { ++index; }
      }
      byte num = 0;
      if ((int) cgdb_api.rxbuf[2] != (int) pdch_addr & (int) cgdb_api.rxbuf[3] != (int) cgdb_addr)
      {
        Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} DIAGNOSTIC COMMAND RESPONSE PACKET: IN CORRECT SOURCE ADDRESS");
        num = (byte) 1;
      }
      if (Pkt_length != (ushort) 16 /*0x10*/)
      {
        Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} DIAGNOSTIC COMMAND RESPONSE PACKET: IN VALID DATA LENGTH");
        num = (byte) 1;
      }
      if (cgdb_api.rxbuf[7] != (byte) 202)
      {
        Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} DIAGNOSTIC COMMAND RESPONSE PACKET: IN VALID FUNCTION CODE");
        num = (byte) 1;
      }
      switch (cgdb_api.rxbuf[8])
      {
        case 0:
          Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} DIAGNOSTIC COMMAND PACKET: Packet Received and Processed Successfully");
          break;
        case 2:
          Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} DIAGNOSTIC COMMAND PACKET: CRC FAIL");
          num = (byte) 1;
          break;
        case 6:
          Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} DIAGNOSTIC COMMAND PACKET: IN VALID FUNCTION CODE");
          num = (byte) 1;
          break;
        case 35:
          Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} DIAGNOSTIC COMMAND PACKET: IN VALID DATA LENGTH");
          num = (byte) 1;
          break;
      }
      manu[0] = cgdb_api.rxbuf[9];
      manu[1] = cgdb_api.rxbuf[10];
      manu[2] = cgdb_api.rxbuf[11];
      manu[3] = cgdb_api.rxbuf[12];
      manu[4] = cgdb_api.rxbuf[13];
      if (Checksum.Checksum_Calc(ref cgdb_api.rxbuf, Pkt_length) == 0)
      {
        Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr}LINK CHECK RESPONSE PACKET: CHECKSUM FAILED ");
        return 9;
      }
      if (num != (byte) 0)
      {
        Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} DIAGNOSTIC COMMAND IS UNSUCCESSFUL");
        return 0;
      }
      Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} DIAGNOSTIC COMMAND IS SUCCESSFUL");
      return 1;
    }
    Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} LINK FAILURE or ADDRESSED CGDB DOESn't EXIST");
    return 2;
  }

  public static byte cgdb_optional_cmd(
    byte pdch_addr,
    byte cgdb_addr,
    string platform_no,
    ref byte[] cgdb_pkt,
    ref short length)
  {
    cgdb_api.pkt_buf[0] = (byte) 170;
    cgdb_api.pkt_buf[1] = (byte) 204;
    cgdb_api.pkt_buf[2] = (byte) 0;
    cgdb_api.pkt_buf[3] = (byte) 10;
    cgdb_api.pkt_buf[4] = frmMainFormIPIS.ccu_addr;
    cgdb_api.pkt_buf[5] = (byte) 0;
    cgdb_api.pkt_buf[6] = pdch_addr;
    cgdb_api.pkt_buf[7] = cgdb_addr;
    cgdb_api.pkt_buf[8] = (byte) 0;
    cgdb_api.pkt_buf[9] = (byte) 139;
    ushort length1 = checked ((ushort) ((int) unchecked ((ushort) ((int) (ushort) (0U << 8) | 10)) + 2));
    length = checked ((short) length1);
    int index = 0;
    while (index < (int) length1)
    {
      cgdb_pkt[index] = cgdb_api.pkt_buf[index];
      checked { ++index; }
    }
    Checksum.prepare_checksum(ref cgdb_api.pkt_buf, length1);
    if (RS232.Serial_Write(ref cgdb_api.pkt_buf, (int) length1) == 1)
      return 1;
    Log_file.Log(string.Format("Write to COM port failed, may be COM port deos't exit. Please check the COM PORT settings"));
    return 3;
  }

  public static byte cgdb_opt_cmd_res_pkt(
    byte pdch_addr,
    byte cgdb_addr,
    string platform_no,
    ref byte[] cgdb_pkt,
    ref short length,
    ref byte[] manu)
  {
    if (RS232.Serial_Read(ref cgdb_api.rxbuf) == (byte) 1)
    {
      ushort Pkt_length = checked ((ushort) unchecked ((int) (ushort) ((uint) (ushort) cgdb_api.rxbuf[0] << 8) + (int) cgdb_api.rxbuf[1]));
      length = checked ((short) ((int) Pkt_length + 2));
      byte index = 0;
      cgdb_pkt[0] = (byte) 170;
      cgdb_pkt[1] = (byte) 204;
      while ((uint) index < (uint) Pkt_length)
      {
        cgdb_pkt[checked ((int) index + 2)] = cgdb_api.rxbuf[(int) index];
        checked { ++index; }
      }
      byte num = 0;
      if ((int) cgdb_api.rxbuf[2] != (int) pdch_addr & (int) cgdb_api.rxbuf[3] != (int) cgdb_addr)
      {
        Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} OPTIONAL COMMAND RESPONSE PACKET: IN CORRECT SOURCE ADDRESS");
        num = (byte) 1;
      }
      if (Pkt_length != (ushort) 16 /*0x10*/)
      {
        Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} OPTIONAL COMMAND RESPONSE PACKET: IN VALID DATA LENGTH");
        num = (byte) 1;
      }
      if (cgdb_api.rxbuf[7] != (byte) 203)
      {
        Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} OPTIONAL COMMAND RESPONSE PACKET: IN VALID FUNCTION CODE");
        num = (byte) 1;
      }
      switch (cgdb_api.rxbuf[8])
      {
        case 0:
          Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} OPTIONAL COMMAND PACKET: Packet Received and Processed Successfully");
          break;
        case 2:
          Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} OPTIONAL COMMAND PACKET: CRC FAIL");
          num = (byte) 1;
          break;
        case 6:
          Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} OPTIONAL COMMAND PACKET: IN VALID FUNCTION CODE");
          num = (byte) 1;
          break;
        case 35:
          Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} OPTIONAL COMMAND PACKET: IN VALID DATA LENGTH");
          num = (byte) 1;
          break;
      }
      manu[0] = cgdb_api.rxbuf[9];
      manu[1] = cgdb_api.rxbuf[10];
      manu[2] = cgdb_api.rxbuf[11];
      manu[3] = cgdb_api.rxbuf[12];
      manu[4] = cgdb_api.rxbuf[13];
      if (Checksum.Checksum_Calc(ref cgdb_api.rxbuf, Pkt_length) == 0)
      {
        Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr}LINK CHECK RESPONSE PACKET: CHECKSUM FAILED ");
        return 9;
      }
      if (num != (byte) 0)
      {
        Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} OPTIONAL COMMAND IS UNSUCCESSFUL");
        return 0;
      }
      Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} OPTIONAL COMMAND IS SUCCESSFUL");
      return 1;
    }
    Log_file.Log("PLATFORM NO:{platform_no} CGDB Address:{cgdb_addr} LINK FAILURE or ADDRESSED CGDB DOESn't EXIST");
    return 2;
  }

  public static byte cgdb_send_test_pkt(
    byte cgdb_addr,
    byte pdch_addr,
    string platform_no,
    ref byte[] cgdb_pkt,
    ref short length)
  {
    cgdb_api.pkt_buf[0] = (byte) 170;
    cgdb_api.pkt_buf[1] = (byte) 204;
    cgdb_api.pkt_buf[2] = (byte) 0;
    cgdb_api.pkt_buf[3] = (byte) 14;
    cgdb_api.pkt_buf[4] = frmMainFormIPIS.ccu_addr;
    cgdb_api.pkt_buf[5] = (byte) 0;
    cgdb_api.pkt_buf[6] = pdch_addr;
    cgdb_api.pkt_buf[7] = cgdb_addr;
    cgdb_api.pkt_buf[8] = (byte) 0;
    cgdb_api.pkt_buf[9] = (byte) 129;
    cgdb_api.pkt_buf[10] = (byte) 3;
    cgdb_api.pkt_buf[11] = (byte) 0;
    cgdb_api.pkt_buf[12] = (byte) 0;
    cgdb_api.pkt_buf[13] = (byte) 0;
    ushort length1 = checked ((ushort) (14 + 2));
    Checksum.prepare_checksum(ref cgdb_api.pkt_buf, length1);
    length = checked ((short) length1);
    int index = 0;
    while (index < (int) length1)
    {
      cgdb_pkt[index] = cgdb_api.pkt_buf[index];
      checked { ++index; }
    }
    byte num = 0;
    if (RS232.Serial_Write(ref cgdb_api.pkt_buf, (int) length1) != 1)
    {
      Log_file.Log(string.Format("Write to COM port failed, may be COM port deos't exit. Please check the COM PORT settings"));
      num = (byte) 3;
    }
    else
      Thread.Sleep(100);
    return num;
  }

  public static byte cgdb_send_test_pkt_res(
    byte cgdb_addr,
    byte pdch_addr,
    string Platform_no,
    ref byte[] cgdb_pkt,
    ref short length)
  {
    if (RS232.Serial_Read(ref cgdb_api.rxbuf) == (byte) 1)
    {
      ushort Pkt_length = checked ((ushort) unchecked ((int) (ushort) ((uint) (ushort) cgdb_api.rxbuf[0] << 8) + (int) cgdb_api.rxbuf[1]));
      length = checked ((short) ((int) Pkt_length + 2));
      byte index = 0;
      cgdb_pkt[0] = (byte) 170;
      cgdb_pkt[1] = (byte) 204;
      while ((uint) index < (uint) Pkt_length)
      {
        cgdb_pkt[checked ((int) index + 2)] = cgdb_api.rxbuf[(int) index];
        checked { ++index; }
      }
      byte num = 0;
      if ((int) cgdb_api.rxbuf[2] != (int) pdch_addr & (int) cgdb_api.rxbuf[3] != (int) cgdb_addr)
      {
        Log_file.Log("PLATFORM NO:{Platform_no} CGDB Address:{cgdb_addr} OPTIONAL COMMAND RESPONSE PACKET: IN CORRECT SOURCE ADDRESS");
        num = (byte) 1;
      }
      if (Pkt_length != (ushort) 11)
      {
        Log_file.Log("PLATFORM NO:{Platform_no} CGDB Address:{cgdb_addr} OPTIONAL COMMAND RESPONSE PACKET: IN VALID DATA LENGTH");
        num = (byte) 1;
      }
      if (cgdb_api.rxbuf[7] != (byte) 193)
      {
        Log_file.Log("PLATFORM NO:{Platform_no} CGDB Address:{cgdb_addr} TEST COMMAND RESPONSE PACKET: IN VALID FUNCTION CODE");
        num = (byte) 1;
      }
      switch (cgdb_api.rxbuf[8])
      {
        case 0:
          Log_file.Log("PLATFORM NO:{Platform_no} CGDB Address:{cgdb_addr} TEST COMMAND PACKET: Packet Received and Processed Successfully");
          break;
        case 2:
          Log_file.Log("PLATFORM NO:{Platform_no} CGDB Address:{cgdb_addr} TEST COMMAND PACKET: CRC FAIL");
          num = (byte) 1;
          break;
        case 6:
          Log_file.Log("PLATFORM NO:{Platform_no} CGDB Address:{cgdb_addr} TEST COMMAND PACKET: IN VALID FUNCTION CODE");
          num = (byte) 1;
          break;
        case 35:
          Log_file.Log("PLATFORM NO:{Platform_no} CGDB Address:{cgdb_addr} TEST COMMAND PACKET: IN VALID DATA LENGTH");
          num = (byte) 1;
          break;
      }
      if (Checksum.Checksum_Calc(ref cgdb_api.rxbuf, Pkt_length) == 0)
      {
        Log_file.Log("PLATFORM NO:{Platform_no} CGDB Address:{cgdb_addr}TEST COMMAND RESPONSE PACKET: CHECKSUM FAILED ");
        return 9;
      }
      if (num != (byte) 0)
      {
        Log_file.Log("PLATFORM NO:{Platform_no} CGDB Address:{cgdb_addr} TEST COMMAND IS UNSUCCESSFUL");
        return 0;
      }
      Log_file.Log("PLATFORM NO:{Platform_no} CGDB Address:{cgdb_addr} TEST COMMAND IS SUCCESSFUL");
      return 1;
    }
    Log_file.Log("PLATFORM NO:{Platform_no} CGDB Address:{cgdb_addr} LINK FAILURE or ADDRESSED CGDB DOESn't EXIST");
    return 2;
  }
}

}