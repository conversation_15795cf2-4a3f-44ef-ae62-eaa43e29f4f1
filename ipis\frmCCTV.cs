// Decompiled with JetBrains decompiler
// Type: ipis.frmCCTV
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

namespace ipis
{

[DesignerGenerated]
public class frmCCTV : Form
{
  private static List<WeakReference> __ENCList = new List<WeakReference>();
  private IContainer components;

  [DebuggerNonUserCode]
  static frmCCTV()
  {
  }

  [DebuggerNonUserCode]
  public frmCCTV()
  {
    this.Load += new EventHandler(this.frmCCTV_Load);
    frmCCTV.__ENCAddToList((object) this);
    this.InitializeComponent();
  }

  [DebuggerNonUserCode]
  private static void __ENCAddToList(object value)
  {
    lock (frmCCTV.__ENCList)
    {
      if (frmCCTV.__ENCList.Count == frmCCTV.__ENCList.Capacity)
      {
        int index1 = 0;
        int num = checked (frmCCTV.__ENCList.Count - 1);
        int index2 = 0;
        while (index2 <= num)
        {
          if (frmCCTV.__ENCList[index2].IsAlive)
          {
            if (index2 != index1)
              frmCCTV.__ENCList[index1] = frmCCTV.__ENCList[index2];
            checked { ++index1; }
          }
          checked { ++index2; }
        }
        frmCCTV.__ENCList.RemoveRange(index1, checked (frmCCTV.__ENCList.Count - index1));
        frmCCTV.__ENCList.Capacity = frmCCTV.__ENCList.Count;
      }
      frmCCTV.__ENCList.Add(new WeakReference(RuntimeHelpers.GetObjectValue(value)));
    }
  }

  [DebuggerNonUserCode]
  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  [DebuggerStepThrough]
  private void InitializeComponent()
  {
    this.SuspendLayout();
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.ClientSize = new Size(643, 390);
    this.ControlBox = false;
    this.MaximizeBox = false;
    this.MinimizeBox = false;
    this.Name = "frmCCTV";
    this.ShowIcon = false;
    this.ResumeLayout(false);
  }

  private void frmCCTV_Load(object sender, EventArgs e)
  {
  }
}

}