using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ipis_V2_jules.Data; // For DatabaseHelper
using ipis_V2_jules.ApiClients; // For TrainDataErail
using ipis_V2_jules.Hardware.Clients; // For IBoardClient
using ipis_V2_jules.DisplayFormatters; // For IDisplayDataFormatter and specific formatters
// using ipis_V2_jules.Models; // For DisplayBoardConfig (actual model)

namespace ipis_V2_jules.Services
{
    public class DisplayBoardService
    {
        private readonly DatabaseHelper _dbHelper;
        private readonly Dictionary<string, IBoardClient> _boardClients; // Key: BoardName or BoardType
        private readonly Dictionary<string, IDisplayDataFormatter> _dataFormatters; // Key: BoardType or FormatterName

        // A more robust approach might use a factory pattern or dependency injection framework
        // to provide clients and formatters based on board configuration.
        public DisplayBoardService(
            DatabaseHelper dbHelper,
            Dictionary<string, IBoardClient> boardClients,
            Dictionary<string, IDisplayDataFormatter> dataFormatters)
        {
            _dbHelper = dbHelper ?? throw new ArgumentNullException(nameof(dbHelper));
            _boardClients = boardClients ?? throw new ArgumentNullException(nameof(boardClients));
            _dataFormatters = dataFormatters ?? throw new ArgumentNullException(nameof(dataFormatters));
        }

        /// <summary>
        /// Updates the display for all boards associated with a given platform number
        /// with the provided train information.
        /// </summary>
        public async Task UpdateTrainDisplayOnPlatformAsync(TrainDataErail trainInfo, string platformNo, Dictionary<string, string> platformSpecificInfo)
        {
            if (trainInfo == null) throw new ArgumentNullException(nameof(trainInfo));
            if (string.IsNullOrWhiteSpace(platformNo)) throw new ArgumentNullException(nameof(platformNo));

            Console.WriteLine($"DisplayBoardService: Updating displays for platform {platformNo} with train {trainInfo.TrainNo} - {trainInfo.TrainName}");

            // 1. Query DisplayBoardConfig for boards on platformNo.
            //    This requires DisplayBoardConfig table to have a PlatformNumber or link to Platforms table which has PlatformNumber.
            //    SELECT * FROM DisplayBoardConfig WHERE PlatformID IN (SELECT PlatformID FROM Platforms WHERE PlatformNumber = @platformNo)
            //    For now, using a placeholder list of board configurations.

            // Placeholder: Replace with actual DB query to get board configurations for the platform.
            // List<DisplayBoardConfigModel> platformBoards = GetBoardConfigsForPlatform(platformNo);
            var platformBoards = new List<Dictionary<string, string>> // Placeholder for actual board config model
            {
                // Example board config (replace with actual data from DB)
                new Dictionary<string, string> { {"BoardName", "PF1_AGDB1"}, {"BoardType", "AGDB"}, {"BoardAddress", "1"}, {"SubAddress", "1"}, {"PlatformNumber", platformNo}, {"CharsPerLine", "24"} },
                new Dictionary<string, string> { {"BoardName", "PF1_CGDB1"}, {"BoardType", "CGDB"}, {"BoardAddress", "2"}, {"SubAddress", "1"}, {"PlatformNumber", platformNo}, {"DisplayLengthChars", "40"} }
            };

            if (!platformBoards.Any())
            {
                Console.WriteLine($"DisplayBoardService: No boards found configured for platform {platformNo}.");
                return;
            }

            foreach (var boardConfig in platformBoards)
            {
                string boardName = boardConfig["BoardName"];
                string boardType = boardConfig["BoardType"]; // e.g., "AGDB", "CGDB", "MLDB", "PDB" (or "TADDB")

                // 2. Get the correct IBoardClient and IDisplayDataFormatter based on boardType or boardName.
                if (!_boardClients.TryGetValue(boardType, out IBoardClient? client) || client == null)
                {
                    Console.WriteLine($"DisplayBoardService: No client found for board type '{boardType}' (Board: {boardName}). Skipping.");
                    continue;
                }
                if (!_dataFormatters.TryGetValue(boardType, out IDisplayDataFormatter? formatter) || formatter == null)
                {
                    Console.WriteLine($"DisplayBoardService: No formatter found for board type '{boardType}' (Board: {boardName}). Skipping.");
                    continue;
                }

                try
                {
                    // 3. Call formatter, then client's SendMessageAsync.
                    // The 'platformSpecificInfo' dictionary should contain details like ETA, ETD for *this* train at *this* station.
                    FormattedDisplayData displayData = formatter.FormatTrainData(trainInfo, platformSpecificInfo, boardConfig);

                    byte deviceAddress = byte.Parse(boardConfig["BoardAddress"]);
                    byte subAddress = byte.Parse(boardConfig["SubAddress"]);
                    byte serialNo = GetNextSerialNo(); // Implement a way to get unique serial numbers

                    Console.WriteLine($"DisplayBoardService: Sending to board {boardName} (Type: {boardType}, Address: {deviceAddress}/{subAddress})");
                    bool success = await client.SendMessageAsync(displayData, deviceAddress, subAddress, serialNo);
                    if (success)
                    {
                        Console.WriteLine($"DisplayBoardService: Successfully updated board {boardName}.");
                    }
                    else
                    {
                        Console.WriteLine($"DisplayBoardService: Failed to update board {boardName}.");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"DisplayBoardService: Error processing board {boardName}: {ex.Message}");
                    // TODO: Log exception
                }
            }
        }

        /// <summary>
        /// Sends a generic message to a specific display board.
        /// </summary>
        public async Task SendMessageToBoardAsync(string message, string boardName)
        {
            if (string.IsNullOrWhiteSpace(message)) throw new ArgumentNullException(nameof(message));
            if (string.IsNullOrWhiteSpace(boardName)) throw new ArgumentNullException(nameof(boardName));

            Console.WriteLine($"DisplayBoardService: Sending message \"{message.Substring(0, Math.Min(message.Length,30))}...\" to board {boardName}");

            // 1. Query DisplayBoardConfig for the specific boardName.
            // Placeholder: Replace with actual DB query.
            // DisplayBoardConfigModel boardConfig = GetBoardConfigByName(boardName);
            var boardConfig = new Dictionary<string, string> // Placeholder for actual board config model
            {
                // Example board config (replace with actual data from DB)
                 {"BoardName", boardName}, {"BoardType", "AGDB"}, {"BoardAddress", "5"}, {"SubAddress", "1"}, {"CharsPerLine", "24"}
            };

            if (boardConfig == null || !boardConfig.ContainsKey("BoardType"))
            {
                Console.WriteLine($"DisplayBoardService: Board configuration not found for board name '{boardName}'.");
                return;
            }

            string boardType = boardConfig["BoardType"];

            // 2. Get client and formatter.
            if (!_boardClients.TryGetValue(boardType, out IBoardClient? client) || client == null)
            {
                Console.WriteLine($"DisplayBoardService: No client found for board type '{boardType}' (Board: {boardName}). Skipping.");
                return;
            }
            if (!_dataFormatters.TryGetValue(boardType, out IDisplayDataFormatter? formatter) || formatter == null)
            {
                Console.WriteLine($"DisplayBoardService: No formatter found for board type '{boardType}' (Board: {boardName}). Skipping.");
                return;
            }

            try
            {
                // 3. Format and send.
                FormattedDisplayData displayData = formatter.FormatMessage(message, boardConfig);

                byte deviceAddress = byte.Parse(boardConfig["BoardAddress"]);
                byte subAddress = byte.Parse(boardConfig["SubAddress"]);
                byte serialNo = GetNextSerialNo();

                Console.WriteLine($"DisplayBoardService: Sending message to board {boardName} (Type: {boardType}, Address: {deviceAddress}/{subAddress})");
                bool success = await client.SendMessageAsync(displayData, deviceAddress, subAddress, serialNo);
                if (success)
                {
                    Console.WriteLine($"DisplayBoardService: Successfully sent message to board {boardName}.");
                }
                else
                {
                    Console.WriteLine($"DisplayBoardService: Failed to send message to board {boardName}.");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"DisplayBoardService: Error processing message for board {boardName}: {ex.Message}");
                // TODO: Log exception
            }
        }

        private byte GetNextSerialNo()
        {
            // Placeholder for a proper serial number generation/tracking mechanism.
            // This should ideally be unique per transaction or follow protocol requirements.
            // For now, returning a pseudo-random byte.
            return (byte)(DateTime.UtcNow.Ticks % 256);
        }

        // Placeholder methods for fetching board configurations from DB
        // private List<DisplayBoardConfigModel> GetBoardConfigsForPlatform(string platformNo) { /* DB Query Logic */ return new List<DisplayBoardConfigModel>(); }
        // private DisplayBoardConfigModel GetBoardConfigByName(string boardName) { /* DB Query Logic */ return null; }
    }
}
