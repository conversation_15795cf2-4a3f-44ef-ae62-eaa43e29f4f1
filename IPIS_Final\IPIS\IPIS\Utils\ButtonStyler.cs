using System.Drawing;
using System.Windows.Forms;

namespace IPIS.Utils
{
    public static class ButtonStyler
    {
        // Standard button sizes
        public static class Sizes
        {
            public static readonly Size Small = new Size(80, 32);
            public static readonly Size Medium = new Size(100, 36);
            public static readonly Size Large = new Size(120, 40);
            public static readonly Size ExtraLarge = new Size(140, 44);
        }

        // Standard fonts
        public static class Fonts
        {
            public static readonly Font Small = new Font("Segoe UI", 9, FontStyle.Regular);
            public static readonly Font Medium = new Font("Segoe UI", 10, FontStyle.Regular);
            public static readonly Font Large = new Font("Segoe UI", 11, FontStyle.Regular);
            public static readonly Font Bold = new Font("Segoe UI", 10, FontStyle.Bold);
        }

        // Standard colors
        public static class Colors
        {
            public static readonly Color Primary = Color.FromArgb(0, 123, 255);
            public static readonly Color PrimaryHover = Color.FromArgb(0, 105, 217);
            public static readonly Color Success = Color.FromArgb(40, 167, 69);
            public static readonly Color SuccessHover = Color.FromArgb(30, 126, 52);
            public static readonly Color Danger = Color.FromArgb(220, 53, 69);
            public static readonly Color DangerHover = Color.FromArgb(200, 35, 51);
            public static readonly Color Warning = Color.FromArgb(255, 193, 7);
            public static readonly Color WarningHover = Color.FromArgb(224, 168, 0);
            public static readonly Color Info = Color.FromArgb(23, 162, 184);
            public static readonly Color InfoHover = Color.FromArgb(17, 122, 139);
            public static readonly Color Secondary = Color.FromArgb(108, 117, 125);
            public static readonly Color SecondaryHover = Color.FromArgb(90, 98, 104);
            public static readonly Color Light = Color.FromArgb(248, 249, 250);
            public static readonly Color LightHover = Color.FromArgb(233, 236, 239);
            public static readonly Color Dark = Color.FromArgb(52, 58, 64);
            public static readonly Color DarkHover = Color.FromArgb(35, 39, 43);
        }

        public static void ApplyStandardStyle(Button btn, string type = "primary", string size = "medium", int? width = null, int? height = null, Padding? padding = null)
        {
            // Set basic properties
            btn.FlatStyle = FlatStyle.Flat;
            btn.FlatAppearance.BorderSize = 0;
            btn.Cursor = Cursors.Hand;
            btn.UseVisualStyleBackColor = false;
            
            // Set size
            switch (size.ToLower())
            {
                case "small":
                    btn.Size = Sizes.Small;
                    btn.Font = Fonts.Small;
                    btn.Padding = new Padding(8, 4, 8, 4);
                    break;
                case "large":
                    btn.Size = Sizes.Large;
                    btn.Font = Fonts.Large;
                    btn.Padding = new Padding(12, 8, 12, 8);
                    break;
                case "extralarge":
                    btn.Size = Sizes.ExtraLarge;
                    btn.Font = Fonts.Large;
                    btn.Padding = new Padding(16, 10, 16, 10);
                    break;
                default: // medium
                    btn.Size = Sizes.Medium;
                    btn.Font = Fonts.Medium;
                    btn.Padding = new Padding(10, 0, 10, 0);
                    break;
            }

            btn.Margin = new Padding(4);

            // Set colors based on type
            switch (type.ToLower())
            {
                case "primary":
                    btn.BackColor = Colors.Primary;
                    btn.ForeColor = Color.White;
                    btn.FlatAppearance.MouseOverBackColor = Colors.PrimaryHover;
                    break;
                case "success":
                    btn.BackColor = Colors.Success;
                    btn.ForeColor = Color.White;
                    btn.FlatAppearance.MouseOverBackColor = Colors.SuccessHover;
                    break;
                case "danger":
                    btn.BackColor = Colors.Danger;
                    btn.ForeColor = Color.White;
                    btn.FlatAppearance.MouseOverBackColor = Colors.DangerHover;
                    break;
                case "warning":
                    btn.BackColor = Colors.Warning;
                    btn.ForeColor = Color.Black;
                    btn.FlatAppearance.MouseOverBackColor = Colors.WarningHover;
                    break;
                case "info":
                    btn.BackColor = Colors.Info;
                    btn.ForeColor = Color.White;
                    btn.FlatAppearance.MouseOverBackColor = Colors.InfoHover;
                    break;
                case "secondary":
                    btn.BackColor = Colors.Secondary;
                    btn.ForeColor = Color.White;
                    btn.FlatAppearance.MouseOverBackColor = Colors.SecondaryHover;
                    break;
                case "light":
                    btn.BackColor = Colors.Light;
                    btn.ForeColor = Color.Black;
                    btn.FlatAppearance.MouseOverBackColor = Colors.LightHover;
                    break;
                case "dark":
                    btn.BackColor = Colors.Dark;
                    btn.ForeColor = Color.White;
                    btn.FlatAppearance.MouseOverBackColor = Colors.DarkHover;
                    break;
                case "outline-primary":
                    btn.BackColor = Color.Transparent;
                    btn.ForeColor = Colors.Primary;
                    btn.FlatAppearance.BorderColor = Colors.Primary;
                    btn.FlatAppearance.BorderSize = 1;
                    btn.FlatAppearance.MouseOverBackColor = Colors.Primary;
                    // btn.FlatAppearance.MouseOverForeColor = Color.White; // Not supported in WinForms
                    break;
                case "outline-success":
                    btn.BackColor = Color.Transparent;
                    btn.ForeColor = Colors.Success;
                    btn.FlatAppearance.BorderColor = Colors.Success;
                    btn.FlatAppearance.BorderSize = 1;
                    btn.FlatAppearance.MouseOverBackColor = Colors.Success;
                    // btn.FlatAppearance.MouseOverForeColor = Color.White; // Not supported in WinForms
                    break;
                case "outline-danger":
                    btn.BackColor = Color.Transparent;
                    btn.ForeColor = Colors.Danger;
                    btn.FlatAppearance.BorderColor = Colors.Danger;
                    btn.FlatAppearance.BorderSize = 1;
                    btn.FlatAppearance.MouseOverBackColor = Colors.Danger;
                    // btn.FlatAppearance.MouseOverForeColor = Color.White; // Not supported in WinForms
                    break;
                default:
                    btn.BackColor = Colors.Primary;
                    btn.ForeColor = Color.White;
                    btn.FlatAppearance.MouseOverBackColor = Colors.PrimaryHover;
                    break;
            }

            if (width != null) btn.Width = width.Value;
            if (height != null) btn.Height = height.Value;
            if (padding != null) btn.Padding = padding.Value;
        }

        public static void ApplyIconStyle(Button btn, string type = "primary", string size = "medium")
        {
            ApplyStandardStyle(btn, type, size);
            btn.TextAlign = ContentAlignment.MiddleCenter;
            btn.ImageAlign = ContentAlignment.MiddleCenter;
        }

        public static void ApplyLinkStyle(Button btn, string type = "primary")
        {
            btn.FlatStyle = FlatStyle.Flat;
            btn.FlatAppearance.BorderSize = 0;
            btn.BackColor = Color.Transparent;
            btn.Cursor = Cursors.Hand;
            btn.Font = Fonts.Medium;
            btn.Padding = new Padding(4);
            btn.Margin = new Padding(2);
            btn.AutoSize = true;

            switch (type.ToLower())
            {
                case "primary":
                    btn.ForeColor = Colors.Primary;
                    btn.FlatAppearance.MouseOverBackColor = Color.FromArgb(240, 248, 255);
                    break;
                case "success":
                    btn.ForeColor = Colors.Success;
                    btn.FlatAppearance.MouseOverBackColor = Color.FromArgb(240, 255, 244);
                    break;
                case "danger":
                    btn.ForeColor = Colors.Danger;
                    btn.FlatAppearance.MouseOverBackColor = Color.FromArgb(255, 240, 240);
                    break;
                default:
                    btn.ForeColor = Colors.Primary;
                    btn.FlatAppearance.MouseOverBackColor = Color.FromArgb(240, 248, 255);
                    break;
            }
        }
    }
} 