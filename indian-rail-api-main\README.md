## Indian-Rail-Api

A simple rest api for Indian Railway data for personal projects which allowe to GetTrainInformation,GetRoute,GetTrainOnDate,GetTrainBtwStations

[![API Documentation - Read here](https://img.shields.io/static/v1?label=API+Documentation&message=Read+here&color=2ea44f&style=for-the-badge&logo=postman&logoColor=orange)](https://documenter.getpostman.com/view/23795265/2s8YYHMiHF)

## Technologies Used

1. Nodejs
2. `NPM Package`: express
3. `NPM Package`: user-agents

<!-- GETTING STARTED -->

## <img align="center" src="https://cdn.iconscout.com/icon/free/png-512/laptop-user-1-1179329.png" width="32" height="32"> Getting Started

To get a local copy up and running follow these simple steps.

### Prerequisites

In order to get a copy of the project you will require you to have Node.js (v14+) and the NPM package manager installed. If you don't have it, you can download the latest version of Node.js from the [official website](https://nodejs.org/en/download/) which also installs the NPM package manager by default.

### Installation

Open the terminal in the folder in which you wish to clone the repository and enter the following command:

```
git clone https://github.com/AniCrad/indian-rail-api.git .
```

Install all the NPM packages:

```
npm i
```

In order to run the server:

```
npm run start
```

<!-- CONTRIBUTING -->

## <img align="center" src="https://hpe-developer-portal.s3.amazonaws.com/uploads/media/2020/3/git-icon-1788c-1590702885345.png" width=32 height=32> Contributing

Contributions are what make the open source community such an amazing place to be learn, inspire, and create. Any contributions you make are **greatly appreciated**.

1. Fork the Project
2. Create your Feature Branch (`git checkout -b feature/YourAmazingFeature`)
3. Add Your Changes (`git add .`)
4. Commit your Changes (`npm run commit`)
5. Push to the Branch (`git push origin feature/YourAmazingFeature`)
6. Open a Pull Request

Note : only use `npm run commit` while commiting and select your tag whatever you done and then push it.

<!-- CONTRIBUTERS -->

## ⚫Contributors

• [Dojeto](https://github.com/Dojeto)
