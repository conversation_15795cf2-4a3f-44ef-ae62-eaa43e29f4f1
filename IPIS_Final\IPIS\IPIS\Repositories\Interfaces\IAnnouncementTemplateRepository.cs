using System.Collections.Generic;
using System.Threading.Tasks;
using IPIS.Models;

namespace IPIS.Repositories.Interfaces
{
    public interface IAnnouncementTemplateRepository
    {
        Task<IEnumerable<AnnouncementTemplate>> GetAllTemplatesAsync();
        Task<IEnumerable<AnnouncementTemplate>> GetActiveTemplatesAsync();
        Task<IEnumerable<AnnouncementTemplate>> GetActiveTemplatesByArrivalDepartureAsync(string arrivalDeparture);
        Task<AnnouncementTemplate> GetTemplateByIdAsync(int id);
        Task<AnnouncementTemplate> GetTemplateByNameAsync(string name);
        Task<int> AddTemplateAsync(AnnouncementTemplate template);
        Task<bool> UpdateTemplateAsync(AnnouncementTemplate template);
        Task<bool> DeleteTemplateAsync(int id);
        Task<bool> ToggleTemplateStatusAsync(int id);
    }
} 