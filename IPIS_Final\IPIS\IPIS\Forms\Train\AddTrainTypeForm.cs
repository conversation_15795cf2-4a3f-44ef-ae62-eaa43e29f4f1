using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using IPIS.Services;
using IPIS.Repositories;
using IPIS.Utils;
using IPIS.Models;

namespace IPIS.Forms.Train
{
    public partial class AddTrainTypeForm : Form
    {
        private TrainTypeService trainTypeService;
        private readonly ToastNotification toast;
        private bool isEditMode = false;
        private string currentTrainTypeId = null;

        // Controls
        private TextBox txtTrainTypeName;
        private TextBox txtDescription;
        private CheckBox chkIsActive;
        private ComboBox cmbExistingTrainTypes;
        private DataGridView dgvTrainTypes;
        private Button btnSave;
        private Button btnUpdate;
        private Button btnDelete;
        private Button btnClear;

        public AddTrainTypeForm()
        {
            InitializeComponent();
            trainTypeService = new TrainTypeService(new SQLiteTrainTypeRepository());
            toast = new ToastNotification(this);
            LoadTrainTypes();
        }

        private void InitializeComponent()
        {
            this.Text = "Manage Train Types";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;

            // Main layout
            TableLayoutPanel mainLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 3,
                Padding = new Padding(10)
            };

            // Set column styles
            mainLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 40F));
            mainLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 60F));

            // Set row styles
            mainLayout.RowStyles.Add(new RowStyle(SizeType.AutoSize));
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            mainLayout.RowStyles.Add(new RowStyle(SizeType.AutoSize));

            // Input Panel
            Panel inputPanel = CreateInputPanel();
            mainLayout.Controls.Add(inputPanel, 0, 0);
            mainLayout.SetRowSpan(inputPanel, 2);

            // DataGridView for existing train types
            dgvTrainTypes = new DataGridView
            {
                Dock = DockStyle.Fill,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                ReadOnly = true,
                AllowUserToAddRows = false
            };
            dgvTrainTypes.CellClick += DgvTrainTypes_CellClick;

            mainLayout.Controls.Add(dgvTrainTypes, 1, 0);
            mainLayout.SetRowSpan(dgvTrainTypes, 2);

            // Button Panel
            Panel buttonPanel = CreateButtonPanel();
            mainLayout.Controls.Add(buttonPanel, 0, 2);
            mainLayout.SetColumnSpan(buttonPanel, 2);

            this.Controls.Add(mainLayout);
        }

        private Panel CreateInputPanel()
        {
            Panel panel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(5)
            };

            TableLayoutPanel layout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 5,
                AutoSize = true
            };

            // Set column styles
            layout.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize));
            layout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));

            // Train Type Name
            layout.Controls.Add(new Label { Text = "Train Type Name:", AutoSize = true, TextAlign = ContentAlignment.MiddleLeft }, 0, 0);
            txtTrainTypeName = new TextBox { Width = 200, MaxLength = 50 };
            layout.Controls.Add(txtTrainTypeName, 1, 0);

            // Description
            layout.Controls.Add(new Label { Text = "Description:", AutoSize = true, TextAlign = ContentAlignment.MiddleLeft }, 0, 1);
            txtDescription = new TextBox { Width = 200, MaxLength = 200, Multiline = true, Height = 60 };
            layout.Controls.Add(txtDescription, 1, 1);

            // Is Active
            layout.Controls.Add(new Label { Text = "Active:", AutoSize = true, TextAlign = ContentAlignment.MiddleLeft }, 0, 2);
            chkIsActive = new CheckBox { Checked = true };
            layout.Controls.Add(chkIsActive, 1, 2);

            // Existing Train Types (for editing)
            layout.Controls.Add(new Label { Text = "Select to Edit:", AutoSize = true, TextAlign = ContentAlignment.MiddleLeft }, 0, 3);
            cmbExistingTrainTypes = new ComboBox
            {
                Width = 200,
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbExistingTrainTypes.SelectedIndexChanged += CmbExistingTrainTypes_SelectedIndexChanged;
            layout.Controls.Add(cmbExistingTrainTypes, 1, 3);

            panel.Controls.Add(layout);
            return panel;
        }

        private Panel CreateButtonPanel()
        {
            Panel panel = new Panel
            {
                Height = 50,
                Dock = DockStyle.Fill
            };

            FlowLayoutPanel buttonFlow = new FlowLayoutPanel
            {
                Dock = DockStyle.Fill,
                FlowDirection = FlowDirection.LeftToRight,
                AutoSize = true
            };

            btnSave = new Button { Text = "Add", Width = 80, Height = 30 };
            btnSave.Click += BtnSave_Click;

            btnUpdate = new Button { Text = "Update", Width = 80, Height = 30, Enabled = false };
            btnUpdate.Click += BtnUpdate_Click;

            btnDelete = new Button { Text = "Delete", Width = 80, Height = 30, Enabled = false };
            btnDelete.Click += BtnDelete_Click;

            btnClear = new Button { Text = "Clear", Width = 80, Height = 30 };
            btnClear.Click += BtnClear_Click;

            buttonFlow.Controls.AddRange(new Control[] { btnSave, btnUpdate, btnDelete, btnClear });
            panel.Controls.Add(buttonFlow);

            return panel;
        }

        private void LoadTrainTypes()
        {
            try
            {
                var trainTypes = trainTypeService.GetAllTrainTypes();

                // Setup DataGridView columns
                if (dgvTrainTypes.Columns.Count == 0)
                {
                    dgvTrainTypes.Columns.Add("ID", "ID");
                    dgvTrainTypes.Columns.Add("Name", "Train Type Name");
                    dgvTrainTypes.Columns.Add("Description", "Description");
                    dgvTrainTypes.Columns.Add("IsActive", "Active");
                    dgvTrainTypes.Columns.Add("CreatedDate", "Created Date");

                    dgvTrainTypes.Columns["ID"].Visible = false; // Hide ID column
                }

                // Clear existing data
                dgvTrainTypes.Rows.Clear();
                cmbExistingTrainTypes.Items.Clear();
                cmbExistingTrainTypes.Items.Add("-- Select Train Type --");

                // Populate DataGridView and ComboBox
                foreach (DataRow row in trainTypes.Rows)
                {
                    dgvTrainTypes.Rows.Add(
                        row["ID"],
                        row["Name"],
                        row["Description"],
                        Convert.ToBoolean(row["IsActive"]) ? "Yes" : "No",
                        Convert.ToDateTime(row["CreatedDate"]).ToString("yyyy-MM-dd")
                    );

                    // Add to ComboBox for editing (only active train types)
                    if (Convert.ToBoolean(row["IsActive"]))
                    {
                        cmbExistingTrainTypes.Items.Add(new TrainTypeComboItem
                        {
                            ID = row["ID"].ToString(),
                            Name = row["Name"].ToString()
                        });
                    }
                }

                cmbExistingTrainTypes.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                toast.ShowError($"Error loading train types: {ex.Message}");
            }
        }

        private void CmbExistingTrainTypes_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (cmbExistingTrainTypes.SelectedIndex > 0 && cmbExistingTrainTypes.SelectedItem is TrainTypeComboItem selectedItem)
            {
                LoadTrainTypeForEdit(selectedItem.ID);
            }
            else
            {
                ClearForm();
            }
        }

        private void LoadTrainTypeForEdit(string trainTypeId)
        {
            try
            {
                var trainType = trainTypeService.GetTrainTypeById(trainTypeId);
                if (trainType != null)
                {
                    txtTrainTypeName.Text = trainType.Name;
                    txtDescription.Text = trainType.Description;
                    chkIsActive.Checked = trainType.IsActive;

                    currentTrainTypeId = trainType.ID;
                    isEditMode = true;

                    btnSave.Enabled = false;
                    btnUpdate.Enabled = true;
                    btnDelete.Enabled = true;
                }
            }
            catch (Exception ex)
            {
                toast.ShowError($"Error loading train type: {ex.Message}");
            }
        }

        private void DgvTrainTypes_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                var trainTypeId = dgvTrainTypes.Rows[e.RowIndex].Cells["ID"].Value.ToString();
                LoadTrainTypeForEdit(trainTypeId);

                // Update ComboBox selection
                for (int i = 1; i < cmbExistingTrainTypes.Items.Count; i++)
                {
                    if (cmbExistingTrainTypes.Items[i] is TrainTypeComboItem item && item.ID == trainTypeId)
                    {
                        cmbExistingTrainTypes.SelectedIndex = i;
                        break;
                    }
                }
            }
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (ValidateInput())
            {
                try
                {
                    var trainType = new TrainType
                    {
                        Name = txtTrainTypeName.Text.Trim(),
                        Description = txtDescription.Text.Trim(),
                        IsActive = chkIsActive.Checked
                    };

                    trainTypeService.AddTrainType(trainType);
                    toast.ShowSuccess("Train type added successfully!");
                    LoadTrainTypes();
                    ClearForm();
                }
                catch (Exception ex)
                {
                    toast.ShowError($"Error adding train type: {ex.Message}");
                }
            }
        }

        private void BtnUpdate_Click(object sender, EventArgs e)
        {
            if (ValidateInput() && !string.IsNullOrEmpty(currentTrainTypeId))
            {
                try
                {
                    var trainType = new TrainType
                    {
                        ID = currentTrainTypeId,
                        Name = txtTrainTypeName.Text.Trim(),
                        Description = txtDescription.Text.Trim(),
                        IsActive = chkIsActive.Checked
                    };

                    trainTypeService.UpdateTrainType(trainType);
                    toast.ShowSuccess("Train type updated successfully!");
                    LoadTrainTypes();
                    ClearForm();
                }
                catch (Exception ex)
                {
                    toast.ShowError($"Error updating train type: {ex.Message}");
                }
            }
        }

        private void BtnDelete_Click(object sender, EventArgs e)
        {
            if (!string.IsNullOrEmpty(currentTrainTypeId))
            {
                var result = MessageBox.Show(
                    "Are you sure you want to delete this train type?",
                    "Confirm Delete",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question
                );

                if (result == DialogResult.Yes)
                {
                    try
                    {
                        trainTypeService.DeleteTrainType(currentTrainTypeId);
                        toast.ShowSuccess("Train type deleted successfully!");
                        LoadTrainTypes();
                        ClearForm();
                    }
                    catch (Exception ex)
                    {
                        toast.ShowError($"Error deleting train type: {ex.Message}");
                    }
                }
            }
        }

        private void BtnClear_Click(object sender, EventArgs e)
        {
            ClearForm();
        }

        private void ClearForm()
        {
            txtTrainTypeName.Text = string.Empty;
            txtDescription.Text = string.Empty;
            chkIsActive.Checked = true;
            cmbExistingTrainTypes.SelectedIndex = 0;

            currentTrainTypeId = null;
            isEditMode = false;

            btnSave.Enabled = true;
            btnUpdate.Enabled = false;
            btnDelete.Enabled = false;
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(txtTrainTypeName.Text))
            {
                toast.ShowError("Please enter train type name.");
                txtTrainTypeName.Focus();
                return false;
            }

            if (txtTrainTypeName.Text.Trim().Length < 2)
            {
                toast.ShowError("Train type name must be at least 2 characters long.");
                txtTrainTypeName.Focus();
                return false;
            }

            return true;
        }

        // Helper class for ComboBox items
        private class TrainTypeComboItem
        {
            public string ID { get; set; }
            public string Name { get; set; }

            public override string ToString()
            {
                return Name;
            }
        }
    }
}