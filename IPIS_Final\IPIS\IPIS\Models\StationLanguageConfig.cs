using System;

namespace IPIS.Models
{
    public class StationLanguageConfig
    {
        public int Id { get; set; }
        public string StationName { get; set; }
        public string LanguageCode { get; set; }
        public bool IsEnabled { get; set; }
        public string WaveFilePath { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }

        // Navigation properties
        public Language Language { get; set; }
        public StationDetails Station { get; set; }
    }
} 