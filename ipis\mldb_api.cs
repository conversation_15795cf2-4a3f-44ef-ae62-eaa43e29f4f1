// Decompiled with JetBrains decompiler
// Type: ipis.mldb_api
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Diagnostics;
using System.Threading;

namespace ipis
{

public class mldb_api
{
  private static byte[] pkt_buf = new byte[4000];
  private static byte[] rxbuf = new byte[2001];

  [DebuggerNonUserCode]
  public mldb_api()
  {
  }

  public static byte mldb_link_check(byte mldb_addr, ref byte[] mldb_lc_pkt, ref short length)
  {
    int index = 0;
    try
    {
      mldb_api.pkt_buf[0] = (byte) 170;
      mldb_api.pkt_buf[1] = (byte) 204;
      mldb_api.pkt_buf[2] = (byte) 0;
      mldb_api.pkt_buf[3] = (byte) 10;
      mldb_api.pkt_buf[4] = frmMainFormIPIS.ccu_addr;
      mldb_api.pkt_buf[5] = (byte) 0;
      mldb_api.pkt_buf[6] = mldb_addr;
      mldb_api.pkt_buf[7] = (byte) 0;
      mldb_api.pkt_buf[8] = (byte) 0;
      mldb_api.pkt_buf[9] = (byte) 128 /*0x80*/;
      ushort length1 = checked ((ushort) ((int) unchecked ((ushort) ((int) (ushort) (0U << 8) | 10)) + 2));
      Checksum.prepare_checksum(ref mldb_api.pkt_buf, length1);
      length = checked ((short) length1);
      while (index < (int) length1)
      {
        mldb_lc_pkt[index] = mldb_api.pkt_buf[index];
        checked { ++index; }
      }
      if (RS232.Serial_Write(ref mldb_api.pkt_buf, (int) length1) != 1)
      {
        Log_file.Log(string.Format("Write to COM port failed, may be COM port deos't exit. Please check the COM PORT settings"));
        return 3;
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    return 1;
  }

  public static byte mldb_link_check_res_pkt(
    byte mldb_addr,
    ref byte[] mldb_lc_pkt,
    ref short length,
    ref byte mldb_sys_cfg)
  {
    byte num1 = 0;
    try
    {
      if (RS232.Serial_Read(ref mldb_api.rxbuf) == (byte) 1)
      {
        ushort Pkt_length = checked ((ushort) unchecked ((int) (ushort) ((uint) (ushort) mldb_api.rxbuf[0] << 8) + (int) mldb_api.rxbuf[1]));
        length = checked ((short) (2 + (int) Pkt_length));
        byte index = 0;
        mldb_lc_pkt[0] = (byte) 170;
        mldb_lc_pkt[1] = (byte) 204;
        while ((uint) index < (uint) Pkt_length)
        {
          mldb_lc_pkt[checked ((int) index + 2)] = mldb_api.rxbuf[(int) index];
          checked { ++index; }
        }
        mldb_sys_cfg = mldb_api.rxbuf[9];
        byte num2 = 0;
        if ((int) mldb_api.rxbuf[2] != (int) mldb_addr & mldb_api.rxbuf[3] != (byte) 0)
        {
          Log_file.Log("MLDB Address:{mldb_addr}LINK CHECK RESPONSE PACKET: IN CORRECT SOURCE ADDRESS");
          num2 = (byte) 1;
        }
        if (Pkt_length != (ushort) 12)
        {
          Log_file.Log(" MLDB Address:{mldb_addr}LINK CHECK RESPONSE PACKET: IN VALID DATA LENGTH");
          num2 = (byte) 1;
        }
        if (mldb_api.rxbuf[7] != (byte) 192 /*0xC0*/)
        {
          Log_file.Log(" MLDB Address:{mldb_addr}LINK CHECK RESPONSE PACKET: IN-VALID RESPONSE PACKET FUNCTION CODE");
          num1 = (byte) 9;
        }
        else
        {
          switch (mldb_api.rxbuf[8])
          {
            case 0:
              Log_file.Log(" MLDB Address:{mldb_addr}LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully ");
              break;
            case 2:
              Log_file.Log(" MLDB Address:{mldb_addr}LINK CHECK COMMAND PACKET STATUS: CRC FAIL");
              num2 = (byte) 1;
              break;
            case 6:
              Log_file.Log(" MLDB Address:{mldb_addr}LINK CHECK COMMAND PACKET STATUS: IN VALID FUNCTION CODE");
              num2 = (byte) 1;
              break;
            case 35:
              Log_file.Log(" MLDB Address:{mldb_addr}LINK CHECK COMMAND PACKET STATUS: IN VALID DATA LENGTH");
              num2 = (byte) 1;
              break;
          }
          if (Checksum.Checksum_Calc(ref mldb_api.rxbuf, Pkt_length) == 0)
          {
            Log_file.Log("MLDB Address:{mldb_addr}LINK CHECK RESPONSE PACKET: CHECKSUM FAILED ");
            num1 = (byte) 9;
          }
          else if (num2 != (byte) 0)
          {
            Log_file.Log(" MLDB Address:{mldb_addr}LINK CHECK COMMAND IS UNSUCCESSFULL");
            num1 = (byte) 0;
          }
          else
          {
            Log_file.Log(" MLDB Address:{mldb_addr}LINK CHECK COMMAND IS SUCCESSFUL");
            num1 = (byte) 1;
          }
        }
      }
      else
      {
        Log_file.Log(" MLDB Address:{mldb_addr} LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST");
        num1 = (byte) 2;
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num3 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    return num1;
  }

  public static byte mldb_message(
    byte mldb_addr,
    ref byte[] mldb_msg,
    byte mldb_effect,
    int mldb_msg_len,
    byte serial_no,
    byte mldb_sw_dly,
    byte mldb_video,
    byte packet_type,
    byte run_speed)
  {
    int index1 = 0;
    byte num1 = 0;
    try
    {
      while (index1 < 4000)
      {
        mldb_api.pkt_buf[index1] = (byte) 0;
        checked { ++index1; }
      }
      mldb_api.pkt_buf[0] = (byte) 170;
      mldb_api.pkt_buf[1] = (byte) 204;
      mldb_api.pkt_buf[4] = frmMainFormIPIS.ccu_addr;
      mldb_api.pkt_buf[5] = (byte) 0;
      mldb_api.pkt_buf[6] = mldb_addr;
      mldb_api.pkt_buf[7] = (byte) 0;
      mldb_api.pkt_buf[8] = serial_no;
      mldb_api.pkt_buf[9] = (byte) 129;
      mldb_api.pkt_buf[10] = packet_type;
      mldb_api.pkt_buf[11] = mldb_sw_dly;
      mldb_effect = checked ((byte) (((int) mldb_effect & 15) << 4));
      checked { mldb_video &= (byte) 15; }
      mldb_api.pkt_buf[12] = (byte) ((int) mldb_effect | (int) mldb_video);
      mldb_api.pkt_buf[13] = run_speed;
      int index2 = 0;
      while (index2 < mldb_msg_len)
      {
        mldb_api.pkt_buf[checked (14 + index2)] = mldb_msg[index2];
        checked { ++index2; }
      }
      ushort num2 = checked ((ushort) (mldb_msg_len + 14));
      mldb_api.pkt_buf[3] = checked ((byte) ((int) num2 & (int) byte.MaxValue));
      mldb_api.pkt_buf[2] = checked ((byte) (((int) num2 & 65280) >> 8));
      ushort length = checked ((ushort) ((int) num2 + 2));
      Checksum.prepare_checksum(ref mldb_api.pkt_buf, length);
      if (RS232.Serial_Write(ref mldb_api.pkt_buf, (int) length) != 1)
      {
        Log_file.Log(string.Format("Write to COM port failed, may be COM port deos't exit. Please check the COM PORT settings"));
        num1 = (byte) 3;
      }
      else
      {
        int num3 = 0;
        if (RS232.Serial_Read(ref mldb_api.rxbuf) == (byte) 1)
        {
          ushort Pkt_length = checked ((ushort) unchecked ((int) (ushort) ((uint) (ushort) mldb_api.rxbuf[0] << 8) + (int) mldb_api.rxbuf[1]));
          if (Checksum.Checksum_Calc(ref mldb_api.rxbuf, Pkt_length) == 0)
          {
            Log_file.Log(" MLDB Address:{mldb_addr} DATA RESPONSE PACKET: CHECKSUM FAILED ");
            num3 = 1;
          }
          if ((int) mldb_api.rxbuf[2] != (int) mldb_addr & mldb_api.rxbuf[3] != (byte) 0)
          {
            Log_file.Log(" MLDB Address:{mldb_addr} DATA RESPONSE PACKET: IN CORRECT SOURCE ADDRESS");
            num3 = 1;
          }
          if (Pkt_length != (ushort) 11)
          {
            Log_file.Log(" MLDB Address:{mldb_addr} DATA RESPONSE PACKET: IN VALID DATA LENGTH");
            num3 = 1;
          }
          if (mldb_api.rxbuf[7] != (byte) 193)
          {
            Log_file.Log(" MLDB Address:{mldb_addr} DATA RESPONSE PACKET: IN-VALID RESPONSE PACKET FUNCTION CODE");
            num3 = 1;
          }
          switch (mldb_api.rxbuf[8])
          {
            case 0:
              Log_file.Log(" MLDB Address:{mldb_addr} DATA PACKET STATUS: Packet Received and proceed successfully ");
              break;
            case 2:
              Log_file.Log(" MLDB Address:{mldb_addr} DATA PACKET STATUS: CRC FAIL");
              num3 = 1;
              break;
            case 6:
              Log_file.Log(" MLDB Address:{mldb_addr} DATA PACKET STATUS: IN VALID FUNCTION CODE");
              num3 = 1;
              break;
            case 35:
              Log_file.Log(" MLDB Address:{mldb_addr} DATA PACKET STATUS: IN VALID DATA LENGTH");
              num3 = 1;
              break;
          }
          if (num3 != 0)
          {
            Log_file.Log(" MLDB Address:{mldb_addr} DATA PACKET IS UNSUCCESSFULL");
            num1 = (byte) 0;
          }
          else
          {
            Log_file.Log(" MLDB Address:{mldb_addr} DATA PACKET IS SUCCESSFUL");
            num1 = (byte) 1;
          }
        }
        else
        {
          Log_file.Log(" MLDB Address:{mldb_addr} LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST");
          num1 = (byte) 2;
        }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num4 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    return num1;
  }

  public static byte mldb_delete_msg(byte mldb_addr, byte serial_no)
  {
    int index = 0;
    byte num1 = 0;
    try
    {
      while (index < 4000)
      {
        mldb_api.pkt_buf[index] = (byte) 0;
        checked { ++index; }
      }
      mldb_api.pkt_buf[0] = (byte) 170;
      mldb_api.pkt_buf[1] = (byte) 204;
      mldb_api.pkt_buf[4] = frmMainFormIPIS.ccu_addr;
      mldb_api.pkt_buf[5] = (byte) 0;
      mldb_api.pkt_buf[6] = mldb_addr;
      mldb_api.pkt_buf[7] = (byte) 0;
      mldb_api.pkt_buf[8] = serial_no;
      mldb_api.pkt_buf[9] = (byte) 129;
      mldb_api.pkt_buf[10] = (byte) 2;
      mldb_api.pkt_buf[11] = (byte) 0;
      mldb_api.pkt_buf[12] = (byte) 0;
      mldb_api.pkt_buf[13] = (byte) 0;
      ushort num2 = 14;
      mldb_api.pkt_buf[2] = (byte) 0;
      mldb_api.pkt_buf[3] = (byte) 14;
      ushort length = checked ((ushort) ((int) num2 + 2));
      Checksum.prepare_checksum(ref mldb_api.pkt_buf, length);
      if (RS232.Serial_Write(ref mldb_api.pkt_buf, (int) length) != 1)
      {
        Log_file.Log(string.Format("Write to COM port failed, may be COM port deos't exit. Please check the COM PORT settings"));
        num1 = (byte) 3;
      }
      else
      {
        int num3 = 0;
        if (RS232.Serial_Read(ref mldb_api.rxbuf) == (byte) 1)
        {
          ushort Pkt_length = checked ((ushort) unchecked ((int) (ushort) ((uint) (ushort) mldb_api.rxbuf[0] << 8) + (int) mldb_api.rxbuf[1]));
          if (Checksum.Checksum_Calc(ref mldb_api.rxbuf, Pkt_length) == 0)
          {
            Log_file.Log(" MLDB Address:{mldb_addr} DATA RESPONSE PACKET: CHECKSUM FAILED ");
            num3 = 1;
          }
          if ((int) mldb_api.rxbuf[2] != (int) mldb_addr & mldb_api.rxbuf[3] != (byte) 0)
          {
            Log_file.Log(" MLDB Address:{mldb_addr} DATA RESPONSE PACKET: IN CORRECT SOURCE ADDRESS");
            num3 = 1;
          }
          if (Pkt_length != (ushort) 11)
          {
            Log_file.Log(" MLDB Address:{mldb_addr} DATA RESPONSE PACKET: IN VALID DATA LENGTH");
            num3 = 1;
          }
          if (mldb_api.rxbuf[7] != (byte) 193)
          {
            Log_file.Log(" MLDB Address:{mldb_addr} DATA RESPONSE PACKET: IN-VALID RESPONSE PACKET FUNCTION CODE");
            num3 = 1;
          }
          switch (mldb_api.rxbuf[8])
          {
            case 0:
              Log_file.Log(" MLDB Address:{mldb_addr} DATA PACKET STATUS: Packet Received and proceed successfully ");
              break;
            case 2:
              Log_file.Log(" MLDB Address:{mldb_addr} DATA PACKET STATUS: CRC FAIL");
              num3 = 1;
              break;
            case 6:
              Log_file.Log(" MLDB Address:{mldb_addr} DATA PACKET STATUS: IN VALID FUNCTION CODE");
              num3 = 1;
              break;
            case 35:
              Log_file.Log(" MLDB Address:{mldb_addr} DATA PACKET STATUS: IN VALID DATA LENGTH");
              num3 = 1;
              break;
          }
          if (num3 != 0)
          {
            Log_file.Log(" MLDB Address:{mldb_addr} DATA PACKET IS UNSUCCESSFULL");
            num1 = (byte) 0;
          }
          else
          {
            Log_file.Log(" MLDB Address:{mldb_addr} DATA PACKET IS SUCCESSFUL");
            num1 = (byte) 1;
          }
        }
        else
        {
          Log_file.Log(" MLDB Address:{mldb_addr} LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST");
          num1 = (byte) 2;
        }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num4 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    return num1;
  }

  public static byte mldb_set_cfg_send_pkt(byte mldb_addr, ref byte[] mldb_sc_pkt, ref byte length)
  {
    int index1 = 0;
    byte noOfLines = 0;
    while (index1 < (int) taddb_msg.mldb_dis_brd.no_of_mldbs)
    {
      if ((int) taddb_msg.mldb_dis_brd.mdlb[index1].mldb_addr == (int) mldb_addr)
        noOfLines = taddb_msg.mldb_dis_brd.mdlb[index1].no_of_lines;
      checked { ++index1; }
    }
    byte num1 = 0;
    try
    {
      mldb_api.pkt_buf[0] = (byte) 170;
      mldb_api.pkt_buf[1] = (byte) 204;
      mldb_api.pkt_buf[2] = (byte) 0;
      mldb_api.pkt_buf[3] = (byte) 12;
      mldb_api.pkt_buf[4] = frmMainFormIPIS.ccu_addr;
      mldb_api.pkt_buf[5] = (byte) 0;
      mldb_api.pkt_buf[6] = mldb_addr;
      mldb_api.pkt_buf[7] = (byte) 0;
      mldb_api.pkt_buf[8] = (byte) 0;
      mldb_api.pkt_buf[9] = (byte) 132;
      mldb_api.pkt_buf[10] = frmMainFormIPIS.intensity;
      mldb_api.pkt_buf[11] = noOfLines;
      ushort length1 = checked ((ushort) ((int) unchecked ((ushort) ((int) (ushort) (0U << 8) | 12)) + 2));
      Checksum.prepare_checksum(ref mldb_api.pkt_buf, length1);
      length = checked ((byte) length1);
      int index2 = 0;
      while (index2 < (int) length1)
      {
        mldb_sc_pkt[index2] = mldb_api.pkt_buf[index2];
        checked { ++index2; }
      }
      if (RS232.Serial_Write(ref mldb_api.pkt_buf, (int) length1) != 1)
      {
        Log_file.Log(string.Format("Write to COM port failed, may be COM port deos't exit. Please check the COM PORT settings"));
        num1 = (byte) 3;
      }
      else
        num1 = (byte) 1;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num2 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    return num1;
  }

  public static byte mldb_set_cfg_res_pkt(byte mldb_addr, ref byte[] mldb_sc_pkt, ref byte length)
  {
    byte num1 = 0;
    try
    {
      if (RS232.Serial_Read(ref mldb_api.rxbuf) == (byte) 1)
      {
        ushort Pkt_length = checked ((ushort) unchecked ((int) (ushort) ((uint) (ushort) mldb_api.rxbuf[0] << 8) + (int) mldb_api.rxbuf[1]));
        length = checked ((byte) ((int) Pkt_length + 2));
        byte index = 0;
        mldb_sc_pkt[0] = (byte) 170;
        mldb_sc_pkt[1] = (byte) 204;
        while ((uint) index < (uint) Pkt_length)
        {
          mldb_sc_pkt[checked ((int) index + 2)] = mldb_api.rxbuf[(int) index];
          checked { ++index; }
        }
        byte num2 = 0;
        if ((int) mldb_api.rxbuf[2] != (int) mldb_addr & mldb_api.rxbuf[3] != (byte) 0)
        {
          Log_file.Log(" MLDB Address:{mldb_addr} SET CONFIGURATION RESPONSE PACKET: IN CORRECT SOURCE ADDRESS");
          num2 = (byte) 1;
        }
        if (Pkt_length != (ushort) 11)
        {
          Log_file.Log(" MLDB Address:{mldb_addr} SET CONFIGURATION RESPONSE PACKET: IN VALID DATA LENGTH");
          num2 = (byte) 1;
        }
        if (mldb_api.rxbuf[7] != (byte) 196)
        {
          Log_file.Log(" MLDB Address:{mldb_addr} SET CONFIGURATION RESPONSE PACKET: IN VALID FUNCTION CODE");
          num2 = (byte) 1;
        }
        switch (mldb_api.rxbuf[8])
        {
          case 0:
            Log_file.Log(" MLDB Address:{mldb_addr} SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully");
            break;
          case 2:
            Log_file.Log(" MLDB Address:{mldb_addr} SET CONFIGURATION COMMAND PACKET: CRC FAIL");
            num2 = (byte) 1;
            break;
          case 6:
            Log_file.Log(" MLDB Address:{mldb_addr} SET CONFIGURATION COMMAND PACKET: IN VALID FUNCTION CODE");
            num2 = (byte) 1;
            break;
          case 35:
            Log_file.Log(" MLDB Address:{mldb_addr} SET CONFIGURATION COMMAND PACKET: IN VALID DATA LENGTH");
            num2 = (byte) 1;
            break;
        }
        if (Checksum.Checksum_Calc(ref mldb_api.rxbuf, Pkt_length) == 0)
        {
          Log_file.Log(" MLDB Address:{mldb_addr} SET CONFIGURATION RESPONSE PACKET: CHECKSUM FAILED");
          num1 = (byte) 9;
        }
        else if (num2 != (byte) 0)
        {
          Log_file.Log(" MLDB Address:{mldb_addr} SET CONFIGURATION COMMAND IS UNSUCCESSFUL");
          num1 = (byte) 0;
        }
        else
        {
          Log_file.Log(" MLDB Address:{mldb_addr} SET CONFIGURATION COMMAND IS SUCCESSFUL");
          num1 = (byte) 1;
        }
      }
      else
      {
        Log_file.Log(" MLDB Address:{mldb_addr} LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST");
        num1 = (byte) 2;
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num3 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    return num1;
  }

  public static byte mldb_get_cfg_send_pkt(byte mldb_addr, ref byte[] mldb_gc_pkt, ref byte length)
  {
    byte cfgSendPkt = 0;
    try
    {
      mldb_api.pkt_buf[0] = (byte) 170;
      mldb_api.pkt_buf[1] = (byte) 204;
      mldb_api.pkt_buf[2] = (byte) 0;
      mldb_api.pkt_buf[3] = (byte) 10;
      mldb_api.pkt_buf[4] = frmMainFormIPIS.ccu_addr;
      mldb_api.pkt_buf[5] = (byte) 0;
      mldb_api.pkt_buf[6] = mldb_addr;
      mldb_api.pkt_buf[7] = (byte) 0;
      mldb_api.pkt_buf[8] = (byte) 0;
      mldb_api.pkt_buf[9] = (byte) 133;
      ushort length1 = checked ((ushort) ((int) unchecked ((ushort) ((int) (ushort) (0U << 8) | 10)) + 2));
      Checksum.prepare_checksum(ref mldb_api.pkt_buf, length1);
      int index = 0;
      while (index < (int) length1)
      {
        mldb_gc_pkt[index] = mldb_api.pkt_buf[index];
        checked { ++index; }
      }
      length = checked ((byte) length1);
      if (RS232.Serial_Write(ref mldb_api.pkt_buf, (int) length1) != 1)
      {
        Log_file.Log(string.Format("Write to COM port failed, may be COM port deos't exit. Please check the COM PORT settings"));
        cfgSendPkt = (byte) 3;
      }
      else
        cfgSendPkt = (byte) 1;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    return cfgSendPkt;
  }

  public static byte mldb_get_cfg_res_pkt(
    byte mldb_addr,
    ref byte[] mldb_gc_pkt,
    ref byte length,
    ref byte mldb_intensity,
    ref byte mldb_sys_cfg)
  {
    byte cfgResPkt = 0;
    try
    {
      if (RS232.Serial_Read(ref mldb_api.rxbuf) == (byte) 1)
      {
        ushort Pkt_length = checked ((ushort) unchecked ((int) (ushort) ((uint) (ushort) mldb_api.rxbuf[0] << 8) + (int) mldb_api.rxbuf[1]));
        length = checked ((byte) ((int) Pkt_length + 2));
        byte index = 0;
        mldb_gc_pkt[0] = (byte) 170;
        mldb_gc_pkt[1] = (byte) 204;
        while ((uint) index < (uint) Pkt_length)
        {
          mldb_gc_pkt[checked ((int) index + 2)] = mldb_api.rxbuf[(int) index];
          checked { ++index; }
        }
        byte num = 0;
        if ((int) mldb_api.rxbuf[2] != (int) mldb_addr & mldb_api.rxbuf[3] != (byte) 0)
        {
          Log_file.Log(" MLDB Address:{mldb_addr} GET CONFIGURATION RESPONSE PACKET: IN CORRECT SOURCE ADDRESS");
          num = (byte) 1;
        }
        if (Pkt_length != (ushort) 14)
        {
          Log_file.Log(" MLDB Address:{mldb_addr} GET CONFIGURATION RESPONSE PACKET: IN VALID DATA LENGTH");
          num = (byte) 1;
        }
        if (mldb_api.rxbuf[7] != (byte) 197)
        {
          Log_file.Log(" MLDB Address:{mldb_addr} GET CONFIGURATION RESPONSE PACKET: IN VALID FUNCTION CODE");
          num = (byte) 1;
        }
        switch (mldb_api.rxbuf[8])
        {
          case 0:
            Log_file.Log(" MLDB Address:{mldb_addr} GET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully");
            break;
          case 2:
            Log_file.Log(" MLDB Address:{mldb_addr} GET CONFIGURATION COMMAND PACKET: CRC FAIL");
            num = (byte) 1;
            break;
          case 6:
            Log_file.Log(" MLDB Address:{mldb_addr} GET CONFIGURATION COMMAND PACKET: IN VALID FUNCTION CODE");
            num = (byte) 1;
            break;
          case 35:
            Log_file.Log(" MLDB Address:{mldb_addr} GET CONFIGURATION COMMAND PACKET: IN VALID DATA LENGTH");
            num = (byte) 1;
            break;
        }
        mldb_sys_cfg = mldb_api.rxbuf[9];
        mldb_intensity = mldb_api.rxbuf[10];
        if (Checksum.Checksum_Calc(ref mldb_api.rxbuf, Pkt_length) == 0)
        {
          Log_file.Log(" MLDB Address:{mldb_addr} GET CONFIGURATION RESPONSE PACKET: CHECKSUM FAILED");
          cfgResPkt = (byte) 9;
        }
        else if (num != (byte) 0)
        {
          Log_file.Log(" MLDB Address:{mldb_addr} GET CONFIGURATION COMMAND IS UNSUCCESSFUL");
          cfgResPkt = (byte) 0;
        }
        else
        {
          Log_file.Log(" MLDB Address:{mldb_addr} GET CONFIGURATION COMMAND IS SUCCESSFUL");
          cfgResPkt = (byte) 1;
        }
      }
      else
      {
        Log_file.Log(" MLDB Address:{mldb_addr} LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST");
        cfgResPkt = (byte) 2;
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    return cfgResPkt;
  }

  public static byte mldb_get_cfg(byte mldb_addr, ref byte mldb_intensity, ref byte mldb_sys_cfg)
  {
    byte cfg = 0;
    try
    {
      mldb_api.pkt_buf[0] = (byte) 170;
      mldb_api.pkt_buf[1] = (byte) 204;
      mldb_api.pkt_buf[2] = (byte) 0;
      mldb_api.pkt_buf[3] = (byte) 10;
      mldb_api.pkt_buf[4] = frmMainFormIPIS.ccu_addr;
      mldb_api.pkt_buf[5] = (byte) 0;
      mldb_api.pkt_buf[6] = mldb_addr;
      mldb_api.pkt_buf[7] = (byte) 0;
      mldb_api.pkt_buf[8] = (byte) 0;
      mldb_api.pkt_buf[9] = (byte) 133;
      ushort length = checked ((ushort) ((int) unchecked ((ushort) ((int) (ushort) (0U << 8) | 10)) + 2));
      Checksum.prepare_checksum(ref mldb_api.pkt_buf, length);
      if (RS232.Serial_Write(ref mldb_api.pkt_buf, (int) length) != 1)
      {
        Log_file.Log(string.Format("Write to COM port failed, may be COM port deos't exit. Please check the COM PORT settings"));
        cfg = (byte) 3;
      }
      else
      {
        byte num = 0;
        if (RS232.Serial_Read(ref mldb_api.rxbuf) == (byte) 1)
        {
          ushort Pkt_length = checked ((ushort) unchecked ((int) (ushort) ((uint) (ushort) mldb_api.rxbuf[0] << 8) + (int) mldb_api.rxbuf[1]));
          if (Checksum.Checksum_Calc(ref mldb_api.rxbuf, Pkt_length) == 0)
          {
            Log_file.Log(" MLDB Address:{mldb_addr} GET CONFIGURATION RESPONSE PACKET: CHECKSUM FAILED");
            num = (byte) 1;
          }
          if ((int) mldb_api.rxbuf[2] != (int) mldb_addr & mldb_api.rxbuf[3] != (byte) 0)
          {
            Log_file.Log(" MLDB Address:{mldb_addr} GET CONFIGURATION RESPONSE PACKET: IN CORRECT SOURCE ADDRESS");
            num = (byte) 1;
          }
          if (Pkt_length != (ushort) 14)
          {
            Log_file.Log(" MLDB Address:{mldb_addr} GET CONFIGURATION RESPONSE PACKET: IN VALID DATA LENGTH");
            num = (byte) 1;
          }
          if (mldb_api.rxbuf[7] != (byte) 197)
          {
            Log_file.Log(" MLDB Address:{mldb_addr} GET CONFIGURATION RESPONSE PACKET: IN VALID FUNCTION CODE");
            num = (byte) 1;
          }
          switch (mldb_api.rxbuf[8])
          {
            case 0:
              Log_file.Log(" MLDB Address:{mldb_addr} GET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully");
              break;
            case 2:
              Log_file.Log(" MLDB Address:{mldb_addr} GET CONFIGURATION COMMAND PACKET: CRC FAIL");
              num = (byte) 1;
              break;
            case 6:
              Log_file.Log(" MLDB Address:{mldb_addr} GET CONFIGURATION COMMAND PACKET: IN VALID FUNCTION CODE");
              num = (byte) 1;
              break;
            case 35:
              Log_file.Log(" MLDB Address:{mldb_addr} GET CONFIGURATION COMMAND PACKET: IN VALID DATA LENGTH");
              num = (byte) 1;
              break;
          }
          mldb_sys_cfg = mldb_api.rxbuf[9];
          mldb_intensity = mldb_api.rxbuf[10];
          if (num != (byte) 0)
          {
            Log_file.Log(" MLDB Address:{mldb_addr} GET CONFIGURATION COMMAND IS UNSUCCESSFUL");
            cfg = (byte) 0;
          }
          else
          {
            Log_file.Log(" MLDB Address:{mldb_addr} GET CONFIGURATION COMMAND IS SUCCESSFUL");
            cfg = (byte) 1;
          }
        }
        else
        {
          Log_file.Log(" MLDB Address:{mldb_addr} LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST");
          cfg = (byte) 2;
        }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    return cfg;
  }

  public static byte mldb_soft_reset(byte mldb_addr, ref byte[] mldb_pkt, ref byte length)
  {
    byte num1 = 0;
    try
    {
      mldb_api.pkt_buf[0] = (byte) 170;
      mldb_api.pkt_buf[1] = (byte) 204;
      mldb_api.pkt_buf[2] = (byte) 0;
      mldb_api.pkt_buf[3] = (byte) 10;
      mldb_api.pkt_buf[4] = frmMainFormIPIS.ccu_addr;
      mldb_api.pkt_buf[5] = (byte) 0;
      mldb_api.pkt_buf[6] = mldb_addr;
      mldb_api.pkt_buf[7] = (byte) 0;
      mldb_api.pkt_buf[8] = (byte) 0;
      mldb_api.pkt_buf[9] = (byte) 134;
      ushort length1 = checked ((ushort) ((int) unchecked ((ushort) ((int) (ushort) (0U << 8) | 10)) + 2));
      Checksum.prepare_checksum(ref mldb_api.pkt_buf, length1);
      length = checked ((byte) length1);
      int index = 0;
      while (index < (int) length1)
      {
        mldb_pkt[index] = mldb_api.pkt_buf[index];
        checked { ++index; }
      }
      if (RS232.Serial_Write(ref mldb_api.pkt_buf, (int) length1) != 1)
      {
        Log_file.Log(string.Format("Write to COM port failed, may be COM port deos't exit. Please check the COM PORT settings"));
        num1 = (byte) 3;
      }
      else
      {
        Thread.Sleep(10);
        Log_file.Log(" MLDB Address:{mldb_addr} SOFT RESET COMMAND IS SUCCESSFUL");
        num1 = (byte) 1;
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num2 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    return num1;
  }

  public static byte mldb_clr_reset(byte mldb_addr, ref byte[] mldb_pkt, ref byte length)
  {
    byte num = 0;
    try
    {
      mldb_api.pkt_buf[0] = (byte) 170;
      mldb_api.pkt_buf[1] = (byte) 204;
      mldb_api.pkt_buf[2] = (byte) 0;
      mldb_api.pkt_buf[3] = (byte) 10;
      mldb_api.pkt_buf[4] = frmMainFormIPIS.ccu_addr;
      mldb_api.pkt_buf[5] = (byte) 0;
      mldb_api.pkt_buf[6] = mldb_addr;
      mldb_api.pkt_buf[7] = (byte) 0;
      mldb_api.pkt_buf[8] = (byte) 0;
      mldb_api.pkt_buf[9] = (byte) 135;
      ushort length1 = checked ((ushort) ((int) unchecked ((ushort) ((int) (ushort) (0U << 8) | 10)) + 2));
      Checksum.prepare_checksum(ref mldb_api.pkt_buf, length1);
      int index = 0;
      while (index < (int) length1)
      {
        mldb_pkt[index] = mldb_api.pkt_buf[index];
        checked { ++index; }
      }
      length = checked ((byte) length1);
      if (RS232.Serial_Write(ref mldb_api.pkt_buf, (int) length1) != 1)
      {
        Log_file.Log(string.Format("Write to COM port failed, may be COM port deos't exit. Please check the COM PORT settings"));
        num = (byte) 3;
      }
      else
        num = (byte) 1;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
    return num;
  }

  public static byte mldb_clr_reset_res_pkt(byte mldb_addr, ref byte[] mldb_pkt, ref byte length)
  {
    byte num1 = 0;
    try
    {
      if (RS232.Serial_Read(ref mldb_api.rxbuf) == (byte) 1)
      {
        ushort Pkt_length = checked ((ushort) unchecked ((int) (ushort) ((uint) (ushort) mldb_api.rxbuf[0] << 8) + (int) mldb_api.rxbuf[1]));
        length = checked ((byte) ((int) Pkt_length + 2));
        byte index = 0;
        mldb_pkt[0] = (byte) 170;
        mldb_pkt[1] = (byte) 204;
        while ((uint) index < (uint) Pkt_length)
        {
          mldb_pkt[checked ((int) index + 2)] = mldb_api.rxbuf[(int) index];
          checked { ++index; }
        }
        byte num2 = 0;
        if ((int) mldb_api.rxbuf[2] != (int) mldb_addr & mldb_api.rxbuf[3] != (byte) 0)
        {
          Log_file.Log(" MLDB Address:{mldb_addr} CLEAR RESET RESPONSE PACKET: IN CORRECT SOURCE ADDRESS");
          num2 = (byte) 1;
        }
        if (Pkt_length != (ushort) 11)
        {
          Log_file.Log(" MLDB Address:{mldb_addr} CLEAR RESET RESPONSE PACKET: IN VALID DATA LENGTH");
          num2 = (byte) 1;
        }
        if (mldb_api.rxbuf[7] != (byte) 199)
        {
          Log_file.Log(" MLDB Address:{mldb_addr} CLEAR RESET RESPONSE PACKET: IN VALID FUNCTION CODE");
          num2 = (byte) 1;
        }
        switch (mldb_api.rxbuf[8])
        {
          case 0:
            Log_file.Log(" MLDB Address:{mldb_addr} CLEAR RESET COMMAND PACKET: Packet Received and Processed Successfully");
            break;
          case 2:
            Log_file.Log(" MLDB Address:{mldb_addr} CLEAR RESET COMMAND PACKET: CRC FAIL");
            num2 = (byte) 1;
            break;
          case 6:
            Log_file.Log(" MLDB Address:{mldb_addr} CLEAR RESET COMMAND PACKET: IN VALID FUNCTION CODE");
            num2 = (byte) 1;
            break;
          case 35:
            Log_file.Log(" MLDB Address:{mldb_addr} CLEAR RESET COMMAND PACKET: IN VALID DATA LENGTH");
            num2 = (byte) 1;
            break;
        }
        if (Checksum.Checksum_Calc(ref mldb_api.rxbuf, Pkt_length) == 0)
        {
          Log_file.Log(" MLDB Address:{mldb_addr} CLEAR RESET RESPONSE PACKET: CHECKSUM FAILED");
          num1 = (byte) 9;
        }
        else if (num2 != (byte) 0)
        {
          Log_file.Log(" MLDB Address:{mldb_addr} CLEAT RESET COMMAND IS UNSUCCESSFUL");
          num1 = (byte) 0;
        }
        else
        {
          Log_file.Log(" MLDB Address:{mldb_addr} CLEAR RESET COMMAND IS SUCCESSFUL");
          num1 = (byte) 1;
        }
      }
      else
      {
        Log_file.Log(" MLDB Address:{mldb_addr} LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST");
        num1 = (byte) 2;
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num3 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    return num1;
  }

  public static byte mldb_pre_cmd_status(byte mldb_addr, ref byte[] mldb_pkt, ref byte length)
  {
    byte num1 = 0;
    try
    {
      mldb_api.pkt_buf[0] = (byte) 170;
      mldb_api.pkt_buf[1] = (byte) 204;
      mldb_api.pkt_buf[2] = (byte) 0;
      mldb_api.pkt_buf[3] = (byte) 10;
      mldb_api.pkt_buf[4] = frmMainFormIPIS.ccu_addr;
      mldb_api.pkt_buf[5] = (byte) 0;
      mldb_api.pkt_buf[6] = mldb_addr;
      mldb_api.pkt_buf[7] = (byte) 0;
      mldb_api.pkt_buf[8] = (byte) 0;
      mldb_api.pkt_buf[9] = (byte) 136;
      ushort length1 = checked ((ushort) ((int) unchecked ((ushort) ((int) (ushort) (0U << 8) | 10)) + 2));
      Checksum.prepare_checksum(ref mldb_api.pkt_buf, length1);
      int index = 0;
      while (index < (int) length1)
      {
        mldb_pkt[index] = mldb_api.pkt_buf[index];
        checked { ++index; }
      }
      length = checked ((byte) length1);
      if (RS232.Serial_Write(ref mldb_api.pkt_buf, (int) length1) != 1)
      {
        Log_file.Log(string.Format("Write to COM port failed, may be COM port deos't exit. Please check the COM PORT settings"));
        num1 = (byte) 3;
      }
      else
        num1 = (byte) 1;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num2 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    return num1;
  }

  public static byte mldb_pre_cmd_res_pkt(
    byte mldb_addr,
    ref byte pre_cmd_status,
    ref byte pre_cmd_serial_no,
    ref byte pre_cmd_fc,
    ref byte[] mldb_pkt,
    ref byte length)
  {
    byte num1 = 0;
    try
    {
      if (RS232.Serial_Read(ref mldb_api.rxbuf) == (byte) 1)
      {
        ushort Pkt_length = checked ((ushort) unchecked ((int) (ushort) ((uint) (ushort) mldb_api.rxbuf[0] << 8) + (int) mldb_api.rxbuf[1]));
        length = checked ((byte) ((int) Pkt_length + 2));
        byte index = 0;
        mldb_pkt[0] = (byte) 170;
        mldb_pkt[1] = (byte) 204;
        while ((uint) index < (uint) Pkt_length)
        {
          mldb_pkt[checked ((int) index + 2)] = mldb_api.rxbuf[(int) index];
          checked { ++index; }
        }
        byte num2 = 0;
        if ((int) mldb_api.rxbuf[2] != (int) mldb_addr & mldb_api.rxbuf[3] != (byte) 0)
        {
          Log_file.Log(" MLDB Address:{mldb_addr} PREVIOUS COMMAND RESPONSE PACKET: IN CORRECT SOURCE ADDRESS");
          num2 = (byte) 1;
        }
        if (Pkt_length != (ushort) 14)
        {
          Log_file.Log(" MLDB Address:{mldb_addr} PREVIOUS COMMAND RESPONSE PACKET: IN VALID DATA LENGTH");
          num2 = (byte) 1;
        }
        if (mldb_api.rxbuf[7] != (byte) 200)
        {
          Log_file.Log(" MLDB Address:{mldb_addr} PREVIOUS COMMAND RESPONSE PACKET: IN VALID FUNCTION CODE");
          num2 = (byte) 1;
        }
        switch (mldb_api.rxbuf[8])
        {
          case 0:
            Log_file.Log(" MLDB Address:{mldb_addr} PREVIOUS COMMAND PACKET: Packet Received and Processed Successfully");
            break;
          case 2:
            Log_file.Log(" MLDB Address:{mldb_addr} PREVIOUS COMMAND PACKET: CRC FAIL");
            num2 = (byte) 1;
            break;
          case 6:
            Log_file.Log(" MLDB Address:{mldb_addr} PREVIOUS COMMAND PACKET: IN VALID FUNCTION CODE");
            num2 = (byte) 1;
            break;
          case 35:
            Log_file.Log(" MLDB Address:{mldb_addr} PREVIOUS COMMAND PACKET: IN VALID DATA LENGTH");
            num2 = (byte) 1;
            break;
        }
        pre_cmd_status = mldb_api.rxbuf[9];
        pre_cmd_serial_no = mldb_api.rxbuf[10];
        pre_cmd_fc = mldb_api.rxbuf[11];
        if (Checksum.Checksum_Calc(ref mldb_api.rxbuf, Pkt_length) == 0)
        {
          Log_file.Log(" MLDB Address:{mldb_addr} PREVIOUS COMMAND RESPONSE PACKET: CHECKSUM FAILED");
          num1 = (byte) 9;
        }
        else if (num2 != (byte) 0)
        {
          Log_file.Log(" MLDB Address:{mldb_addr} PREVIOUS COMMAND IS UNSUCCESSFUL");
          num1 = (byte) 0;
        }
        else
        {
          Log_file.Log(" MLDB Address:{mldb_addr} PREVIOUS COMMAND IS SUCCESSFUL");
          num1 = (byte) 1;
        }
      }
      else
      {
        Log_file.Log(" MLDB Address:{mldb_addr} LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST");
        num1 = (byte) 2;
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num3 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    return num1;
  }

  public static byte mldb_diag_cmd(byte mldb_addr, ref byte[] mldb_pkt, ref byte length)
  {
    byte num1 = 0;
    try
    {
      mldb_api.pkt_buf[0] = (byte) 170;
      mldb_api.pkt_buf[1] = (byte) 204;
      mldb_api.pkt_buf[2] = (byte) 0;
      mldb_api.pkt_buf[3] = (byte) 10;
      mldb_api.pkt_buf[4] = frmMainFormIPIS.ccu_addr;
      mldb_api.pkt_buf[5] = (byte) 0;
      mldb_api.pkt_buf[6] = mldb_addr;
      mldb_api.pkt_buf[7] = (byte) 0;
      mldb_api.pkt_buf[8] = (byte) 0;
      mldb_api.pkt_buf[9] = (byte) 138;
      ushort length1 = checked ((ushort) ((int) unchecked ((ushort) ((int) (ushort) (0U << 8) | 10)) + 2));
      Checksum.prepare_checksum(ref mldb_api.pkt_buf, length1);
      int index = 0;
      while (index < (int) length1)
      {
        mldb_pkt[index] = mldb_api.pkt_buf[index];
        checked { ++index; }
      }
      length = checked ((byte) length1);
      if (RS232.Serial_Write(ref mldb_api.pkt_buf, (int) length1) != 1)
      {
        Log_file.Log(string.Format("Write to COM port failed, may be COM port deos't exit. Please check the COM PORT settings"));
        num1 = (byte) 3;
      }
      else
        num1 = (byte) 1;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num2 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    return num1;
  }

  public static byte mldb_diag_cmd_res_pkt(
    byte mldb_addr,
    ref byte[] mldb_pkt,
    ref byte length,
    ref byte[] manu)
  {
    byte num1 = 0;
    try
    {
      if (RS232.Serial_Read(ref mldb_api.rxbuf) == (byte) 1)
      {
        ushort Pkt_length = checked ((ushort) unchecked ((int) (ushort) ((uint) (ushort) mldb_api.rxbuf[0] << 8) + (int) mldb_api.rxbuf[1]));
        length = checked ((byte) ((int) Pkt_length + 2));
        byte index = 0;
        mldb_pkt[0] = (byte) 170;
        mldb_pkt[1] = (byte) 204;
        while ((uint) index < (uint) Pkt_length)
        {
          mldb_pkt[checked ((int) index + 2)] = mldb_api.rxbuf[(int) index];
          checked { ++index; }
        }
        byte num2 = 0;
        if ((int) mldb_api.rxbuf[2] != (int) mldb_addr & mldb_api.rxbuf[3] != (byte) 0)
        {
          Log_file.Log(" MLDB Address:{mldb_addr} DIAGNOSTIC COMMAND RESPONSE PACKET: IN CORRECT SOURCE ADDRESS");
          num2 = (byte) 1;
        }
        if (Pkt_length != (ushort) 16 /*0x10*/)
        {
          Log_file.Log(" MLDB Address:{mldb_addr} DIAGNOSTIC COMMAND RESPONSE PACKET: IN VALID DATA LENGTH");
          num2 = (byte) 1;
        }
        if (mldb_api.rxbuf[7] != (byte) 202)
        {
          Log_file.Log(string.Format(" mldb Address:(1) DIAGNOSTIC COMMAND RESPONSE PACKET: IN VALID FUNCTION CODE", (object) mldb_addr));
          num2 = (byte) 1;
        }
        switch (mldb_api.rxbuf[8])
        {
          case 0:
            Log_file.Log(" MLDB Address:{mldb_addr} DIAGNOSTIC COMMAND PACKET: Packet Received and Processed Successfully");
            break;
          case 2:
            Log_file.Log(" MLDB Address:{mldb_addr} DIAGNOSTIC COMMAND PACKET: CRC FAIL");
            num2 = (byte) 1;
            break;
          case 6:
            Log_file.Log(" MLDB Address:{mldb_addr} DIAGNOSTIC COMMAND PACKET: IN VALID FUNCTION CODE");
            num2 = (byte) 1;
            break;
          case 35:
            Log_file.Log(" MLDB Address:{mldb_addr} DIAGNOSTIC COMMAND PACKET: IN VALID DATA LENGTH");
            num2 = (byte) 1;
            break;
        }
        manu[0] = mldb_api.rxbuf[9];
        manu[1] = mldb_api.rxbuf[10];
        manu[2] = mldb_api.rxbuf[11];
        manu[3] = mldb_api.rxbuf[12];
        manu[4] = mldb_api.rxbuf[13];
        if (Checksum.Checksum_Calc(ref mldb_api.rxbuf, Pkt_length) == 0)
        {
          Log_file.Log(" MLDB Address:{mldb_addr} DIAGNOSTIC COMMAND RESPONSE PACKET: CHECKSUM FAILED");
          num2 = (byte) 1;
        }
        if (num2 != (byte) 0)
        {
          Log_file.Log(" MLDB Address:{mldb_addr} DIAGNOSTIC COMMAND IS UNSUCCESSFUL");
          num1 = (byte) 0;
        }
        else
        {
          Log_file.Log(" MLDB Address:{mldb_addr} DIAGNOSTIC COMMAND IS SUCCESSFUL");
          num1 = (byte) 1;
        }
      }
      else
      {
        Log_file.Log(" MLDB Address:{mldb_addr} LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST");
        num1 = (byte) 2;
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num3 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    return num1;
  }

  public static byte mldb_optional_cmd(byte mldb_addr, ref byte[] mldb_pkt, ref byte length)
  {
    byte num1 = 0;
    try
    {
      mldb_api.pkt_buf[0] = (byte) 170;
      mldb_api.pkt_buf[1] = (byte) 204;
      mldb_api.pkt_buf[2] = (byte) 0;
      mldb_api.pkt_buf[3] = (byte) 10;
      mldb_api.pkt_buf[4] = frmMainFormIPIS.ccu_addr;
      mldb_api.pkt_buf[5] = (byte) 0;
      mldb_api.pkt_buf[6] = mldb_addr;
      mldb_api.pkt_buf[7] = (byte) 0;
      mldb_api.pkt_buf[8] = (byte) 0;
      mldb_api.pkt_buf[9] = (byte) 139;
      ushort length1 = checked ((ushort) ((int) unchecked ((ushort) ((int) (ushort) (0U << 8) | 10)) + 2));
      Checksum.prepare_checksum(ref mldb_api.pkt_buf, length1);
      int index = 0;
      while (index < (int) length1)
      {
        mldb_pkt[index] = mldb_api.pkt_buf[index];
        checked { ++index; }
      }
      length = checked ((byte) length1);
      if (RS232.Serial_Write(ref mldb_api.pkt_buf, (int) length1) != 1)
      {
        Log_file.Log(string.Format("Write to COM port failed, may be COM port deos't exit. Please check the COM PORT settings"));
        num1 = (byte) 3;
      }
      else
        num1 = (byte) 1;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num2 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    return num1;
  }

  public static byte mldb_opt_cmd_res_pkt(
    byte mldb_addr,
    ref byte[] mldb_pkt,
    ref byte length,
    ref byte[] manu)
  {
    byte num1 = 0;
    try
    {
      if (RS232.Serial_Read(ref mldb_api.rxbuf) == (byte) 1)
      {
        ushort Pkt_length = checked ((ushort) unchecked ((int) (ushort) ((uint) (ushort) mldb_api.rxbuf[0] << 8) + (int) mldb_api.rxbuf[1]));
        length = checked ((byte) ((int) Pkt_length + 2));
        byte index = 0;
        mldb_pkt[0] = (byte) 170;
        mldb_pkt[1] = (byte) 204;
        while ((uint) index < (uint) Pkt_length)
        {
          mldb_pkt[checked ((int) index + 2)] = mldb_api.rxbuf[(int) index];
          checked { ++index; }
        }
        byte num2 = 0;
        if ((int) mldb_api.rxbuf[2] != (int) mldb_addr & mldb_api.rxbuf[3] != (byte) 0)
        {
          Log_file.Log(" MLDB Address:{mldb_addr} OPTIONAL COMMAND RESPONSE PACKET: IN CORRECT SOURCE ADDRESS");
          num2 = (byte) 1;
        }
        if (Pkt_length != (ushort) 16 /*0x10*/)
        {
          Log_file.Log(" MLDB Address:{mldb_addr} OPTIONAL COMMAND RESPONSE PACKET: IN VALID DATA LENGTH");
          num2 = (byte) 1;
        }
        if (mldb_api.rxbuf[7] != (byte) 203)
        {
          Log_file.Log(" MLDB Address:{mldb_addr} OPTIONAL COMMAND RESPONSE PACKET: IN VALID FUNCTION CODE");
          num2 = (byte) 1;
        }
        switch (mldb_api.rxbuf[8])
        {
          case 0:
            Log_file.Log(" MLDB Address:{mldb_addr} OPTIONAL COMMAND PACKET: Packet Received and Processed Successfully");
            break;
          case 2:
            Log_file.Log(" MLDB Address:{mldb_addr} OPTIONAL COMMAND PACKET: CRC FAIL");
            num2 = (byte) 1;
            break;
          case 6:
            Log_file.Log(" MLDB Address:{mldb_addr} OPTIONAL COMMAND PACKET: IN VALID FUNCTION CODE");
            num2 = (byte) 1;
            break;
          case 35:
            Log_file.Log(" MLDB Address:{mldb_addr} OPTIONAL COMMAND PACKET: IN VALID DATA LENGTH");
            num2 = (byte) 1;
            break;
        }
        manu[0] = mldb_api.rxbuf[9];
        manu[1] = mldb_api.rxbuf[10];
        manu[2] = mldb_api.rxbuf[11];
        manu[3] = mldb_api.rxbuf[12];
        manu[4] = mldb_api.rxbuf[13];
        if (Checksum.Checksum_Calc(ref mldb_api.rxbuf, Pkt_length) == 0)
        {
          Log_file.Log(" MLDB Address:{mldb_addr} OPTIONAL COMMAND RESPONSE PACKET: CHECKSUM FAILED");
          num1 = (byte) 9;
        }
        else if (num2 != (byte) 0)
        {
          Log_file.Log(" MLDB Address:{mldb_addr} OPTIONAL COMMAND IS UNSUCCESSFUL");
          num1 = (byte) 0;
        }
        else
        {
          Log_file.Log(" MLDB Address:{mldb_addr} OPTIONAL COMMAND IS SUCCESSFUL");
          num1 = (byte) 1;
        }
      }
      else
      {
        Log_file.Log(" MLDB Address:{mldb_addr} LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST");
        num1 = (byte) 2;
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num3 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    return num1;
  }

  public static byte mldb_send_test_pkt(byte mldb_addr, ref byte[] mldb_pkt, ref byte length)
  {
    int index1 = 0;
    while (index1 < 100)
    {
      mldb_api.pkt_buf[index1] = (byte) 0;
      checked { ++index1; }
    }
    mldb_api.pkt_buf[0] = (byte) 170;
    mldb_api.pkt_buf[1] = (byte) 204;
    mldb_api.pkt_buf[4] = frmMainFormIPIS.ccu_addr;
    mldb_api.pkt_buf[5] = (byte) 0;
    mldb_api.pkt_buf[6] = mldb_addr;
    mldb_api.pkt_buf[7] = (byte) 0;
    mldb_api.pkt_buf[8] = (byte) 0;
    mldb_api.pkt_buf[9] = (byte) 129;
    mldb_api.pkt_buf[10] = (byte) 3;
    mldb_api.pkt_buf[11] = (byte) 0;
    mldb_api.pkt_buf[12] = (byte) 0;
    mldb_api.pkt_buf[13] = (byte) 0;
    ushort num = 14;
    mldb_api.pkt_buf[2] = (byte) 0;
    mldb_api.pkt_buf[3] = (byte) 14;
    ushort length1 = checked ((ushort) ((int) num + 2));
    Checksum.prepare_checksum(ref mldb_api.pkt_buf, length1);
    length = checked ((byte) length1);
    int index2 = 0;
    while (index2 < (int) length1)
    {
      mldb_pkt[index2] = mldb_api.pkt_buf[index2];
      checked { ++index2; }
    }
    if (RS232.Serial_Write(ref mldb_api.pkt_buf, (int) length1) == 1)
      return 1;
    Log_file.Log(string.Format("Write to COM port failed, may be COM port deos't exit. Please check the COM PORT settings"));
    return 3;
  }

  public static byte mldb_send_test_pkt_res(byte mldb_addr, ref byte[] mldb_pkt, ref byte length)
  {
    if (RS232.Serial_Read(ref mldb_api.rxbuf) == (byte) 1)
    {
      ushort Pkt_length = checked ((ushort) unchecked ((int) (ushort) ((uint) (ushort) mldb_api.rxbuf[0] << 8) + (int) mldb_api.rxbuf[1]));
      length = checked ((byte) ((int) Pkt_length + 2));
      uint index = 0;
      mldb_pkt[0] = (byte) 170;
      mldb_pkt[1] = (byte) 204;
      for (; index < (uint) Pkt_length; index = checked ((uint) ((long) index + 1L)))
        mldb_pkt[checked ((int) ((long) index + 2L))] = mldb_api.rxbuf[checked ((int) index)];
      uint num = 0;
      if ((int) mldb_api.rxbuf[2] != (int) mldb_addr & mldb_api.rxbuf[3] != (byte) 0)
      {
        Log_file.Log(" MLDB Address:{mldb_addr} DATA RESPONSE PACKET: IN CORRECT SOURCE ADDRESS");
        num = 1U;
      }
      if (Pkt_length != (ushort) 11)
      {
        Log_file.Log(" MLDB Address:{mldb_addr} DATA RESPONSE PACKET: IN VALID DATA LENGTH");
        num = 1U;
      }
      if (mldb_api.rxbuf[7] != (byte) 193)
      {
        Log_file.Log(" MLDB Address:{mldb_addr} DATA RESPONSE PACKET: IN-VALID RESPONSE PACKET FUNCTION CODE");
        num = 1U;
      }
      switch (mldb_api.rxbuf[8])
      {
        case 0:
          Log_file.Log(" MLDB Address:{mldb_addr} DATA PACKET STATUS: Packet Received and proceed successfully ");
          break;
        case 2:
          Log_file.Log(" MLDB Address:{mldb_addr} DATA PACKET STATUS: CRC FAIL");
          num = 1U;
          break;
        case 6:
          Log_file.Log(" MLDB Address:{mldb_addr} DATA PACKET STATUS: IN VALID FUNCTION CODE");
          num = 1U;
          break;
        case 35:
          Log_file.Log(" MLDB Address:{mldb_addr} DATA PACKET STATUS: IN VALID DATA LENGTH");
          num = 1U;
          break;
      }
      if (Checksum.Checksum_Calc(ref mldb_api.rxbuf, Pkt_length) == 0)
      {
        Log_file.Log(" MLDB Address:{mldb_addr} DATA RESPONSE PACKET: CHECKSUM FAILED ");
        return 9;
      }
      if (num != 0U)
      {
        Log_file.Log(" MLDB Address:{mldb_addr} DATA PACKET IS UNSUCCESSFULL");
        return 0;
      }
      Log_file.Log(" MLDB Address:{mldb_addr} DATA PACKET IS SUCCESSFUL");
      return 1;
    }
    Log_file.Log(" MLDB Address:{mldb_addr} LINK FAILURE or ADDRESSED MLDB DOESN'T EXIST");
    return 2;
  }
}

}