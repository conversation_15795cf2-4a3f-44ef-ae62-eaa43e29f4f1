# A/D Dropdown Implementation

## Overview
This document describes the implementation of the A/D (Arrival/Departure) dropdown functionality in the IPIS system. The changes allow users to specify whether announcement templates are for arrival or departure, and the announcement board filters status options based on the selected A/D value.

## Database Changes

### 1. AnnouncementTemplates Table
- **New Column**: `ArrivalDeparture TEXT NOT NULL DEFAULT 'A'`
- **Purpose**: Stores whether the template is for Arrival ('A') or Departure ('D')
- **Default Value**: 'A' (Arrival)

### 2. Index
- **New Index**: `idx_announcement_templates_ad` on `ArrivalDeparture` column
- **Purpose**: Improves query performance when filtering by A/D

### 3. Default Data Updates
- **Arrival Templates** (A): IS ARRIVING ON, HAS ARRIVED ON, WILL ARRIVE SHORTLY, RUNNING RIGHT TIME, RUNNING LATE, INDEFINITE LATE, CANCELLED, PLATFORM CHANGED, TERMINATED
- **Departure Templates** (D): DEPARTING, WILL DEPART SHORTLY, HAS DEPARTED, DEPARTURE DELAYED

## Code Changes

### 1. Models
**File**: `Models/AnnouncementTemplate.cs`
- Added `ArrivalDeparture` property with default value "A"
- Updated constructor to set default value

### 2. Repositories
**File**: `Repositories/SQLiteAnnouncementTemplateRepository.cs`
- Updated all SQL queries to include `ArrivalDeparture` field
- Added new method: `GetActiveTemplatesByArrivalDepartureAsync(string arrivalDeparture)`
- Updated `AddTemplateAsync` and `UpdateTemplateAsync` methods

**File**: `Repositories/Interfaces/IAnnouncementTemplateRepository.cs`
- Added new method signature: `GetActiveTemplatesByArrivalDepartureAsync`

**File**: `Repositories/SQLiteTrainRepository.cs`
- Updated `GetOnlineTrains()` to include `Train_AD` field
- Updated `AddOnlineTrain()` method signature to include `trainAD` parameter

### 3. Services
**File**: `Services/AnnouncementTemplateService.cs`
- Added new method: `GetActiveTemplatesByArrivalDepartureAsync(string arrivalDeparture)`
- Added validation for A/D values (must be 'A' or 'D')

**File**: `Services/TrainService.cs`
- Updated `AddOnlineTrain()` method signature to include `trainAD` parameter

**File**: `Repositories/Interfaces/ITrainRepository.cs`
- Updated `AddOnlineTrain()` method signature to include `trainAD` parameter

### 4. Forms

#### TemplateForm
**File**: `Forms/Settings/Announcement/TemplateForm.cs`
- Added A/D dropdown (ComboBox) with "A" and "D" options
- Set "A" as default value
- Added validation for A/D selection
- Updated form layout to accommodate new field

#### AnnouncementManagementForm
**File**: `Forms/Settings/Announcement/AnnouncementManagementForm.cs`
- Added "A/D" column to templates DataGridView
- Updated data loading to include ArrivalDeparture field

#### AnnouncementBoardForm
**File**: `Forms/Announcement/AnnouncementBoardForm.cs`
- Modified `LoadDynamicStatusOptions()` to load all templates initially
- Added new method `LoadDynamicStatusOptionsForAD(string arrivalDeparture)` to filter by A/D
- Updated `TrainGrid_CellValueChanged()` to handle A/D column changes
- When A/D value changes, status options are filtered accordingly
- Set default A/D value to "A" when adding new trains

## Database Migration

### Migration Script
**File**: `Database/migrate_add_arrival_departure.sql`
- Adds `ArrivalDeparture` column to existing `AnnouncementTemplates` table
- Creates index for performance
- Updates existing templates with appropriate A/D values
- Inserts additional departure templates

### How to Run Migration
1. Execute the migration script on your existing database
2. The script is safe to run multiple times (uses `ALTER TABLE ADD COLUMN IF NOT EXISTS`)

## Usage Instructions

### 1. Creating Templates
1. Go to Settings > Announcement Management
2. Click "Add Template"
3. Fill in template details
4. Select A/D from dropdown (A for Arrival, D for Departure)
5. Save the template

### 2. Announcement Board
1. Open Announcement Board
2. Add trains to the grid (default A/D is "A")
3. Change A/D value for any train
4. Status dropdown will automatically filter to show only relevant templates
5. Select appropriate status for the A/D type

### 3. Default Behavior
- New templates default to "A" (Arrival)
- New trains in announcement board default to "A" (Arrival)
- Status options are filtered based on selected A/D value

## Benefits

1. **Better Organization**: Templates are clearly categorized as arrival or departure
2. **Improved UX**: Users only see relevant status options based on A/D selection
3. **Reduced Errors**: Prevents selection of inappropriate status for A/D type
4. **Flexibility**: Easy to add new arrival or departure templates
5. **Backward Compatibility**: Existing templates are automatically categorized

## Technical Notes

- All changes are backward compatible
- Default values ensure existing functionality continues to work
- Database migration handles existing data gracefully
- Performance is maintained with proper indexing
- Validation ensures data integrity 