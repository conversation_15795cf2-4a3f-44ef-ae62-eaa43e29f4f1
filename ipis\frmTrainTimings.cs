// Decompiled with JetBrains decompiler
// Type: ipis.frmTrainTimings
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using ipis.My;
using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

namespace ipis
{

[DesignerGenerated]
public class frmTrainTimings : Form
{
  private static List<WeakReference> __ENCList = new List<WeakReference>();
  private IContainer components;
  [AccessedThroughProperty("lblMin")]
  private Label _lblMin;
  [AccessedThroughProperty("btnExit")]
  private Button _btnExit;
  [AccessedThroughProperty("dgvTrainTimings")]
  private DataGridView _dgvTrainTimings;
  [AccessedThroughProperty("btnDisplay")]
  private Button _btnDisplay;
  [AccessedThroughProperty("cmbDTime")]
  private ComboBox _cmbDTime;
  [AccessedThroughProperty("radDTime")]
  private RadioButton _radDTime;
  [AccessedThroughProperty("radFTime")]
  private RadioButton _radFTime;
  [AccessedThroughProperty("cmbTTime")]
  private ComboBox _cmbTTime;
  [AccessedThroughProperty("cmbFTime")]
  private ComboBox _cmbFTime;
  [AccessedThroughProperty("lblTTime")]
  private Label _lblTTime;
  [AccessedThroughProperty("SNo")]
  private DataGridViewTextBoxColumn _SNo;
  [AccessedThroughProperty("train_no")]
  private DataGridViewTextBoxColumn _train_no;
  [AccessedThroughProperty("train_name")]
  private DataGridViewTextBoxColumn _train_name;
  [AccessedThroughProperty("arrival_time")]
  private DataGridViewTextBoxColumn _arrival_time;
  [AccessedThroughProperty("dep_time")]
  private DataGridViewTextBoxColumn _dep_time;
  [AccessedThroughProperty("pfno")]
  private DataGridViewTextBoxColumn _pfno;
  [AccessedThroughProperty("radAuto")]
  private RadioButton _radAuto;
  [AccessedThroughProperty("radMan")]
  private RadioButton _radMan;
  [AccessedThroughProperty("Panel1")]
  private Panel _Panel1;
  [AccessedThroughProperty("PictureBox1")]
  private PictureBox _PictureBox1;
  [AccessedThroughProperty("RadioButton1")]
  private RadioButton _RadioButton1;
  private string time_from;
  private string time_to;
  private string time_dur;
  [AccessedThroughProperty("cgs_main_frm_event")]
  private frm_CgsOnlineForm _cgs_main_frm_event;

  [DebuggerNonUserCode]
  static frmTrainTimings()
  {
  }

  [DebuggerNonUserCode]
  public frmTrainTimings()
  {
    this.Load += new EventHandler(this.frmTrainTimings_Load);
    frmTrainTimings.__ENCAddToList((object) this);
    this.InitializeComponent();
  }

  [DebuggerNonUserCode]
  private static void __ENCAddToList(object value)
  {
    lock (frmTrainTimings.__ENCList)
    {
      if (frmTrainTimings.__ENCList.Count == frmTrainTimings.__ENCList.Capacity)
      {
        int index1 = 0;
        int num = checked (frmTrainTimings.__ENCList.Count - 1);
        int index2 = 0;
        while (index2 <= num)
        {
          if (frmTrainTimings.__ENCList[index2].IsAlive)
          {
            if (index2 != index1)
              frmTrainTimings.__ENCList[index1] = frmTrainTimings.__ENCList[index2];
            checked { ++index1; }
          }
          checked { ++index2; }
        }
        frmTrainTimings.__ENCList.RemoveRange(index1, checked (frmTrainTimings.__ENCList.Count - index1));
        frmTrainTimings.__ENCList.Capacity = frmTrainTimings.__ENCList.Count;
      }
      frmTrainTimings.__ENCList.Add(new WeakReference(RuntimeHelpers.GetObjectValue(value)));
    }
  }

  [DebuggerNonUserCode]
  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  [DebuggerStepThrough]
  private void InitializeComponent()
  {
    DataGridViewCellStyle gridViewCellStyle1 = new DataGridViewCellStyle();
    DataGridViewCellStyle gridViewCellStyle2 = new DataGridViewCellStyle();
    DataGridViewCellStyle gridViewCellStyle3 = new DataGridViewCellStyle();
    DataGridViewCellStyle gridViewCellStyle4 = new DataGridViewCellStyle();
    this.lblMin = new Label();
    this.btnExit = new Button();
    this.dgvTrainTimings = new DataGridView();
    this.SNo = new DataGridViewTextBoxColumn();
    this.train_no = new DataGridViewTextBoxColumn();
    this.train_name = new DataGridViewTextBoxColumn();
    this.arrival_time = new DataGridViewTextBoxColumn();
    this.dep_time = new DataGridViewTextBoxColumn();
    this.pfno = new DataGridViewTextBoxColumn();
    this.btnDisplay = new Button();
    this.cmbDTime = new ComboBox();
    this.radDTime = new RadioButton();
    this.radFTime = new RadioButton();
    this.cmbTTime = new ComboBox();
    this.cmbFTime = new ComboBox();
    this.lblTTime = new Label();
    this.radAuto = new RadioButton();
    this.radMan = new RadioButton();
    this.Panel1 = new Panel();
    this.RadioButton1 = new RadioButton();
    this.PictureBox1 = new PictureBox();
    ((ISupportInitialize) this.dgvTrainTimings).BeginInit();
    this.Panel1.SuspendLayout();
    ((ISupportInitialize) this.PictureBox1).BeginInit();
    this.SuspendLayout();
    this.lblMin.AutoSize = true;
    this.lblMin.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblMin1 = this.lblMin;
    Point point1 = new Point(170, 212);
    Point point2 = point1;
    lblMin1.Location = point2;
    this.lblMin.Name = "lblMin";
    Label lblMin2 = this.lblMin;
    Size size1 = new Size(32 /*0x20*/, 16 /*0x10*/);
    Size size2 = size1;
    lblMin2.Size = size2;
    this.lblMin.TabIndex = 23;
    this.lblMin.Text = "min";
    this.btnExit.BackColor = Color.SeaShell;
    this.btnExit.DialogResult = DialogResult.Cancel;
    this.btnExit.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnExit1 = this.btnExit;
    point1 = new Point(163, 281);
    Point point3 = point1;
    btnExit1.Location = point3;
    this.btnExit.Name = "btnExit";
    Button btnExit2 = this.btnExit;
    size1 = new Size(61, 25);
    Size size3 = size1;
    btnExit2.Size = size3;
    this.btnExit.TabIndex = 7;
    this.btnExit.Text = "Exit";
    this.btnExit.UseVisualStyleBackColor = false;
    this.dgvTrainTimings.AllowDrop = true;
    this.dgvTrainTimings.AllowUserToAddRows = false;
    this.dgvTrainTimings.AllowUserToDeleteRows = false;
    this.dgvTrainTimings.AllowUserToResizeColumns = false;
    this.dgvTrainTimings.AllowUserToResizeRows = false;
    gridViewCellStyle1.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    gridViewCellStyle1.ForeColor = SystemColors.ControlText;
    this.dgvTrainTimings.AlternatingRowsDefaultCellStyle = gridViewCellStyle1;
    this.dgvTrainTimings.BackgroundColor = SystemColors.ControlLightLight;
    gridViewCellStyle2.Alignment = DataGridViewContentAlignment.MiddleCenter;
    gridViewCellStyle2.BackColor = Color.SkyBlue;
    gridViewCellStyle2.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    gridViewCellStyle2.ForeColor = SystemColors.WindowText;
    gridViewCellStyle2.SelectionBackColor = SystemColors.Highlight;
    gridViewCellStyle2.SelectionForeColor = SystemColors.HighlightText;
    gridViewCellStyle2.WrapMode = DataGridViewTriState.True;
    this.dgvTrainTimings.ColumnHeadersDefaultCellStyle = gridViewCellStyle2;
    this.dgvTrainTimings.Columns.AddRange((DataGridViewColumn) this.SNo, (DataGridViewColumn) this.train_no, (DataGridViewColumn) this.train_name, (DataGridViewColumn) this.arrival_time, (DataGridViewColumn) this.dep_time, (DataGridViewColumn) this.pfno);
    gridViewCellStyle3.Alignment = DataGridViewContentAlignment.MiddleLeft;
    gridViewCellStyle3.BackColor = SystemColors.ControlLightLight;
    gridViewCellStyle3.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    gridViewCellStyle3.ForeColor = SystemColors.ControlText;
    gridViewCellStyle3.SelectionBackColor = SystemColors.Highlight;
    gridViewCellStyle3.SelectionForeColor = SystemColors.HighlightText;
    gridViewCellStyle3.WrapMode = DataGridViewTriState.False;
    this.dgvTrainTimings.DefaultCellStyle = gridViewCellStyle3;
    DataGridView dgvTrainTimings1 = this.dgvTrainTimings;
    point1 = new Point(0, -1);
    Point point4 = point1;
    dgvTrainTimings1.Location = point4;
    this.dgvTrainTimings.Name = "dgvTrainTimings";
    this.dgvTrainTimings.ReadOnly = true;
    gridViewCellStyle4.Alignment = DataGridViewContentAlignment.MiddleLeft;
    gridViewCellStyle4.BackColor = SystemColors.Control;
    gridViewCellStyle4.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    gridViewCellStyle4.ForeColor = SystemColors.ControlText;
    gridViewCellStyle4.SelectionBackColor = SystemColors.Highlight;
    gridViewCellStyle4.SelectionForeColor = SystemColors.HighlightText;
    gridViewCellStyle4.WrapMode = DataGridViewTriState.True;
    this.dgvTrainTimings.RowHeadersDefaultCellStyle = gridViewCellStyle4;
    DataGridView dgvTrainTimings2 = this.dgvTrainTimings;
    size1 = new Size(657, 146);
    Size size4 = size1;
    dgvTrainTimings2.Size = size4;
    this.dgvTrainTimings.TabIndex = 21;
    this.SNo.HeaderText = "S No";
    this.SNo.Name = "SNo";
    this.SNo.ReadOnly = true;
    this.SNo.Width = 50;
    this.train_no.HeaderText = "Train No";
    this.train_no.Name = "train_no";
    this.train_no.ReadOnly = true;
    this.train_no.Width = 90;
    this.train_name.HeaderText = "Train Name";
    this.train_name.Name = "train_name";
    this.train_name.ReadOnly = true;
    this.train_name.Width = 200;
    this.arrival_time.HeaderText = "Arrival Time";
    this.arrival_time.Name = "arrival_time";
    this.arrival_time.ReadOnly = true;
    this.dep_time.HeaderText = "Depature Time";
    this.dep_time.Name = "dep_time";
    this.dep_time.ReadOnly = true;
    this.pfno.HeaderText = "Pf No";
    this.pfno.Name = "pfno";
    this.pfno.ReadOnly = true;
    this.pfno.Width = 70;
    this.btnDisplay.BackColor = Color.SeaShell;
    this.btnDisplay.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnDisplay1 = this.btnDisplay;
    point1 = new Point(60, 281);
    Point point5 = point1;
    btnDisplay1.Location = point5;
    this.btnDisplay.Name = "btnDisplay";
    Button btnDisplay2 = this.btnDisplay;
    size1 = new Size(60, 25);
    Size size5 = size1;
    btnDisplay2.Size = size5;
    this.btnDisplay.TabIndex = 6;
    this.btnDisplay.Text = "Ok";
    this.btnDisplay.UseVisualStyleBackColor = false;
    this.cmbDTime.FormattingEnabled = true;
    ComboBox cmbDtime1 = this.cmbDTime;
    point1 = new Point(89, 211);
    Point point6 = point1;
    cmbDtime1.Location = point6;
    this.cmbDTime.Name = "cmbDTime";
    ComboBox cmbDtime2 = this.cmbDTime;
    size1 = new Size(75, 21);
    Size size6 = size1;
    cmbDtime2.Size = size6;
    this.cmbDTime.TabIndex = 5;
    this.radDTime.AutoSize = true;
    this.radDTime.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    RadioButton radDtime1 = this.radDTime;
    point1 = new Point(10, 210);
    Point point7 = point1;
    radDtime1.Location = point7;
    this.radDTime.Name = "radDTime";
    RadioButton radDtime2 = this.radDTime;
    size1 = new Size(57, 20);
    Size size7 = size1;
    radDtime2.Size = size7;
    this.radDTime.TabIndex = 2;
    this.radDTime.TabStop = true;
    this.radDTime.Text = "Next";
    this.radDTime.UseVisualStyleBackColor = true;
    this.radFTime.AutoSize = true;
    this.radFTime.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    RadioButton radFtime1 = this.radFTime;
    point1 = new Point(10, 164);
    Point point8 = point1;
    radFtime1.Location = point8;
    this.radFTime.Name = "radFTime";
    RadioButton radFtime2 = this.radFTime;
    size1 = new Size(61, 20);
    Size size8 = size1;
    radFtime2.Size = size8;
    this.radFTime.TabIndex = 1;
    this.radFTime.TabStop = true;
    this.radFTime.Tag = (object) "";
    this.radFTime.Text = "From";
    this.radFTime.UseVisualStyleBackColor = true;
    this.cmbTTime.FormattingEnabled = true;
    ComboBox cmbTtime1 = this.cmbTTime;
    point1 = new Point(280, 165);
    Point point9 = point1;
    cmbTtime1.Location = point9;
    this.cmbTTime.Name = "cmbTTime";
    ComboBox cmbTtime2 = this.cmbTTime;
    size1 = new Size(70, 21);
    Size size9 = size1;
    cmbTtime2.Size = size9;
    this.cmbTTime.TabIndex = 4;
    this.cmbFTime.FormattingEnabled = true;
    ComboBox cmbFtime1 = this.cmbFTime;
    point1 = new Point(89, 165);
    Point point10 = point1;
    cmbFtime1.Location = point10;
    this.cmbFTime.Name = "cmbFTime";
    ComboBox cmbFtime2 = this.cmbFTime;
    size1 = new Size(75, 21);
    Size size10 = size1;
    cmbFtime2.Size = size10;
    this.cmbFTime.TabIndex = 3;
    this.lblTTime.AutoSize = true;
    this.lblTTime.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblTtime1 = this.lblTTime;
    point1 = new Point(208 /*0xD0*/, 166);
    Point point11 = point1;
    lblTtime1.Location = point11;
    this.lblTTime.Name = "lblTTime";
    Label lblTtime2 = this.lblTTime;
    size1 = new Size(66, 16 /*0x10*/);
    Size size11 = size1;
    lblTtime2.Size = size11;
    this.lblTTime.TabIndex = 14;
    this.lblTTime.Text = "To Time";
    this.radAuto.AutoSize = true;
    RadioButton radAuto1 = this.radAuto;
    point1 = new Point(223, 12);
    Point point12 = point1;
    radAuto1.Location = point12;
    this.radAuto.Name = "radAuto";
    RadioButton radAuto2 = this.radAuto;
    size1 = new Size(118, 17);
    Size size12 = size1;
    radAuto2.Size = size12;
    this.radAuto.TabIndex = 24;
    this.radAuto.Text = "NTES Automatic";
    this.radAuto.UseVisualStyleBackColor = true;
    this.radAuto.Visible = false;
    this.radMan.AutoSize = true;
    RadioButton radMan1 = this.radMan;
    point1 = new Point(71, 12);
    Point point13 = point1;
    radMan1.Location = point13;
    this.radMan.Name = "radMan";
    RadioButton radMan2 = this.radMan;
    size1 = new Size(149, 17);
    Size size13 = size1;
    radMan2.Size = size13;
    this.radMan.TabIndex = 25;
    this.radMan.Text = "NTES Semi Automatic";
    this.radMan.UseVisualStyleBackColor = true;
    this.radMan.Visible = false;
    this.Panel1.Controls.Add((Control) this.RadioButton1);
    this.Panel1.Controls.Add((Control) this.radAuto);
    this.Panel1.Controls.Add((Control) this.radMan);
    Panel panel1_1 = this.Panel1;
    point1 = new Point(0, 237);
    Point point14 = point1;
    panel1_1.Location = point14;
    this.Panel1.Name = "Panel1";
    Panel panel1_2 = this.Panel1;
    size1 = new Size(347, 38);
    Size size14 = size1;
    panel1_2.Size = size14;
    this.Panel1.TabIndex = 26;
    this.RadioButton1.AutoSize = true;
    this.RadioButton1.Checked = true;
    RadioButton radioButton1_1 = this.RadioButton1;
    point1 = new Point(4, 12);
    Point point15 = point1;
    radioButton1_1.Location = point15;
    this.RadioButton1.Name = "RadioButton1";
    RadioButton radioButton1_2 = this.RadioButton1;
    size1 = new Size(66, 17);
    Size size15 = size1;
    radioButton1_2.Size = size15;
    this.RadioButton1.TabIndex = 26;
    this.RadioButton1.TabStop = true;
    this.RadioButton1.Text = "Manual";
    this.RadioButton1.UseVisualStyleBackColor = true;
    this.PictureBox1.Image = (Image) ipis.My.Resources.Resources.nmc;
    PictureBox pictureBox1_1 = this.PictureBox1;
    point1 = new Point(353, 164);
    Point point16 = point1;
    pictureBox1_1.Location = point16;
    this.PictureBox1.Name = "PictureBox1";
    PictureBox pictureBox1_2 = this.PictureBox1;
    size1 = new Size(337, 161);
    Size size16 = size1;
    pictureBox1_2.Size = size16;
    this.PictureBox1.TabIndex = 27;
    this.PictureBox1.TabStop = false;
    this.AcceptButton = (IButtonControl) this.btnDisplay;
    this.AutoScaleDimensions = new SizeF(7f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.BackColor = Color.PowderBlue;
    this.CancelButton = (IButtonControl) this.btnExit;
    size1 = new Size(680, 321);
    this.ClientSize = size1;
    this.Controls.Add((Control) this.PictureBox1);
    this.Controls.Add((Control) this.Panel1);
    this.Controls.Add((Control) this.lblMin);
    this.Controls.Add((Control) this.btnExit);
    this.Controls.Add((Control) this.dgvTrainTimings);
    this.Controls.Add((Control) this.btnDisplay);
    this.Controls.Add((Control) this.cmbDTime);
    this.Controls.Add((Control) this.radDTime);
    this.Controls.Add((Control) this.radFTime);
    this.Controls.Add((Control) this.cmbTTime);
    this.Controls.Add((Control) this.cmbFTime);
    this.Controls.Add((Control) this.lblTTime);
    this.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.FormBorderStyle = FormBorderStyle.FixedToolWindow;
    this.Name = "frmTrainTimings";
    this.Text = "TrainTimings(Powered by Ninja Media Creations)";
    ((ISupportInitialize) this.dgvTrainTimings).EndInit();
    this.Panel1.ResumeLayout(false);
    this.Panel1.PerformLayout();
    ((ISupportInitialize) this.PictureBox1).EndInit();
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  internal virtual Label lblMin
  {
    [DebuggerNonUserCode] get { return this._lblMin; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._lblMin = value; }
  }

  internal virtual Button btnExit
  {
    [DebuggerNonUserCode] get { return this._btnExit; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnExit_Click);
      if (this._btnExit != null)
        this._btnExit.Click -= eventHandler;
      this._btnExit = value;
      if (this._btnExit == null)
        return;
      this._btnExit.Click += eventHandler;
    }
  }

  internal virtual DataGridView dgvTrainTimings
  {
    [DebuggerNonUserCode] get { return this._dgvTrainTimings; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._dgvTrainTimings = value;
    }
  }

  internal virtual Button btnDisplay
  {
    [DebuggerNonUserCode] get { return this._btnDisplay; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnDisplay_Click);
      if (this._btnDisplay != null)
        this._btnDisplay.Click -= eventHandler;
      this._btnDisplay = value;
      if (this._btnDisplay == null)
        return;
      this._btnDisplay.Click += eventHandler;
    }
  }

  internal virtual ComboBox cmbDTime
  {
    [DebuggerNonUserCode] get { return this._cmbDTime; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbDTime = value; }
  }

  internal virtual RadioButton radDTime
  {
    [DebuggerNonUserCode] get { return this._radDTime; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.radDTime_CheckedChanged);
      if (this._radDTime != null)
        this._radDTime.CheckedChanged -= eventHandler;
      this._radDTime = value;
      if (this._radDTime == null)
        return;
      this._radDTime.CheckedChanged += eventHandler;
    }
  }

  internal virtual RadioButton radFTime
  {
    [DebuggerNonUserCode] get { return this._radFTime; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.radFTime_CheckedChanged);
      if (this._radFTime != null)
        this._radFTime.CheckedChanged -= eventHandler;
      this._radFTime = value;
      if (this._radFTime == null)
        return;
      this._radFTime.CheckedChanged += eventHandler;
    }
  }

  internal virtual ComboBox cmbTTime
  {
    [DebuggerNonUserCode] get { return this._cmbTTime; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbTTime = value; }
  }

  internal virtual ComboBox cmbFTime
  {
    [DebuggerNonUserCode] get { return this._cmbFTime; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbFTime = value; }
  }

  internal virtual Label lblTTime
  {
    [DebuggerNonUserCode] get { return this._lblTTime; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._lblTTime = value; }
  }

  internal virtual DataGridViewTextBoxColumn SNo
  {
    [DebuggerNonUserCode] get { return this._SNo; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._SNo = value; }
  }

  internal virtual DataGridViewTextBoxColumn train_no
  {
    [DebuggerNonUserCode] get { return this._train_no; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._train_no = value; }
  }

  internal virtual DataGridViewTextBoxColumn train_name
  {
    [DebuggerNonUserCode] get { return this._train_name; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._train_name = value;
    }
  }

  internal virtual DataGridViewTextBoxColumn arrival_time
  {
    [DebuggerNonUserCode] get { return this._arrival_time; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._arrival_time = value;
    }
  }

  internal virtual DataGridViewTextBoxColumn dep_time
  {
    [DebuggerNonUserCode] get { return this._dep_time; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._dep_time = value; }
  }

  internal virtual DataGridViewTextBoxColumn pfno
  {
    [DebuggerNonUserCode] get { return this._pfno; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._pfno = value; }
  }

  internal virtual RadioButton radAuto
  {
    [DebuggerNonUserCode] get { return this._radAuto; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._radAuto = value; }
  }

  internal virtual RadioButton radMan
  {
    [DebuggerNonUserCode] get { return this._radMan; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.radMan_CheckedChanged);
      if (this._radMan != null)
        this._radMan.CheckedChanged -= eventHandler;
      this._radMan = value;
      if (this._radMan == null)
        return;
      this._radMan.CheckedChanged += eventHandler;
    }
  }

  internal virtual Panel Panel1
  {
    [DebuggerNonUserCode] get { return this._Panel1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Panel1 = value; }
  }

  internal virtual PictureBox PictureBox1
  {
    [DebuggerNonUserCode] get { return this._PictureBox1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._PictureBox1 = value;
    }
  }

  internal virtual RadioButton RadioButton1
  {
    [DebuggerNonUserCode] get { return this._RadioButton1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._RadioButton1 = value;
    }
  }

  protected virtual frm_CgsOnlineForm cgs_main_frm_event
  {
    [DebuggerNonUserCode] get { return this._cgs_main_frm_event; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._cgs_main_frm_event = value;
    }
  }

  private void frmTrainTimings_Load(object sender, EventArgs e)
  {
    int num1 = 0;
    int num2 = 0;
    int index1 = 0;
    string empty = string.Empty;
    try
    {
      while (num1 < 24)
      {
        int num3 = 0;
        while (num3 < 60)
        {
          DateTime Expression = DateAndTime.TimeValue("{num1.ToString()}:{num3.ToString()}:{num2.ToString()}");
          this.cmbFTime.Items.Insert(index1, (object) Strings.Format((object) Expression, "HH:mm"));
          this.cmbTTime.Items.Insert(index1, (object) Strings.Format((object) Expression, "HH:mm"));
          checked { ++num3; }
          checked { ++index1; }
        }
        checked { ++num1; }
      }
      int index2 = 0;
      int num4 = 5;
      int num5 = 5;
      while (num4 <= 180)
      {
        num4 = checked (num5 * index2 + 1);
        this.cmbDTime.Items.Insert(index2, (object) num4);
        checked { ++index2; }
      }
      network_db_read.get_train_timings(ref this.time_from, ref this.time_to, ref this.time_dur, ref empty);
      if (Operators.CompareString(empty, "from", false) == 0)
      {
        this.cmbFTime.Text = this.time_from;
        this.cmbTTime.Text = this.time_to;
        this.radFTime.Checked = true;
      }
      else
      {
        this.cmbDTime.Text = this.time_dur;
        this.radDTime.Checked = true;
      }
      int index3 = 0;
      this.dgvTrainTimings.Rows.Clear();
      while (index3 < frmMainFormIPIS.online_train_cnt)
      {
        this.dgvTrainTimings.Rows.Add((object) checked (index3 + 1), (object) frmMainFormIPIS.online_train_data[index3].train_no, (object) frmMainFormIPIS.online_train_data[index3].train_name, (object) frmMainFormIPIS.online_train_data[index3].sch_arr_time, (object) frmMainFormIPIS.online_train_data[index3].sch_dep_time, (object) frmMainFormIPIS.online_train_data[index3].pfno);
        checked { ++index3; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num6 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void btnDisplay_Click(object sender, EventArgs e)
  {
    string flag = string.Empty;
    try
    {
      if (this.radAuto.Checked)
        network_db_read.set_Automatic_details("Automatic");
      else if (this.radMan.Checked)
        network_db_read.set_Automatic_details("Manual");
      else if (this.RadioButton1.Checked)
        network_db_read.set_Automatic_details("NON-NTES");
      if (!this.radAuto.Checked & !this.radMan.Checked & !this.RadioButton1.Checked)
      {
        int num = (int) MessageBox.Show("Please Select Announcement Type");
      }
      if (this.radFTime.Checked)
      {
        flag = "from";
        if (Operators.CompareString(this.cmbFTime.Text, "", false) == 0 & Operators.CompareString(this.cmbTTime.Text, "", false) == 0)
        {
          flag = "next";
          this.time_dur = "200";
        }
        else
        {
          this.time_from = this.cmbFTime.Text;
          this.time_to = this.cmbTTime.Text;
          this.time_dur = "0";
        }
      }
      else if (this.radDTime.Checked)
      {
        flag = "next";
        if (Operators.CompareString(this.cmbDTime.Text, "", false) == 0)
        {
          this.time_dur = "200";
          this.time_from = "00:00";
          this.time_to = "00:00";
        }
        else
        {
          this.time_dur = this.cmbDTime.Text;
          this.time_from = "00:00";
          this.time_to = "00:00";
        }
      }
      network_db_read.update_train_timings(this.time_from, this.time_to, this.time_dur, flag);
      if (!frmMainFormIPIS.online_train_status)
      {
        frmMainFormIPIS.online_train_status = true;
        online_trains.next_online_train_details();
        frmMainFormIPIS.online_train_status = false;
      }
      int index = 0;
      this.dgvTrainTimings.Rows.Clear();
      while (index < frmMainFormIPIS.online_train_cnt)
      {
        this.dgvTrainTimings.Rows.Add((object) checked (index + 1), (object) frmMainFormIPIS.online_train_data[index].train_no, (object) frmMainFormIPIS.online_train_data[index].train_name, (object) frmMainFormIPIS.online_train_data[index].sch_arr_time, (object) frmMainFormIPIS.online_train_data[index].sch_dep_time, (object) frmMainFormIPIS.online_train_data[index].pfno);
        checked { ++index; }
      }
      this.Close();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    try
    {
      string str = "Z:\\Database\\StationDetails.mdb";
      string sourceFileName = "C:\\IPIS\\Database\\StationDetails.mdb";
      if (!File.Exists(str))
        File.Create(str);
      bool overwrite = true;
      MyProject.Computer.FileSystem.CopyFile(sourceFileName, str, overwrite);
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
  }

  private void sort_dgv()
  {
    int index = 0;
    Strings.Format((object) DateAndTime.TimeOfDay, "HH:mm");
    try
    {
      while (index < online_trains.temp_cnt)
      {
        int num = index;
        while (num < checked (online_trains.temp_cnt - 1))
        {
          if (Operators.CompareString(frmMainFormIPIS.online_train_data[index].exp_arr_time, frmMainFormIPIS.online_train_data[index].exp_dep_time, false) <= 0)
          {
            if (Operators.CompareString(frmMainFormIPIS.online_train_data[index].exp_arr_time, frmMainFormIPIS.online_train_data[checked (num + 1)].exp_arr_time, false) <= 0 | Operators.CompareString(frmMainFormIPIS.online_train_data[index].exp_arr_time, frmMainFormIPIS.online_train_data[checked (num + 1)].exp_dep_time, false) <= 0)
            {
              checked { ++num; }
            }
            else
            {
              frmMainFormIPIS.online_train_details onlineTrainDetails = frmMainFormIPIS.online_train_data[index];
              frmMainFormIPIS.online_train_data[index] = frmMainFormIPIS.online_train_data[checked (num + 1)];
              frmMainFormIPIS.online_train_data[checked (num + 1)] = onlineTrainDetails;
              checked { ++num; }
            }
          }
          else if (Operators.CompareString(frmMainFormIPIS.online_train_data[index].exp_dep_time, frmMainFormIPIS.online_train_data[checked (num + 1)].exp_dep_time, false) <= 0 | Operators.CompareString(frmMainFormIPIS.online_train_data[index].exp_dep_time, frmMainFormIPIS.online_train_data[checked (num + 1)].exp_arr_time, false) <= 0)
          {
            checked { ++num; }
          }
          else
          {
            frmMainFormIPIS.online_train_details onlineTrainDetails = frmMainFormIPIS.online_train_data[index];
            frmMainFormIPIS.online_train_data[index] = frmMainFormIPIS.online_train_data[checked (num + 1)];
            frmMainFormIPIS.online_train_data[checked (num + 1)] = onlineTrainDetails;
            checked { ++num; }
          }
        }
        checked { ++index; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void btnExit_Click(object sender, EventArgs e)
{
  this.Close();
}

  private void radFTime_CheckedChanged(object sender, EventArgs e)
  {
    this.cmbFTime.Enabled = true;
    this.lblTTime.Enabled = true;
    this.cmbTTime.Enabled = true;
    this.cmbDTime.Enabled = false;
    this.lblMin.Enabled = false;
  }

  private void radDTime_CheckedChanged(object sender, EventArgs e)
  {
    this.cmbFTime.Enabled = false;
    this.lblTTime.Enabled = false;
    this.cmbTTime.Enabled = false;
    this.cmbDTime.Enabled = true;
    this.lblMin.Enabled = true;
  }

  private void radMan_CheckedChanged(object sender, EventArgs e)
  {
  }
}

}