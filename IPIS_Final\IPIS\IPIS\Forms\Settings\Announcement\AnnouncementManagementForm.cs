using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Media;
using System.Threading.Tasks;
using System.Windows.Forms;
using IPIS.Models;
using IPIS.Repositories;
using IPIS.Services;
using IPIS.Forms.Announcement;

namespace IPIS.Forms.Settings.Announcement
{
    public partial class AnnouncementManagementControl : UserControl
    {
        private readonly AnnouncementTemplateService _templateService;
        private readonly LanguageService _languageService;
        private readonly SQLiteAnnouncementSequenceRepository _sequenceRepository;
        private readonly SQLiteSequenceItemRepository _itemRepository;

        // Controls
        private TabControl tabControl;
        private TabPage templatesTab;
        private TabPage sequencesTab;
        private StatusStrip statusStrip;
        private ToolStripStatusLabel statusLabel;

        // Template Tab Controls
        private DataGridView dgvTemplates;
        private Button btnAddTemplate;
        private Button btnEditTemplate;
        private Button btnDeleteTemplate;
        private Button btnToggleTemplateStatus;
        private Button btnRefreshTemplates;

        // Sequence Tab Controls
        private ComboBox cboTemplates;
        private ComboBox cboLanguages;
        private DataGridView dgvSequences;
        private Button btnAddSequence;
        private Button btnEditSequence;
        private Button btnDeleteSequence;
        private Button btnManageSequence;
        private Button btnRefreshSequences;

        public AnnouncementManagementControl()
        {
            _templateService = new AnnouncementTemplateService(new SQLiteAnnouncementTemplateRepository());
            _languageService = new LanguageService(new SQLiteLanguageRepository());
            _sequenceRepository = new SQLiteAnnouncementSequenceRepository();
            _itemRepository = new SQLiteSequenceItemRepository();

            InitializeComponent();
            LoadDataAsync();
        }

        private void InitializeComponent()
        {
            this.tabControl = new TabControl();
            this.templatesTab = new TabPage();
            this.sequencesTab = new TabPage();
            this.statusStrip = new StatusStrip();
            this.statusLabel = new ToolStripStatusLabel();

            // Form
            this.ClientSize = new Size(1000, 700);
            this.Name = "AnnouncementManagementControl";
            this.Text = "Announcement Management";
            this.Dock = DockStyle.Fill;

            // TabControl
            this.tabControl.Dock = DockStyle.Fill;
            this.tabControl.Name = "tabControl";
            this.tabControl.SelectedIndexChanged += new EventHandler(this.tabControl_SelectedIndexChanged);

            // Templates Tab
            this.templatesTab.Text = "Templates";
            this.templatesTab.UseVisualStyleBackColor = true;
            this.InitializeTemplatesTab();

            // Sequences Tab
            this.sequencesTab.Text = "Sequences";
            this.sequencesTab.UseVisualStyleBackColor = true;
            this.InitializeSequencesTab();

            // Add tabs to TabControl
            this.tabControl.Controls.AddRange(new Control[] {
                this.sequencesTab,
                this.templatesTab
            });

            // StatusStrip
            this.statusStrip.Items.AddRange(new ToolStripItem[] {
                this.statusLabel
            });
            this.statusStrip.Location = new Point(0, 678);
            this.statusStrip.Name = "statusStrip";
            this.statusStrip.Size = new Size(1000, 22);
            this.statusStrip.TabIndex = 1;

            // Status Label
            this.statusLabel.Name = "statusLabel";
            this.statusLabel.Text = "Ready";

            // Add controls to form
            this.Controls.AddRange(new Control[] {
                this.tabControl,
                this.statusStrip
            });
        }

        private void InitializeTemplatesTab()
        {
            // Create a panel to contain all controls
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20)
            };

            // Title Label
            var lblTemplates = new Label
            {
                AutoSize = true,
                Location = new Point(0, 0),
                Text = "Announcement Templates",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.FromArgb(64, 64, 64)
            };

            // Create a panel for the DataGridView
            var gridPanel = new Panel
            {
                Location = new Point(0, 40),
                Size = new Size(mainPanel.Width - 40, mainPanel.Height - 120),
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Bottom
            };

            // DataGridView
            this.dgvTemplates = new DataGridView
            {
                Dock = DockStyle.Fill,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D,
                GridColor = Color.LightGray,
                RowHeadersVisible = false,
                Font = new Font("Segoe UI", 9F, FontStyle.Regular)
            };

            // Configure DataGridView columns
            this.dgvTemplates.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn { Name = "Id", HeaderText = "ID", Width = 50, Visible = false },
                new DataGridViewTextBoxColumn { Name = "Name", HeaderText = "Template Name", Width = 200 },
                new DataGridViewTextBoxColumn { Name = "Description", HeaderText = "Description", Width = 250 },
                new DataGridViewTextBoxColumn { Name = "ArrivalDeparture", HeaderText = "A/D", Width = 60 },
                new DataGridViewCheckBoxColumn { Name = "IsActive", HeaderText = "Active", Width = 80 },
                new DataGridViewTextBoxColumn { Name = "CreatedAt", HeaderText = "Created", Width = 150 },
                new DataGridViewTextBoxColumn { Name = "UpdatedAt", HeaderText = "Updated", Width = 150 }
            });

            // Create a panel for buttons
            var buttonPanel = new Panel
            {
                Location = new Point(0, gridPanel.Bottom + 20),
                Size = new Size(mainPanel.Width - 40, 50),
                Anchor = AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Bottom
            };

            // Buttons
            this.btnAddTemplate = new Button
            {
                Location = new Point(0, 0),
                Size = new Size(120, 35),
                Text = "Add Template",
                Font = new Font("Segoe UI", 9F, FontStyle.Regular),
                BackColor = Color.FromArgb(0, 122, 204),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            this.btnAddTemplate.Click += new EventHandler(this.btnAddTemplate_Click);

            this.btnEditTemplate = new Button
            {
                Location = new Point(140, 0),
                Size = new Size(120, 35),
                Text = "Edit Template",
                Font = new Font("Segoe UI", 9F, FontStyle.Regular),
                BackColor = Color.FromArgb(255, 193, 7),
                ForeColor = Color.Black,
                FlatStyle = FlatStyle.Flat
            };
            this.btnEditTemplate.Click += new EventHandler(this.btnEditTemplate_Click);

            this.btnDeleteTemplate = new Button
            {
                Location = new Point(280, 0),
                Size = new Size(120, 35),
                Text = "Delete Template",
                Font = new Font("Segoe UI", 9F, FontStyle.Regular),
                BackColor = Color.FromArgb(220, 53, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            this.btnDeleteTemplate.Click += new EventHandler(this.btnDeleteTemplate_Click);

            this.btnToggleTemplateStatus = new Button
            {
                Location = new Point(420, 0),
                Size = new Size(120, 35),
                Text = "Toggle Status",
                Font = new Font("Segoe UI", 9F, FontStyle.Regular),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            this.btnToggleTemplateStatus.Click += new EventHandler(this.btnToggleTemplateStatus_Click);

            this.btnRefreshTemplates = new Button
            {
                Location = new Point(buttonPanel.Width - 120, 0),
                Size = new Size(120, 35),
                Text = "Refresh",
                Font = new Font("Segoe UI", 9F, FontStyle.Regular),
                BackColor = Color.FromArgb(23, 162, 184),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Anchor = AnchorStyles.Right
            };
            this.btnRefreshTemplates.Click += new EventHandler(this.btnRefreshTemplates_Click);

            // Add controls to panels
            gridPanel.Controls.Add(this.dgvTemplates);
            
            buttonPanel.Controls.AddRange(new Control[]
            {
                this.btnAddTemplate,
                this.btnEditTemplate,
                this.btnDeleteTemplate,
                this.btnToggleTemplateStatus,
                this.btnRefreshTemplates
            });

            // Add controls to main panel
            mainPanel.Controls.AddRange(new Control[]
            {
                lblTemplates,
                gridPanel,
                buttonPanel
            });

            // Add main panel to templates tab
            this.templatesTab.Controls.Add(mainPanel);
        }

        private void InitializeSequencesTab()
        {
            // Main container panel (horizontal split)
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill
            };

            // Left panel (filters + grid)
            var leftPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20, 20, 10, 20)
            };

            // Right sidebar panel (buttons)
            var rightPanel = new Panel
            {
                Dock = DockStyle.Right,
                Width = 180,
                Padding = new Padding(10, 20, 20, 20)
            };

            // Title Label
            var lblSequences = new Label
            {
                AutoSize = true,
                Location = new Point(0, 0),
                Text = "Announcement Sequences",
                Font = new Font("Segoe UI", 12F, FontStyle.Bold),
                ForeColor = Color.FromArgb(64, 64, 64)
            };

            // Template ComboBox Label
            var lblTemplate = new Label
            {
                AutoSize = true,
                Location = new Point(0, 40),
                Text = "Template:",
                Font = new Font("Segoe UI", 9F, FontStyle.Regular)
            };

            // Template ComboBox
            this.cboTemplates = new ComboBox
            {
                Location = new Point(70, 37),
                Size = new Size(180, 25),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Segoe UI", 9F, FontStyle.Regular)
            };
            this.cboTemplates.SelectedIndexChanged += new EventHandler(this.cboTemplates_SelectedIndexChanged);

            // Language ComboBox Label
            var lblLanguage = new Label
            {
                AutoSize = true,
                Location = new Point(270, 40),
                Text = "Language:",
                Font = new Font("Segoe UI", 9F, FontStyle.Regular)
            };

            // Language ComboBox
            this.cboLanguages = new ComboBox
            {
                Location = new Point(340, 37),
                Size = new Size(180, 25),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Segoe UI", 9F, FontStyle.Regular)
            };
            this.cboLanguages.SelectedIndexChanged += new EventHandler(this.cboLanguages_SelectedIndexChanged);

            // DataGridView
            this.dgvSequences = new DataGridView
            {
                Location = new Point(0, 75),
                Size = new Size(1, 1), // Will be docked
                Dock = DockStyle.Fill,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D,
                GridColor = Color.LightGray,
                RowHeadersVisible = false,
                Font = new Font("Segoe UI", 9F, FontStyle.Regular)
            };

            // Configure DataGridView columns
            this.dgvSequences.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn { Name = "Id", HeaderText = "ID", Width = 50, Visible = false },
                new DataGridViewTextBoxColumn { Name = "TemplateName", HeaderText = "Template", Width = 150 },
                new DataGridViewTextBoxColumn { Name = "LanguageName", HeaderText = "Language", Width = 100 },
                new DataGridViewTextBoxColumn { Name = "Name", HeaderText = "Sequence Name", Width = 200 },
                new DataGridViewCheckBoxColumn { Name = "IsActive", HeaderText = "Active", Width = 80 },
                new DataGridViewTextBoxColumn { Name = "CreatedAt", HeaderText = "Created", Width = 150 },
                new DataGridViewTextBoxColumn { Name = "UpdatedAt", HeaderText = "Updated", Width = 150 }
            });

            // Add filter controls to a filterPanel
            var filterPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 70
            };
            filterPanel.Controls.AddRange(new Control[]
            {
                lblSequences,
                lblTemplate,
                this.cboTemplates,
                lblLanguage,
                this.cboLanguages
            });

            // Arrange filter controls horizontally
            lblSequences.Location = new Point(0, 0);
            lblTemplate.Location = new Point(0, 40);
            this.cboTemplates.Location = new Point(70, 37);
            lblLanguage.Location = new Point(270, 40);
            this.cboLanguages.Location = new Point(340, 37);

            // Add filterPanel and grid to leftPanel
            leftPanel.Controls.Add(filterPanel);
            leftPanel.Controls.Add(this.dgvSequences);
            this.dgvSequences.BringToFront();

            // Buttons (vertical stack in rightPanel)
            int btnWidth = 140, btnHeight = 40, btnSpacing = 15, btnTop = 0;
            this.btnAddSequence = new Button
            {
                Size = new Size(btnWidth, btnHeight),
                Text = "Add Sequence",
                Font = new Font("Segoe UI", 9F, FontStyle.Regular),
                BackColor = Color.FromArgb(0, 122, 204),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Location = new Point(10, btnTop)
            };
            this.btnAddSequence.Click += new EventHandler(this.btnAddSequence_Click);
            btnTop += btnHeight + btnSpacing;

            this.btnEditSequence = new Button
            {
                Size = new Size(btnWidth, btnHeight),
                Text = "Edit Sequence",
                Font = new Font("Segoe UI", 9F, FontStyle.Regular),
                BackColor = Color.FromArgb(255, 193, 7),
                ForeColor = Color.Black,
                FlatStyle = FlatStyle.Flat,
                Location = new Point(10, btnTop)
            };
            this.btnEditSequence.Click += new EventHandler(this.btnEditSequence_Click);
            btnTop += btnHeight + btnSpacing;

            this.btnDeleteSequence = new Button
            {
                Size = new Size(btnWidth, btnHeight),
                Text = "Delete",
                Font = new Font("Segoe UI", 9F, FontStyle.Regular),
                BackColor = Color.FromArgb(220, 53, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Location = new Point(10, btnTop)
            };
            this.btnDeleteSequence.Click += new EventHandler(this.btnDeleteSequence_Click);
            btnTop += btnHeight + btnSpacing;

            this.btnManageSequence = new Button
            {
                Size = new Size(btnWidth, btnHeight),
                Text = "Manage Items",
                Font = new Font("Segoe UI", 9F, FontStyle.Regular),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Location = new Point(10, btnTop)
            };
            this.btnManageSequence.Click += new EventHandler(this.btnManageSequence_Click);
            btnTop += btnHeight + btnSpacing;

            this.btnRefreshSequences = new Button
            {
                Size = new Size(btnWidth, btnHeight),
                Text = "Refresh",
                Font = new Font("Segoe UI", 9F, FontStyle.Regular),
                BackColor = Color.FromArgb(23, 162, 184),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Location = new Point(10, btnTop)
            };
            this.btnRefreshSequences.Click += new EventHandler(this.btnRefreshSequences_Click);

            rightPanel.Controls.AddRange(new Control[]
            {
                this.btnAddSequence,
                this.btnEditSequence,
                this.btnDeleteSequence,
                this.btnManageSequence,
                this.btnRefreshSequences
            });

            // Add left and right panels to mainPanel
            mainPanel.Controls.Add(leftPanel);
            mainPanel.Controls.Add(rightPanel);

            // Add mainPanel to sequencesTab
            this.sequencesTab.Controls.Add(mainPanel);
        }

        private async void LoadDataAsync()
        {
            try
            {
                statusLabel.Text = "Loading data...";
                await LoadTemplatesAsync();
                await LoadLanguagesAsync();
                await LoadSequencesAsync();
                statusLabel.Text = "Ready";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading data: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                statusLabel.Text = "Error loading data";
            }
        }

        private async Task LoadTemplatesAsync()
        {
            var templates = await _templateService.GetAllTemplatesAsync();
            
            dgvTemplates.Rows.Clear();
            foreach (var template in templates)
            {
                dgvTemplates.Rows.Add(
                    template.Id,
                    template.Name,
                    template.Description,
                    template.ArrivalDeparture,
                    template.IsActive,
                    template.CreatedAt.ToString("yyyy-MM-dd HH:mm"),
                    template.UpdatedAt?.ToString("yyyy-MM-dd HH:mm") ?? "-"
                );
            }

            // Update combo boxes
            cboTemplates.Items.Clear();
            cboTemplates.Items.Add(new ComboBoxItem { Id = 0, Text = "All Templates" });
            foreach (var template in templates.Where(t => t.IsActive))
            {
                cboTemplates.Items.Add(new ComboBoxItem { Id = template.Id, Text = template.Name });
            }
            if (cboTemplates.Items.Count > 0)
                cboTemplates.SelectedIndex = 0;
        }

        private async Task LoadLanguagesAsync()
        {
            var languages = await _languageService.GetAllLanguagesAsync();
            
            cboLanguages.Items.Clear();
            cboLanguages.Items.Add(new ComboBoxItem { Id = 0, Text = "All Languages" });
            foreach (var language in languages.Where(l => l.IsActive))
            {
                cboLanguages.Items.Add(new ComboBoxItem { Id = language.Id, Text = language.Name });
            }
            if (cboLanguages.Items.Count > 0)
                cboLanguages.SelectedIndex = 0;
        }

        private async Task LoadSequencesAsync()
        {
            try
            {
                var selectedTemplate = cboTemplates.SelectedItem as ComboBoxItem;
                var selectedLanguage = cboLanguages.SelectedItem as ComboBoxItem;

                var sequences = await _sequenceRepository.GetAllSequencesAsync();
                
                // Apply filters
                if (selectedTemplate?.Id > 0)
                {
                    sequences = sequences.Where(s => s.TemplateId == selectedTemplate.Id);
                }
                
                if (selectedLanguage?.Id > 0)
                {
                    sequences = sequences.Where(s => s.LanguageId == selectedLanguage.Id);
                }
                
                dgvSequences.Rows.Clear();
                foreach (var sequence in sequences)
                {
                    dgvSequences.Rows.Add(
                        sequence.Id,
                        sequence.Template?.Name ?? "-",
                        sequence.Language?.Name ?? "-",
                        sequence.Name,
                        sequence.IsActive,
                        sequence.CreatedAt.ToString("yyyy-MM-dd HH:mm"),
                        sequence.UpdatedAt?.ToString("yyyy-MM-dd HH:mm") ?? "-"
                    );
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading sequences: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // Event Handlers
        private void tabControl_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (tabControl.SelectedTab == templatesTab)
            {
                LoadTemplatesAsync();
            }
            else if (tabControl.SelectedTab == sequencesTab)
            {
                LoadSequencesAsync();
            }
        }

        private void cboTemplates_SelectedIndexChanged(object sender, EventArgs e)
        {
            LoadSequencesAsync();
        }

        private void cboLanguages_SelectedIndexChanged(object sender, EventArgs e)
        {
            LoadSequencesAsync();
        }

        private void btnAddTemplate_Click(object sender, EventArgs e)
        {
            using (var form = new TemplateForm())
            {
                if (form.ShowDialog() == DialogResult.OK)
                {
                    LoadTemplatesAsync();
                    statusLabel.Text = "Template added successfully";
                }
            }
        }

        private async void btnEditTemplate_Click(object sender, EventArgs e)
        {
            if (dgvTemplates.SelectedRows.Count == 0)
            {
                MessageBox.Show("Please select a template to edit.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            var selectedRow = dgvTemplates.SelectedRows[0];
            var templateId = Convert.ToInt32(selectedRow.Cells["Id"].Value);

            try
            {
                var template = await _templateService.GetTemplateByIdAsync(templateId);
                if (template == null)
                {
                    MessageBox.Show("Template not found.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                using (var form = new TemplateForm(template))
                {
                    if (form.ShowDialog() == DialogResult.OK)
                    {
                        LoadTemplatesAsync();
                        statusLabel.Text = "Template updated successfully";
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading template: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void btnDeleteTemplate_Click(object sender, EventArgs e)
        {
            if (dgvTemplates.SelectedRows.Count == 0)
            {
                MessageBox.Show("Please select a template to delete.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            var selectedRow = dgvTemplates.SelectedRows[0];
            var templateId = Convert.ToInt32(selectedRow.Cells["Id"].Value);
            var templateName = selectedRow.Cells["Name"].Value.ToString();

            var result = MessageBox.Show(
                $"Are you sure you want to delete the template '{templateName}'?\n\nThis action cannot be undone.",
                "Confirm Delete",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Warning);

            if (result == DialogResult.Yes)
            {
                try
                {
                    statusLabel.Text = "Deleting template...";
                    var success = await _templateService.DeleteTemplateAsync(templateId);
                    
                    if (success)
                    {
                        LoadTemplatesAsync();
                        statusLabel.Text = "Template deleted successfully";
                    }
                    else
                    {
                        MessageBox.Show("Failed to delete template.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        statusLabel.Text = "Failed to delete template";
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error deleting template: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    statusLabel.Text = "Error deleting template";
                }
            }
        }

        private async void btnToggleTemplateStatus_Click(object sender, EventArgs e)
        {
            if (dgvTemplates.SelectedRows.Count == 0)
            {
                MessageBox.Show("Please select a template to toggle status.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            var selectedRow = dgvTemplates.SelectedRows[0];
            var templateId = Convert.ToInt32(selectedRow.Cells["Id"].Value);
            var templateName = selectedRow.Cells["Name"].Value.ToString();
            var currentStatus = Convert.ToBoolean(selectedRow.Cells["IsActive"].Value);

            try
            {
                statusLabel.Text = "Updating template status...";
                var success = await _templateService.ToggleTemplateStatusAsync(templateId);
                
                if (success)
                {
                    LoadTemplatesAsync();
                    statusLabel.Text = $"Template '{templateName}' {(currentStatus ? "deactivated" : "activated")} successfully";
                }
                else
                {
                    MessageBox.Show("Failed to update template status.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    statusLabel.Text = "Failed to update template status";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error updating template status: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                statusLabel.Text = "Error updating template status";
            }
        }

        private void btnRefreshTemplates_Click(object sender, EventArgs e)
        {
            LoadTemplatesAsync();
            statusLabel.Text = "Templates refreshed";
        }

        private void btnAddSequence_Click(object sender, EventArgs e)
        {
            using (var form = new SequenceForm())
            {
                if (form.ShowDialog() == DialogResult.OK)
                {
                    LoadSequencesAsync();
                    statusLabel.Text = "Sequence added successfully";
                }
            }
        }

        private async void btnEditSequence_Click(object sender, EventArgs e)
        {
            if (dgvSequences.SelectedRows.Count == 0)
            {
                MessageBox.Show("Please select a sequence to edit.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            var selectedRow = dgvSequences.SelectedRows[0];
            var sequenceId = Convert.ToInt32(selectedRow.Cells["Id"].Value);

            try
            {
                var sequence = await _sequenceRepository.GetSequenceByIdAsync(sequenceId);
                if (sequence == null)
                {
                    MessageBox.Show("Sequence not found.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                using (var form = new SequenceForm(sequence))
                {
                    if (form.ShowDialog() == DialogResult.OK)
                    {
                        LoadSequencesAsync();
                        statusLabel.Text = "Sequence updated successfully";
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading sequence: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void btnDeleteSequence_Click(object sender, EventArgs e)
        {
            if (dgvSequences.SelectedRows.Count == 0)
            {
                MessageBox.Show("Please select a sequence to delete.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            var selectedRow = dgvSequences.SelectedRows[0];
            var sequenceId = Convert.ToInt32(selectedRow.Cells["Id"].Value);
            var sequenceName = selectedRow.Cells["Name"].Value.ToString();

            var result = MessageBox.Show(
                $"Are you sure you want to delete the sequence '{sequenceName}'?\n\nThis action cannot be undone.",
                "Confirm Delete",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Warning);

            if (result == DialogResult.Yes)
            {
                try
                {
                    statusLabel.Text = "Deleting sequence...";
                    var success = await _sequenceRepository.DeleteSequenceAsync(sequenceId);
                    
                    if (success)
                    {
                        LoadSequencesAsync();
                        statusLabel.Text = "Sequence deleted successfully";
                    }
                    else
                    {
                        MessageBox.Show("Failed to delete sequence.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        statusLabel.Text = "Failed to delete sequence";
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error deleting sequence: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    statusLabel.Text = "Error deleting sequence";
                }
            }
        }

        private void btnManageSequence_Click(object sender, EventArgs e)
        {
            if (dgvSequences.SelectedRows.Count == 0)
            {
                MessageBox.Show("Please select a sequence to manage.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            var selectedRow = dgvSequences.SelectedRows[0];
            var sequenceId = Convert.ToInt32(selectedRow.Cells["Id"].Value);
            var sequenceName = selectedRow.Cells["Name"].Value.ToString();

            using (var sequenceForm = new SequenceItemManagementForm(sequenceId, sequenceName))
            {
                sequenceForm.ShowDialog();
            }
        }

        private void btnRefreshSequences_Click(object sender, EventArgs e)
        {
            LoadSequencesAsync();
            statusLabel.Text = "Sequences refreshed";
        }

        // Helper class for ComboBox items
        private class ComboBoxItem
        {
            public int Id { get; set; }
            public string Text { get; set; }

            public override string ToString()
            {
                return Text;
            }
        }
    }
} 