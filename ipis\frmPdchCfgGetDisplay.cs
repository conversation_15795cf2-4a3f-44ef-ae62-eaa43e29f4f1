// Decompiled with JetBrains decompiler
// Type: ipis.frmPdchCfgGetDisplay
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

namespace ipis
{

[DesignerGenerated]
public class frmPdchCfgGetDisplay : Form
{
  private static List<WeakReference> __ENCList = new List<WeakReference>();
  private IContainer components;
  [AccessedThroughProperty("pdch_dgv")]
  private DataGridView _pdch_dgv;
  [AccessedThroughProperty("port_no")]
  private DataGridViewTextBoxColumn _port_no;
  [AccessedThroughProperty("port_type")]
  private DataGridViewTextBoxColumn _port_type;
  [AccessedThroughProperty("no_sys_ids")]
  private DataGridViewTextBoxColumn _no_sys_ids;
  [AccessedThroughProperty("sys_id1")]
  private DataGridViewTextBoxColumn _sys_id1;
  [AccessedThroughProperty("sys_id2")]
  private DataGridViewTextBoxColumn _sys_id2;
  [AccessedThroughProperty("sys_id3")]
  private DataGridViewTextBoxColumn _sys_id3;
  [AccessedThroughProperty("sys_id4")]
  private DataGridViewTextBoxColumn _sys_id4;
  [AccessedThroughProperty("sys_id5")]
  private DataGridViewTextBoxColumn _sys_id5;
  [AccessedThroughProperty("sys_id6")]
  private DataGridViewTextBoxColumn _sys_id6;
  [AccessedThroughProperty("sys_id7")]
  private DataGridViewTextBoxColumn _sys_id7;
  [AccessedThroughProperty("sys_id8")]
  private DataGridViewTextBoxColumn _sys_id8;
  [AccessedThroughProperty("sys_id9")]
  private DataGridViewTextBoxColumn _sys_id9;
  [AccessedThroughProperty("sys_id10")]
  private DataGridViewTextBoxColumn _sys_id10;
  [AccessedThroughProperty("sys_id11")]
  private DataGridViewTextBoxColumn _sys_id11;
  [AccessedThroughProperty("sys_id12")]
  private DataGridViewTextBoxColumn _sys_id12;
  [AccessedThroughProperty("sys_id13")]
  private DataGridViewTextBoxColumn _sys_id13;
  [AccessedThroughProperty("sys_id14")]
  private DataGridViewTextBoxColumn _sys_id14;
  [AccessedThroughProperty("sys_id15")]
  private DataGridViewTextBoxColumn _sys_id15;
  [AccessedThroughProperty("sys_id16")]
  private DataGridViewTextBoxColumn _sys_id16;
  [AccessedThroughProperty("sys_id17")]
  private DataGridViewTextBoxColumn _sys_id17;
  [AccessedThroughProperty("sys_id18")]
  private DataGridViewTextBoxColumn _sys_id18;
  [AccessedThroughProperty("sys_id19")]
  private DataGridViewTextBoxColumn _sys_id19;
  [AccessedThroughProperty("sys_id20")]
  private DataGridViewTextBoxColumn _sys_id20;
  [AccessedThroughProperty("btnExit")]
  private Button _btnExit;
  [AccessedThroughProperty("txtSystem_status")]
  private TextBox _txtSystem_status;
  [AccessedThroughProperty("Label2")]
  private Label _Label2;
  [AccessedThroughProperty("txtPdchAddr")]
  private TextBox _txtPdchAddr;
  [AccessedThroughProperty("Label1")]
  private Label _Label1;
  [AccessedThroughProperty("txtPdchName")]
  private TextBox _txtPdchName;
  [AccessedThroughProperty("Label3")]
  private Label _Label3;

  [DebuggerNonUserCode]
  static frmPdchCfgGetDisplay()
  {
  }

  [DebuggerNonUserCode]
  public frmPdchCfgGetDisplay()
  {
    frmPdchCfgGetDisplay.__ENCAddToList((object) this);
    this.InitializeComponent();
  }

  [DebuggerNonUserCode]
  private static void __ENCAddToList(object value)
  {
    lock (frmPdchCfgGetDisplay.__ENCList)
    {
      if (frmPdchCfgGetDisplay.__ENCList.Count == frmPdchCfgGetDisplay.__ENCList.Capacity)
      {
        int index1 = 0;
        int num = checked (frmPdchCfgGetDisplay.__ENCList.Count - 1);
        int index2 = 0;
        while (index2 <= num)
        {
          if (frmPdchCfgGetDisplay.__ENCList[index2].IsAlive)
          {
            if (index2 != index1)
              frmPdchCfgGetDisplay.__ENCList[index1] = frmPdchCfgGetDisplay.__ENCList[index2];
            checked { ++index1; }
          }
          checked { ++index2; }
        }
        frmPdchCfgGetDisplay.__ENCList.RemoveRange(index1, checked (frmPdchCfgGetDisplay.__ENCList.Count - index1));
        frmPdchCfgGetDisplay.__ENCList.Capacity = frmPdchCfgGetDisplay.__ENCList.Count;
      }
      frmPdchCfgGetDisplay.__ENCList.Add(new WeakReference(RuntimeHelpers.GetObjectValue(value)));
    }
  }

  [DebuggerNonUserCode]
  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  [DebuggerStepThrough]
  private void InitializeComponent()
  {
    DataGridViewCellStyle gridViewCellStyle = new DataGridViewCellStyle();
    this.pdch_dgv = new DataGridView();
    this.port_no = new DataGridViewTextBoxColumn();
    this.port_type = new DataGridViewTextBoxColumn();
    this.no_sys_ids = new DataGridViewTextBoxColumn();
    this.sys_id1 = new DataGridViewTextBoxColumn();
    this.sys_id2 = new DataGridViewTextBoxColumn();
    this.sys_id3 = new DataGridViewTextBoxColumn();
    this.sys_id4 = new DataGridViewTextBoxColumn();
    this.sys_id5 = new DataGridViewTextBoxColumn();
    this.sys_id6 = new DataGridViewTextBoxColumn();
    this.sys_id7 = new DataGridViewTextBoxColumn();
    this.sys_id8 = new DataGridViewTextBoxColumn();
    this.sys_id9 = new DataGridViewTextBoxColumn();
    this.sys_id10 = new DataGridViewTextBoxColumn();
    this.sys_id11 = new DataGridViewTextBoxColumn();
    this.sys_id12 = new DataGridViewTextBoxColumn();
    this.sys_id13 = new DataGridViewTextBoxColumn();
    this.sys_id14 = new DataGridViewTextBoxColumn();
    this.sys_id15 = new DataGridViewTextBoxColumn();
    this.sys_id16 = new DataGridViewTextBoxColumn();
    this.sys_id17 = new DataGridViewTextBoxColumn();
    this.sys_id18 = new DataGridViewTextBoxColumn();
    this.sys_id19 = new DataGridViewTextBoxColumn();
    this.sys_id20 = new DataGridViewTextBoxColumn();
    this.btnExit = new Button();
    this.txtSystem_status = new TextBox();
    this.Label2 = new Label();
    this.txtPdchAddr = new TextBox();
    this.Label1 = new Label();
    this.txtPdchName = new TextBox();
    this.Label3 = new Label();
    ((ISupportInitialize) this.pdch_dgv).BeginInit();
    this.SuspendLayout();
    this.pdch_dgv.AccessibleDescription = "";
    this.pdch_dgv.BackgroundColor = SystemColors.ButtonHighlight;
    gridViewCellStyle.Alignment = DataGridViewContentAlignment.MiddleLeft;
    gridViewCellStyle.BackColor = Color.SkyBlue;
    gridViewCellStyle.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
    gridViewCellStyle.ForeColor = SystemColors.WindowText;
    gridViewCellStyle.SelectionBackColor = SystemColors.Highlight;
    gridViewCellStyle.SelectionForeColor = SystemColors.HighlightText;
    gridViewCellStyle.WrapMode = DataGridViewTriState.True;
    this.pdch_dgv.ColumnHeadersDefaultCellStyle = gridViewCellStyle;
    this.pdch_dgv.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
    this.pdch_dgv.Columns.AddRange((DataGridViewColumn) this.port_no, (DataGridViewColumn) this.port_type, (DataGridViewColumn) this.no_sys_ids, (DataGridViewColumn) this.sys_id1, (DataGridViewColumn) this.sys_id2, (DataGridViewColumn) this.sys_id3, (DataGridViewColumn) this.sys_id4, (DataGridViewColumn) this.sys_id5, (DataGridViewColumn) this.sys_id6, (DataGridViewColumn) this.sys_id7, (DataGridViewColumn) this.sys_id8, (DataGridViewColumn) this.sys_id9, (DataGridViewColumn) this.sys_id10, (DataGridViewColumn) this.sys_id11, (DataGridViewColumn) this.sys_id12, (DataGridViewColumn) this.sys_id13, (DataGridViewColumn) this.sys_id14, (DataGridViewColumn) this.sys_id15, (DataGridViewColumn) this.sys_id16, (DataGridViewColumn) this.sys_id17, (DataGridViewColumn) this.sys_id18, (DataGridViewColumn) this.sys_id19, (DataGridViewColumn) this.sys_id20);
    DataGridView pdchDgv1 = this.pdch_dgv;
    Point point1 = new Point(2, 124);
    Point point2 = point1;
    pdchDgv1.Location = point2;
    this.pdch_dgv.Name = "pdch_dgv";
    this.pdch_dgv.ReadOnly = true;
    DataGridView pdchDgv2 = this.pdch_dgv;
    Size size1 = new Size(1027, 385);
    Size size2 = size1;
    pdchDgv2.Size = size2;
    this.pdch_dgv.TabIndex = 18;
    this.port_no.HeaderText = "PORT_NO";
    this.port_no.Name = "port_no";
    this.port_no.ReadOnly = true;
    this.port_no.Width = 60;
    this.port_type.HeaderText = "PORT_TYPE";
    this.port_type.Name = "port_type";
    this.port_type.ReadOnly = true;
    this.port_type.Width = 75;
    this.no_sys_ids.HeaderText = "NO_OF_IDS";
    this.no_sys_ids.Name = "no_sys_ids";
    this.no_sys_ids.ReadOnly = true;
    this.no_sys_ids.Width = 70;
    this.sys_id1.HeaderText = "ID1";
    this.sys_id1.Name = "sys_id1";
    this.sys_id1.ReadOnly = true;
    this.sys_id1.Width = 38;
    this.sys_id2.HeaderText = "ID2";
    this.sys_id2.Name = "sys_id2";
    this.sys_id2.ReadOnly = true;
    this.sys_id2.Width = 38;
    this.sys_id3.HeaderText = "ID3";
    this.sys_id3.Name = "sys_id3";
    this.sys_id3.ReadOnly = true;
    this.sys_id3.Width = 38;
    this.sys_id4.HeaderText = "ID4";
    this.sys_id4.Name = "sys_id4";
    this.sys_id4.ReadOnly = true;
    this.sys_id4.Width = 38;
    this.sys_id5.HeaderText = "ID5";
    this.sys_id5.Name = "sys_id5";
    this.sys_id5.ReadOnly = true;
    this.sys_id5.Width = 38;
    this.sys_id6.HeaderText = "ID6";
    this.sys_id6.Name = "sys_id6";
    this.sys_id6.ReadOnly = true;
    this.sys_id6.Width = 38;
    this.sys_id7.HeaderText = "ID7";
    this.sys_id7.Name = "sys_id7";
    this.sys_id7.ReadOnly = true;
    this.sys_id7.Width = 38;
    this.sys_id8.HeaderText = "ID8";
    this.sys_id8.Name = "sys_id8";
    this.sys_id8.ReadOnly = true;
    this.sys_id8.Width = 38;
    this.sys_id9.HeaderText = "ID9";
    this.sys_id9.Name = "sys_id9";
    this.sys_id9.ReadOnly = true;
    this.sys_id9.Width = 38;
    this.sys_id10.HeaderText = "ID10";
    this.sys_id10.Name = "sys_id10";
    this.sys_id10.ReadOnly = true;
    this.sys_id10.Width = 40;
    this.sys_id11.HeaderText = "ID11";
    this.sys_id11.Name = "sys_id11";
    this.sys_id11.ReadOnly = true;
    this.sys_id11.Width = 40;
    this.sys_id12.HeaderText = "ID12";
    this.sys_id12.Name = "sys_id12";
    this.sys_id12.ReadOnly = true;
    this.sys_id12.Width = 40;
    this.sys_id13.HeaderText = "ID13";
    this.sys_id13.Name = "sys_id13";
    this.sys_id13.ReadOnly = true;
    this.sys_id13.Width = 40;
    this.sys_id14.HeaderText = "ID14";
    this.sys_id14.Name = "sys_id14";
    this.sys_id14.ReadOnly = true;
    this.sys_id14.Width = 40;
    this.sys_id15.HeaderText = "ID15";
    this.sys_id15.Name = "sys_id15";
    this.sys_id15.ReadOnly = true;
    this.sys_id15.Width = 40;
    this.sys_id16.HeaderText = "ID16";
    this.sys_id16.Name = "sys_id16";
    this.sys_id16.ReadOnly = true;
    this.sys_id16.Width = 40;
    this.sys_id17.HeaderText = "ID17";
    this.sys_id17.Name = "sys_id17";
    this.sys_id17.ReadOnly = true;
    this.sys_id17.Width = 40;
    this.sys_id18.HeaderText = "ID18";
    this.sys_id18.Name = "sys_id18";
    this.sys_id18.ReadOnly = true;
    this.sys_id18.Width = 40;
    this.sys_id19.HeaderText = "ID19";
    this.sys_id19.Name = "sys_id19";
    this.sys_id19.ReadOnly = true;
    this.sys_id19.Width = 40;
    this.sys_id20.HeaderText = "ID20";
    this.sys_id20.Name = "sys_id20";
    this.sys_id20.ReadOnly = true;
    this.sys_id20.Width = 40;
    this.btnExit.BackColor = Color.SeaShell;
    this.btnExit.DialogResult = DialogResult.Cancel;
    this.btnExit.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnExit1 = this.btnExit;
    point1 = new Point(452, 515);
    Point point3 = point1;
    btnExit1.Location = point3;
    this.btnExit.Name = "btnExit";
    Button btnExit2 = this.btnExit;
    size1 = new Size(75, 23);
    Size size3 = size1;
    btnExit2.Size = size3;
    this.btnExit.TabIndex = 4;
    this.btnExit.Text = "Exit";
    this.btnExit.UseVisualStyleBackColor = false;
    this.txtSystem_status.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtSystemStatus1 = this.txtSystem_status;
    point1 = new Point(568, 81);
    Point point4 = point1;
    txtSystemStatus1.Location = point4;
    this.txtSystem_status.MaxLength = 50;
    this.txtSystem_status.Name = "txtSystem_status";
    this.txtSystem_status.ReadOnly = true;
    TextBox txtSystemStatus2 = this.txtSystem_status;
    size1 = new Size(209, 20);
    Size size4 = size1;
    txtSystemStatus2.Size = size4;
    this.txtSystem_status.TabIndex = 3;
    this.Label2.AutoSize = true;
    this.Label2.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label label2_1 = this.Label2;
    point1 = new Point(443, 88);
    Point point5 = point1;
    label2_1.Location = point5;
    this.Label2.Name = "Label2";
    Label label2_2 = this.Label2;
    size1 = new Size(106, 16 /*0x10*/);
    Size size5 = size1;
    label2_2.Size = size5;
    this.Label2.TabIndex = 15;
    this.Label2.Text = "System Status";
    this.txtPdchAddr.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtPdchAddr1 = this.txtPdchAddr;
    point1 = new Point(568, 17);
    Point point6 = point1;
    txtPdchAddr1.Location = point6;
    this.txtPdchAddr.MaxLength = 3;
    this.txtPdchAddr.Name = "txtPdchAddr";
    this.txtPdchAddr.ReadOnly = true;
    TextBox txtPdchAddr2 = this.txtPdchAddr;
    size1 = new Size(55, 20);
    Size size6 = size1;
    txtPdchAddr2.Size = size6;
    this.txtPdchAddr.TabIndex = 1;
    this.Label1.AutoSize = true;
    this.Label1.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label label1_1 = this.Label1;
    point1 = new Point(439, 20);
    Point point7 = point1;
    label1_1.Location = point7;
    this.Label1.Name = "Label1";
    Label label1_2 = this.Label1;
    size1 = new Size(112 /*0x70*/, 16 /*0x10*/);
    Size size7 = size1;
    label1_2.Size = size7;
    this.Label1.TabIndex = 13;
    this.Label1.Text = "PDCH Address";
    this.txtPdchName.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtPdchName1 = this.txtPdchName;
    point1 = new Point(568, 52);
    Point point8 = point1;
    txtPdchName1.Location = point8;
    this.txtPdchName.MaxLength = 15;
    this.txtPdchName.Name = "txtPdchName";
    this.txtPdchName.ReadOnly = true;
    TextBox txtPdchName2 = this.txtPdchName;
    size1 = new Size(120, 20);
    Size size8 = size1;
    txtPdchName2.Size = size8;
    this.txtPdchName.TabIndex = 2;
    this.Label3.AutoSize = true;
    this.Label3.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label label3_1 = this.Label3;
    point1 = new Point(449, 55);
    Point point9 = point1;
    label3_1.Location = point9;
    this.Label3.Name = "Label3";
    Label label3_2 = this.Label3;
    size1 = new Size(95, 16 /*0x10*/);
    Size size9 = size1;
    label3_2.Size = size9;
    this.Label3.TabIndex = 19;
    this.Label3.Text = "PDCH Name";
    this.AcceptButton = (IButtonControl) this.btnExit;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.BackColor = Color.PowderBlue;
    this.CancelButton = (IButtonControl) this.btnExit;
    size1 = new Size(1029, 550);
    this.ClientSize = size1;
    this.Controls.Add((Control) this.txtPdchName);
    this.Controls.Add((Control) this.Label3);
    this.Controls.Add((Control) this.pdch_dgv);
    this.Controls.Add((Control) this.btnExit);
    this.Controls.Add((Control) this.txtSystem_status);
    this.Controls.Add((Control) this.Label2);
    this.Controls.Add((Control) this.txtPdchAddr);
    this.Controls.Add((Control) this.Label1);
    this.FormBorderStyle = FormBorderStyle.FixedToolWindow;
    this.Name = "frmPdchCfgGetDisplay";
    this.Text = "PDCH CONFIGURATION";
    ((ISupportInitialize) this.pdch_dgv).EndInit();
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  internal virtual DataGridView pdch_dgv
  {
    [DebuggerNonUserCode] get { return this._pdch_dgv; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._pdch_dgv = value; }
  }

  internal virtual DataGridViewTextBoxColumn port_no
  {
    [DebuggerNonUserCode] get { return this._port_no; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._port_no = value; }
  }

  internal virtual DataGridViewTextBoxColumn port_type
  {
    [DebuggerNonUserCode] get { return this._port_type; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._port_type = value;
    }
  }

  internal virtual DataGridViewTextBoxColumn no_sys_ids
  {
    [DebuggerNonUserCode] get { return this._no_sys_ids; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._no_sys_ids = value;
    }
  }

  internal virtual DataGridViewTextBoxColumn sys_id1
  {
    [DebuggerNonUserCode] get { return this._sys_id1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._sys_id1 = value; }
  }

  internal virtual DataGridViewTextBoxColumn sys_id2
  {
    [DebuggerNonUserCode] get { return this._sys_id2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._sys_id2 = value; }
  }

  internal virtual DataGridViewTextBoxColumn sys_id3
  {
    [DebuggerNonUserCode] get { return this._sys_id3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._sys_id3 = value; }
  }

  internal virtual DataGridViewTextBoxColumn sys_id4
  {
    [DebuggerNonUserCode] get { return this._sys_id4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._sys_id4 = value; }
  }

  internal virtual DataGridViewTextBoxColumn sys_id5
  {
    [DebuggerNonUserCode] get { return this._sys_id5; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._sys_id5 = value; }
  }

  internal virtual DataGridViewTextBoxColumn sys_id6
  {
    [DebuggerNonUserCode] get { return this._sys_id6; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._sys_id6 = value; }
  }

  internal virtual DataGridViewTextBoxColumn sys_id7
  {
    [DebuggerNonUserCode] get { return this._sys_id7; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._sys_id7 = value; }
  }

  internal virtual DataGridViewTextBoxColumn sys_id8
  {
    [DebuggerNonUserCode] get { return this._sys_id8; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._sys_id8 = value; }
  }

  internal virtual DataGridViewTextBoxColumn sys_id9
  {
    [DebuggerNonUserCode] get { return this._sys_id9; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._sys_id9 = value; }
  }

  internal virtual DataGridViewTextBoxColumn sys_id10
  {
    [DebuggerNonUserCode] get { return this._sys_id10; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._sys_id10 = value; }
  }

  internal virtual DataGridViewTextBoxColumn sys_id11
  {
    [DebuggerNonUserCode] get { return this._sys_id11; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._sys_id11 = value; }
  }

  internal virtual DataGridViewTextBoxColumn sys_id12
  {
    [DebuggerNonUserCode] get { return this._sys_id12; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._sys_id12 = value; }
  }

  internal virtual DataGridViewTextBoxColumn sys_id13
  {
    [DebuggerNonUserCode] get { return this._sys_id13; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._sys_id13 = value; }
  }

  internal virtual DataGridViewTextBoxColumn sys_id14
  {
    [DebuggerNonUserCode] get { return this._sys_id14; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._sys_id14 = value; }
  }

  internal virtual DataGridViewTextBoxColumn sys_id15
  {
    [DebuggerNonUserCode] get { return this._sys_id15; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._sys_id15 = value; }
  }

  internal virtual DataGridViewTextBoxColumn sys_id16
  {
    [DebuggerNonUserCode] get { return this._sys_id16; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._sys_id16 = value; }
  }

  internal virtual DataGridViewTextBoxColumn sys_id17
  {
    [DebuggerNonUserCode] get { return this._sys_id17; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._sys_id17 = value; }
  }

  internal virtual DataGridViewTextBoxColumn sys_id18
  {
    [DebuggerNonUserCode] get { return this._sys_id18; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._sys_id18 = value; }
  }

  internal virtual DataGridViewTextBoxColumn sys_id19
  {
    [DebuggerNonUserCode] get { return this._sys_id19; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._sys_id19 = value; }
  }

  internal virtual DataGridViewTextBoxColumn sys_id20
  {
    [DebuggerNonUserCode] get { return this._sys_id20; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._sys_id20 = value; }
  }

  internal virtual Button btnExit
  {
    [DebuggerNonUserCode] get { return this._btnExit; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnExit_Click);
      if (this._btnExit != null)
        this._btnExit.Click -= eventHandler;
      this._btnExit = value;
      if (this._btnExit == null)
        return;
      this._btnExit.Click += eventHandler;
    }
  }

  internal virtual TextBox txtSystem_status
  {
    [DebuggerNonUserCode] get { return this._txtSystem_status; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtSystem_status = value;
    }
  }

  internal virtual Label Label2
  {
    [DebuggerNonUserCode] get { return this._Label2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Label2 = value; }
  }

  internal virtual TextBox txtPdchAddr
  {
    [DebuggerNonUserCode] get { return this._txtPdchAddr; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtPdchAddr = value;
    }
  }

  internal virtual Label Label1
  {
    [DebuggerNonUserCode] get { return this._Label1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Label1 = value; }
  }

  internal virtual TextBox txtPdchName
  {
    [DebuggerNonUserCode] get { return this._txtPdchName; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtPdchName = value;
    }
  }

  internal virtual Label Label3
  {
    [DebuggerNonUserCode] get { return this._Label3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Label3 = value; }
  }

  private void btnExit_Click(object sender, EventArgs e)
{
  this.Close();
}
}

}