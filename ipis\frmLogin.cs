// Decompiled with JetBrains decompiler
// Type: ipis.frmLogin
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using ipis.My;
using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

namespace ipis
{

[DesignerGenerated]
public class frmLogin : Form
{
  private static List<WeakReference> __ENCList = new List<WeakReference>();
  private IContainer components;
  [AccessedThroughProperty("btnExit")]
  private Button _btnExit;
  [AccessedThroughProperty("btnOk")]
  private Button _btnOk;
  [AccessedThroughProperty("txtPassword")]
  private TextBox _txtPassword;
  [AccessedThroughProperty("txtUserName")]
  private TextBox _txtUserName;
  [AccessedThroughProperty("PasswordLabel")]
  private Label _PasswordLabel;
  [AccessedThroughProperty("UsernameLabel")]
  private Label _UsernameLabel;

  [DebuggerNonUserCode]
  static frmLogin()
  {
  }

  [DebuggerNonUserCode]
  public frmLogin()
  {
    this.Load += new EventHandler(this.frmLogin_Load);
    frmLogin.__ENCAddToList((object) this);
    this.InitializeComponent();
  }

  [DebuggerNonUserCode]
  private static void __ENCAddToList(object value)
  {
    lock (frmLogin.__ENCList)
    {
      if (frmLogin.__ENCList.Count == frmLogin.__ENCList.Capacity)
      {
        int index1 = 0;
        int num = checked (frmLogin.__ENCList.Count - 1);
        int index2 = 0;
        while (index2 <= num)
        {
          if (frmLogin.__ENCList[index2].IsAlive)
          {
            if (index2 != index1)
              frmLogin.__ENCList[index1] = frmLogin.__ENCList[index2];
            checked { ++index1; }
          }
          checked { ++index2; }
        }
        frmLogin.__ENCList.RemoveRange(index1, checked (frmLogin.__ENCList.Count - index1));
        frmLogin.__ENCList.Capacity = frmLogin.__ENCList.Count;
      }
      frmLogin.__ENCList.Add(new WeakReference(RuntimeHelpers.GetObjectValue(value)));
    }
  }

  [DebuggerNonUserCode]
  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  [DebuggerStepThrough]
  private void InitializeComponent()
  {
    this.btnExit = new Button();
    this.txtPassword = new TextBox();
    this.txtUserName = new TextBox();
    this.PasswordLabel = new Label();
    this.UsernameLabel = new Label();
    this.btnOk = new Button();
    this.SuspendLayout();
    this.btnExit.BackColor = Color.SeaShell;
    this.btnExit.DialogResult = DialogResult.Cancel;
    this.btnExit.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnExit1 = this.btnExit;
    Point point1 = new Point(762, 299);
    Point point2 = point1;
    btnExit1.Location = point2;
    this.btnExit.Name = "btnExit";
    Button btnExit2 = this.btnExit;
    Size size1 = new Size(67, 23);
    Size size2 = size1;
    btnExit2.Size = size2;
    this.btnExit.TabIndex = 4;
    this.btnExit.Text = "&Exit";
    this.btnExit.UseVisualStyleBackColor = false;
    this.txtPassword.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtPassword1 = this.txtPassword;
    point1 = new Point(635, 226);
    Point point3 = point1;
    txtPassword1.Location = point3;
    this.txtPassword.Name = "txtPassword";
    this.txtPassword.PasswordChar = '*';
    TextBox txtPassword2 = this.txtPassword;
    size1 = new Size(220, 22);
    Size size3 = size1;
    txtPassword2.Size = size3;
    this.txtPassword.TabIndex = 2;
    this.txtUserName.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtUserName1 = this.txtUserName;
    point1 = new Point(635, 133);
    Point point4 = point1;
    txtUserName1.Location = point4;
    this.txtUserName.Name = "txtUserName";
    TextBox txtUserName2 = this.txtUserName;
    size1 = new Size(220, 22);
    Size size4 = size1;
    txtUserName2.Size = size4;
    this.txtUserName.TabIndex = 1;
    this.PasswordLabel.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label passwordLabel1 = this.PasswordLabel;
    point1 = new Point(635, 189);
    Point point5 = point1;
    passwordLabel1.Location = point5;
    this.PasswordLabel.Name = "PasswordLabel";
    Label passwordLabel2 = this.PasswordLabel;
    size1 = new Size(84, 23);
    Size size5 = size1;
    passwordLabel2.Size = size5;
    this.PasswordLabel.TabIndex = 9;
    this.PasswordLabel.Text = "Password";
    this.PasswordLabel.TextAlign = ContentAlignment.MiddleLeft;
    this.UsernameLabel.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label usernameLabel1 = this.UsernameLabel;
    point1 = new Point(635, 91);
    Point point6 = point1;
    usernameLabel1.Location = point6;
    this.UsernameLabel.Name = "UsernameLabel";
    Label usernameLabel2 = this.UsernameLabel;
    size1 = new Size(93, 23);
    Size size6 = size1;
    usernameLabel2.Size = size6;
    this.UsernameLabel.TabIndex = 7;
    this.UsernameLabel.Text = "User Name";
    this.UsernameLabel.TextAlign = ContentAlignment.MiddleLeft;
    this.btnOk.BackColor = Color.SeaShell;
    this.btnOk.DialogResult = DialogResult.OK;
    this.btnOk.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnOk1 = this.btnOk;
    point1 = new Point(641, 299);
    Point point7 = point1;
    btnOk1.Location = point7;
    this.btnOk.Name = "btnOk";
    Button btnOk2 = this.btnOk;
    size1 = new Size(65, 23);
    Size size7 = size1;
    btnOk2.Size = size7;
    this.btnOk.TabIndex = 3;
    this.btnOk.Text = "&OK";
    this.btnOk.UseVisualStyleBackColor = false;
    this.AcceptButton = (IButtonControl) this.btnOk;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.BackColor = Color.LightBlue;
    this.BackgroundImage = (Image) ipis.My.Resources.Resources.train_picture;
    this.BackgroundImageLayout = ImageLayout.None;
    this.CancelButton = (IButtonControl) this.btnExit;
    size1 = new Size(902, 381);
    this.ClientSize = size1;
    this.Controls.Add((Control) this.btnExit);
    this.Controls.Add((Control) this.btnOk);
    this.Controls.Add((Control) this.txtPassword);
    this.Controls.Add((Control) this.txtUserName);
    this.Controls.Add((Control) this.PasswordLabel);
    this.Controls.Add((Control) this.UsernameLabel);
    this.FormBorderStyle = FormBorderStyle.FixedToolWindow;
    this.Name = "frmLogin";
    this.Text = "Login";
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  internal virtual Button btnExit
  {
    [DebuggerNonUserCode] get { return this._btnExit; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnExit_Click);
      if (this._btnExit != null)
        this._btnExit.Click -= eventHandler;
      this._btnExit = value;
      if (this._btnExit == null)
        return;
      this._btnExit.Click += eventHandler;
    }
  }

  internal virtual Button btnOk
  {
    [DebuggerNonUserCode] get { return this._btnOk; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnOk_Click);
      if (this._btnOk != null)
        this._btnOk.Click -= eventHandler;
      this._btnOk = value;
      if (this._btnOk == null)
        return;
      this._btnOk.Click += eventHandler;
    }
  }

  internal virtual TextBox txtPassword
  {
    [DebuggerNonUserCode] get { return this._txtPassword; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtPassword = value;
    }
  }

  internal virtual TextBox txtUserName
  {
    [DebuggerNonUserCode] get { return this._txtUserName; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      KeyEventHandler keyEventHandler = new KeyEventHandler(this.txtUserName_KeyDown);
      if (this._txtUserName != null)
        this._txtUserName.KeyDown -= keyEventHandler;
      this._txtUserName = value;
      if (this._txtUserName == null)
        return;
      this._txtUserName.KeyDown += keyEventHandler;
    }
  }

  internal virtual Label PasswordLabel
  {
    [DebuggerNonUserCode] get { return this._PasswordLabel; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._PasswordLabel = value;
    }
  }

  internal virtual Label UsernameLabel
  {
    [DebuggerNonUserCode] get { return this._UsernameLabel; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._UsernameLabel = value;
    }
  }

  private void btnOk_Click(object sender, EventArgs e)
  {
    string str1 = Strings.Format((object) DateTime.Now.Date, "dd-MM-yyyy");
    this.btnOk.UseWaitCursor = true;
    int index = 0;
    string empty = string.Empty;
    network_db_read.user_info();
    frmMainFormIPIS.user_login = false;
    if (frmMainFormIPIS.user_cnt.cnt == (byte) 0 && Operators.CompareString(Strings.Trim(this.txtUserName.Text), "admin", false) == 0 & Operators.CompareString(Strings.Trim(this.txtPassword.Text), "password", false) == 0)
    {
      frmMainFormIPIS.user_login = true;
      frmMainFormIPIS.user_login_details.user_id = "001";
      frmMainFormIPIS.user_login_details.user_name = "admin";
      frmMainFormIPIS.user_login_details.entry_time = Conversions.ToDate(DateTime.Now.ToShortTimeString());
      frmMainFormIPIS.user_login_details.exit_time = Conversions.ToDate(DateTime.Now.ToShortTimeString());
      frmMainFormIPIS.user_login_details.group = "Admin";
      MyProject.Forms.frmMainFormIPIS.LOGINToolStripMenuItem.Text = "Log Out";
      MyProject.Forms.frmMainFormIPIS.LOGINToolStripMenuItem.ForeColor = Color.Red;
      MyProject.Forms.frmMainFormIPIS.main_form_enable();
      MyProject.Forms.frmMainFormIPIS.NetworkToolStripMenuItem.Enabled = true;
      MyProject.Forms.frmMainFormIPIS.TrainToolStripMenuItem1.Enabled = true;
      MyProject.Forms.frmMainFormIPIS.VoiceToolStripMenuItem.Enabled = true;
    }
    else
    {
      while (index < (int) frmMainFormIPIS.user_cnt.cnt)
      {
        network_db_read.dec_pwd(frmMainFormIPIS.user_details[index].pwd, ref empty, Conversions.ToString(frmMainFormIPIS.user_details[index].pwd_length));
        if (Operators.CompareString(Strings.Trim(this.txtUserName.Text), Strings.Trim(frmMainFormIPIS.user_details[index].user_name), false) == 0 & Operators.CompareString(Strings.Trim(this.txtPassword.Text), Strings.Trim(empty), false) == 0)
        {
          frmMainFormIPIS.user_login = true;
          frmMainFormIPIS.user_login_details.user_id = frmMainFormIPIS.user_details[index].user_id;
          frmMainFormIPIS.user_login_details.user_name = frmMainFormIPIS.user_details[index].user_name;
          DateTime now = DateTime.Now;
          DateTime date1 = Conversions.ToDate(now.ToShortTimeString());
          frmMainFormIPIS.user_login_details.entry_time = date1;
          now = DateTime.Now;
          DateTime date2 = Conversions.ToDate(now.ToShortTimeString());
          frmMainFormIPIS.user_login_details.exit_time = date2;
          frmMainFormIPIS.user_login_details.group = frmMainFormIPIS.user_details[index].group;
          MyProject.Forms.frmMainFormIPIS.LOGINToolStripMenuItem.Text = "Log Out";
          MyProject.Forms.frmMainFormIPIS.LOGINToolStripMenuItem.ForeColor = Color.Red;
          MyProject.Forms.frmMainFormIPIS.main_form_enable();
          if (Operators.CompareString(Strings.Trim(frmMainFormIPIS.user_details[index].group), "Admin", false) == 0)
          {
            MyProject.Forms.frmMainFormIPIS.NetworkToolStripMenuItem.Enabled = true;
            MyProject.Forms.frmMainFormIPIS.TrainToolStripMenuItem1.Enabled = true;
            MyProject.Forms.frmMainFormIPIS.VoiceToolStripMenuItem.Enabled = true;
            goto label_10;
          }
          MyProject.Forms.frmMainFormIPIS.NetworkToolStripMenuItem.Enabled = false;
          MyProject.Forms.frmMainFormIPIS.TrainToolStripMenuItem1.Enabled = false;
          MyProject.Forms.frmMainFormIPIS.VoiceToolStripMenuItem.Enabled = false;
          goto label_10;
        }
        checked { ++index; }
      }
      if (!frmMainFormIPIS.user_login)
      {
        MyProject.Forms.frmMainFormIPIS.main_form_disable();
        int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Please Check Name Or Password Entered Correctly\r\nCheck Whether CAPS Lock is ON", "Msg Box", 0, 0, 0);
        this.BringToFront();
        this.btnOk.UseWaitCursor = false;
        return;
      }
    }
label_10:
    try
    {
      if (Directory.Exists("Z:\\"))
      {
        string str2 = "Z:\\shared_info\\login_details\\{str1}.txt";
        string sourceFileName = "C:\\IPIS\\shared_info\\login_details\\{str1}.txt";
        if (!File.Exists(str2))
          File.Create(str2);
        bool overwrite = true;
        MyProject.Computer.FileSystem.CopyFile(sourceFileName, str2, overwrite);
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
    this.btnOk.UseWaitCursor = false;
    this.Close();
  }

  private void btnExit_Click(object sender, EventArgs e)
{
  this.Close();
}

  private void frmLogin_Load(object sender, EventArgs e)
  {
    this.txtPassword.Text = string.Empty;
    this.txtUserName.Text = string.Empty;
    this.txtPassword.SelectAll();
  }

  private void txtUserName_KeyDown(object sender, KeyEventArgs e)
  {
    if (e.KeyData != Keys.Down)
      return;
    this.txtPassword.SelectAll();
  }
}

}