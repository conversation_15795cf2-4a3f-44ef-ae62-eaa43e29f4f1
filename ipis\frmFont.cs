// Decompiled with JetBrains decompiler
// Type: ipis.frmFont
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using ipis.My;
using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

namespace ipis
{

[DesignerGenerated]
public class frmFont : Form
{
  private static List<WeakReference> __ENCList = new List<WeakReference>();
  private IContainer components;
  [AccessedThroughProperty("cmbLang")]
  private ComboBox _cmbLang;
  [AccessedThroughProperty("lblFontSize")]
  private Label _lblFontSize;
  [AccessedThroughProperty("txtFontSize")]
  private TextBox _txtFontSize;
  [AccessedThroughProperty("lblFontType")]
  private Label _lblFontType;
  [AccessedThroughProperty("txtFontType")]
  private TextBox _txtFontType;
  [AccessedThroughProperty("lblFontName")]
  private Label _lblFontName;
  [AccessedThroughProperty("btnFont")]
  private Button _btnFont;
  [AccessedThroughProperty("txtFontName")]
  private TextBox _txtFontName;
  [AccessedThroughProperty("lblFont")]
  private Label _lblFont;
  [AccessedThroughProperty("lblLangName")]
  private Label _lblLangName;
  [AccessedThroughProperty("txtLangId")]
  private TextBox _txtLangId;
  [AccessedThroughProperty("lblLandId")]
  private Label _lblLandId;
  [AccessedThroughProperty("btnAdd")]
  private Button _btnAdd;
  [AccessedThroughProperty("btnDelete")]
  private Button _btnDelete;
  [AccessedThroughProperty("btnEdit")]
  private Button _btnEdit;
  [AccessedThroughProperty("btnExit")]
  private Button _btnExit;
  [AccessedThroughProperty("btnSave")]
  private Button _btnSave;
  [AccessedThroughProperty("dgvLang")]
  private DataGridView _dgvLang;
  [AccessedThroughProperty("FontDialog1")]
  private FontDialog _FontDialog1;
  [AccessedThroughProperty("lang_id")]
  private DataGridViewTextBoxColumn _lang_id;
  [AccessedThroughProperty("lang_name")]
  private DataGridViewTextBoxColumn _lang_name;
  [AccessedThroughProperty("font_name")]
  private DataGridViewTextBoxColumn _font_name;
  [AccessedThroughProperty("font_type")]
  private DataGridViewTextBoxColumn _font_type;
  [AccessedThroughProperty("font_size")]
  private DataGridViewTextBoxColumn _font_size;

  [DebuggerNonUserCode]
  static frmFont()
  {
  }

  [DebuggerNonUserCode]
  public frmFont()
  {
    this.Load += new EventHandler(this.frmLanguage_Load);
    frmFont.__ENCAddToList((object) this);
    this.InitializeComponent();
  }

  [DebuggerNonUserCode]
  private static void __ENCAddToList(object value)
  {
    lock (frmFont.__ENCList)
    {
      if (frmFont.__ENCList.Count == frmFont.__ENCList.Capacity)
      {
        int index1 = 0;
        int num = checked (frmFont.__ENCList.Count - 1);
        int index2 = 0;
        while (index2 <= num)
        {
          if (frmFont.__ENCList[index2].IsAlive)
          {
            if (index2 != index1)
              frmFont.__ENCList[index1] = frmFont.__ENCList[index2];
            checked { ++index1; }
          }
          checked { ++index2; }
        }
        frmFont.__ENCList.RemoveRange(index1, checked (frmFont.__ENCList.Count - index1));
        frmFont.__ENCList.Capacity = frmFont.__ENCList.Count;
      }
      frmFont.__ENCList.Add(new WeakReference(RuntimeHelpers.GetObjectValue(value)));
    }
  }

  [DebuggerNonUserCode]
  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  [DebuggerStepThrough]
  private void InitializeComponent()
  {
    DataGridViewCellStyle gridViewCellStyle = new DataGridViewCellStyle();
    this.cmbLang = new ComboBox();
    this.lblFontSize = new Label();
    this.txtFontSize = new TextBox();
    this.lblFontType = new Label();
    this.txtFontType = new TextBox();
    this.lblFontName = new Label();
    this.btnFont = new Button();
    this.txtFontName = new TextBox();
    this.lblFont = new Label();
    this.lblLangName = new Label();
    this.txtLangId = new TextBox();
    this.lblLandId = new Label();
    this.btnAdd = new Button();
    this.btnDelete = new Button();
    this.btnEdit = new Button();
    this.btnExit = new Button();
    this.btnSave = new Button();
    this.dgvLang = new DataGridView();
    this.lang_id = new DataGridViewTextBoxColumn();
    this.lang_name = new DataGridViewTextBoxColumn();
    this.font_name = new DataGridViewTextBoxColumn();
    this.font_type = new DataGridViewTextBoxColumn();
    this.font_size = new DataGridViewTextBoxColumn();
    this.FontDialog1 = new FontDialog();
    ((ISupportInitialize) this.dgvLang).BeginInit();
    this.SuspendLayout();
    this.cmbLang.Enabled = false;
    this.cmbLang.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.cmbLang.FormattingEnabled = true;
    this.cmbLang.Items.AddRange(new object[21]
    {
      (object) "Assamese",
      (object) "Bengali",
      (object) "Bhilli",
      (object) "Bhojpuri",
      (object) "Bihari",
      (object) "Devanagiri",
      (object) "English",
      (object) "Gujarati",
      (object) "Hindi",
      (object) "Kannada",
      (object) "Kashmiri",
      (object) "Konkani",
      (object) "Malayalam",
      (object) "Marathi",
      (object) "Marwari",
      (object) "Oriya",
      (object) "Pahari",
      (object) "Punjabi",
      (object) "Santhali",
      (object) "Tamil",
      (object) "Telugu"
    });
    ComboBox cmbLang1 = this.cmbLang;
    Point point1 = new Point(423, 196);
    Point point2 = point1;
    cmbLang1.Location = point2;
    this.cmbLang.Name = "cmbLang";
    ComboBox cmbLang2 = this.cmbLang;
    Size size1 = new Size(135, 24);
    Size size2 = size1;
    cmbLang2.Size = size2;
    this.cmbLang.Sorted = true;
    this.cmbLang.TabIndex = 2;
    this.lblFontSize.AutoSize = true;
    this.lblFontSize.Enabled = false;
    this.lblFontSize.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblFontSize1 = this.lblFontSize;
    point1 = new Point(58, 378);
    Point point3 = point1;
    lblFontSize1.Location = point3;
    this.lblFontSize.Name = "lblFontSize";
    Label lblFontSize2 = this.lblFontSize;
    size1 = new Size(72, 16 /*0x10*/);
    Size size3 = size1;
    lblFontSize2.Size = size3;
    this.lblFontSize.TabIndex = 71;
    this.lblFontSize.Text = "Font Size";
    this.txtFontSize.Enabled = false;
    this.txtFontSize.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtFontSize1 = this.txtFontSize;
    point1 = new Point(169, 375);
    Point point4 = point1;
    txtFontSize1.Location = point4;
    this.txtFontSize.MaxLength = 30;
    this.txtFontSize.Name = "txtFontSize";
    this.txtFontSize.ReadOnly = true;
    TextBox txtFontSize2 = this.txtFontSize;
    size1 = new Size(307, 22);
    Size size4 = size1;
    txtFontSize2.Size = size4;
    this.txtFontSize.TabIndex = 6;
    this.lblFontType.AutoSize = true;
    this.lblFontType.Enabled = false;
    this.lblFontType.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblFontType1 = this.lblFontType;
    point1 = new Point(58, 335);
    Point point5 = point1;
    lblFontType1.Location = point5;
    this.lblFontType.Name = "lblFontType";
    Label lblFontType2 = this.lblFontType;
    size1 = new Size(78, 16 /*0x10*/);
    Size size5 = size1;
    lblFontType2.Size = size5;
    this.lblFontType.TabIndex = 69;
    this.lblFontType.Text = "Font Type";
    this.txtFontType.Enabled = false;
    this.txtFontType.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtFontType1 = this.txtFontType;
    point1 = new Point(169, 332);
    Point point6 = point1;
    txtFontType1.Location = point6;
    this.txtFontType.MaxLength = 30;
    this.txtFontType.Name = "txtFontType";
    this.txtFontType.ReadOnly = true;
    TextBox txtFontType2 = this.txtFontType;
    size1 = new Size(307, 22);
    Size size6 = size1;
    txtFontType2.Size = size6;
    this.txtFontType.TabIndex = 5;
    this.lblFontName.AutoSize = true;
    this.lblFontName.Enabled = false;
    this.lblFontName.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblFontName1 = this.lblFontName;
    point1 = new Point(58, 287);
    Point point7 = point1;
    lblFontName1.Location = point7;
    this.lblFontName.Name = "lblFontName";
    Label lblFontName2 = this.lblFontName;
    size1 = new Size(83, 16 /*0x10*/);
    Size size7 = size1;
    lblFontName2.Size = size7;
    this.lblFontName.TabIndex = 67;
    this.lblFontName.Text = "Font Name";
    this.btnFont.Enabled = false;
    this.btnFont.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnFont1 = this.btnFont;
    point1 = new Point(169, 240 /*0xF0*/);
    Point point8 = point1;
    btnFont1.Location = point8;
    this.btnFont.Name = "btnFont";
    Button btnFont2 = this.btnFont;
    size1 = new Size(101, 23);
    Size size8 = size1;
    btnFont2.Size = size8;
    this.btnFont.TabIndex = 3;
    this.btnFont.Text = "Font";
    this.btnFont.UseVisualStyleBackColor = true;
    this.txtFontName.Enabled = false;
    this.txtFontName.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtFontName1 = this.txtFontName;
    point1 = new Point(169, 284);
    Point point9 = point1;
    txtFontName1.Location = point9;
    this.txtFontName.MaxLength = 30;
    this.txtFontName.Name = "txtFontName";
    this.txtFontName.ReadOnly = true;
    TextBox txtFontName2 = this.txtFontName;
    size1 = new Size(307, 22);
    Size size9 = size1;
    txtFontName2.Size = size9;
    this.txtFontName.TabIndex = 4;
    this.lblFont.AutoSize = true;
    this.lblFont.Enabled = false;
    this.lblFont.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblFont1 = this.lblFont;
    point1 = new Point(58, 243);
    Point point10 = point1;
    lblFont1.Location = point10;
    this.lblFont.Name = "lblFont";
    Label lblFont2 = this.lblFont;
    size1 = new Size(86, 16 /*0x10*/);
    Size size10 = size1;
    lblFont2.Size = size10;
    this.lblFont.TabIndex = 64 /*0x40*/;
    this.lblFont.Text = "Select Font";
    this.lblLangName.AutoSize = true;
    this.lblLangName.Enabled = false;
    this.lblLangName.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblLangName1 = this.lblLangName;
    point1 = new Point(295, 199);
    Point point11 = point1;
    lblLangName1.Location = point11;
    this.lblLangName.Name = "lblLangName";
    Label lblLangName2 = this.lblLangName;
    size1 = new Size(122, 16 /*0x10*/);
    Size size11 = size1;
    lblLangName2.Size = size11;
    this.lblLangName.TabIndex = 63 /*0x3F*/;
    this.lblLangName.Text = "Language Name";
    this.txtLangId.Enabled = false;
    this.txtLangId.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtLangId1 = this.txtLangId;
    point1 = new Point(133, 193);
    Point point12 = point1;
    txtLangId1.Location = point12;
    this.txtLangId.MaxLength = 10;
    this.txtLangId.Name = "txtLangId";
    TextBox txtLangId2 = this.txtLangId;
    size1 = new Size(135, 22);
    Size size12 = size1;
    txtLangId2.Size = size12;
    this.txtLangId.TabIndex = 1;
    this.lblLandId.AutoSize = true;
    this.lblLandId.Enabled = false;
    this.lblLandId.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblLandId1 = this.lblLandId;
    point1 = new Point(12, 196);
    Point point13 = point1;
    lblLandId1.Location = point13;
    this.lblLandId.Name = "lblLandId";
    Label lblLandId2 = this.lblLandId;
    size1 = new Size(96 /*0x60*/, 16 /*0x10*/);
    Size size13 = size1;
    lblLandId2.Size = size13;
    this.lblLandId.TabIndex = 61;
    this.lblLandId.Text = "Language ID";
    this.btnAdd.BackColor = SystemColors.ButtonFace;
    this.btnAdd.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnAdd1 = this.btnAdd;
    point1 = new Point(45, 430);
    Point point14 = point1;
    btnAdd1.Location = point14;
    this.btnAdd.Name = "btnAdd";
    Button btnAdd2 = this.btnAdd;
    size1 = new Size(60, 25);
    Size size14 = size1;
    btnAdd2.Size = size14;
    this.btnAdd.TabIndex = 7;
    this.btnAdd.Text = "&Add";
    this.btnAdd.UseVisualStyleBackColor = false;
    this.btnDelete.BackColor = SystemColors.ButtonFace;
    this.btnDelete.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnDelete1 = this.btnDelete;
    point1 = new Point(331, 430);
    Point point15 = point1;
    btnDelete1.Location = point15;
    this.btnDelete.Name = "btnDelete";
    Button btnDelete2 = this.btnDelete;
    size1 = new Size(74, 25);
    Size size15 = size1;
    btnDelete2.Size = size15;
    this.btnDelete.TabIndex = 10;
    this.btnDelete.Text = "&Delete";
    this.btnDelete.UseVisualStyleBackColor = false;
    this.btnEdit.BackColor = SystemColors.ButtonFace;
    this.btnEdit.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnEdit1 = this.btnEdit;
    point1 = new Point(133, 430);
    Point point16 = point1;
    btnEdit1.Location = point16;
    this.btnEdit.Name = "btnEdit";
    Button btnEdit2 = this.btnEdit;
    size1 = new Size(60, 25);
    Size size16 = size1;
    btnEdit2.Size = size16;
    this.btnEdit.TabIndex = 8;
    this.btnEdit.Text = "&Edit";
    this.btnEdit.UseVisualStyleBackColor = false;
    this.btnExit.BackColor = SystemColors.ButtonFace;
    this.btnExit.DialogResult = DialogResult.Cancel;
    this.btnExit.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnExit1 = this.btnExit;
    point1 = new Point(446, 430);
    Point point17 = point1;
    btnExit1.Location = point17;
    this.btnExit.Name = "btnExit";
    Button btnExit2 = this.btnExit;
    size1 = new Size(60, 25);
    Size size17 = size1;
    btnExit2.Size = size17;
    this.btnExit.TabIndex = 11;
    this.btnExit.Text = "E&xit";
    this.btnExit.UseVisualStyleBackColor = false;
    this.btnSave.BackColor = SystemColors.ButtonFace;
    this.btnSave.Enabled = false;
    this.btnSave.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnSave1 = this.btnSave;
    point1 = new Point(229, 430);
    Point point18 = point1;
    btnSave1.Location = point18;
    this.btnSave.Name = "btnSave";
    Button btnSave2 = this.btnSave;
    size1 = new Size(60, 25);
    Size size18 = size1;
    btnSave2.Size = size18;
    this.btnSave.TabIndex = 9;
    this.btnSave.Text = "&Save";
    this.btnSave.UseVisualStyleBackColor = false;
    this.dgvLang.AllowUserToAddRows = false;
    this.dgvLang.AllowUserToDeleteRows = false;
    this.dgvLang.BackgroundColor = SystemColors.ControlLightLight;
    gridViewCellStyle.Alignment = DataGridViewContentAlignment.MiddleLeft;
    gridViewCellStyle.BackColor = Color.LightSkyBlue;
    gridViewCellStyle.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    gridViewCellStyle.ForeColor = SystemColors.WindowText;
    gridViewCellStyle.SelectionBackColor = SystemColors.Highlight;
    gridViewCellStyle.SelectionForeColor = SystemColors.HighlightText;
    gridViewCellStyle.WrapMode = DataGridViewTriState.True;
    this.dgvLang.ColumnHeadersDefaultCellStyle = gridViewCellStyle;
    this.dgvLang.Columns.AddRange((DataGridViewColumn) this.lang_id, (DataGridViewColumn) this.lang_name, (DataGridViewColumn) this.font_name, (DataGridViewColumn) this.font_type, (DataGridViewColumn) this.font_size);
    this.dgvLang.GridColor = SystemColors.MenuHighlight;
    DataGridView dgvLang1 = this.dgvLang;
    point1 = new Point(-2, -2);
    Point point19 = point1;
    dgvLang1.Location = point19;
    this.dgvLang.Name = "dgvLang";
    DataGridView dgvLang2 = this.dgvLang;
    size1 = new Size(620, 150);
    Size size19 = size1;
    dgvLang2.Size = size19;
    this.dgvLang.TabIndex = 55;
    this.lang_id.HeaderText = "LanguageID";
    this.lang_id.Name = "lang_id";
    this.lang_name.HeaderText = "Language Name";
    this.lang_name.Name = "lang_name";
    this.lang_name.Width = 150;
    this.font_name.HeaderText = "Font Name";
    this.font_name.Name = "font_name";
    this.font_name.Width = 125;
    this.font_type.HeaderText = "Font Type";
    this.font_type.Name = "font_type";
    this.font_size.HeaderText = "Font Size";
    this.font_size.Name = "font_size";
    this.AcceptButton = (IButtonControl) this.btnAdd;
    this.AutoScaleDimensions = new SizeF(7f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.BackColor = Color.PowderBlue;
    this.CancelButton = (IButtonControl) this.btnExit;
    size1 = new Size(616, 477);
    this.ClientSize = size1;
    this.Controls.Add((Control) this.cmbLang);
    this.Controls.Add((Control) this.lblFontSize);
    this.Controls.Add((Control) this.txtFontSize);
    this.Controls.Add((Control) this.lblFontType);
    this.Controls.Add((Control) this.txtFontType);
    this.Controls.Add((Control) this.lblFontName);
    this.Controls.Add((Control) this.btnFont);
    this.Controls.Add((Control) this.txtFontName);
    this.Controls.Add((Control) this.lblFont);
    this.Controls.Add((Control) this.lblLangName);
    this.Controls.Add((Control) this.txtLangId);
    this.Controls.Add((Control) this.lblLandId);
    this.Controls.Add((Control) this.btnAdd);
    this.Controls.Add((Control) this.btnDelete);
    this.Controls.Add((Control) this.btnEdit);
    this.Controls.Add((Control) this.btnExit);
    this.Controls.Add((Control) this.btnSave);
    this.Controls.Add((Control) this.dgvLang);
    this.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.FormBorderStyle = FormBorderStyle.FixedToolWindow;
    this.Name = "frmFont";
    this.Text = "Font";
    ((ISupportInitialize) this.dgvLang).EndInit();
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  internal virtual ComboBox cmbLang
  {
    [DebuggerNonUserCode] get { return this._cmbLang; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbLang = value; }
  }

  internal virtual Label lblFontSize
  {
    [DebuggerNonUserCode] get { return this._lblFontSize; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblFontSize = value;
    }
  }

  internal virtual TextBox txtFontSize
  {
    [DebuggerNonUserCode] get { return this._txtFontSize; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtFontSize = value;
    }
  }

  internal virtual Label lblFontType
  {
    [DebuggerNonUserCode] get { return this._lblFontType; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblFontType = value;
    }
  }

  internal virtual TextBox txtFontType
  {
    [DebuggerNonUserCode] get { return this._txtFontType; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtFontType = value;
    }
  }

  internal virtual Label lblFontName
  {
    [DebuggerNonUserCode] get { return this._lblFontName; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblFontName = value;
    }
  }

  internal virtual Button btnFont
  {
    [DebuggerNonUserCode] get { return this._btnFont; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnFont_Click);
      if (this._btnFont != null)
        this._btnFont.Click -= eventHandler;
      this._btnFont = value;
      if (this._btnFont == null)
        return;
      this._btnFont.Click += eventHandler;
    }
  }

  internal virtual TextBox txtFontName
  {
    [DebuggerNonUserCode] get { return this._txtFontName; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtFontName = value;
    }
  }

  internal virtual Label lblFont
  {
    [DebuggerNonUserCode] get { return this._lblFont; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._lblFont = value; }
  }

  internal virtual Label lblLangName
  {
    [DebuggerNonUserCode] get { return this._lblLangName; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblLangName = value;
    }
  }

  internal virtual TextBox txtLangId
  {
    [DebuggerNonUserCode] get { return this._txtLangId; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtLangId = value;
    }
  }

  internal virtual Label lblLandId
  {
    [DebuggerNonUserCode] get { return this._lblLandId; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblLandId = value;
    }
  }

  internal virtual Button btnAdd
  {
    [DebuggerNonUserCode] get { return this._btnAdd; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnAdd_Click);
      if (this._btnAdd != null)
        this._btnAdd.Click -= eventHandler;
      this._btnAdd = value;
      if (this._btnAdd == null)
        return;
      this._btnAdd.Click += eventHandler;
    }
  }

  internal virtual Button btnDelete
  {
    [DebuggerNonUserCode] get { return this._btnDelete; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnDelete_Click);
      if (this._btnDelete != null)
        this._btnDelete.Click -= eventHandler;
      this._btnDelete = value;
      if (this._btnDelete == null)
        return;
      this._btnDelete.Click += eventHandler;
    }
  }

  internal virtual Button btnEdit
  {
    [DebuggerNonUserCode] get { return this._btnEdit; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnEdit_Click);
      if (this._btnEdit != null)
        this._btnEdit.Click -= eventHandler;
      this._btnEdit = value;
      if (this._btnEdit == null)
        return;
      this._btnEdit.Click += eventHandler;
    }
  }

  internal virtual Button btnExit
  {
    [DebuggerNonUserCode] get { return this._btnExit; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnExit_Click);
      if (this._btnExit != null)
        this._btnExit.Click -= eventHandler;
      this._btnExit = value;
      if (this._btnExit == null)
        return;
      this._btnExit.Click += eventHandler;
    }
  }

  internal virtual Button btnSave
  {
    [DebuggerNonUserCode] get { return this._btnSave; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnSave_Click);
      if (this._btnSave != null)
        this._btnSave.Click -= eventHandler;
      this._btnSave = value;
      if (this._btnSave == null)
        return;
      this._btnSave.Click += eventHandler;
    }
  }

  internal virtual DataGridView dgvLang
  {
    [DebuggerNonUserCode] get { return this._dgvLang; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      DataGridViewCellEventHandler cellEventHandler1 = new DataGridViewCellEventHandler(this.dgvLang_CellContentClick);
      DataGridViewCellEventHandler cellEventHandler2 = new DataGridViewCellEventHandler(this.dgvLang_RowEnter);
      if (this._dgvLang != null)
      {
        this._dgvLang.CellContentClick -= cellEventHandler1;
        this._dgvLang.RowEnter -= cellEventHandler2;
      }
      this._dgvLang = value;
      if (this._dgvLang == null)
        return;
      this._dgvLang.CellContentClick += cellEventHandler1;
      this._dgvLang.RowEnter += cellEventHandler2;
    }
  }

  internal virtual FontDialog FontDialog1
  {
    [DebuggerNonUserCode] get { return this._FontDialog1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._FontDialog1 = value;
    }
  }

  internal virtual DataGridViewTextBoxColumn lang_id
  {
    [DebuggerNonUserCode] get { return this._lang_id; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._lang_id = value; }
  }

  internal virtual DataGridViewTextBoxColumn lang_name
  {
    [DebuggerNonUserCode] get { return this._lang_name; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lang_name = value;
    }
  }

  internal virtual DataGridViewTextBoxColumn font_name
  {
    [DebuggerNonUserCode] get { return this._font_name; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._font_name = value;
    }
  }

  internal virtual DataGridViewTextBoxColumn font_type
  {
    [DebuggerNonUserCode] get { return this._font_type; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._font_type = value;
    }
  }

  internal virtual DataGridViewTextBoxColumn font_size
  {
    [DebuggerNonUserCode] get { return this._font_size; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._font_size = value;
    }
  }

  private void btnAdd_Click(object sender, EventArgs e)
  {
    if (this.dgvLang.Rows.Count < 3)
    {
      this.txtLangId.Enabled = true;
      this.btnSave.Enabled = true;
      this.btnEdit.Enabled = false;
      this.btnDelete.Enabled = false;
      this.lblFont.Enabled = true;
      this.lblFontName.Enabled = true;
      this.lblFontSize.Enabled = true;
      this.lblFontType.Enabled = true;
      this.lblLandId.Enabled = true;
      this.lblLangName.Enabled = true;
      this.txtLangId.Enabled = true;
      this.txtFontSize.Enabled = true;
      this.txtFontName.Enabled = true;
      this.txtFontType.Enabled = true;
      this.cmbLang.Enabled = true;
      this.btnFont.Enabled = true;
      this.txtFontName.Text = "";
      this.txtFontSize.Text = "";
      this.txtFontType.Text = "";
      this.txtLangId.Text = "";
      this.cmbLang.Text = "";
    }
    else
    {
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Only 3 Languages are supported,\r\nAlready 3 language are there u can modify those Languages", "Msg Box", 0, 0, 0);
      this.btnSave.Enabled = false;
    }
  }

  private void btnFont_Click(object sender, EventArgs e)
  {
    string empty = string.Empty;
    try
    {
      int num = (int) this.FontDialog1.ShowDialog();
      if (this.FontDialog1.Font.Style == FontStyle.Bold)
        this.txtFontType.Text = "Bold";
      else if (this.FontDialog1.Font.Style == FontStyle.Italic)
        this.txtFontType.Text = "Italic";
      else if (this.FontDialog1.Font.Style == FontStyle.Regular)
        this.txtFontType.Text = "Regular";
      else if ((double) this.FontDialog1.Font.Style == Conversions.ToDouble("3"))
        this.txtFontType.Text = "BoldItalic";
      this.txtFontName.Text = this.FontDialog1.Font.Name;
      this.txtFontSize.Text = Conversions.ToString(checked ((int) Math.Round((double) this.FontDialog1.Font.Size)));
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void btnEdit_Click(object sender, EventArgs e)
  {
    this.btnSave.Enabled = true;
    this.btnAdd.Enabled = false;
    this.btnDelete.Enabled = false;
    this.lblFont.Enabled = true;
    this.lblFontName.Enabled = true;
    this.lblFontSize.Enabled = true;
    this.lblFontType.Enabled = true;
    this.lblLandId.Enabled = true;
    this.lblLangName.Enabled = true;
    this.txtLangId.Enabled = true;
    this.txtFontSize.Enabled = true;
    this.txtFontName.Enabled = true;
    this.txtFontType.Enabled = true;
    this.cmbLang.Enabled = true;
    this.btnFont.Enabled = true;
    this.txtLangId.Enabled = false;
  }

  private void btnDelete_Click(object sender, EventArgs e)
  {
    this.btnSave.Enabled = false;
    this.btnEdit.Enabled = false;
    this.btnAdd.Enabled = false;
    this.lblFont.Enabled = true;
    this.lblFontName.Enabled = true;
    this.lblFontSize.Enabled = true;
    this.lblFontType.Enabled = true;
    this.lblLandId.Enabled = true;
    this.lblLangName.Enabled = true;
    this.txtLangId.Enabled = true;
    this.txtFontSize.Enabled = true;
    this.txtFontName.Enabled = true;
    this.txtFontType.Enabled = true;
    this.cmbLang.Enabled = true;
    this.btnFont.Enabled = true;
    try
    {
      int firstRow = this.dgvLang.Rows.GetFirstRow(DataGridViewElementStates.Selected);
      if (firstRow >= 0)
      {
        bool result = false;
        network_db_read.delete_lang(this.txtLangId.Text, ref result);
        if (result)
        {
          this.dgvLang.Rows.RemoveAt(firstRow);
          int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "deleted Successfully", "Msg Box", 0, 0, 0);
        }
        else
        {
          int num1 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Not Deleted", "Msg Box", 0, 0, 0);
        }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    this.lblFont.Enabled = true;
    this.lblFontName.Enabled = true;
    this.lblFontSize.Enabled = true;
    this.lblFontType.Enabled = true;
    this.lblLandId.Enabled = true;
    this.lblLangName.Enabled = true;
    this.txtLangId.Enabled = false;
    this.txtFontSize.Enabled = false;
    this.txtFontName.Enabled = false;
    this.txtFontType.Enabled = false;
    this.cmbLang.Enabled = false;
    this.btnFont.Enabled = false;
    this.btnSave.Enabled = false;
    this.btnEdit.Enabled = true;
    this.btnAdd.Enabled = true;
  }

  private void btnSave_Click(object sender, EventArgs e)
  {
    string[] strArray = new string[6];
    bool result = false;
    try
    {
      this.dgvLang.Rows.GetLastRow(DataGridViewElementStates.Displayed);
      if (this.btnAdd.Enabled)
      {
        if (frmMainFormIPIS.lang_cnt > (byte) 3)
          return;
        network_db_read.set_lang(this.txtLangId.Text, this.cmbLang.Text, this.txtFontName.Text, this.txtFontType.Text, Conversions.ToInteger(this.txtFontSize.Text));
        int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Message Added ", "Msg Box", 0, 0, 0);
        if (Operators.CompareString(this.cmbLang.Text, "English", false) == 0)
        {
          frmMainFormIPIS.eng_font_name = this.txtFontName.Text;
          frmMainFormIPIS.eng_font_type = this.txtFontType.Text;
          frmMainFormIPIS.eng_font_size = this.txtFontSize.Text;
        }
        else if (Operators.CompareString(this.cmbLang.Text, "Hindi", false) == 0)
        {
          frmMainFormIPIS.hindi_font_name = this.txtFontName.Text;
          frmMainFormIPIS.hindi_font_type = this.txtFontType.Text;
          frmMainFormIPIS.hindi_font_size = this.txtFontSize.Text;
        }
        else
        {
          frmMainFormIPIS.reg_font_name = this.txtFontName.Text;
          frmMainFormIPIS.reg_font_type = this.txtFontType.Text;
          frmMainFormIPIS.reg_font_size = this.txtFontSize.Text;
        }
        strArray[0] = this.txtLangId.Text;
        strArray[1] = this.cmbLang.Text;
        strArray[2] = this.txtFontName.Text;
        strArray[3] = this.txtFontType.Text;
        strArray[4] = this.txtFontSize.Text;
        this.dgvLang.Rows.Add((object[]) strArray);
        this.dgvLang[0, checked ((int) frmMainFormIPIS.lang_cnt - 1)].Value = (object) this.txtLangId.Text;
        this.dgvLang[1, checked ((int) frmMainFormIPIS.lang_cnt - 1)].Value = (object) this.cmbLang.Text;
        this.dgvLang[2, checked ((int) frmMainFormIPIS.lang_cnt - 1)].Value = (object) this.txtFontName.Text;
        this.dgvLang[3, checked ((int) frmMainFormIPIS.lang_cnt - 1)].Value = (object) this.txtFontType.Text;
        this.dgvLang[4, checked ((int) frmMainFormIPIS.lang_cnt - 1)].Value = (object) this.txtFontSize.Text;
        frmMainFormIPIS.language_id[checked ((int) frmMainFormIPIS.lang_cnt - 1)] = this.txtLangId.Text;
        frmMainFormIPIS.language_name[checked ((int) frmMainFormIPIS.lang_cnt - 1)] = this.cmbLang.Text;
        frmMainFormIPIS.language_font_name[checked ((int) frmMainFormIPIS.lang_cnt - 1)] = this.txtFontName.Text;
        frmMainFormIPIS.language_font_type[checked ((int) frmMainFormIPIS.lang_cnt - 1)] = this.txtFontType.Text;
        frmMainFormIPIS.language_font_size[checked ((int) frmMainFormIPIS.lang_cnt - 1)] = this.txtFontSize.Text;
      }
      else if (this.btnEdit.Enabled)
      {
        network_db_read.update_lang(this.txtLangId.Text, this.cmbLang.Text, this.txtFontName.Text, this.txtFontType.Text, Conversions.ToInteger(this.txtFontSize.Text), ref result);
        if (result)
        {
          int num1 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Updated Successfully", "Msg Box", 0, 0, 0);
        }
        else
        {
          int num2 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Not Updated ", "Msg Box", 0, 0, 0);
        }
        int count = this.dgvLang.Rows.Count;
        int rowIndex = 0;
        while (rowIndex < count && Operators.CompareString(this.txtLangId.Text, frmMainFormIPIS.language_id[rowIndex], false) != 0)
          checked { ++rowIndex; }
        frmMainFormIPIS.language_id[rowIndex] = this.txtLangId.Text;
        frmMainFormIPIS.language_name[rowIndex] = this.cmbLang.Text;
        frmMainFormIPIS.language_font_name[rowIndex] = this.txtFontName.Text;
        frmMainFormIPIS.language_font_type[rowIndex] = this.txtFontType.Text;
        frmMainFormIPIS.language_font_size[rowIndex] = this.txtFontSize.Text;
        this.dgvLang[0, rowIndex].Value = (object) this.txtLangId.Text;
        this.dgvLang[1, rowIndex].Value = (object) this.cmbLang.Text;
        this.dgvLang[2, rowIndex].Value = (object) this.txtFontName.Text;
        this.dgvLang[3, rowIndex].Value = (object) this.txtFontType.Text;
        this.dgvLang[4, rowIndex].Value = (object) this.txtFontSize.Text;
      }
      this.btnSave.Enabled = false;
      this.btnAdd.Enabled = true;
      this.btnDelete.Enabled = true;
      this.btnEdit.Enabled = true;
      this.lblFont.Enabled = true;
      this.lblFontName.Enabled = true;
      this.lblFontSize.Enabled = true;
      this.lblFontType.Enabled = true;
      this.lblLandId.Enabled = true;
      this.lblLangName.Enabled = true;
      this.txtLangId.Enabled = false;
      this.txtFontSize.Enabled = false;
      this.txtFontName.Enabled = false;
      this.txtFontType.Enabled = false;
      this.cmbLang.Enabled = false;
      this.btnFont.Enabled = false;
      if (Operators.CompareString(this.cmbLang.Text, "Hindi", false) == 0)
      {
        frmMainFormIPIS.hindi_font_name = this.txtFontName.Text;
        frmMainFormIPIS.hindi_font_type = this.txtFontType.Text;
        frmMainFormIPIS.hindi_font_size = this.txtFontSize.Text;
      }
      else
      {
        frmMainFormIPIS.reg_font_name = this.txtFontName.Text;
        frmMainFormIPIS.reg_font_type = this.txtFontType.Text;
        frmMainFormIPIS.reg_font_size = this.txtFontSize.Text;
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    Strings.Format((object) DateTime.Now.Date, "dd-MM-yyyy");
    try
    {
      string str = "Z:\\Database\\tbl_language.mdb";
      string sourceFileName = "C:\\IPIS\\Database\\tbl_language.mdb";
      if (!File.Exists(str))
        File.Create(str);
      bool overwrite = true;
      MyProject.Computer.FileSystem.CopyFile(sourceFileName, str, overwrite);
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
  }

  private void frmLanguage_Load(object sender, EventArgs e)
  {
    this.txtFontName.Text = "";
    this.txtFontSize.Text = "";
    this.txtFontType.Text = "";
    this.txtLangId.Text = "";
    this.cmbLang.Text = "";
    int index = 0;
    try
    {
      while (index < (int) frmMainFormIPIS.lang_cnt)
      {
        this.dgvLang.Rows.Add((object[]) new string[5]
        {
          frmMainFormIPIS.language_id[index],
          frmMainFormIPIS.language_name[index],
          frmMainFormIPIS.language_font_name[index],
          frmMainFormIPIS.language_font_type[index],
          frmMainFormIPIS.language_font_size[index]
        });
        checked { ++index; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void btnExit_Click(object sender, EventArgs e)
{
  this.Close();
}

  private void dgvLang_CellContentClick(object sender, DataGridViewCellEventArgs e)
  {
  }

  private void dgvLang_RowEnter(object sender, DataGridViewCellEventArgs e)
  {
    try
    {
      int firstRow = this.dgvLang.Rows.GetFirstRow(DataGridViewElementStates.Selected);
      if (this.dgvLang.Rows.Count != 0 & firstRow > 0)
      {
        this.txtLangId.Text = Conversions.ToString(this.dgvLang[0, firstRow].Value);
        this.cmbLang.Text = Conversions.ToString(this.dgvLang[1, firstRow].Value);
        this.txtFontName.Text = Conversions.ToString(this.dgvLang[2, firstRow].Value);
        this.txtFontType.Text = Conversions.ToString(this.dgvLang[3, firstRow].Value);
        this.txtFontSize.Text = Conversions.ToString(this.dgvLang[4, firstRow].Value);
      }
      else
      {
        this.txtLangId.Text = Conversions.ToString(this.dgvLang[0, 0].Value);
        this.cmbLang.Text = Conversions.ToString(this.dgvLang[1, 0].Value);
        this.txtFontName.Text = Conversions.ToString(this.dgvLang[2, 0].Value);
        this.txtFontType.Text = Conversions.ToString(this.dgvLang[3, 0].Value);
        this.txtFontSize.Text = Conversions.ToString(this.dgvLang[4, 0].Value);
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }
}

}