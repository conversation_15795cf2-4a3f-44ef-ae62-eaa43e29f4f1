// Decompiled with JetBrains decompiler
// Type: ipis.frmUserNamePassword
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using ipis.My;
using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

namespace ipis
{

[DesignerGenerated]
public class frmUserNamePassword : Form
{
  private static List<WeakReference> __ENCList = new List<WeakReference>();
  private IContainer components;
  [AccessedThroughProperty("btnExit")]
  private Button _btnExit;
  [AccessedThroughProperty("btnOk")]
  private Button _btnOk;
  [AccessedThroughProperty("txtPassword")]
  private TextBox _txtPassword;
  [AccessedThroughProperty("txtUserName")]
  private TextBox _txtUserName;
  [AccessedThroughProperty("PasswordLabel")]
  private Label _PasswordLabel;
  [AccessedThroughProperty("UsernameLabel")]
  private Label _UsernameLabel;
  [AccessedThroughProperty("lblGroup")]
  private Label _lblGroup;
  [AccessedThroughProperty("cmbGroup")]
  private ComboBox _cmbGroup;
  [AccessedThroughProperty("event_font")]
  private frmFont _event_font;
  [AccessedThroughProperty("event_network_mdch")]
  private frmNetworkMDCH _event_network_mdch;
  [AccessedThroughProperty("event_port_config")]
  private frmPortConfig _event_port_config;
  [AccessedThroughProperty("event_voice_config")]
  private frmVoice _event_voice_config;
  [AccessedThroughProperty("event_user_config")]
  private frmUser _event_user_config;
  [AccessedThroughProperty("addtrain_event")]
  private frmTrainConfig _addtrain_event;
  [AccessedThroughProperty("event_trainstatusmsg")]
  private frmTrainStatusMsg _event_trainstatusmsg;

  [DebuggerNonUserCode]
  static frmUserNamePassword()
  {
  }

  [DebuggerNonUserCode]
  public frmUserNamePassword()
  {
    this.FormClosed += new FormClosedEventHandler(this.frmUserNamePassword_FormClosed);
    frmUserNamePassword.__ENCAddToList((object) this);
    this.InitializeComponent();
  }

  [DebuggerNonUserCode]
  private static void __ENCAddToList(object value)
  {
    lock (frmUserNamePassword.__ENCList)
    {
      if (frmUserNamePassword.__ENCList.Count == frmUserNamePassword.__ENCList.Capacity)
      {
        int index1 = 0;
        int num = checked (frmUserNamePassword.__ENCList.Count - 1);
        int index2 = 0;
        while (index2 <= num)
        {
          if (frmUserNamePassword.__ENCList[index2].IsAlive)
          {
            if (index2 != index1)
              frmUserNamePassword.__ENCList[index1] = frmUserNamePassword.__ENCList[index2];
            checked { ++index1; }
          }
          checked { ++index2; }
        }
        frmUserNamePassword.__ENCList.RemoveRange(index1, checked (frmUserNamePassword.__ENCList.Count - index1));
        frmUserNamePassword.__ENCList.Capacity = frmUserNamePassword.__ENCList.Count;
      }
      frmUserNamePassword.__ENCList.Add(new WeakReference(RuntimeHelpers.GetObjectValue(value)));
    }
  }

  [DebuggerNonUserCode]
  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  [DebuggerStepThrough]
  private void InitializeComponent()
  {
    this.btnExit = new Button();
    this.btnOk = new Button();
    this.txtPassword = new TextBox();
    this.txtUserName = new TextBox();
    this.PasswordLabel = new Label();
    this.UsernameLabel = new Label();
    this.lblGroup = new Label();
    this.cmbGroup = new ComboBox();
    this.SuspendLayout();
    this.btnExit.BackColor = Color.SeaShell;
    this.btnExit.DialogResult = DialogResult.Cancel;
    this.btnExit.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnExit1 = this.btnExit;
    Point point1 = new Point(191, 198);
    Point point2 = point1;
    btnExit1.Location = point2;
    this.btnExit.Name = "btnExit";
    Button btnExit2 = this.btnExit;
    Size size1 = new Size(60, 25);
    Size size2 = size1;
    btnExit2.Size = size2;
    this.btnExit.TabIndex = 5;
    this.btnExit.Text = "Exit";
    this.btnExit.UseVisualStyleBackColor = false;
    this.btnOk.BackColor = Color.SeaShell;
    this.btnOk.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnOk1 = this.btnOk;
    point1 = new Point(107, 198);
    Point point3 = point1;
    btnOk1.Location = point3;
    this.btnOk.Name = "btnOk";
    Button btnOk2 = this.btnOk;
    size1 = new Size(60, 25);
    Size size3 = size1;
    btnOk2.Size = size3;
    this.btnOk.TabIndex = 4;
    this.btnOk.Text = "OK";
    this.btnOk.UseVisualStyleBackColor = false;
    this.txtPassword.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtPassword1 = this.txtPassword;
    point1 = new Point(191, 67);
    Point point4 = point1;
    txtPassword1.Location = point4;
    this.txtPassword.MaxLength = 15;
    this.txtPassword.Name = "txtPassword";
    this.txtPassword.PasswordChar = '*';
    TextBox txtPassword2 = this.txtPassword;
    size1 = new Size(144 /*0x90*/, 22);
    Size size4 = size1;
    txtPassword2.Size = size4;
    this.txtPassword.TabIndex = 2;
    this.txtUserName.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtUserName1 = this.txtUserName;
    point1 = new Point(191, 12);
    Point point5 = point1;
    txtUserName1.Location = point5;
    this.txtUserName.MaxLength = 15;
    this.txtUserName.Name = "txtUserName";
    TextBox txtUserName2 = this.txtUserName;
    size1 = new Size(144 /*0x90*/, 22);
    Size size5 = size1;
    txtUserName2.Size = size5;
    this.txtUserName.TabIndex = 1;
    this.PasswordLabel.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label passwordLabel1 = this.PasswordLabel;
    point1 = new Point(77, 67);
    Point point6 = point1;
    passwordLabel1.Location = point6;
    this.PasswordLabel.Name = "PasswordLabel";
    Label passwordLabel2 = this.PasswordLabel;
    size1 = new Size(76, 23);
    Size size6 = size1;
    passwordLabel2.Size = size6;
    this.PasswordLabel.TabIndex = 19;
    this.PasswordLabel.Text = "Password";
    this.PasswordLabel.TextAlign = ContentAlignment.MiddleLeft;
    this.UsernameLabel.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label usernameLabel1 = this.UsernameLabel;
    point1 = new Point(70, 12);
    Point point7 = point1;
    usernameLabel1.Location = point7;
    this.UsernameLabel.Name = "UsernameLabel";
    Label usernameLabel2 = this.UsernameLabel;
    size1 = new Size(83, 23);
    Size size7 = size1;
    usernameLabel2.Size = size7;
    this.UsernameLabel.TabIndex = 18;
    this.UsernameLabel.Text = "User name";
    this.UsernameLabel.TextAlign = ContentAlignment.MiddleLeft;
    this.lblGroup.AutoSize = true;
    this.lblGroup.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblGroup1 = this.lblGroup;
    point1 = new Point(103, 130);
    Point point8 = point1;
    lblGroup1.Location = point8;
    this.lblGroup.Name = "lblGroup";
    Label lblGroup2 = this.lblGroup;
    size1 = new Size(50, 16 /*0x10*/);
    Size size8 = size1;
    lblGroup2.Size = size8;
    this.lblGroup.TabIndex = 20;
    this.lblGroup.Text = "Group";
    this.cmbGroup.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.cmbGroup.FormattingEnabled = true;
    this.cmbGroup.Items.AddRange(new object[2]
    {
      (object) "Admin",
      (object) "Normal"
    });
    ComboBox cmbGroup1 = this.cmbGroup;
    point1 = new Point(191, (int) sbyte.MaxValue);
    Point point9 = point1;
    cmbGroup1.Location = point9;
    this.cmbGroup.Name = "cmbGroup";
    ComboBox cmbGroup2 = this.cmbGroup;
    size1 = new Size(144 /*0x90*/, 24);
    Size size9 = size1;
    cmbGroup2.Size = size9;
    this.cmbGroup.TabIndex = 3;
    this.AcceptButton = (IButtonControl) this.btnOk;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.BackColor = Color.PowderBlue;
    this.CancelButton = (IButtonControl) this.btnExit;
    size1 = new Size(357, 235);
    this.ClientSize = size1;
    this.Controls.Add((Control) this.cmbGroup);
    this.Controls.Add((Control) this.lblGroup);
    this.Controls.Add((Control) this.btnExit);
    this.Controls.Add((Control) this.btnOk);
    this.Controls.Add((Control) this.txtPassword);
    this.Controls.Add((Control) this.txtUserName);
    this.Controls.Add((Control) this.PasswordLabel);
    this.Controls.Add((Control) this.UsernameLabel);
    this.FormBorderStyle = FormBorderStyle.FixedToolWindow;
    this.Name = "frmUserNamePassword";
    this.Text = "User";
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  internal virtual Button btnExit
  {
    [DebuggerNonUserCode] get { return this._btnExit; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnExit_Click);
      if (this._btnExit != null)
        this._btnExit.Click -= eventHandler;
      this._btnExit = value;
      if (this._btnExit == null)
        return;
      this._btnExit.Click += eventHandler;
    }
  }

  internal virtual Button btnOk
  {
    [DebuggerNonUserCode] get { return this._btnOk; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnOk_Click);
      if (this._btnOk != null)
        this._btnOk.Click -= eventHandler;
      this._btnOk = value;
      if (this._btnOk == null)
        return;
      this._btnOk.Click += eventHandler;
    }
  }

  internal virtual TextBox txtPassword
  {
    [DebuggerNonUserCode] get { return this._txtPassword; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtPassword = value;
    }
  }

  internal virtual TextBox txtUserName
  {
    [DebuggerNonUserCode] get { return this._txtUserName; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtUserName = value;
    }
  }

  internal virtual Label PasswordLabel
  {
    [DebuggerNonUserCode] get { return this._PasswordLabel; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._PasswordLabel = value;
    }
  }

  internal virtual Label UsernameLabel
  {
    [DebuggerNonUserCode] get { return this._UsernameLabel; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._UsernameLabel = value;
    }
  }

  internal virtual Label lblGroup
  {
    [DebuggerNonUserCode] get { return this._lblGroup; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._lblGroup = value; }
  }

  internal virtual ComboBox cmbGroup
  {
    [DebuggerNonUserCode] get { return this._cmbGroup; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbGroup = value; }
  }

  protected virtual frmFont event_font
  {
    [DebuggerNonUserCode] get { return this._event_font; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._event_font = value;
    }
  }

  protected virtual frmNetworkMDCH event_network_mdch
  {
    [DebuggerNonUserCode] get { return this._event_network_mdch; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._event_network_mdch = value;
    }
  }

  protected virtual frmPortConfig event_port_config
  {
    [DebuggerNonUserCode] get { return this._event_port_config; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._event_port_config = value;
    }
  }

  protected virtual frmVoice event_voice_config
  {
    [DebuggerNonUserCode] get { return this._event_voice_config; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._event_voice_config = value;
    }
  }

  protected virtual frmUser event_user_config
  {
    [DebuggerNonUserCode] get { return this._event_user_config; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._event_user_config = value;
    }
  }

  protected virtual frmTrainConfig addtrain_event
  {
    [DebuggerNonUserCode] get { return this._addtrain_event; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._addtrain_event = value;
    }
  }

  protected virtual frmTrainStatusMsg event_trainstatusmsg
  {
    [DebuggerNonUserCode] get { return this._event_trainstatusmsg; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._event_trainstatusmsg = value;
    }
  }

  private void btnOk_Click(object sender, EventArgs e)
  {
    int index = 0;
    string empty = string.Empty;
    bool flag1 = false;
    try
    {
      bool flag2;
      if (MyProject.Forms.frmMainFormIPIS.NetworkToolStripMenuItem.CheckState == CheckState.Checked)
      {
        while (index < (int) frmMainFormIPIS.user_cnt.cnt)
        {
          network_db_read.dec_pwd(frmMainFormIPIS.user_details[index].pwd, ref empty, Conversions.ToString(frmMainFormIPIS.user_details[index].pwd_length));
          if (Operators.CompareString(this.txtUserName.Text, frmMainFormIPIS.user_details[index].user_name, false) == 0 & Operators.CompareString(this.txtPassword.Text, empty, false) == 0 & Operators.CompareString(Strings.Trim(this.cmbGroup.Text), "Admin", false) == 0 & Operators.CompareString(Strings.Trim(this.cmbGroup.Text), Strings.Trim(frmMainFormIPIS.user_details[index].group), false) == 0)
          {
            this.Close();
            if (!Information.IsNothing((object) this.event_network_mdch))
            {
              if (!this.event_network_mdch.IsDisposed)
              {
                this.event_network_mdch.WindowState = FormWindowState.Normal;
                this.event_network_mdch.BringToFront();
                this.event_network_mdch.Show();
              }
              else
              {
                this.event_network_mdch = new frmNetworkMDCH();
                this.event_network_mdch.Show();
              }
            }
            else
            {
              this.event_network_mdch = new frmNetworkMDCH();
              this.event_network_mdch.Show();
              this.event_network_mdch.BringToFront();
            }
            flag2 = true;
            MyProject.Forms.frmMainFormIPIS.NetworkToolStripMenuItem.CheckState = CheckState.Unchecked;
            this.Close();
            return;
          }
          checked { ++index; }
        }
        if (!flag1)
        {
          int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Please Check Name Or Password Entered Correctly\r\nCheck Whether CAPS Lock is ON", "Msg Box", 0, 0, 0);
          this.txtPassword.Text = string.Empty;
          this.txtUserName.Text = string.Empty;
        }
      }
      flag2 = false;
      bool flag3 = false;
      if (MyProject.Forms.frmMainFormIPIS.TrainToolStripMenuItem1.CheckState == CheckState.Checked)
      {
        while (index < (int) frmMainFormIPIS.user_cnt.cnt)
        {
          network_db_read.dec_pwd(frmMainFormIPIS.user_details[index].pwd, ref empty, Conversions.ToString(frmMainFormIPIS.user_details[index].pwd_length));
          if (Operators.CompareString(this.txtUserName.Text, frmMainFormIPIS.user_details[index].user_name, false) == 0 & Operators.CompareString(this.txtPassword.Text, empty, false) == 0 & Operators.CompareString(Strings.Trim(this.cmbGroup.Text), "Admin", false) == 0 & Operators.CompareString(Strings.Trim(this.cmbGroup.Text), Strings.Trim(frmMainFormIPIS.user_details[index].group), false) == 0)
          {
            if (!Information.IsNothing((object) this.addtrain_event))
            {
              if (!this.addtrain_event.IsDisposed)
              {
                this.addtrain_event.WindowState = FormWindowState.Normal;
                this.addtrain_event.BringToFront();
              }
              else
              {
                this.addtrain_event = new frmTrainConfig();
                this.addtrain_event.Show();
              }
            }
            else
            {
              this.addtrain_event = new frmTrainConfig();
              this.addtrain_event.Show();
              this.addtrain_event.BringToFront();
            }
            flag2 = true;
            MyProject.Forms.frmMainFormIPIS.TrainToolStripMenuItem1.CheckState = CheckState.Unchecked;
            this.Close();
            return;
          }
          checked { ++index; }
        }
        if (!flag3)
        {
          int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Please Check Name Or Password Entered Correctly\r\nCheck Whether CAPS Lock is ON", "Msg Box", 0, 0, 0);
          this.txtPassword.Text = string.Empty;
          this.txtUserName.Text = string.Empty;
        }
        else
          this.Close();
      }
      bool flag4 = false;
      if (MyProject.Forms.frmMainFormIPIS.VoiceToolStripMenuItem.CheckState == CheckState.Checked)
      {
        while (index < (int) frmMainFormIPIS.user_cnt.cnt)
        {
          network_db_read.dec_pwd(frmMainFormIPIS.user_details[index].pwd, ref empty, Conversions.ToString(frmMainFormIPIS.user_details[index].pwd_length));
          if (Operators.CompareString(this.txtUserName.Text, frmMainFormIPIS.user_details[index].user_name, false) == 0 & Operators.CompareString(this.txtPassword.Text, empty, false) == 0 & Operators.CompareString(Strings.Trim(this.cmbGroup.Text), "Admin", false) == 0 & Operators.CompareString(Strings.Trim(this.cmbGroup.Text), Strings.Trim(frmMainFormIPIS.user_details[index].group), false) == 0)
          {
            if (!Information.IsNothing((object) this.event_voice_config))
            {
              if (!this.event_voice_config.IsDisposed)
              {
                this.event_voice_config.WindowState = FormWindowState.Normal;
                this.event_voice_config.BringToFront();
              }
              else
              {
                this.event_voice_config = new frmVoice();
                this.event_voice_config.Show();
              }
            }
            else
            {
              this.event_voice_config = new frmVoice();
              this.event_voice_config.Show();
              this.event_voice_config.BringToFront();
            }
            flag2 = true;
            MyProject.Forms.frmMainFormIPIS.VoiceToolStripMenuItem.CheckState = CheckState.Unchecked;
            this.Close();
            return;
          }
          checked { ++index; }
        }
        if (!flag4)
        {
          int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Please Check Name Or Password Entered Correctly\r\nCheck Whether CAPS Lock is ON", "Msg Box", 0, 0, 0);
          this.txtPassword.Text = string.Empty;
          this.txtUserName.Text = string.Empty;
        }
      }
      bool flag5 = false;
      if (MyProject.Forms.frmMainFormIPIS.UserToolStripMenuItem1.CheckState == CheckState.Checked)
      {
        while (index < (int) frmMainFormIPIS.user_cnt.cnt)
        {
          network_db_read.dec_pwd(frmMainFormIPIS.user_details[index].pwd, ref empty, Conversions.ToString(frmMainFormIPIS.user_details[index].pwd_length));
          if (Operators.CompareString(this.txtUserName.Text, frmMainFormIPIS.user_details[index].user_name, false) == 0 & Operators.CompareString(this.txtPassword.Text, empty, false) == 0 & Operators.CompareString(Strings.Trim(this.cmbGroup.Text), "Admin", false) == 0 & Operators.CompareString(Strings.Trim(this.cmbGroup.Text), Strings.Trim(frmMainFormIPIS.user_details[index].group), false) == 0)
          {
            if (!Information.IsNothing((object) this.event_user_config))
            {
              if (!this.event_voice_config.IsDisposed)
              {
                this.event_user_config.WindowState = FormWindowState.Normal;
                this.event_user_config.BringToFront();
              }
              else
              {
                this.event_user_config = new frmUser();
                this.event_user_config.Show();
              }
            }
            else
            {
              this.event_user_config = new frmUser();
              this.event_user_config.Show();
              this.event_user_config.BringToFront();
            }
            flag2 = true;
            MyProject.Forms.frmMainFormIPIS.UserToolStripMenuItem1.CheckState = CheckState.Unchecked;
            this.Close();
            return;
          }
          checked { ++index; }
        }
        if (!flag5)
        {
          int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Please Check Name Or Password Entered Correctly\r\nCheck Whether CAPS Lock is ON", "Msg Box", 0, 0, 0);
          this.txtPassword.Text = string.Empty;
          this.txtUserName.Text = string.Empty;
        }
      }
      flag2 = false;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void btnExit_Click(object sender, EventArgs e)
  {
    if (MyProject.Forms.frmMainFormIPIS.NetworkToolStripMenuItem.CheckState == CheckState.Checked)
      MyProject.Forms.frmMainFormIPIS.NetworkToolStripMenuItem.CheckState = CheckState.Unchecked;
    else if (MyProject.Forms.frmMainFormIPIS.TrainToolStripMenuItem1.CheckState == CheckState.Checked)
      MyProject.Forms.frmMainFormIPIS.TrainToolStripMenuItem1.CheckState = CheckState.Unchecked;
    else if (MyProject.Forms.frmMainFormIPIS.VoiceToolStripMenuItem.CheckState == CheckState.Checked)
      MyProject.Forms.frmMainFormIPIS.VoiceToolStripMenuItem.CheckState = CheckState.Unchecked;
    else if (MyProject.Forms.frmMainFormIPIS.UserToolStripMenuItem1.CheckState == CheckState.Checked)
      MyProject.Forms.frmMainFormIPIS.UserToolStripMenuItem1.CheckState = CheckState.Unchecked;
    this.Close();
  }

  private void frmUserNamePassword_FormClosed(object sender, FormClosedEventArgs e)
  {
    MyProject.Forms.frmMainFormIPIS.VoiceToolStripMenuItem.CheckState = CheckState.Unchecked;
    MyProject.Forms.frmMainFormIPIS.NetworkToolStripMenuItem.CheckState = CheckState.Unchecked;
    MyProject.Forms.frmMainFormIPIS.TrainToolStripMenuItem1.CheckState = CheckState.Unchecked;
    MyProject.Forms.frmMainFormIPIS.UserToolStripMenuItem1.CheckState = CheckState.Unchecked;
  }
}

}