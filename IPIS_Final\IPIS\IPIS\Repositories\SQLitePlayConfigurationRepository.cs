using System.Data;
using System.Data.SQLite;
using IPIS.Repositories.Interfaces;
using IPIS.Utils;

namespace IPIS.Repositories
{
    public class SQLitePlayConfigurationRepository : IPlayConfigurationRepository
    {
        private readonly string connectionString;

        public SQLitePlayConfigurationRepository()
        {
            connectionString = Database.ConnectionString;
        }

        public DataTable GetPlayConfiguration(string trainStatus)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = "SELECT * FROM Play_Configuration WHERE Train_Status = @TrainStatus";
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@TrainStatus", trainStatus);
                    using (var adapter = new SQLiteDataAdapter(command))
                    {
                        var dataTable = new DataTable();
                        adapter.Fill(dataTable);
                        return dataTable;
                    }
                }
            }
        }

        public void AddPlayConfiguration(string trainStatus, string trainType, bool pfAvl, bool[] languages)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = @"
                    INSERT INTO Play_Configuration (
                        Train_Status, Train_Type, PF_Avl,
                        English, Hindi, Bengali, Tamil, Telugu,
                        Kannada, Malayalam, Marathi, Gujarati, Punjabi
                    ) VALUES (
                        @TrainStatus, @TrainType, @PFAvl,
                        @English, @Hindi, @Bengali, @Tamil, @Telugu,
                        @Kannada, @Malayalam, @Marathi, @Gujarati, @Punjabi
                    )";

                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@TrainStatus", trainStatus);
                    command.Parameters.AddWithValue("@TrainType", trainType);
                    command.Parameters.AddWithValue("@PFAvl", pfAvl);
                    command.Parameters.AddWithValue("@English", languages[0]);
                    command.Parameters.AddWithValue("@Hindi", languages[1]);
                    command.Parameters.AddWithValue("@Bengali", languages[2]);
                    command.Parameters.AddWithValue("@Tamil", languages[3]);
                    command.Parameters.AddWithValue("@Telugu", languages[4]);
                    command.Parameters.AddWithValue("@Kannada", languages[5]);
                    command.Parameters.AddWithValue("@Malayalam", languages[6]);
                    command.Parameters.AddWithValue("@Marathi", languages[7]);
                    command.Parameters.AddWithValue("@Gujarati", languages[8]);
                    command.Parameters.AddWithValue("@Punjabi", languages[9]);
                    command.ExecuteNonQuery();
                }
            }
        }

        public void UpdatePlayConfiguration(string trainStatus, string trainType, bool pfAvl, bool[] languages)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = @"
                    UPDATE Play_Configuration 
                    SET Train_Type = @TrainType,
                        PF_Avl = @PFAvl,
                        English = @English,
                        Hindi = @Hindi,
                        Bengali = @Bengali,
                        Tamil = @Tamil,
                        Telugu = @Telugu,
                        Kannada = @Kannada,
                        Malayalam = @Malayalam,
                        Marathi = @Marathi,
                        Gujarati = @Gujarati,
                        Punjabi = @Punjabi
                    WHERE Train_Status = @TrainStatus";

                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@TrainStatus", trainStatus);
                    command.Parameters.AddWithValue("@TrainType", trainType);
                    command.Parameters.AddWithValue("@PFAvl", pfAvl);
                    command.Parameters.AddWithValue("@English", languages[0]);
                    command.Parameters.AddWithValue("@Hindi", languages[1]);
                    command.Parameters.AddWithValue("@Bengali", languages[2]);
                    command.Parameters.AddWithValue("@Tamil", languages[3]);
                    command.Parameters.AddWithValue("@Telugu", languages[4]);
                    command.Parameters.AddWithValue("@Kannada", languages[5]);
                    command.Parameters.AddWithValue("@Malayalam", languages[6]);
                    command.Parameters.AddWithValue("@Marathi", languages[7]);
                    command.Parameters.AddWithValue("@Gujarati", languages[8]);
                    command.Parameters.AddWithValue("@Punjabi", languages[9]);
                    command.ExecuteNonQuery();
                }
            }
        }

        public void DeletePlayConfiguration(string trainStatus)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = "DELETE FROM Play_Configuration WHERE Train_Status = @TrainStatus";
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@TrainStatus", trainStatus);
                    command.ExecuteNonQuery();
                }
            }
        }

        public DataTable GetAllPlayConfigurations()
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = "SELECT * FROM Play_Configuration";
                using (var command = new SQLiteCommand(query, connection))
                {
                    using (var adapter = new SQLiteDataAdapter(command))
                    {
                        var dataTable = new DataTable();
                        adapter.Fill(dataTable);
                        return dataTable;
                    }
                }
            }
        }
    }
} 