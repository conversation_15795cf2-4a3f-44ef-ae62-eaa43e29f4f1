using System;
using System.Collections.Generic; // Required for List in BoardStatus
using System.Threading.Tasks;
using ipis_V2_jules.DisplayFormatters;
using ipis_V2_jules.Hardware.Protocols;

namespace ipis_V2_jules.Hardware.Clients
{
    public class AgdbClient : IBoardClient
    {
        private readonly ICommunicationService _communicationService;
        private readonly IDisplayDataFormatter _dataFormatter; // Specifically AgdbDataFormatter

        public AgdbClient(ICommunicationService communicationService, IDisplayDataFormatter dataFormatter)
        {
            _communicationService = communicationService ?? throw new ArgumentNullException(nameof(communicationService));
            if (!(dataFormatter is AgdbDataFormatter))
            {
                // Or log a warning and use a default, but throwing ensures correct setup.
                throw new ArgumentException("AgdbClient requires an AgdbDataFormatter.", nameof(dataFormatter));
            }
            _dataFormatter = dataFormatter;
        }

        public async Task<bool> SendMessageAsync(FormattedDisplayData data, byte boardAddress, byte subAddress, byte serialNo)
        {
            Console.WriteLine($"AGDB Client: Sending message to address {boardAddress}, sub-address {subAddress}, serial {serialNo}.");
            if (data.Line1 == null && data.Line2 == null) {
                Console.WriteLine("AGDB Client: No data in Line1 or Line2 to send.");
                return false;
            }

            // AGDBs typically display text. The AgdbDataFormatter should have converted text to byte patterns.
            // This client might need to handle multi-line displays by sending separate packets or
            // relying on the formatter to combine lines with control codes if the AGDB supports it.
            // For simplicity, sending Line1. If Line2 exists, it might be for a separate command or ignored for now.
            byte[] payload = data.Line1 ?? data.Line2 ?? Array.Empty<byte>();
            if (data.Line1 != null && data.Line2 != null)
            {
                // TODO: Implement logic for two-line AGDB display.
                // This might involve sending Line1, then Line2 in separate commands,
                // or a specific format in the payload if the board supports it.
                // For now, just sending Line1.
                Console.WriteLine("AGDB Client: Line2 data provided but current implementation only sends Line1 for AGDB. Line2 will be ignored.");
                payload = data.Line1;
            }


            byte functionCode = 0x01; // Example function code for "display text/pattern"
            byte[] packet = DisplayPacketBuilder.BuildMessagePacket(boardAddress, subAddress, serialNo, functionCode, payload, data.AdditionalHeaderBytes);

            bool success = _communicationService.SendData(packet);
            return await Task.FromResult(success);
        }

        public async Task<bool> ClearDisplayAsync(byte boardAddress, byte subAddress, byte serialNo)
        {
            Console.WriteLine($"AGDB Client: Clearing display for address {boardAddress}, sub-address {subAddress}, serial {serialNo}.");
            byte[] packet = DisplayPacketBuilder.BuildClearDisplayPacket(boardAddress, subAddress, serialNo);
            bool success = _communicationService.SendData(packet);
            return await Task.FromResult(success);
        }

        public async Task<BoardStatus> CheckLinkAsync(byte boardAddress, byte subAddress, byte serialNo)
        {
            Console.WriteLine($"AGDB Client: Checking link for address {boardAddress}, sub-address {subAddress}, serial {serialNo}.");
            byte[] packet = DisplayPacketBuilder.BuildLinkCheckPacket(boardAddress, subAddress, serialNo);
            _communicationService.SendData(packet);

            await Task.Delay(50);
            return new BoardStatus { IsLinkOk = true, StatusMessage = "Link check placeholder: OK (No actual response parsing)" };
        }

        public async Task<bool> SetConfigurationAsync(byte boardAddress, byte subAddress, byte serialNo, byte[] configData)
        {
            Console.WriteLine($"AGDB Client: Setting configuration for address {boardAddress}, sub-address {subAddress}, serial {serialNo}.");
            byte[] packet = DisplayPacketBuilder.BuildSetConfigPacket(boardAddress, subAddress, serialNo, configData);
            bool success = _communicationService.SendData(packet);
            return await Task.FromResult(success);
        }

        public async Task<byte[]> GetConfigurationAsync(byte boardAddress, byte subAddress, byte serialNo)
        {
            Console.WriteLine($"AGDB Client: Getting configuration for address {boardAddress}, sub-address {subAddress}, serial {serialNo}.");
            byte[] packet = DisplayPacketBuilder.BuildGetConfigPacket(boardAddress, subAddress, serialNo);
            _communicationService.SendData(packet);
            await Task.Delay(50);
            return await Task.FromResult(Array.Empty<byte>());
        }

        public async Task<bool> ResetBoardAsync(byte boardAddress, byte subAddress, byte serialNo)
        {
            Console.WriteLine($"AGDB Client: Resetting board address {boardAddress}, sub-address {subAddress}, serial {serialNo}.");
            byte[] packet = DisplayPacketBuilder.BuildResetPacket(boardAddress, subAddress, serialNo);
            bool success = _communicationService.SendData(packet);
            return await Task.FromResult(success);
        }
    }
}
