﻿using System;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Windows.Forms;

namespace Announcement
{
	// Token: 0x0200000B RID: 11
	public partial class Reports : Form
	{
		// Token: 0x06000046 RID: 70 RVA: 0x0000B0CB File Offset: 0x000092CB
		public Reports()
		{
			this.InitializeComponent();
		}

		// Token: 0x06000047 RID: 71 RVA: 0x000025C1 File Offset: 0x000007C1
		private void dateTimePicker2_ValueChanged(object sender, EventArgs e)
		{
		}

		// Token: 0x06000048 RID: 72 RVA: 0x000025C4 File Offset: 0x000007C4
		private void BTN_Exit_Click(object sender, EventArgs e)
		{
			base.Close();
		}

		// Token: 0x06000049 RID: 73 RVA: 0x0000B0FC File Offset: 0x000092FC
		private void BTN_Gen_Click(object sender, EventArgs e)
		{
			this.BTN_Gen.Enabled = false;
			string path = "";
			bool flag = this.DTP_FDate.Value.Date > this.DTP_TDate.Value.Date;
			if (flag)
			{
				MessageBox.Show("From Date Cannot be greater then To Date...");
			}
			else
			{
				this.Dgv_Report.Rows.Clear();
				this.Dgv_Report.Visible = true;
				this.Dgv_Report.RowHeadersVisible = false;
				int num = 0;
				while (this.DTP_FDate.Value.Date <= this.DTP_TDate.Value.Date)
				{
					string text = this.CB_RType.Text;
					string a = text;
					if (!(a == "Announcment"))
					{
						if (!(a == "Advertisement"))
						{
							if (!(a == "Slogans"))
							{
								if (a == "User Login")
								{
									path = Application.StartupPath + "\\Data\\Logs\\USER\\" + this.DTP_FDate.Value.ToString("dd-MM-yyyy") + ".txt";
									try
									{
										this.Report_Table.Columns.Add("Date");
										this.Report_Table.Columns.Add("Time");
										this.Report_Table.Columns.Add("User Detail");
									}
									catch
									{
									}
								}
							}
							else
							{
								path = Application.StartupPath + "\\Data\\Logs\\ANN\\" + this.DTP_FDate.Value.ToString("dd-MM-yyyy") + ".txt";
								try
								{
									this.Report_Table.Columns.Add("Date");
									this.Report_Table.Columns.Add("Time");
									this.Report_Table.Columns.Add("Slogan Details");
								}
								catch
								{
								}
							}
						}
						else
						{
							path = Application.StartupPath + "\\Data\\Logs\\ANN\\" + this.DTP_FDate.Value.ToString("dd-MM-yyyy") + ".txt";
							try
							{
								this.Report_Table.Columns.Add("Date");
								this.Report_Table.Columns.Add("Time");
								this.Report_Table.Columns.Add("Advertisement Details");
							}
							catch
							{
							}
						}
					}
					else
					{
						path = Application.StartupPath + "\\Data\\Logs\\ANN\\" + this.DTP_FDate.Value.ToString("dd-MM-yyyy") + ".txt";
						try
						{
							this.Report_Table.Columns.Add("Date");
							this.Report_Table.Columns.Add("Time");
							this.Report_Table.Columns.Add("Announcment Details");
						}
						catch
						{
						}
					}
					try
					{
						using (StreamReader streamReader = new StreamReader(path))
						{
							string text2;
							while ((text2 = streamReader.ReadLine()) != null)
							{
								string[] array = text2.Split(new char[]
								{
									','
								});
								bool flag2 = this.CB_RType.Text == "Announcment";
								if (flag2)
								{
									bool flag3 = array[2] == "";
									if (flag3)
									{
										array[2] = "PA MSG#" + array[3] + "#######";
									}
									array[2] = string.Concat(new string[]
									{
										array[0],
										"#",
										array[1],
										"#",
										array[2]
									});
									string[] array2 = array[2].Split(new char[]
									{
										'#'
									});
									DataRowCollection rows = this.Report_Table.Rows;
									object[] values = array2;
									rows.Add(values);
								}
								bool flag4 = this.CB_RType.Text == "Advertisement" || this.CB_RType.Text == "Slogans";
								if (flag4)
								{
									bool flag5 = text2.Contains(this.CB_RType.Text);
									if (flag5)
									{
										array[2] = string.Concat(new string[]
										{
											array[0],
											"#",
											array[1],
											"#",
											array[2]
										});
										string[] array3 = array[2].Split(new char[]
										{
											'#'
										});
										DataRowCollection rows2 = this.Report_Table.Rows;
										object[] values = array3;
										rows2.Add(values);
									}
								}
								else
								{
									this.Report_Table.Rows.Add(new object[]
									{
										array[0],
										array[1],
										array[2]
									});
								}
								num++;
							}
						}
					}
					catch (Exception ex)
					{
						Misc_Functions misc_Functions = new Misc_Functions();
						misc_Functions.Write_Log("ERROR", null, "Error - " + ex.ToString());
					}
					this.DTP_FDate.Value = this.DTP_FDate.Value.Date.AddDays(1.0);
				}
				this.Dgv_Report.DataSource = this.Report_Table;
				this.Dgv_Report.Columns["Date"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
				this.Dgv_Report.Columns["Time"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
				bool flag6 = this.CB_RType.Text == "Announcment";
				if (flag6)
				{
					this.Dgv_Report.Columns["Announcment Details"].Width = 70;
					this.Dgv_Report.Columns["Date"].Width = 200;
					this.Dgv_Report.Columns["Time"].Width = 100;
				}
				else
				{
					bool flag7 = this.CB_RType.Text == "Advertising";
					if (flag7)
					{
						this.Dgv_Report.Columns["Ann Type"].Width = 60;
						this.Dgv_Report.Columns["Ann Name"].Width = 35;
						this.Dgv_Report.Columns["Ann Type"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
						this.Dgv_Report.Columns["Ann Name"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
					}
					else
					{
						this.Dgv_Report.Columns["Date"].Width = 200;
						this.Dgv_Report.Columns["Time"].Width = 100;
					}
				}
				this.Dgv_Report.RefreshEdit();
			}
		}

		// Token: 0x0600004A RID: 74 RVA: 0x000025C1 File Offset: 0x000007C1
		private void Reports_Load(object sender, EventArgs e)
		{
		}

		// Token: 0x0600004B RID: 75 RVA: 0x000025C1 File Offset: 0x000007C1
		private void Dgv_Report_CellContentClick(object sender, DataGridViewCellEventArgs e)
		{
		}

		// Token: 0x0600004C RID: 76 RVA: 0x000025C1 File Offset: 0x000007C1
		private void Grp_Report_Enter(object sender, EventArgs e)
		{
		}

		// Token: 0x0600004D RID: 77 RVA: 0x0000B89C File Offset: 0x00009A9C
		private void BTN_Save_Click(object sender, EventArgs e)
		{
			this.Save_FileDialog.FilterIndex = 1;
			this.Save_FileDialog.Filter = "CSV files (*.csv)| *.csv";
			this.Save_FileDialog.RestoreDirectory = true;
			bool flag = this.Save_FileDialog.ShowDialog() == DialogResult.OK;
			if (flag)
			{
				bool flag2 = !File.Exists(this.Save_FileDialog.FileName);
				FileStream fileStream;
				StreamWriter streamWriter;
				if (flag2)
				{
					fileStream = new FileStream(this.Save_FileDialog.FileName, FileMode.Create, FileAccess.Write, FileShare.None);
					streamWriter = new StreamWriter(fileStream);
					string text = this.CB_RType.Text + " Report for Advertising";
					streamWriter.WriteLine(text);
					string text2 = this.CB_RType.Text;
					string a = text2;
					if (!(a == "Loging Details"))
					{
						if (!(a == "Train Transmission"))
						{
							if (a == "Announcment")
							{
								text = "Date,Time,ANN Type, Name";
							}
						}
						else
						{
							text = "Date,Time,Train_No,Train Name,Train_A/D,Train Status,Exp Arr Time,Exp Dep Time,Late Time,PF No,City";
						}
					}
					else
					{
						text = "Date,Time,Login/Out";
					}
					streamWriter.WriteLine(text);
				}
				else
				{
					fileStream = new FileStream(this.Save_FileDialog.FileName, FileMode.Append, FileAccess.Write, FileShare.None);
					streamWriter = new StreamWriter(fileStream);
				}
				for (int i = 0; i < this.Dgv_Report.Rows.Count - 1; i++)
				{
					string text = "";
					for (int j = 0; j < this.Dgv_Report.Columns.Count; j++)
					{
						text = text + this.Dgv_Report.Rows[i].Cells[j].Value.ToString() + ",";
					}
					streamWriter.WriteLine(text);
				}
				streamWriter.Flush();
				streamWriter.Close();
				fileStream.Close();
				MessageBox.Show("File Saved Sucessfully");
			}
		}

		// Token: 0x0600004E RID: 78 RVA: 0x000025C1 File Offset: 0x000007C1
		private void CB_RType_SelectedIndexChanged(object sender, EventArgs e)
		{
		}

		// Token: 0x0600004F RID: 79 RVA: 0x000025C1 File Offset: 0x000007C1
		private void DTP_FDate_ValueChanged(object sender, EventArgs e)
		{
		}

		// Token: 0x04000054 RID: 84
		private Class_Database DB = new Class_Database();

		// Token: 0x04000055 RID: 85
		public DataTable Report_Table = new DataTable();
	}
}
