using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ipis_V2_jules.DisplayFormatters; // For TaddbDataFormatter, FormattedDisplayData
using ipis_V2_jules.Services.DisplayBoard.Hardware.Protocols; // For DisplayPacketBuilder
using ipis_V2_jules.Services.DisplayBoard.Hardware.Communication; // For ICommunicationService
using ipis_V2_jules.Models; // For DisplayBoardConfig
using ipis_V2_jules.ApiClients; // For TrainDataErail

// Assuming TaddbDataFormatter is in ipis_V2_jules.Services.DisplayBoard.DisplayFormatters (Turn 51-53)

namespace ipis_V2_jules.Services.DisplayBoard.Hardware.Clients
{
    public class TaddbClient : IBoardClient
    {
        private readonly ICommunicationService _communicationService;
        private readonly TaddbDataFormatter _dataFormatter;
        private readonly DisplayBoardConfig _boardConfig;

        public BoardStatus Status { get; private set; }

        // Using placeholder command codes from DisplayPacketBuilder for TADDB lines
        // These were defined as: CMD_TADDB_DISPLAY_LINE_1 = 0x61, etc.

        public TaddbClient(ICommunicationService communicationService,
                           TaddbDataFormatter dataFormatter,
                           DisplayBoardConfig boardConfig)
        {
            _communicationService = communicationService ?? throw new ArgumentNullException(nameof(communicationService));
            _dataFormatter = dataFormatter ?? throw new ArgumentNullException(nameof(dataFormatter));
            _boardConfig = boardConfig ?? throw new ArgumentNullException(nameof(boardConfig));
            Status = new BoardStatus { IsLinkOk = false, StatusMessage = "Initialized", FirmwareVersion = "N/A" };
        }

        private Dictionary<string, string> GetBoardConfigAsDictionary()
        {
            return _boardConfig.ToDictionary();
        }

        private async Task<bool> SendSingleLineDataAsync(byte[] lineBitmapData, byte lineCommandCode)
        {
            if (lineBitmapData == null || lineBitmapData.Length == 0)
            {
                Console.WriteLine($"TADDB Client ({_boardConfig.BoardName}): No bitmap data for line command {lineCommandCode}. Sending empty payload if protocol requires.");
                lineBitmapData = Array.Empty<byte>();
            }

            byte[] packet = DisplayPacketBuilder.BuildTaddbBitmapDisplayPacket(
                _boardConfig.BoardId,
                lineCommandCode,
                lineBitmapData
            );

            try
            {
                await _communicationService.WriteDataAsync(packet);
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"TADDB Client ({_boardConfig.BoardName}) Error: Failed to send line data (Cmd:{lineCommandCode}). Exception: {ex.Message}");
                Status = new BoardStatus { IsLinkOk = false, StatusMessage = $"Send line (Cmd:{lineCommandCode}) failed: {ex.Message}", FirmwareVersion = Status.FirmwareVersion };
                return false;
            }
        }

        public async Task<bool> SendMessageAsync(FormattedDisplayData data, byte boardAddress, byte subAddress, byte serialNo)
        {
            if (boardAddress != _boardConfig.BoardId)
            {
                Console.WriteLine($"TADDB Client ({_boardConfig.BoardName}) Error: Mismatched boardAddress ({boardAddress}). Expected {_boardConfig.BoardId}.");
                Status = new BoardStatus { IsLinkOk = false, StatusMessage = "Config error: Board ID mismatch.", FirmwareVersion = Status.FirmwareVersion };
                return false;
            }

            bool overallSuccess = true;
            // Use placeholder command codes from DisplayPacketBuilder or define them here if specific
            // For now, assuming DisplayPacketBuilder has CMD_TADDB_DISPLAY_LINE_1, _2, _3
            byte[] taddbLineCommands = new byte[] {
                DisplayPacketBuilder.CMD_TADDB_DISPLAY_LINE_1,
                DisplayPacketBuilder.CMD_TADDB_DISPLAY_LINE_2,
                DisplayPacketBuilder.CMD_TADDB_DISPLAY_LINE_3
            };


            if (data.Line1 != null)
            {
                Console.WriteLine($"TADDB Client ({_boardConfig.BoardName}): Sending Line 1 data.");
                if (!await SendSingleLineDataAsync(data.Line1, taddbLineCommands[0])) overallSuccess = false;
            }
            if (overallSuccess && data.Line2 != null)
            {
                Console.WriteLine($"TADDB Client ({_boardConfig.BoardName}): Sending Line 2 data.");
                if (!await SendSingleLineDataAsync(data.Line2, taddbLineCommands[1])) overallSuccess = false;
            }
            if (overallSuccess && data.Line3 != null)
            {
                Console.WriteLine($"TADDB Client ({_boardConfig.BoardName}): Sending Line 3 data.");
                if (!await SendSingleLineDataAsync(data.Line3, taddbLineCommands[2])) overallSuccess = false;
            }
            // Extend for more lines if TADDB supports and FormattedDisplayData/taddbLineCommands are expanded

            if (overallSuccess)
            {
                Status = new BoardStatus { IsLinkOk = true, StatusMessage = "Message sent successfully.", FirmwareVersion = Status.FirmwareVersion };
            }
            return overallSuccess;
        }

        public async Task<bool> SendMessageAsync(string message)
        {
            if (string.IsNullOrEmpty(message)) return false;
            Console.WriteLine($"TADDB Client ({_boardConfig.BoardName}): Formatting and sending message: \"{message.Substring(0, Math.Min(message.Length, 20))}...\"");
            FormattedDisplayData formattedData = _dataFormatter.FormatMessage(message, GetBoardConfigAsDictionary());
            return await SendMessageAsync(formattedData, _boardConfig.BoardId, 0, 0);
        }

        public async Task<bool> UpdateTrainDisplayAsync(TrainDataErail trainData, Dictionary<string, string> platformInfo)
        {
            if (trainData == null) return false;
            Console.WriteLine($"TADDB Client ({_boardConfig.BoardName}): Formatting and sending train data for {trainData.TrainNo}");
            FormattedDisplayData formattedData = _dataFormatter.FormatTrainData(trainData, platformInfo, GetBoardConfigAsDictionary());
            return await SendMessageAsync(formattedData, _boardConfig.BoardId, 0, 0);
        }

        public async Task<bool> ClearDisplayAsync() => await ClearDisplayAsync(_boardConfig.BoardId, 0, 0);

        public async Task<bool> ClearDisplayAsync(byte boardAddress, byte subAddress, byte serialNo)
        {
            if (boardAddress != _boardConfig.BoardId)
            {
                 Console.WriteLine($"TADDB Client ({_boardConfig.BoardName}) Error: Mismatched boardAddress ({boardAddress}) for clear.");
                 Status = new BoardStatus { IsLinkOk = false, StatusMessage = "Config error: Board ID mismatch for clear.", FirmwareVersion = Status.FirmwareVersion };
                 return false;
            }
            Console.WriteLine($"TADDB Client ({_boardConfig.BoardName}): Clearing display.");
            byte[] packet = DisplayPacketBuilder.BuildTaddbClearScreenPacket(_boardConfig.BoardId);
            try
            {
                await _communicationService.WriteDataAsync(packet);
                Status = new BoardStatus { IsLinkOk = true, StatusMessage = "Display cleared.", FirmwareVersion = Status.FirmwareVersion };
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"TADDB Client ({_boardConfig.BoardName}) Error: Failed to clear display. Exception: {ex.Message}");
                Status = new BoardStatus { IsLinkOk = false, StatusMessage = $"Clear failed: {ex.Message}", FirmwareVersion = Status.FirmwareVersion };
                return false;
            }
        }

        public async Task<BoardStatus> CheckLinkAsync() => await CheckLinkAsync(_boardConfig.BoardId, 0, 0);

        public async Task<BoardStatus> CheckLinkAsync(byte boardAddress, byte subAddress, byte serialNo)
        {
            if (boardAddress != _boardConfig.BoardId)
            {
                 Console.WriteLine($"TADDB Client ({_boardConfig.BoardName}) Error: Mismatched boardAddress ({boardAddress}) for link check.");
                 Status = new BoardStatus { IsLinkOk = false, StatusMessage = "Config error: Board ID mismatch for link check.", FirmwareVersion = Status.FirmwareVersion };
                 return Status;
            }
            Console.WriteLine($"TADDB Client ({_boardConfig.BoardName}): Checking link.");
            byte[] packet = DisplayPacketBuilder.BuildLinkCheckPacket(_boardConfig.BoardId); // Generic link check
            try
            {
                await _communicationService.WriteDataAsync(packet);
                Status = new BoardStatus { IsLinkOk = true, StatusMessage = "Link check sent (response check not implemented).", FirmwareVersion = Status.FirmwareVersion };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"TADDB Client ({_boardConfig.BoardName}) Error: Failed to send link check. Exception: {ex.Message}");
                Status = new BoardStatus { IsLinkOk = false, StatusMessage = $"Link check send failed: {ex.Message}", FirmwareVersion = Status.FirmwareVersion };
            }
            return Status;
        }

        // Stub implementations
        public async Task<bool> SetConfigurationAsync(byte boardAddress, byte subAddress, byte serialNo, byte[] configData)
        {
            Console.WriteLine($"TADDB Client ({_boardConfig.BoardName}): SetConfigurationAsync - NOT IMPLEMENTED.");
            await Task.CompletedTask;
            Status = new BoardStatus { IsLinkOk = Status.IsLinkOk, StatusMessage = "SetConfiguration not implemented.", FirmwareVersion = Status.FirmwareVersion };
            return false;
        }

        public async Task<byte[]> GetConfigurationAsync(byte boardAddress, byte subAddress, byte serialNo)
        {
            Console.WriteLine($"TADDB Client ({_boardConfig.BoardName}): GetConfigurationAsync - NOT IMPLEMENTED.");
            await Task.CompletedTask;
            Status = new BoardStatus { IsLinkOk = Status.IsLinkOk, StatusMessage = "GetConfiguration not implemented.", FirmwareVersion = Status.FirmwareVersion };
            return Array.Empty<byte>();
        }

        public async Task<bool> ResetBoardAsync(byte boardAddress, byte subAddress, byte serialNo)
        {
            Console.WriteLine($"TADDB Client ({_boardConfig.BoardName}): ResetBoardAsync - NOT IMPLEMENTED.");
            await Task.CompletedTask;
            Status = new BoardStatus { IsLinkOk = Status.IsLinkOk, StatusMessage = "ResetBoard not implemented.", FirmwareVersion = Status.FirmwareVersion };
            return false;
        }
    }
}
