using System;
using System.Collections.Generic;
using System.Data;
using IPIS.Repositories.Interfaces;
using IPIS.Models;

namespace IPIS.Services
{
    public class RoleService
    {
        private readonly IRoleRepository _roleRepository;

        public RoleService(IRoleRepository roleRepository)
        {
            _roleRepository = roleRepository;
        }

        public void AddRole(string name, string description, List<string> permissions)
        {
            // Prevent creating Administrator role
            if (name.Equals("Administrator", StringComparison.OrdinalIgnoreCase))
            {
                throw new InvalidOperationException("Administrator role cannot be created through this interface.");
            }

            if (_roleRepository.RoleExists(name))
            {
                throw new InvalidOperationException($"Role '{name}' already exists.");
            }

            _roleRepository.AddRole(name, description, permissions);
        }

        public void UpdateRole(long roleId, string name, string description, List<string> permissions)
        {
            var existingRole = _roleRepository.GetRoleById(roleId);
            if (existingRole == null)
            {
                throw new InvalidOperationException("Role not found.");
            }

            // Prevent editing Administrator role
            if (existingRole.IsAdministrator())
            {
                throw new InvalidOperationException("Administrator role cannot be modified.");
            }

            // Prevent changing name to Administrator
            if (name.Equals("Administrator", StringComparison.OrdinalIgnoreCase))
            {
                throw new InvalidOperationException("Role name cannot be changed to Administrator.");
            }

            // Check if new name conflicts with existing role (excluding current role)
            if (!name.Equals(existingRole.Name, StringComparison.OrdinalIgnoreCase) && _roleRepository.RoleExists(name))
            {
                throw new InvalidOperationException($"Role '{name}' already exists.");
            }

            _roleRepository.UpdateRole(roleId, name, description, permissions);
        }

        public void DeleteRole(long roleId)
        {
            var role = _roleRepository.GetRoleById(roleId);
            if (role == null)
            {
                throw new InvalidOperationException("Role not found.");
            }

            // Prevent deleting Administrator role
            if (role.IsAdministrator())
            {
                throw new InvalidOperationException("Administrator role cannot be deleted.");
            }

            // Check if role is in use
            if (_roleRepository.IsRoleInUse(roleId))
            {
                throw new InvalidOperationException($"Cannot delete role '{role.Name}' because it is assigned to one or more users.");
            }

            _roleRepository.DeleteRole(roleId);
        }

        public DataTable GetAllRoles()
        {
            return _roleRepository.GetAllRoles();
        }

        public Role GetRoleById(long roleId)
        {
            return _roleRepository.GetRoleById(roleId);
        }

        public Role GetRoleByName(string name)
        {
            return _roleRepository.GetRoleByName(name);
        }

        public List<string> GetRolePermissions(long roleId)
        {
            return _roleRepository.GetRolePermissions(roleId);
        }

        public bool RoleExists(string name)
        {
            return _roleRepository.RoleExists(name);
        }

        public bool IsRoleInUse(long roleId)
        {
            return _roleRepository.IsRoleInUse(roleId);
        }

        public List<string> GetAvailablePermissions()
        {
            return new List<string>
            {
                "Reports",
                "Add User",
                "Station Details",
                "Add Station Code",
                "Train Data Entry",
                "Add Advertising"
            };
        }
    }
} 