﻿using System;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using System.Windows.Forms;

namespace Announcement
{
	// Token: 0x0200000F RID: 15
	public partial class User : Form
	{
		// Token: 0x06000080 RID: 128 RVA: 0x00015A58 File Offset: 0x00013C58
		public User()
		{
			this.InitializeComponent();
		}

		// Token: 0x06000081 RID: 129 RVA: 0x000025C1 File Offset: 0x000007C1
		private void pictureBox1_Click(object sender, EventArgs e)
		{
		}

		// Token: 0x06000082 RID: 130 RVA: 0x000025C4 File Offset: 0x000007C4
		private void button2_Click(object sender, EventArgs e)
		{
			base.Close();
		}

		// Token: 0x06000083 RID: 131 RVA: 0x00015A94 File Offset: 0x00013C94
		private void BTN_Add_Click(object sender, EventArgs e)
		{
			this.BTN_Save.Enabled = true;
			this.TB_Pass.Enabled = true;
			this.TB_CPass.Enabled = true;
			this.TB_PassHint.Enabled = true;
			this.GB_Sup.Enabled = true;
			this.TB_UName.Enabled = true;
			this.Cmb_UserType.Enabled = true;
			this.GB_Sup.Enabled = true;
			this.CHH_Adve.Enabled = true;
			this.CHH_TDEntry.Enabled = true;
			this.CHH_ASCode.Enabled = true;
			this.CHH_AUser.Enabled = true;
			this.CHH_Reports.Enabled = true;
			this.CHH_SD.Enabled = true;
			this.TB_UName.Text = "";
			this.TB_Pass.Text = "";
			this.TB_CPass.Text = "";
			this.TB_PassHint.Text = "";
			this.GB_Sup.Text = "";
			this.Cmb_User.Text = "New";
			this.Cmb_UserType.Text = "Administrator";
		}

		// Token: 0x06000084 RID: 132 RVA: 0x00015BD0 File Offset: 0x00013DD0
		private void BTN_Edit_Click(object sender, EventArgs e)
		{
			this.Flag_Edit = true;
			this.BTN_Save.Enabled = true;
			this.TB_Pass.Enabled = true;
			this.TB_CPass.Enabled = true;
			this.TB_PassHint.Enabled = true;
			this.GB_Sup.Enabled = true;
			this.TB_UName.Enabled = true;
			this.CHH_Adve.Enabled = true;
			this.CHH_TDEntry.Enabled = true;
			this.CHH_ASCode.Enabled = true;
			this.CHH_AUser.Enabled = true;
			this.CHH_Reports.Enabled = true;
			this.CHH_SD.Enabled = true;
		}

		// Token: 0x06000085 RID: 133 RVA: 0x00015C84 File Offset: 0x00013E84
		private void BTN_Save_Click(object sender, EventArgs e)
		{
			DataTable dataTable = new DataTable();
			bool flag = this.TB_UName.Text == "";
			if (flag)
			{
				MessageBox.Show("User Name cannot be Blank...");
			}
			else
			{
				bool flag2 = this.TB_Pass.Text == "";
				if (flag2)
				{
					MessageBox.Show("Password cannot be Blank...");
				}
				else
				{
					bool flag3 = this.TB_Pass.Text != this.TB_CPass.Text;
					if (flag3)
					{
						this.TB_Pass.Text = "";
						this.TB_CPass.Text = "";
						MessageBox.Show("Confirmation Password is not same as Entred Password, Please Correct...");
					}
					else
					{
						bool flag4 = !this.Flag_Edit;
						if (flag4)
						{
							dataTable = this.DB.Read_Database("SELECT * From User_Details WHERE User_Name = '" + this.TB_UName.Text + "'");
							bool flag5 = dataTable.Rows.Count > 0;
							if (flag5)
							{
								MessageBox.Show("User " + this.TB_UName.Text + " Already Exsist...");
								return;
							}
							bool flag6 = this.TB_UName.Text == "EDS";
							if (flag6)
							{
								MessageBox.Show("User " + this.TB_UName.Text + " is Blocked...");
								return;
							}
						}
						string text = this.Encrypt(this.TB_Pass.Text);
						this.DB.Insert_Database("DELETE * From User_Details WHERE User_Name = '" + this.TB_UName.Text + "'");
						this.DB.Insert_Database(string.Concat(new string[]
						{
							"INSERT INTO User_Details(User_Name,Pass,User_Type,Hint_Pass,Chk_Adver,Chk_User,Chk_StnC,Chk_TrnD,Chk_StnDet,Chk_Rep) VALUES('",
							this.TB_UName.Text,
							"','",
							text,
							"','",
							this.Cmb_UserType.Text,
							"','",
							this.TB_PassHint.Text,
							"','",
							this.CHH_Adve.Checked.ToString(),
							"','",
							this.CHH_AUser.Checked.ToString(),
							"','",
							this.CHH_ASCode.Checked.ToString(),
							"','",
							this.CHH_TDEntry.Checked.ToString(),
							"','",
							this.CHH_SD.Checked.ToString(),
							"','",
							this.CHH_Reports.Checked.ToString(),
							"')"
						}));
						bool flag7 = !this.Flag_Edit;
						if (flag7)
						{
							MessageBox.Show("User " + this.TB_UName.Text + " Created Sucessfully..");
							this.Cmb_User.Items.Add(this.TB_UName.Text);
						}
						else
						{
							MessageBox.Show("User " + this.TB_UName.Text + " Modified Sucessfully..");
						}
						this.Flag_Edit = false;
						this.Cmb_User.Enabled = true;
						this.BTN_Save.Enabled = false;
						this.TB_Pass.Enabled = false;
						this.TB_CPass.Enabled = false;
						this.GB_Sup.Enabled = false;
						this.TB_PassHint.Enabled = false;
					}
				}
			}
		}

		// Token: 0x06000086 RID: 134 RVA: 0x00016028 File Offset: 0x00014228
		private string Encrypt(string cipherText)
		{
			string password = "NAKV2SPBNI99212";
			byte[] bytes = Encoding.Unicode.GetBytes(cipherText);
			using (Aes aes = Aes.Create())
			{
				Rfc2898DeriveBytes rfc2898DeriveBytes = new Rfc2898DeriveBytes(password, new byte[]
				{
					73,
					118,
					97,
					110,
					32,
					77,
					101,
					100,
					118,
					101,
					100,
					101,
					118
				});
				aes.Key = rfc2898DeriveBytes.GetBytes(32);
				aes.IV = rfc2898DeriveBytes.GetBytes(16);
				using (MemoryStream memoryStream = new MemoryStream())
				{
					using (CryptoStream cryptoStream = new CryptoStream(memoryStream, aes.CreateEncryptor(), CryptoStreamMode.Write))
					{
						cryptoStream.Write(bytes, 0, bytes.Length);
						cryptoStream.Close();
					}
					cipherText = Convert.ToBase64String(memoryStream.ToArray());
				}
			}
			return cipherText;
		}

		// Token: 0x06000087 RID: 135 RVA: 0x00016120 File Offset: 0x00014320
		public string Decrypt(string cipherText)
		{
			string password = "NAKV2SPBNI99212";
			byte[] array = Convert.FromBase64String(cipherText);
			using (Aes aes = Aes.Create())
			{
				Rfc2898DeriveBytes rfc2898DeriveBytes = new Rfc2898DeriveBytes(password, new byte[]
				{
					73,
					118,
					97,
					110,
					32,
					77,
					101,
					100,
					118,
					101,
					100,
					101,
					118
				});
				aes.Key = rfc2898DeriveBytes.GetBytes(32);
				aes.IV = rfc2898DeriveBytes.GetBytes(16);
				using (MemoryStream memoryStream = new MemoryStream())
				{
					using (CryptoStream cryptoStream = new CryptoStream(memoryStream, aes.CreateDecryptor(), CryptoStreamMode.Write))
					{
						cryptoStream.Write(array, 0, array.Length);
						cryptoStream.Close();
					}
					cipherText = Encoding.Unicode.GetString(memoryStream.ToArray());
				}
			}
			return cipherText;
		}

		// Token: 0x06000088 RID: 136 RVA: 0x00016218 File Offset: 0x00014418
		private void CB_SUType_SelectedIndexChanged(object sender, EventArgs e)
		{
			bool flag = this.Cmb_User.Text == "New";
			if (flag)
			{
				this.TB_UName.Text = "";
				this.TB_Pass.Text = "";
				this.TB_CPass.Text = "";
				this.Cmb_User.Text = "Administrator";
				this.GB_Sup.Enabled = false;
				this.CHH_Adve.Checked = false;
				this.CHH_AUser.Enabled = false;
				this.CHH_ASCode.Enabled = false;
				this.CHH_TDEntry.Checked = false;
				this.CHH_Reports.Checked = false;
				this.CHH_SD.Enabled = false;
			}
			else
			{
				DataTable dataTable = new DataTable();
				dataTable = this.DB.Read_Database("SELECT * From User_Details WHERE User_Name = '" + this.Cmb_User.Text + "'");
				this.TB_UName.Text = dataTable.Rows[0]["User_Name"].ToString();
				this.TB_Pass.Text = this.Decrypt(dataTable.Rows[0]["Pass"].ToString());
				this.TB_CPass.Text = this.Decrypt(dataTable.Rows[0]["Pass"].ToString());
				this.Cmb_User.Text = dataTable.Rows[0]["User_type"].ToString();
				this.TB_PassHint.Text = dataTable.Rows[0]["Hint_Pass"].ToString();
				bool flag2 = dataTable.Rows[0]["Chk_Adver"].ToString() == "True";
				if (flag2)
				{
					this.CHH_Adve.Checked = true;
				}
				else
				{
					this.CHH_Adve.Checked = false;
				}
				bool flag3 = dataTable.Rows[0]["Chk_User"].ToString() == "True";
				if (flag3)
				{
					this.CHH_AUser.Checked = true;
				}
				else
				{
					this.CHH_AUser.Checked = false;
				}
				bool flag4 = dataTable.Rows[0]["Chk_StnC"].ToString() == "True";
				if (flag4)
				{
					this.CHH_ASCode.Checked = true;
				}
				else
				{
					this.CHH_ASCode.Checked = false;
				}
				bool flag5 = dataTable.Rows[0]["Chk_TrnD"].ToString() == "True";
				if (flag5)
				{
					this.CHH_TDEntry.Checked = true;
				}
				else
				{
					this.CHH_TDEntry.Checked = false;
				}
				bool flag6 = dataTable.Rows[0]["Chk_StnDet"].ToString() == "True";
				if (flag6)
				{
					this.CHH_SD.Checked = true;
				}
				else
				{
					this.CHH_SD.Checked = false;
				}
				bool flag7 = dataTable.Rows[0]["Chk_Rep"].ToString() == "True";
				if (flag7)
				{
					this.CHH_Reports.Checked = true;
				}
				else
				{
					this.CHH_Reports.Checked = false;
				}
			}
		}

		// Token: 0x06000089 RID: 137 RVA: 0x0001658C File Offset: 0x0001478C
		private void BTN_Del_Click(object sender, EventArgs e)
		{
			DataTable dataTable = new DataTable();
			this.DB.Insert_Database("DELETE * From User_Details WHERE User_Name = '" + this.TB_UName.Text + "'");
			dataTable = this.DB.Read_Database("SELECT * From User_Details");
			this.Cmb_User.Items.Remove(this.TB_UName.Text);
			bool flag = dataTable.Rows.Count > 0;
			if (flag)
			{
				this.Cmb_User.Text = dataTable.Rows[0]["User_Name"].ToString();
			}
			else
			{
				this.Cmb_User.Text = "New";
			}
		}

		// Token: 0x0600008A RID: 138 RVA: 0x00016640 File Offset: 0x00014840
		private void User_Load(object sender, EventArgs e)
		{
			DataTable dataTable = new DataTable();
			dataTable = this.DB.Read_Database("Select * From User_Details");
			this.Cmb_User.Items.Clear();
			for (int i = 0; i < dataTable.Rows.Count; i++)
			{
				this.Cmb_User.Items.Add(dataTable.Rows[i]["User_Name"].ToString());
			}
			this.Cmb_User.Items.Add("New");
		}

		// Token: 0x0600008B RID: 139 RVA: 0x000025C1 File Offset: 0x000007C1
		private void CB_UName_SelectedIndexChanged(object sender, EventArgs e)
		{
		}

		// Token: 0x17000003 RID: 3
		// (get) Token: 0x0600008C RID: 140 RVA: 0x000166D4 File Offset: 0x000148D4
		// (set) Token: 0x0600008D RID: 141 RVA: 0x000166DC File Offset: 0x000148DC
		public string Enc_Psw { get; set; }

		// Token: 0x0600008E RID: 142 RVA: 0x000166E8 File Offset: 0x000148E8
		private void Cmb_User_SelectedIndexChanged(object sender, EventArgs e)
		{
			bool flag = this.Cmb_User.Text == "New";
			if (flag)
			{
				this.TB_UName.Text = "";
				this.TB_Pass.Text = "";
				this.TB_CPass.Text = "";
				this.Cmb_UserType.Text = "Administrator";
				this.GB_Sup.Enabled = false;
				this.CHH_Adve.Checked = false;
				this.CHH_TDEntry.Enabled = false;
				this.CHH_ASCode.Enabled = false;
				this.CHH_AUser.Checked = false;
				this.CHH_Reports.Checked = false;
				this.CHH_SD.Enabled = false;
			}
			else
			{
				DataTable dataTable = new DataTable();
				dataTable = this.DB.Read_Database("SELECT * From User_Details WHERE User_Name = '" + this.Cmb_User.Text + "'");
				this.TB_UName.Text = dataTable.Rows[0]["User_Name"].ToString();
				this.TB_Pass.Text = this.Decrypt(dataTable.Rows[0]["Pass"].ToString());
				this.TB_CPass.Text = this.Decrypt(dataTable.Rows[0]["Pass"].ToString());
				this.Cmb_UserType.Text = dataTable.Rows[0]["User_type"].ToString();
				this.TB_PassHint.Text = dataTable.Rows[0]["Hint_Pass"].ToString();
				bool flag2 = dataTable.Rows[0]["Chk_Adver"].ToString() == "True";
				if (flag2)
				{
					this.CHH_Adve.Checked = true;
				}
				else
				{
					this.CHH_Adve.Checked = false;
				}
				bool flag3 = dataTable.Rows[0]["Chk_User"].ToString() == "True";
				if (flag3)
				{
					this.CHH_AUser.Checked = true;
				}
				else
				{
					this.CHH_AUser.Checked = false;
				}
				bool flag4 = dataTable.Rows[0]["Chk_StnC"].ToString() == "True";
				if (flag4)
				{
					this.CHH_ASCode.Checked = true;
				}
				else
				{
					this.CHH_ASCode.Checked = false;
				}
				bool flag5 = dataTable.Rows[0]["Chk_TrnD"].ToString() == "True";
				if (flag5)
				{
					this.CHH_TDEntry.Checked = true;
				}
				else
				{
					this.CHH_TDEntry.Checked = false;
				}
				bool flag6 = dataTable.Rows[0]["Chk_StnDet"].ToString() == "True";
				if (flag6)
				{
					this.CHH_SD.Checked = true;
				}
				else
				{
					this.CHH_SD.Checked = false;
				}
				bool flag7 = dataTable.Rows[0]["Chk_Rep"].ToString() == "True";
				if (flag7)
				{
					this.CHH_Reports.Checked = true;
				}
				else
				{
					this.CHH_Reports.Checked = false;
				}
			}
		}

		// Token: 0x0600008F RID: 143 RVA: 0x000025C1 File Offset: 0x000007C1
		private void Dev_Font_Apply(object sender, EventArgs e)
		{
		}

		// Token: 0x06000090 RID: 144 RVA: 0x000025C1 File Offset: 0x000007C1
		private void label1_Click(object sender, EventArgs e)
		{
		}

		// Token: 0x06000091 RID: 145 RVA: 0x000025C1 File Offset: 0x000007C1
		private void TB_PassHint_TextChanged(object sender, EventArgs e)
		{
		}

		// Token: 0x06000092 RID: 146 RVA: 0x000025C1 File Offset: 0x000007C1
		private void Lbl_UserType_Click(object sender, EventArgs e)
		{
		}

		// Token: 0x06000093 RID: 147 RVA: 0x00016A5C File Offset: 0x00014C5C
		private void Cmb_UserType_SelectedIndexChanged(object sender, EventArgs e)
		{
			bool flag = this.Cmb_User.Text == "New";
			if (flag)
			{
				bool flag2 = this.Cmb_UserType.Text == "Administrator";
				if (flag2)
				{
					this.GB_Sup.Enabled = true;
					this.CHH_Adve.Checked = true;
					this.CHH_TDEntry.Checked = true;
					this.CHH_ASCode.Checked = true;
					this.CHH_AUser.Checked = true;
					this.CHH_Reports.Checked = true;
					this.CHH_SD.Checked = true;
				}
				else
				{
					bool flag3 = this.Cmb_UserType.Text == "Supervisor";
					if (flag3)
					{
						this.GB_Sup.Enabled = true;
						this.CHH_AUser.Checked = true;
						this.CHH_Reports.Checked = true;
						this.CHH_SD.Checked = false;
						this.CHH_AUser.Checked = true;
						this.CHH_Reports.Checked = true;
						this.CHH_SD.Checked = true;
					}
					else
					{
						bool flag4 = this.Cmb_UserType.Text == "Operator";
						if (flag4)
						{
							this.GB_Sup.Enabled = true;
							this.CHH_Adve.Checked = true;
							this.CHH_TDEntry.Checked = false;
							this.CHH_ASCode.Checked = false;
							this.CHH_AUser.Checked = false;
							this.CHH_Reports.Checked = false;
							this.CHH_SD.Checked = false;
						}
						else
						{
							bool flag5 = this.Cmb_UserType.Text == "User";
							if (flag5)
							{
								this.GB_Sup.Enabled = true;
								this.CHH_Adve.Checked = true;
								this.CHH_TDEntry.Checked = false;
								this.CHH_ASCode.Checked = false;
								this.CHH_AUser.Checked = false;
								this.CHH_Reports.Checked = false;
								this.CHH_SD.Checked = false;
							}
						}
					}
				}
			}
		}

		// Token: 0x040000E7 RID: 231
		public bool Flag_Edit = false;

		// Token: 0x040000E8 RID: 232
		private DataTable UserTable = new DataTable();

		// Token: 0x040000E9 RID: 233
		private Class_Database DB = new Class_Database();

		// Token: 0x040000EA RID: 234
		private DataTable TrainTable;

		// Token: 0x040000EB RID: 235
		private DataTable StnTable;

		// Token: 0x040000EC RID: 236
		private int GP_Counter;

		// Token: 0x040000ED RID: 237
		private int Edit_ID = 0;
	}
}
