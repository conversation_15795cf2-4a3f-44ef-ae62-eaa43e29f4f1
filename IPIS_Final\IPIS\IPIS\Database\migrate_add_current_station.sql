-- Migration to add Is<PERSON><PERSON><PERSON> column to Station_Details table
-- This allows marking one station as the current active station

-- Add IsCurrent column to Station_Details table
ALTER TABLE Station_Details ADD COLUMN Is_Current BOOLEAN DEFAULT FALSE;

-- Create index for better performance when querying current station
CREATE INDEX IF NOT EXISTS idx_station_details_is_current ON Station_Details(Is_Current);

-- Update existing stations to ensure only one is marked as current (if any exist)
-- This will set the first station as current if no station is currently marked
UPDATE Station_Details 
SET Is_Current = TRUE 
WHERE Station_Name = (
    SELECT Station_Name 
    FROM Station_Details 
    WHERE Is_Current = FALSE OR Is_Current IS NULL 
    ORDER BY Station_Name 
    LIMIT 1
) 
AND (SELECT COUNT(*) FROM Station_Details WHERE Is_Current = TRUE) = 0; 