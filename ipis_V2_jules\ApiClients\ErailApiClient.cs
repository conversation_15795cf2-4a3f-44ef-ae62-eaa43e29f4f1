using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading.Tasks;
using System.Linq; // For Linq's Split and Skip

namespace ipis_V2_jules.ApiClients
{
    /// <summary>
    /// Models the data parsed from the erail.in GetTrainDetails endpoint.
    /// Format: ^TRAIN_NO~TRAIN_NAME~SRC_STN_NAME~SRC_STN_CODE~DEST_STN_NAME~DEST_STN_CODE~START_TIME~END_TIME~TRAVEL_TIME~RUNNING_DAYS~...
    /// </summary>
    public class TrainDataErail
    {
        public string TrainNo { get; set; }
        public string TrainName { get; set; }
        public string SourceStationName { get; set; }
        public string SourceStationCode { get; set; }
        public string DestinationStationName { get; set; }
        public string DestinationStationCode { get; set; }
        public string StartTime { get; set; } // e.g., "18:00"
        public string EndTime { get; set; }   // e.g., "06:00"
        public string TravelTime { get; set; } // e.g., "12h 0m"
        public string RunningDaysString { get; set; } // e.g., "YNNYYNY" (Mon-Sun)
        public string TrainType { get; set; } // Typically the 11th field if present
        public string PantryCar { get; set; } // Typically the 12th field if present
        public List<string> Coaches { get; set; } // For coach composition, e.g., ["SLR", "GEN", "S1"]

        public TrainDataErail()
        {
            Coaches = new List<string>();
        }

        public override string ToString()
        {
            var coachStr = Coaches != null && Coaches.Any() ? string.Join("-", Coaches) : "N/A";
            return $"Train: {TrainNo} - {TrainName}\n" +
                   $"From: {SourceStationName} ({SourceStationCode}) at {StartTime}\n" +
                   $"To: {DestinationStationName} ({DestinationStationCode}) at {EndTime}\n" +
                   $"Duration: {TravelTime}\n" +
                   $"Runs On: {RunningDaysString}\n" +
                   $"Type: {TrainType ?? "N/A"}\nPantry: {PantryCar ?? "N/A"}\n" +
                   $"Coaches: {coachStr}";
        }
    }

    public class ErailApiClient
    {
        private const string BaseUrl = "https://erail.in/";
        private static readonly HttpClient client = new HttpClient();

        public ErailApiClient()
        {
            // Configure HttpClient if needed (e.g., User-Agent)
            client.DefaultRequestHeaders.UserAgent.ParseAdd("Mozilla/5.0 (compatible; IPIS_V2_Agent/1.0)");
        }

        /// <summary>
        /// Gets train details from erail.in.
        /// </summary>
        /// <param name="trainNo">The train number.</param>
        /// <returns>A TrainDataErail object, or null if an error occurs or train not found.</returns>
        public async Task<TrainDataErail> GetTrainDetailsAsync(string trainNo)
        {
            if (string.IsNullOrWhiteSpace(trainNo)) throw new ArgumentNullException(nameof(trainNo));

            string apiUrl = $"{BaseUrl}rail/getTrains.aspx?TrainNo={trainNo}&DataSource=0&Language=0&Cache=true";

            try
            {
                Console.WriteLine($"Making API Request to: {apiUrl}");
                HttpResponseMessage response = await client.GetAsync(apiUrl);

                if (response.IsSuccessStatusCode)
                {
                    string responseString = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"API Response for GetTrainDetailsAsync (TrainNo: {trainNo}): {responseString.Substring(0, Math.Min(responseString.Length, 200))}...");

                    // Example response: ^12951~MUMBAI RAJDHANI~MUMBAI CENTRAL~BCT~NEW DELHI~NDLS~17:00~08:35~15h 35m~YYYYYYY~RAJ~Y~
                    // The useful data starts after the first '^'.
                    // If the response is empty or doesn't contain '^' or '~', it's not the expected format.
                    if (string.IsNullOrWhiteSpace(responseString) || !responseString.Contains("^") || !responseString.Contains("~"))
                    {
                        Console.WriteLine($"Train details for {trainNo} not found or unexpected format (empty or no delimiters). Response: {responseString}");
                        return null;
                    }

                    // Skip the initial '^' and then split by '~'
                    string[] parts = responseString.TrimStart('^').Split('~');

                    if (parts.Length < 10) // Need at least 10 parts for the core fields
                    {
                        Console.WriteLine($"Error parsing train details for {trainNo}: Insufficient data parts. Expected at least 10, got {parts.Length}. Response: {responseString}");
                        return null;
                    }

                    var trainData = new TrainDataErail
                    {
                        TrainNo = parts[0],
                        TrainName = parts[1],
                        SourceStationName = parts[2],
                        SourceStationCode = parts[3],
                        DestinationStationName = parts[4],
                        DestinationStationCode = parts[5],
                        StartTime = parts[6],
                        EndTime = parts[7],
                        TravelTime = parts[8],
                        RunningDaysString = parts[9]
                        // Coaches list would be populated from a different field or logic if available in this response
                    };

                    // Optional fields
                    if (parts.Length > 10) trainData.TrainType = parts[10];
                    if (parts.Length > 11) trainData.PantryCar = parts[11];
                    // Example: If coach info was part 12, comma-separated:
                    // if (parts.Length > 12 && !string.IsNullOrWhiteSpace(parts[12]))
                    // {
                    //     trainData.Coaches.AddRange(parts[12].Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries));
                    // }


                    return trainData;
                }
                else
                {
                    string errorContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"API Error for GetTrainDetailsAsync (TrainNo: {trainNo}): {response.StatusCode} - {errorContent}");
                    return null;
                }
            }
            catch (HttpRequestException ex)
            {
                Console.WriteLine($"HttpRequestException in GetTrainDetailsAsync (TrainNo: {trainNo}): {ex.Message}");
                return null;
            }
            catch (Exception ex) // Catch other exceptions like IndexOutOfRangeException during parsing
            {
                Console.WriteLine($"Generic Exception in GetTrainDetailsAsync (TrainNo: {trainNo}): {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Gets the live station HTML page content from erail.in.
        /// This HTML needs to be parsed to extract meaningful data.
        /// </summary>
        /// <param name="stationCode">The station code (e.g., "NDLS").</param>
        /// <returns>Raw HTML string, or null if an error occurs.</returns>
        public async Task<string> GetLiveStationHtmlAsync(string stationCode)
        {
            if (string.IsNullOrWhiteSpace(stationCode)) throw new ArgumentNullException(nameof(stationCode));

            string apiUrl = $"{BaseUrl}station-live/{stationCode}?DataSource=0&Language=0&Cache=true";

            try
            {
                Console.WriteLine($"Making API Request to: {apiUrl}");
                HttpResponseMessage response = await client.GetAsync(apiUrl);

                if (response.IsSuccessStatusCode)
                {
                    string htmlContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"API Response for GetLiveStationHtmlAsync (Station: {stationCode}): HTML received, length {htmlContent.Length}.");
                    // Comment: The returned HTML content needs to be parsed using a library
                    // like HtmlAgilityPack or AngleSharp to extract meaningful train arrival/departure data.
                    return htmlContent;
                }
                else
                {
                    string errorContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"API Error for GetLiveStationHtmlAsync (Station: {stationCode}): {response.StatusCode} - {errorContent}");
                    return null;
                }
            }
            catch (HttpRequestException ex)
            {
                Console.WriteLine($"HttpRequestException in GetLiveStationHtmlAsync (Station: {stationCode}): {ex.Message}");
                return null;
            }
             catch (Exception ex)
            {
                Console.WriteLine($"Generic Exception in GetLiveStationHtmlAsync (Station: {stationCode}): {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Gets the PNR status HTML page content from confirmtkt.com.
        /// This HTML needs to be parsed to extract meaningful data.
        /// </summary>
        /// <param name="pnrNo">The PNR number.</param>
        /// <returns>Raw HTML string, or null if an error occurs.</returns>
        public async Task<string> GetPnrStatusHtmlAsync(string pnrNo)
        {
            if (string.IsNullOrWhiteSpace(pnrNo)) throw new ArgumentNullException(nameof(pnrNo));

            // Using confirmtkt.com as specified for PNR status
            string apiUrl = $"https://www.confirmtkt.com/pnr-status/{pnrNo}";

            try
            {
                Console.WriteLine($"Making API Request to: {apiUrl}");
                HttpResponseMessage response = await client.GetAsync(apiUrl);

                if (response.IsSuccessStatusCode)
                {
                    string htmlContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"API Response for GetPnrStatusHtmlAsync (PNR: {pnrNo}): HTML received, length {htmlContent.Length}.");
                    // Comment: The returned HTML content needs to be parsed using a library
                    // like HtmlAgilityPack or AngleSharp to extract PNR status details.
                    return htmlContent;
                }
                else
                {
                    string errorContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"API Error for GetPnrStatusHtmlAsync (PNR: {pnrNo}): {response.StatusCode} - {errorContent}");
                    return null;
                }
            }
            catch (HttpRequestException ex)
            {
                Console.WriteLine($"HttpRequestException in GetPnrStatusHtmlAsync (PNR: {pnrNo}): {ex.Message}");
                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Generic Exception in GetPnrStatusHtmlAsync (PNR: {pnrNo}): {ex.Message}");
                return null;
            }
        }
    }
}
