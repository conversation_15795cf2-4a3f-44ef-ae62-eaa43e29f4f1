using System;

namespace IPIS.Models
{
    public class StationAnnouncementConfig
    {
        public int Id { get; set; }
        public string StationName { get; set; }
        public string FirstLanguageCode { get; set; }
        public string SecondLanguageCode { get; set; }
        public bool FirstLanguageEnabled { get; set; }
        public bool SecondLanguageEnabled { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }

        // Navigation properties
        public Language FirstLanguage { get; set; }
        public Language SecondLanguage { get; set; }
        public StationDetails Station { get; set; }
    }
} 