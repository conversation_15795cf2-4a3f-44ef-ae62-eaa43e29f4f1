using System;
using System.Collections.Generic;

namespace IPIS.Models
{
    public class User
    {
        public long Id { get; set; }
        public string Username { get; set; }
        public string Password { get; set; }
        public string Role { get; set; }
        public DateTime? LastLogin { get; set; }
        public string Status { get; set; }
        public List<string> Permissions { get; set; } = new List<string>();

        // Legacy User_Details table fields
        public string UserName { get; set; }  // Primary key in User_Details
        public string Pass { get; set; }
        public string UserType { get; set; }
        public string HintPass { get; set; }
        public bool ChkAdver { get; set; }
        public bool ChkTDEntry { get; set; }
        public bool ChkReports { get; set; }
        public bool ChkSD { get; set; }
        public bool ChkAUser { get; set; }
        public bool ChkASCode { get; set; }
        public bool ChkRep { get; set; }

        public User()
        {
            Permissions = new List<string>();
        }

        public bool HasPermission(string permission)
        {
            return Permissions.Contains(permission);
        }

        public bool IsActive()
        {
            return Status?.Equals("Active", StringComparison.OrdinalIgnoreCase) == true;
        }
    }
} 