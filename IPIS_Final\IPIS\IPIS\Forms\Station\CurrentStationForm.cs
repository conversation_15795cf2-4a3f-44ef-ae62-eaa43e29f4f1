using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using IPIS.Models;
using IPIS.Services;
using IPIS.Utils;

namespace IPIS.Forms.Station
{
    public class CurrentStationForm : Form
    {
        private readonly StationService stationService;
        private readonly LanguageService languageService;
        private readonly StationLanguageService stationLanguageService;
        private readonly ToastNotification toast;
        private StationDetails currentStation;

        // Auto train load/delete controls
        private CheckBox chkAutoLoad, chkAutoDelete;
        private NumericUpDown numAutoLoadInterval, numAutoDeleteInterval, numAutoDeletePostInterval;

        // Announcement preference controls
        private CheckBox chkLang1Enb, chkLang2Enb;
        private ComboBox cmb1stLanguage, cmb2ndLanguage;

        // Language preference controls
        private Dictionary<string, CheckBox> languageCheckboxes;
        private Dictionary<string, TextBox> languageWaveTextBoxes;

        // Action buttons
        private Button btnSave, btnCancel;

        public CurrentStationForm()
        {
            stationService = new StationService(new Repositories.SQLiteStationRepository());
            languageService = new LanguageService(new Repositories.SQLiteLanguageRepository());
            stationLanguageService = new StationLanguageService(
                new Repositories.SQLiteStationLanguageConfigRepository(),
                new Repositories.SQLiteStationAnnouncementConfigRepository(),
                new Repositories.SQLiteLanguageRepository());
            toast = new ToastNotification(this);
            languageCheckboxes = new Dictionary<string, CheckBox>();
            languageWaveTextBoxes = new Dictionary<string, TextBox>();

            InitializeComponent();
            LoadCurrentStation();
        }

        private void InitializeComponent()
        {
            this.Text = "Current Station Configuration";
            this.Size = new Size(800, 700);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;
            this.BackColor = UIStyler.Colors.Background;

            var mainLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 2,
                Padding = new Padding(20)
            };
            mainLayout.RowStyles.Add(new RowStyle(SizeType.AutoSize)); // Title
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 100)); // Scrollable content
            this.Controls.Add(mainLayout);

            // Title
            var lblTitle = new Label { Text = "Current Station Configuration", Height = 40, AutoSize = true };
            UIStyler.ApplyLabelStyle(lblTitle, "h4");
            mainLayout.Controls.Add(lblTitle, 0, 0);

            // Scrollable content
            var scrollPanel = new Panel { Dock = DockStyle.Fill, AutoScroll = true };
            mainLayout.Controls.Add(scrollPanel, 0, 1);

            var contentPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Top,
                ColumnCount = 1,
                AutoSize = true,
                Padding = new Padding(10)
            };
            scrollPanel.Controls.Add(contentPanel);

            // Auto Train Load/Delete Section
            CreateAutoTrainSection(contentPanel);

            // Announcement Preference Section
            CreateAnnouncementSection(contentPanel);

            // Language Preference Section
            CreateLanguageSection(contentPanel);

            // Wave Files Section
            CreateWaveFilesSection(contentPanel);

            // Action buttons
            CreateActionButtons(contentPanel);
        }

        private void CreateAutoTrainSection(TableLayoutPanel parent)
        {
            var grpAutoTrain = new GroupBox { Text = "Auto Train Load/Delete", Dock = DockStyle.Top, AutoSize = true };
            UIStyler.ApplyGroupBoxStyle(grpAutoTrain);

            var layout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 2,
                RowCount = 4,
                AutoSize = true,
                Padding = new Padding(10)
            };
            layout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 60F)); // Label
            layout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 40F)); // Input (right)

            // Auto Load
            var lblAutoLoad = new Label { Text = "Auto Load", Anchor = AnchorStyles.Left, AutoSize = true };
            UIStyler.ApplyLabelStyle(lblAutoLoad, "body-bold");
            chkAutoLoad = new CheckBox { Text = "Enable Auto Load", Anchor = AnchorStyles.Right, AutoSize = true };
            UIStyler.ApplyCheckBoxStyle(chkAutoLoad);
            layout.Controls.Add(lblAutoLoad, 0, 0);
            layout.Controls.Add(chkAutoLoad, 1, 0);

            // Auto Load Interval
            var lblAutoLoadInterval = new Label { Text = "Auto Load Interval (minutes)", Anchor = AnchorStyles.Left, AutoSize = true };
            UIStyler.ApplyLabelStyle(lblAutoLoadInterval, "body-bold");
            numAutoLoadInterval = new NumericUpDown { Minimum = 1, Maximum = 1440, Value = 60, Anchor = AnchorStyles.Right, Width = 80 };
            UIStyler.ApplyNumericUpDownStyle(numAutoLoadInterval, "medium");
            layout.Controls.Add(lblAutoLoadInterval, 0, 1);
            layout.Controls.Add(numAutoLoadInterval, 1, 1);

            // Auto Delete
            var lblAutoDelete = new Label { Text = "Auto Delete", Anchor = AnchorStyles.Left, AutoSize = true };
            UIStyler.ApplyLabelStyle(lblAutoDelete, "body-bold");
            chkAutoDelete = new CheckBox { Text = "Enable Auto Delete", Anchor = AnchorStyles.Right, AutoSize = true };
            UIStyler.ApplyCheckBoxStyle(chkAutoDelete);
            layout.Controls.Add(lblAutoDelete, 0, 2);
            layout.Controls.Add(chkAutoDelete, 1, 2);

            // Auto Delete Interval
            var lblAutoDeleteInterval = new Label { Text = "Auto Delete Interval (minutes)", Anchor = AnchorStyles.Left, AutoSize = true };
            UIStyler.ApplyLabelStyle(lblAutoDeleteInterval, "body-bold");
            numAutoDeleteInterval = new NumericUpDown { Minimum = 1, Maximum = 1440, Value = 30, Anchor = AnchorStyles.Right, Width = 80 };
            UIStyler.ApplyNumericUpDownStyle(numAutoDeleteInterval, "medium");
            layout.Controls.Add(lblAutoDeleteInterval, 0, 3);
            layout.Controls.Add(numAutoDeleteInterval, 1, 3);

            grpAutoTrain.Controls.Add(layout);
            parent.Controls.Add(grpAutoTrain);
        }

        private void CreateAnnouncementSection(TableLayoutPanel parent)
        {
            var grpAnnouncement = new GroupBox { Text = "Announcement Preference", Dock = DockStyle.Top, AutoSize = true };
            UIStyler.ApplyGroupBoxStyle(grpAnnouncement);

            var layout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 2,
                AutoSize = true,
                Padding = new Padding(10)
            };
            layout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 40F)); // Label
            layout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 20F)); // Enable Checkbox
            layout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 40F)); // Language Dropdown

            // First Language
            var lbl1stLang = new Label { Text = "1st Language", Anchor = AnchorStyles.Left, AutoSize = true };
            UIStyler.ApplyLabelStyle(lbl1stLang, "body-bold");
            chkLang1Enb = new CheckBox { Text = "Enable", Checked = true, Anchor = AnchorStyles.Right, AutoSize = true };
            UIStyler.ApplyCheckBoxStyle(chkLang1Enb);
            cmb1stLanguage = new ComboBox { DropDownStyle = ComboBoxStyle.DropDownList, Anchor = AnchorStyles.Right, Width = 120 };
            UIStyler.ApplyComboBoxStyle(cmb1stLanguage, "medium");
            layout.Controls.Add(lbl1stLang, 0, 0);
            layout.Controls.Add(chkLang1Enb, 1, 0);
            layout.Controls.Add(cmb1stLanguage, 2, 0);

            // Second Language
            var lbl2ndLang = new Label { Text = "2nd Language", Anchor = AnchorStyles.Left, AutoSize = true };
            UIStyler.ApplyLabelStyle(lbl2ndLang, "body-bold");
            chkLang2Enb = new CheckBox { Text = "Enable", Checked = true, Anchor = AnchorStyles.Right, AutoSize = true };
            UIStyler.ApplyCheckBoxStyle(chkLang2Enb);
            cmb2ndLanguage = new ComboBox { DropDownStyle = ComboBoxStyle.DropDownList, Anchor = AnchorStyles.Right, Width = 120 };
            UIStyler.ApplyComboBoxStyle(cmb2ndLanguage, "medium");
            layout.Controls.Add(lbl2ndLang, 0, 1);
            layout.Controls.Add(chkLang2Enb, 1, 1);
            layout.Controls.Add(cmb2ndLanguage, 2, 1);

            grpAnnouncement.Controls.Add(layout);
            parent.Controls.Add(grpAnnouncement);
        }

        private void CreateLanguageSection(TableLayoutPanel parent)
        {
            var grpLanguages = new GroupBox { Text = "Language Preferences", Dock = DockStyle.Top, AutoSize = true };
            UIStyler.ApplyGroupBoxStyle(grpLanguages);

            var layout = new FlowLayoutPanel
            {
                Dock = DockStyle.Fill,
                FlowDirection = FlowDirection.TopDown,
                AutoSize = true,
                Padding = new Padding(10)
            };

            grpLanguages.Controls.Add(layout);
            parent.Controls.Add(grpLanguages);

            // Language checkboxes will be added dynamically in LoadLanguages()
        }

        private void CreateWaveFilesSection(TableLayoutPanel parent)
        {
            var grpWaveFiles = new GroupBox { Text = "Wave Files Configuration", Dock = DockStyle.Top, AutoSize = true };
            UIStyler.ApplyGroupBoxStyle(grpWaveFiles);

            var layout = new FlowLayoutPanel
            {
                Dock = DockStyle.Fill,
                FlowDirection = FlowDirection.TopDown,
                AutoSize = true,
                Padding = new Padding(10)
            };

            grpWaveFiles.Controls.Add(layout);
            parent.Controls.Add(grpWaveFiles);

            // Wave file controls will be added dynamically in LoadLanguages()
        }

        private void CreateActionButtons(TableLayoutPanel parent)
        {
            var btnPanel = new FlowLayoutPanel
            {
                Dock = DockStyle.Top,
                AutoSize = true,
                Padding = new Padding(10)
            };

            btnSave = new Button { Text = "Save Configuration" };
            btnCancel = new Button { Text = "Cancel" };
            
            ButtonStyler.ApplyStandardStyle(btnSave, "success", "medium");
            ButtonStyler.ApplyStandardStyle(btnCancel, "secondary", "medium");
            
            btnSave.Click += BtnSave_Click;
            btnCancel.Click += (s, e) => this.Close();
            
            btnPanel.Controls.Add(btnSave);
            btnPanel.Controls.Add(btnCancel);
            
            parent.Controls.Add(btnPanel);
        }

        private async void LoadCurrentStation()
        {
            try
            {
                currentStation = stationService.GetCurrentStation();
                if (currentStation == null)
                {
                    MessageBox.Show("No current station is selected. Please select a station and mark it as current first.", 
                        "No Current Station", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    this.Close();
                    return;
                }

                // Load auto train settings
                chkAutoLoad.Checked = currentStation.AutoLoad;
                chkAutoDelete.Checked = currentStation.AutoDelete;
                numAutoLoadInterval.Value = currentStation.AutoLoadInterval;
                numAutoDeleteInterval.Value = currentStation.AutoDeleteInterval;

                // Load languages
                await LoadLanguagesAsync();
                
                // Load announcement configuration
                await LoadAnnouncementConfigurationAsync();
                
                // Load language configurations
                await LoadLanguageConfigurationsAsync();

                this.Text = $"Current Station Configuration - {currentStation.StationName}";
            }
            catch (Exception ex)
            {
                toast.ShowError($"Error loading current station: {ex.Message}");
                this.Close();
            }
        }

        private async Task LoadLanguagesAsync()
        {
            try
            {
                var languages = await languageService.GetActiveLanguagesAsync();
                
                // Add language checkboxes
                var languagePanel = FindLanguagePanel();
                if (languagePanel != null)
                {
                    languagePanel.Controls.Clear();
                    languageCheckboxes.Clear();
                    
                    foreach (var language in languages)
                    {
                        var chkLang = new CheckBox { Text = language.Name, Tag = language.Code, Checked = true };
                        UIStyler.ApplyCheckBoxStyle(chkLang);
                        languagePanel.Controls.Add(chkLang);
                        languageCheckboxes[language.Code] = chkLang;
                    }
                }

                // Add languages to combo boxes
                var languageNames = languages.Select(l => l.Name).ToArray();
                cmb1stLanguage.Items.Clear();
                cmb2ndLanguage.Items.Clear();
                cmb1stLanguage.Items.AddRange(languageNames);
                cmb2ndLanguage.Items.AddRange(languageNames);
                
                if (languages.Any())
                {
                    cmb1stLanguage.SelectedIndex = 0;
                    if (languages.Count > 1)
                    {
                        cmb2ndLanguage.SelectedIndex = 1;
                    }
                }

                // Create wave file controls
                var wavePanel = FindWavePanel();
                if (wavePanel != null)
                {
                    wavePanel.Controls.Clear();
                    languageWaveTextBoxes.Clear();

                    foreach (var language in languages)
                    {
                        var row = new TableLayoutPanel
                        {
                            ColumnCount = 3,
                            RowCount = 1,
                            Dock = DockStyle.Top,
                            AutoSize = true,
                            Width = wavePanel.Width - 40,
                            Margin = new Padding(0, 4, 0, 4)
                        };
                        row.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 30F)); // Label
                        row.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 55F)); // TextBox
                        row.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 15F)); // Button

                        var lblWave = new Label { Text = $"{language.Name} Wave File", Anchor = AnchorStyles.Left, AutoSize = true };
                        UIStyler.ApplyLabelStyle(lblWave, "body-bold");

                        var txtWave = new TextBox { ReadOnly = true, Anchor = AnchorStyles.Right, Width = 400, Margin = new Padding(0, 0, 8, 0) };
                        UIStyler.ApplyTextBoxStyle(txtWave, "medium");

                        var btnSelect = new Button { Text = "Select", Tag = language, Anchor = AnchorStyles.Right, AutoSize = true };
                        ButtonStyler.ApplyStandardStyle(btnSelect, "primary", "small");
                        btnSelect.Click += BtnSelectWave_Click;

                        row.Controls.Add(lblWave, 0, 0);
                        row.Controls.Add(txtWave, 1, 0);
                        row.Controls.Add(btnSelect, 2, 0);

                        wavePanel.Controls.Add(row);
                        languageWaveTextBoxes[language.Code] = txtWave;
                    }
                }
            }
            catch (Exception ex)
            {
                toast.ShowError($"Error loading languages: {ex.Message}");
            }
        }

        private async Task LoadAnnouncementConfigurationAsync()
        {
            try
            {
                var announcementConfig = await stationLanguageService.GetStationAnnouncementConfigAsync(currentStation.StationName);
                if (announcementConfig != null)
                {
                    // Set first language
                    if (!string.IsNullOrEmpty(announcementConfig.FirstLanguageCode))
                    {
                        var firstLanguage = await languageService.GetLanguageByCodeAsync(announcementConfig.FirstLanguageCode);
                        if (firstLanguage != null)
                        {
                            cmb1stLanguage.SelectedItem = firstLanguage.Name;
                        }
                    }

                    // Set second language
                    if (!string.IsNullOrEmpty(announcementConfig.SecondLanguageCode))
                    {
                        var secondLanguage = await languageService.GetLanguageByCodeAsync(announcementConfig.SecondLanguageCode);
                        if (secondLanguage != null)
                        {
                            cmb2ndLanguage.SelectedItem = secondLanguage.Name;
                        }
                    }

                    chkLang1Enb.Checked = announcementConfig.FirstLanguageEnabled;
                    chkLang2Enb.Checked = announcementConfig.SecondLanguageEnabled;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading announcement configuration: {ex.Message}");
            }
        }

        private async Task LoadLanguageConfigurationsAsync()
        {
            try
            {
                var stationLanguages = await stationLanguageService.GetStationLanguagesAsync(currentStation.StationName);
                var waveFilePaths = await stationLanguageService.GetWaveFilePathsAsync(currentStation.StationName);

                // Set language checkboxes
                foreach (var kvp in languageCheckboxes)
                {
                    string languageCode = kvp.Key;
                    var stationLanguage = stationLanguages.FirstOrDefault(sl => sl.LanguageCode == languageCode);
                    kvp.Value.Checked = stationLanguage?.IsEnabled ?? false;
                }

                // Set wave file paths
                foreach (var kvp in languageWaveTextBoxes)
                {
                    string languageCode = kvp.Key;
                    if (waveFilePaths.ContainsKey(languageCode))
                    {
                        kvp.Value.Text = waveFilePaths[languageCode];
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading language configurations: {ex.Message}");
            }
        }

        private FlowLayoutPanel FindLanguagePanel()
        {
            foreach (Control control in this.Controls)
            {
                if (control is Panel mainPanel)
                {
                    foreach (Control scrollControl in mainPanel.Controls)
                    {
                        if (scrollControl is Panel scrollPanel)
                        {
                            foreach (Control contentControl in scrollPanel.Controls)
                            {
                                if (contentControl is TableLayoutPanel contentPanel)
                                {
                                    foreach (Control groupControl in contentPanel.Controls)
                                    {
                                        if (groupControl is GroupBox grp && grp.Text == "Language Preferences")
                                        {
                                            return grp.Controls.OfType<FlowLayoutPanel>().FirstOrDefault();
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            return null;
        }

        private FlowLayoutPanel FindWavePanel()
        {
            foreach (Control control in this.Controls)
            {
                if (control is Panel mainPanel)
                {
                    foreach (Control scrollControl in mainPanel.Controls)
                    {
                        if (scrollControl is Panel scrollPanel)
                        {
                            foreach (Control contentControl in scrollPanel.Controls)
                            {
                                if (contentControl is TableLayoutPanel contentPanel)
                                {
                                    foreach (Control groupControl in contentPanel.Controls)
                                    {
                                        if (groupControl is GroupBox grp && grp.Text == "Wave Files Configuration")
                                        {
                                            return grp.Controls.OfType<FlowLayoutPanel>().FirstOrDefault();
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            return null;
        }

        private async void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                // Update station with auto train settings
                currentStation.AutoLoad = chkAutoLoad.Checked;
                currentStation.AutoDelete = chkAutoDelete.Checked;
                currentStation.AutoLoadInterval = (int)numAutoLoadInterval.Value;
                currentStation.AutoDeleteInterval = (int)numAutoDeleteInterval.Value;
                
                stationService.UpdateStation(currentStation);

                // Save language configurations
                await SaveLanguageConfigurationsAsync();
                
                // Save announcement configuration
                await SaveAnnouncementConfigurationAsync();

                toast.ShowSuccess("Current station configuration saved successfully!");
                this.Close();
            }
            catch (Exception ex)
            {
                toast.ShowError($"Error saving configuration: {ex.Message}");
            }
        }

        private async Task SaveLanguageConfigurationsAsync()
        {
            try
            {
                // Clear existing language configurations for this station
                var existingConfigs = await stationLanguageService.GetStationLanguagesAsync(currentStation.StationName);
                foreach (var config in existingConfigs)
                {
                    await stationLanguageService.DeleteStationLanguageAsync(currentStation.StationName, config.LanguageCode);
                }

                // Add new language configurations based on checkboxes
                foreach (var kvp in languageCheckboxes)
                {
                    string languageCode = kvp.Key;
                    bool isEnabled = kvp.Value.Checked;
                    string waveFilePath = languageWaveTextBoxes.ContainsKey(languageCode) ? languageWaveTextBoxes[languageCode].Text : null;

                    if (isEnabled)
                    {
                        await stationLanguageService.AddStationLanguageAsync(currentStation.StationName, languageCode, true, waveFilePath);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error saving language configurations: {ex.Message}");
            }
        }

        private async Task SaveAnnouncementConfigurationAsync()
        {
            try
            {
                // Get language codes from language names
                string firstLanguageCode = null;
                string secondLanguageCode = null;

                if (!string.IsNullOrEmpty(cmb1stLanguage.SelectedItem?.ToString()))
                {
                    var firstLanguage = await languageService.GetActiveLanguagesAsync();
                    var selectedFirstLang = firstLanguage.FirstOrDefault(l => l.Name == cmb1stLanguage.SelectedItem.ToString());
                    firstLanguageCode = selectedFirstLang?.Code;
                }

                if (!string.IsNullOrEmpty(cmb2ndLanguage.SelectedItem?.ToString()))
                {
                    var secondLanguage = await languageService.GetActiveLanguagesAsync();
                    var selectedSecondLang = secondLanguage.FirstOrDefault(l => l.Name == cmb2ndLanguage.SelectedItem.ToString());
                    secondLanguageCode = selectedSecondLang?.Code;
                }

                var announcementConfig = new StationAnnouncementConfig
                {
                    StationName = currentStation.StationName,
                    FirstLanguageCode = firstLanguageCode,
                    SecondLanguageCode = secondLanguageCode,
                    FirstLanguageEnabled = chkLang1Enb.Checked,
                    SecondLanguageEnabled = chkLang2Enb.Checked,
                    CreatedAt = DateTime.Now
                };

                await stationLanguageService.SaveStationAnnouncementConfigAsync(announcementConfig);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error saving announcement configuration: {ex.Message}");
            }
        }

        private void BtnSelectWave_Click(object sender, EventArgs e)
        {
            if (sender is Button btn && btn.Tag is Language language)
            {
                using (OpenFileDialog openFileDialog = new OpenFileDialog())
                {
                    openFileDialog.Filter = "Wave Files (*.wav)|*.wav|All Files (*.*)|*.*";
                    openFileDialog.Title = $"Select {language.Name} Wave File";
                    
                    if (openFileDialog.ShowDialog() == DialogResult.OK)
                    {
                        languageWaveTextBoxes[language.Code].Text = openFileDialog.FileName;
                    }
                }
            }
        }
    }
} 