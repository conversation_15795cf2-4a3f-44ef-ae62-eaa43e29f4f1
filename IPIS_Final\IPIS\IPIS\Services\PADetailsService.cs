using System.Data;
using IPIS.Repositories.Interfaces;

namespace IPIS.Services
{
    public class PADetailsService
    {
        private readonly IPADetailsRepository _repository;

        public PADetailsService(IPADetailsRepository repository)
        {
            _repository = repository;
        }

        public DataTable GetPADetails(string partType, string partValue, string language)
        {
            return _repository.GetPADetails(partType, partValue, language);
        }

        public void AddPADetails(string partType, string partValue, string language, string filePath)
        {
            // Validate inputs
            if (string.IsNullOrWhiteSpace(partType))
                throw new ArgumentException("Part type cannot be empty", nameof(partType));
            if (string.IsNullOrWhiteSpace(partValue))
                throw new ArgumentException("Part value cannot be empty", nameof(partValue));
            if (string.IsNullOrWhiteSpace(language))
                throw new ArgumentException("Language cannot be empty", nameof(language));
            if (string.IsNullOrWhiteSpace(filePath))
                throw new ArgumentException("File path cannot be empty", nameof(filePath));

            _repository.AddPADetails(partType, partValue, language, filePath);
        }

        public void UpdatePADetails(string partType, string partValue, string language, string filePath)
        {
            // Validate inputs
            if (string.IsNullOrWhiteSpace(partType))
                throw new ArgumentException("Part type cannot be empty", nameof(partType));
            if (string.IsNullOrWhiteSpace(partValue))
                throw new ArgumentException("Part value cannot be empty", nameof(partValue));
            if (string.IsNullOrWhiteSpace(language))
                throw new ArgumentException("Language cannot be empty", nameof(language));
            if (string.IsNullOrWhiteSpace(filePath))
                throw new ArgumentException("File path cannot be empty", nameof(filePath));

            _repository.UpdatePADetails(partType, partValue, language, filePath);
        }

        public void DeletePADetails(string partType, string partValue, string language)
        {
            // Validate inputs
            if (string.IsNullOrWhiteSpace(partType))
                throw new ArgumentException("Part type cannot be empty", nameof(partType));
            if (string.IsNullOrWhiteSpace(partValue))
                throw new ArgumentException("Part value cannot be empty", nameof(partValue));
            if (string.IsNullOrWhiteSpace(language))
                throw new ArgumentException("Language cannot be empty", nameof(language));

            _repository.DeletePADetails(partType, partValue, language);
        }
    }
} 