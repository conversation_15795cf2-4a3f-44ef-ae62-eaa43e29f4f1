using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using ipis_V2_jules.ApiClients; // For TrainDataErail
using ipis_V2_jules.DisplayFormatters; // For IDisplayDataFormatter, FormattedDisplayData

// Assuming DisplayBoardConfig keys: "BoardId", "CharsPerLine" (number of coach slots), "CoachSlotLength" (chars per coach name)
// Assuming PlatformInfo is not directly used by CGDB formatter but is part of the interface.

namespace ipis_V2_jules.Services.DisplayBoard.DisplayFormatters
{
    public class CgdbDataFormatter : IDisplayDataFormatter
    {
        public FormattedDisplayData FormatTrainData(TrainDataErail trainInfo, Dictionary<string, string> platformInfo, Dictionary<string, string> boardConfig)
        {
            Console.WriteLine($"CGDB Formatter: Formatting Coach Data for TrainNo: {trainInfo?.TrainNo ?? "N/A"}");

            if (trainInfo == null || trainInfo.Coaches == null || !trainInfo.Coaches.Any())
            {
                // CGDBs primarily show coach info. If not available, show a default message or clear.
                return FormatMessage("NO COACH DATA", boardConfig);
            }

            byte boardId = boardConfig.TryGetValue("BoardId", out string idStr) && byte.TryParse(idStr, out byte id) ? id : (byte)0x01;
            // "CharsPerLine" for CGDB might represent the number of coach "slots" it can display
            int coachSlots = boardConfig.TryGetValue("CharsPerLine", out string slotsStr) && int.TryParse(slotsStr, out int slots) ? slots : 12; // Default 12 coach slots
            int coachSlotLength = boardConfig.TryGetValue("CoachSlotLength", out string lenStr) && int.TryParse(lenStr, out int len) ? len : 4; // Default 4 chars per coach (e.g. " S1 " or "GEN ")

            var formattedData = new FormattedDisplayData();
            StringBuilder coachLineBuilder = new StringBuilder();

            for (int i = 0; i < Math.Min(trainInfo.Coaches.Count, coachSlots); i++)
            {
                string coachName = trainInfo.Coaches[i].ToUpper();
                if (coachName.Length > coachSlotLength)
                {
                    coachName = coachName.Substring(0, coachSlotLength); // Truncate
                }
                else if (coachName.Length < coachSlotLength)
                {
                    // Pad with spaces, typically centered or left-aligned based on board behavior
                    // Example: Left align: coachName.PadRight(coachSlotLength)
                    // Example: Center:
                    int padding = coachSlotLength - coachName.Length;
                    int padLeft = padding / 2;
                    int padRight = padding - padLeft;
                    coachName = new string(' ', padLeft) + coachName + new string(' ', padRight);
                }
                coachLineBuilder.Append(coachName);
            }

            // If fewer coaches than slots, fill remaining slots with spaces
            int currentLength = coachLineBuilder.Length;
            int totalExpectedChars = coachSlots * coachSlotLength;
            if (currentLength < totalExpectedChars)
            {
                coachLineBuilder.Append(new string(' ', totalExpectedChars - currentLength));
            }


            formattedData.Line1 = ConvertTextToAsciiBytes(coachLineBuilder.ToString(), totalExpectedChars);
            formattedData.Line2 = null;
            formattedData.Line3 = null;

            // Placeholder AdditionalHeaderBytes. This will need refinement.
            // It might include a command byte specific to "display coach composition".
            formattedData.AdditionalHeaderBytes = new List<byte> { boardId, 0x02 }; // Example: boardId, command for coach display

            Console.WriteLine($"CGDB Formatter: Coach display string: \"{coachLineBuilder.ToString()}\", Bytes: {formattedData.Line1?.Length ?? 0}");
            return formattedData;
        }

        public FormattedDisplayData FormatMessage(string message, Dictionary<string, string> boardConfig)
        {
            Console.WriteLine($"CGDB Formatter: Formatting Message: \"{message.Substring(0, Math.Min(message.Length, 20))}...\"");

            byte boardId = boardConfig.TryGetValue("BoardId", out string idStr) && byte.TryParse(idStr, out byte id) ? id : (byte)0x01;
            // For messages, CharsPerLine might be the total character capacity of the display line used for messages
            int charsPerLine = boardConfig.TryGetValue("MessageLengthChars", out string charsStr) && int.TryParse(charsStr, out int cpl) ? cpl : 48; // Default for messages

            var formattedData = new FormattedDisplayData();
            message = message.ToUpper();

            formattedData.Line1 = ConvertTextToAsciiBytes(message, charsPerLine);
            formattedData.Line2 = null;
            formattedData.Line3 = null;

            // Placeholder AdditionalHeaderBytes. This might use a different command for generic text.
            formattedData.AdditionalHeaderBytes = new List<byte> { boardId, 0x01 }; // Example: boardId, command for text display

            return formattedData;
        }

        private byte[] ConvertTextToAsciiBytes(string text, int maxLength)
        {
            if (text == null) text = "";

            if (text.Length > maxLength)
            {
                text = text.Substring(0, maxLength); // Truncate
            }
            else if (text.Length < maxLength)
            {
                text = text.PadRight(maxLength, ' '); // Pad with spaces
            }
            return Encoding.ASCII.GetBytes(text);
        }
    }
}
