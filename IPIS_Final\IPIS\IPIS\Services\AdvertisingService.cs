using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using IPIS.Repositories;
using IPIS.Utils;
using IPIS.Models;

namespace IPIS.Services
{
    public class AdvertisingService
    {
        private readonly IAdvertisingRepository _repository;

        public AdvertisingService(IAdvertisingRepository repository)
        {
            _repository = repository;
        }

        public DataTable GetAllAdvertisements()
        {
            return _repository.GetAllAdvertisements();
        }

        public void AddAdvertisement(string annType, string adverName, string hindiWave, string engWave, string adverTime, string adverCount)
        {
            var requestParams = new Dictionary<string, object>
            {
                ["annType"] = annType,
                ["adverName"] = adverName,
                ["hindiWave"] = hindiWave ?? "null",
                ["engWave"] = engWave ?? "null",
                ["adverTime"] = adverTime,
                ["adverCount"] = adverCount
            };

            try
            {
                // Log request
                Logger.LogRequest(LogCategory.Advertising, "AddAdvertisement", requestParams, "AdvertisingService.AddAdvertisement");

                // Validate inputs
                if (string.IsNullOrWhiteSpace(annType))
                    throw new ArgumentException("Announcement type cannot be empty", nameof(annType));
                if (string.IsNullOrWhiteSpace(adverName))
                    throw new ArgumentException("Advertisement name cannot be empty", nameof(adverName));

                // Check for duplicate entry
                var existingAds = _repository.GetAllAdvertisements();
                var duplicate = existingAds.Select($"Adver_Name = '{adverName}' AND Ann_Type = '{annType}'");
                if (duplicate.Length > 0)
                    throw new InvalidOperationException("An advertisement with this name and type already exists");

                _repository.AddAdvertisement(annType, adverName, hindiWave, engWave, adverTime, adverCount,
                    "ALL", null, null, "Before", 1, 0, 0, 0, 0, 0, 0, "");

                // Log successful response
                var responseData = new
                {
                    adverName = adverName,
                    annType = annType,
                    adverTime = adverTime,
                    adverCount = adverCount,
                    status = "created"
                };
                Logger.LogResponse(LogCategory.Advertising, "AddAdvertisement", true, responseData, null, "AdvertisingService.AddAdvertisement");
                Logger.LogDatabaseOperation("INSERT", "Advertising", true, $"Advertisement {adverName} added successfully");
            }
            catch (Exception ex)
            {
                // Log failed response
                Logger.LogResponse(LogCategory.Advertising, "AddAdvertisement", false, null, ex.Message, "AdvertisingService.AddAdvertisement");
                Logger.LogDatabaseOperation("INSERT", "Advertising", false, $"Failed to add advertisement {adverName}: {ex.Message}");
                throw;
            }
        }

        public void UpdateAdvertisement(string annType, string adverName, string hindiWave, string engWave, string adverTime, string adverCount)
        {
            // Validate inputs
            if (string.IsNullOrWhiteSpace(annType))
                throw new ArgumentException("Announcement type cannot be empty", nameof(annType));
            if (string.IsNullOrWhiteSpace(adverName))
                throw new ArgumentException("Advertisement name cannot be empty", nameof(adverName));

            // Check if the advertisement exists
            var existingAds = _repository.GetAllAdvertisements();
            var existing = existingAds.Select($"Adver_Name = '{adverName}' AND Ann_Type = '{annType}'");
            if (existing.Length == 0)
                throw new InvalidOperationException("Advertisement not found");

            _repository.UpdateAdvertisement(annType, adverName, hindiWave, engWave, adverTime, adverCount,
                "ALL", null, null, "Before", 1, 0, 0, 0, 0, 0, 0, "");
        }

        public void DeleteAdvertisement(string annType, string adverName)
        {
            // Validate inputs
            if (string.IsNullOrWhiteSpace(annType))
                throw new ArgumentException("Announcement type cannot be empty", nameof(annType));
            if (string.IsNullOrWhiteSpace(adverName))
                throw new ArgumentException("Advertisement name cannot be empty", nameof(adverName));

            // Check if the advertisement exists
            var existingAds = _repository.GetAllAdvertisements();
            var existing = existingAds.Select($"Adver_Name = '{adverName}' AND Ann_Type = '{annType}'");
            if (existing.Length == 0)
                throw new InvalidOperationException("Advertisement not found");

            _repository.DeleteAdvertisement(annType, adverName);
        }

        // New overload for AddAdvertisement with all advanced fields (now 17 arguments, no language)
        public void AddAdvertisement(string annType, string adverName, string hindiWave, string engWave, string adverTime, string adverCount,
            string platform, string timeSlot, string trainNumber, string playPosition, int rank, int randomize, int monthQuota, int weekQuota, int dayQuota, int slotQuota, int extraQuota)
        {
            var requestParams = new Dictionary<string, object>
            {
                ["annType"] = annType,
                ["adverName"] = adverName,
                ["hindiWave"] = hindiWave ?? "null",
                ["engWave"] = engWave ?? "null",
                ["adverTime"] = adverTime,
                ["adverCount"] = adverCount,
                ["platform"] = platform,
                ["timeSlot"] = timeSlot,
                ["trainNumber"] = trainNumber,
                ["playPosition"] = playPosition,
                ["rank"] = rank,
                ["randomize"] = randomize,
                ["monthQuota"] = monthQuota,
                ["weekQuota"] = weekQuota,
                ["dayQuota"] = dayQuota,
                ["slotQuota"] = slotQuota,
                ["extraQuota"] = extraQuota
            };
            // Validation (basic)
            if (string.IsNullOrWhiteSpace(annType))
                throw new ArgumentException("Announcement type cannot be empty", nameof(annType));
            if (string.IsNullOrWhiteSpace(adverName))
                throw new ArgumentException("Advertisement name cannot be empty", nameof(adverName));
            // Check for duplicate entry
            var existingAds = _repository.GetAllAdvertisements();
            var duplicate = existingAds.Select($"Adver_Name = '{adverName}' AND Ann_Type = '{annType}'");
            if (duplicate.Length > 0)
                throw new InvalidOperationException("An advertisement with this name and type already exists");
            _repository.AddAdvertisement(annType, adverName, hindiWave, engWave, adverTime, adverCount, platform, timeSlot, trainNumber, playPosition, rank, randomize, monthQuota, weekQuota, dayQuota, slotQuota, extraQuota, "");
        }

        // New overload for UpdateAdvertisement with all advanced fields (now 17 arguments, no language)
        public void UpdateAdvertisement(string annType, string adverName, string hindiWave, string engWave, string adverTime, string adverCount,
            string platform, string timeSlot, string trainNumber, string playPosition, int rank, int randomize, int monthQuota, int weekQuota, int dayQuota, int slotQuota, int extraQuota)
        {
            if (string.IsNullOrWhiteSpace(annType))
                throw new ArgumentException("Announcement type cannot be empty", nameof(annType));
            if (string.IsNullOrWhiteSpace(adverName))
                throw new ArgumentException("Advertisement name cannot be empty", nameof(adverName));
            var existingAds = _repository.GetAllAdvertisements();
            var existing = existingAds.Select($"Adver_Name = '{adverName}' AND Ann_Type = '{annType}'");
            if (existing.Length == 0)
                throw new InvalidOperationException("Advertisement not found");
            _repository.UpdateAdvertisement(annType, adverName, hindiWave, engWave, adverTime, adverCount, platform, timeSlot, trainNumber, playPosition, rank, randomize, monthQuota, weekQuota, dayQuota, slotQuota, extraQuota, "");
        }

        private int GetAdvertisingId(string annType, string adverName)
        {
            using (var connection = new System.Data.SQLite.SQLiteConnection(IPIS.Utils.Database.ConnectionString))
            {
                connection.Open();
                using (var command = new System.Data.SQLite.SQLiteCommand(connection))
                {
                    command.CommandText = "SELECT rowid FROM Advertising WHERE Ann_Type = @annType AND Adver_Name = @adverName";
                    command.Parameters.AddWithValue("@annType", annType);
                    command.Parameters.AddWithValue("@adverName", adverName);
                    var result = command.ExecuteScalar();
                    if (result != null && int.TryParse(result.ToString(), out int id))
                        return id;
                }
            }
            return -1;
        }

        public void AddAdvertisementWithLanguages(string annType, string adverName, Dictionary<string, string> languageWaves,
            string platform, string timeSlot, string trainNumber, string playPosition, int rank, int randomize,
            int monthQuota, int weekQuota, int dayQuota, int slotQuota, int extraQuota, string days)
        {
            var requestParams = new Dictionary<string, object>
            {
                ["annType"] = annType,
                ["adverName"] = adverName,
                ["languageWaves"] = string.Join(",", languageWaves.Select(kvp => $"{kvp.Key}:{kvp.Value}")),
                ["platform"] = platform,
                ["timeSlot"] = timeSlot,
                ["trainNumber"] = trainNumber,
                ["playPosition"] = playPosition,
                ["rank"] = rank,
                ["randomize"] = randomize,
                ["monthQuota"] = monthQuota,
                ["weekQuota"] = weekQuota,
                ["dayQuota"] = dayQuota,
                ["slotQuota"] = slotQuota,
                ["extraQuota"] = extraQuota,
                ["days"] = days
            };

            try
            {
                // Log request
                Logger.LogRequest(LogCategory.Advertising, "AddAdvertisementWithLanguages", requestParams, "AdvertisingService.AddAdvertisementWithLanguages");

                // Validate inputs
                if (string.IsNullOrWhiteSpace(annType))
                    throw new ArgumentException("Announcement type cannot be empty", nameof(annType));
                if (string.IsNullOrWhiteSpace(adverName))
                    throw new ArgumentException("Advertisement name cannot be empty", nameof(adverName));

                // Check for duplicate entry
                var existingAds = _repository.GetAllAdvertisements();
                var duplicate = existingAds.Select($"Adver_Name = '{adverName}' AND Ann_Type = '{annType}'");
                if (duplicate.Length > 0)
                    throw new InvalidOperationException("An advertisement with this name and type already exists");

                // Extract Hindi and English waves for backward compatibility
                string hindiWave = languageWaves.ContainsKey("HINDI") ? languageWaves["HINDI"] : "";
                string engWave = languageWaves.ContainsKey("ENGLISH") ? languageWaves["ENGLISH"] : "";

                // Add the advertisement
                _repository.AddAdvertisement(annType, adverName, hindiWave, engWave, "", "",
                    platform, timeSlot, trainNumber, playPosition, rank, randomize,
                    monthQuota, weekQuota, dayQuota, slotQuota, extraQuota, days);

                // Fetch the new AdvertisingId
                int advertisingId = GetAdvertisingId(annType, adverName);
                if (advertisingId > 0)
                {
                    // Save language-specific wave files using ID
                    _repository.SaveAdvertisementLanguageWaves(advertisingId, annType, adverName, languageWaves);
                }
                else
                {
                    // Fallback to old method if ID not found
                    _repository.SaveAdvertisementLanguageWaves(annType, adverName, languageWaves);
                }

                // Log success
                Logger.LogInfo(LogCategory.Advertising, "Advertisement with languages added successfully",
                    $"Advertisement: {adverName}, Type: {annType}, Languages: {string.Join(", ", languageWaves.Keys)}",
                    "AdvertisingService.AddAdvertisementWithLanguages");
            }
            catch (Exception ex)
            {
                Logger.LogError(LogCategory.Advertising, "Failed to add advertisement with languages",
                    $"Advertisement: {adverName}, Type: {annType}",
                    "AdvertisingService.AddAdvertisementWithLanguages", ex);
                throw;
            }
        }

        public void UpdateAdvertisementWithLanguages(string annType, string adverName, Dictionary<string, string> languageWaves,
            string platform, string timeSlot, string trainNumber, string playPosition, int rank, int randomize,
            int monthQuota, int weekQuota, int dayQuota, int slotQuota, int extraQuota, string days)
        {
            var requestParams = new Dictionary<string, object>
            {
                ["annType"] = annType,
                ["adverName"] = adverName,
                ["languageWaves"] = string.Join(",", languageWaves.Select(kvp => $"{kvp.Key}:{kvp.Value}")),
                ["platform"] = platform,
                ["timeSlot"] = timeSlot,
                ["trainNumber"] = trainNumber,
                ["playPosition"] = playPosition,
                ["rank"] = rank,
                ["randomize"] = randomize,
                ["monthQuota"] = monthQuota,
                ["weekQuota"] = weekQuota,
                ["dayQuota"] = dayQuota,
                ["slotQuota"] = slotQuota,
                ["extraQuota"] = extraQuota,
                ["days"] = days
            };

            try
            {
                // Log request
                Logger.LogRequest(LogCategory.Advertising, "UpdateAdvertisementWithLanguages", requestParams, "AdvertisingService.UpdateAdvertisementWithLanguages");

                // Validate inputs
                if (string.IsNullOrWhiteSpace(annType))
                    throw new ArgumentException("Announcement type cannot be empty", nameof(annType));
                if (string.IsNullOrWhiteSpace(adverName))
                    throw new ArgumentException("Advertisement name cannot be empty", nameof(adverName));

                // Check if the advertisement exists
                var existingAds = _repository.GetAllAdvertisements();
                var existing = existingAds.Select($"Adver_Name = '{adverName}' AND Ann_Type = '{annType}'");
                if (existing.Length == 0)
                    throw new InvalidOperationException("Advertisement not found");

                // Extract Hindi and English waves for backward compatibility
                string hindiWave = languageWaves.ContainsKey("HINDI") ? languageWaves["HINDI"] : "";
                string engWave = languageWaves.ContainsKey("ENGLISH") ? languageWaves["ENGLISH"] : "";

                // Update the advertisement
                _repository.UpdateAdvertisement(annType, adverName, hindiWave, engWave, "", "",
                    platform, timeSlot, trainNumber, playPosition, rank, randomize,
                    monthQuota, weekQuota, dayQuota, slotQuota, extraQuota, days);

                // Fetch the AdvertisingId
                int advertisingId = GetAdvertisingId(annType, adverName);
                if (advertisingId > 0)
                {
                    // Save language-specific wave files using ID
                    _repository.SaveAdvertisementLanguageWaves(advertisingId, annType, adverName, languageWaves);
                }
                else
                {
                    // Fallback to old method if ID not found
                    _repository.SaveAdvertisementLanguageWaves(annType, adverName, languageWaves);
                }

                // Log success
                Logger.LogInfo(LogCategory.Advertising, "Advertisement with languages updated successfully",
                    $"Advertisement: {adverName}, Type: {annType}, Languages: {string.Join(", ", languageWaves.Keys)}",
                    "AdvertisingService.UpdateAdvertisementWithLanguages");
            }
            catch (Exception ex)
            {
                Logger.LogError(LogCategory.Advertising, "Failed to update advertisement with languages",
                    $"Advertisement: {adverName}, Type: {annType}",
                    "AdvertisingService.UpdateAdvertisementWithLanguages", ex);
                throw;
            }
        }

        public Dictionary<string, string> GetAdvertisementLanguageWaves(string annType, string adverName)
        {
            return _repository.GetAdvertisementLanguageWaves(annType, adverName);
        }

        public void UpdateTotalDuration(string annType, string adverName, double totalDuration, string formattedDuration)
        {
            try
            {
                // Validate inputs
                if (string.IsNullOrWhiteSpace(annType))
                    throw new ArgumentException("Announcement type cannot be empty", nameof(annType));
                if (string.IsNullOrWhiteSpace(adverName))
                    throw new ArgumentException("Advertisement name cannot be empty", nameof(adverName));

                // Check if the advertisement exists
                var existingAds = _repository.GetAllAdvertisements();
                var existing = existingAds.Select($"Adver_Name = '{adverName}' AND Ann_Type = '{annType}'");
                if (existing.Length == 0)
                    throw new InvalidOperationException("Advertisement not found");

                _repository.UpdateTotalDuration(annType, adverName, totalDuration, formattedDuration);

                // Log the operation
                Logger.LogDatabaseOperation("UPDATE", "Advertising", true,
                    $"Updated total duration for advertisement {adverName}: {formattedDuration}");
            }
            catch (Exception ex)
            {
                Logger.LogDatabaseOperation("UPDATE", "Advertising", false,
                    $"Failed to update total duration for advertisement {adverName}: {ex.Message}");
                throw;
            }
        }

        public void IncrementQuotaUsed(string annType, string adverName)
        {
            var requestParams = new Dictionary<string, object>
            {
                ["annType"] = annType,
                ["adverName"] = adverName
            };

            try
            {
                Logger.LogRequest(LogCategory.Advertising, "IncrementQuotaUsed", requestParams, "AdvertisingService.IncrementQuotaUsed");

                // Validate inputs
                if (string.IsNullOrWhiteSpace(annType))
                    throw new ArgumentException("Announcement type cannot be empty", nameof(annType));
                if (string.IsNullOrWhiteSpace(adverName))
                    throw new ArgumentException("Advertisement name cannot be empty", nameof(adverName));

                // Check if the advertisement exists
                var existingAds = _repository.GetAllAdvertisements();
                var existing = existingAds.Select($"Adver_Name = '{adverName}' AND Ann_Type = '{annType}'");
                if (existing.Length == 0)
                    throw new InvalidOperationException("Advertisement not found");

                _repository.IncrementQuotaUsed(annType, adverName);

                var responseData = new
                {
                    annType = annType,
                    adverName = adverName,
                    status = "quota_incremented"
                };
                Logger.LogResponse(LogCategory.Advertising, "IncrementQuotaUsed", true, responseData, null, "AdvertisingService.IncrementQuotaUsed");
                Logger.LogDatabaseOperation("UPDATE", "Advertising", true, $"Quota used incremented for {adverName}");
            }
            catch (Exception ex)
            {
                Logger.LogResponse(LogCategory.Advertising, "IncrementQuotaUsed", false, null, ex.Message, "AdvertisingService.IncrementQuotaUsed");
                Logger.LogDatabaseOperation("UPDATE", "Advertising", false, $"Failed to increment quota used for {adverName}: {ex.Message}");
                throw;
            }
        }
    }
}