using System;
using System.Threading.Tasks;
using IPIS.Models;

namespace IPIS.Repositories.Interfaces
{
    public interface IStationAnnouncementConfigRepository
    {
        Task<StationAnnouncementConfig> GetByStationNameAsync(string stationName);
        Task<int> AddAsync(StationAnnouncementConfig config);
        Task<bool> UpdateAsync(StationAnnouncementConfig config);
        Task<bool> DeleteAsync(int id);
        Task<bool> DeleteByStationAsync(string stationName);
        Task<bool> ExistsAsync(string stationName);
    }
} 