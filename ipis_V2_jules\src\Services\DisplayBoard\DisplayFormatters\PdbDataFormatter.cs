using System;
using System.Collections.Generic;
using System.Linq;
using ipis_V2_jules.ApiClients; // For TrainDataErail
using ipis_V2_jules.DisplayFormatters; // For IDisplayDataFormatter, FormattedDisplayData, SimpleFont

// Assuming DisplayBoardConfig keys: "Lines", "CharsPerLine", "BoardId"
// Assuming PlatformInfo keys: "Platform", "ETA", "StatusMessage"

namespace ipis_V2_jules.Services.DisplayBoard.DisplayFormatters
{
    public class PdbDataFormatter : IDisplayDataFormatter
    {
        // For this iteration, PDB formatting is assumed to be identical to MLDB.
        // If specific differences arise (e.g., in header bytes or bitmap structure),
        // this class can be diverged from MldbDataFormatter.

        public FormattedDisplayData FormatTrainData(TrainDataErail trainInfo, Dictionary<string, string> platformInfo, Dictionary<string, string> boardConfig)
        {
            Console.WriteLine($"PDB Formatter: Formatting Train Data for TrainNo: {trainInfo?.TrainNo ?? "N/A"} (using MLDB logic)");

            if (trainInfo == null)
            {
                return FormatMessage("NO TRAIN DATA", boardConfig);
            }

            int lines = boardConfig.TryGetValue("Lines", out string linesStr) && int.TryParse(linesStr, out int l) ? l : 1;
            int charsPerLine = boardConfig.TryGetValue("CharsPerLine", out string charsStr) && int.TryParse(charsStr, out int cpl) ? cpl : 20;
            byte boardId = boardConfig.TryGetValue("BoardId", out string idStr) && byte.TryParse(idStr, out byte id) ? id : (byte)0x01;

            var formattedData = new FormattedDisplayData();

            string line1Text = $"{trainInfo.TrainNo} {trainInfo.TrainName}".ToUpper();

            string platformStr = platformInfo.GetValueOrDefault("Platform", "N/A") ?? "N/A";
            string etaStr = platformInfo.GetValueOrDefault("ETA", trainInfo.StartTime) ?? "N/A";
            string statusMsg = platformInfo.GetValueOrDefault("StatusMessage", "ON TIME") ?? "ON TIME";
            string line2Text = $"PF {platformStr} ETA {etaStr} STS {statusMsg}".ToUpper();

            formattedData.Line1 = ConvertTextToBitmapBytes(line1Text, charsPerLine);
            Console.WriteLine($"PDB Formatter: Line 1 Text: \"{line1Text}\", Bytes: {formattedData.Line1?.Length ?? 0}");

            if (lines >= 2)
            {
                formattedData.Line2 = ConvertTextToBitmapBytes(line2Text, charsPerLine);
                Console.WriteLine($"PDB Formatter: Line 2 Text: \"{line2Text}\", Bytes: {formattedData.Line2?.Length ?? 0}");
            }
            else
            {
                formattedData.Line2 = null;
            }
            formattedData.Line3 = null;

            formattedData.AdditionalHeaderBytes = new List<byte> { boardId };
            // formattedData.AdditionalHeaderBytes = null;

            return formattedData;
        }

        public FormattedDisplayData FormatMessage(string message, Dictionary<string, string> boardConfig)
        {
            Console.WriteLine($"PDB Formatter: Formatting Message: \"{message.Substring(0, Math.Min(message.Length, 20))}...\" (using MLDB logic)");

            int lines = boardConfig.TryGetValue("Lines", out string linesStr) && int.TryParse(linesStr, out int l) ? l : 1;
            int charsPerLine = boardConfig.TryGetValue("CharsPerLine", out string charsStr) && int.TryParse(charsStr, out int cpl) ? cpl : 20;
            byte boardId = boardConfig.TryGetValue("BoardId", out string idStr) && byte.TryParse(idStr, out byte id) ? id : (byte)0x01;

            var formattedData = new FormattedDisplayData();
            message = message.ToUpper();

            List<string> messageLines = new List<string>();
            for (int i = 0; i < message.Length; i += charsPerLine)
            {
                messageLines.Add(message.Substring(i, Math.Min(charsPerLine, message.Length - i)));
            }

            if (messageLines.Count > 0)
            {
                formattedData.Line1 = ConvertTextToBitmapBytes(messageLines[0], charsPerLine);
            }

            if (lines >= 2)
            {
                if (messageLines.Count > 1)
                    formattedData.Line2 = ConvertTextToBitmapBytes(messageLines[1], charsPerLine);
                else
                    formattedData.Line2 = ConvertTextToBitmapBytes("", charsPerLine); // Empty line
            }
            else
            {
                formattedData.Line2 = null;
            }
            formattedData.Line3 = null;

            formattedData.AdditionalHeaderBytes = new List<byte> { boardId };
            // formattedData.AdditionalHeaderBytes = null;

            return formattedData;
        }

        private byte[] ConvertTextToBitmapBytes(string text, int charsPerLine)
        {
            if (text == null) text = "";
            text = text.ToUpper();

            List<byte> lineBitmapBytes = new List<byte>();

            for (int i = 0; i < charsPerLine; i++)
            {
                char c = (i < text.Length) ? text[i] : ' ';
                byte[] charBitmap = SimpleFont.GetCharBitmap(c);
                lineBitmapBytes.AddRange(charBitmap);
            }
            return lineBitmapBytes.ToArray();
        }
    }
}
