# Language Differences in IPIS Audio Sequences

## Overview
This document summarizes the key differences in audio file sequences between English and Hindi languages in the IPIS system.

## Key Differences

### 1. Language-Specific Keywords

| Function | English | Hindi |
|----------|---------|-------|
| Train Number | ETRN.wav | HTRN.wav |
| From Station | EFROM.wav | HFROM.wav |
| To Station | ETO.wav | HTO.wav |
| Via Station | EVIA.wav | HVIA.wav |
| Platform | PATO.wav | LTO.wav |
| Up Direction | UP.wav | UP.wav |
| Down Direction | DN.wav | DN.wav |

### 2. Audio File Locations

**English Files:**
- Base Path: `Data/WAVE/ENGLISH/`
- Keywords: `Data/WAVE/ENGLISH/EFROM.wav`, `Data/WAVE/ENGLISH/ETO.wav`, etc.

**Hindi Files:**
- Base Path: `Data/WAVE/HINDI/`
- Keywords: `Data/WAVE/HINDI/HFROM.wav`, `Data/WAVE/HINDI/HTO.wav`, etc.

### 3. Content Differences

#### Standard Status Messages
- **STD1.wav**: 
  - English: "may I have your attention please"
  - Hindi: "kirpya dhayn diji"
- Most other STD messages are primarily in English
- Some messages may not exist in both languages

#### Station Names
- English: Uses English pronunciation of station names
- Hindi: Uses Hindi pronunciation of station names
- Example: "Delhi" vs "दिल्ली"

#### Train Names
- English: English pronunciation of train names
- Hindi: Hindi pronunciation of train names
- Example: "Rajdhani Express" vs "राजधानी एक्सप्रेस"

### 4. Sequence Structure

The basic sequence structure remains the same for both languages:

1. **Attention Bell** (`SPL/TADA.wav`) - Same for both
2. **Attention Message** (`STD1.wav`) - Different content
3. **Train Number Keyword** - Language-specific (ETRN/HTRN)
4. **Train Number Digits** - Same digit files
5. **Train Name** - Language-specific pronunciation
6. **Station Information** - Language-specific keywords and names
7. **Status Message** - Language-specific content
8. **Platform/Time Information** - Same digit files

### 5. Implementation Notes

#### Code Implementation
```csharp
// Language-specific keyword selection
var trainNumberKeyword = GetAudioFilesForPart("Keyword", 
    language.Code == "EN" ? "ETRN" : "HTRN", language);

// Language-specific station keywords
var fromFiles = GetAudioFilesForPart("Keyword", 
    language.Code == "EN" ? "EFROM" : "HFROM", language);
var toFiles = GetAudioFilesForPart("Keyword", 
    language.Code == "EN" ? "ETO" : "HTO", language);
var viaFiles = GetAudioFilesForPart("Keyword", 
    language.Code == "EN" ? "EVIA" : "HVIA", language);
```

#### File Naming Patterns
- **English**: E-prefixed keywords (EFROM, ETO, EVIA, ETRN)
- **Hindi**: H-prefixed keywords (HFROM, HTO, HVIA, HTRN)
- **Digits**: Same files for both languages (0.wav, 1.wav, etc.)
- **Status Messages**: STD1.wav, STD2.wav, etc. (content varies)

### 6. Example Comparison

**For Train 12345 "Rajdhani Express" from Delhi to Mumbai:**

**English Sequence:**
1. `SPL/TADA.wav`
2. `ENGLISH/STD/STD1.wav` ("may I have your attention please")
3. `ENGLISH/ETRN.wav`
4. `ENGLISH/TRNO/1.wav`, `ENGLISH/TRNO/2.wav`, etc.
5. `TRAIN TYPE/Rajdhani Express.wav`
6. `ENGLISH/EFROM.wav`
7. `ENGLISH/CITY/Delhi.wav`
8. `ENGLISH/ETO.wav`
9. `ENGLISH/CITY/Mumbai.wav`

**Hindi Sequence:**
1. `SPL/TADA.wav`
2. `HINDI/STD/STD1.wav` ("kirpya dhayn diji")
3. `HINDI/HTRN.wav`
4. `HINDI/TRNO/1.wav`, `HINDI/TRNO/2.wav`, etc.
5. `TRAIN TYPE/Rajdhani Express.wav`
6. `HINDI/HFROM.wav`
7. `HINDI/CITY/Delhi.wav`
8. `HINDI/HTO.wav`
9. `HINDI/CITY/Mumbai.wav`

### 7. Common Elements

**Same for Both Languages:**
- Special sounds (TADA.wav, Bell.wav)
- Digit pronunciations (0-9)
- Train type announcements
- Platform number digits
- Time announcements (hours/minutes)
- Delay time announcements

**Different Between Languages:**
- Keywords (EFROM vs HFROM, etc.)
- Status message content
- Station name pronunciations
- Train name pronunciations
- Attention messages

### 8. Maintenance Considerations

1. **File Organization**: Keep English and Hindi files in separate folders
2. **Naming Convention**: Use consistent prefixes (E- for English, H- for Hindi)
3. **Content Synchronization**: Ensure both languages have equivalent messages
4. **Testing**: Test sequences in both languages to ensure consistency
5. **Documentation**: Maintain separate documentation for each language

This summary provides a quick reference for understanding the key differences between English and Hindi audio sequences in the IPIS system. 