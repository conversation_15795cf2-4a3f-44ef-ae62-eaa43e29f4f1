using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using IPIS.Models;

namespace IPIS.Repositories.Interfaces
{
    public interface IStationLanguageConfigRepository
    {
        Task<List<StationLanguageConfig>> GetByStationNameAsync(string stationName);
        Task<StationLanguageConfig> GetByStationAndLanguageAsync(string stationName, string languageCode);
        Task<List<StationLanguageConfig>> GetEnabledByStationAsync(string stationName);
        Task<int> AddAsync(StationLanguageConfig config);
        Task<bool> UpdateAsync(StationLanguageConfig config);
        Task<bool> DeleteAsync(int id);
        Task<bool> DeleteByStationAndLanguageAsync(string stationName, string languageCode);
        Task<bool> ExistsAsync(string stationName, string languageCode);
        Task<List<string>> GetEnabledLanguageCodesAsync(string stationName);
        Task<Dictionary<string, string>> GetWaveFilePathsAsync(string stationName);
    }
} 