using System;
using System.Data.SQLite;
using System.Threading.Tasks;
using IPIS.Models;
using IPIS.Repositories.Interfaces;
using IPIS.Utils;

namespace IPIS.Repositories
{
    public class SQLiteStationAnnouncementConfigRepository : IStationAnnouncementConfigRepository
    {
        private readonly string _connectionString;

        public SQLiteStationAnnouncementConfigRepository()
        {
            _connectionString = Database.ConnectionString;
        }

        public async Task<StationAnnouncementConfig> GetByStationNameAsync(string stationName)
        {
            using (var connection = new SQLiteConnection(_connectionString))
            {
                await connection.OpenAsync();
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = @"
                        SELECT sac.*, 
                               l1.Name as FirstLanguageName, l1.NativeName as FirstLanguageNativeName,
                               l2.Name as SecondLanguageName, l2.NativeName as SecondLanguageNativeName
                        FROM Station_Announcement_Config sac
                        LEFT JOIN Languages l1 ON sac.First_Language_Code = l1.Code
                        LEFT JOIN Languages l2 ON sac.Second_Language_Code = l2.Code
                        WHERE sac.Station_Name = @StationName";
                    
                    command.Parameters.AddWithValue("@StationName", stationName);
                    
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        if (await reader.ReadAsync())
                        {
                            return MapReaderToStationAnnouncementConfig(reader);
                        }
                    }
                }
            }
            
            return null;
        }

        public async Task<int> AddAsync(StationAnnouncementConfig config)
        {
            using (var connection = new SQLiteConnection(_connectionString))
            {
                await connection.OpenAsync();
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = @"
                        INSERT INTO Station_Announcement_Config (Station_Name, First_Language_Code, Second_Language_Code, First_Language_Enabled, Second_Language_Enabled, Created_At)
                        VALUES (@StationName, @FirstLanguageCode, @SecondLanguageCode, @FirstLanguageEnabled, @SecondLanguageEnabled, @CreatedAt);
                        SELECT last_insert_rowid();";
                    
                    command.Parameters.AddWithValue("@StationName", config.StationName);
                    command.Parameters.AddWithValue("@FirstLanguageCode", config.FirstLanguageCode ?? "");
                    command.Parameters.AddWithValue("@SecondLanguageCode", config.SecondLanguageCode ?? "");
                    command.Parameters.AddWithValue("@FirstLanguageEnabled", config.FirstLanguageEnabled ? 1 : 0);
                    command.Parameters.AddWithValue("@SecondLanguageEnabled", config.SecondLanguageEnabled ? 1 : 0);
                    command.Parameters.AddWithValue("@CreatedAt", config.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss"));
                    
                    return Convert.ToInt32(await command.ExecuteScalarAsync());
                }
            }
        }

        public async Task<bool> UpdateAsync(StationAnnouncementConfig config)
        {
            using (var connection = new SQLiteConnection(_connectionString))
            {
                await connection.OpenAsync();
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = @"
                        UPDATE Station_Announcement_Config 
                        SET First_Language_Code = @FirstLanguageCode, 
                            Second_Language_Code = @SecondLanguageCode, 
                            First_Language_Enabled = @FirstLanguageEnabled, 
                            Second_Language_Enabled = @SecondLanguageEnabled, 
                            Updated_At = @UpdatedAt
                        WHERE Station_Name = @StationName";
                    
                    command.Parameters.AddWithValue("@StationName", config.StationName);
                    command.Parameters.AddWithValue("@FirstLanguageCode", config.FirstLanguageCode ?? "");
                    command.Parameters.AddWithValue("@SecondLanguageCode", config.SecondLanguageCode ?? "");
                    command.Parameters.AddWithValue("@FirstLanguageEnabled", config.FirstLanguageEnabled ? 1 : 0);
                    command.Parameters.AddWithValue("@SecondLanguageEnabled", config.SecondLanguageEnabled ? 1 : 0);
                    command.Parameters.AddWithValue("@UpdatedAt", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                    
                    return await command.ExecuteNonQueryAsync() > 0;
                }
            }
        }

        public async Task<bool> DeleteAsync(int id)
        {
            using (var connection = new SQLiteConnection(_connectionString))
            {
                await connection.OpenAsync();
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = "DELETE FROM Station_Announcement_Config WHERE Id = @Id";
                    command.Parameters.AddWithValue("@Id", id);
                    
                    return await command.ExecuteNonQueryAsync() > 0;
                }
            }
        }

        public async Task<bool> DeleteByStationAsync(string stationName)
        {
            using (var connection = new SQLiteConnection(_connectionString))
            {
                await connection.OpenAsync();
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = "DELETE FROM Station_Announcement_Config WHERE Station_Name = @StationName";
                    command.Parameters.AddWithValue("@StationName", stationName);
                    
                    return await command.ExecuteNonQueryAsync() > 0;
                }
            }
        }

        public async Task<bool> ExistsAsync(string stationName)
        {
            using (var connection = new SQLiteConnection(_connectionString))
            {
                await connection.OpenAsync();
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = "SELECT COUNT(*) FROM Station_Announcement_Config WHERE Station_Name = @StationName";
                    command.Parameters.AddWithValue("@StationName", stationName);
                    
                    var count = Convert.ToInt32(await command.ExecuteScalarAsync());
                    return count > 0;
                }
            }
        }

        private StationAnnouncementConfig MapReaderToStationAnnouncementConfig(System.Data.Common.DbDataReader reader)
        {
            return new StationAnnouncementConfig
            {
                Id = Convert.ToInt32(reader["Id"]),
                StationName = reader["Station_Name"].ToString(),
                FirstLanguageCode = reader["First_Language_Code"].ToString(),
                SecondLanguageCode = reader["Second_Language_Code"].ToString(),
                FirstLanguageEnabled = Convert.ToBoolean(reader["First_Language_Enabled"]),
                SecondLanguageEnabled = Convert.ToBoolean(reader["Second_Language_Enabled"]),
                CreatedAt = DateTime.Parse(reader["Created_At"].ToString()),
                UpdatedAt = reader["Updated_At"] != DBNull.Value ? DateTime.Parse(reader["Updated_At"].ToString()) : null,
                FirstLanguage = new Language
                {
                    Code = reader["First_Language_Code"].ToString(),
                    Name = reader["FirstLanguageName"]?.ToString() ?? "",
                    NativeName = reader["FirstLanguageNativeName"]?.ToString() ?? ""
                },
                SecondLanguage = new Language
                {
                    Code = reader["Second_Language_Code"].ToString(),
                    Name = reader["SecondLanguageName"]?.ToString() ?? "",
                    NativeName = reader["SecondLanguageNativeName"]?.ToString() ?? ""
                }
            };
        }
    }
} 