using System.Data;

namespace IPIS.Repositories.Interfaces
{
    public interface IPADetailsRepository
    {
        DataTable GetPADetails(string partType, string partValue, string language);
        void AddPADetails(string partType, string partValue, string language, string filePath);
        void UpdatePADetails(string partType, string partValue, string language, string filePath);
        void DeletePADetails(string partType, string partValue, string language);
    }
} 