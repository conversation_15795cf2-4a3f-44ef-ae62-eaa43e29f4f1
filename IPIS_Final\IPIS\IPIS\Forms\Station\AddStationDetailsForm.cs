using System;
using System.Drawing;
using System.Windows.Forms;
using IPIS.Utils;
using System.Collections.Generic;
using IPIS.Services;
using IPIS.Models;
using IPIS.Repositories;
using System.Linq;
using System.Threading.Tasks;

namespace IPIS.Forms.Station
{
    public partial class AddStationDetailsForm : Form
    {
        private const int DEFAULT_AVAILABLE_PLATFORMS = 3;
        private const int DEFAULT_MAXIMUM_PLATFORMS = 16;
        private readonly StationService stationService;
        private readonly LanguageService languageService;
        private readonly StationLanguageService stationLanguageService;
        private readonly ToastNotification toast;
        
        // Grid controls
        private DataGridView dgvStations;
        private TextBox txtSearch;
        private Button btnSearch, btnPrev, btnNext;
        private int currentPage = 1, pageSize = 25, totalRecords = 0;
        private List<StationDetails> allStations = new List<StationDetails>();
        private StationDetails editingStation = null;
        
        // <PERSON> controls
        private TextBox txtStationName;
        private TextBox txtStationCode;
        private NumericUpDown numAvailablePF;
        private TextBox[] platformTextBoxes;
        private TextBox txtWaveFile;
        private Button btnAdd, btnClear, btnCurrentStation;
        private bool isEditMode = false;

        public AddStationDetailsForm()
        {
            stationService = new StationService(new SQLiteStationRepository());
            languageService = new LanguageService(new SQLiteLanguageRepository());
            stationLanguageService = new StationLanguageService(
                new SQLiteStationLanguageConfigRepository(),
                new SQLiteStationAnnouncementConfigRepository(),
                new SQLiteLanguageRepository());
            platformTextBoxes = new TextBox[DEFAULT_MAXIMUM_PLATFORMS];
            InitializeComponent();
            toast = new ToastNotification(this);
            // Highlight current station row
            if (dgvStations != null)
                dgvStations.RowPrePaint += DgvStations_RowPrePaint;
            // Load stations
            LoadStations();
        }

        private void InitializeComponent()
        {
            this.Text = "Station Details Management";
            this.Size = new Size(1200, 700);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;
            this.BackColor = UIStyler.Colors.Background;

            // Main split container
            var split = new SplitContainer
            {
                Dock = DockStyle.Fill,
                Orientation = Orientation.Vertical,
                SplitterDistance = 500
            };
            this.Controls.Add(split);

            // Left Panel: DataGridView with search and pagination
            CreateLeftPanel(split.Panel1);

            // Right Panel: Station details form
            CreateRightPanel(split.Panel2);
        }

        private void CreateLeftPanel(Panel panel)
        {
            // Page size selector
            var lblPageSize = new Label { Text = "Page Size:" };
            UIStyler.ApplyLabelStyle(lblPageSize, "body");
            var cmbPageSize = new ComboBox { DropDownStyle = ComboBoxStyle.DropDownList };
            UIStyler.ApplyComboBoxStyle(cmbPageSize, "small");
            cmbPageSize.Items.AddRange(new object[] { 10, 25, 50, 100 });
            cmbPageSize.SelectedItem = 25; // Default
            cmbPageSize.SelectedIndexChanged += (s, e) => 
            { 
                pageSize = (int)cmbPageSize.SelectedItem; 
                currentPage = 1; 
                LoadStations(); 
            };

            // DataGridView
            dgvStations = new DataGridView
            {
                Dock = DockStyle.Fill,
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                AutoGenerateColumns = false,
                MultiSelect = false,
                RowHeadersVisible = false,
                Margin = new Padding(100, 0, 10, 0)
            };
            UIStyler.ApplyDataGridViewStyle(dgvStations);
            
            // Add columns
            dgvStations.Columns.Add(new DataGridViewTextBoxColumn { HeaderText = "Station Name", DataPropertyName = "StationName", Width = 150 });
            dgvStations.Columns.Add(new DataGridViewTextBoxColumn { HeaderText = "Code", DataPropertyName = "StationCode", Width = 80 });
            dgvStations.Columns.Add(new DataGridViewTextBoxColumn { HeaderText = "Platforms", DataPropertyName = "AvailablePF", Width = 80 });
            dgvStations.Columns.Add(new DataGridViewCheckBoxColumn { HeaderText = "Current", DataPropertyName = "IsCurrent", Name = "Current", Width = 70 });
            
            var btnEdit = new DataGridViewButtonColumn
            {
                HeaderText = "Edit",
                Width = 40,
                Text = "✏",
                UseColumnTextForButtonValue = true,
                FlatStyle = FlatStyle.Flat
            };
            var btnDelete = new DataGridViewButtonColumn
            {
                HeaderText = "Delete",
                Width = 40,
                Text = "🗑",
                UseColumnTextForButtonValue = true,
                FlatStyle = FlatStyle.Flat
            };
            var btnMarkCurrent = new DataGridViewButtonColumn { HeaderText = "Set as current station", Text = "Set as current station", UseColumnTextForButtonValue = true, Width = 110 };
            
            dgvStations.Columns.Add(btnEdit);
            dgvStations.Columns.Add(btnDelete);
            dgvStations.Columns.Add(btnMarkCurrent);
            
            dgvStations.CellClick += DgvStations_CellClick;
            panel.Controls.Add(dgvStations);

            // Pagination panel
            btnPrev = new Button { Text = "Previous" };
            btnNext = new Button { Text = "Next" };
            ButtonStyler.ApplyStandardStyle(btnPrev, "secondary", "small", 100);
            ButtonStyler.ApplyStandardStyle(btnNext, "secondary", "small", 100);
            btnPrev.Click += (s, e) => { if (currentPage > 1) { currentPage--; LoadStations(); } };
            btnNext.Click += (s, e) => { if (currentPage * pageSize < totalRecords) { currentPage++; LoadStations(); } };

            var paginationPanel = new FlowLayoutPanel 
            { 
                Dock = DockStyle.Bottom, 
                Height = 50, 
                AutoSize = true,
                Padding = new Padding(10),
                Margin = new Padding(0, 5, 0, 0),
                Location = new Point(0, 100)
            };
            // Add page size selector next to pagination buttons
            paginationPanel.Controls.Add(lblPageSize);
            paginationPanel.Controls.Add(cmbPageSize);
            paginationPanel.Controls.Add(btnPrev);
            paginationPanel.Controls.Add(btnNext);
            panel.Controls.Add(paginationPanel);
        }

        private void CreateRightPanel(Panel panel)
        {
            var mainPanel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(0) };
            panel.Controls.Add(mainPanel);

            // Main wrapper layout - 2 rows: search+title and form
            var wrapperLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 2,
                Padding = new Padding(10)
            };
            wrapperLayout.RowStyles.Add(new RowStyle(SizeType.AutoSize)); // Search+Title row
            wrapperLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 100)); // Form row
            mainPanel.Controls.Add(wrapperLayout);

            // Search bar panel (moved from left)
            var searchPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Top,
                ColumnCount = 1,
                RowCount = 2,
                AutoSize = true,
                Padding = new Padding(0, 0, 0, 0),
                Margin = new Padding(0, 0, 0, 0)
            };
            searchPanel.RowStyles.Add(new RowStyle(SizeType.AutoSize));
            searchPanel.RowStyles.Add(new RowStyle(SizeType.AutoSize));
            txtSearch = new TextBox { PlaceholderText = "Search station name or code...", Dock = DockStyle.Fill };
            UIStyler.ApplyTextBoxStyle(txtSearch, "medium");
            searchPanel.Controls.Add(txtSearch, 0, 0);
            var btnSearchPanel = new FlowLayoutPanel
            {
                Dock = DockStyle.Right,
                FlowDirection = FlowDirection.RightToLeft,
                WrapContents = false
            };
            btnSearch = new Button { Text = "Search", Anchor = AnchorStyles.Right };
            ButtonStyler.ApplyStandardStyle(btnSearch, "primary", "small");
            btnSearch.Click += (s, e) => { currentPage = 1; LoadStations(); };
            var btnClearSearch = new Button { Text = "Clear", Anchor = AnchorStyles.Right };
            ButtonStyler.ApplyStandardStyle(btnClearSearch, "secondary", "small");
            btnClearSearch.Click += (s, e) => { txtSearch.Text = ""; currentPage = 1; LoadStations(); };
            btnSearchPanel.Controls.Add(btnClearSearch);
            btnSearchPanel.Controls.Add(btnSearch);
            searchPanel.Controls.Add(btnSearchPanel, 0, 1);

            // Title
            var lblTitle = new Label { Text = "Station Details", Dock = DockStyle.Fill };
            UIStyler.ApplyLabelStyle(lblTitle, "h4");

            // Title+search layout
            var titleSearchPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 2,
                Padding = new Padding(0)
            };
            titleSearchPanel.RowStyles.Add(new RowStyle(SizeType.AutoSize));
            titleSearchPanel.RowStyles.Add(new RowStyle(SizeType.AutoSize));
            titleSearchPanel.Controls.Add(searchPanel, 0, 0);
            titleSearchPanel.Controls.Add(lblTitle, 0, 1);
            wrapperLayout.Controls.Add(titleSearchPanel, 0, 0);

            // Form layout - single column with alternating label-input rows
            var formLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 20,
                AutoSize = true,
                Padding = new Padding(10)
            };
            formLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100));
            wrapperLayout.Controls.Add(formLayout, 0, 1);

            // Station Name
            var lblStationName = new Label { Text = "Station Name" };
            UIStyler.ApplyLabelStyle(lblStationName, "body-bold");
            formLayout.Controls.Add(lblStationName, 0, 0);
            txtStationName = new TextBox { Dock = DockStyle.Fill };
            UIStyler.ApplyTextBoxStyle(txtStationName, "medium");
            formLayout.Controls.Add(txtStationName, 0, 1);

            // Station Code
            var lblStationCode = new Label { Text = "Station Code" };
            UIStyler.ApplyLabelStyle(lblStationCode, "body-bold");
            formLayout.Controls.Add(lblStationCode, 0, 2);
            txtStationCode = new TextBox { Dock = DockStyle.Fill };
            UIStyler.ApplyTextBoxStyle(txtStationCode, "medium");
            formLayout.Controls.Add(txtStationCode, 0, 3);

            // Available Platforms
            var lblAvailablePF = new Label { Text = "Available Platforms" };
            UIStyler.ApplyLabelStyle(lblAvailablePF, "body-bold");
            formLayout.Controls.Add(lblAvailablePF, 0, 4);
            numAvailablePF = new NumericUpDown { Minimum = 1, Maximum = DEFAULT_MAXIMUM_PLATFORMS, Value = DEFAULT_AVAILABLE_PLATFORMS, Dock = DockStyle.Fill };
            UIStyler.ApplyNumericUpDownStyle(numAvailablePF, "medium");
            numAvailablePF.ValueChanged += NumAvailablePF_ValueChanged;
            formLayout.Controls.Add(numAvailablePF, 0, 5);

            // Platform textboxes - arranged in rows of 5, all equal size
            var lblPlatforms = new Label { Text = "Platform Names" };
            UIStyler.ApplyLabelStyle(lblPlatforms, "body-bold");
            formLayout.Controls.Add(lblPlatforms, 0, 6);
            var pnlPlatforms = new TableLayoutPanel 
            { 
                ColumnCount = 5, 
                RowCount = 4, 
                AutoSize = true,
                Dock = DockStyle.Fill,
                Width = 250,
                Height = 120
            };
            for (int i = 0; i < 5; i++) pnlPlatforms.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 48));
            for (int i = 0; i < 4; i++) pnlPlatforms.RowStyles.Add(new RowStyle(SizeType.Absolute, 30));
            for (int i = 0; i < DEFAULT_MAXIMUM_PLATFORMS; i++)
            {
                platformTextBoxes[i] = new TextBox { Width = 45, Height = 28, Text = (i + 1).ToString(), TextAlign = HorizontalAlignment.Center, Margin = new Padding(2) };
                UIStyler.ApplyTextBoxStyle(platformTextBoxes[i], "small");
                platformTextBoxes[i].MinimumSize = new Size(45, 28);
                platformTextBoxes[i].MaximumSize = new Size(45, 28);
                platformTextBoxes[i].Enabled = i < DEFAULT_AVAILABLE_PLATFORMS;
                if (!platformTextBoxes[i].Enabled)
                {
                    platformTextBoxes[i].BackColor = System.Drawing.Color.LightGray;
                    platformTextBoxes[i].ForeColor = System.Drawing.Color.DimGray;
                }
                pnlPlatforms.Controls.Add(platformTextBoxes[i], i % 5, i / 5);
            }
            formLayout.Controls.Add(pnlPlatforms, 0, 7);

            // Action buttons - 2 per row
            btnAdd = new Button { Text = "Add Station" };
            btnClear = new Button { Text = "Clear" };
            btnCurrentStation = new Button { Text = "Configure Current Station", Dock = DockStyle.Fill };
            ButtonStyler.ApplyStandardStyle(btnAdd, "success", "medium");
            ButtonStyler.ApplyStandardStyle(btnClear, "warning", "medium");
            ButtonStyler.ApplyStandardStyle(btnCurrentStation, "info", "medium");
            btnAdd.Click += BtnAdd_Click;
            btnClear.Click += BtnClear_Click;
            btnCurrentStation.Click += BtnCurrentStation_Click;
            var btnPanel = new TableLayoutPanel
            {
                ColumnCount = 2,
                RowCount = 2,
                AutoSize = true,
                Dock = DockStyle.Fill
            };
            btnPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));
            btnPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));
            btnPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 38));
            btnPanel.RowStyles.Add(new RowStyle(SizeType.Absolute, 38));
            btnPanel.Controls.Add(btnAdd, 0, 0);
            btnPanel.Controls.Add(btnClear, 1, 0);
            btnPanel.Controls.Add(btnCurrentStation, 0, 1);
            btnPanel.SetColumnSpan(btnCurrentStation, 2);
            formLayout.Controls.Add(btnPanel, 0, 8);
        }

        private void LoadStations()
        {
            try
            {
                string search = txtSearch.Text.Trim();
                allStations = string.IsNullOrEmpty(search) ? stationService.GetAllStations() : stationService.SearchStations(search);
                totalRecords = allStations.Count;
                var paged = allStations.Skip((currentPage - 1) * pageSize).Take(pageSize).ToList();
                
                dgvStations.DataSource = paged.Select(s => new
                {
                    s.StationName,
                    s.StationCode,
                    s.AvailablePF,
                    s.IsCurrent
                }).ToList();
            }
            catch (Exception ex)
            {
                toast.ShowError($"Error loading stations: {ex.Message}");
            }
        }

        private void DgvStations_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex < 0) return;
            
            var paged = allStations.Skip((currentPage - 1) * pageSize).Take(pageSize).ToList();
            var station = paged[e.RowIndex];
            
            if (e.ColumnIndex == 4) // Edit (now image column)
            {
                LoadStationToForm(station);
            }
            else if (e.ColumnIndex == 5) // Delete (now image column)
            {
                if (MessageBox.Show($"Are you sure you want to delete station '{station.StationName}'?", 
                    "Confirm Delete", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                {
                    try
                    {
                        stationService.DeleteStation(station.StationName);
                        toast.ShowSuccess("Station deleted successfully!");
                        LoadStations();
                        ClearForm();
                    }
                    catch (Exception ex)
                    {
                        toast.ShowError($"Error deleting station: {ex.Message}");
                    }
                }
            }
            else if (e.ColumnIndex == 6) // Set as current station
            {
                if (!station.IsCurrent)
                {
                    try
                    {
                        stationService.SetCurrentStation(station.StationName);
                        toast.ShowInfo($"'{station.StationName}' is now the current station. All configurations will run according to this station.");
                        LoadStations();
                    }
                    catch (Exception ex)
                    {
                        toast.ShowError($"Error setting current station: {ex.Message}");
                    }
                }
                else
                {
                    toast.ShowInfo($"'{station.StationName}' is already the current station.");
                }
            }
        }

        private void LoadStationToForm(StationDetails station)
        {
            editingStation = station;
            isEditMode = true;
            txtStationName.Text = station.StationName;
            txtStationCode.Text = station.StationCode;
            numAvailablePF.Value = station.AvailablePF;
            // Load platform textboxes
            platformTextBoxes[0].Text = station.P1 ?? "1";
            platformTextBoxes[1].Text = station.P2 ?? "2";
            platformTextBoxes[2].Text = station.P3 ?? "3";
            platformTextBoxes[3].Text = station.P4 ?? "4";
            platformTextBoxes[4].Text = station.P5 ?? "5";
            platformTextBoxes[5].Text = station.P6 ?? "6";
            platformTextBoxes[6].Text = station.P7 ?? "7";
            platformTextBoxes[7].Text = station.P8 ?? "8";
            platformTextBoxes[8].Text = station.P9 ?? "9";
            platformTextBoxes[9].Text = station.P10 ?? "10";
            btnAdd.Text = "Update Station";
            toast.ShowInfo("Edit mode enabled. Click 'Update Station' to save changes.");
        }

        private void BtnAdd_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtStationName.Text) || string.IsNullOrWhiteSpace(txtStationCode.Text))
            {
                toast.ShowError("Station name and code are required.");
                return;
            }
            try
            {
                var station = new StationDetails
                {
                    StationName = txtStationName.Text.Trim(),
                    StationCode = txtStationCode.Text.Trim(),
                    AvailablePF = (int)numAvailablePF.Value,
                    P1 = platformTextBoxes[0].Text,
                    P2 = platformTextBoxes[1].Text,
                    P3 = platformTextBoxes[2].Text,
                    P4 = platformTextBoxes[3].Text,
                    P5 = platformTextBoxes[4].Text,
                    P6 = platformTextBoxes[5].Text,
                    P7 = platformTextBoxes[6].Text,
                    P8 = platformTextBoxes[7].Text,
                    P9 = platformTextBoxes[8].Text,
                    P10 = platformTextBoxes[9].Text,
                    IsCurrent = editingStation?.IsCurrent ?? false
                };
                if (isEditMode)
                {
                    stationService.UpdateStation(station);
                    toast.ShowSuccess("Station updated successfully!");
                }
                else
                {
                    stationService.AddStation(station);
                    toast.ShowSuccess("Station added successfully!");
                }
                LoadStations();
                ClearForm();
            }
            catch (Exception ex)
            {
                toast.ShowError($"Error saving station: {ex.Message}");
            }
        }

        private void BtnCurrentStation_Click(object sender, EventArgs e)
        {
            var currentStation = stationService.GetCurrentStation();
            if (currentStation == null)
            {
                toast.ShowWarning("No current station is selected. Please select a station and mark it as current first.");
                return;
            }
            
            var currentStationForm = new CurrentStationForm();
            currentStationForm.ShowDialog();
        }

        private void NumAvailablePF_ValueChanged(object sender, EventArgs e)
        {
            int availableCount = (int)numAvailablePF.Value;
            for (int i = 0; i < platformTextBoxes.Length; i++)
            {
                platformTextBoxes[i].Enabled = i < availableCount;
                if (!platformTextBoxes[i].Enabled)
                {
                    platformTextBoxes[i].BackColor = System.Drawing.Color.LightGray;
                    platformTextBoxes[i].ForeColor = System.Drawing.Color.DimGray;
                }
                else
                {
                    platformTextBoxes[i].BackColor = System.Drawing.SystemColors.Window;
                    platformTextBoxes[i].ForeColor = System.Drawing.SystemColors.WindowText;
                }
            }
        }

        private void BtnClear_Click(object sender, EventArgs e)
        {
            ClearForm();
            toast.ShowInfo("Form cleared");
        }

        private void ClearForm()
        {
            editingStation = null;
            isEditMode = false;
            txtStationName.Text = "";
            txtStationCode.Text = "";
            numAvailablePF.Value = DEFAULT_AVAILABLE_PLATFORMS;
            for (int i = 0; i < DEFAULT_MAXIMUM_PLATFORMS; i++)
            {
                platformTextBoxes[i].Text = (i + 1).ToString();
                platformTextBoxes[i].Enabled = i < DEFAULT_AVAILABLE_PLATFORMS;
                if (!platformTextBoxes[i].Enabled)
                {
                    platformTextBoxes[i].BackColor = System.Drawing.Color.LightGray;
                    platformTextBoxes[i].ForeColor = System.Drawing.Color.DimGray;
                }
            }
            btnAdd.Text = "Add Station";
        }

        private void DgvStations_RowPrePaint(object sender, DataGridViewRowPrePaintEventArgs e)
        {
            var grid = sender as DataGridView;
            if (grid == null || grid.Rows.Count <= e.RowIndex) return;
            var row = grid.Rows[e.RowIndex];
            var isCurrentCell = row.Cells["Current"];
            if (isCurrentCell != null && isCurrentCell.Value is bool isCurrent && isCurrent)
            {
                row.DefaultCellStyle.BackColor = System.Drawing.Color.FromArgb(200, 240, 255); // Light blue
                row.DefaultCellStyle.ForeColor = System.Drawing.Color.Black;
            }
            else
            {
                row.DefaultCellStyle.BackColor = System.Drawing.SystemColors.Window;
                row.DefaultCellStyle.ForeColor = System.Drawing.SystemColors.ControlText;
            }
        }
    }
}