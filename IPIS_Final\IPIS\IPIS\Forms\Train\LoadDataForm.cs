using System;
using System.Drawing;
using System.Windows.Forms;
using System.Threading.Tasks;
using System.Collections.Generic;
using IPIS.Services;
using IPIS.Models;
using IPIS.Utils;
using System.Net.Http;
using System.Text.Json;
using System.Linq;

namespace IPIS.Forms.Train
{
    public partial class LoadDataForm : Form
    {
        private readonly StationService stationService;
        private readonly TrainService trainService;
        private readonly TrainTypeService trainTypeService;
        private readonly LanguageService languageService;
        private readonly ToastNotification toast;
        private readonly HttpClient httpClient;

        // Form controls
        private Label lblCurrentStation;
        private Label lblStationDetails;
        private Button btnUpdateData;
        private Button btnLoadFreshData;
        private CheckBox chkClearStationData;
        private RichTextBox txtLogs;
        private ProgressBar progressBar;
        private Label lblProgress;
        private Button btnCancel;

        // API configuration
        private const string BASE_URL = "http://localhost:3000/";
        private bool isLoading = false;
        private bool cancelRequested = false;

        public LoadDataForm()
        {
            stationService = new StationService(new Repositories.SQLiteStationRepository());
            trainService = new TrainService(new Repositories.SQLiteTrainRepository());
            trainTypeService = new TrainTypeService(new Repositories.SQLiteTrainTypeRepository());
            languageService = new LanguageService(new Repositories.SQLiteLanguageRepository());
            toast = new ToastNotification(this);
            httpClient = new HttpClient();
            httpClient.Timeout = TimeSpan.FromSeconds(30); // 30 second timeout

            // Add common headers that might be required by the API
            httpClient.DefaultRequestHeaders.Add("Accept", "application/json");
            httpClient.DefaultRequestHeaders.Add("User-Agent", "IPIS-Client/1.0");

            InitializeComponent();
            LoadCurrentStationInfo();
        }

        private void InitializeComponent()
        {
            this.Text = "Load Train Data from API";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;
            this.BackColor = UIStyler.Colors.Background;

            // Main layout
            var mainLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 4,
                Padding = new Padding(16),
                CellBorderStyle = TableLayoutPanelCellBorderStyle.None
            };
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 80F)); // Station info
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 60F)); // Controls
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 60F)); // Progress
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 100F)); // Logs

            // Station information section
            CreateStationInfoSection(mainLayout);

            // Controls section
            CreateControlsSection(mainLayout);

            // Progress section
            CreateProgressSection(mainLayout);

            // Logs section
            CreateLogsSection(mainLayout);

            this.Controls.Add(mainLayout);
        }

        private void CreateStationInfoSection(TableLayoutPanel parent)
        {
            var stationPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = UIStyler.Colors.Light,
                Padding = new Padding(12)
            };

            lblCurrentStation = new Label
            {
                Text = "Current Station: Loading...",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = UIStyler.Colors.Primary,
                AutoSize = true
            };

            lblStationDetails = new Label
            {
                Text = "Station Details: Loading...",
                Font = new Font("Segoe UI", 9),
                ForeColor = UIStyler.Colors.TextPrimary,
                AutoSize = true,
                Location = new Point(0, 30)
            };

            stationPanel.Controls.Add(lblCurrentStation);
            stationPanel.Controls.Add(lblStationDetails);
            parent.Controls.Add(stationPanel);
        }

        private void CreateControlsSection(TableLayoutPanel parent)
        {
            var controlsPanel = new FlowLayoutPanel
            {
                Dock = DockStyle.Fill,
                FlowDirection = FlowDirection.LeftToRight,
                AutoSize = true,
                Padding = new Padding(12)
            };

            btnUpdateData = new Button
            {
                Text = "Update Data",
                Size = new Size(150, 35)
            };
            ButtonStyler.ApplyStandardStyle(btnUpdateData, "primary", "medium", 150);
            btnUpdateData.Click += BtnUpdateData_Click;

            btnLoadFreshData = new Button
            {
                Text = "Load Fresh Data",
                Size = new Size(150, 35)
            };
            ButtonStyler.ApplyStandardStyle(btnLoadFreshData, "warning", "medium", 150);
            btnLoadFreshData.Click += BtnLoadFreshData_Click;

            chkClearStationData = new CheckBox
            {
                Text = "Clear Station Data Also",
                AutoSize = true,
                Checked = false
            };
            UIStyler.ApplyCheckBoxStyle(chkClearStationData);
            chkClearStationData.Width = 200;

            btnCancel = new Button
            {
                Text = "Cancel",
                Size = new Size(100, 35),
                Enabled = false
            };
            ButtonStyler.ApplyStandardStyle(btnCancel, "secondary", "medium");
            btnCancel.Click += BtnCancel_Click;

            controlsPanel.Controls.Add(btnUpdateData);
            controlsPanel.Controls.Add(btnLoadFreshData);
            controlsPanel.Controls.Add(chkClearStationData);
            controlsPanel.Controls.Add(btnCancel);
            parent.Controls.Add(controlsPanel);
        }

        private void CreateLogsSection(TableLayoutPanel parent)
        {
            var logsPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 2,
                Padding = new Padding(12)
            };

            // Configure rows - label takes minimal space, RichTextBox takes the rest
            logsPanel.RowStyles.Add(new RowStyle(SizeType.AutoSize));
            logsPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));

            var logsLabel = new Label
            {
                Text = "Process Logs:",
                Font = new Font("Segoe UI", 9, FontStyle.Bold),
                AutoSize = true,
                Dock = DockStyle.Fill,
                Margin = new Padding(0, 0, 0, 8) // Add bottom margin for spacing
            };

            txtLogs = new RichTextBox
            {
                Dock = DockStyle.Fill,
                ReadOnly = true,
                BackColor = Color.White,
                Font = new Font("Consolas", 9),
                BorderStyle = BorderStyle.FixedSingle,
                MinimumSize = new Size(0, 200) // Set minimum height to reduce the box size
            };

            // Add controls to separate rows
            logsPanel.Controls.Add(logsLabel, 0, 0);
            logsPanel.Controls.Add(txtLogs, 0, 1);
            parent.Controls.Add(logsPanel);
        }

        private void CreateProgressSection(TableLayoutPanel parent)
        {
            var progressPanel = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 2,
                Padding = new Padding(12),
                AutoSize = true
            };

            // Configure rows
            progressPanel.RowStyles.Add(new RowStyle(SizeType.AutoSize));
            progressPanel.RowStyles.Add(new RowStyle(SizeType.AutoSize));

            lblProgress = new Label
            {
                Text = "Ready to load data",
                AutoSize = true,
                Dock = DockStyle.Fill
            };

            progressBar = new ProgressBar
            {
                Size = new Size(400, 20),
                Style = ProgressBarStyle.Continuous,
                Dock = DockStyle.Fill
            };

            // Add controls to separate rows
            progressPanel.Controls.Add(lblProgress, 0, 0);
            progressPanel.Controls.Add(progressBar, 0, 1);
            parent.Controls.Add(progressPanel);
        }

        private void LoadCurrentStationInfo()
        {
            try
            {
                var currentStation = stationService.GetCurrentStation();
                if (currentStation != null)
                {
                    lblCurrentStation.Text = $"Current Station: {currentStation.StationName} ({currentStation.StationCode})";
                    lblStationDetails.Text = $"Platforms: {currentStation.AvailablePF} | Auto Load: {(currentStation.AutoLoad ? "Enabled" : "Disabled")} | Auto Delete: {(currentStation.AutoDelete ? "Enabled" : "Disabled")}";
                    LogMessage($"Loaded current station: {currentStation.StationName} ({currentStation.StationCode})");
                }
                else
                {
                    lblCurrentStation.Text = "Current Station: PRYJ (Default)";
                    lblStationDetails.Text = "No current station found, using default station code PRYJ";
                    LogMessage("No current station found, will use default station code PRYJ");
                }
            }
            catch (Exception ex)
            {
                lblCurrentStation.Text = "Current Station: PRYJ (Default)";
                lblStationDetails.Text = "Error loading station info, using default station code PRYJ";
                LogMessage($"Error loading current station: {ex.Message}");
            }
        }

        private async void BtnUpdateData_Click(object sender, EventArgs e)
        {
            await LoadDataFromApiAsync(false);
        }

        private async void BtnLoadFreshData_Click(object sender, EventArgs e)
        {
            var result = MessageBox.Show(
                "This will clear all existing train data and reload from API. Are you sure you want to continue?",
                "Confirm Fresh Data Load",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Warning
            );

            if (result == DialogResult.Yes)
            {
                await LoadDataFromApiAsync(true);
            }
        }

        private async Task LoadDataFromApiAsync(bool isFreshLoad)
        {
            if (isLoading)
            {
                toast.ShowWarning("Data loading is already in progress.");
                return;
            }

            using var dataLogger = BatchLoggerExtensions.CreateTrainDataLoader();

            try
            {
                isLoading = true;
                cancelRequested = false;
                btnUpdateData.Enabled = false;
                btnLoadFreshData.Enabled = false;
                btnCancel.Enabled = true;
                progressBar.Value = 0;
                txtLogs.Clear();

                UpdateProgress("Initializing data load...");
                LogMessage($"Starting {(isFreshLoad ? "fresh" : "update")} data load from API...");
                LogMessage($"API Base URL: {BASE_URL}");
                dataLogger.LogStep($"Starting {(isFreshLoad ? "fresh" : "update")} data load from API");

                // Suppress verbose logging during bulk operations
                Logger.SetVerboseLogging(false);

                // Test API connection first
                UpdateProgress("Testing API connection...");
                LogMessage("Testing API connection...");
                if (!await TestApiConnectionAsync())
                {
                    UpdateProgress("API connection failed");
                    LogMessage("API connection test failed. Please check if the API server is running.");
                    dataLogger.LogFailure("API connection test", "API server is not responding");
                    return;
                }

                // Clear existing data if fresh load
                if (isFreshLoad)
                {
                    UpdateProgress("Clearing existing data...");
                    LogMessage("Clearing existing train data...");
                    await ClearExistingDataAsync();
                }

                // Get current station code
                string stationCode = GetCurrentStationCode();
                LogMessage($"Using station code: {stationCode}");

                // Step 1: Get running trains for the station
                UpdateProgress("Fetching running trains...");
                LogMessage("Step 1: Fetching running trains from API...");
                var runningTrains = await GetRunningTrainsAsync(stationCode);

                if (runningTrains == null || runningTrains.Count == 0)
                {
                    UpdateProgress("No trains found");
                    LogMessage("No running trains found for the station.");
                    return;
                }

                LogMessage($"Found {runningTrains.Count} running trains.");
                progressBar.Maximum = runningTrains.Count * 3; // 3 steps per train
                int progress = 0;

                // Step 2: Process each train
                foreach (var train in runningTrains)
                {
                    if (cancelRequested)
                    {
                        UpdateProgress("Data loading cancelled");
                        LogMessage("Data loading cancelled by user.");
                        dataLogger.LogStep("Data loading cancelled by user");
                        break;
                    }

                    UpdateProgress($"Processing train {train.TrainNo}", progress, progressBar.Maximum);
                    LogMessage($"Processing train: {train.TrainNo} - {train.TrainName}");

                    try
                    {
                        // Get train details
                        var trainDetails = await GetTrainDetailsAsync(train.TrainNo);
                        if (trainDetails != null)
                        {
                            LogMessage($"  - Train details retrieved successfully");
                            progress++;
                            progressBar.Value = progress;

                            // Get train route
                            var trainRoute = await GetTrainRouteAsync(train.TrainNo);
                            if (trainRoute != null)
                            {
                                LogMessage($"  - Train route retrieved successfully ({trainRoute.Count} stations)");
                                progress++;
                                progressBar.Value = progress;

                                // Save train data
                                await SaveTrainDataAsync(trainDetails, trainRoute);
                                LogMessage($"  - Train data saved successfully");

                                // Log individual operation details
                                var operationDetail = $"Train data saved: {train.TrainNo} - {train.TrainName} (Route: {trainRoute.Count} stations)";
                                dataLogger.IncrementSuccess(operationDetail);
                                progress++;
                                progressBar.Value = progress;
                            }
                            else
                            {
                                LogMessage($"  - Failed to retrieve train route");
                                var operationDetail = $"Train route retrieval failed: {train.TrainNo} - {train.TrainName}";
                                dataLogger.IncrementFailure(operationDetail, "Failed to retrieve route data");
                                progress += 2;
                                progressBar.Value = progress;
                            }
                        }
                        else
                        {
                            LogMessage($"  - Failed to retrieve train details");
                            var operationDetail = $"Train details retrieval failed: {train.TrainNo} - {train.TrainName}";
                            dataLogger.IncrementFailure(operationDetail, "Failed to retrieve train details");
                            progress += 3;
                            progressBar.Value = progress;
                        }
                    }
                    catch (Exception ex)
                    {
                        LogMessage($"  - Error processing train {train.TrainNo}: {ex.Message}");
                        var operationDetail = $"Train processing error: {train.TrainNo} - {train.TrainName}";
                        dataLogger.IncrementFailure(operationDetail, ex.Message);
                        progress += 3;
                        progressBar.Value = progress;
                    }
                }

                if (!cancelRequested)
                {
                    UpdateProgress($"{(isFreshLoad ? "Fresh" : "Update")} data loading completed successfully!");
                    LogMessage($"{(isFreshLoad ? "Fresh" : "Update")} data loading completed successfully!");
                    toast.ShowSuccess($"Train data {(isFreshLoad ? "loaded" : "updated")} successfully from API!");
                }
            }
            catch (Exception ex)
            {
                UpdateProgress("Error occurred during data loading");
                LogMessage($"Error during data loading: {ex.Message}");
                toast.ShowError($"Error loading data: {ex.Message}");
            }
            finally
            {
                // Re-enable verbose logging
                Logger.SetVerboseLogging(true);

                isLoading = false;
                btnUpdateData.Enabled = true;
                btnLoadFreshData.Enabled = true;
                btnCancel.Enabled = false;
                UpdateProgress("Ready to load data");
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            cancelRequested = true;
            LogMessage("Cancellation requested...");
        }

        private string GetCurrentStationCode()
        {
            try
            {
                var currentStation = stationService.GetCurrentStation();
                return currentStation?.StationCode ?? "PRYJ";
            }
            catch
            {
                return "PRYJ";
            }
        }

        private async Task<bool> TestApiConnectionAsync()
        {
            try
            {
                var testUrl = $"{BASE_URL}trains/stationLive?code=PRYJ";
                LogMessage($"Testing connection to: {testUrl}");

                var response = await httpClient.GetAsync(testUrl);
                LogMessage($"API response status: {response.StatusCode}");

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    LogMessage($"API test response: {content}");
                    return true;
                }
                else
                {
                    LogMessage($"API test failed with status: {response.StatusCode}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                LogMessage($"API connection test error: {ex.Message}");
                return false;
            }
        }

        private async Task ClearExistingDataAsync()
        {
            try
            {
                LogMessage("Clearing Train_Data table...");
                await Task.Run(() => trainService.ClearAllTrains());
                LogMessage("Train_Data table cleared successfully.");

                if (chkClearStationData.Checked)
                {
                    LogMessage("Clearing Station_Details table...");
                    await Task.Run(() => stationService.ClearAllStations());
                    LogMessage("Station_Details table cleared successfully.");
                }
                else
                {
                    LogMessage("Station data will be preserved (checkbox not checked).");
                }
            }
            catch (Exception ex)
            {
                LogMessage($"Error clearing existing data: {ex.Message}");
                throw;
            }
        }

        private async Task<List<RunningTrain>> GetRunningTrainsAsync(string stationCode)
        {
            try
            {
                var url = $"{BASE_URL}trains/stationLive?code={stationCode}";
                LogMessage($"Fetching from: {url}");

                var response = await httpClient.GetStringAsync(url);
                LogMessage($"Raw API Response: {response}");

                var apiResponse = JsonSerializer.Deserialize<ApiResponse<List<RunningTrain>>>(response);

                if (apiResponse?.Success == true && apiResponse.Data != null)
                {
                    LogMessage($"Successfully parsed {apiResponse.Data.Count} trains");
                    return apiResponse.Data;
                }
                else
                {
                    LogMessage($"API returned success: {apiResponse?.Success}, Data count: {apiResponse?.Data?.Count ?? 0}");

                    // Try to parse as direct array if the API response structure is different
                    try
                    {
                        var directResponse = JsonSerializer.Deserialize<List<RunningTrain>>(response);
                        if (directResponse != null && directResponse.Count > 0)
                        {
                            LogMessage($"Parsed as direct array: {directResponse.Count} trains");
                            return directResponse;
                        }
                    }
                    catch (Exception directEx)
                    {
                        LogMessage($"Failed to parse as direct array: {directEx.Message}");
                    }

                    return new List<RunningTrain>();
                }
            }
            catch (Exception ex)
            {
                LogMessage($"Error fetching running trains: {ex.Message}");
                return new List<RunningTrain>();
            }
        }

        private async Task<TrainDetails> GetTrainDetailsAsync(string trainNo)
        {
            try
            {
                var url = $"{BASE_URL}trains/getTrain?trainNo={trainNo}";
                var response = await httpClient.GetStringAsync(url);
                LogMessage($"Train details response for {trainNo}: {response}");

                var apiResponse = JsonSerializer.Deserialize<ApiResponse<TrainDetails>>(response);

                if (apiResponse?.Success == true && apiResponse.Data != null)
                {
                    return apiResponse.Data;
                }
                else
                {
                    LogMessage($"API returned error for train {trainNo}: {apiResponse?.Success}");

                    // Try to parse as direct object if the API response structure is different
                    try
                    {
                        var directResponse = JsonSerializer.Deserialize<TrainDetails>(response);
                        if (directResponse != null)
                        {
                            LogMessage($"Parsed train details as direct object for {trainNo}");
                            return directResponse;
                        }
                    }
                    catch (Exception directEx)
                    {
                        LogMessage($"Failed to parse train details as direct object: {directEx.Message}");
                    }

                    return null;
                }
            }
            catch (Exception ex)
            {
                LogMessage($"Error fetching train details for {trainNo}: {ex.Message}");
                return null;
            }
        }

        private async Task<List<RouteStation>> GetTrainRouteAsync(string trainNo)
        {
            try
            {
                var url = $"{BASE_URL}trains/getRoute?trainNo={trainNo}";
                var response = await httpClient.GetStringAsync(url);
                LogMessage($"Train route response for {trainNo}: {response}");

                var apiResponse = JsonSerializer.Deserialize<ApiResponse<List<RouteStation>>>(response);

                if (apiResponse?.Success == true && apiResponse.Data != null)
                {
                    return apiResponse.Data;
                }
                else
                {
                    LogMessage($"API returned error for train route {trainNo}: {apiResponse?.Success}");

                    // Try to parse as direct array if the API response structure is different
                    try
                    {
                        var directResponse = JsonSerializer.Deserialize<List<RouteStation>>(response);
                        if (directResponse != null && directResponse.Count > 0)
                        {
                            LogMessage($"Parsed train route as direct array for {trainNo}: {directResponse.Count} stations");
                            return directResponse;
                        }
                    }
                    catch (Exception directEx)
                    {
                        LogMessage($"Failed to parse train route as direct array: {directEx.Message}");
                    }

                    return null;
                }
            }
            catch (Exception ex)
            {
                LogMessage($"Error fetching train route for {trainNo}: {ex.Message}");
                return null;
            }
        }

        private async Task SaveTrainDataAsync(TrainDetails trainDetails, List<RouteStation> routeStations)
        {
            try
            {
                // Step 1: Ensure train type exists
                await EnsureTrainTypeExistsAsync(trainDetails.Type);

                // Step 2: Ensure source and destination stations exist
                await EnsureStationExistsAsync(trainDetails.FromStnCode, trainDetails.FromStnName);
                await EnsureStationExistsAsync(trainDetails.ToStnCode, trainDetails.ToStnName);

                // Step 3: Add route stations
                foreach (var routeStation in routeStations)
                {
                    await EnsureStationExistsAsync(routeStation.SourceStnCode, routeStation.SourceStnName);
                }

                // Step 4: Get current station code
                string currentStationCode = GetCurrentStationCode();

                // Step 5: Save train data with current station times
                await SaveTrainToDatabaseAsync(trainDetails, routeStations, currentStationCode);

                // Step 6: Save wave files for configured languages
                await SaveWaveFilesAsync(trainDetails, routeStations);

            }
            catch (Exception ex)
            {
                LogMessage($"Error saving train data: {ex.Message}");
                throw;
            }
        }

        private async Task EnsureTrainTypeExistsAsync(string trainType)
        {
            if (string.IsNullOrEmpty(trainType))
                return;

            try
            {
                var existingType = trainTypeService.GetTrainTypeByName(trainType);
                if (existingType == null)
                {
                    var newTrainType = new TrainType
                    {
                        ID = Guid.NewGuid().ToString(),
                        Name = trainType,
                        Description = $"Auto-generated from API for {trainType}",
                        IsActive = true
                    };

                    trainTypeService.AddTrainType(newTrainType);
                    LogMessage($"  - Added new train type: {trainType}");
                }
            }
            catch (Exception ex)
            {
                LogMessage($"  - Error ensuring train type {trainType}: {ex.Message}");
            }
        }

        private async Task EnsureStationExistsAsync(string stationCode, string stationName)
        {
            if (string.IsNullOrEmpty(stationCode) || string.IsNullOrEmpty(stationName))
                return;

            try
            {
                var existingStation = stationService.GetStationDetails(stationName);
                if (existingStation == null)
                {
                    var newStation = new StationDetails
                    {
                        StationName = stationName,
                        StationCode = stationCode,
                        AvailablePF = 3, // Default value
                        AutoLoad = false,
                        AutoDelete = false,
                        AutoLoadInterval = 5,
                        AutoDeleteInterval = 10,
                        AutoDeletePostInterval = 5,
                        IsCurrent = false
                    };

                    stationService.AddStation(newStation);
                    LogMessage($"  - Added new station: {stationName} ({stationCode})");
                }
            }
            catch (Exception ex)
            {
                LogMessage($"  - Error ensuring station {stationName}: {ex.Message}");
            }
        }

        private async Task SaveTrainToDatabaseAsync(TrainDetails trainDetails, List<RouteStation> routeStations, string currentStationCode)
        {
            try
            {
                // Convert running days string to boolean array
                bool[] operatingDays = ConvertRunningDaysToArray(trainDetails.RunningDays);

                // Find current station in the route to get arrival and departure times
                var currentStationInRoute = routeStations.FirstOrDefault(rs =>
                    rs.SourceStnCode.Equals(currentStationCode, StringComparison.OrdinalIgnoreCase));

                string arrivalTime = "00:00";
                string departureTime = "00:00";
                string trainAD = "A"; // Default to Arrival

                if (currentStationInRoute != null)
                {
                    // Use times from the route for current station
                    arrivalTime = currentStationInRoute.Arrive == "First" ? "00:00" : currentStationInRoute.Arrive;
                    departureTime = currentStationInRoute.Depart == "Last" ? "23:59" : currentStationInRoute.Depart;

                    // Determine A/D value based on station position in route
                    if (currentStationInRoute.Arrive == "First")
                    {
                        // This is the originating station - train departs from here
                        trainAD = "D";
                        LogMessage($"  - Current station is originating station (First) - A/D set to 'D' (Departure)");
                    }
                    else if (currentStationInRoute.Depart == "Last")
                    {
                        // This is the terminating station - train arrives here
                        trainAD = "A";
                        LogMessage($"  - Current station is terminating station (Last) - A/D set to 'A' (Arrival)");
                    }
                    else
                    {
                        // This is an intermediate station - determine if it's primarily arrival or departure
                        // For intermediate stations, we'll use "A" (Arrival) as the default
                        // since the train arrives first, then departs
                        trainAD = "A";
                        LogMessage($"  - Current station is intermediate station - A/D set to 'A' (Arrival)");
                    }

                    LogMessage($"  - Found current station in route: {currentStationInRoute.SourceStnName} ({currentStationInRoute.SourceStnCode})");
                    LogMessage($"  - Arrival time: {arrivalTime}, Departure time: {departureTime}, A/D: {trainAD}");
                }
                else
                {
                    // Fallback to original times if current station not found in route
                    arrivalTime = trainDetails.FromTime;
                    departureTime = trainDetails.ToTime;
                    LogMessage($"  - Current station {currentStationCode} not found in route, using original times");
                }

                // Check if train already exists
                var existingTrains = trainService.GetAllTrains();
                var existingTrain = existingTrains.Select($"Train_No = '{trainDetails.TrainNo}'");

                if (existingTrain.Length > 0)
                {
                    // Update existing train
                    trainService.UpdateTrain(
                        trainDetails.TrainNo,
                        trainDetails.TrainName,
                        trainDetails.Type,
                        trainAD, // Use calculated A/D value
                        arrivalTime,
                        departureTime,
                        "1", // Default platform
                        trainDetails.FromStnCode,
                        trainDetails.ToStnCode,
                        new string[0], // No via stations for now
                        operatingDays
                    );
                    LogMessage($"  - Updated existing train: {trainDetails.TrainNo}");
                }
                else
                {
                    // Add new train
                    trainService.AddTrain(
                        trainDetails.TrainNo,
                        trainDetails.TrainName,
                        trainDetails.Type,
                        trainAD, // Use calculated A/D value
                        arrivalTime,
                        departureTime,
                        "1", // Default platform
                        trainDetails.FromStnCode,
                        trainDetails.ToStnCode,
                        new string[0], // No via stations for now
                        operatingDays
                    );
                    LogMessage($"  - Added new train: {trainDetails.TrainNo}");
                }
            }
            catch (Exception ex)
            {
                LogMessage($"  - Error saving train to database: {ex.Message}");
                throw;
            }
        }

        private async Task SaveWaveFilesAsync(TrainDetails trainDetails, List<RouteStation> routeStations)
        {
            try
            {
                // Get configured languages
                var languages = await languageService.GetActiveLanguagesAsync();

                foreach (var language in languages)
                {
                    // Save wave files for each station in the route
                    foreach (var routeStation in routeStations)
                    {
                        string waveFilePath = $"WAVE\\{language.Code}\\CITY\\{routeStation.SourceStnCode}.wav";
                        LogMessage($"  - Wave file path: {waveFilePath}");

                        // Note: In a real implementation, you would download or copy the wave file here
                        // For now, we just log the path
                    }
                }
            }
            catch (Exception ex)
            {
                LogMessage($"  - Error saving wave files: {ex.Message}");
            }
        }

        private bool[] ConvertRunningDaysToArray(string runningDays)
        {
            // runningDays format: "1111111" (7 digits, 1 for each day of week)
            var result = new bool[8]; // [All_Days, Sun, Mon, Tue, Wed, Thu, Fri, Sat]

            if (string.IsNullOrEmpty(runningDays) || runningDays.Length != 7)
            {
                result[0] = true; // All days
                return result;
            }

            // Check if all days are 1
            bool allDays = runningDays.All(c => c == '1');
            result[0] = allDays; // All_Days

            if (!allDays)
            {
                // Individual days (Sunday to Saturday)
                result[1] = runningDays[0] == '1'; // Sun
                result[2] = runningDays[1] == '1'; // Mon
                result[3] = runningDays[2] == '1'; // Tue
                result[4] = runningDays[3] == '1'; // Wed
                result[5] = runningDays[4] == '1'; // Thu
                result[6] = runningDays[5] == '1'; // Fri
                result[7] = runningDays[6] == '1'; // Sat
            }

            return result;
        }

        private void LogMessage(string message)
        {
            if (txtLogs.InvokeRequired)
            {
                txtLogs.Invoke(new Action<string>(LogMessage), message);
                return;
            }

            string timestamp = DateTime.Now.ToString("HH:mm:ss");
            txtLogs.AppendText($"[{timestamp}] {message}\n");
            txtLogs.ScrollToCaret();

            Application.DoEvents();
        }

        private void UpdateProgress(string status, int currentProgress = -1, int totalProgress = -1)
        {
            if (lblProgress.InvokeRequired)
            {
                lblProgress.Invoke(new Action<string, int, int>(UpdateProgress), status, currentProgress, totalProgress);
                return;
            }

            if (currentProgress >= 0 && totalProgress > 0)
            {
                int percentage = (int)((double)currentProgress / totalProgress * 100);
                lblProgress.Text = $"In Progress: {percentage}% - {status}";
            }
            else
            {
                lblProgress.Text = status;
            }

            Application.DoEvents();
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            if (isLoading)
            {
                var result = MessageBox.Show("Data loading is in progress. Are you sure you want to close?",
                    "Confirm Close", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.No)
                {
                    e.Cancel = true;
                    return;
                }

                cancelRequested = true;
            }

            httpClient?.Dispose();
            base.OnFormClosing(e);
        }
    }

    // API Response Models
    public class ApiResponse<T>
    {
        [System.Text.Json.Serialization.JsonPropertyName("success")]
        public bool Success { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("time_stamp")]
        public long TimeStamp { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("data")]
        public T Data { get; set; }
    }

    public class RunningTrain
    {
        [System.Text.Json.Serialization.JsonPropertyName("train_no")]
        public string TrainNo { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("train_name")]
        public string TrainName { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("source_stn_name")]
        public string SourceStnName { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("dstn_stn_name")]
        public string DstnStnName { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("time_at")]
        public string TimeAt { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("detail")]
        public string Detail { get; set; }
    }

    public class TrainDetails
    {
        [System.Text.Json.Serialization.JsonPropertyName("train_no")]
        public string TrainNo { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("train_name")]
        public string TrainName { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("from_stn_code")]
        public string FromStnCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("to_stn_code")]
        public string ToStnCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("running_days")]
        public string RunningDays { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("type")]
        public string Type { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("from_stn_name")]
        public string FromStnName { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("to_stn_name")]
        public string ToStnName { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("from_time")]
        public string FromTime { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("to_time")]
        public string ToTime { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("travel_time")]
        public string TravelTime { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("train_id")]
        public string TrainId { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("distance_from_to")]
        public string DistanceFromTo { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("average_speed")]
        public string AverageSpeed { get; set; }
    }

    public class RouteStation
    {
        [System.Text.Json.Serialization.JsonPropertyName("source_stn_name")]
        public string SourceStnName { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("source_stn_code")]
        public string SourceStnCode { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("arrive")]
        public string Arrive { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("depart")]
        public string Depart { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("distance")]
        public string Distance { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("day")]
        public string Day { get; set; }

        [System.Text.Json.Serialization.JsonPropertyName("zone")]
        public string Zone { get; set; }
    }
}