using System;
using System.Drawing;

namespace IPIS.Models
{
    public class LoginConfiguration
    {
        public int Id { get; set; }
        public string StationName { get; set; } = "IPIS";
        public string WelcomeMessage { get; set; } = "Welcome to";
        public string SubtitleMessage { get; set; } = "Integrated Passenger\nInformation System";
        public string LogoPath { get; set; } = "";
        public string BackgroundImagePath { get; set; } = "";
        public string PrimaryColor { get; set; } = "#007BFF"; // Blue
        public string SecondaryColor { get; set; } = "#C8DCFF"; // Light blue
        public string BackgroundColor { get; set; } = "#F0F4F8"; // Light gray
        public string StationTextColor { get; set; } = "#333333"; // Dark gray
        public bool UseCustomLogo { get; set; } = false;
        public bool UseBackgroundImage { get; set; } = false;
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        // Helper methods to convert color strings to Color objects
        public Color GetPrimaryColor()
        {
            try
            {
                return ColorTranslator.FromHtml(PrimaryColor);
            }
            catch
            {
                return Color.FromArgb(0, 123, 255); // Default blue
            }
        }

        public Color GetSecondaryColor()
        {
            try
            {
                return ColorTranslator.FromHtml(SecondaryColor);
            }
            catch
            {
                return Color.FromArgb(200, 220, 255); // Default light blue
            }
        }

        public Color GetBackgroundColor()
        {
            try
            {
                return ColorTranslator.FromHtml(BackgroundColor);
            }
            catch
            {
                return Color.FromArgb(240, 244, 248); // Default light gray
            }
        }

        public Color GetStationTextColor()
        {
            try
            {
                return ColorTranslator.FromHtml(StationTextColor);
            }
            catch
            {
                return Color.FromArgb(51, 51, 51); // Default dark gray
            }
        }

        // Helper method to set colors from Color objects
        public void SetPrimaryColor(Color color)
        {
            PrimaryColor = ColorTranslator.ToHtml(color);
        }

        public void SetSecondaryColor(Color color)
        {
            SecondaryColor = ColorTranslator.ToHtml(color);
        }

        public void SetBackgroundColor(Color color)
        {
            BackgroundColor = ColorTranslator.ToHtml(color);
        }

        public void SetStationTextColor(Color color)
        {
            StationTextColor = ColorTranslator.ToHtml(color);
        }
    }
}
