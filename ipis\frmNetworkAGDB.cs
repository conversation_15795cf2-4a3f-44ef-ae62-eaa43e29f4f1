// Decompiled with JetBrains decompiler
// Type: ipis.frmNetworkAGDB
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

namespace ipis
{

[DesignerGenerated]
public class frmNetworkAGDB : Form
{
  private static List<WeakReference> __ENCList = new List<WeakReference>();
  private IContainer components;
  [AccessedThroughProperty("txtAgdbMsgSwDly")]
  private TextBox _txtAgdbMsgSwDly;
  [AccessedThroughProperty("lblMsgSwDly")]
  private Label _lblMsgSwDly;
  [AccessedThroughProperty("lblSharedPfno")]
  private Label _lblSharedPfno;
  [AccessedThroughProperty("lblPfno")]
  private Label _lblPfno;
  [AccessedThroughProperty("btnExit")]
  private Button _btnExit;
  [AccessedThroughProperty("btnOk")]
  private Button _btnOk;
  [AccessedThroughProperty("txtAgdbAddress")]
  private TextBox _txtAgdbAddress;
  [AccessedThroughProperty("txtAgdbName")]
  private TextBox _txtAgdbName;
  [AccessedThroughProperty("lblAddress")]
  private Label _lblAddress;
  [AccessedThroughProperty("lblName")]
  private Label _lblName;
  [AccessedThroughProperty("cmbAgdbPfno")]
  private ComboBox _cmbAgdbPfno;
  [AccessedThroughProperty("cmbAgdbSharedPfno")]
  private ComboBox _cmbAgdbSharedPfno;
  [AccessedThroughProperty("chkAGDBSharedPfNo")]
  private CheckBox _chkAGDBSharedPfNo;
  [AccessedThroughProperty("lblSharedPlatform")]
  private Label _lblSharedPlatform;
  [AccessedThroughProperty("lblSign")]
  private Label _lblSign;
  [AccessedThroughProperty("numSign")]
  private NumericUpDown _numSign;
  [AccessedThroughProperty("lblAllPfno")]
  private Label _lblAllPfno;
  [AccessedThroughProperty("chkAgdbAllPfno")]
  private CheckBox _chkAgdbAllPfno;
  public static byte agdb_msg_sw_dly;
  public static bool agdb_all_platforms_checked;

  [DebuggerNonUserCode]
  static frmNetworkAGDB()
  {
  }

  [DebuggerNonUserCode]
  public frmNetworkAGDB()
  {
    this.Load += new EventHandler(this.frmNetworkAGDB_Load);
    frmNetworkAGDB.__ENCAddToList((object) this);
    this.InitializeComponent();
  }

  [DebuggerNonUserCode]
  private static void __ENCAddToList(object value)
  {
    lock (frmNetworkAGDB.__ENCList)
    {
      if (frmNetworkAGDB.__ENCList.Count == frmNetworkAGDB.__ENCList.Capacity)
      {
        int index1 = 0;
        int num = checked (frmNetworkAGDB.__ENCList.Count - 1);
        int index2 = 0;
        while (index2 <= num)
        {
          if (frmNetworkAGDB.__ENCList[index2].IsAlive)
          {
            if (index2 != index1)
              frmNetworkAGDB.__ENCList[index1] = frmNetworkAGDB.__ENCList[index2];
            checked { ++index1; }
          }
          checked { ++index2; }
        }
        frmNetworkAGDB.__ENCList.RemoveRange(index1, checked (frmNetworkAGDB.__ENCList.Count - index1));
        frmNetworkAGDB.__ENCList.Capacity = frmNetworkAGDB.__ENCList.Count;
      }
      frmNetworkAGDB.__ENCList.Add(new WeakReference(RuntimeHelpers.GetObjectValue(value)));
    }
  }

  [DebuggerNonUserCode]
  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  [DebuggerStepThrough]
  private void InitializeComponent()
  {
    this.txtAgdbMsgSwDly = new TextBox();
    this.lblMsgSwDly = new Label();
    this.lblSharedPfno = new Label();
    this.lblPfno = new Label();
    this.btnExit = new Button();
    this.btnOk = new Button();
    this.txtAgdbAddress = new TextBox();
    this.txtAgdbName = new TextBox();
    this.lblAddress = new Label();
    this.lblName = new Label();
    this.cmbAgdbPfno = new ComboBox();
    this.cmbAgdbSharedPfno = new ComboBox();
    this.chkAGDBSharedPfNo = new CheckBox();
    this.lblSharedPlatform = new Label();
    this.lblSign = new Label();
    this.numSign = new NumericUpDown();
    this.lblAllPfno = new Label();
    this.chkAgdbAllPfno = new CheckBox();
    this.numSign.BeginInit();
    this.SuspendLayout();
    this.txtAgdbMsgSwDly.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtAgdbMsgSwDly1 = this.txtAgdbMsgSwDly;
    Point point1 = new Point(258, 299);
    Point point2 = point1;
    txtAgdbMsgSwDly1.Location = point2;
    this.txtAgdbMsgSwDly.MaxLength = 2;
    this.txtAgdbMsgSwDly.Name = "txtAgdbMsgSwDly";
    TextBox txtAgdbMsgSwDly2 = this.txtAgdbMsgSwDly;
    Size size1 = new Size(54, 22);
    Size size2 = size1;
    txtAgdbMsgSwDly2.Size = size2;
    this.txtAgdbMsgSwDly.TabIndex = 7;
    this.txtAgdbMsgSwDly.TextAlign = HorizontalAlignment.Center;
    this.lblMsgSwDly.AutoSize = true;
    this.lblMsgSwDly.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblMsgSwDly1 = this.lblMsgSwDly;
    point1 = new Point(33, 299);
    Point point3 = point1;
    lblMsgSwDly1.Location = point3;
    this.lblMsgSwDly.Name = "lblMsgSwDly";
    Label lblMsgSwDly2 = this.lblMsgSwDly;
    size1 = new Size(186, 16 /*0x10*/);
    Size size3 = size1;
    lblMsgSwDly2.Size = size3;
    this.lblMsgSwDly.TabIndex = 39;
    this.lblMsgSwDly.Text = "Message Switching Delay";
    this.lblSharedPfno.AutoSize = true;
    this.lblSharedPfno.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblSharedPfno1 = this.lblSharedPfno;
    point1 = new Point(76, 242);
    Point point4 = point1;
    lblSharedPfno1.Location = point4;
    this.lblSharedPfno.Name = "lblSharedPfno";
    Label lblSharedPfno2 = this.lblSharedPfno;
    size1 = new Size(143, 32 /*0x20*/);
    Size size4 = size1;
    lblSharedPfno2.Size = size4;
    this.lblSharedPfno.TabIndex = 38;
    this.lblSharedPfno.Text = "Shared Platform No\r\n\r\n";
    this.lblSharedPfno.Visible = false;
    this.lblPfno.AutoSize = true;
    this.lblPfno.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblPfno1 = this.lblPfno;
    point1 = new Point(130, 149);
    Point point5 = point1;
    lblPfno1.Location = point5;
    this.lblPfno.Name = "lblPfno";
    Label lblPfno2 = this.lblPfno;
    size1 = new Size(89, 16 /*0x10*/);
    Size size5 = size1;
    lblPfno2.Size = size5;
    this.lblPfno.TabIndex = 36;
    this.lblPfno.Text = "Platform No";
    this.btnExit.BackColor = Color.SeaShell;
    this.btnExit.DialogResult = DialogResult.Cancel;
    this.btnExit.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnExit1 = this.btnExit;
    point1 = new Point(252, 419);
    Point point6 = point1;
    btnExit1.Location = point6;
    this.btnExit.Name = "btnExit";
    Button btnExit2 = this.btnExit;
    size1 = new Size(60, 25);
    Size size6 = size1;
    btnExit2.Size = size6;
    this.btnExit.TabIndex = 10;
    this.btnExit.Text = "Exit";
    this.btnExit.UseVisualStyleBackColor = false;
    this.btnOk.BackColor = Color.SeaShell;
    this.btnOk.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnOk1 = this.btnOk;
    point1 = new Point(143, 419);
    Point point7 = point1;
    btnOk1.Location = point7;
    this.btnOk.Name = "btnOk";
    Button btnOk2 = this.btnOk;
    size1 = new Size(60, 25);
    Size size7 = size1;
    btnOk2.Size = size7;
    this.btnOk.TabIndex = 9;
    this.btnOk.Text = "Ok";
    this.btnOk.UseVisualStyleBackColor = false;
    this.txtAgdbAddress.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtAgdbAddress1 = this.txtAgdbAddress;
    point1 = new Point(258, 60);
    Point point8 = point1;
    txtAgdbAddress1.Location = point8;
    this.txtAgdbAddress.MaxLength = 3;
    this.txtAgdbAddress.Name = "txtAgdbAddress";
    TextBox txtAgdbAddress2 = this.txtAgdbAddress;
    size1 = new Size(45, 22);
    Size size8 = size1;
    txtAgdbAddress2.Size = size8;
    this.txtAgdbAddress.TabIndex = 2;
    this.txtAgdbAddress.TextAlign = HorizontalAlignment.Center;
    this.txtAgdbName.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtAgdbName1 = this.txtAgdbName;
    point1 = new Point(258, 12);
    Point point9 = point1;
    txtAgdbName1.Location = point9;
    this.txtAgdbName.MaxLength = 15;
    this.txtAgdbName.Name = "txtAgdbName";
    TextBox txtAgdbName2 = this.txtAgdbName;
    size1 = new Size(100, 22);
    Size size9 = size1;
    txtAgdbName2.Size = size9;
    this.txtAgdbName.TabIndex = 1;
    this.txtAgdbName.TextAlign = HorizontalAlignment.Center;
    this.lblAddress.AutoSize = true;
    this.lblAddress.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblAddress1 = this.lblAddress;
    point1 = new Point(153, 63 /*0x3F*/);
    Point point10 = point1;
    lblAddress1.Location = point10;
    this.lblAddress.Name = "lblAddress";
    Label lblAddress2 = this.lblAddress;
    size1 = new Size(66, 16 /*0x10*/);
    Size size10 = size1;
    lblAddress2.Size = size10;
    this.lblAddress.TabIndex = 35;
    this.lblAddress.Text = "Address";
    this.lblName.AutoSize = true;
    this.lblName.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblName1 = this.lblName;
    point1 = new Point(170, 18);
    Point point11 = point1;
    lblName1.Location = point11;
    this.lblName.Name = "lblName";
    Label lblName2 = this.lblName;
    size1 = new Size(49, 16 /*0x10*/);
    Size size11 = size1;
    lblName2.Size = size11;
    this.lblName.TabIndex = 34;
    this.lblName.Text = "Name";
    this.cmbAgdbPfno.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.cmbAgdbPfno.FormattingEnabled = true;
    ComboBox cmbAgdbPfno1 = this.cmbAgdbPfno;
    point1 = new Point(258, 149);
    Point point12 = point1;
    cmbAgdbPfno1.Location = point12;
    this.cmbAgdbPfno.Name = "cmbAgdbPfno";
    ComboBox cmbAgdbPfno2 = this.cmbAgdbPfno;
    size1 = new Size(70, 24);
    Size size12 = size1;
    cmbAgdbPfno2.Size = size12;
    this.cmbAgdbPfno.TabIndex = 4;
    this.cmbAgdbSharedPfno.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.cmbAgdbSharedPfno.FormattingEnabled = true;
    ComboBox cmbAgdbSharedPfno1 = this.cmbAgdbSharedPfno;
    point1 = new Point(258, 242);
    Point point13 = point1;
    cmbAgdbSharedPfno1.Location = point13;
    this.cmbAgdbSharedPfno.Name = "cmbAgdbSharedPfno";
    ComboBox cmbAgdbSharedPfno2 = this.cmbAgdbSharedPfno;
    size1 = new Size(54, 24);
    Size size13 = size1;
    cmbAgdbSharedPfno2.Size = size13;
    this.cmbAgdbSharedPfno.TabIndex = 6;
    this.cmbAgdbSharedPfno.Visible = false;
    this.chkAGDBSharedPfNo.AutoSize = true;
    this.chkAGDBSharedPfNo.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    CheckBox chkAgdbSharedPfNo1 = this.chkAGDBSharedPfNo;
    point1 = new Point(258, 199);
    Point point14 = point1;
    chkAgdbSharedPfNo1.Location = point14;
    this.chkAGDBSharedPfNo.Name = "chkAGDBSharedPfNo";
    CheckBox chkAgdbSharedPfNo2 = this.chkAGDBSharedPfNo;
    size1 = new Size(15, 14);
    Size size14 = size1;
    chkAgdbSharedPfNo2.Size = size14;
    this.chkAGDBSharedPfNo.TabIndex = 5;
    this.chkAGDBSharedPfNo.UseVisualStyleBackColor = true;
    this.lblSharedPlatform.AutoSize = true;
    this.lblSharedPlatform.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblSharedPlatform1 = this.lblSharedPlatform;
    point1 = new Point(100, 199);
    Point point15 = point1;
    lblSharedPlatform1.Location = point15;
    this.lblSharedPlatform.Name = "lblSharedPlatform";
    Label lblSharedPlatform2 = this.lblSharedPlatform;
    size1 = new Size(119, 16 /*0x10*/);
    Size size15 = size1;
    lblSharedPlatform2.Size = size15;
    this.lblSharedPlatform.TabIndex = 172;
    this.lblSharedPlatform.Text = "Shared Platform\r\n";
    this.lblSign.AutoSize = true;
    this.lblSign.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblSign1 = this.lblSign;
    point1 = new Point(11, 346);
    Point point16 = point1;
    lblSign1.Location = point16;
    this.lblSign.Name = "lblSign";
    Label lblSign2 = this.lblSign;
    size1 = new Size(208 /*0xD0*/, 16 /*0x10*/);
    Size size16 = size1;
    lblSign2.Size = size16;
    this.lblSign.TabIndex = 173;
    this.lblSign.Text = "Arrow Sign after no of boards";
    this.numSign.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    NumericUpDown numSign1 = this.numSign;
    point1 = new Point(258, 346);
    Point point17 = point1;
    numSign1.Location = point17;
    this.numSign.Maximum = new Decimal(new int[4]
    {
      26,
      0,
      0,
      0
    });
    this.numSign.Name = "numSign";
    NumericUpDown numSign2 = this.numSign;
    size1 = new Size(45, 22);
    Size size17 = size1;
    numSign2.Size = size17;
    this.numSign.TabIndex = 8;
    this.lblAllPfno.AutoSize = true;
    this.lblAllPfno.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblAllPfno1 = this.lblAllPfno;
    point1 = new Point(124, 105);
    Point point18 = point1;
    lblAllPfno1.Location = point18;
    this.lblAllPfno.Name = "lblAllPfno";
    Label lblAllPfno2 = this.lblAllPfno;
    size1 = new Size(95, 16 /*0x10*/);
    Size size18 = size1;
    lblAllPfno2.Size = size18;
    this.lblAllPfno.TabIndex = 176 /*0xB0*/;
    this.lblAllPfno.Text = "All Platforms";
    this.chkAgdbAllPfno.AutoSize = true;
    this.chkAgdbAllPfno.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    CheckBox chkAgdbAllPfno1 = this.chkAgdbAllPfno;
    point1 = new Point(258, 107);
    Point point19 = point1;
    chkAgdbAllPfno1.Location = point19;
    this.chkAgdbAllPfno.Name = "chkAgdbAllPfno";
    CheckBox chkAgdbAllPfno2 = this.chkAgdbAllPfno;
    size1 = new Size(15, 14);
    Size size19 = size1;
    chkAgdbAllPfno2.Size = size19;
    this.chkAgdbAllPfno.TabIndex = 3;
    this.chkAgdbAllPfno.UseVisualStyleBackColor = true;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.BackColor = Color.PowderBlue;
    size1 = new Size(436, 465);
    this.ClientSize = size1;
    this.Controls.Add((Control) this.chkAgdbAllPfno);
    this.Controls.Add((Control) this.lblAllPfno);
    this.Controls.Add((Control) this.numSign);
    this.Controls.Add((Control) this.lblSign);
    this.Controls.Add((Control) this.chkAGDBSharedPfNo);
    this.Controls.Add((Control) this.lblSharedPlatform);
    this.Controls.Add((Control) this.cmbAgdbSharedPfno);
    this.Controls.Add((Control) this.cmbAgdbPfno);
    this.Controls.Add((Control) this.txtAgdbMsgSwDly);
    this.Controls.Add((Control) this.lblMsgSwDly);
    this.Controls.Add((Control) this.lblSharedPfno);
    this.Controls.Add((Control) this.lblPfno);
    this.Controls.Add((Control) this.btnExit);
    this.Controls.Add((Control) this.btnOk);
    this.Controls.Add((Control) this.txtAgdbAddress);
    this.Controls.Add((Control) this.txtAgdbName);
    this.Controls.Add((Control) this.lblAddress);
    this.Controls.Add((Control) this.lblName);
    this.FormBorderStyle = FormBorderStyle.FixedToolWindow;
    this.Name = "frmNetworkAGDB";
    this.Text = "AGDB";
    this.numSign.EndInit();
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  internal virtual TextBox txtAgdbMsgSwDly
  {
    [DebuggerNonUserCode] get { return this._txtAgdbMsgSwDly; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtAgdbMsgSwDly = value;
    }
  }

  internal virtual Label lblMsgSwDly
  {
    [DebuggerNonUserCode] get { return this._lblMsgSwDly; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblMsgSwDly = value;
    }
  }

  internal virtual Label lblSharedPfno
  {
    [DebuggerNonUserCode] get { return this._lblSharedPfno; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblSharedPfno = value;
    }
  }

  internal virtual Label lblPfno
  {
    [DebuggerNonUserCode] get { return this._lblPfno; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._lblPfno = value; }
  }

  internal virtual Button btnExit
  {
    [DebuggerNonUserCode] get { return this._btnExit; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnExit_Click);
      if (this._btnExit != null)
        this._btnExit.Click -= eventHandler;
      this._btnExit = value;
      if (this._btnExit == null)
        return;
      this._btnExit.Click += eventHandler;
    }
  }

  internal virtual Button btnOk
  {
    [DebuggerNonUserCode] get { return this._btnOk; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnOk_Click);
      if (this._btnOk != null)
        this._btnOk.Click -= eventHandler;
      this._btnOk = value;
      if (this._btnOk == null)
        return;
      this._btnOk.Click += eventHandler;
    }
  }

  internal virtual TextBox txtAgdbAddress
  {
    [DebuggerNonUserCode] get { return this._txtAgdbAddress; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtAgdbAddress = value;
    }
  }

  internal virtual TextBox txtAgdbName
  {
    [DebuggerNonUserCode] get { return this._txtAgdbName; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtAgdbName = value;
    }
  }

  internal virtual Label lblAddress
  {
    [DebuggerNonUserCode] get { return this._lblAddress; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblAddress = value;
    }
  }

  internal virtual Label lblName
  {
    [DebuggerNonUserCode] get { return this._lblName; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._lblName = value; }
  }

  internal virtual ComboBox cmbAgdbPfno
  {
    [DebuggerNonUserCode] get { return this._cmbAgdbPfno; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._cmbAgdbPfno = value;
    }
  }

  internal virtual ComboBox cmbAgdbSharedPfno
  {
    [DebuggerNonUserCode] get { return this._cmbAgdbSharedPfno; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.cmbAgdbSharedPfno_DropDown);
      if (this._cmbAgdbSharedPfno != null)
        this._cmbAgdbSharedPfno.DropDown -= eventHandler;
      this._cmbAgdbSharedPfno = value;
      if (this._cmbAgdbSharedPfno == null)
        return;
      this._cmbAgdbSharedPfno.DropDown += eventHandler;
    }
  }

  internal virtual CheckBox chkAGDBSharedPfNo
  {
    [DebuggerNonUserCode] get { return this._chkAGDBSharedPfNo; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.chkAGDBSharedPfNo_CheckedChanged);
      if (this._chkAGDBSharedPfNo != null)
        this._chkAGDBSharedPfNo.CheckedChanged -= eventHandler;
      this._chkAGDBSharedPfNo = value;
      if (this._chkAGDBSharedPfNo == null)
        return;
      this._chkAGDBSharedPfNo.CheckedChanged += eventHandler;
    }
  }

  internal virtual Label lblSharedPlatform
  {
    [DebuggerNonUserCode] get { return this._lblSharedPlatform; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblSharedPlatform = value;
    }
  }

  internal virtual Label lblSign
  {
    [DebuggerNonUserCode] get { return this._lblSign; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._lblSign = value; }
  }

  internal virtual NumericUpDown numSign
  {
    [DebuggerNonUserCode] get { return this._numSign; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._numSign = value; }
  }

  internal virtual Label lblAllPfno
  {
    [DebuggerNonUserCode] get { return this._lblAllPfno; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblAllPfno = value;
    }
  }

  internal virtual CheckBox chkAgdbAllPfno
  {
    [DebuggerNonUserCode] get { return this._chkAgdbAllPfno; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.chkAgdbAllPfno_CheckedChanged);
      if (this._chkAgdbAllPfno != null)
        this._chkAgdbAllPfno.CheckedChanged -= eventHandler;
      this._chkAgdbAllPfno = value;
      if (this._chkAgdbAllPfno == null)
        return;
      this._chkAgdbAllPfno.CheckedChanged += eventHandler;
    }
  }

  private void btnOk_Click(object sender, EventArgs e)
  {
    try
    {
      if (Operators.CompareString(this.txtAgdbName.Text, "", false) == 0)
      {
        int num1 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Please Enter the Name", "Msg Box", 0, 0, 0);
      }
      else if (Operators.CompareString(this.txtAgdbAddress.Text, "", false) == 0)
      {
        int num2 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Please Enter the Address", "Msg Box", 0, 0, 0);
      }
      else if (!this.chkAgdbAllPfno.Checked && Operators.CompareString(this.cmbAgdbPfno.Text, "", false) == 0)
      {
        int num3 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Please select the Platform No", "Msg Box", 0, 0, 0);
      }
      else if (Operators.CompareString(this.txtAgdbMsgSwDly.Text, "", false) == 0)
      {
        int num4 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Please enter the message switching delay", "Msg Box", 0, 0, 0);
      }
      else
      {
        if (frmNetworkMDCH.hub_type)
        {
          frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].system_type[(int) frmNetworkMDCH.mdch_system_num] = "AGDB";
          frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].dis_board[(int) frmNetworkMDCH.mdch_system_num].dis_board_addr = Conversions.ToByte(this.txtAgdbAddress.Text);
          frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].dis_board[(int) frmNetworkMDCH.mdch_system_num].dis_board_name = this.txtAgdbName.Text;
          frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].dis_board[(int) frmNetworkMDCH.mdch_system_num].dis_board_type = "AGDB";
          frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].dis_board[(int) frmNetworkMDCH.mdch_system_num].platform_no = Operators.CompareString(this.cmbAgdbPfno.Text, "", false) != 0 ? this.cmbAgdbPfno.Text : string.Empty;
          if (this.chkAGDBSharedPfNo.Checked)
          {
            frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].dis_board[(int) frmNetworkMDCH.mdch_system_num].shared_platform = true;
            frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].dis_board[(int) frmNetworkMDCH.mdch_system_num].shared_platform_no = this.cmbAgdbSharedPfno.Text;
          }
          else
          {
            frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].dis_board[(int) frmNetworkMDCH.mdch_system_num].shared_platform = false;
            frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].dis_board[(int) frmNetworkMDCH.mdch_system_num].shared_platform_no = string.Empty;
          }
          frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].dis_board[(int) frmNetworkMDCH.mdch_system_num].all_platfroms = this.chkAgdbAllPfno.Checked;
          frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].dis_board[(int) frmNetworkMDCH.mdch_system_num].switching_time = Conversions.ToByte(this.txtAgdbMsgSwDly.Text);
          frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].dis_board[(int) frmNetworkMDCH.mdch_system_num].agdb_sign = Convert.ToByte(this.numSign.Value);
        }
        else
        {
          checked { --frmNetworkPDCH.pdch_port_num; }
          checked { --frmNetworkPDCH.pdch_system_num; }
          frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].pdch_port[(int) frmNetworkPDCH.pdch_port_num].dis_board[(int) frmNetworkPDCH.pdch_system_num].dis_board_addr = Conversions.ToByte(this.txtAgdbAddress.Text);
          frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].pdch_port[(int) frmNetworkPDCH.pdch_port_num].dis_board[(int) frmNetworkPDCH.pdch_system_num].dis_board_name = this.txtAgdbName.Text;
          frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].pdch_port[(int) frmNetworkPDCH.pdch_port_num].dis_board[(int) frmNetworkPDCH.pdch_system_num].dis_board_type = "AGDB";
          frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].pdch_port[(int) frmNetworkPDCH.pdch_port_num].dis_board[(int) frmNetworkPDCH.pdch_system_num].platform_no = Operators.CompareString(this.cmbAgdbPfno.Text, "", false) != 0 ? this.cmbAgdbPfno.Text : string.Empty;
          if (!this.chkAGDBSharedPfNo.Checked)
          {
            frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].pdch_port[(int) frmNetworkPDCH.pdch_port_num].dis_board[(int) frmNetworkPDCH.pdch_system_num].shared_platform = false;
            frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].pdch_port[(int) frmNetworkPDCH.pdch_port_num].dis_board[(int) frmNetworkPDCH.pdch_system_num].shared_platform_no = string.Empty;
          }
          else
          {
            frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].pdch_port[(int) frmNetworkPDCH.pdch_port_num].dis_board[(int) frmNetworkPDCH.pdch_system_num].shared_platform = true;
            frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].pdch_port[(int) frmNetworkPDCH.pdch_port_num].dis_board[(int) frmNetworkPDCH.pdch_system_num].shared_platform_no = this.cmbAgdbSharedPfno.Text;
          }
          frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].pdch_port[(int) frmNetworkPDCH.pdch_port_num].dis_board[(int) frmNetworkPDCH.pdch_system_num].all_platfroms = this.chkAgdbAllPfno.Checked;
          frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].pdch_port[(int) frmNetworkPDCH.pdch_port_num].dis_board[(int) frmNetworkPDCH.pdch_system_num].switching_time = Conversions.ToByte(this.txtAgdbMsgSwDly.Text);
          frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].pdch_port[(int) frmNetworkPDCH.pdch_port_num].dis_board[(int) frmNetworkPDCH.pdch_system_num].agdb_sign = Convert.ToByte(this.numSign.Value);
        }
        this.Close();
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void btnExit_Click(object sender, EventArgs e)
{
  this.Close();
}

  private void chkAgdbAllPfno_CheckedChanged(object sender, EventArgs e)
  {
    if (this.chkAgdbAllPfno.Checked)
    {
      this.cmbAgdbPfno.Text = "";
      this.cmbAgdbSharedPfno.Text = "";
      this.cmbAgdbPfno.Enabled = false;
      this.cmbAgdbSharedPfno.Enabled = false;
      this.chkAGDBSharedPfNo.Checked = false;
      this.chkAGDBSharedPfNo.Enabled = false;
    }
    else
    {
      this.cmbAgdbPfno.Enabled = true;
      this.cmbAgdbSharedPfno.Enabled = true;
      this.chkAGDBSharedPfNo.Enabled = true;
    }
  }

  private void cmbAgdbSharedPfno_DropDown(object sender, EventArgs e)
  {
    int index = 0;
    this.cmbAgdbSharedPfno.Items.Clear();
    while (index < frmMainFormIPIS.pfno_cnt)
    {
      this.cmbAgdbSharedPfno.Items.Add((object) frmMainFormIPIS.platform_nos[index]);
      checked { ++index; }
    }
  }

  private void chkAGDBSharedPfNo_CheckedChanged(object sender, EventArgs e)
  {
    if (this.chkAGDBSharedPfNo.Checked)
    {
      this.cmbAgdbSharedPfno.Visible = true;
      this.lblSharedPfno.Visible = true;
    }
    else
    {
      this.cmbAgdbSharedPfno.Visible = false;
      this.lblSharedPfno.Visible = false;
    }
  }

  private void frmNetworkAGDB_Load(object sender, EventArgs e)
  {
    int index = 0;
    this.cmbAgdbPfno.Items.Clear();
    while (index < frmMainFormIPIS.pfno_cnt)
    {
      this.cmbAgdbPfno.Items.Add((object) frmMainFormIPIS.platform_nos[index]);
      checked { ++index; }
    }
    if (frmNetworkMDCH.hub_type)
    {
      this.lblAllPfno.Visible = true;
      this.chkAgdbAllPfno.Visible = true;
    }
    else
    {
      this.lblAllPfno.Visible = false;
      this.chkAgdbAllPfno.Visible = false;
    }
  }
}

}