﻿namespace Announcement
{
	// Token: 0x0200000C RID: 12
	public partial class Station_Code : global::System.Windows.Forms.Form
	{
		// Token: 0x0600005F RID: 95 RVA: 0x0000D190 File Offset: 0x0000B390
		protected override void Dispose(bool disposing)
		{
			bool flag = disposing && this.components != null;
			if (flag)
			{
				this.components.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06000060 RID: 96 RVA: 0x0000D1C8 File Offset: 0x0000B3C8
		private void InitializeComponent()
		{
			global::System.ComponentModel.ComponentResourceManager componentResourceManager = new global::System.ComponentModel.ComponentResourceManager(typeof(global::Announcement.Station_Code));
			this.BTN_Save = new global::System.Windows.Forms.Button();
			this.CB_SCode = new global::System.Windows.Forms.ComboBox();
			this.BTN_Exit = new global::System.Windows.Forms.Button();
			this.BTN_Del = new global::System.Windows.Forms.Button();
			this.BTN_New = new global::System.Windows.Forms.Button();
			this.LB_5 = new global::System.Windows.Forms.Label();
			this.label1 = new global::System.Windows.Forms.Label();
			this.TB_SName = new global::System.Windows.Forms.TextBox();
			this.Open_WaveFile = new global::System.Windows.Forms.OpenFileDialog();
			this.BTN_Edit = new global::System.Windows.Forms.Button();
			this.GB_Wave = new global::System.Windows.Forms.GroupBox();
			this.TB_Ewf = new global::System.Windows.Forms.TextBox();
			this.TB_Hwf = new global::System.Windows.Forms.TextBox();
			this.BTN_Ewf = new global::System.Windows.Forms.Button();
			this.BTN_Hwf = new global::System.Windows.Forms.Button();
			this.Btn_CancelStn = new global::System.Windows.Forms.Button();
			this.GB_Wave.SuspendLayout();
			base.SuspendLayout();
			this.BTN_Save.Enabled = false;
			this.BTN_Save.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 14.25f, global::System.Drawing.FontStyle.Bold, global::System.Drawing.GraphicsUnit.Point, 0);
			this.BTN_Save.ForeColor = global::System.Drawing.Color.FromArgb(0, 0, 192);
			this.BTN_Save.Image = (global::System.Drawing.Image)componentResourceManager.GetObject("BTN_Save.Image");
			this.BTN_Save.ImageAlign = global::System.Drawing.ContentAlignment.MiddleLeft;
			this.BTN_Save.Location = new global::System.Drawing.Point(689, 66);
			this.BTN_Save.Name = "BTN_Save";
			this.BTN_Save.Size = new global::System.Drawing.Size(113, 46);
			this.BTN_Save.TabIndex = 22;
			this.BTN_Save.Text = "SAVE";
			this.BTN_Save.TextAlign = global::System.Drawing.ContentAlignment.MiddleRight;
			this.BTN_Save.UseVisualStyleBackColor = true;
			this.BTN_Save.Click += new global::System.EventHandler(this.BTN_Save_Click);
			this.CB_SCode.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 12f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 0);
			this.CB_SCode.FormattingEnabled = true;
			this.CB_SCode.ItemHeight = 20;
			this.CB_SCode.Location = new global::System.Drawing.Point(111, 10);
			this.CB_SCode.Name = "CB_SCode";
			this.CB_SCode.Size = new global::System.Drawing.Size(96, 28);
			this.CB_SCode.TabIndex = 21;
			this.CB_SCode.SelectedIndexChanged += new global::System.EventHandler(this.CB_SCode_SelectedIndexChanged);
			this.BTN_Exit.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 14.25f, global::System.Drawing.FontStyle.Bold, global::System.Drawing.GraphicsUnit.Point, 0);
			this.BTN_Exit.ForeColor = global::System.Drawing.Color.Red;
			this.BTN_Exit.Image = (global::System.Drawing.Image)componentResourceManager.GetObject("BTN_Exit.Image");
			this.BTN_Exit.ImageAlign = global::System.Drawing.ContentAlignment.MiddleLeft;
			this.BTN_Exit.Location = new global::System.Drawing.Point(689, 117);
			this.BTN_Exit.Name = "BTN_Exit";
			this.BTN_Exit.Size = new global::System.Drawing.Size(113, 46);
			this.BTN_Exit.TabIndex = 20;
			this.BTN_Exit.Text = "EXIT";
			this.BTN_Exit.TextAlign = global::System.Drawing.ContentAlignment.MiddleRight;
			this.BTN_Exit.UseVisualStyleBackColor = true;
			this.BTN_Exit.Click += new global::System.EventHandler(this.BTN_Exit_Click);
			this.BTN_Del.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 12f, global::System.Drawing.FontStyle.Bold, global::System.Drawing.GraphicsUnit.Point, 0);
			this.BTN_Del.ForeColor = global::System.Drawing.Color.Red;
			this.BTN_Del.Image = (global::System.Drawing.Image)componentResourceManager.GetObject("BTN_Del.Image");
			this.BTN_Del.ImageAlign = global::System.Drawing.ContentAlignment.MiddleLeft;
			this.BTN_Del.Location = new global::System.Drawing.Point(569, 66);
			this.BTN_Del.Name = "BTN_Del";
			this.BTN_Del.Size = new global::System.Drawing.Size(113, 46);
			this.BTN_Del.TabIndex = 19;
			this.BTN_Del.Text = "DELETE";
			this.BTN_Del.TextAlign = global::System.Drawing.ContentAlignment.MiddleRight;
			this.BTN_Del.UseVisualStyleBackColor = true;
			this.BTN_Del.Click += new global::System.EventHandler(this.BTN_Del_Click);
			this.BTN_New.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 14.25f, global::System.Drawing.FontStyle.Bold, global::System.Drawing.GraphicsUnit.Point, 0);
			this.BTN_New.ForeColor = global::System.Drawing.Color.FromArgb(0, 192, 0);
			this.BTN_New.Image = (global::System.Drawing.Image)componentResourceManager.GetObject("BTN_New.Image");
			this.BTN_New.ImageAlign = global::System.Drawing.ContentAlignment.MiddleLeft;
			this.BTN_New.Location = new global::System.Drawing.Point(569, 10);
			this.BTN_New.Name = "BTN_New";
			this.BTN_New.Size = new global::System.Drawing.Size(113, 46);
			this.BTN_New.TabIndex = 18;
			this.BTN_New.Text = "NEW";
			this.BTN_New.TextAlign = global::System.Drawing.ContentAlignment.MiddleRight;
			this.BTN_New.UseVisualStyleBackColor = true;
			this.BTN_New.Click += new global::System.EventHandler(this.BTN_New_Click);
			this.LB_5.AutoSize = true;
			this.LB_5.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 12f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 0);
			this.LB_5.ForeColor = global::System.Drawing.Color.FromArgb(0, 0, 192);
			this.LB_5.Location = new global::System.Drawing.Point(210, 15);
			this.LB_5.Name = "LB_5";
			this.LB_5.Size = new global::System.Drawing.Size(106, 20);
			this.LB_5.TabIndex = 13;
			this.LB_5.Text = "Station Name";
			this.label1.AutoSize = true;
			this.label1.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 12f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 0);
			this.label1.ForeColor = global::System.Drawing.Color.FromArgb(0, 0, 192);
			this.label1.Location = new global::System.Drawing.Point(5, 14);
			this.label1.Name = "label1";
			this.label1.Size = new global::System.Drawing.Size(102, 20);
			this.label1.TabIndex = 25;
			this.label1.Text = "Station Code";
			this.TB_SName.Enabled = false;
			this.TB_SName.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 12f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 0);
			this.TB_SName.Location = new global::System.Drawing.Point(318, 12);
			this.TB_SName.MaxLength = 30;
			this.TB_SName.Name = "TB_SName";
			this.TB_SName.Size = new global::System.Drawing.Size(238, 26);
			this.TB_SName.TabIndex = 26;
			this.TB_SName.TextChanged += new global::System.EventHandler(this.TB_SName_TextChanged);
			this.Open_WaveFile.FileName = "Open_WaveFile";
			this.Open_WaveFile.FileOk += new global::System.ComponentModel.CancelEventHandler(this.Open_WaveFile_FileOk);
			this.BTN_Edit.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 12f, global::System.Drawing.FontStyle.Bold, global::System.Drawing.GraphicsUnit.Point, 0);
			this.BTN_Edit.ForeColor = global::System.Drawing.Color.FromArgb(0, 0, 192);
			this.BTN_Edit.Image = (global::System.Drawing.Image)componentResourceManager.GetObject("BTN_Edit.Image");
			this.BTN_Edit.ImageAlign = global::System.Drawing.ContentAlignment.MiddleLeft;
			this.BTN_Edit.Location = new global::System.Drawing.Point(688, 10);
			this.BTN_Edit.Name = "BTN_Edit";
			this.BTN_Edit.Size = new global::System.Drawing.Size(114, 46);
			this.BTN_Edit.TabIndex = 45;
			this.BTN_Edit.Text = "EDIT";
			this.BTN_Edit.TextAlign = global::System.Drawing.ContentAlignment.MiddleRight;
			this.BTN_Edit.UseVisualStyleBackColor = true;
			this.BTN_Edit.Click += new global::System.EventHandler(this.BTN_Edit_Click);
			this.GB_Wave.Controls.Add(this.TB_Ewf);
			this.GB_Wave.Controls.Add(this.TB_Hwf);
			this.GB_Wave.Controls.Add(this.BTN_Ewf);
			this.GB_Wave.Controls.Add(this.BTN_Hwf);
			this.GB_Wave.Enabled = false;
			this.GB_Wave.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 9.75f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 0);
			this.GB_Wave.ForeColor = global::System.Drawing.Color.FromArgb(0, 0, 192);
			this.GB_Wave.Location = new global::System.Drawing.Point(9, 59);
			this.GB_Wave.Name = "GB_Wave";
			this.GB_Wave.Size = new global::System.Drawing.Size(554, 112);
			this.GB_Wave.TabIndex = 46;
			this.GB_Wave.TabStop = false;
			this.GB_Wave.Text = "Select Wave File";
			this.TB_Ewf.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 12f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 0);
			this.TB_Ewf.Location = new global::System.Drawing.Point(193, 72);
			this.TB_Ewf.Name = "TB_Ewf";
			this.TB_Ewf.Size = new global::System.Drawing.Size(354, 26);
			this.TB_Ewf.TabIndex = 3;
			this.TB_Hwf.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 12f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 0);
			this.TB_Hwf.Location = new global::System.Drawing.Point(193, 25);
			this.TB_Hwf.Name = "TB_Hwf";
			this.TB_Hwf.Size = new global::System.Drawing.Size(354, 26);
			this.TB_Hwf.TabIndex = 2;
			this.BTN_Ewf.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 12f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 0);
			this.BTN_Ewf.ForeColor = global::System.Drawing.Color.Black;
			this.BTN_Ewf.Location = new global::System.Drawing.Point(6, 67);
			this.BTN_Ewf.Name = "BTN_Ewf";
			this.BTN_Ewf.Size = new global::System.Drawing.Size(172, 37);
			this.BTN_Ewf.TabIndex = 1;
			this.BTN_Ewf.Text = "Select English Wave";
			this.BTN_Ewf.UseVisualStyleBackColor = true;
			this.BTN_Ewf.Click += new global::System.EventHandler(this.BTN_Ewf_Click);
			this.BTN_Hwf.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 12f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 0);
			this.BTN_Hwf.ForeColor = global::System.Drawing.Color.Black;
			this.BTN_Hwf.Location = new global::System.Drawing.Point(6, 20);
			this.BTN_Hwf.Name = "BTN_Hwf";
			this.BTN_Hwf.Size = new global::System.Drawing.Size(172, 37);
			this.BTN_Hwf.TabIndex = 0;
			this.BTN_Hwf.Text = "Select Hindi Wave";
			this.BTN_Hwf.UseVisualStyleBackColor = true;
			this.BTN_Hwf.Click += new global::System.EventHandler(this.BTN_Hwf_Click);
			this.Btn_CancelStn.Anchor = global::System.Windows.Forms.AnchorStyles.Top;
			this.Btn_CancelStn.DialogResult = global::System.Windows.Forms.DialogResult.Cancel;
			this.Btn_CancelStn.Enabled = false;
			this.Btn_CancelStn.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 14.25f, global::System.Drawing.FontStyle.Bold, global::System.Drawing.GraphicsUnit.Point, 0);
			this.Btn_CancelStn.ForeColor = global::System.Drawing.Color.FromArgb(192, 64, 0);
			this.Btn_CancelStn.ImageAlign = global::System.Drawing.ContentAlignment.MiddleLeft;
			this.Btn_CancelStn.Location = new global::System.Drawing.Point(572, 118);
			this.Btn_CancelStn.Name = "Btn_CancelStn";
			this.Btn_CancelStn.Size = new global::System.Drawing.Size(110, 45);
			this.Btn_CancelStn.TabIndex = 47;
			this.Btn_CancelStn.Text = "&CANCEL";
			this.Btn_CancelStn.UseVisualStyleBackColor = true;
			this.Btn_CancelStn.Click += new global::System.EventHandler(this.Btn_CancelStn_Click);
			base.AutoScaleDimensions = new global::System.Drawing.SizeF(6f, 13f);
			base.AutoScaleMode = global::System.Windows.Forms.AutoScaleMode.Font;
			this.BackColor = global::System.Drawing.Color.FromArgb(128, 255, 255);
			base.ClientSize = new global::System.Drawing.Size(811, 176);
			base.ControlBox = false;
			base.Controls.Add(this.Btn_CancelStn);
			base.Controls.Add(this.GB_Wave);
			base.Controls.Add(this.BTN_Edit);
			base.Controls.Add(this.BTN_New);
			base.Controls.Add(this.TB_SName);
			base.Controls.Add(this.label1);
			base.Controls.Add(this.BTN_Save);
			base.Controls.Add(this.CB_SCode);
			base.Controls.Add(this.BTN_Exit);
			base.Controls.Add(this.BTN_Del);
			base.Controls.Add(this.LB_5);
			base.Name = "Station_Code";
			base.StartPosition = global::System.Windows.Forms.FormStartPosition.CenterScreen;
			this.Text = "Station_Code";
			base.Load += new global::System.EventHandler(this.Station_Code_Load);
			this.GB_Wave.ResumeLayout(false);
			this.GB_Wave.PerformLayout();
			base.ResumeLayout(false);
			base.PerformLayout();
		}

		// Token: 0x0400006A RID: 106
		private global::System.ComponentModel.IContainer components = null;

		// Token: 0x0400006B RID: 107
		private global::System.Windows.Forms.Button BTN_Save;

		// Token: 0x0400006C RID: 108
		private global::System.Windows.Forms.ComboBox CB_SCode;

		// Token: 0x0400006D RID: 109
		private global::System.Windows.Forms.Button BTN_Exit;

		// Token: 0x0400006E RID: 110
		private global::System.Windows.Forms.Button BTN_Del;

		// Token: 0x0400006F RID: 111
		private global::System.Windows.Forms.Button BTN_New;

		// Token: 0x04000070 RID: 112
		private global::System.Windows.Forms.Label LB_5;

		// Token: 0x04000071 RID: 113
		private global::System.Windows.Forms.Label label1;

		// Token: 0x04000072 RID: 114
		private global::System.Windows.Forms.TextBox TB_SName;

		// Token: 0x04000073 RID: 115
		private global::System.Windows.Forms.OpenFileDialog Open_WaveFile;

		// Token: 0x04000074 RID: 116
		private global::System.Windows.Forms.Button BTN_Edit;

		// Token: 0x04000075 RID: 117
		private global::System.Windows.Forms.GroupBox GB_Wave;

		// Token: 0x04000076 RID: 118
		private global::System.Windows.Forms.Button BTN_Hwf;

		// Token: 0x04000077 RID: 119
		private global::System.Windows.Forms.Button BTN_Ewf;

		// Token: 0x04000078 RID: 120
		private global::System.Windows.Forms.TextBox TB_Ewf;

		// Token: 0x04000079 RID: 121
		private global::System.Windows.Forms.TextBox TB_Hwf;

		// Token: 0x0400007A RID: 122
		private global::System.Windows.Forms.Button Btn_CancelStn;
	}
}
