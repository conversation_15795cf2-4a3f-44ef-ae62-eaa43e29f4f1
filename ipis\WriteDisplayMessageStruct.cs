// Decompiled with JetBrains decompiler
// Type: ipis.WriteDisplayMessageStruct
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Diagnostics;
using System.IO;
using System.Text;

namespace ipis
{

public class WriteDisplayMessageStruct
{
  [DebuggerNonUserCode]
  public WriteDisplayMessageStruct()
  {
  }

  public static void WritePdbStruct()
  {
    string empty1 = string.Empty;
    string path = "C:\\IPIS\\shared_info\\PdbMsgDb.txt";
    try
    {
      FileStream fileStream = new FileStream(path, FileMode.Create, FileAccess.Write, FileShare.None);
      StreamWriter streamWriter = new StreamWriter((Stream) fileStream);
      string empty2 = string.Empty;
      string empty3 = string.Empty;
      int index1 = 0;
      while (index1 < (int) byte.MaxValue)
      {
        int index2 = 0;
        if (taddb_msg.pdb_msg[index1].no_of_msgs > 0)
        {
          string data1 = "{Conversions.ToString(index1)},{Conversions.ToString(taddb_msg.pdb_msg[index1].no_of_msgs)}";
          string empty4 = string.Empty;
          WriteDisplayMessageStruct.EncryptingTextFileData(data1, ref empty4);
          string str1 = "{Conversions.ToString(data1.Length)};{empty4}";
          streamWriter.WriteLine(str1);
          while (index2 < taddb_msg.pdb_msg[index1].no_of_msgs)
          {
            string data2 = "{Conversions.ToString(taddb_msg.pdb_msg[index1].tr_msg[index2].used)},{taddb_msg.pdb_msg[index1].tr_msg[index2].ar_time},{taddb_msg.pdb_msg[index1].tr_msg[index2].dp_time},{Conversions.ToString(taddb_msg.pdb_msg[index1].tr_msg[index2].effect)},{taddb_msg.pdb_msg[index1].tr_msg[index2].platform_no},{taddb_msg.pdb_msg[index1].tr_msg[index2].status},{taddb_msg.pdb_msg[index1].tr_msg[index2].train_no},{Conversions.ToString(taddb_msg.pdb_msg[index1].tr_msg[index2].def_msg_no)}";
            string empty5 = string.Empty;
            WriteDisplayMessageStruct.EncryptingTextFileData(data2, ref empty5);
            string str2 = "{Conversions.ToString(data2.Length)};{empty5}";
            streamWriter.WriteLine(str2);
            checked { ++index2; }
          }
        }
        checked { ++index1; }
      }
      streamWriter.Flush();
      streamWriter.Close();
      fileStream.Close();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
  }

  public static void ReadPdbStruct()
  {
    string[] strArray1 = new string[3];
    string[] strArray2 = new string[10001];
    string empty = string.Empty;
    int num1 = 0;
    string path = "C:\\IPIS\\shared_info\\PdbMsgDb.txt";
    try
    {
      if (!File.Exists(path))
        return;
      FileStream fileStream = new FileStream(path, FileMode.Open, FileAccess.Read, FileShare.None);
      StreamReader streamReader = new StreamReader((Stream) fileStream);
      int index1 = 0;
      while (streamReader.Peek() >= 0)
      {
        strArray2[index1] = streamReader.ReadLine();
        string[] strArray3 = strArray2[index1].Split(';');
        int integer = Conversions.ToInteger(strArray3[0]);
        WriteDisplayMessageStruct.DecryptingTextFileData(strArray3[1], ref empty, Conversions.ToString(integer));
        strArray2[index1] = empty;
        checked { ++index1; }
      }
      fileStream.Close();
      streamReader.Close();
      int num2 = index1;
      if (num2 == 0)
        return;
      int index2 = 0;
      int index3 = 0;
      num1 = 0;
      while (index2 < (int) byte.MaxValue)
      {
        string[] strArray4 = strArray2[index3].Split(',');
        if (strArray4.Length == 2 && (double) index2 == Conversions.ToDouble(strArray4[0]))
        {
          taddb_msg.pdb_msg[index2].no_of_msgs = Conversions.ToInteger(strArray4[1]);
          int index4 = 0;
          checked { ++index3; }
          while (index4 < taddb_msg.pdb_msg[index2].no_of_msgs)
          {
            string[] strArray5 = strArray2[index3].Split(',');
            if (strArray5.Length == 9)
            {
              taddb_msg.pdb_msg[index2].tr_msg[index4].used = Conversions.ToBoolean(strArray5[0]);
              taddb_msg.pdb_msg[index2].tr_msg[index4].ar_time = strArray5[1];
              taddb_msg.pdb_msg[index2].tr_msg[index4].dp_time = strArray5[2];
              taddb_msg.pdb_msg[index2].tr_msg[index4].effect = Conversions.ToByte(strArray5[3]);
              taddb_msg.pdb_msg[index2].tr_msg[index4].platform_no = strArray5[4];
              taddb_msg.pdb_msg[index2].tr_msg[index4].status = strArray5[5];
              taddb_msg.pdb_msg[index2].tr_msg[index4].train_no = strArray5[6];
              taddb_msg.pdb_msg[index2].tr_msg[index4].def_msg_no = Conversions.ToInteger(strArray5[7]);
              checked { ++index3; }
            }
            checked { ++index4; }
          }
          if (index3 == num2)
            break;
        }
        checked { ++index2; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
  }

  public static void WriteMldbPdbDefMsg()
  {
    string empty1 = string.Empty;
    string path = "C:\\IPIS\\shared_info\\MldbPdbDefMsg.txt";
    try
    {
      FileStream fileStream = new FileStream(path, FileMode.Create, FileAccess.Write, FileShare.None);
      StreamWriter streamWriter = new StreamWriter((Stream) fileStream);
      int index = 0;
      while (index < frmMainFormIPIS.addmsg_count)
      {
        string data = "{Conversions.ToString(frmMainFormIPIS.addmsg_count)},{Conversions.ToString(frmMainFormIPIS.chk_array[index])}";
        string empty2 = string.Empty;
        WriteDisplayMessageStruct.EncryptingTextFileData(data, ref empty2);
        string str = "{Conversions.ToString(data.Length)};{empty2}";
        streamWriter.WriteLine(str);
        streamWriter.Flush();
        checked { ++index; }
      }
      string data1 = "{Conversions.ToString(taddb_msg.def_mldb_addr)},{taddb_msg.def_mldb_name},{frmMainFormIPIS.pdb_msg_platform_no}";
      string empty3 = string.Empty;
      WriteDisplayMessageStruct.EncryptingTextFileData(data1, ref empty3);
      string str1 = "{Conversions.ToString(data1.Length)};{empty3}";
      streamWriter.WriteLine(str1);
      streamWriter.Flush();
      streamWriter.Close();
      fileStream.Close();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
  }

  public static void ReadMldbPdbDefMsg()
  {
    string[] strArray1 = new string[3];
    string path = "C:\\IPIS\\shared_info\\MldbPdbDefMsg.txt";
    try
    {
      if (!File.Exists(path))
        return;
      FileStream fileStream = new FileStream(path, FileMode.Open, FileAccess.Read, FileShare.None);
      StreamReader streamReader = new StreamReader((Stream) fileStream);
      int index = 0;
      string empty = string.Empty;
      while (streamReader.Peek() >= 0)
      {
        string[] strArray2 = streamReader.ReadLine().Split(';');
        int integer1 = Conversions.ToInteger(strArray2[0]);
        WriteDisplayMessageStruct.DecryptingTextFileData(strArray2[1], ref empty, Conversions.ToString(integer1));
        string[] strArray3 = empty.Split(',');
        int integer2 = 0;
        if (strArray3.Length == 2)
        {
          integer2 = Conversions.ToInteger(strArray3[0]);
          frmMainFormIPIS.chk_array[index] = Conversions.ToByte(strArray3[1]);
        }
        if (index == integer2)
        {
          taddb_msg.def_mldb_addr = Conversions.ToByte(strArray3[0]);
          taddb_msg.def_mldb_name = strArray3[1];
          frmMainFormIPIS.pdb_msg_platform_no = strArray3[2];
          break;
        }
        checked { ++index; }
      }
      fileStream.Close();
      streamReader.Close();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
  }

  public static void WriteMldbStruct()
  {
    string empty1 = string.Empty;
    string path = "C:\\IPIS\\shared_info\\mldbMsgDb.txt";
    try
    {
      FileStream fileStream = new FileStream(path, FileMode.Create, FileAccess.ReadWrite, FileShare.ReadWrite);
      StreamWriter streamWriter = new StreamWriter((Stream) fileStream);
      string empty2 = string.Empty;
      string empty3 = string.Empty;
      int index1 = 0;
      while (index1 < (int) byte.MaxValue)
      {
        int index2 = 0;
        if (taddb_msg.mldb_msg[index1].no_of_msgs > 0)
        {
          string data1 = "{Conversions.ToString(index1)},{Conversions.ToString(taddb_msg.mldb_msg[index1].no_of_msgs)}";
          string empty4 = string.Empty;
          WriteDisplayMessageStruct.EncryptingTextFileData(data1, ref empty4);
          string str1 = "{Conversions.ToString(data1.Length)};{empty4}";
          streamWriter.WriteLine(str1);
          while (index2 < taddb_msg.mldb_msg[index1].no_of_msgs)
          {
            string data2 = "{Conversions.ToString(taddb_msg.mldb_msg[index1].tr_msg[index2].used)},{taddb_msg.mldb_msg[index1].tr_msg[index2].ar_time},{taddb_msg.mldb_msg[index1].tr_msg[index2].dp_time},{Conversions.ToString(taddb_msg.mldb_msg[index1].tr_msg[index2].effect)},{taddb_msg.mldb_msg[index1].tr_msg[index2].platform_no},{taddb_msg.mldb_msg[index1].tr_msg[index2].status},{taddb_msg.mldb_msg[index1].tr_msg[index2].train_no},{Conversions.ToString(taddb_msg.mldb_msg[index1].tr_msg[index2].def_msg_no)}";
            string empty5 = string.Empty;
            WriteDisplayMessageStruct.EncryptingTextFileData(data2, ref empty5);
            string str2 = "{Conversions.ToString(data2.Length)};{empty5}";
            streamWriter.WriteLine(str2);
            checked { ++index2; }
          }
        }
        checked { ++index1; }
      }
      streamWriter.Flush();
      streamWriter.Close();
      fileStream.Close();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
  }

  public static void ReadMldbStruct()
  {
    string[] strArray1 = new string[3];
    string[] strArray2 = new string[10001];
    string empty = string.Empty;
    int num1 = 0;
    string path = "C:\\IPIS\\shared_info\\MldbMsgDb.txt";
    try
    {
      if (!File.Exists(path))
        return;
      FileStream fileStream = new FileStream(path, FileMode.Open, FileAccess.Read, FileShare.None);
      StreamReader streamReader = new StreamReader((Stream) fileStream);
      int index1 = 0;
      while (streamReader.Peek() >= 0)
      {
        strArray2[index1] = streamReader.ReadLine();
        string[] strArray3 = strArray2[index1].Split(';');
        int integer = Conversions.ToInteger(strArray3[0]);
        WriteDisplayMessageStruct.DecryptingTextFileData(strArray3[1], ref empty, Conversions.ToString(integer));
        strArray2[index1] = empty;
        checked { ++index1; }
      }
      fileStream.Close();
      streamReader.Close();
      int num2 = index1;
      if (num2 == 0)
        return;
      int index2 = 0;
      int index3 = 0;
      num1 = 0;
      while (index2 < (int) byte.MaxValue)
      {
        string[] strArray4 = strArray2[index3].Split(',');
        if (strArray4.Length == 2 && (double) index2 == Conversions.ToDouble(strArray4[0]))
        {
          taddb_msg.mldb_msg[index2].no_of_msgs = Conversions.ToInteger(strArray4[1]);
          int index4 = 0;
          checked { ++index3; }
          while (index4 < taddb_msg.mldb_msg[index2].no_of_msgs)
          {
            string[] strArray5 = strArray2[index3].Split(',');
            if (strArray5.Length == 9)
            {
              taddb_msg.mldb_msg[index2].tr_msg[index4].used = Conversions.ToBoolean(strArray5[0]);
              taddb_msg.mldb_msg[index2].tr_msg[index4].ar_time = strArray5[1];
              taddb_msg.mldb_msg[index2].tr_msg[index4].dp_time = strArray5[2];
              taddb_msg.mldb_msg[index2].tr_msg[index4].effect = Conversions.ToByte(strArray5[3]);
              taddb_msg.mldb_msg[index2].tr_msg[index4].platform_no = strArray5[4];
              taddb_msg.mldb_msg[index2].tr_msg[index4].status = strArray5[5];
              taddb_msg.mldb_msg[index2].tr_msg[index4].train_no = strArray5[6];
              taddb_msg.mldb_msg[index2].tr_msg[index4].def_msg_no = Conversions.ToInteger(strArray5[7]);
              checked { ++index3; }
            }
            checked { ++index4; }
          }
          if (index3 == num2)
            break;
        }
        checked { ++index2; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
  }

  public static void WriteAgdbStruct()
  {
    string empty1 = string.Empty;
    string path = "C:\\IPIS\\shared_info\\AgdbMsgDb.txt";
    try
    {
      FileStream fileStream = new FileStream(path, FileMode.Create, FileAccess.Write, FileShare.None);
      StreamWriter streamWriter = new StreamWriter((Stream) fileStream);
      string empty2 = string.Empty;
      string empty3 = string.Empty;
      int index1 = 0;
      while (index1 < (int) byte.MaxValue)
      {
        int index2 = 0;
        if (taddb_msg.agdb_msg[index1].no_of_msgs > 0)
        {
          string data1 = "{Conversions.ToString(index1)},{Conversions.ToString(taddb_msg.agdb_msg[index1].no_of_msgs)}";
          string empty4 = string.Empty;
          WriteDisplayMessageStruct.EncryptingTextFileData(data1, ref empty4);
          string str1 = "{Conversions.ToString(data1.Length)};{empty4}";
          streamWriter.WriteLine(str1);
          while (index2 < taddb_msg.agdb_msg[index1].no_of_msgs)
          {
            string data2 = "{Conversions.ToString(taddb_msg.agdb_msg[index1].tr_msg[index2].used)},{taddb_msg.agdb_msg[index1].tr_msg[index2].ar_time},{taddb_msg.agdb_msg[index1].tr_msg[index2].dp_time},{Conversions.ToString(taddb_msg.agdb_msg[index1].tr_msg[index2].effect)},{taddb_msg.agdb_msg[index1].tr_msg[index2].platform_no},{taddb_msg.agdb_msg[index1].tr_msg[index2].status},{taddb_msg.agdb_msg[index1].tr_msg[index2].train_no},{Conversions.ToString(taddb_msg.agdb_msg[index1].tr_msg[index2].def_msg_no)}";
            string empty5 = string.Empty;
            WriteDisplayMessageStruct.EncryptingTextFileData(data2, ref empty5);
            string str2 = "{Conversions.ToString(data2.Length)};{empty5}";
            streamWriter.WriteLine(str2);
            checked { ++index2; }
          }
        }
        checked { ++index1; }
      }
      streamWriter.Flush();
      streamWriter.Close();
      fileStream.Close();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
  }

  public static void ReadAgdbStruct()
  {
    string[] strArray1 = new string[3];
    string[] strArray2 = new string[10001];
    string empty = string.Empty;
    int num1 = 0;
    string path = "C:\\IPIS\\shared_info\\AgdbMsgDb.txt";
    try
    {
      if (!File.Exists(path))
        return;
      FileStream fileStream = new FileStream(path, FileMode.Open, FileAccess.Read, FileShare.None);
      StreamReader streamReader = new StreamReader((Stream) fileStream);
      int index1 = 0;
      while (streamReader.Peek() >= 0)
      {
        strArray2[index1] = streamReader.ReadLine();
        string[] strArray3 = strArray2[index1].Split(';');
        int integer = Conversions.ToInteger(strArray3[0]);
        WriteDisplayMessageStruct.DecryptingTextFileData(strArray3[1], ref empty, Conversions.ToString(integer));
        strArray2[index1] = empty;
        checked { ++index1; }
      }
      fileStream.Close();
      streamReader.Close();
      int num2 = index1;
      if (num2 == 0)
        return;
      int index2 = 0;
      int index3 = 0;
      num1 = 0;
      while (index2 < (int) byte.MaxValue)
      {
        string[] strArray4 = strArray2[index3].Split(',');
        if (strArray4.Length == 2 && (double) index2 == Conversions.ToDouble(strArray4[0]))
        {
          taddb_msg.agdb_msg[index2].no_of_msgs = Conversions.ToInteger(strArray4[1]);
          int index4 = 0;
          checked { ++index3; }
          while (index4 < taddb_msg.agdb_msg[index2].no_of_msgs)
          {
            string[] strArray5 = strArray2[index3].Split(',');
            if (strArray5.Length == 9)
            {
              taddb_msg.agdb_msg[index2].tr_msg[index4].used = Conversions.ToBoolean(strArray5[0]);
              taddb_msg.agdb_msg[index2].tr_msg[index4].ar_time = strArray5[1];
              taddb_msg.agdb_msg[index2].tr_msg[index4].dp_time = strArray5[2];
              taddb_msg.agdb_msg[index2].tr_msg[index4].effect = Conversions.ToByte(strArray5[3]);
              taddb_msg.agdb_msg[index2].tr_msg[index4].platform_no = strArray5[4];
              taddb_msg.agdb_msg[index2].tr_msg[index4].status = strArray5[5];
              taddb_msg.agdb_msg[index2].tr_msg[index4].train_no = strArray5[6];
              taddb_msg.agdb_msg[index2].tr_msg[index4].def_msg_no = Conversions.ToInteger(strArray5[7]);
              checked { ++index3; }
            }
            checked { ++index4; }
          }
          if (index3 == num2)
            break;
        }
        checked { ++index2; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      int num3 = (int) Interaction.MsgBox((object) ex.Message);
      ProjectData.ClearProjectError();
    }
  }

  public static void WriteAgdbComStruct()
  {
    string empty1 = string.Empty;
    string empty2 = string.Empty;
    string path = "C:\\IPIS\\shared_info\\AgdbComMsgDb.txt";
    try
    {
      FileStream fileStream = new FileStream(path, FileMode.Create, FileAccess.Write, FileShare.None);
      StreamWriter streamWriter = new StreamWriter((Stream) fileStream);
      string empty3 = string.Empty;
      int index1 = 0;
      while (index1 < (int) byte.MaxValue)
      {
        int index2 = 0;
        if (taddb_msg.agdb_com_msg[index1].no_of_msgs > 0)
        {
          string data1 = "{Conversions.ToString(index1)},{Conversions.ToString(taddb_msg.agdb_com_msg[index1].no_of_msgs)}";
          string empty4 = string.Empty;
          WriteDisplayMessageStruct.EncryptingTextFileData(data1, ref empty4);
          string str1 = "{Conversions.ToString(data1.Length)};{empty4}";
          streamWriter.WriteLine(str1);
          while (index2 < taddb_msg.agdb_com_msg[index1].no_of_msgs)
          {
            string data2 = "{Conversions.ToString(taddb_msg.agdb_com_msg[index1].tr_msg[index2].used)},{taddb_msg.agdb_com_msg[index1].tr_msg[index2].ar_time},{taddb_msg.agdb_com_msg[index1].tr_msg[index2].dp_time},{Conversions.ToString(taddb_msg.agdb_com_msg[index1].tr_msg[index2].effect)},{taddb_msg.agdb_com_msg[index1].tr_msg[index2].platform_no},{taddb_msg.agdb_com_msg[index1].tr_msg[index2].status},{taddb_msg.agdb_com_msg[index1].tr_msg[index2].train_no},{Conversions.ToString(taddb_msg.agdb_com_msg[index1].tr_msg[index2].def_msg_no)}";
            empty4 = string.Empty;
            WriteDisplayMessageStruct.EncryptingTextFileData(data2, ref empty4);
            string str2 = "{Conversions.ToString(data2.Length)};{empty4}";
            streamWriter.WriteLine(str2);
            checked { ++index2; }
          }
        }
        checked { ++index1; }
      }
      streamWriter.Flush();
      streamWriter.Close();
      fileStream.Close();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
  }

  public static void ReadAgdbComStruct()
  {
    string[] strArray1 = new string[3];
    string[] strArray2 = new string[10001];
    string empty = string.Empty;
    int num1 = 0;
    string path = "C:\\IPIS\\shared_info\\AgdbComMsgDb.txt";
    try
    {
      if (!File.Exists(path))
        return;
      FileStream fileStream = new FileStream(path, FileMode.Open, FileAccess.Read, FileShare.None);
      StreamReader streamReader = new StreamReader((Stream) fileStream);
      int index1 = 0;
      while (streamReader.Peek() >= 0)
      {
        strArray2[index1] = streamReader.ReadLine();
        string[] strArray3 = strArray2[index1].Split(';');
        int integer = Conversions.ToInteger(strArray3[0]);
        WriteDisplayMessageStruct.DecryptingTextFileData(strArray3[1], ref empty, Conversions.ToString(integer));
        strArray2[index1] = empty;
        checked { ++index1; }
      }
      fileStream.Close();
      streamReader.Close();
      int num2 = index1;
      if (num2 == 0)
        return;
      int index2 = 0;
      int index3 = 0;
      num1 = 0;
      while (index2 < (int) byte.MaxValue && Operators.CompareString(strArray2[index2], (string) null, false) != 0)
      {
        string[] strArray4 = strArray2[index3].Split(',');
        if (strArray4.Length == 2 && (double) index2 == Conversions.ToDouble(strArray4[0]))
        {
          taddb_msg.agdb_com_msg[index2].no_of_msgs = Conversions.ToInteger(strArray4[1]);
          int index4 = 0;
          checked { ++index3; }
          while (index4 < taddb_msg.agdb_com_msg[index2].no_of_msgs)
          {
            string[] strArray5 = strArray2[index3].Split(',');
            if (strArray5.Length == 9)
            {
              taddb_msg.agdb_com_msg[index2].tr_msg[index4].used = Conversions.ToBoolean(strArray5[0]);
              taddb_msg.agdb_com_msg[index2].tr_msg[index4].ar_time = strArray5[1];
              taddb_msg.agdb_com_msg[index2].tr_msg[index4].dp_time = strArray5[2];
              taddb_msg.agdb_com_msg[index2].tr_msg[index4].effect = Conversions.ToByte(strArray5[3]);
              taddb_msg.agdb_com_msg[index2].tr_msg[index4].platform_no = strArray5[4];
              taddb_msg.agdb_com_msg[index2].tr_msg[index4].status = strArray5[5];
              taddb_msg.agdb_com_msg[index2].tr_msg[index4].train_no = strArray5[6];
              taddb_msg.agdb_com_msg[index2].tr_msg[index4].def_msg_no = Conversions.ToInteger(strArray5[7]);
              checked { ++index3; }
            }
            checked { ++index4; }
          }
          if (index3 == num2)
            break;
        }
        checked { ++index2; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
  }

  public static void WriteCgdbStruct()
  {
    string empty1 = string.Empty;
    string path = "C:\\IPIS\\shared_info\\CgdbMsgDb.txt";
    try
    {
      FileStream fileStream = new FileStream(path, FileMode.Create, FileAccess.Write, FileShare.None);
      StreamWriter streamWriter = new StreamWriter((Stream) fileStream);
      string empty2 = string.Empty;
      int index = 0;
      while (index < (int) byte.MaxValue)
      {
        string data = "{cgdb_dis.cgdb_brd_msg[index].pfno},{cgdb_dis.cgdb_brd_msg[index].status},{cgdb_dis.cgdb_brd_msg[index].trainno},{Conversions.ToString(cgdb_dis.cgdb_brd_msg[index].used)}";
        string empty3 = string.Empty;
        WriteDisplayMessageStruct.EncryptingTextFileData(data, ref empty3);
        string str = "{Conversions.ToString(data.Length)};{empty3}";
        streamWriter.WriteLine(str);
        checked { ++index; }
      }
      streamWriter.Flush();
      streamWriter.Close();
      fileStream.Close();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
  }

  public static void ReadCgdbStruct()
  {
    int num = 0;
    string[] strArray1 = new string[3];
    string path = "C:\\IPIS\\shared_info\\CgdbMsgDb.txt";
    try
    {
      if (!File.Exists(path))
        return;
      FileStream fileStream = new FileStream(path, FileMode.Open, FileAccess.Read, FileShare.None);
      StreamReader streamReader = new StreamReader((Stream) fileStream);
      num = 0;
      string empty = string.Empty;
      int index = 0;
      while (streamReader.Peek() >= 0)
      {
        string[] strArray2 = streamReader.ReadLine().Split(';');
        int integer = Conversions.ToInteger(strArray2[0]);
        WriteDisplayMessageStruct.DecryptingTextFileData(strArray2[1], ref empty, Conversions.ToString(integer));
        string[] strArray3 = empty.Split(',');
        if (strArray3.Length == 4)
        {
          cgdb_dis.cgdb_brd_msg[index].pfno = strArray3[0];
          cgdb_dis.cgdb_brd_msg[index].status = strArray3[1];
          cgdb_dis.cgdb_brd_msg[index].trainno = strArray3[2];
          cgdb_dis.cgdb_brd_msg[index].used = Conversions.ToBoolean(strArray3[3]);
        }
        checked { ++index; }
      }
      fileStream.Close();
      streamReader.Close();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
  }

  public static void EncryptingTextFileData(string data, ref string encrypted)
  {
    try
    {
      int hashCode = data.GetHashCode();
      byte[] bytes = Encoding.BigEndianUnicode.GetBytes(data + Conversions.ToString(hashCode));
      encrypted = Convert.ToBase64String(bytes);
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      int num = (int) Interaction.MsgBox((object) ex.Message);
      ProjectData.ClearProjectError();
    }
  }

  public static void DecryptingTextFileData(
    string encrypted_data,
    ref string decrypted_data,
    string data_length)
  {
    try
    {
      byte[] bytes = Convert.FromBase64String(encrypted_data);
      decrypted_data = Encoding.BigEndianUnicode.GetString(bytes);
      decrypted_data = decrypted_data.Substring(0, Conversions.ToInteger(data_length));
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      int num = (int) Interaction.MsgBox((object) ex.Message);
      ProjectData.ClearProjectError();
    }
  }
}

}