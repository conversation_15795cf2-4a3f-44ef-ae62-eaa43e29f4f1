using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ipis_V2_jules.DisplayFormatters; // For AgdbDataFormatter, FormattedDisplayData
// Corrected using statement for DisplayPacketBuilder based on its new location
using ipis_V2_jules.Services.DisplayBoard.Hardware.Protocols;
using ipis_V2_jules.Services.DisplayBoard.Hardware.Communication; // For ICommunicationService
using ipis_V2_jules.Models; // For DisplayBoardConfig
using ipis_V2_jules.ApiClients; // For TrainDataErail

// Ensure AgdbDataFormatter is in the correct namespace, adjust if it was also moved to the new structure
// Assuming AgdbDataFormatter is in ipis_V2_jules.Services.DisplayBoard.DisplayFormatters (from Turn 49/50)

namespace ipis_V2_jules.Services.DisplayBoard.Hardware.Clients
{
    public class AgdbClient : IBoardClient
    {
        private readonly ICommunicationService _communicationService;
        private readonly AgdbDataFormatter _dataFormatter;
        private readonly DisplayBoardConfig _boardConfig;

        public BoardStatus Status { get; private set; }

        public AgdbClient(ICommunicationService communicationService,
                          AgdbDataFormatter dataFormatter, // Expecting the concrete AgdbDataFormatter
                          DisplayBoardConfig boardConfig)
        {
            _communicationService = communicationService ?? throw new ArgumentNullException(nameof(communicationService));
            _dataFormatter = dataFormatter ?? throw new ArgumentNullException(nameof(dataFormatter));
            _boardConfig = boardConfig ?? throw new ArgumentNullException(nameof(boardConfig));
            Status = new BoardStatus { IsLinkOk = false, StatusMessage = "Initialized", FirmwareVersion = "N/A" };
        }

        private Dictionary<string, string> GetBoardConfigAsDictionary()
        {
            return _boardConfig.ToDictionary();
        }

        // Generic SendMessageAsync from IBoardClient
        public async Task<bool> SendMessageAsync(FormattedDisplayData data, byte boardAddress, byte subAddress, byte serialNo)
        {
             if (boardAddress != _boardConfig.BoardId)
            {
                Console.WriteLine($"AGDB Client Error: Mismatched boardAddress ({boardAddress}) and configured BoardId ({_boardConfig.BoardId}).");
                Status = new BoardStatus { IsLinkOk = false, StatusMessage = "Configuration error: Board ID mismatch.", FirmwareVersion = Status.FirmwareVersion };
                return false;
            }
            // serialNo and subAddress are not explicitly used in this AGDB packet structure's main fields,
            // but could be part of data.AdditionalHeaderBytes if needed by specific AGDB protocols.
            // The AgdbDataFormatter should prepare AdditionalHeaderBytes correctly.

            Console.WriteLine($"AGDB Client: Sending pre-formatted message to BoardID: {boardAddress}");

            byte[] packet = DisplayPacketBuilder.BuildAgdbDisplayPacket(
                _boardConfig.BoardId,
                data.AdditionalHeaderBytes?.ToArray(),
                data.Line1,
                data.Line2,
                data.Line3
            );

            try
            {
                // Ensure port is open before writing
                // This logic might be better placed in a higher-level service or if ICommunicationService handles auto-open.
                if (!_communicationService.IsPortOpen)
                {
                    // Attempt to open port using settings from DisplayBoardConfig
                    // This assumes ICommunicationService was instantiated without auto-opening.
                    // If ICommunicationService is shared, this becomes more complex.
                    // For now, let's assume OpenPort() without args is not how it works,
                    // and it should be opened by whoever constructed the ICommunicationService.
                    // If _communicationService is specific to this client, it could be opened here.
                    Console.WriteLine($"AGDB Client Warning: Communication port for {_boardConfig.PortName} is not open. Attempting send may fail.");
                    // A better approach: the service using this client should ensure the port is open.
                }

                await _communicationService.WriteDataAsync(packet);
                Status = new BoardStatus { IsLinkOk = true, StatusMessage = "Message sent successfully.", FirmwareVersion = Status.FirmwareVersion };
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"AGDB Client Error: Failed to send message to BoardID {_boardConfig.BoardId}. Exception: {ex.Message}");
                Status = new BoardStatus { IsLinkOk = false, StatusMessage = $"Send failed: {ex.Message}", FirmwareVersion = Status.FirmwareVersion };
                return false;
            }
        }

        /// <summary>
        /// Sends a text message to the AGDB.
        /// </summary>
        public async Task<bool> SendMessageAsync(string message)
        {
            if (string.IsNullOrEmpty(message)) return false;

            Console.WriteLine($"AGDB Client: Formatting and sending message: \"{message.Substring(0, Math.Min(message.Length, 20))}...\" to BoardID: {_boardConfig.BoardId}");
            FormattedDisplayData formattedData = _dataFormatter.FormatMessage(message, GetBoardConfigAsDictionary());

            // Call the main SendMessageAsync with the formatted data
            return await SendMessageAsync(formattedData, _boardConfig.BoardId, 0, 0); // subAddress, serialNo might be part of AdditionalHeaderBytes
        }

        /// <summary>
        /// Updates the AGDB with train display information.
        /// </summary>
        public async Task<bool> UpdateTrainDisplayAsync(TrainDataErail trainData, Dictionary<string, string> platformInfo)
        {
            if (trainData == null) return false;

            Console.WriteLine($"AGDB Client: Formatting and sending train data for {trainData.TrainNo} to BoardID: {_boardConfig.BoardId}");
            FormattedDisplayData formattedData = _dataFormatter.FormatTrainData(trainData, platformInfo, GetBoardConfigAsDictionary());

            return await SendMessageAsync(formattedData, _boardConfig.BoardId, 0, 0);
        }

        // Specific ClearDisplayAsync for this client, using its own boardConfig
        public async Task<bool> ClearDisplayAsync() => await ClearDisplayAsync(_boardConfig.BoardId, 0, 0);

        // IBoardClient implementation
        public async Task<bool> ClearDisplayAsync(byte boardAddress, byte subAddress, byte serialNo)
        {
            if (boardAddress != _boardConfig.BoardId)
            {
                 Console.WriteLine($"AGDB Client Error: Mismatched boardAddress ({boardAddress}) for clear command. Expected {_boardConfig.BoardId}.");
                 Status = new BoardStatus { IsLinkOk = false, StatusMessage = "Configuration error: Board ID mismatch for clear.", FirmwareVersion = Status.FirmwareVersion };
                 return false;
            }
            Console.WriteLine($"AGDB Client: Clearing display for BoardID: {_boardConfig.BoardId}");
            byte[] packet = DisplayPacketBuilder.BuildAgdbClearScreenPacket(_boardConfig.BoardId);
            try
            {
                await _communicationService.WriteDataAsync(packet);
                Status = new BoardStatus { IsLinkOk = true, StatusMessage = "Display cleared.", FirmwareVersion = Status.FirmwareVersion };
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"AGDB Client Error: Failed to clear display for BoardID {_boardConfig.BoardId}. Exception: {ex.Message}");
                Status = new BoardStatus { IsLinkOk = false, StatusMessage = $"Clear failed: {ex.Message}", FirmwareVersion = Status.FirmVersion };
                return false;
            }
        }

        // Specific CheckLinkAsync for this client
        public async Task<BoardStatus> CheckLinkAsync() => await CheckLinkAsync(_boardConfig.BoardId, 0, 0);

        // IBoardClient implementation
        public async Task<BoardStatus> CheckLinkAsync(byte boardAddress, byte subAddress, byte serialNo)
        {
             if (boardAddress != _boardConfig.BoardId)
            {
                 Console.WriteLine($"AGDB Client Error: Mismatched boardAddress ({boardAddress}) for link check. Expected {_boardConfig.BoardId}.");
                 Status = new BoardStatus { IsLinkOk = false, StatusMessage = "Configuration error: Board ID mismatch for link check.", FirmwareVersion = Status.FirmwareVersion };
                 return Status;
            }
            Console.WriteLine($"AGDB Client: Checking link for BoardID: {_boardConfig.BoardId}");
            // Using generic BuildLinkCheckPacket. If AGDB needs a specific one, it should be added to DisplayPacketBuilder.
            byte[] packet = DisplayPacketBuilder.BuildLinkCheckPacket(_boardConfig.BoardId);

            try
            {
                await _communicationService.WriteDataAsync(packet);
                // TODO: Implement response reading if AGDB link check provides one.
                // byte[] response = await _communicationService.ReadDataAsync(expectedResponseLength, timeoutMs);
                // if (response indicates success) { Status.IsLinkOk = true; Status.StatusMessage = "Link OK"; }
                // else { Status.IsLinkOk = false; Status.StatusMessage = "Link check response indicates error or no response."; }
                Status = new BoardStatus { IsLinkOk = true, StatusMessage = "Link check sent (response check not implemented).", FirmwareVersion = Status.FirmwareVersion };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"AGDB Client Error: Failed to send link check to BoardID {_boardConfig.BoardId}. Exception: {ex.Message}");
                Status = new BoardStatus { IsLinkOk = false, StatusMessage = $"Link check send failed: {ex.Message}", FirmwareVersion = Status.FirmVersion };
            }
            return Status;
        }

        public async Task<bool> SetConfigurationAsync(byte boardAddress, byte subAddress, byte serialNo, byte[] configData)
        {
            Console.WriteLine($"AGDB Client: SetConfigurationAsync called for {boardAddress} - NOT IMPLEMENTED.");
            await Task.CompletedTask; // To make it awaitable
            Status = new BoardStatus { IsLinkOk = Status.IsLinkOk, StatusMessage = "SetConfiguration not implemented.", FirmwareVersion = Status.FirmwareVersion };
            return false;
        }

        public async Task<byte[]> GetConfigurationAsync(byte boardAddress, byte subAddress, byte serialNo)
        {
            Console.WriteLine($"AGDB Client: GetConfigurationAsync called for {boardAddress} - NOT IMPLEMENTED.");
            await Task.CompletedTask;
            Status = new BoardStatus { IsLinkOk = Status.IsLinkOk, StatusMessage = "GetConfiguration not implemented.", FirmwareVersion = Status.FirmwareVersion };
            return Array.Empty<byte>();
        }

        public async Task<bool> ResetBoardAsync(byte boardAddress, byte subAddress, byte serialNo)
        {
            Console.WriteLine($"AGDB Client: ResetBoardAsync called for {boardAddress} - NOT IMPLEMENTED.");
            await Task.CompletedTask;
            Status = new BoardStatus { IsLinkOk = Status.IsLinkOk, StatusMessage = "ResetBoard not implemented.", FirmwareVersion = Status.FirmVersion };
            return false;
        }
    }
}
