using System;
using System.Collections.Generic;

namespace IPIS.Models
{
    public class Role
    {
        public long Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public List<string> Permissions { get; set; } = new List<string>();

        public Role()
        {
            Permissions = new List<string>();
            CreatedAt = DateTime.Now;
            IsActive = true;
        }

        public bool HasPermission(string permission)
        {
            return Permissions.Contains(permission);
        }

        public void AddPermission(string permission)
        {
            if (!Permissions.Contains(permission))
            {
                Permissions.Add(permission);
            }
        }

        public void RemovePermission(string permission)
        {
            Permissions.Remove(permission);
        }

        public bool IsAdministrator()
        {
            return Name?.Equals("Administrator", StringComparison.OrdinalIgnoreCase) == true;
        }
    }
} 