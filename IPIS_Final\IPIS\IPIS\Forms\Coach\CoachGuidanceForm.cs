using System;
using System.Windows.Forms;
using System.Drawing;
using IPIS.Utils;

namespace IPIS.Forms.Coach
{
    public partial class CoachGuidanceForm : Form
    {
        private DataGridView coachGrid;
        private Panel rightSidebar;
        private Button btnAddCoach;
        private Button btnEditCoach;
        private Button btnDeleteCoach;
        private TextBox txtSearch;
        private Label lblTitle;

        public CoachGuidanceForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "Coach Guidance";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;

            // Title
            lblTitle = new Label
            {
                Text = "Coach Guidance System",
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                Location = new Point(20, 20),
                AutoSize = true
            };

            // Search box
            txtSearch = new TextBox
            {
                Location = new Point(20, 60),
                Size = new Size(200, 23),
                PlaceholderText = "Search coaches..."
            };

            // Main grid
            coachGrid = new DataGridView
            {
                Location = new Point(20, 100),
                Size = new Size(800, 600),
                AllowUserToAddRows = false,
                RowHeadersVisible = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                Font = new Font("Segoe UI", 10)
            };

            // Add columns
            coachGrid.Columns.Add(new DataGridViewTextBoxColumn { HeaderText = "Coach Number", Name = "CoachNumber", Width = 100 });
            coachGrid.Columns.Add(new DataGridViewTextBoxColumn { HeaderText = "Type", Name = "Type", Width = 100 });
            coachGrid.Columns.Add(new DataGridViewTextBoxColumn { HeaderText = "Platform", Name = "Platform", Width = 80 });
            coachGrid.Columns.Add(new DataGridViewTextBoxColumn { HeaderText = "Position", Name = "Position", Width = 80 });
            coachGrid.Columns.Add(new DataGridViewTextBoxColumn { HeaderText = "Status", Name = "Status", Width = 120 });
            coachGrid.Columns.Add(new DataGridViewTextBoxColumn { HeaderText = "Last Updated", Name = "LastUpdated", Width = 150 });

            // Right sidebar
            rightSidebar = new Panel
            {
                Location = new Point(840, 100),
                Size = new Size(300, 600),
                BackColor = Color.FromArgb(240, 240, 240)
            };

            // Add Coach button
            btnAddCoach = new Button
            {
                Text = "Add Coach",
                Location = new Point(20, 20),
                Size = new Size(260, 40)
            };
            ButtonStyler.ApplyStandardStyle(btnAddCoach, "primary");
            btnAddCoach.Click += BtnAddCoach_Click;

            // Edit Coach button
            btnEditCoach = new Button
            {
                Text = "Edit Coach",
                Location = new Point(20, 70),
                Size = new Size(260, 40)
            };
            ButtonStyler.ApplyStandardStyle(btnEditCoach, "secondary");
            btnEditCoach.Click += BtnEditCoach_Click;

            // Delete Coach button
            btnDeleteCoach = new Button
            {
                Text = "Delete Coach",
                Location = new Point(20, 120),
                Size = new Size(260, 40)
            };
            ButtonStyler.ApplyStandardStyle(btnDeleteCoach, "danger");
            btnDeleteCoach.Click += BtnDeleteCoach_Click;

            // Add controls to sidebar
            rightSidebar.Controls.AddRange(new Control[] {
                btnAddCoach,
                btnEditCoach,
                btnDeleteCoach
            });

            // Add controls to form
            this.Controls.AddRange(new Control[] {
                lblTitle,
                txtSearch,
                coachGrid,
                rightSidebar
            });

            // Add some sample data
            AddSampleData();
        }

        private void AddSampleData()
        {
            coachGrid.Rows.Add("A1", "AC First Class", "1", "Front", "In Service", DateTime.Now.ToString());
            coachGrid.Rows.Add("B1", "AC 2 Tier", "1", "Middle", "In Service", DateTime.Now.ToString());
            coachGrid.Rows.Add("C1", "AC 3 Tier", "1", "Rear", "In Service", DateTime.Now.ToString());
            coachGrid.Rows.Add("S1", "Sleeper", "2", "Front", "In Service", DateTime.Now.ToString());
            coachGrid.Rows.Add("S2", "Sleeper", "2", "Middle", "Maintenance", DateTime.Now.ToString());
        }

        private void BtnAddCoach_Click(object sender, EventArgs e)
        {
            MessageBox.Show("Add Coach functionality will be implemented later.", "Demo", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void BtnEditCoach_Click(object sender, EventArgs e)
        {
            if (coachGrid.SelectedRows.Count > 0)
            {
                MessageBox.Show($"Edit Coach {coachGrid.SelectedRows[0].Cells["CoachNumber"].Value} functionality will be implemented later.", "Demo", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            else
            {
                MessageBox.Show("Please select a coach to edit.", "Demo", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void BtnDeleteCoach_Click(object sender, EventArgs e)
        {
            if (coachGrid.SelectedRows.Count > 0)
            {
                if (MessageBox.Show($"Are you sure you want to delete Coach {coachGrid.SelectedRows[0].Cells["CoachNumber"].Value}?", "Confirm Delete", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                {
                    coachGrid.Rows.Remove(coachGrid.SelectedRows[0]);
                }
            }
            else
            {
                MessageBox.Show("Please select a coach to delete.", "Demo", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }
    }
} 