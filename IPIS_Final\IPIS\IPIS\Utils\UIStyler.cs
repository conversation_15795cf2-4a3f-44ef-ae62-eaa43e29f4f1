using System.Drawing;
using System.Windows.Forms;

namespace IPIS.Utils
{
    public static class UIStyler
    {
        // Standard fonts for different UI elements
        public static class Fonts
        {
            public static readonly Font Heading1 = new Font("Segoe UI", 24, FontStyle.Bold);
            public static readonly Font Heading2 = new Font("Segoe UI", 20, FontStyle.Bold);
            public static readonly Font Heading3 = new Font("Segoe UI", 16, FontStyle.Bold);
            public static readonly Font Heading4 = new Font("Segoe UI", 14, FontStyle.Bold);
            public static readonly Font Body = new Font("Segoe UI", 10, FontStyle.Regular);
            public static readonly Font BodyBold = new Font("Segoe UI", 10, FontStyle.Bold);
            public static readonly Font Small = new Font("Segoe UI", 9, FontStyle.Regular);
            public static readonly Font Caption = new Font("Segoe UI", 8, FontStyle.Regular);
        }

        // Standard colors
        public static class Colors
        {
            public static readonly Color Primary = Color.FromArgb(0, 123, 255);
            public static readonly Color Secondary = Color.FromArgb(108, 117, 125);
            public static readonly Color Success = Color.FromArgb(40, 167, 69);
            public static readonly Color Danger = Color.FromArgb(220, 53, 69);
            public static readonly Color Warning = Color.FromArgb(255, 193, 7);
            public static readonly Color Info = Color.FromArgb(23, 162, 184);
            public static readonly Color Light = Color.FromArgb(248, 249, 250);
            public static readonly Color Dark = Color.FromArgb(52, 58, 64);
            public static readonly Color Muted = Color.FromArgb(108, 117, 125);
            public static readonly Color Border = Color.FromArgb(222, 226, 230);
            public static readonly Color Background = Color.FromArgb(248, 249, 250);
            public static readonly Color TextPrimary = Color.FromArgb(33, 37, 41);
            public static readonly Color TextSecondary = Color.FromArgb(108, 117, 125);
            public static readonly Color TextMuted = Color.FromArgb(108, 117, 125);
        }

        // Standard sizes
        public static class Sizes
        {
            public static readonly Size TextBoxSmall = new Size(120, 28);
            public static readonly Size TextBoxMedium = new Size(200, 32);
            public static readonly Size TextBoxLarge = new Size(300, 36);
            public static readonly Size ComboBoxSmall = new Size(120, 28);
            public static readonly Size ComboBoxMedium = new Size(200, 32);
            public static readonly Size ComboBoxLarge = new Size(300, 36);
        }

        // Label styling
        public static void ApplyLabelStyle(Label label, string type = "body")
        {
            switch (type.ToLower())
            {
                case "h1":
                    label.Font = Fonts.Heading1;
                    label.ForeColor = Colors.TextPrimary;
                    break;
                case "h2":
                    label.Font = Fonts.Heading2;
                    label.ForeColor = Colors.TextPrimary;
                    break;
                case "h3":
                    label.Font = Fonts.Heading3;
                    label.ForeColor = Colors.TextPrimary;
                    break;
                case "h4":
                    label.Font = Fonts.Heading4;
                    label.ForeColor = Colors.TextPrimary;
                    break;
                case "body":
                    label.Font = Fonts.Body;
                    label.ForeColor = Colors.TextPrimary;
                    break;
                case "body-bold":
                    label.Font = Fonts.BodyBold;
                    label.ForeColor = Colors.TextPrimary;
                    break;
                case "small":
                    label.Font = Fonts.Small;
                    label.ForeColor = Colors.TextSecondary;
                    break;
                case "caption":
                    label.Font = Fonts.Caption;
                    label.ForeColor = Colors.TextMuted;
                    break;
                case "success":
                    label.Font = Fonts.Body;
                    label.ForeColor = Colors.Success;
                    break;
                case "danger":
                    label.Font = Fonts.Body;
                    label.ForeColor = Colors.Danger;
                    break;
                case "warning":
                    label.Font = Fonts.Body;
                    label.ForeColor = Colors.Warning;
                    break;
                case "info":
                    label.Font = Fonts.Body;
                    label.ForeColor = Colors.Info;
                    break;
                default:
                    label.Font = Fonts.Body;
                    label.ForeColor = Colors.TextPrimary;
                    break;
            }

            label.Margin = new Padding(4);
            label.AutoSize = true;
        }

        // TextBox styling
        public static void ApplyTextBoxStyle(TextBox textBox, string size = "medium")
        {
            textBox.Font = Fonts.Body;
            textBox.BorderStyle = BorderStyle.FixedSingle;
            textBox.BackColor = Color.White;
            textBox.ForeColor = Colors.TextPrimary;
            textBox.Padding = new Padding(6, 4, 6, 4);
            textBox.Margin = new Padding(4);

            switch (size.ToLower())
            {
                case "small":
                    textBox.Size = Sizes.TextBoxSmall;
                    break;
                case "large":
                    textBox.Size = Sizes.TextBoxLarge;
                    break;
                default: // medium
                    textBox.Size = Sizes.TextBoxMedium;
                    break;
            }
        }

        // ComboBox styling
        public static void ApplyComboBoxStyle(ComboBox comboBox, string size = "medium")
        {
            comboBox.Font = Fonts.Body;
            comboBox.BackColor = Color.White;
            comboBox.ForeColor = Colors.TextPrimary;
            comboBox.FlatStyle = FlatStyle.Flat;
            comboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            comboBox.Margin = new Padding(4);

            switch (size.ToLower())
            {
                case "small":
                    comboBox.Size = Sizes.ComboBoxSmall;
                    break;
                case "large":
                    comboBox.Size = Sizes.ComboBoxLarge;
                    break;
                default: // medium
                    comboBox.Size = Sizes.ComboBoxMedium;
                    break;
            }
        }

        // NumericUpDown styling
        public static void ApplyNumericUpDownStyle(NumericUpDown numericUpDown, string size = "medium")
        {
            numericUpDown.Font = Fonts.Body;
            numericUpDown.BackColor = Color.White;
            numericUpDown.ForeColor = Colors.TextPrimary;
            numericUpDown.BorderStyle = BorderStyle.FixedSingle;
            numericUpDown.TextAlign = HorizontalAlignment.Center;
            numericUpDown.Margin = new Padding(4);

            switch (size.ToLower())
            {
                case "small":
                    numericUpDown.Size = Sizes.TextBoxSmall;
                    break;
                case "large":
                    numericUpDown.Size = Sizes.TextBoxLarge;
                    break;
                case "custom":
                    break;
                default: // medium
                    numericUpDown.Size = Sizes.TextBoxMedium;
                    break;
            }
        }

        // CheckBox styling
        public static void ApplyCheckBoxStyle(CheckBox checkBox)
        {
            checkBox.Font = Fonts.Body;
            checkBox.ForeColor = Colors.TextPrimary;
            checkBox.Margin = new Padding(4);
            checkBox.AutoSize = false;
        }

        // RadioButton styling
        public static void ApplyRadioButtonStyle(RadioButton radioButton)
        {
            radioButton.Font = Fonts.Body;
            radioButton.ForeColor = Colors.TextPrimary;
            radioButton.Margin = new Padding(4);
            radioButton.AutoSize = true;
        }

        // GroupBox styling
        public static void ApplyGroupBoxStyle(GroupBox groupBox)
        {
            groupBox.Font = Fonts.BodyBold;
            groupBox.ForeColor = Colors.TextPrimary;
            groupBox.Padding = new Padding(10);
            groupBox.Margin = new Padding(8);
        }

        // DataGridView styling
        public static void ApplyDataGridViewStyle(DataGridView dataGridView)
        {
            dataGridView.Font = Fonts.Body;
            dataGridView.BackgroundColor = Color.White;
            dataGridView.BorderStyle = BorderStyle.None;
            dataGridView.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;
            dataGridView.GridColor = Colors.Border;
            dataGridView.RowHeadersVisible = false;
            dataGridView.AllowUserToAddRows = false;
            dataGridView.AllowUserToDeleteRows = false;
            dataGridView.ReadOnly = false;
            dataGridView.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dataGridView.MultiSelect = false;
            dataGridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            dataGridView.ColumnHeadersDefaultCellStyle.Font = Fonts.BodyBold;
            dataGridView.ColumnHeadersDefaultCellStyle.BackColor = Colors.Background;
            dataGridView.ColumnHeadersDefaultCellStyle.ForeColor = Colors.TextPrimary;
            dataGridView.ColumnHeadersDefaultCellStyle.SelectionBackColor = Colors.Background;
            dataGridView.ColumnHeadersDefaultCellStyle.SelectionForeColor = Colors.TextPrimary;
            dataGridView.DefaultCellStyle.Font = Fonts.Body;
            dataGridView.DefaultCellStyle.BackColor = Color.White;
            dataGridView.DefaultCellStyle.ForeColor = Colors.TextPrimary;
            dataGridView.DefaultCellStyle.SelectionBackColor = Colors.Primary;
            dataGridView.DefaultCellStyle.SelectionForeColor = Color.White;
            dataGridView.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(248, 249, 250);
        }

        // Panel styling
        public static void ApplyPanelStyle(Panel panel, string type = "default")
        {
            switch (type.ToLower())
            {
                case "card":
                    panel.BackColor = Color.White;
                    panel.BorderStyle = BorderStyle.FixedSingle;
                    panel.Padding = new Padding(16, 8, 16, 8);
                    panel.Margin = new Padding(8);
                    panel.AutoSize = true;
                    break;
                case "section":
                    panel.BackColor = Colors.Background;
                    panel.Padding = new Padding(12);
                    panel.Margin = new Padding(4);
                    break;
                default:
                    panel.BackColor = Color.Transparent;
                    panel.Padding = new Padding(8);
                    panel.Margin = new Padding(4);
                    break;
            }
        }

        // StatusStrip styling
        public static void ApplyStatusStripStyle(StatusStrip statusStrip)
        {
            statusStrip.BackColor = Colors.Background;
            statusStrip.ForeColor = Colors.TextSecondary;
            statusStrip.Font = Fonts.Small;
        }

        // MenuStrip styling
        public static void ApplyMenuStripStyle(MenuStrip menuStrip)
        {
            menuStrip.BackColor = Color.White;
            menuStrip.ForeColor = Colors.TextPrimary;
            menuStrip.Font = Fonts.Body;
        }

        // ToolStrip styling
        public static void ApplyToolStripStyle(ToolStrip toolStrip)
        {
            toolStrip.BackColor = Color.White;
            toolStrip.ForeColor = Colors.TextPrimary;
            toolStrip.Font = Fonts.Body;
        }
    }
} 
