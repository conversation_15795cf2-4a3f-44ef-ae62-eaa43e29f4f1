// Decompiled with JetBrains decompiler
// Type: ipis.frmTrainConfig
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using ipis.My;
using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

namespace ipis
{

[DesignerGenerated]
public class frmTrainConfig : Form
{
  private static List<WeakReference> __ENCList = new List<WeakReference>();
  private IContainer components;
  [AccessedThroughProperty("cmbDir")]
  private ComboBox _cmbDir;
  [AccessedThroughProperty("lblDir")]
  private Label _lblDir;
  [AccessedThroughProperty("lblEnglish")]
  private Label _lblEnglish;
  [AccessedThroughProperty("txtTrainNameReg")]
  private TextBox _txtTrainNameReg;
  [AccessedThroughProperty("lblHindi")]
  private Label _lblHindi;
  [AccessedThroughProperty("txtTrainNameHin")]
  private TextBox _txtTrainNameHin;
  [AccessedThroughProperty("lblReg")]
  private Label _lblReg;
  [AccessedThroughProperty("lblPfno")]
  private Label _lblPfno;
  [AccessedThroughProperty("lblDepTime")]
  private Label _lblDepTime;
  [AccessedThroughProperty("lblArrTime")]
  private Label _lblArrTime;
  [AccessedThroughProperty("lblEndStn")]
  private Label _lblEndStn;
  [AccessedThroughProperty("lblSrtStn")]
  private Label _lblSrtStn;
  [AccessedThroughProperty("lblTrainName")]
  private Label _lblTrainName;
  [AccessedThroughProperty("btnAdd")]
  private Button _btnAdd;
  [AccessedThroughProperty("btnDelete")]
  private Button _btnDelete;
  [AccessedThroughProperty("btnEdit")]
  private Button _btnEdit;
  [AccessedThroughProperty("btnExit")]
  private Button _btnExit;
  [AccessedThroughProperty("btnsave")]
  private Button _btnsave;
  [AccessedThroughProperty("cmbTrainNo")]
  private ComboBox _cmbTrainNo;
  [AccessedThroughProperty("btnCgs")]
  private Button _btnCgs;
  [AccessedThroughProperty("btnTrainDetails")]
  private Button _btnTrainDetails;
  [AccessedThroughProperty("cmbDepTime")]
  private ComboBox _cmbDepTime;
  [AccessedThroughProperty("cmbArrTime")]
  private ComboBox _cmbArrTime;
  [AccessedThroughProperty("txtEndStn")]
  private TextBox _txtEndStn;
  [AccessedThroughProperty("txtStartStn")]
  private TextBox _txtStartStn;
  [AccessedThroughProperty("txtTrainName")]
  private TextBox _txtTrainName;
  [AccessedThroughProperty("lblTrainNo")]
  private Label _lblTrainNo;
  [AccessedThroughProperty("TrainconfigtableBindingSource")]
  private BindingSource _TrainconfigtableBindingSource;
  [AccessedThroughProperty("btnCancel")]
  private Button _btnCancel;
  [AccessedThroughProperty("cmbPfno")]
  private ComboBox _cmbPfno;
  [AccessedThroughProperty("lblStationPos")]
  private Label _lblStationPos;
  [AccessedThroughProperty("cmbStationPos")]
  private ComboBox _cmbStationPos;
  [AccessedThroughProperty("txtRegLang")]
  private TextBox _txtRegLang;
  private frmTrainDetails myform;
  public static bool train_details_enter = false;
  public static bool cgs_form_enter = false;
  public static int send_rowno_train_details;
  public static int send_rowno_cgs;
  private frmCgs cgsfrm;
  [AccessedThroughProperty("event_traindetails")]
  private frmTrainDetails _event_traindetails;
  [AccessedThroughProperty("event_cgs")]
  private frmCgs _event_cgs;

  public frmTrainConfig()
  {
    this.Load += new EventHandler(this.addtrain_Load);
    this.FormClosed += new FormClosedEventHandler(this.frmTrainConfig_FormClosed);
    frmTrainConfig.__ENCAddToList((object) this);
    this.myform = new frmTrainDetails();
    this.cgsfrm = new frmCgs();
    this.InitializeComponent();
  }

  [DebuggerNonUserCode]
  private static void __ENCAddToList(object value)
  {
    lock (frmTrainConfig.__ENCList)
    {
      if (frmTrainConfig.__ENCList.Count == frmTrainConfig.__ENCList.Capacity)
      {
        int index1 = 0;
        int num = checked (frmTrainConfig.__ENCList.Count - 1);
        int index2 = 0;
        while (index2 <= num)
        {
          if (frmTrainConfig.__ENCList[index2].IsAlive)
          {
            if (index2 != index1)
              frmTrainConfig.__ENCList[index1] = frmTrainConfig.__ENCList[index2];
            checked { ++index1; }
          }
          checked { ++index2; }
        }
        frmTrainConfig.__ENCList.RemoveRange(index1, checked (frmTrainConfig.__ENCList.Count - index1));
        frmTrainConfig.__ENCList.Capacity = frmTrainConfig.__ENCList.Count;
      }
      frmTrainConfig.__ENCList.Add(new WeakReference(RuntimeHelpers.GetObjectValue(value)));
    }
  }

  [DebuggerNonUserCode]
  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  [DebuggerStepThrough]
  private void InitializeComponent()
  {
    this.components = (IContainer) new System.ComponentModel.Container();
    this.cmbDir = new ComboBox();
    this.lblDir = new Label();
    this.lblEnglish = new Label();
    this.txtTrainNameReg = new TextBox();
    this.lblHindi = new Label();
    this.txtTrainNameHin = new TextBox();
    this.lblReg = new Label();
    this.lblPfno = new Label();
    this.lblDepTime = new Label();
    this.lblArrTime = new Label();
    this.lblEndStn = new Label();
    this.lblSrtStn = new Label();
    this.lblTrainName = new Label();
    this.btnAdd = new Button();
    this.btnDelete = new Button();
    this.btnEdit = new Button();
    this.btnExit = new Button();
    this.btnsave = new Button();
    this.cmbTrainNo = new ComboBox();
    this.TrainconfigtableBindingSource = new BindingSource(this.components);
    this.btnCgs = new Button();
    this.btnTrainDetails = new Button();
    this.cmbDepTime = new ComboBox();
    this.cmbArrTime = new ComboBox();
    this.txtEndStn = new TextBox();
    this.txtStartStn = new TextBox();
    this.txtTrainName = new TextBox();
    this.lblTrainNo = new Label();
    this.btnCancel = new Button();
    this.cmbPfno = new ComboBox();
    this.lblStationPos = new Label();
    this.cmbStationPos = new ComboBox();
    this.txtRegLang = new TextBox();
    ((ISupportInitialize) this.TrainconfigtableBindingSource).BeginInit();
    this.SuspendLayout();
    this.cmbDir.Enabled = false;
    this.cmbDir.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.cmbDir.FormattingEnabled = true;
    this.cmbDir.Items.AddRange(new object[2]
    {
      (object) "UP",
      (object) "DOWN"
    });
    ComboBox cmbDir1 = this.cmbDir;
    Point point1 = new Point(658, 378);
    Point point2 = point1;
    cmbDir1.Location = point2;
    ComboBox cmbDir2 = this.cmbDir;
    Padding padding1 = new Padding(4);
    Padding padding2 = padding1;
    cmbDir2.Margin = padding2;
    this.cmbDir.Name = "cmbDir";
    ComboBox cmbDir3 = this.cmbDir;
    Size size1 = new Size(102, 24);
    Size size2 = size1;
    cmbDir3.Size = size2;
    this.cmbDir.TabIndex = 11;
    this.lblDir.AutoSize = true;
    this.lblDir.Enabled = false;
    this.lblDir.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblDir1 = this.lblDir;
    point1 = new Point(562, 379);
    Point point3 = point1;
    lblDir1.Location = point3;
    Label lblDir2 = this.lblDir;
    padding1 = new Padding(4, 0, 4, 0);
    Padding padding3 = padding1;
    lblDir2.Margin = padding3;
    this.lblDir.Name = "lblDir";
    Label lblDir3 = this.lblDir;
    size1 = new Size(70, 16 /*0x10*/);
    Size size3 = size1;
    lblDir3.Size = size3;
    this.lblDir.TabIndex = 83;
    this.lblDir.Text = "Direction\r\n";
    this.lblEnglish.AutoSize = true;
    this.lblEnglish.Enabled = false;
    this.lblEnglish.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblEnglish1 = this.lblEnglish;
    point1 = new Point(404, 56);
    Point point4 = point1;
    lblEnglish1.Location = point4;
    Label lblEnglish2 = this.lblEnglish;
    padding1 = new Padding(4, 0, 4, 0);
    Padding padding4 = padding1;
    lblEnglish2.Margin = padding4;
    this.lblEnglish.Name = "lblEnglish";
    Label lblEnglish3 = this.lblEnglish;
    size1 = new Size(59, 16 /*0x10*/);
    Size size4 = size1;
    lblEnglish3.Size = size4;
    this.lblEnglish.TabIndex = 82;
    this.lblEnglish.Text = "English";
    this.lblEnglish.TextAlign = ContentAlignment.TopRight;
    this.txtTrainNameReg.Enabled = false;
    this.txtTrainNameReg.Font = new Font("Microsoft Sans Serif", 14.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtTrainNameReg1 = this.txtTrainNameReg;
    point1 = new Point(492, 156);
    Point point5 = point1;
    txtTrainNameReg1.Location = point5;
    TextBox txtTrainNameReg2 = this.txtTrainNameReg;
    padding1 = new Padding(4);
    Padding padding5 = padding1;
    txtTrainNameReg2.Margin = padding5;
    this.txtTrainNameReg.MaxLength = 25;
    this.txtTrainNameReg.Name = "txtTrainNameReg";
    TextBox txtTrainNameReg3 = this.txtTrainNameReg;
    size1 = new Size(248, 29);
    Size size5 = size1;
    txtTrainNameReg3.Size = size5;
    this.txtTrainNameReg.TabIndex = 4;
    this.lblHindi.AutoSize = true;
    this.lblHindi.Enabled = false;
    this.lblHindi.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblHindi1 = this.lblHindi;
    point1 = new Point(422, 103);
    Point point6 = point1;
    lblHindi1.Location = point6;
    Label lblHindi2 = this.lblHindi;
    padding1 = new Padding(4, 0, 4, 0);
    Padding padding6 = padding1;
    lblHindi2.Margin = padding6;
    this.lblHindi.Name = "lblHindi";
    Label lblHindi3 = this.lblHindi;
    size1 = new Size(44, 16 /*0x10*/);
    Size size6 = size1;
    lblHindi3.Size = size6;
    this.lblHindi.TabIndex = 79;
    this.lblHindi.Text = "Hindi";
    this.lblHindi.TextAlign = ContentAlignment.TopRight;
    this.txtTrainNameHin.Enabled = false;
    this.txtTrainNameHin.Font = new Font("Microsoft Sans Serif", 14.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtTrainNameHin1 = this.txtTrainNameHin;
    point1 = new Point(492, 98);
    Point point7 = point1;
    txtTrainNameHin1.Location = point7;
    TextBox txtTrainNameHin2 = this.txtTrainNameHin;
    padding1 = new Padding(4);
    Padding padding7 = padding1;
    txtTrainNameHin2.Margin = padding7;
    this.txtTrainNameHin.MaxLength = 25;
    this.txtTrainNameHin.Name = "txtTrainNameHin";
    TextBox txtTrainNameHin3 = this.txtTrainNameHin;
    size1 = new Size(248, 29);
    Size size7 = size1;
    txtTrainNameHin3.Size = size7;
    this.txtTrainNameHin.TabIndex = 3;
    this.lblReg.AutoSize = true;
    this.lblReg.Enabled = false;
    this.lblReg.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblReg1 = this.lblReg;
    point1 = new Point(391, 160 /*0xA0*/);
    Point point8 = point1;
    lblReg1.Location = point8;
    Label lblReg2 = this.lblReg;
    padding1 = new Padding(4, 0, 4, 0);
    Padding padding8 = padding1;
    lblReg2.Margin = padding8;
    this.lblReg.Name = "lblReg";
    Label lblReg3 = this.lblReg;
    size1 = new Size(71, 16 /*0x10*/);
    Size size8 = size1;
    lblReg3.Size = size8;
    this.lblReg.TabIndex = 77;
    this.lblReg.Text = "Regional";
    this.lblReg.TextAlign = ContentAlignment.TopRight;
    this.lblPfno.AutoSize = true;
    this.lblPfno.Enabled = false;
    this.lblPfno.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblPfno1 = this.lblPfno;
    point1 = new Point(22, 379);
    Point point9 = point1;
    lblPfno1.Location = point9;
    Label lblPfno2 = this.lblPfno;
    padding1 = new Padding(4, 0, 4, 0);
    Padding padding9 = padding1;
    lblPfno2.Margin = padding9;
    this.lblPfno.Name = "lblPfno";
    Label lblPfno3 = this.lblPfno;
    size1 = new Size(123, 16 /*0x10*/);
    Size size9 = size1;
    lblPfno3.Size = size9;
    this.lblPfno.TabIndex = 76;
    this.lblPfno.Text = "Platform Number";
    this.lblDepTime.AutoSize = true;
    this.lblDepTime.Enabled = false;
    this.lblDepTime.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblDepTime1 = this.lblDepTime;
    point1 = new Point(516, 310);
    Point point10 = point1;
    lblDepTime1.Location = point10;
    Label lblDepTime2 = this.lblDepTime;
    padding1 = new Padding(4, 0, 4, 0);
    Padding padding10 = padding1;
    lblDepTime2.Margin = padding10;
    this.lblDepTime.Name = "lblDepTime";
    Label lblDepTime3 = this.lblDepTime;
    size1 = new Size(116, 16 /*0x10*/);
    Size size10 = size1;
    lblDepTime3.Size = size10;
    this.lblDepTime.TabIndex = 75;
    this.lblDepTime.Text = "Departure Time";
    this.lblArrTime.AutoSize = true;
    this.lblArrTime.Enabled = false;
    this.lblArrTime.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblArrTime1 = this.lblArrTime;
    point1 = new Point(540, 242);
    Point point11 = point1;
    lblArrTime1.Location = point11;
    Label lblArrTime2 = this.lblArrTime;
    padding1 = new Padding(4, 0, 4, 0);
    Padding padding11 = padding1;
    lblArrTime2.Margin = padding11;
    this.lblArrTime.Name = "lblArrTime";
    Label lblArrTime3 = this.lblArrTime;
    size1 = new Size(92, 16 /*0x10*/);
    Size size11 = size1;
    lblArrTime3.Size = size11;
    this.lblArrTime.TabIndex = 74;
    this.lblArrTime.Text = "Arrival Time";
    this.lblEndStn.AutoSize = true;
    this.lblEndStn.Enabled = false;
    this.lblEndStn.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblEndStn1 = this.lblEndStn;
    point1 = new Point(58, 306);
    Point point12 = point1;
    lblEndStn1.Location = point12;
    Label lblEndStn2 = this.lblEndStn;
    padding1 = new Padding(4, 0, 4, 0);
    Padding padding12 = padding1;
    lblEndStn2.Margin = padding12;
    this.lblEndStn.Name = "lblEndStn";
    Label lblEndStn3 = this.lblEndStn;
    size1 = new Size(87, 16 /*0x10*/);
    Size size12 = size1;
    lblEndStn3.Size = size12;
    this.lblEndStn.TabIndex = 73;
    this.lblEndStn.Text = "End Station";
    this.lblSrtStn.AutoSize = true;
    this.lblSrtStn.Enabled = false;
    this.lblSrtStn.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblSrtStn1 = this.lblSrtStn;
    point1 = new Point(53, 242);
    Point point13 = point1;
    lblSrtStn1.Location = point13;
    Label lblSrtStn2 = this.lblSrtStn;
    padding1 = new Padding(4, 0, 4, 0);
    Padding padding13 = padding1;
    lblSrtStn2.Margin = padding13;
    this.lblSrtStn.Name = "lblSrtStn";
    Label lblSrtStn3 = this.lblSrtStn;
    size1 = new Size(92, 16 /*0x10*/);
    Size size13 = size1;
    lblSrtStn3.Size = size13;
    this.lblSrtStn.TabIndex = 72;
    this.lblSrtStn.Text = "Start Station";
    this.lblTrainName.AutoSize = true;
    this.lblTrainName.Enabled = false;
    this.lblTrainName.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblTrainName1 = this.lblTrainName;
    point1 = new Point(564, 22);
    Point point14 = point1;
    lblTrainName1.Location = point14;
    Label lblTrainName2 = this.lblTrainName;
    padding1 = new Padding(4, 0, 4, 0);
    Padding padding14 = padding1;
    lblTrainName2.Margin = padding14;
    this.lblTrainName.Name = "lblTrainName";
    Label lblTrainName3 = this.lblTrainName;
    size1 = new Size(93, 16 /*0x10*/);
    Size size14 = size1;
    lblTrainName3.Size = size14;
    this.lblTrainName.TabIndex = 71;
    this.lblTrainName.Text = "Train Name ";
    this.lblTrainName.TextAlign = ContentAlignment.TopRight;
    this.btnAdd.BackColor = SystemColors.ButtonFace;
    this.btnAdd.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnAdd1 = this.btnAdd;
    point1 = new Point(100, 585);
    Point point15 = point1;
    btnAdd1.Location = point15;
    Button btnAdd2 = this.btnAdd;
    padding1 = new Padding(4);
    Padding padding15 = padding1;
    btnAdd2.Margin = padding15;
    this.btnAdd.Name = "btnAdd";
    Button btnAdd3 = this.btnAdd;
    size1 = new Size(63 /*0x3F*/, 31 /*0x1F*/);
    Size size15 = size1;
    btnAdd3.Size = size15;
    this.btnAdd.TabIndex = 14;
    this.btnAdd.Text = "&Add";
    this.btnAdd.UseVisualStyleBackColor = false;
    this.btnDelete.BackColor = SystemColors.ButtonFace;
    this.btnDelete.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnDelete1 = this.btnDelete;
    point1 = new Point(403, 585);
    Point point16 = point1;
    btnDelete1.Location = point16;
    Button btnDelete2 = this.btnDelete;
    padding1 = new Padding(4);
    Padding padding16 = padding1;
    btnDelete2.Margin = padding16;
    this.btnDelete.Name = "btnDelete";
    Button btnDelete3 = this.btnDelete;
    size1 = new Size(63 /*0x3F*/, 31 /*0x1F*/);
    Size size16 = size1;
    btnDelete3.Size = size16;
    this.btnDelete.TabIndex = 17;
    this.btnDelete.Text = "&Delete";
    this.btnDelete.UseVisualStyleBackColor = false;
    this.btnEdit.BackColor = SystemColors.ButtonFace;
    this.btnEdit.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnEdit1 = this.btnEdit;
    point1 = new Point(204, 585);
    Point point17 = point1;
    btnEdit1.Location = point17;
    Button btnEdit2 = this.btnEdit;
    padding1 = new Padding(4);
    Padding padding17 = padding1;
    btnEdit2.Margin = padding17;
    this.btnEdit.Name = "btnEdit";
    Button btnEdit3 = this.btnEdit;
    size1 = new Size(63 /*0x3F*/, 31 /*0x1F*/);
    Size size17 = size1;
    btnEdit3.Size = size17;
    this.btnEdit.TabIndex = 15;
    this.btnEdit.Text = "&Edit";
    this.btnEdit.UseVisualStyleBackColor = false;
    this.btnExit.BackColor = SystemColors.ButtonFace;
    this.btnExit.DialogResult = DialogResult.Cancel;
    this.btnExit.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnExit1 = this.btnExit;
    point1 = new Point(621, 585);
    Point point18 = point1;
    btnExit1.Location = point18;
    Button btnExit2 = this.btnExit;
    padding1 = new Padding(4);
    Padding padding18 = padding1;
    btnExit2.Margin = padding18;
    this.btnExit.Name = "btnExit";
    Button btnExit3 = this.btnExit;
    size1 = new Size(61, 31 /*0x1F*/);
    Size size18 = size1;
    btnExit3.Size = size18;
    this.btnExit.TabIndex = 19;
    this.btnExit.Text = "E&xit";
    this.btnExit.UseVisualStyleBackColor = false;
    this.btnsave.BackColor = SystemColors.ButtonFace;
    this.btnsave.Enabled = false;
    this.btnsave.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnsave1 = this.btnsave;
    point1 = new Point(306, 585);
    Point point19 = point1;
    btnsave1.Location = point19;
    Button btnsave2 = this.btnsave;
    padding1 = new Padding(4);
    Padding padding19 = padding1;
    btnsave2.Margin = padding19;
    this.btnsave.Name = "btnsave";
    Button btnsave3 = this.btnsave;
    size1 = new Size(63 /*0x3F*/, 31 /*0x1F*/);
    Size size19 = size1;
    btnsave3.Size = size19;
    this.btnsave.TabIndex = 16 /*0x10*/;
    this.btnsave.Text = "&Save";
    this.btnsave.UseVisualStyleBackColor = false;
    this.cmbTrainNo.Enabled = false;
    this.cmbTrainNo.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.cmbTrainNo.FormattingEnabled = true;
    ComboBox cmbTrainNo1 = this.cmbTrainNo;
    point1 = new Point(170, 54);
    Point point20 = point1;
    cmbTrainNo1.Location = point20;
    ComboBox cmbTrainNo2 = this.cmbTrainNo;
    padding1 = new Padding(4);
    Padding padding20 = padding1;
    cmbTrainNo2.Margin = padding20;
    this.cmbTrainNo.Name = "cmbTrainNo";
    ComboBox cmbTrainNo3 = this.cmbTrainNo;
    size1 = new Size(116, 24);
    Size size20 = size1;
    cmbTrainNo3.Size = size20;
    this.cmbTrainNo.Sorted = true;
    this.cmbTrainNo.TabIndex = 1;
    this.btnCgs.BackColor = SystemColors.ButtonFace;
    this.btnCgs.Enabled = false;
    this.btnCgs.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.btnCgs.ForeColor = SystemColors.ControlText;
    Button btnCgs1 = this.btnCgs;
    point1 = new Point(100, 509);
    Point point21 = point1;
    btnCgs1.Location = point21;
    Button btnCgs2 = this.btnCgs;
    padding1 = new Padding(4);
    Padding padding21 = padding1;
    btnCgs2.Margin = padding21;
    this.btnCgs.Name = "btnCgs";
    Button btnCgs3 = this.btnCgs;
    size1 = new Size(63 /*0x3F*/, 26);
    Size size21 = size1;
    btnCgs3.Size = size21;
    this.btnCgs.TabIndex = 13;
    this.btnCgs.Tag = (object) "";
    this.btnCgs.Text = "CGS Position";
    this.btnCgs.UseVisualStyleBackColor = false;
    this.btnTrainDetails.BackColor = SystemColors.ButtonFace;
    this.btnTrainDetails.Enabled = false;
    this.btnTrainDetails.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.btnTrainDetails.ForeColor = SystemColors.ControlText;
    Button btnTrainDetails1 = this.btnTrainDetails;
    point1 = new Point(61, 451);
    Point point22 = point1;
    btnTrainDetails1.Location = point22;
    Button btnTrainDetails2 = this.btnTrainDetails;
    padding1 = new Padding(4);
    Padding padding22 = padding1;
    btnTrainDetails2.Margin = padding22;
    this.btnTrainDetails.Name = "btnTrainDetails";
    Button btnTrainDetails3 = this.btnTrainDetails;
    size1 = new Size(129, 31 /*0x1F*/);
    Size size22 = size1;
    btnTrainDetails3.Size = size22;
    this.btnTrainDetails.TabIndex = 12;
    this.btnTrainDetails.Text = "Train Details";
    this.btnTrainDetails.UseVisualStyleBackColor = false;
    this.cmbDepTime.Enabled = false;
    this.cmbDepTime.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.cmbDepTime.FormattingEnabled = true;
    ComboBox cmbDepTime1 = this.cmbDepTime;
    point1 = new Point(658, 305);
    Point point23 = point1;
    cmbDepTime1.Location = point23;
    ComboBox cmbDepTime2 = this.cmbDepTime;
    padding1 = new Padding(4);
    Padding padding23 = padding1;
    cmbDepTime2.Margin = padding23;
    this.cmbDepTime.Name = "cmbDepTime";
    ComboBox cmbDepTime3 = this.cmbDepTime;
    size1 = new Size(102, 24);
    Size size23 = size1;
    cmbDepTime3.Size = size23;
    this.cmbDepTime.TabIndex = 10;
    this.cmbArrTime.Enabled = false;
    this.cmbArrTime.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.cmbArrTime.FormattingEnabled = true;
    ComboBox cmbArrTime1 = this.cmbArrTime;
    point1 = new Point(658, 241);
    Point point24 = point1;
    cmbArrTime1.Location = point24;
    ComboBox cmbArrTime2 = this.cmbArrTime;
    padding1 = new Padding(4);
    Padding padding24 = padding1;
    cmbArrTime2.Margin = padding24;
    this.cmbArrTime.Name = "cmbArrTime";
    ComboBox cmbArrTime3 = this.cmbArrTime;
    size1 = new Size(102, 24);
    Size size24 = size1;
    cmbArrTime3.Size = size24;
    this.cmbArrTime.TabIndex = 9;
    this.txtEndStn.Enabled = false;
    this.txtEndStn.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtEndStn1 = this.txtEndStn;
    point1 = new Point(170, 309);
    Point point25 = point1;
    txtEndStn1.Location = point25;
    TextBox txtEndStn2 = this.txtEndStn;
    padding1 = new Padding(4);
    Padding padding25 = padding1;
    txtEndStn2.Margin = padding25;
    this.txtEndStn.MaxLength = 18;
    this.txtEndStn.Name = "txtEndStn";
    TextBox txtEndStn3 = this.txtEndStn;
    size1 = new Size(226, 22);
    Size size25 = size1;
    txtEndStn3.Size = size25;
    this.txtEndStn.TabIndex = 7;
    this.txtStartStn.Enabled = false;
    this.txtStartStn.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtStartStn1 = this.txtStartStn;
    point1 = new Point(170, 238);
    Point point26 = point1;
    txtStartStn1.Location = point26;
    TextBox txtStartStn2 = this.txtStartStn;
    padding1 = new Padding(4);
    Padding padding26 = padding1;
    txtStartStn2.Margin = padding26;
    this.txtStartStn.MaxLength = 18;
    this.txtStartStn.Name = "txtStartStn";
    TextBox txtStartStn3 = this.txtStartStn;
    size1 = new Size(226, 22);
    Size size26 = size1;
    txtStartStn3.Size = size26;
    this.txtStartStn.TabIndex = 6;
    this.txtTrainName.Enabled = false;
    this.txtTrainName.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtTrainName1 = this.txtTrainName;
    point1 = new Point(492, 51);
    Point point27 = point1;
    txtTrainName1.Location = point27;
    TextBox txtTrainName2 = this.txtTrainName;
    padding1 = new Padding(4);
    Padding padding27 = padding1;
    txtTrainName2.Margin = padding27;
    this.txtTrainName.MaxLength = 18;
    this.txtTrainName.Name = "txtTrainName";
    TextBox txtTrainName3 = this.txtTrainName;
    size1 = new Size(248, 22);
    Size size27 = size1;
    txtTrainName3.Size = size27;
    this.txtTrainName.TabIndex = 2;
    this.lblTrainNo.AutoSize = true;
    this.lblTrainNo.Enabled = false;
    this.lblTrainNo.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.lblTrainNo.ForeColor = SystemColors.WindowText;
    Label lblTrainNo1 = this.lblTrainNo;
    point1 = new Point(43, 55);
    Point point28 = point1;
    lblTrainNo1.Location = point28;
    Label lblTrainNo2 = this.lblTrainNo;
    padding1 = new Padding(4, 0, 4, 0);
    Padding padding28 = padding1;
    lblTrainNo2.Margin = padding28;
    this.lblTrainNo.Name = "lblTrainNo";
    Label lblTrainNo3 = this.lblTrainNo;
    size1 = new Size(102, 16 /*0x10*/);
    Size size28 = size1;
    lblTrainNo3.Size = size28;
    this.lblTrainNo.TabIndex = 56;
    this.lblTrainNo.Text = "Train Number";
    this.btnCancel.BackColor = SystemColors.ButtonFace;
    this.btnCancel.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnCancel1 = this.btnCancel;
    point1 = new Point(508, 585);
    Point point29 = point1;
    btnCancel1.Location = point29;
    Button btnCancel2 = this.btnCancel;
    padding1 = new Padding(4);
    Padding padding29 = padding1;
    btnCancel2.Margin = padding29;
    this.btnCancel.Name = "btnCancel";
    Button btnCancel3 = this.btnCancel;
    size1 = new Size(68, 31 /*0x1F*/);
    Size size29 = size1;
    btnCancel3.Size = size29;
    this.btnCancel.TabIndex = 18;
    this.btnCancel.Text = "&Cancel";
    this.btnCancel.UseVisualStyleBackColor = false;
    this.cmbPfno.FormattingEnabled = true;
    ComboBox cmbPfno1 = this.cmbPfno;
    point1 = new Point(170, 371);
    Point point30 = point1;
    cmbPfno1.Location = point30;
    ComboBox cmbPfno2 = this.cmbPfno;
    padding1 = new Padding(4);
    Padding padding30 = padding1;
    cmbPfno2.Margin = padding30;
    this.cmbPfno.Name = "cmbPfno";
    ComboBox cmbPfno3 = this.cmbPfno;
    size1 = new Size(97, 24);
    Size size30 = size1;
    cmbPfno3.Size = size30;
    this.cmbPfno.TabIndex = 85;
    this.lblStationPos.AutoSize = true;
    this.lblStationPos.Enabled = false;
    this.lblStationPos.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblStationPos1 = this.lblStationPos;
    point1 = new Point(516, 441);
    Point point31 = point1;
    lblStationPos1.Location = point31;
    Label lblStationPos2 = this.lblStationPos;
    padding1 = new Padding(4, 0, 4, 0);
    Padding padding31 = padding1;
    lblStationPos2.Margin = padding31;
    this.lblStationPos.Name = "lblStationPos";
    Label lblStationPos3 = this.lblStationPos;
    size1 = new Size(116, 16 /*0x10*/);
    Size size31 = size1;
    lblStationPos3.Size = size31;
    this.lblStationPos.TabIndex = 89;
    this.lblStationPos.Text = "Station Position";
    this.cmbStationPos.Enabled = false;
    this.cmbStationPos.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.cmbStationPos.FormattingEnabled = true;
    this.cmbStationPos.Items.AddRange(new object[3]
    {
      (object) "START STATION",
      (object) "END STATION",
      (object) "NONE"
    });
    ComboBox cmbStationPos1 = this.cmbStationPos;
    point1 = new Point(658, 436);
    Point point32 = point1;
    cmbStationPos1.Location = point32;
    ComboBox cmbStationPos2 = this.cmbStationPos;
    padding1 = new Padding(4);
    Padding padding32 = padding1;
    cmbStationPos2.Margin = padding32;
    this.cmbStationPos.Name = "cmbStationPos";
    ComboBox cmbStationPos3 = this.cmbStationPos;
    size1 = new Size(102, 24);
    Size size32 = size1;
    cmbStationPos3.Size = size32;
    this.cmbStationPos.TabIndex = 90;
    this.txtRegLang.Enabled = false;
    TextBox txtRegLang1 = this.txtRegLang;
    point1 = new Point(747, 154);
    Point point33 = point1;
    txtRegLang1.Location = point33;
    this.txtRegLang.Name = "txtRegLang";
    TextBox txtRegLang2 = this.txtRegLang;
    size1 = new Size(100, 22);
    Size size33 = size1;
    txtRegLang2.Size = size33;
    this.txtRegLang.TabIndex = 91;
    this.AcceptButton = (IButtonControl) this.btnAdd;
    this.AutoScaleDimensions = new SizeF(9f, 16f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.BackColor = Color.PowderBlue;
    this.CancelButton = (IButtonControl) this.btnExit;
    size1 = new Size(865, 638);
    this.ClientSize = size1;
    this.Controls.Add((Control) this.txtRegLang);
    this.Controls.Add((Control) this.cmbStationPos);
    this.Controls.Add((Control) this.lblStationPos);
    this.Controls.Add((Control) this.cmbPfno);
    this.Controls.Add((Control) this.btnCancel);
    this.Controls.Add((Control) this.cmbDir);
    this.Controls.Add((Control) this.lblDir);
    this.Controls.Add((Control) this.lblEnglish);
    this.Controls.Add((Control) this.txtTrainNameReg);
    this.Controls.Add((Control) this.lblHindi);
    this.Controls.Add((Control) this.txtTrainNameHin);
    this.Controls.Add((Control) this.lblReg);
    this.Controls.Add((Control) this.lblPfno);
    this.Controls.Add((Control) this.lblDepTime);
    this.Controls.Add((Control) this.lblArrTime);
    this.Controls.Add((Control) this.lblEndStn);
    this.Controls.Add((Control) this.lblSrtStn);
    this.Controls.Add((Control) this.lblTrainName);
    this.Controls.Add((Control) this.btnAdd);
    this.Controls.Add((Control) this.btnDelete);
    this.Controls.Add((Control) this.btnEdit);
    this.Controls.Add((Control) this.btnExit);
    this.Controls.Add((Control) this.btnsave);
    this.Controls.Add((Control) this.cmbTrainNo);
    this.Controls.Add((Control) this.btnCgs);
    this.Controls.Add((Control) this.btnTrainDetails);
    this.Controls.Add((Control) this.cmbDepTime);
    this.Controls.Add((Control) this.cmbArrTime);
    this.Controls.Add((Control) this.txtEndStn);
    this.Controls.Add((Control) this.txtStartStn);
    this.Controls.Add((Control) this.txtTrainName);
    this.Controls.Add((Control) this.lblTrainNo);
    this.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.FormBorderStyle = FormBorderStyle.FixedToolWindow;
    padding1 = new Padding(4);
    this.Margin = padding1;
    this.Name = "frmTrainConfig";
    this.Text = "Train Configuration";
    ((ISupportInitialize) this.TrainconfigtableBindingSource).EndInit();
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  internal virtual ComboBox cmbDir
  {
    [DebuggerNonUserCode] get { return this._cmbDir; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbDir = value; }
  }

  internal virtual Label lblDir
  {
    [DebuggerNonUserCode] get { return this._lblDir; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._lblDir = value; }
  }

  internal virtual Label lblEnglish
  {
    [DebuggerNonUserCode] get { return this._lblEnglish; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblEnglish = value;
    }
  }

  internal virtual TextBox txtTrainNameReg
  {
    [DebuggerNonUserCode] get { return this._txtTrainNameReg; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.txtTrainNameReg_TextChanged);
      if (this._txtTrainNameReg != null)
        this._txtTrainNameReg.TextChanged -= eventHandler;
      this._txtTrainNameReg = value;
      if (this._txtTrainNameReg == null)
        return;
      this._txtTrainNameReg.TextChanged += eventHandler;
    }
  }

  internal virtual Label lblHindi
  {
    [DebuggerNonUserCode] get { return this._lblHindi; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._lblHindi = value; }
  }

  internal virtual TextBox txtTrainNameHin
  {
    [DebuggerNonUserCode] get { return this._txtTrainNameHin; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtTrainNameHin = value;
    }
  }

  internal virtual Label lblReg
  {
    [DebuggerNonUserCode] get { return this._lblReg; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._lblReg = value; }
  }

  internal virtual Label lblPfno
  {
    [DebuggerNonUserCode] get { return this._lblPfno; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._lblPfno = value; }
  }

  internal virtual Label lblDepTime
  {
    [DebuggerNonUserCode] get { return this._lblDepTime; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblDepTime = value;
    }
  }

  internal virtual Label lblArrTime
  {
    [DebuggerNonUserCode] get { return this._lblArrTime; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblArrTime = value;
    }
  }

  internal virtual Label lblEndStn
  {
    [DebuggerNonUserCode] get { return this._lblEndStn; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblEndStn = value;
    }
  }

  internal virtual Label lblSrtStn
  {
    [DebuggerNonUserCode] get { return this._lblSrtStn; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblSrtStn = value;
    }
  }

  internal virtual Label lblTrainName
  {
    [DebuggerNonUserCode] get { return this._lblTrainName; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblTrainName = value;
    }
  }

  internal virtual Button btnAdd
  {
    [DebuggerNonUserCode] get { return this._btnAdd; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnAdd_Click);
      if (this._btnAdd != null)
        this._btnAdd.Click -= eventHandler;
      this._btnAdd = value;
      if (this._btnAdd == null)
        return;
      this._btnAdd.Click += eventHandler;
    }
  }

  internal virtual Button btnDelete
  {
    [DebuggerNonUserCode] get { return this._btnDelete; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnDelete_Click);
      if (this._btnDelete != null)
        this._btnDelete.Click -= eventHandler;
      this._btnDelete = value;
      if (this._btnDelete == null)
        return;
      this._btnDelete.Click += eventHandler;
    }
  }

  internal virtual Button btnEdit
  {
    [DebuggerNonUserCode] get { return this._btnEdit; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnEdit_Click);
      if (this._btnEdit != null)
        this._btnEdit.Click -= eventHandler;
      this._btnEdit = value;
      if (this._btnEdit == null)
        return;
      this._btnEdit.Click += eventHandler;
    }
  }

  internal virtual Button btnExit
  {
    [DebuggerNonUserCode] get { return this._btnExit; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnExit_Click);
      if (this._btnExit != null)
        this._btnExit.Click -= eventHandler;
      this._btnExit = value;
      if (this._btnExit == null)
        return;
      this._btnExit.Click += eventHandler;
    }
  }

  internal virtual Button btnsave
  {
    [DebuggerNonUserCode] get { return this._btnsave; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnsave_Click);
      if (this._btnsave != null)
        this._btnsave.Click -= eventHandler;
      this._btnsave = value;
      if (this._btnsave == null)
        return;
      this._btnsave.Click += eventHandler;
    }
  }

  internal virtual ComboBox cmbTrainNo
  {
    [DebuggerNonUserCode] get { return this._cmbTrainNo; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler1 = new EventHandler(this.cmbTrainNo_SelectedValueChanged);
      EventHandler eventHandler2 = new EventHandler(this.cmbTrainNo_SelectedIndexChanged);
      if (this._cmbTrainNo != null)
      {
        this._cmbTrainNo.SelectedValueChanged -= eventHandler1;
        this._cmbTrainNo.SelectedIndexChanged -= eventHandler2;
      }
      this._cmbTrainNo = value;
      if (this._cmbTrainNo == null)
        return;
      this._cmbTrainNo.SelectedValueChanged += eventHandler1;
      this._cmbTrainNo.SelectedIndexChanged += eventHandler2;
    }
  }

  internal virtual Button btnCgs
  {
    [DebuggerNonUserCode] get { return this._btnCgs; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnCGS_Click);
      if (this._btnCgs != null)
        this._btnCgs.Click -= eventHandler;
      this._btnCgs = value;
      if (this._btnCgs == null)
        return;
      this._btnCgs.Click += eventHandler;
    }
  }

  internal virtual Button btnTrainDetails
  {
    [DebuggerNonUserCode] get { return this._btnTrainDetails; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnTrainDetails_Click);
      if (this._btnTrainDetails != null)
        this._btnTrainDetails.Click -= eventHandler;
      this._btnTrainDetails = value;
      if (this._btnTrainDetails == null)
        return;
      this._btnTrainDetails.Click += eventHandler;
    }
  }

  internal virtual ComboBox cmbDepTime
  {
    [DebuggerNonUserCode] get { return this._cmbDepTime; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._cmbDepTime = value;
    }
  }

  internal virtual ComboBox cmbArrTime
  {
    [DebuggerNonUserCode] get { return this._cmbArrTime; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._cmbArrTime = value;
    }
  }

  internal virtual TextBox txtEndStn
  {
    [DebuggerNonUserCode] get { return this._txtEndStn; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtEndStn = value;
    }
  }

  internal virtual TextBox txtStartStn
  {
    [DebuggerNonUserCode] get { return this._txtStartStn; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtStartStn = value;
    }
  }

  internal virtual TextBox txtTrainName
  {
    [DebuggerNonUserCode] get { return this._txtTrainName; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtTrainName = value;
    }
  }

  internal virtual Label lblTrainNo
  {
    [DebuggerNonUserCode] get { return this._lblTrainNo; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblTrainNo = value;
    }
  }

  internal virtual BindingSource TrainconfigtableBindingSource
  {
    [DebuggerNonUserCode] get { return this._TrainconfigtableBindingSource; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._TrainconfigtableBindingSource = value;
    }
  }

  internal virtual Button btnCancel
  {
    [DebuggerNonUserCode] get { return this._btnCancel; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnCancel_Click);
      if (this._btnCancel != null)
        this._btnCancel.Click -= eventHandler;
      this._btnCancel = value;
      if (this._btnCancel == null)
        return;
      this._btnCancel.Click += eventHandler;
    }
  }

  internal virtual ComboBox cmbPfno
  {
    [DebuggerNonUserCode] get { return this._cmbPfno; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbPfno = value; }
  }

  internal virtual Label lblStationPos
  {
    [DebuggerNonUserCode] get { return this._lblStationPos; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblStationPos = value;
    }
  }

  internal virtual ComboBox cmbStationPos
  {
    [DebuggerNonUserCode] get { return this._cmbStationPos; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._cmbStationPos = value;
    }
  }

  internal virtual TextBox txtRegLang
  {
    [DebuggerNonUserCode] get { return this._txtRegLang; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtRegLang = value;
    }
  }

  protected virtual frmTrainDetails event_traindetails
  {
    [DebuggerNonUserCode] get { return this._event_traindetails; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._event_traindetails = value;
    }
  }

  protected virtual frmCgs event_cgs
  {
    [DebuggerNonUserCode] get { return this._event_cgs; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._event_cgs = value;
    }
  }

  private void cmbTrainNo_SelectedValueChanged(object sender, EventArgs e)
  {
    int index1 = 0;
    int index2 = 0;
    try
    {
      if (Operators.CompareString(this.cmbTrainNo.Text, "", false) == 0)
        return;
      while (index1 < frmMainFormIPIS.train_cnt && Operators.CompareString(this.cmbTrainNo.Text, frmMainFormIPIS.train_details[index1].train_no, false) != 0)
        checked { ++index1; }
      this.txtTrainName.Text = frmMainFormIPIS.train_details[index1].train_name;
      this.txtTrainNameReg.Text = frmMainFormIPIS.train_details[index1].train_name_reg;
      this.txtTrainNameHin.Text = frmMainFormIPIS.train_details[index1].train_name_hin;
      this.txtStartStn.Text = frmMainFormIPIS.train_details[index1].start_station;
      this.txtEndStn.Text = frmMainFormIPIS.train_details[index1].end_Station;
      this.cmbArrTime.Text = frmMainFormIPIS.train_details[index1].arr_time;
      this.cmbDepTime.Text = frmMainFormIPIS.train_details[index1].dep_time;
      this.cmbPfno.Text = frmMainFormIPIS.train_details[index1].pf_no;
      this.cmbDir.Text = frmMainFormIPIS.train_details[index1].dir;
      this.cmbStationPos.Text = frmMainFormIPIS.train_details[index1].station_pos;
      if (frmMainFormIPIS.train_details[index1].daily == -1)
        MyProject.Forms.frmTrainDetails.radDaily.Checked = true;
      frmTrainDetails frmTrainDetails = new frmTrainDetails();
      if (frmMainFormIPIS.train_details[index1].daily == 1)
        frmTrainDetails.radDaily.Checked = true;
      else if (frmMainFormIPIS.train_details[index1].specificdays == 1)
      {
        frmTrainDetails.radSpecificDaysWeek.Checked = true;
        if (Operators.CompareString(Strings.Trim(frmMainFormIPIS.train_details[index1].days[0]), "Sunday", false) == 0)
          frmTrainDetails.chklstWeekDays.SetItemChecked(0, true);
        else
          frmTrainDetails.chklstWeekDays.SetItemChecked(0, false);
        if (Operators.CompareString(Strings.Trim(frmMainFormIPIS.train_details[index1].days[1]), "Monday", false) == 0)
          frmTrainDetails.chklstWeekDays.SetItemChecked(1, true);
        else
          frmTrainDetails.chklstWeekDays.SetItemChecked(1, false);
        if (Operators.CompareString(Strings.Trim(frmMainFormIPIS.train_details[index1].days[2]), "Tuesday", false) == 0)
          frmTrainDetails.chklstWeekDays.SetItemChecked(2, true);
        else
          frmTrainDetails.chklstWeekDays.SetItemChecked(2, false);
        if (Operators.CompareString(Strings.Trim(frmMainFormIPIS.train_details[index1].days[3]), "Wednesday", false) == 0)
          frmTrainDetails.chklstWeekDays.SetItemChecked(3, true);
        else
          frmTrainDetails.chklstWeekDays.SetItemChecked(3, false);
        if (Operators.CompareString(Strings.Trim(frmMainFormIPIS.train_details[index1].days[4]), "Thursday", false) == 0)
          frmTrainDetails.chklstWeekDays.SetItemChecked(4, true);
        else
          frmTrainDetails.chklstWeekDays.SetItemChecked(4, false);
        if (Operators.CompareString(Strings.Trim(frmMainFormIPIS.train_details[index1].days[5]), "Friday", false) == 0)
          frmTrainDetails.chklstWeekDays.SetItemChecked(5, true);
        else
          frmTrainDetails.chklstWeekDays.SetItemChecked(5, false);
        if (Operators.CompareString(Strings.Trim(frmMainFormIPIS.train_details[index1].days[6]), "Saturday", false) == 0)
          frmTrainDetails.chklstWeekDays.SetItemChecked(6, true);
        else
          frmTrainDetails.chklstWeekDays.SetItemChecked(6, false);
      }
      else if (frmMainFormIPIS.train_details[index1].period == 1)
      {
        frmTrainDetails.radPeriod.Checked = true;
        frmTrainDetails.datFrom.Text = Conversions.ToString(frmMainFormIPIS.train_details[index1].fromdt);
        frmTrainDetails.datTo.Text = Conversions.ToString(frmMainFormIPIS.train_details[index1].todt);
      }
      else if (frmMainFormIPIS.train_details[index1].specificdate == 1)
      {
        frmTrainDetails.radSpecificDates.Checked = true;
        int index3 = 0;
        while (index3 < 10)
        {
          if (DateTime.Compare(frmMainFormIPIS.train_details[index1].specificdates[index3], DateTime.MinValue) != 0)
            frmTrainDetails.lstboxSpecificDates.Items.Add((object) frmMainFormIPIS.train_details[index1].specificdates[index3]);
          checked { ++index3; }
        }
      }
      frmCgs frmCgs = new frmCgs();
      int index4 = index1;
      frmCgs.t1.Text = frmMainFormIPIS.train_details[index4].cgs_inf[index2];
      int index5 = checked (index2 + 1);
      frmCgs.t2.Text = frmMainFormIPIS.train_details[index4].cgs_inf[index5];
      int index6 = checked (index5 + 1);
      frmCgs.t3.Text = frmMainFormIPIS.train_details[index4].cgs_inf[index6];
      int index7 = checked (index6 + 1);
      frmCgs.t4.Text = frmMainFormIPIS.train_details[index4].cgs_inf[index7];
      int index8 = checked (index7 + 1);
      frmCgs.t5.Text = frmMainFormIPIS.train_details[index4].cgs_inf[index8];
      int index9 = checked (index8 + 1);
      frmCgs.t6.Text = frmMainFormIPIS.train_details[index4].cgs_inf[index9];
      int index10 = checked (index9 + 1);
      frmCgs.t7.Text = frmMainFormIPIS.train_details[index4].cgs_inf[index10];
      int index11 = checked (index10 + 1);
      frmCgs.t8.Text = frmMainFormIPIS.train_details[index4].cgs_inf[index11];
      int index12 = checked (index11 + 1);
      frmCgs.t9.Text = frmMainFormIPIS.train_details[index4].cgs_inf[index12];
      int index13 = checked (index12 + 1);
      frmCgs.t10.Text = frmMainFormIPIS.train_details[index4].cgs_inf[index13];
      int index14 = checked (index13 + 1);
      frmCgs.t11.Text = frmMainFormIPIS.train_details[index4].cgs_inf[index14];
      int index15 = checked (index14 + 1);
      frmCgs.t12.Text = frmMainFormIPIS.train_details[index4].cgs_inf[index15];
      int index16 = checked (index15 + 1);
      frmCgs.t13.Text = frmMainFormIPIS.train_details[index4].cgs_inf[index16];
      int index17 = checked (index16 + 1);
      frmCgs.t14.Text = frmMainFormIPIS.train_details[index4].cgs_inf[index17];
      int index18 = checked (index17 + 1);
      frmCgs.t15.Text = frmMainFormIPIS.train_details[index4].cgs_inf[index18];
      int index19 = checked (index18 + 1);
      frmCgs.t16.Text = frmMainFormIPIS.train_details[index4].cgs_inf[index19];
      int index20 = checked (index19 + 1);
      frmCgs.t17.Text = frmMainFormIPIS.train_details[index4].cgs_inf[index20];
      int index21 = checked (index20 + 1);
      frmCgs.t18.Text = frmMainFormIPIS.train_details[index4].cgs_inf[index21];
      int index22 = checked (index21 + 1);
      frmCgs.t19.Text = frmMainFormIPIS.train_details[index4].cgs_inf[index22];
      int index23 = checked (index22 + 1);
      frmCgs.t20.Text = frmMainFormIPIS.train_details[index4].cgs_inf[index23];
      int index24 = checked (index23 + 1);
      frmCgs.t21.Text = frmMainFormIPIS.train_details[index4].cgs_inf[index24];
      int index25 = checked (index24 + 1);
      frmCgs.t22.Text = frmMainFormIPIS.train_details[index4].cgs_inf[index25];
      int index26 = checked (index25 + 1);
      frmCgs.t23.Text = frmMainFormIPIS.train_details[index4].cgs_inf[index26];
      int index27 = checked (index26 + 1);
      frmCgs.t24.Text = frmMainFormIPIS.train_details[index4].cgs_inf[index27];
      int index28 = checked (index27 + 1);
      frmCgs.t25.Text = frmMainFormIPIS.train_details[index4].cgs_inf[index28];
      int index29 = checked (index28 + 1);
      frmCgs.t26.Text = frmMainFormIPIS.train_details[index4].cgs_inf[index29];
      if (this.btnDelete.Enabled)
      {
        if (MessageBox.Show(" Confirm Delete (Y/N)", "Delete", MessageBoxButtons.YesNo, MessageBoxIcon.None, MessageBoxDefaultButton.Button1) == DialogResult.No)
        {
          this.btnDelete.Enabled = true;
          this.btnAdd.Enabled = true;
          this.btnEdit.Enabled = true;
          this.btnsave.Enabled = false;
          this.txtTrainName.Text = string.Empty;
          this.txtTrainNameReg.Text = string.Empty;
          this.txtTrainNameHin.Text = string.Empty;
          this.txtStartStn.Text = string.Empty;
          this.txtEndStn.Text = string.Empty;
          this.cmbArrTime.Text = string.Empty;
          this.cmbDepTime.Text = string.Empty;
          this.cmbPfno.Text = string.Empty;
          this.cmbTrainNo.Enabled = false;
          this.txtTrainName.Enabled = false;
          this.txtTrainNameReg.Enabled = false;
          this.txtTrainNameHin.Enabled = false;
          this.txtStartStn.Enabled = false;
          this.txtEndStn.Enabled = false;
          this.cmbArrTime.Enabled = false;
          this.cmbDepTime.Enabled = false;
          this.cmbPfno.Enabled = false;
          this.btnTrainDetails.Enabled = false;
          this.btnCgs.Enabled = false;
          this.cmbDir.Enabled = false;
          this.lblDir.Enabled = false;
          this.lblArrTime.Enabled = false;
          this.lblTrainNo.Enabled = false;
          this.lblTrainName.Enabled = false;
          this.lblSrtStn.Enabled = false;
          this.lblReg.Enabled = false;
          this.lblPfno.Enabled = false;
          this.lblHindi.Enabled = false;
          this.lblEnglish.Enabled = false;
          this.lblEndStn.Enabled = false;
          this.lblDir.Enabled = false;
          this.lblDepTime.Enabled = false;
          this.lblArrTime.Enabled = false;
          this.cmbStationPos.Enabled = false;
          this.lblStationPos.Enabled = false;
          this.cmbStationPos.Text = string.Empty;
        }
        else
        {
          network_db_read.delete_train_info(this.cmbTrainNo.Text);
          string text = this.cmbTrainNo.Text;
          this.cmbTrainNo.Items.Remove((object) this.cmbTrainNo.Text);
          while (index1 < frmMainFormIPIS.train_cnt)
          {
            frmMainFormIPIS.train_details[index1].train_no = frmMainFormIPIS.train_details[checked (index1 + 1)].train_no;
            frmMainFormIPIS.train_details[index1].train_name = frmMainFormIPIS.train_details[checked (index1 + 1)].train_name;
            frmMainFormIPIS.train_details[index1].train_name_reg = frmMainFormIPIS.train_details[checked (index1 + 1)].train_name_reg;
            frmMainFormIPIS.train_details[index1].train_name_hin = frmMainFormIPIS.train_details[checked (index1 + 1)].train_name_hin;
            frmMainFormIPIS.train_details[index1].start_station = frmMainFormIPIS.train_details[checked (index1 + 1)].start_station;
            frmMainFormIPIS.train_details[index1].end_Station = frmMainFormIPIS.train_details[checked (index1 + 1)].end_Station;
            frmMainFormIPIS.train_details[index1].arr_time = frmMainFormIPIS.train_details[checked (index1 + 1)].arr_time;
            frmMainFormIPIS.train_details[index1].dep_time = frmMainFormIPIS.train_details[checked (index1 + 1)].dep_time;
            frmMainFormIPIS.train_details[index1].language_name = frmMainFormIPIS.train_details[checked (index1 + 1)].language_name;
            frmMainFormIPIS.train_details[index1].dir = frmMainFormIPIS.train_details[checked (index1 + 1)].dir;
            frmMainFormIPIS.train_details[index1].daily = frmMainFormIPIS.train_details[checked (index1 + 1)].daily;
            frmMainFormIPIS.train_details[index1].specificdays = frmMainFormIPIS.train_details[checked (index1 + 1)].specificdays;
            frmMainFormIPIS.train_details[index1].days[0] = frmMainFormIPIS.train_details[checked (index1 + 1)].days[0];
            frmMainFormIPIS.train_details[index1].days[1] = frmMainFormIPIS.train_details[checked (index1 + 1)].days[1];
            frmMainFormIPIS.train_details[index1].days[2] = frmMainFormIPIS.train_details[checked (index1 + 1)].days[2];
            frmMainFormIPIS.train_details[index1].days[3] = frmMainFormIPIS.train_details[checked (index1 + 1)].days[3];
            frmMainFormIPIS.train_details[index1].days[4] = frmMainFormIPIS.train_details[checked (index1 + 1)].days[4];
            frmMainFormIPIS.train_details[index1].days[5] = frmMainFormIPIS.train_details[checked (index1 + 1)].days[5];
            frmMainFormIPIS.train_details[index1].days[6] = frmMainFormIPIS.train_details[checked (index1 + 1)].days[6];
            frmMainFormIPIS.train_details[index1].period = frmMainFormIPIS.train_details[checked (index1 + 1)].period;
            frmMainFormIPIS.train_details[index1].fromdt = frmMainFormIPIS.train_details[checked (index1 + 1)].fromdt;
            frmMainFormIPIS.train_details[index1].todt = frmMainFormIPIS.train_details[checked (index1 + 1)].todt;
            frmMainFormIPIS.train_details[index1].specificdate = frmMainFormIPIS.train_details[checked (index1 + 1)].specificdate;
            frmMainFormIPIS.train_details[index1].specificdates[0] = frmMainFormIPIS.train_details[checked (index1 + 1)].specificdates[0];
            frmMainFormIPIS.train_details[index1].specificdates[1] = frmMainFormIPIS.train_details[checked (index1 + 1)].specificdates[1];
            frmMainFormIPIS.train_details[index1].specificdates[2] = frmMainFormIPIS.train_details[checked (index1 + 1)].specificdates[2];
            frmMainFormIPIS.train_details[index1].specificdates[3] = frmMainFormIPIS.train_details[checked (index1 + 1)].specificdates[3];
            frmMainFormIPIS.train_details[index1].specificdates[4] = frmMainFormIPIS.train_details[checked (index1 + 1)].specificdates[4];
            frmMainFormIPIS.train_details[index1].specificdates[5] = frmMainFormIPIS.train_details[checked (index1 + 1)].specificdates[5];
            frmMainFormIPIS.train_details[index1].specificdates[6] = frmMainFormIPIS.train_details[checked (index1 + 1)].specificdates[6];
            frmMainFormIPIS.train_details[index1].specificdates[7] = frmMainFormIPIS.train_details[checked (index1 + 1)].specificdates[7];
            frmMainFormIPIS.train_details[index1].specificdates[8] = frmMainFormIPIS.train_details[checked (index1 + 1)].specificdates[8];
            frmMainFormIPIS.train_details[index1].specificdates[9] = frmMainFormIPIS.train_details[checked (index1 + 1)].specificdates[9];
            frmMainFormIPIS.train_details[index1].cgs_inf[0] = frmMainFormIPIS.train_details[checked (index1 + 1)].cgs_inf[0];
            frmMainFormIPIS.train_details[index1].cgs_inf[1] = frmMainFormIPIS.train_details[checked (index1 + 1)].cgs_inf[1];
            frmMainFormIPIS.train_details[index1].cgs_inf[2] = frmMainFormIPIS.train_details[checked (index1 + 1)].cgs_inf[2];
            frmMainFormIPIS.train_details[index1].cgs_inf[3] = frmMainFormIPIS.train_details[checked (index1 + 1)].cgs_inf[3];
            frmMainFormIPIS.train_details[index1].cgs_inf[4] = frmMainFormIPIS.train_details[checked (index1 + 1)].cgs_inf[4];
            frmMainFormIPIS.train_details[index1].cgs_inf[5] = frmMainFormIPIS.train_details[checked (index1 + 1)].cgs_inf[5];
            frmMainFormIPIS.train_details[index1].cgs_inf[6] = frmMainFormIPIS.train_details[checked (index1 + 1)].cgs_inf[6];
            frmMainFormIPIS.train_details[index1].cgs_inf[7] = frmMainFormIPIS.train_details[checked (index1 + 1)].cgs_inf[7];
            frmMainFormIPIS.train_details[index1].cgs_inf[8] = frmMainFormIPIS.train_details[checked (index1 + 1)].cgs_inf[8];
            frmMainFormIPIS.train_details[index1].cgs_inf[9] = frmMainFormIPIS.train_details[checked (index1 + 1)].cgs_inf[9];
            frmMainFormIPIS.train_details[index1].cgs_inf[10] = frmMainFormIPIS.train_details[checked (index1 + 1)].cgs_inf[10];
            frmMainFormIPIS.train_details[index1].cgs_inf[11] = frmMainFormIPIS.train_details[checked (index1 + 1)].cgs_inf[11];
            frmMainFormIPIS.train_details[index1].cgs_inf[12] = frmMainFormIPIS.train_details[checked (index1 + 1)].cgs_inf[12];
            frmMainFormIPIS.train_details[index1].cgs_inf[13] = frmMainFormIPIS.train_details[checked (index1 + 1)].cgs_inf[13];
            frmMainFormIPIS.train_details[index1].cgs_inf[14] = frmMainFormIPIS.train_details[checked (index1 + 1)].cgs_inf[14];
            frmMainFormIPIS.train_details[index1].cgs_inf[15] = frmMainFormIPIS.train_details[checked (index1 + 1)].cgs_inf[15];
            frmMainFormIPIS.train_details[index1].cgs_inf[16 /*0x10*/] = frmMainFormIPIS.train_details[checked (index1 + 1)].cgs_inf[16 /*0x10*/];
            frmMainFormIPIS.train_details[index1].cgs_inf[17] = frmMainFormIPIS.train_details[checked (index1 + 1)].cgs_inf[17];
            frmMainFormIPIS.train_details[index1].cgs_inf[18] = frmMainFormIPIS.train_details[checked (index1 + 1)].cgs_inf[18];
            frmMainFormIPIS.train_details[index1].cgs_inf[19] = frmMainFormIPIS.train_details[checked (index1 + 1)].cgs_inf[19];
            frmMainFormIPIS.train_details[index1].cgs_inf[20] = frmMainFormIPIS.train_details[checked (index1 + 1)].cgs_inf[20];
            frmMainFormIPIS.train_details[index1].cgs_inf[21] = frmMainFormIPIS.train_details[checked (index1 + 1)].cgs_inf[21];
            frmMainFormIPIS.train_details[index1].cgs_inf[22] = frmMainFormIPIS.train_details[checked (index1 + 1)].cgs_inf[22];
            frmMainFormIPIS.train_details[index1].cgs_inf[23] = frmMainFormIPIS.train_details[checked (index1 + 1)].cgs_inf[23];
            frmMainFormIPIS.train_details[index1].cgs_inf[24] = frmMainFormIPIS.train_details[checked (index1 + 1)].cgs_inf[24];
            frmMainFormIPIS.train_details[index1].cgs_inf[25] = frmMainFormIPIS.train_details[checked (index1 + 1)].cgs_inf[25];
            checked { ++index1; }
          }
          frmMainFormIPIS.train_details[index1].arr_time = string.Empty;
          int index30 = 0;
          while (index30 < 26)
          {
            frmMainFormIPIS.train_details[index1].cgs_inf[index30] = string.Empty;
            checked { ++index30; }
          }
          frmMainFormIPIS.train_details[index1].daily = 0;
          int index31 = 0;
          while (index31 < 7)
          {
            frmMainFormIPIS.train_details[index1].days[index31] = string.Empty;
            checked { ++index31; }
          }
          frmMainFormIPIS.train_details[index1].dep_time = string.Empty;
          frmMainFormIPIS.train_details[index1].dir = string.Empty;
          frmMainFormIPIS.train_details[index1].end_Station = string.Empty;
          frmMainFormIPIS.train_details[index1].fromdt = DateTime.MinValue;
          frmMainFormIPIS.train_details[index1].language_name = string.Empty;
          frmMainFormIPIS.train_details[index1].period = 0;
          frmMainFormIPIS.train_details[index1].pf_no = string.Empty;
          frmMainFormIPIS.train_details[index1].specificdate = 0;
          int index32 = 0;
          while (index32 < 10)
          {
            frmMainFormIPIS.train_details[index1].specificdates[index32] = DateTime.MinValue;
            checked { ++index32; }
          }
          frmMainFormIPIS.train_details[index1].specificdays = 0;
          frmMainFormIPIS.train_details[index1].start_station = string.Empty;
          frmMainFormIPIS.train_details[index1].todt = DateTime.MinValue;
          frmMainFormIPIS.train_details[index1].train_name = string.Empty;
          frmMainFormIPIS.train_details[index1].train_name_hin = string.Empty;
          frmMainFormIPIS.train_details[index1].train_name_reg = string.Empty;
          frmMainFormIPIS.train_details[index1].train_no = string.Empty;
          checked { --frmMainFormIPIS.train_cnt; }
          int index33 = 0;
          lock (this)
          {
            while (index33 < frmMainFormIPIS.online_train_cnt)
            {
              if (Operators.CompareString(Strings.Trim(frmMainFormIPIS.online_train_data[index33].train_no), Strings.Trim(text), false) == 0)
              {
                while (index33 < frmMainFormIPIS.online_train_cnt)
                {
                  frmMainFormIPIS.online_train_data[index33] = frmMainFormIPIS.online_train_data[checked (index33 + 1)];
                  checked { ++index33; }
                }
                frmMainFormIPIS.online_train_data[index33].AD = string.Empty;
                frmMainFormIPIS.online_train_data[index33].announce_checked = false;
                int index34 = 0;
                while (index34 < 26)
                {
                  frmMainFormIPIS.online_train_data[index33].cgs_array_values[index34] = string.Empty;
                  checked { ++index34; }
                }
                frmMainFormIPIS.online_train_data[index33].display_checked = false;
                frmMainFormIPIS.online_train_data[index33].cgs_checked = false;
                frmMainFormIPIS.online_train_data[index33].exp_arr_time = string.Empty;
                frmMainFormIPIS.online_train_data[index33].exp_dep_time = string.Empty;
                frmMainFormIPIS.online_train_data[index33].late = string.Empty;
                frmMainFormIPIS.online_train_data[index33].pfno = string.Empty;
                frmMainFormIPIS.online_train_data[index33].sch_arr_time = string.Empty;
                frmMainFormIPIS.online_train_data[index33].sch_dep_time = string.Empty;
                frmMainFormIPIS.online_train_data[index33].sno = 0;
                frmMainFormIPIS.online_train_data[index33].station_pos = frmMainFormIPIS.online_train_data[checked (index33 + 1)].station_pos;
                frmMainFormIPIS.online_train_data[index33].train_name = string.Empty;
                frmMainFormIPIS.online_train_data[index33].train_name_hin = string.Empty;
                frmMainFormIPIS.online_train_data[index33].train_name_reg = string.Empty;
                frmMainFormIPIS.online_train_data[index33].train_no = string.Empty;
                frmMainFormIPIS.online_train_data[index33].train_status = string.Empty;
                frmMainFormIPIS.online_train_data[index33].cgs_array_modified = false;
                checked { --frmMainFormIPIS.online_train_cnt; }
                break;
              }
              checked { ++index33; }
            }
          }
          online_trains.update_dgv();
          this.btnDelete.Enabled = true;
          this.btnAdd.Enabled = true;
          this.btnEdit.Enabled = true;
          this.btnsave.Enabled = false;
          this.txtTrainName.Text = string.Empty;
          this.txtTrainNameReg.Text = string.Empty;
          this.txtTrainNameHin.Text = string.Empty;
          this.txtStartStn.Text = string.Empty;
          this.txtEndStn.Text = string.Empty;
          this.cmbArrTime.Text = string.Empty;
          this.cmbDepTime.Text = string.Empty;
          this.cmbPfno.Text = string.Empty;
          this.cmbTrainNo.Enabled = false;
          this.txtTrainName.Enabled = false;
          this.txtTrainNameReg.Enabled = false;
          this.txtTrainNameHin.Enabled = false;
          this.txtStartStn.Enabled = false;
          this.txtEndStn.Enabled = false;
          this.cmbArrTime.Enabled = false;
          this.cmbDepTime.Enabled = false;
          this.cmbPfno.Enabled = false;
          this.btnTrainDetails.Enabled = false;
          this.btnCgs.Enabled = false;
          this.cmbDir.Enabled = false;
          this.lblDir.Enabled = false;
          this.lblArrTime.Enabled = false;
          this.lblTrainNo.Enabled = false;
          this.lblTrainName.Enabled = false;
          this.lblSrtStn.Enabled = false;
          this.lblReg.Enabled = false;
          this.lblPfno.Enabled = false;
          this.lblHindi.Enabled = false;
          this.lblEnglish.Enabled = false;
          this.lblEndStn.Enabled = false;
          this.lblDir.Enabled = false;
          this.lblDepTime.Enabled = false;
          this.lblArrTime.Enabled = false;
          this.cmbStationPos.Enabled = false;
          this.lblStationPos.Enabled = false;
          this.cmbStationPos.Text = string.Empty;
        }
      }
      frmMainFormIPIS frmMainFormIpis = new frmMainFormIPIS();
      lock (this)
      {
        if (!frmMainFormIPIS.online_update)
          frmMainFormIpis.update_online_trains();
      }
      frmTrainConfig.train_details_enter = false;
      frmTrainConfig.cgs_form_enter = false;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(this.Handle.ToInt32(), exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    try
    {
      string str = "Z:\\Database\\train_db.mdb";
      string sourceFileName = "C:\\IPIS\\Database\\train_db.mdb";
      if (!File.Exists(str))
        File.Create(str);
      bool overwrite = true;
      MyProject.Computer.FileSystem.CopyFile(sourceFileName, str, overwrite);
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
  }

  private void btnsave_Click(object sender, EventArgs e)
  {
    bool flag1 = false;
    try
    {
      if (Operators.CompareString(this.cmbTrainNo.Text, "", false) == 0)
      {
        int num = (int) basMsgBoxEx.MsgBoxMove(this.Handle.ToInt32(), "Please Enter the Train No", "Msg Box", 0, 0, 0);
        return;
      }
      if (Operators.CompareString(this.txtTrainName.Text, "", false) == 0)
      {
        int num = (int) basMsgBoxEx.MsgBoxMove(this.Handle.ToInt32(), "Please Enter the Train Name", "Msg Box", 0, 0, 0);
        return;
      }
      int index1 = 0;
      while (index1 < this.cmbTrainNo.Text.Length)
      {
        if (char.IsLower(this.cmbTrainNo.Text[index1]))
        {
          int num = (int) basMsgBoxEx.MsgBoxMove(this.Handle.ToInt32(), "Please enter Numbers and Capital Letters only in Train Name", "Msg Box", 0, 0, 0);
          return;
        }
        checked { ++index1; }
      }
      int index2 = 0;
      if (Operators.CompareString(this.cmbPfno.Text, "", false) == 0)
      {
        int num = (int) basMsgBoxEx.MsgBoxMove(this.Handle.ToInt32(), "Please Enter the Platform No", "Msg Box", 0, 0, 0);
        return;
      }
      while (index2 < frmMainFormIPIS.pfno_cnt)
      {
        if (Operators.CompareString(Strings.Trim(this.cmbPfno.Text), Strings.Trim(frmMainFormIPIS.platform_nos[index2]), false) == 0)
        {
          flag1 = true;
          break;
        }
        checked { ++index2; }
      }
      if (!flag1)
      {
        int num = (int) basMsgBoxEx.MsgBoxMove(this.Handle.ToInt32(), "Select Pfno from the List", "Msg Box", 0, 0, 0);
        return;
      }
      bool flag2 = false;
      if (Operators.CompareString(this.txtTrainNameHin.Text, "", false) == 0)
      {
        int num = (int) basMsgBoxEx.MsgBoxMove(this.Handle.ToInt32(), "Please Enter the Train Name in Hindi", "Msg Box", 0, 0, 0);
        return;
      }
      network_db_read.get_language_details();
      if (frmMainFormIPIS.language_selection.regional_language_selected)
      {
        if (Operators.CompareString(this.txtTrainNameReg.Text, "", false) == 0)
        {
          int num = (int) basMsgBoxEx.MsgBoxMove(this.Handle.ToInt32(), "Please Enter the Train Name in Regional Language", "Msg Box", 0, 0, 0);
          return;
        }
        this.txtTrainNameReg.Visible = true;
      }
      else
      {
        this.txtTrainNameReg.Text = string.Empty;
        this.txtTrainNameReg.Visible = false;
      }
      if (Operators.CompareString(this.txtStartStn.Text, "", false) == 0)
      {
        int num = (int) basMsgBoxEx.MsgBoxMove(this.Handle.ToInt32(), "Please Enter the Starting  Station", "Msg Box", 0, 0, 0);
        return;
      }
      if (Operators.CompareString(this.txtEndStn.Text, "", false) == 0)
      {
        int num = (int) basMsgBoxEx.MsgBoxMove(this.Handle.ToInt32(), "Please Enter the END  Station", "Msg Box", 0, 0, 0);
        return;
      }
      if (Operators.CompareString(this.cmbArrTime.Text, "", false) == 0)
      {
        int num = (int) basMsgBoxEx.MsgBoxMove(this.Handle.ToInt32(), "Please Enter the Arrival Time", "Msg Box", 0, 0, 0);
        return;
      }
      if (Operators.CompareString(this.cmbDepTime.Text, "", false) == 0)
      {
        int num = (int) basMsgBoxEx.MsgBoxMove(this.Handle.ToInt32(), "Please Enter the Departure Time", "Msg Box", 0, 0, 0);
        return;
      }
      if (Operators.CompareString(this.cmbDir.Text, "", false) == 0)
      {
        int num = (int) basMsgBoxEx.MsgBoxMove(this.Handle.ToInt32(), "Please Enter the Direction", "Msg Box", 0, 0, 0);
        return;
      }
      if (Operators.CompareString(this.cmbStationPos.Text, "", false) == 0)
      {
        int num = (int) basMsgBoxEx.MsgBoxMove(this.Handle.ToInt32(), "Please select the Start Position  ", "Msg Box", 0, 0, 0);
        return;
      }
      if (this.btnAdd.Enabled)
      {
        int index3 = 0;
        while (index3 < frmMainFormIPIS.train_cnt)
        {
          if (Operators.CompareString(this.cmbTrainNo.Text, frmMainFormIPIS.train_details[index3].train_no, false) == 0)
          {
            int num = (int) basMsgBoxEx.MsgBoxMove(this.Handle.ToInt32(), "Train No {this.cmbTrainNo.Text} is already stored in the database\r\nGive a new train no or edit the train details ", "Msg Box", 0, 0, 0);
            goto label_72;
          }
          checked { ++index3; }
        }
        int trainCnt = frmMainFormIPIS.train_cnt;
        frmMainFormIPIS.train_details[trainCnt].train_no = this.cmbTrainNo.Text;
        frmMainFormIPIS.train_details[trainCnt].train_name = this.txtTrainName.Text;
        frmMainFormIPIS.train_details[trainCnt].train_name_reg = this.txtTrainNameReg.Text;
        frmMainFormIPIS.train_details[trainCnt].train_name_hin = this.txtTrainNameHin.Text;
        frmMainFormIPIS.train_details[trainCnt].start_station = this.txtStartStn.Text;
        frmMainFormIPIS.train_details[trainCnt].end_Station = this.txtEndStn.Text;
        frmMainFormIPIS.train_details[trainCnt].arr_time = this.cmbArrTime.Text;
        frmMainFormIPIS.train_details[trainCnt].dep_time = this.cmbDepTime.Text;
        frmMainFormIPIS.train_details[trainCnt].pf_no = this.cmbPfno.Text;
        frmMainFormIPIS.train_details[trainCnt].language_name = this.txtRegLang.Text;
        frmMainFormIPIS.train_details[trainCnt].dir = this.cmbDir.Text;
        frmMainFormIPIS.train_details[trainCnt].station_pos = this.cmbStationPos.Text;
        frmMainFormIPIS.train_details[trainCnt].daily = this.myform.dailydays;
        frmMainFormIPIS.train_details[trainCnt].specificdays = this.myform.specificdays;
        frmMainFormIPIS.train_details[trainCnt].days[0] = this.myform.Sunday;
        frmMainFormIPIS.train_details[trainCnt].days[1] = this.myform.Monday;
        frmMainFormIPIS.train_details[trainCnt].days[2] = this.myform.Tuesday;
        frmMainFormIPIS.train_details[trainCnt].days[3] = this.myform.Wednesday;
        frmMainFormIPIS.train_details[trainCnt].days[4] = this.myform.Thursday;
        frmMainFormIPIS.train_details[trainCnt].days[5] = this.myform.Friday;
        frmMainFormIPIS.train_details[trainCnt].days[6] = this.myform.Saturday;
        frmMainFormIPIS.train_details[trainCnt].period = this.myform.perioddates;
        frmMainFormIPIS.train_details[trainCnt].fromdt = this.myform.fdate;
        frmMainFormIPIS.train_details[trainCnt].todt = this.myform.tdate;
        frmMainFormIPIS.train_details[trainCnt].specificdate = this.myform.specificdates;
        frmMainFormIPIS.train_details[trainCnt].specificdates[0] = this.myform.spdate1;
        frmMainFormIPIS.train_details[trainCnt].specificdates[1] = this.myform.spdate2;
        frmMainFormIPIS.train_details[trainCnt].specificdates[2] = this.myform.spdate3;
        frmMainFormIPIS.train_details[trainCnt].specificdates[3] = this.myform.spdate4;
        frmMainFormIPIS.train_details[trainCnt].specificdates[4] = this.myform.spdate5;
        frmMainFormIPIS.train_details[trainCnt].specificdates[5] = this.myform.spdate6;
        frmMainFormIPIS.train_details[trainCnt].specificdates[6] = this.myform.spdate7;
        frmMainFormIPIS.train_details[trainCnt].specificdates[7] = this.myform.spdate8;
        frmMainFormIPIS.train_details[trainCnt].specificdates[8] = this.myform.spdate9;
        frmMainFormIPIS.train_details[trainCnt].specificdates[9] = this.myform.spdate10;
        frmMainFormIPIS.train_details[trainCnt].cgs_inf[0] = this.cgsfrm.TT1;
        frmMainFormIPIS.train_details[trainCnt].cgs_inf[1] = this.cgsfrm.TT2;
        frmMainFormIPIS.train_details[trainCnt].cgs_inf[2] = this.cgsfrm.TT3;
        frmMainFormIPIS.train_details[trainCnt].cgs_inf[3] = this.cgsfrm.TT4;
        frmMainFormIPIS.train_details[trainCnt].cgs_inf[4] = this.cgsfrm.TT5;
        frmMainFormIPIS.train_details[trainCnt].cgs_inf[5] = this.cgsfrm.TT6;
        frmMainFormIPIS.train_details[trainCnt].cgs_inf[6] = this.cgsfrm.TT7;
        frmMainFormIPIS.train_details[trainCnt].cgs_inf[7] = this.cgsfrm.TT8;
        frmMainFormIPIS.train_details[trainCnt].cgs_inf[8] = this.cgsfrm.TT9;
        frmMainFormIPIS.train_details[trainCnt].cgs_inf[9] = this.cgsfrm.TT10;
        frmMainFormIPIS.train_details[trainCnt].cgs_inf[10] = this.cgsfrm.TT11;
        frmMainFormIPIS.train_details[trainCnt].cgs_inf[11] = this.cgsfrm.TT12;
        frmMainFormIPIS.train_details[trainCnt].cgs_inf[12] = this.cgsfrm.TT13;
        frmMainFormIPIS.train_details[trainCnt].cgs_inf[13] = this.cgsfrm.TT14;
        frmMainFormIPIS.train_details[trainCnt].cgs_inf[14] = this.cgsfrm.TT15;
        frmMainFormIPIS.train_details[trainCnt].cgs_inf[15] = this.cgsfrm.TT16;
        frmMainFormIPIS.train_details[trainCnt].cgs_inf[16 /*0x10*/] = this.cgsfrm.TT17;
        frmMainFormIPIS.train_details[trainCnt].cgs_inf[17] = this.cgsfrm.TT18;
        frmMainFormIPIS.train_details[trainCnt].cgs_inf[18] = this.cgsfrm.TT19;
        frmMainFormIPIS.train_details[trainCnt].cgs_inf[19] = this.cgsfrm.TT20;
        frmMainFormIPIS.train_details[trainCnt].cgs_inf[20] = this.cgsfrm.TT21;
        frmMainFormIPIS.train_details[trainCnt].cgs_inf[21] = this.cgsfrm.TT22;
        frmMainFormIPIS.train_details[trainCnt].cgs_inf[22] = this.cgsfrm.TT23;
        frmMainFormIPIS.train_details[trainCnt].cgs_inf[23] = this.cgsfrm.TT24;
        frmMainFormIPIS.train_details[trainCnt].cgs_inf[24] = this.cgsfrm.TT25;
        frmMainFormIPIS.train_details[trainCnt].cgs_inf[25] = this.cgsfrm.TT26;
        network_db_read.set_train_info();
        this.cmbTrainNo.Items.Add((object) this.cmbTrainNo.Text);
      }
      else if (this.btnEdit.Enabled)
      {
        if (Operators.CompareString(this.cmbTrainNo.Text, "", false) == 0)
          return;
        int i = 0;
        while (i < frmMainFormIPIS.train_cnt && Operators.CompareString(this.cmbTrainNo.Text, frmMainFormIPIS.train_details[i].train_no, false) != 0)
          checked { ++i; }
        frmMainFormIPIS.train_details[i].train_name = this.txtTrainName.Text;
        frmMainFormIPIS.train_details[i].train_name_reg = this.txtTrainNameReg.Text;
        frmMainFormIPIS.train_details[i].train_name_hin = this.txtTrainNameHin.Text;
        frmMainFormIPIS.train_details[i].start_station = this.txtStartStn.Text;
        frmMainFormIPIS.train_details[i].end_Station = this.txtEndStn.Text;
        frmMainFormIPIS.train_details[i].arr_time = this.cmbArrTime.Text;
        frmMainFormIPIS.train_details[i].dep_time = this.cmbDepTime.Text;
        frmMainFormIPIS.train_details[i].pf_no = this.cmbPfno.Text;
        frmMainFormIPIS.train_details[i].language_name = this.txtRegLang.Text;
        frmMainFormIPIS.train_details[i].dir = this.cmbDir.Text;
        frmMainFormIPIS.train_details[i].station_pos = this.cmbStationPos.Text;
        if (frmTrainConfig.train_details_enter)
        {
          frmMainFormIPIS.train_details[i].daily = this.myform.dailydays;
          frmMainFormIPIS.train_details[i].specificdays = this.myform.specificdays;
          frmMainFormIPIS.train_details[i].days[0] = this.myform.Sunday;
          frmMainFormIPIS.train_details[i].days[1] = this.myform.Monday;
          frmMainFormIPIS.train_details[i].days[2] = this.myform.Tuesday;
          frmMainFormIPIS.train_details[i].days[3] = this.myform.Wednesday;
          frmMainFormIPIS.train_details[i].days[4] = this.myform.Thursday;
          frmMainFormIPIS.train_details[i].days[5] = this.myform.Friday;
          frmMainFormIPIS.train_details[i].days[6] = this.myform.Saturday;
          frmMainFormIPIS.train_details[i].fromdt = DateTime.Compare(this.myform.fdate, DateTime.MinValue) != 0 ? this.myform.fdate : DateTime.MinValue;
          frmMainFormIPIS.train_details[i].todt = DateTime.Compare(this.myform.tdate, DateTime.MinValue) != 0 ? this.myform.tdate : DateTime.MinValue;
          frmMainFormIPIS.train_details[i].period = this.myform.perioddates;
          frmMainFormIPIS.train_details[i].specificdate = this.myform.specificdates;
          frmMainFormIPIS.train_details[i].specificdates[0] = frmTrainDetails.spdate[0];
          frmMainFormIPIS.train_details[i].specificdates[1] = frmTrainDetails.spdate[1];
          frmMainFormIPIS.train_details[i].specificdates[2] = frmTrainDetails.spdate[2];
          frmMainFormIPIS.train_details[i].specificdates[3] = frmTrainDetails.spdate[3];
          frmMainFormIPIS.train_details[i].specificdates[4] = frmTrainDetails.spdate[4];
          frmMainFormIPIS.train_details[i].specificdates[5] = frmTrainDetails.spdate[5];
          frmMainFormIPIS.train_details[i].specificdates[6] = frmTrainDetails.spdate[6];
          frmMainFormIPIS.train_details[i].specificdates[7] = frmTrainDetails.spdate[7];
          frmMainFormIPIS.train_details[i].specificdates[8] = frmTrainDetails.spdate[8];
          frmMainFormIPIS.train_details[i].specificdates[9] = frmTrainDetails.spdate[9];
        }
        if (frmTrainConfig.cgs_form_enter)
        {
          frmMainFormIPIS.train_details[i].cgs_inf[0] = this.cgsfrm.TT1;
          frmMainFormIPIS.train_details[i].cgs_inf[1] = this.cgsfrm.TT2;
          frmMainFormIPIS.train_details[i].cgs_inf[2] = this.cgsfrm.TT3;
          frmMainFormIPIS.train_details[i].cgs_inf[3] = this.cgsfrm.TT4;
          frmMainFormIPIS.train_details[i].cgs_inf[4] = this.cgsfrm.TT5;
          frmMainFormIPIS.train_details[i].cgs_inf[5] = this.cgsfrm.TT6;
          frmMainFormIPIS.train_details[i].cgs_inf[6] = this.cgsfrm.TT7;
          frmMainFormIPIS.train_details[i].cgs_inf[7] = this.cgsfrm.TT8;
          frmMainFormIPIS.train_details[i].cgs_inf[8] = this.cgsfrm.TT9;
          frmMainFormIPIS.train_details[i].cgs_inf[9] = this.cgsfrm.TT10;
          frmMainFormIPIS.train_details[i].cgs_inf[10] = this.cgsfrm.TT11;
          frmMainFormIPIS.train_details[i].cgs_inf[11] = this.cgsfrm.TT12;
          frmMainFormIPIS.train_details[i].cgs_inf[12] = this.cgsfrm.TT13;
          frmMainFormIPIS.train_details[i].cgs_inf[13] = this.cgsfrm.TT14;
          frmMainFormIPIS.train_details[i].cgs_inf[14] = this.cgsfrm.TT15;
          frmMainFormIPIS.train_details[i].cgs_inf[15] = this.cgsfrm.TT16;
          frmMainFormIPIS.train_details[i].cgs_inf[16 /*0x10*/] = this.cgsfrm.TT17;
          frmMainFormIPIS.train_details[i].cgs_inf[17] = this.cgsfrm.TT18;
          frmMainFormIPIS.train_details[i].cgs_inf[18] = this.cgsfrm.TT19;
          frmMainFormIPIS.train_details[i].cgs_inf[19] = this.cgsfrm.TT20;
          frmMainFormIPIS.train_details[i].cgs_inf[20] = this.cgsfrm.TT21;
          frmMainFormIPIS.train_details[i].cgs_inf[21] = this.cgsfrm.TT22;
          frmMainFormIPIS.train_details[i].cgs_inf[22] = this.cgsfrm.TT23;
          frmMainFormIPIS.train_details[i].cgs_inf[23] = this.cgsfrm.TT24;
          frmMainFormIPIS.train_details[i].cgs_inf[24] = this.cgsfrm.TT25;
          frmMainFormIPIS.train_details[i].cgs_inf[25] = this.cgsfrm.TT26;
        }
        int index4 = 0;
        bool flag3 = false;
        while (index4 < frmMainFormIPIS.online_train_cnt)
        {
          if (Operators.CompareString(frmMainFormIPIS.train_details[i].train_no, frmMainFormIPIS.online_train_data[index4].train_no, false) == 0)
          {
            flag3 = true;
            break;
          }
          checked { ++index4; }
        }
        if (flag3)
        {
          int index5 = 0;
          while (index5 < 26)
          {
            frmMainFormIPIS.online_train_data[index4].cgs_array_values[index5] = frmMainFormIPIS.train_details[i].cgs_inf[index5];
            checked { ++index5; }
          }
          frmMainFormIPIS.online_train_data[index4].exp_arr_time = frmMainFormIPIS.train_details[i].arr_time;
          frmMainFormIPIS.online_train_data[index4].exp_dep_time = frmMainFormIPIS.train_details[i].dep_time;
          int index6 = 0;
          while (index6 < frmMainFormIPIS.pfno_cnt)
          {
            if (Operators.CompareString(Strings.Trim(frmMainFormIPIS.platform_nos[index6]), Strings.Trim(frmMainFormIPIS.train_details[i].pf_no), false) == 0)
            {
              frmMainFormIPIS.online_train_data[index4].pfno = frmMainFormIPIS.train_details[i].pf_no;
              flag2 = true;
              break;
            }
            checked { ++index6; }
          }
          if (!flag2)
            frmMainFormIPIS.online_train_data[index4].pfno = string.Empty;
          frmMainFormIPIS.online_train_data[index4].sch_arr_time = frmMainFormIPIS.train_details[i].arr_time;
          frmMainFormIPIS.online_train_data[index4].sch_dep_time = frmMainFormIPIS.train_details[i].dep_time;
          frmMainFormIPIS.online_train_data[index4].train_name = frmMainFormIPIS.train_details[i].train_name;
          frmMainFormIPIS.online_train_data[index4].train_name_hin = frmMainFormIPIS.train_details[i].train_name_hin;
          frmMainFormIPIS.online_train_data[index4].train_name_reg = frmMainFormIPIS.train_details[i].train_name_reg;
          frmMainFormIPIS.online_train_data[index4].train_no = frmMainFormIPIS.train_details[i].train_no;
          frmMainFormIPIS.online_train_data[index4].station_pos = frmMainFormIPIS.train_details[i].station_pos;
          online_trains.update_dgv();
        }
        network_db_read.update_train_info(i);
      }
label_72:
      this.txtTrainName.Text = string.Empty;
      this.txtTrainNameReg.Text = string.Empty;
      this.txtTrainNameHin.Text = string.Empty;
      this.txtStartStn.Text = string.Empty;
      this.txtEndStn.Text = string.Empty;
      this.cmbArrTime.Text = string.Empty;
      this.cmbDepTime.Text = string.Empty;
      this.cmbDir.Text = string.Empty;
      this.cmbPfno.Text = string.Empty;
      this.cmbTrainNo.Enabled = false;
      this.txtTrainName.Enabled = false;
      this.txtTrainNameReg.Enabled = false;
      this.txtTrainNameHin.Enabled = false;
      this.txtStartStn.Enabled = false;
      this.txtEndStn.Enabled = false;
      this.cmbArrTime.Enabled = false;
      this.cmbDepTime.Enabled = false;
      this.cmbPfno.Enabled = false;
      this.btnTrainDetails.Enabled = false;
      this.btnCgs.Enabled = false;
      this.cmbDir.Enabled = false;
      this.lblDir.Enabled = false;
      this.lblArrTime.Enabled = false;
      this.lblTrainNo.Enabled = false;
      this.lblTrainName.Enabled = false;
      this.lblSrtStn.Enabled = false;
      this.lblReg.Enabled = false;
      this.lblPfno.Enabled = false;
      this.lblHindi.Enabled = false;
      this.lblEnglish.Enabled = false;
      this.lblEndStn.Enabled = false;
      this.lblDir.Enabled = false;
      this.lblDepTime.Enabled = false;
      this.lblArrTime.Enabled = false;
      this.cmbStationPos.Enabled = false;
      this.lblStationPos.Enabled = false;
      this.cmbStationPos.Text = string.Empty;
      this.btnsave.Enabled = false;
      this.btnDelete.Enabled = true;
      this.btnAdd.Enabled = true;
      this.btnEdit.Enabled = true;
      frmTrainConfig.train_details_enter = false;
      frmTrainConfig.cgs_form_enter = false;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(this.Handle.ToInt32(), exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    try
    {
      string str = "Z:\\Database\\train_db.mdb";
      string sourceFileName = "C:\\IPIS\\Database\\train_db.mdb";
      if (!File.Exists(str))
        File.Create(str);
      bool overwrite = true;
      MyProject.Computer.FileSystem.CopyFile(sourceFileName, str, overwrite);
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
  }

  private void btnExit_Click(object sender, EventArgs e)
{
  this.Close();
}

  public void btnTrainDetails_Click(object sender, EventArgs e)
  {
    try
    {
      if (this.btnAdd.Enabled)
      {
        if (!Information.IsNothing((object) this.event_traindetails))
        {
          if (!this.event_traindetails.IsDisposed)
          {
            this.event_traindetails.WindowState = FormWindowState.Normal;
            this.event_traindetails.BringToFront();
          }
          else
          {
            this.event_traindetails = new frmTrainDetails();
            this.event_traindetails.Show();
          }
        }
        else
        {
          this.event_traindetails = new frmTrainDetails();
          this.event_traindetails.Show();
        }
      }
      else
      {
        frmTrainDetails frmTrainDetails = new frmTrainDetails();
        int index1 = 0;
        if (Operators.CompareString(this.cmbTrainNo.Text, "", false) != 0)
        {
          while (index1 < frmMainFormIPIS.train_cnt && Operators.CompareString(this.cmbTrainNo.Text, frmMainFormIPIS.train_details[index1].train_no, false) != 0)
            checked { ++index1; }
        }
        frmTrainConfig.send_rowno_train_details = index1;
        if (frmMainFormIPIS.train_details[index1].daily == 1)
          frmTrainDetails.radDaily.Checked = true;
        else if (frmMainFormIPIS.train_details[index1].specificdays == 1)
        {
          frmTrainDetails.radSpecificDaysWeek.Checked = true;
          if (Operators.CompareString(Strings.Trim(frmMainFormIPIS.train_details[index1].days[0]), "Sunday", false) == 0)
            frmTrainDetails.chklstWeekDays.SetItemChecked(0, true);
          else
            frmTrainDetails.chklstWeekDays.SetItemChecked(0, false);
          if (Operators.CompareString(Strings.Trim(frmMainFormIPIS.train_details[index1].days[1]), "Monday", false) == 0)
            frmTrainDetails.chklstWeekDays.SetItemChecked(1, true);
          else
            frmTrainDetails.chklstWeekDays.SetItemChecked(1, false);
          if (Operators.CompareString(Strings.Trim(frmMainFormIPIS.train_details[index1].days[2]), "Tuesday", false) == 0)
            frmTrainDetails.chklstWeekDays.SetItemChecked(2, true);
          else
            frmTrainDetails.chklstWeekDays.SetItemChecked(2, false);
          if (Operators.CompareString(Strings.Trim(frmMainFormIPIS.train_details[index1].days[3]), "Wednesday", false) == 0)
            frmTrainDetails.chklstWeekDays.SetItemChecked(3, true);
          else
            frmTrainDetails.chklstWeekDays.SetItemChecked(3, false);
          if (Operators.CompareString(Strings.Trim(frmMainFormIPIS.train_details[index1].days[4]), "Thursday", false) == 0)
            frmTrainDetails.chklstWeekDays.SetItemChecked(4, true);
          else
            frmTrainDetails.chklstWeekDays.SetItemChecked(4, false);
          if (Operators.CompareString(Strings.Trim(frmMainFormIPIS.train_details[index1].days[5]), "Friday", false) == 0)
            frmTrainDetails.chklstWeekDays.SetItemChecked(5, true);
          else
            frmTrainDetails.chklstWeekDays.SetItemChecked(5, false);
          if (Operators.CompareString(Strings.Trim(frmMainFormIPIS.train_details[index1].days[6]), "Saturday", false) == 0)
            frmTrainDetails.chklstWeekDays.SetItemChecked(6, true);
          else
            frmTrainDetails.chklstWeekDays.SetItemChecked(6, false);
        }
        else if (frmMainFormIPIS.train_details[index1].period == 1)
        {
          frmTrainDetails.radPeriod.Checked = true;
          frmTrainDetails.datFrom.Text = Conversions.ToString(frmMainFormIPIS.train_details[index1].fromdt);
          frmTrainDetails.datTo.Text = Conversions.ToString(frmMainFormIPIS.train_details[index1].todt);
        }
        else if (frmMainFormIPIS.train_details[index1].specificdate == 1)
        {
          frmTrainDetails.radSpecificDates.Checked = true;
          byte index2 = 0;
          frmTrainDetails.lstboxSpecificDates.Items.Clear();
          while (index2 < (byte) 10)
          {
            if (DateTime.Compare(frmMainFormIPIS.train_details[index1].specificdates[(int) index2], DateTime.MinValue) != 0 && DateTime.Compare(frmMainFormIPIS.train_details[index1].specificdates[(int) index2], new DateTime(599264352000000000L)) != 0)
              frmTrainDetails.lstboxSpecificDates.Items.Add((object) frmMainFormIPIS.train_details[index1].specificdates[(int) index2]);
            checked { ++index2; }
          }
        }
        frmTrainDetails.Show();
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(this.Handle.ToInt32(), exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void frmTrainConfig_FormClosed(object sender, FormClosedEventArgs e)
  {
    MyProject.Forms.frmMainFormIPIS.BringToFront();
  }

  public void addtrain_Load(object sender, EventArgs e)
  {
    int num1 = 0;
    int num2 = 0;
    int index1 = 0;
    try
    {
      while (index1 < frmMainFormIPIS.train_cnt)
      {
        this.cmbTrainNo.Items.Add((object) frmMainFormIPIS.train_details[index1].train_no);
        checked { ++index1; }
      }
      int index2 = 0;
      while (index2 < frmMainFormIPIS.pfno_cnt)
      {
        this.cmbPfno.Items.Add((object) frmMainFormIPIS.platform_nos[index2]);
        checked { ++index2; }
      }
      int index3 = 0;
      while (num1 < 24)
      {
        int num3 = 0;
        while (num3 < 60)
        {
          DateTime Expression = DateAndTime.TimeValue("{num1.ToString()}:{num3.ToString()}:{num2.ToString()}");
          this.cmbArrTime.Items.Insert(index3, (object) Strings.Format((object) Expression, "HH:mm"));
          this.cmbDepTime.Items.Insert(index3, (object) Strings.Format((object) Expression, "HH:mm"));
          checked { ++num3; }
          checked { ++index3; }
        }
        checked { ++num1; }
      }
      this.cmbTrainNo.Text = string.Empty;
      this.txtTrainName.Text = string.Empty;
      this.txtTrainNameReg.Text = string.Empty;
      this.txtTrainNameHin.Text = string.Empty;
      this.txtStartStn.Text = string.Empty;
      this.txtEndStn.Text = string.Empty;
      this.cmbArrTime.Text = string.Empty;
      this.cmbDepTime.Text = string.Empty;
      this.cmbStationPos.Text = string.Empty;
      network_db_read.get_language_details();
      if (frmMainFormIPIS.language_selection.regional_language_selected)
      {
        this.txtTrainNameReg.Visible = true;
        this.txtRegLang.Visible = true;
        this.txtRegLang.Text = frmMainFormIPIS.language_selection.regional_language_name;
        this.txtRegLang.ReadOnly = true;
      }
      else
      {
        this.txtRegLang.Visible = false;
        this.txtTrainNameReg.Visible = false;
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num4 = (int) basMsgBoxEx.MsgBoxMove(this.Handle.ToInt32(), exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void btnDelete_Click(object sender, EventArgs e)
  {
    this.btnsave.Enabled = false;
    this.btnDelete.Enabled = true;
    this.btnAdd.Enabled = false;
    this.btnEdit.Enabled = false;
    this.cmbTrainNo.Enabled = true;
    this.txtTrainName.Enabled = true;
    if (frmMainFormIPIS.language_selection.regional_language_selected)
    {
      this.txtTrainNameReg.Text = string.Empty;
      this.txtTrainNameReg.Visible = true;
    }
    this.txtTrainNameHin.Enabled = true;
    this.txtStartStn.Enabled = true;
    this.txtEndStn.Enabled = true;
    this.cmbArrTime.Enabled = true;
    this.cmbDepTime.Enabled = true;
    this.cmbPfno.Enabled = true;
    this.btnTrainDetails.Enabled = true;
    this.btnCgs.Enabled = true;
    this.cmbDir.Enabled = true;
    this.cmbStationPos.Enabled = true;
    this.lblDir.Enabled = true;
    this.lblArrTime.Enabled = true;
    this.lblTrainNo.Enabled = true;
    this.lblTrainName.Enabled = true;
    this.lblSrtStn.Enabled = true;
    this.lblReg.Enabled = true;
    this.lblPfno.Enabled = true;
    this.lblHindi.Enabled = true;
    this.lblEnglish.Enabled = true;
    this.lblEndStn.Enabled = true;
    this.lblDir.Enabled = true;
    this.lblDepTime.Enabled = true;
    this.lblArrTime.Enabled = true;
    this.lblStationPos.Enabled = true;
  }

  private void btnEdit_Click(object sender, EventArgs e)
  {
    this.btnDelete.Enabled = false;
    this.cmbTrainNo.Enabled = true;
    frmTrainDetails frmTrainDetails = new frmTrainDetails();
    this.cmbTrainNo.Enabled = true;
    this.txtTrainName.Enabled = true;
    if (frmMainFormIPIS.language_selection.regional_language_selected)
    {
      this.txtTrainNameReg.Enabled = true;
      this.txtTrainNameReg.Visible = true;
    }
    else
    {
      this.txtTrainNameReg.Text = string.Empty;
      this.txtTrainNameReg.Visible = false;
    }
    this.txtTrainNameHin.Enabled = true;
    this.txtStartStn.Enabled = true;
    this.txtEndStn.Enabled = true;
    this.cmbArrTime.Enabled = true;
    this.cmbDepTime.Enabled = true;
    this.cmbPfno.Enabled = true;
    this.btnTrainDetails.Enabled = true;
    this.btnCgs.Enabled = true;
    this.cmbDir.Enabled = true;
    this.cmbStationPos.Enabled = true;
    this.lblStationPos.Enabled = true;
    this.lblDir.Enabled = true;
    this.lblArrTime.Enabled = true;
    this.lblTrainNo.Enabled = true;
    this.lblTrainName.Enabled = true;
    this.lblSrtStn.Enabled = true;
    this.lblReg.Enabled = true;
    this.lblPfno.Enabled = true;
    this.lblHindi.Enabled = true;
    this.lblEnglish.Enabled = true;
    this.lblEndStn.Enabled = true;
    this.lblDir.Enabled = true;
    this.lblDepTime.Enabled = true;
    this.lblArrTime.Enabled = true;
    this.btnAdd.Enabled = false;
    this.btnsave.Enabled = true;
    this.btnEdit.Enabled = true;
    this.btnDelete.Enabled = false;
  }

  private void btnAdd_Click(object sender, EventArgs e)
  {
    this.btnsave.Enabled = true;
    this.btnDelete.Enabled = false;
    this.btnEdit.Enabled = false;
    this.btnAdd.Enabled = true;
    this.cmbTrainNo.Enabled = true;
    this.txtTrainName.Enabled = true;
    if (frmMainFormIPIS.language_selection.regional_language_selected)
    {
      this.txtTrainNameReg.Text = string.Empty;
      this.txtTrainNameReg.Visible = true;
      this.txtTrainNameReg.Enabled = true;
    }
    else
    {
      this.txtTrainNameReg.Text = string.Empty;
      this.txtTrainNameReg.Visible = false;
    }
    this.txtTrainNameHin.Enabled = true;
    this.txtStartStn.Enabled = true;
    this.txtEndStn.Enabled = true;
    this.cmbArrTime.Enabled = true;
    this.cmbDepTime.Enabled = true;
    this.cmbPfno.Enabled = true;
    this.btnTrainDetails.Enabled = true;
    this.btnCgs.Enabled = true;
    this.cmbDir.Enabled = true;
    this.cmbStationPos.Enabled = true;
    this.lblDir.Enabled = true;
    this.lblArrTime.Enabled = true;
    this.lblTrainNo.Enabled = true;
    this.lblTrainName.Enabled = true;
    this.lblSrtStn.Enabled = true;
    this.lblReg.Enabled = true;
    this.lblPfno.Enabled = true;
    this.lblHindi.Enabled = true;
    this.lblEnglish.Enabled = true;
    this.lblEndStn.Enabled = true;
    this.lblDir.Enabled = true;
    this.lblDepTime.Enabled = true;
    this.lblArrTime.Enabled = true;
    this.lblStationPos.Enabled = true;
    this.cmbTrainNo.Text = string.Empty;
    this.txtTrainName.Text = string.Empty;
    this.txtTrainNameReg.Text = string.Empty;
    this.txtTrainNameHin.Text = string.Empty;
    this.txtStartStn.Text = string.Empty;
    this.txtEndStn.Text = string.Empty;
    this.cmbArrTime.Text = string.Empty;
    this.cmbDepTime.Text = string.Empty;
    this.cmbPfno.Text = string.Empty;
    this.cmbStationPos.Text = string.Empty;
  }

  private void btnCGS_Click(object sender, EventArgs e)
  {
    try
    {
      if (this.btnAdd.Enabled)
      {
        if (!Information.IsNothing((object) this.event_cgs))
        {
          if (!this.event_cgs.IsDisposed)
          {
            this.event_cgs.WindowState = FormWindowState.Normal;
            this.event_cgs.BringToFront();
          }
          else
          {
            this.event_cgs = new frmCgs();
            this.event_cgs.Show();
          }
        }
        else
        {
          this.event_cgs = new frmCgs();
          this.event_cgs.Show();
        }
      }
      else
      {
        int num = 0;
        frmCgs frmCgs = new frmCgs();
        num = 0;
        byte index1 = 0;
        if (Operators.CompareString(this.cmbTrainNo.Text, "", false) != 0)
        {
          while ((int) index1 < frmMainFormIPIS.train_cnt && Operators.CompareString(this.cmbTrainNo.Text, frmMainFormIPIS.train_details[(int) index1].train_no, false) != 0)
            checked { ++index1; }
        }
        int index2 = 0;
        frmCgs.t1.Text = frmMainFormIPIS.train_details[(int) index1].cgs_inf[index2];
        int index3 = checked (index2 + 1);
        frmCgs.t2.Text = frmMainFormIPIS.train_details[(int) index1].cgs_inf[index3];
        int index4 = checked (index3 + 1);
        frmCgs.t3.Text = frmMainFormIPIS.train_details[(int) index1].cgs_inf[index4];
        int index5 = checked (index4 + 1);
        frmCgs.t4.Text = frmMainFormIPIS.train_details[(int) index1].cgs_inf[index5];
        int index6 = checked (index5 + 1);
        frmCgs.t5.Text = frmMainFormIPIS.train_details[(int) index1].cgs_inf[index6];
        int index7 = checked (index6 + 1);
        frmCgs.t6.Text = frmMainFormIPIS.train_details[(int) index1].cgs_inf[index7];
        int index8 = checked (index7 + 1);
        frmCgs.t7.Text = frmMainFormIPIS.train_details[(int) index1].cgs_inf[index8];
        int index9 = checked (index8 + 1);
        frmCgs.t8.Text = frmMainFormIPIS.train_details[(int) index1].cgs_inf[index9];
        int index10 = checked (index9 + 1);
        frmCgs.t9.Text = frmMainFormIPIS.train_details[(int) index1].cgs_inf[index10];
        int index11 = checked (index10 + 1);
        frmCgs.t10.Text = frmMainFormIPIS.train_details[(int) index1].cgs_inf[index11];
        int index12 = checked (index11 + 1);
        frmCgs.t11.Text = frmMainFormIPIS.train_details[(int) index1].cgs_inf[index12];
        int index13 = checked (index12 + 1);
        frmCgs.t12.Text = frmMainFormIPIS.train_details[(int) index1].cgs_inf[index13];
        int index14 = checked (index13 + 1);
        frmCgs.t13.Text = frmMainFormIPIS.train_details[(int) index1].cgs_inf[index14];
        int index15 = checked (index14 + 1);
        frmCgs.t14.Text = frmMainFormIPIS.train_details[(int) index1].cgs_inf[index15];
        int index16 = checked (index15 + 1);
        frmCgs.t15.Text = frmMainFormIPIS.train_details[(int) index1].cgs_inf[index16];
        int index17 = checked (index16 + 1);
        frmCgs.t16.Text = frmMainFormIPIS.train_details[(int) index1].cgs_inf[index17];
        int index18 = checked (index17 + 1);
        frmCgs.t17.Text = frmMainFormIPIS.train_details[(int) index1].cgs_inf[index18];
        int index19 = checked (index18 + 1);
        frmCgs.t18.Text = frmMainFormIPIS.train_details[(int) index1].cgs_inf[index19];
        int index20 = checked (index19 + 1);
        frmCgs.t19.Text = frmMainFormIPIS.train_details[(int) index1].cgs_inf[index20];
        int index21 = checked (index20 + 1);
        frmCgs.t20.Text = frmMainFormIPIS.train_details[(int) index1].cgs_inf[index21];
        int index22 = checked (index21 + 1);
        frmCgs.t21.Text = frmMainFormIPIS.train_details[(int) index1].cgs_inf[index22];
        int index23 = checked (index22 + 1);
        frmCgs.t22.Text = frmMainFormIPIS.train_details[(int) index1].cgs_inf[index23];
        int index24 = checked (index23 + 1);
        frmCgs.t23.Text = frmMainFormIPIS.train_details[(int) index1].cgs_inf[index24];
        int index25 = checked (index24 + 1);
        frmCgs.t24.Text = frmMainFormIPIS.train_details[(int) index1].cgs_inf[index25];
        int index26 = checked (index25 + 1);
        frmCgs.t25.Text = frmMainFormIPIS.train_details[(int) index1].cgs_inf[index26];
        int index27 = checked (index26 + 1);
        frmCgs.t26.Text = frmMainFormIPIS.train_details[(int) index1].cgs_inf[index27];
        frmTrainConfig.send_rowno_cgs = (int) index1;
        frmCgs.Show();
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(this.Handle.ToInt32(), exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void btnCancel_Click(object sender, EventArgs e)
  {
    this.btnAdd.Enabled = true;
    this.btnEdit.Enabled = true;
    this.btnDelete.Enabled = true;
    this.btnsave.Enabled = true;
    this.cmbTrainNo.Text = string.Empty;
    this.txtTrainName.Text = string.Empty;
    this.txtTrainNameReg.Text = string.Empty;
    this.txtTrainNameHin.Text = string.Empty;
    this.txtStartStn.Text = string.Empty;
    this.txtEndStn.Text = string.Empty;
    this.cmbArrTime.Text = string.Empty;
    this.cmbDepTime.Text = string.Empty;
    this.cmbPfno.Text = string.Empty;
    this.cmbStationPos.Text = string.Empty;
    this.cmbTrainNo.Enabled = false;
    this.txtTrainName.Enabled = false;
    this.txtTrainNameReg.Enabled = false;
    this.txtTrainNameHin.Enabled = false;
    this.txtStartStn.Enabled = false;
    this.txtEndStn.Enabled = false;
    this.cmbArrTime.Enabled = false;
    this.cmbDepTime.Enabled = false;
    this.cmbPfno.Enabled = false;
    this.btnTrainDetails.Enabled = false;
    this.btnCgs.Enabled = false;
    this.cmbDir.Enabled = false;
    this.cmbStationPos.Enabled = false;
    this.lblDir.Enabled = false;
    this.lblArrTime.Enabled = false;
    this.lblTrainNo.Enabled = false;
    this.lblTrainName.Enabled = false;
    this.lblSrtStn.Enabled = false;
    this.lblReg.Enabled = false;
    this.lblPfno.Enabled = false;
    this.lblHindi.Enabled = false;
    this.lblEnglish.Enabled = false;
    this.lblEndStn.Enabled = false;
    this.lblDir.Enabled = false;
    this.lblDepTime.Enabled = false;
    this.lblArrTime.Enabled = false;
    this.lblStationPos.Enabled = false;
    frmTrainConfig.train_details_enter = false;
    frmTrainConfig.cgs_form_enter = false;
  }

  private void txtTrainNameReg_TextChanged(object sender, EventArgs e)
  {
    if (Operators.CompareString(this.txtRegLang.Text, "Oriya", false) == 0)
    {
      this.txtTrainNameReg.Font = new Font("Maan Normal Odia Akhayara", 8.25f, FontStyle.Regular, GraphicsUnit.Point);
    }
    else
    {
      if (Operators.CompareString(this.txtRegLang.Text, "Telugu", false) != 0)
        return;
      this.txtTrainNameReg.Font = new Font("Gautami", 8.25f, FontStyle.Regular, GraphicsUnit.Point);
    }
  }

  private void cmbTrainNo_SelectedIndexChanged(object sender, EventArgs e)
  {
  }
}

}