-- Initialize User Tables for IPIS Login System

-- Create Users table (new system)
CREATE TABLE IF NOT EXISTS "Users" (
    "Id" INTEGER PRIMARY KEY AUTOINCREMENT,
    "Username" TEXT NOT NULL UNIQUE,
    "Password" TEXT NOT NULL,
    "Role" TEXT NOT NULL,
    "LastLogin" TEXT,
    "Status" TEXT DEFAULT 'Active'
);

-- Create UserPermissions table (new system)
CREATE TABLE IF NOT EXISTS "UserPermissions" (
    "Id" INTEGER PRIMARY KEY AUTOINCREMENT,
    "UserId" INTEGER NOT NULL,
    "Permission" TEXT NOT NULL,
    FOREIGN KEY("UserId") REFERENCES "Users"("Id")
);

-- Create User_Details table (legacy system)
CREATE TABLE IF NOT EXISTS "User_Details" (
    "User_Name" TEXT PRIMARY KEY,
    "Pass" TEXT,
    "User_type" TEXT,
    "Hint_Pass" TEXT,
    "Chk_Adver" INTEGER DEFAULT 0,
    "Chk_TDEntry" INTEGER DEFAULT 0,
    "Chk_Reports" INTEGER DEFAULT 0,
    "Chk_SD" INTEGER DEFAULT 0,
    "Chk_AUser" INTEGER DEFAULT 0,
    "Chk_ASCode" INTEGER DEFAULT 0,
    "Chk_Rep" INTEGER DEFAULT 0
);

-- Insert default administrator user
INSERT OR IGNORE INTO "Users" ("Username", "Password", "Role", "Status") 
VALUES ('admin', 'admin123', 'Administrator', 'Active');

-- Insert default permissions for admin
INSERT OR IGNORE INTO "UserPermissions" ("UserId", "Permission")
SELECT u.Id, p.Permission
FROM "Users" u
CROSS JOIN (
    SELECT 'Reports' AS Permission UNION ALL
    SELECT 'Add User' UNION ALL
    SELECT 'Station Details' UNION ALL
    SELECT 'Add Station Code' UNION ALL
    SELECT 'Train Data Entry' UNION ALL
    SELECT 'Add Advertising'
) p
WHERE u.Username = 'admin';

-- Insert some legacy users for testing
INSERT OR IGNORE INTO "User_Details" ("User_Name", "Pass", "User_type", "Chk_Adver", "Chk_TDEntry", "Chk_Reports", "Chk_SD", "Chk_AUser", "Chk_ASCode", "Chk_Rep")
VALUES 
    ('operator', 'operator123', 'Operator', 1, 1, 0, 0, 0, 0, 0),
    ('supervisor', 'supervisor123', 'Supervisor', 1, 1, 1, 1, 0, 0, 0),
    ('user', 'user123', 'User', 1, 0, 0, 0, 0, 0, 0);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS "idx_users_username" ON "Users"("Username");
CREATE INDEX IF NOT EXISTS "idx_userpermissions_userid" ON "UserPermissions"("UserId");
CREATE INDEX IF NOT EXISTS "idx_userdetails_username" ON "User_Details"("User_Name"); 