// Decompiled with JetBrains decompiler
// Type: ipis.agdb_api
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using System.Diagnostics;
using System.Threading;

namespace ipis
{

public class agdb_api
{
  private static byte[] pkt_buf = new byte[755];
  private static byte[] rxbuf = new byte[2001];

  [DebuggerNonUserCode]
  public agdb_api()
  {
  }

  public static byte agdb_link_check(
    byte agdb_addr,
    string platform_no,
    ref byte[] agdb_pkt,
    ref short length)
  {
    int index1 = 0;
    while (index1 < 754)
    {
      agdb_api.pkt_buf[index1] = (byte) 0;
      checked { ++index1; }
    }
    agdb_api.pkt_buf[0] = (byte) 170;
    agdb_api.pkt_buf[1] = (byte) 204;
    agdb_api.pkt_buf[2] = (byte) 0;
    agdb_api.pkt_buf[3] = (byte) 10;
    agdb_api.pkt_buf[4] = frmMainFormIPIS.ccu_addr;
    agdb_api.pkt_buf[5] = (byte) 0;
    agdb_api.pkt_buf[6] = agdb_addr;
    agdb_api.pkt_buf[7] = (byte) 0;
    agdb_api.pkt_buf[8] = (byte) 0;
    agdb_api.pkt_buf[9] = (byte) 128 /*0x80*/;
    ushort length1 = checked ((ushort) ((int) unchecked ((ushort) ((int) (ushort) (0U << 8) | 10)) + 2));
    length = checked ((short) length1);
    int index2 = 0;
    while (index2 < (int) length1)
    {
      agdb_pkt[index2] = agdb_api.pkt_buf[index2];
      checked { ++index2; }
    }
    Checksum.prepare_checksum(ref agdb_api.pkt_buf, length1);
    if (RS232.Serial_Write(ref agdb_api.pkt_buf, (int) length1) == 1)
      return 1;
    Log_file.Log(string.Format("Write to COM port failed, may be COM port doesn't exit. Please check the COM PORT settings"));
    return 3;
  }

  public static byte agdb_link_check_res_pkt(
    byte agdb_addr,
    string Platform_no,
    ref byte[] agdb_pkt,
    ref short length,
    ref byte agdb_Sys_Cfg)
  {
    if (RS232.Serial_Read(ref agdb_api.rxbuf) == (byte) 1)
    {
      ushort Pkt_length = checked ((ushort) unchecked ((int) (ushort) ((uint) (ushort) agdb_api.rxbuf[0] << 8) + (int) agdb_api.rxbuf[1]));
      length = checked ((short) ((int) Pkt_length + 2));
      agdb_pkt[0] = (byte) 170;
      agdb_pkt[1] = (byte) 204;
      byte index = 0;
      while ((uint) index < (uint) Pkt_length)
      {
        agdb_pkt[checked ((int) index + 2)] = agdb_api.rxbuf[(int) index];
        checked { ++index; }
      }
      byte num = 0;
      if ((int) agdb_api.rxbuf[2] != (int) agdb_addr & agdb_api.rxbuf[3] != (byte) 0)
      {
        Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr}LINK CHECK RESPONSE PACKET: IN CORRECT SOURCE ADDRESS");
        num = (byte) 1;
      }
      if (Pkt_length != (ushort) 12)
      {
        Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr}LINK CHECK RESPONSE PACKET: IN VALID DATA LENGTH");
        num = (byte) 1;
      }
      if (agdb_api.rxbuf[7] != (byte) 192 /*0xC0*/)
      {
        Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr}LINK CHECK RESPONSE PACKET: IN-VALID RESPONSE PACKET FUNCTION CODE");
        num = (byte) 1;
      }
      switch (agdb_api.rxbuf[8])
      {
        case 0:
          Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr}LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully ");
          break;
        case 2:
          Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr}LINK CHECK COMMAND PACKET STATUS: CRC FAIL");
          num = (byte) 1;
          break;
        case 6:
          Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr}LINK CHECK COMMAND PACKET STATUS: IN VALID FUNCTION CODE");
          num = (byte) 1;
          break;
        case 35:
          Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr}LINK CHECK COMMAND PACKET STATUS: IN VALID DATA LENGTH");
          num = (byte) 1;
          break;
      }
      agdb_Sys_Cfg = agdb_api.rxbuf[9];
      if (Checksum.Checksum_Calc(ref agdb_api.rxbuf, Pkt_length) == 0)
      {
        Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr}LINK CHECK RESPONSE PACKET: CHECKSUM FAILED ");
        return 9;
      }
      if (num != (byte) 0)
      {
        Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr}LINK CHECK COMMAND IS UNSUCCESSFULL");
        return 0;
      }
      Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr}LINK CHECK COMMAND IS SUCCESSFUL");
      return 1;
    }
    Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} LINK FAILURE or ADDRESSED AGDB DOESn't EXIST");
    return 2;
  }

  public static byte agdb_message(
    byte agdb_addr,
    byte[] agdb_line1,
    byte[] agdb_line2,
    byte[] agdb_line3,
    byte serial_no,
    string platform_no,
    byte agdb_sw_dly,
    byte agdb_video,
    byte agdb_pkt_type)
  {
    int index1 = 0;
    while (index1 < 754)
    {
      agdb_api.pkt_buf[index1] = (byte) 0;
      checked { ++index1; }
    }
    agdb_api.pkt_buf[0] = (byte) 170;
    agdb_api.pkt_buf[1] = (byte) 204;
    agdb_api.pkt_buf[2] = (byte) 2;
    agdb_api.pkt_buf[3] = (byte) 222;
    agdb_api.pkt_buf[4] = frmMainFormIPIS.ccu_addr;
    agdb_api.pkt_buf[5] = (byte) 0;
    agdb_api.pkt_buf[6] = agdb_addr;
    agdb_api.pkt_buf[7] = (byte) 0;
    agdb_api.pkt_buf[8] = serial_no;
    agdb_api.pkt_buf[9] = (byte) 129;
    agdb_api.pkt_buf[10] = agdb_pkt_type;
    agdb_api.pkt_buf[11] = agdb_sw_dly;
    agdb_api.pkt_buf[12] = (byte) 0;
    agdb_api.pkt_buf[13] = agdb_video;
    int index2 = 0;
    while (index2 < 240 /*0xF0*/)
    {
      agdb_api.pkt_buf[checked (14 + index2)] = agdb_line1[index2];
      agdb_api.pkt_buf[checked (14 + index2 + 240 /*0xF0*/)] = agdb_line2[index2];
      agdb_api.pkt_buf[checked (14 + index2 + 480)] = agdb_line3[index2];
      checked { ++index2; }
    }
    ushort length = checked ((ushort) ((int) unchecked ((ushort) ((int) (ushort) (2U << 8) | 222)) + 2));
    Checksum.prepare_checksum(ref agdb_api.pkt_buf, length);
    if (RS232.Serial_Write(ref agdb_api.pkt_buf, (int) length) != 1)
    {
      Log_file.Log(string.Format("Write to COM port failed, may be COM port deos't exit. Please check the COM PORT settings"));
      return 3;
    }
    Thread.Sleep(100);
    int num = 0;
    if (RS232.Serial_Read(ref agdb_api.rxbuf) == (byte) 1)
    {
      ushort Pkt_length = checked ((ushort) unchecked ((int) (ushort) ((uint) (ushort) agdb_api.rxbuf[0] << 8) + (int) agdb_api.rxbuf[1]));
      if (Checksum.Checksum_Calc(ref agdb_api.rxbuf, Pkt_length) == 0)
      {
        Log_file.Log("PLATFORM NO:{platform_no} AGDB Address:{agdb_addr} DATA RESPONSE PACKET: CHECKSUM FAILED ");
        num = 1;
      }
      if ((int) agdb_api.rxbuf[2] != (int) agdb_addr & agdb_api.rxbuf[3] != (byte) 0)
      {
        Log_file.Log("PLATFORM NO:{platform_no} AGDB Address:{agdb_addr} DATA RESPONSE PACKET: IN CORRECT SOURCE ADDRESS");
        num = 1;
      }
      if (Pkt_length != (ushort) 11)
      {
        Log_file.Log("PLATFORM NO:{platform_no} AGDB Address:{agdb_addr} DATA RESPONSE PACKET: IN VALID DATA LENGTH");
        num = 1;
      }
      if (agdb_api.rxbuf[7] != (byte) 193)
      {
        Log_file.Log("PLATFORM NO:{platform_no} AGDB Address:{agdb_addr} DATA RESPONSE PACKET: IN-VALID RESPONSE PACKET FUNCTION CODE");
        num = 1;
      }
      switch (agdb_api.rxbuf[8])
      {
        case 0:
          Log_file.Log("PLATFORM NO:{platform_no} AGDB Address:{agdb_addr} DATA PACKET STATUS: Packet Received and proceed successfully ");
          break;
        case 2:
          Log_file.Log("PLATFORM NO:{platform_no} AGDB Address:{agdb_addr} DATA PACKET STATUS: CRC FAIL");
          num = 1;
          break;
        case 6:
          Log_file.Log("PLATFORM NO:{platform_no} AGDB Address:{agdb_addr} DATA PACKET STATUS: IN VALID FUNCTION CODE");
          num = 1;
          break;
        case 35:
          Log_file.Log("PLATFORM NO:{platform_no} AGDB Address:{agdb_addr} DATA PACKET STATUS: IN VALID DATA LENGTH");
          num = 1;
          break;
      }
      if (num != 0)
      {
        Log_file.Log("PLATFORM NO:{platform_no} AGDB Address:{agdb_addr} DATA PACKET IS UNSUCCESSFULL");
        return 0;
      }
      Log_file.Log("PLATFORM NO:{platform_no} AGDB Address:{agdb_addr} DATA PACKET IS SUCCESSFUL");
      return 1;
    }
    Log_file.Log("PLATFORM NO:{platform_no} AGDB Address:{agdb_addr} LINK FAILURE or ADDRESSED AGDB DOESN'T EXIST");
    return 2;
  }

  public static byte agdb_delete_msg(byte agdb_addr, string platform_no, byte serial_no)
  {
    int index = 0;
    while (index < 754)
    {
      agdb_api.pkt_buf[index] = (byte) 0;
      checked { ++index; }
    }
    agdb_api.pkt_buf[0] = (byte) 170;
    agdb_api.pkt_buf[1] = (byte) 204;
    agdb_api.pkt_buf[2] = (byte) 0;
    agdb_api.pkt_buf[3] = (byte) 14;
    agdb_api.pkt_buf[4] = frmMainFormIPIS.ccu_addr;
    agdb_api.pkt_buf[5] = (byte) 0;
    agdb_api.pkt_buf[6] = agdb_addr;
    agdb_api.pkt_buf[7] = (byte) 0;
    agdb_api.pkt_buf[8] = serial_no;
    agdb_api.pkt_buf[9] = (byte) 129;
    agdb_api.pkt_buf[10] = (byte) 2;
    agdb_api.pkt_buf[11] = (byte) 0;
    agdb_api.pkt_buf[12] = (byte) 0;
    agdb_api.pkt_buf[13] = (byte) 0;
    ushort length = checked ((ushort) (14 + 2));
    Checksum.prepare_checksum(ref agdb_api.pkt_buf, length);
    if (RS232.Serial_Write(ref agdb_api.pkt_buf, (int) length) != 1)
    {
      Log_file.Log(string.Format("Write to COM port failed, may be COM port deos't exit. Please check the COM PORT settings"));
      return 3;
    }
    int num = 0;
    if (RS232.Serial_Read(ref agdb_api.rxbuf) == (byte) 1)
    {
      ushort Pkt_length = checked ((ushort) unchecked ((int) (ushort) ((uint) (ushort) agdb_api.rxbuf[0] << 8) + (int) agdb_api.rxbuf[1]));
      if (Checksum.Checksum_Calc(ref agdb_api.rxbuf, Pkt_length) == 0)
      {
        Log_file.Log("PLATFORM NO:{platform_no} AGDB Address:{agdb_addr} DATA RESPONSE PACKET: CHECKSUM FAILED ");
        num = 1;
      }
      if ((int) agdb_api.rxbuf[2] != (int) agdb_addr & agdb_api.rxbuf[3] != (byte) 0)
      {
        Log_file.Log("PLATFORM NO:{platform_no} AGDB Address:{agdb_addr} DATA RESPONSE PACKET: IN CORRECT SOURCE ADDRESS");
        num = 1;
      }
      if (Pkt_length != (ushort) 11)
      {
        Log_file.Log("PLATFORM NO:{platform_no} AGDB Address:{agdb_addr} DATA RESPONSE PACKET: IN VALID DATA LENGTH");
        num = 1;
      }
      if (agdb_api.rxbuf[7] != (byte) 193)
      {
        Log_file.Log("PLATFORM NO:{platform_no} AGDB Address:{agdb_addr} DATA RESPONSE PACKET: IN-VALID RESPONSE PACKET FUNCTION CODE");
        num = 1;
      }
      switch (agdb_api.rxbuf[8])
      {
        case 0:
          Log_file.Log("PLATFORM NO:{platform_no} AGDB Address:{agdb_addr} DATA PACKET STATUS: Packet Received and proceed successfully ");
          break;
        case 2:
          Log_file.Log("PLATFORM NO:{platform_no} AGDB Address:{agdb_addr} DATA PACKET STATUS: CRC FAIL");
          num = 1;
          break;
        case 6:
          Log_file.Log("PLATFORM NO:{platform_no} AGDB Address:{agdb_addr} DATA PACKET STATUS: IN VALID FUNCTION CODE");
          num = 1;
          break;
        case 35:
          Log_file.Log("PLATFORM NO:{platform_no} AGDB Address:{agdb_addr} DATA PACKET STATUS: IN VALID DATA LENGTH");
          num = 1;
          break;
      }
      if (num != 0)
      {
        Log_file.Log("PLATFORM NO:{platform_no} AGDB Address:{agdb_addr} DATA PACKET IS UNSUCCESSFULL");
        return 0;
      }
      Log_file.Log("PLATFORM NO:{platform_no} AGDB Address:{agdb_addr} DATA PACKET IS SUCCESSFUL");
      return 1;
    }
    Log_file.Log("PLATFORM NO:{platform_no} AGDB Address:{agdb_addr} LINK FAILURE or ADDRESSED AGDB DOESN'T EXIST");
    return 2;
  }

  public static byte agdb_set_cfg_send_pkt(
    byte agdb_addr,
    string platform_no,
    ref byte[] agdb_pkt,
    ref short length)
  {
    agdb_api.pkt_buf[0] = (byte) 170;
    agdb_api.pkt_buf[1] = (byte) 204;
    agdb_api.pkt_buf[2] = (byte) 0;
    agdb_api.pkt_buf[3] = (byte) 12;
    agdb_api.pkt_buf[4] = frmMainFormIPIS.ccu_addr;
    agdb_api.pkt_buf[5] = (byte) 0;
    agdb_api.pkt_buf[6] = agdb_addr;
    agdb_api.pkt_buf[7] = (byte) 0;
    agdb_api.pkt_buf[8] = (byte) 0;
    agdb_api.pkt_buf[9] = (byte) 132;
    agdb_api.pkt_buf[10] = frmMainFormIPIS.intensity;
    agdb_api.pkt_buf[11] = (byte) 0;
    ushort length1 = checked ((ushort) ((int) unchecked ((ushort) ((int) (ushort) (0U << 8) | 12)) + 2));
    length = checked ((short) length1);
    int index = 0;
    while (index < (int) length1)
    {
      agdb_pkt[index] = agdb_api.pkt_buf[index];
      checked { ++index; }
    }
    Checksum.prepare_checksum(ref agdb_api.pkt_buf, length1);
    if (RS232.Serial_Write(ref agdb_api.pkt_buf, (int) length1) == 1)
      return 1;
    Log_file.Log(string.Format("Write to COM port failed, may be COM port deos't exit. Please check the COM PORT settings"));
    return 3;
  }

  public static byte agdb_set_cfg_res_pkt(
    byte agdb_addr,
    string Platform_no,
    ref byte[] agdb_pkt,
    ref short length)
  {
    if (RS232.Serial_Read(ref agdb_api.rxbuf) == (byte) 1)
    {
      ushort Pkt_length = checked ((ushort) unchecked ((int) (ushort) ((uint) (ushort) agdb_api.rxbuf[0] << 8) + (int) agdb_api.rxbuf[1]));
      length = checked ((short) ((int) Pkt_length + 2));
      agdb_pkt[0] = (byte) 170;
      agdb_pkt[1] = (byte) 204;
      byte index = 0;
      while ((uint) index < (uint) Pkt_length)
      {
        agdb_pkt[checked ((int) index + 2)] = agdb_api.rxbuf[(int) index];
        checked { ++index; }
      }
      byte num = 0;
      if ((int) agdb_api.rxbuf[2] != (int) agdb_addr & agdb_api.rxbuf[3] != (byte) 0)
      {
        Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} SET CONFIGURATION RESPONSE PACKET: IN CORRECT SOURCE ADDRESS");
        num = (byte) 1;
      }
      if (Pkt_length != (ushort) 11)
      {
        Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} SET CONFIGURATION RESPONSE PACKET: IN VALID DATA LENGTH");
        num = (byte) 1;
      }
      if (agdb_api.rxbuf[7] != (byte) 196)
      {
        Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} SET CONFIGURATION RESPONSE PACKET: IN VALID FUNCTION CODE");
        num = (byte) 1;
      }
      switch (agdb_api.rxbuf[8])
      {
        case 0:
          Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully");
          break;
        case 2:
          Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} SET CONFIGURATION COMMAND PACKET: CRC FAIL");
          num = (byte) 1;
          break;
        case 6:
          Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} SET CONFIGURATION COMMAND PACKET: IN VALID FUNCTION CODE");
          num = (byte) 1;
          break;
        case 35:
          Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} SET CONFIGURATION COMMAND PACKET: IN VALID DATA LENGTH");
          num = (byte) 1;
          break;
      }
      if (Checksum.Checksum_Calc(ref agdb_api.rxbuf, Pkt_length) == 0)
      {
        Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr}LINK CHECK RESPONSE PACKET: CHECKSUM FAILED ");
        return 9;
      }
      if (num != (byte) 0)
      {
        Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} SET CONFIGURATION COMMAND IS UNSUCCESSFUL");
        return 0;
      }
      Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} SET CONFIGURATION COMMAND IS SUCCESSFUL");
      return 1;
    }
    Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} LINK FAILURE or ADDRESSED AGDB DOESN'T EXIST");
    return 2;
  }

  public static byte agdb_get_cfg_send_pkt(
    byte agdb_addr,
    string platform_no,
    ref byte[] agdb_pkt,
    ref short length)
  {
    agdb_api.pkt_buf[0] = (byte) 170;
    agdb_api.pkt_buf[1] = (byte) 204;
    agdb_api.pkt_buf[2] = (byte) 0;
    agdb_api.pkt_buf[3] = (byte) 10;
    agdb_api.pkt_buf[4] = frmMainFormIPIS.ccu_addr;
    agdb_api.pkt_buf[5] = (byte) 0;
    agdb_api.pkt_buf[6] = agdb_addr;
    agdb_api.pkt_buf[7] = (byte) 0;
    agdb_api.pkt_buf[8] = (byte) 0;
    agdb_api.pkt_buf[9] = (byte) 133;
    ushort length1 = checked ((ushort) ((int) unchecked ((ushort) ((int) (ushort) (0U << 8) | 10)) + 2));
    length = checked ((short) length1);
    int index = 0;
    while (index < (int) length1)
    {
      agdb_pkt[index] = agdb_api.pkt_buf[index];
      checked { ++index; }
    }
    Checksum.prepare_checksum(ref agdb_api.pkt_buf, length1);
    if (RS232.Serial_Write(ref agdb_api.pkt_buf, (int) length1) == 1)
      return 1;
    Log_file.Log(string.Format("Write to COM port failed, may be COM port deos't exit. Please check the COM PORT settings"));
    return 3;
  }

  public static byte agdb_get_cfg_res_pkt(
    byte agdb_addr,
    string Platform_no,
    ref byte[] agdb_pkt,
    ref short length,
    ref byte agdb_intensity,
    ref byte agdb_Sys_Cfg)
  {
    if (RS232.Serial_Read(ref agdb_api.rxbuf) == (byte) 1)
    {
      ushort Pkt_length = checked ((ushort) unchecked ((int) (ushort) ((uint) (ushort) agdb_api.rxbuf[0] << 8) + (int) agdb_api.rxbuf[1]));
      length = checked ((short) ((int) Pkt_length + 2));
      agdb_pkt[0] = (byte) 170;
      agdb_pkt[1] = (byte) 204;
      byte index = 0;
      while ((uint) index < (uint) Pkt_length)
      {
        agdb_pkt[checked ((int) index + 2)] = agdb_api.rxbuf[(int) index];
        checked { ++index; }
      }
      byte num = 0;
      if ((int) agdb_api.rxbuf[2] != (int) agdb_addr & agdb_api.rxbuf[3] != (byte) 0)
      {
        Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} GET CONFIGURATION RESPONSE PACKET: IN CORRECT SOURCE ADDRESS");
        num = (byte) 1;
      }
      if (Pkt_length != (ushort) 14)
      {
        Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} GET CONFIGURATION RESPONSE PACKET: IN VALID DATA LENGTH");
        num = (byte) 1;
      }
      if (agdb_api.rxbuf[7] != (byte) 197)
      {
        Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} GET CONFIGURATION RESPONSE PACKET: IN VALID FUNCTION CODE");
        num = (byte) 1;
      }
      switch (agdb_api.rxbuf[8])
      {
        case 0:
          Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} GET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully");
          break;
        case 2:
          Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} GET CONFIGURATION COMMAND PACKET: CRC FAIL");
          num = (byte) 1;
          break;
        case 6:
          Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} GET CONFIGURATION COMMAND PACKET: IN VALID FUNCTION CODE");
          num = (byte) 1;
          break;
        case 35:
          Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} GET CONFIGURATION COMMAND PACKET: IN VALID DATA LENGTH");
          num = (byte) 1;
          break;
      }
      agdb_Sys_Cfg = agdb_api.rxbuf[9];
      agdb_intensity = agdb_api.rxbuf[10];
      if (Checksum.Checksum_Calc(ref agdb_api.rxbuf, Pkt_length) == 0)
      {
        Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr}LINK CHECK RESPONSE PACKET: CHECKSUM FAILED ");
        return 9;
      }
      if (num != (byte) 0)
      {
        Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} GET CONFIGURATION COMMAND IS UNSUCCESSFUL");
        return 0;
      }
      Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} GET CONFIGURATION COMMAND IS SUCCESSFUL");
      return 1;
    }
    Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} LINK FAILURE or ADDRESSED AGDB DOESN'T EXIST");
    return 2;
  }

  public static byte agdb_soft_reset(
    byte agdb_addr,
    string platform_no,
    ref byte[] agdb_pkt,
    ref short length)
  {
    agdb_api.pkt_buf[0] = (byte) 170;
    agdb_api.pkt_buf[1] = (byte) 204;
    agdb_api.pkt_buf[2] = (byte) 0;
    agdb_api.pkt_buf[3] = (byte) 10;
    agdb_api.pkt_buf[4] = frmMainFormIPIS.ccu_addr;
    agdb_api.pkt_buf[5] = (byte) 0;
    agdb_api.pkt_buf[6] = agdb_addr;
    agdb_api.pkt_buf[7] = (byte) 0;
    agdb_api.pkt_buf[8] = (byte) 0;
    agdb_api.pkt_buf[9] = (byte) 134;
    ushort length1 = checked ((ushort) ((int) unchecked ((ushort) ((int) (ushort) (0U << 8) | 10)) + 2));
    length = checked ((short) length1);
    int index = 0;
    while (index < (int) length1)
    {
      agdb_pkt[index] = agdb_api.pkt_buf[index];
      checked { ++index; }
    }
    Checksum.prepare_checksum(ref agdb_api.pkt_buf, length1);
    if (RS232.Serial_Write(ref agdb_api.pkt_buf, (int) length1) != 1)
    {
      Log_file.Log(string.Format("Write to COM port failed, may be COM port deos't exit. Please check the COM PORT settings"));
      return 3;
    }
    Thread.Sleep(10);
    Log_file.Log("PLATFORM NO:{platform_no} AGDB Address:{agdb_addr} SOFT RESET COMMAND IS SUCCESSFUL");
    return 1;
  }

  public static byte agdb_clr_reset(
    byte agdb_addr,
    string platform_no,
    ref byte[] agdb_pkt,
    ref short length)
  {
    agdb_api.pkt_buf[0] = (byte) 170;
    agdb_api.pkt_buf[1] = (byte) 204;
    agdb_api.pkt_buf[2] = (byte) 0;
    agdb_api.pkt_buf[3] = (byte) 10;
    agdb_api.pkt_buf[4] = frmMainFormIPIS.ccu_addr;
    agdb_api.pkt_buf[5] = (byte) 0;
    agdb_api.pkt_buf[6] = agdb_addr;
    agdb_api.pkt_buf[7] = (byte) 0;
    agdb_api.pkt_buf[8] = (byte) 0;
    agdb_api.pkt_buf[9] = (byte) 135;
    ushort length1 = checked ((ushort) ((int) unchecked ((ushort) ((int) (ushort) (0U << 8) | 10)) + 2));
    length = checked ((short) length1);
    int index = 0;
    while (index < (int) length1)
    {
      agdb_pkt[index] = agdb_api.pkt_buf[index];
      checked { ++index; }
    }
    Checksum.prepare_checksum(ref agdb_api.pkt_buf, length1);
    if (RS232.Serial_Write(ref agdb_api.pkt_buf, (int) length1) == 1)
      return 1;
    Log_file.Log(string.Format("Write to COM port failed, may be COM port deos't exit. Please check the COM PORT settings"));
    return 3;
  }

  public static byte agdb_clr_reset_res_pkt(
    byte agdb_addr,
    string Platform_no,
    ref byte[] agdb_pkt,
    ref short length)
  {
    if (RS232.Serial_Read(ref agdb_api.rxbuf) == (byte) 1)
    {
      ushort Pkt_length = checked ((ushort) unchecked ((int) (ushort) ((uint) (ushort) agdb_api.rxbuf[0] << 8) + (int) agdb_api.rxbuf[1]));
      length = checked ((short) ((int) Pkt_length + 2));
      agdb_pkt[0] = (byte) 170;
      agdb_pkt[1] = (byte) 204;
      byte index = 0;
      while ((uint) index < (uint) Pkt_length)
      {
        agdb_pkt[checked ((int) index + 2)] = agdb_api.rxbuf[(int) index];
        checked { ++index; }
      }
      byte num = 0;
      if ((int) agdb_api.rxbuf[2] != (int) agdb_addr & agdb_api.rxbuf[3] != (byte) 0)
      {
        Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} CLEAR RESET RESPONSE PACKET: IN CORRECT SOURCE ADDRESS");
        num = (byte) 1;
      }
      if (Pkt_length != (ushort) 11)
      {
        Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} CLEAR RESET RESPONSE PACKET: IN VALID DATA LENGTH");
        num = (byte) 1;
      }
      if (agdb_api.rxbuf[7] != (byte) 199)
      {
        Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} CLEAR RESET RESPONSE PACKET: IN VALID FUNCTION CODE");
        num = (byte) 1;
      }
      switch (agdb_api.rxbuf[8])
      {
        case 0:
          Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} CLEAR RESET COMMAND PACKET: Packet Received and Processed Successfully");
          break;
        case 2:
          Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} CLEAR RESET COMMAND PACKET: CRC FAIL");
          num = (byte) 1;
          break;
        case 6:
          Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} CLEAR RESET COMMAND PACKET: IN VALID FUNCTION CODE");
          num = (byte) 1;
          break;
        case 35:
          Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} CLEAR RESET COMMAND PACKET: IN VALID DATA LENGTH");
          num = (byte) 1;
          break;
      }
      if (Checksum.Checksum_Calc(ref agdb_api.rxbuf, Pkt_length) == 0)
      {
        Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr}LINK CHECK RESPONSE PACKET: CHECKSUM FAILED ");
        return 9;
      }
      if (num != (byte) 0)
      {
        Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} CLEAT RESET COMMAND IS UNSUCCESSFUL");
        return 0;
      }
      Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} CLEAR RESET COMMAND IS SUCCESSFUL");
      return 1;
    }
    Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} LINK FAILURE or ADDRESSED AGDB DOESN'T EXIST");
    return 2;
  }

  public static byte agdb_pre_cmd_status(
    byte agdb_addr,
    string platform_no,
    ref byte[] agdb_pkt,
    ref short length)
  {
    agdb_api.pkt_buf[0] = (byte) 170;
    agdb_api.pkt_buf[1] = (byte) 204;
    agdb_api.pkt_buf[2] = (byte) 0;
    agdb_api.pkt_buf[3] = (byte) 10;
    agdb_api.pkt_buf[4] = frmMainFormIPIS.ccu_addr;
    agdb_api.pkt_buf[5] = (byte) 0;
    agdb_api.pkt_buf[6] = agdb_addr;
    agdb_api.pkt_buf[7] = (byte) 0;
    agdb_api.pkt_buf[8] = (byte) 0;
    agdb_api.pkt_buf[9] = (byte) 136;
    ushort length1 = checked ((ushort) ((int) unchecked ((ushort) ((int) (ushort) (0U << 8) | 10)) + 2));
    length = checked ((short) length1);
    int index = 0;
    while (index < (int) length1)
    {
      agdb_pkt[index] = agdb_api.pkt_buf[index];
      checked { ++index; }
    }
    Checksum.prepare_checksum(ref agdb_api.pkt_buf, length1);
    if (RS232.Serial_Write(ref agdb_api.pkt_buf, (int) length1) == 1)
      return 1;
    Log_file.Log(string.Format("Write to COM port failed, may be COM port deos't exit. Please check the COM PORT settings"));
    return 3;
  }

  public static byte agdb_pre_cmd_res_pkt(
    byte agdb_addr,
    string Platform_no,
    ref byte pre_cmd_status,
    ref byte pre_cmd_serial_no,
    ref byte pre_cmd_fc,
    ref byte[] agdb_pkt,
    ref short length)
  {
    if (RS232.Serial_Read(ref agdb_api.rxbuf) == (byte) 1)
    {
      ushort Pkt_length = checked ((ushort) unchecked ((int) (ushort) ((uint) (ushort) agdb_api.rxbuf[0] << 8) + (int) agdb_api.rxbuf[1]));
      length = checked ((short) ((int) Pkt_length + 2));
      agdb_pkt[0] = (byte) 170;
      agdb_pkt[1] = (byte) 204;
      byte index = 0;
      while ((uint) index < (uint) Pkt_length)
      {
        agdb_pkt[checked ((int) index + 2)] = agdb_api.rxbuf[(int) index];
        checked { ++index; }
      }
      byte num = 0;
      if ((int) agdb_api.rxbuf[2] != (int) agdb_addr & agdb_api.rxbuf[3] != (byte) 0)
      {
        Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} PREVIOUS COMMAND RESPONSE PACKET: IN CORRECT SOURCE ADDRESS");
        num = (byte) 1;
      }
      if (Pkt_length != (ushort) 14)
      {
        Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} PREVIOUS COMMAND RESPONSE PACKET: IN VALID DATA LENGTH");
        num = (byte) 1;
      }
      if (agdb_api.rxbuf[7] != (byte) 200)
      {
        Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} PREVIOUS COMMAND RESPONSE PACKET: IN VALID FUNCTION CODE");
        num = (byte) 1;
      }
      switch (agdb_api.rxbuf[8])
      {
        case 0:
          Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} PREVIOUS COMMAND PACKET: Packet Received and Processed Successfully");
          break;
        case 2:
          Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} PREVIOUS COMMAND PACKET: CRC FAIL");
          num = (byte) 1;
          break;
        case 6:
          Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} PREVIOUS COMMAND PACKET: IN VALID FUNCTION CODE");
          num = (byte) 1;
          break;
        case 35:
          Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} PREVIOUS COMMAND PACKET: IN VALID DATA LENGTH");
          num = (byte) 1;
          break;
      }
      pre_cmd_status = agdb_api.rxbuf[9];
      pre_cmd_serial_no = agdb_api.rxbuf[10];
      pre_cmd_fc = agdb_api.rxbuf[11];
      if (Checksum.Checksum_Calc(ref agdb_api.rxbuf, Pkt_length) == 0)
      {
        Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr}LINK CHECK RESPONSE PACKET: CHECKSUM FAILED ");
        return 9;
      }
      if (num != (byte) 0)
      {
        Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} PREVIOUS COMMAND IS UNSUCCESSFUL");
        return 0;
      }
      Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} PREVIOUS COMMAND IS SUCCESSFUL");
      return 1;
    }
    Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} LINK FAILURE or ADDRESSED AGDB DOESN'T EXIST");
    return 2;
  }

  public static byte agdb_diag_cmd(
    byte agdb_addr,
    string platform_no,
    ref byte[] agdb_pkt,
    ref short length)
  {
    agdb_api.pkt_buf[0] = (byte) 170;
    agdb_api.pkt_buf[1] = (byte) 204;
    agdb_api.pkt_buf[2] = (byte) 0;
    agdb_api.pkt_buf[3] = (byte) 10;
    agdb_api.pkt_buf[4] = frmMainFormIPIS.ccu_addr;
    agdb_api.pkt_buf[5] = (byte) 0;
    agdb_api.pkt_buf[6] = agdb_addr;
    agdb_api.pkt_buf[7] = (byte) 0;
    agdb_api.pkt_buf[8] = (byte) 0;
    agdb_api.pkt_buf[9] = (byte) 138;
    ushort length1 = checked ((ushort) ((int) unchecked ((ushort) ((int) (ushort) (0U << 8) | 10)) + 2));
    length = checked ((short) length1);
    int index = 0;
    while (index < (int) length1)
    {
      agdb_pkt[index] = agdb_api.pkt_buf[index];
      checked { ++index; }
    }
    Checksum.prepare_checksum(ref agdb_api.pkt_buf, length1);
    if (RS232.Serial_Write(ref agdb_api.pkt_buf, (int) length1) == 1)
      return 1;
    Log_file.Log(string.Format("Write to COM port failed, may be COM port deos't exit. Please check the COM PORT settings"));
    return 3;
  }

  public static byte agdb_diag_cmd_res_pkt(
    byte agdb_addr,
    string Platform_no,
    ref byte[] agdb_pkt,
    ref short length,
    ref byte[] manu)
  {
    if (RS232.Serial_Read(ref agdb_api.rxbuf) == (byte) 1)
    {
      ushort Pkt_length = checked ((ushort) unchecked ((int) (ushort) ((uint) (ushort) agdb_api.rxbuf[0] << 8) + (int) agdb_api.rxbuf[1]));
      length = checked ((short) ((int) Pkt_length + 2));
      agdb_pkt[0] = (byte) 170;
      agdb_pkt[1] = (byte) 204;
      byte index = 0;
      while ((uint) index < (uint) Pkt_length)
      {
        agdb_pkt[checked ((int) index + 2)] = agdb_api.rxbuf[(int) index];
        checked { ++index; }
      }
      byte num = 0;
      if ((int) agdb_api.rxbuf[2] != (int) agdb_addr & agdb_api.rxbuf[3] != (byte) 0)
      {
        Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} DIAGNOSTIC COMMAND RESPONSE PACKET: IN CORRECT SOURCE ADDRESS");
        num = (byte) 1;
      }
      if (Pkt_length != (ushort) 16 /*0x10*/)
      {
        Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} DIAGNOSTIC COMMAND RESPONSE PACKET: IN VALID DATA LENGTH");
        num = (byte) 1;
      }
      if (agdb_api.rxbuf[7] != (byte) 202)
      {
        Log_file.Log(string.Format("PLATFORM NO:{0} agdb Address:(1) DIAGNOSTIC COMMAND RESPONSE PACKET: IN VALID FUNCTION CODE", (object) Platform_no, (object) agdb_addr));
        num = (byte) 1;
      }
      switch (agdb_api.rxbuf[8])
      {
        case 0:
          Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} DIAGNOSTIC COMMAND PACKET: Packet Received and Processed Successfully");
          break;
        case 2:
          Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} DIAGNOSTIC COMMAND PACKET: CRC FAIL");
          num = (byte) 1;
          break;
        case 6:
          Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} DIAGNOSTIC COMMAND PACKET: IN VALID FUNCTION CODE");
          num = (byte) 1;
          break;
        case 35:
          Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} DIAGNOSTIC COMMAND PACKET: IN VALID DATA LENGTH");
          num = (byte) 1;
          break;
      }
      manu[0] = agdb_api.rxbuf[9];
      manu[1] = agdb_api.rxbuf[10];
      manu[2] = agdb_api.rxbuf[11];
      manu[3] = agdb_api.rxbuf[12];
      manu[4] = agdb_api.rxbuf[13];
      if (Checksum.Checksum_Calc(ref agdb_api.rxbuf, Pkt_length) == 0)
      {
        Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr}LINK CHECK RESPONSE PACKET: CHECKSUM FAILED ");
        return 9;
      }
      if (num != (byte) 0)
      {
        Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} DIAGNOSTIC COMMAND IS UNSUCCESSFUL");
        return 0;
      }
      Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} DIAGNOSTIC COMMAND IS SUCCESSFUL");
      return 1;
    }
    Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} LINK FAILURE or ADDRESSED AGDB DOESN'T EXIST");
    return 2;
  }

  public static byte agdb_optional_cmd(
    byte agdb_addr,
    string platform_no,
    ref byte[] agdb_pkt,
    ref short length)
  {
    agdb_api.pkt_buf[0] = (byte) 170;
    agdb_api.pkt_buf[1] = (byte) 204;
    agdb_api.pkt_buf[2] = (byte) 0;
    agdb_api.pkt_buf[3] = (byte) 10;
    agdb_api.pkt_buf[4] = frmMainFormIPIS.ccu_addr;
    agdb_api.pkt_buf[5] = (byte) 0;
    agdb_api.pkt_buf[6] = agdb_addr;
    agdb_api.pkt_buf[7] = (byte) 0;
    agdb_api.pkt_buf[8] = (byte) 0;
    agdb_api.pkt_buf[9] = (byte) 139;
    ushort length1 = checked ((ushort) ((int) unchecked ((ushort) ((int) (ushort) (0U << 8) | 10)) + 2));
    length = checked ((short) length1);
    int index = 0;
    while (index < (int) length1)
    {
      agdb_pkt[index] = agdb_api.pkt_buf[index];
      checked { ++index; }
    }
    Checksum.prepare_checksum(ref agdb_api.pkt_buf, length1);
    if (RS232.Serial_Write(ref agdb_api.pkt_buf, (int) length1) == 1)
      return 1;
    Log_file.Log(string.Format("Write to COM port failed, may be COM port deos't exit. Please check the COM PORT settings"));
    return 3;
  }

  public static byte agdb_opt_cmd_res_pkt(
    byte agdb_addr,
    string Platform_no,
    ref byte[] agdb_pkt,
    ref short length,
    ref byte[] manu)
  {
    if (RS232.Serial_Read(ref agdb_api.rxbuf) == (byte) 1)
    {
      ushort Pkt_length = checked ((ushort) unchecked ((int) (ushort) ((uint) (ushort) agdb_api.rxbuf[0] << 8) + (int) agdb_api.rxbuf[1]));
      length = checked ((short) ((int) Pkt_length + 2));
      agdb_pkt[0] = (byte) 170;
      agdb_pkt[1] = (byte) 204;
      byte index = 0;
      while ((uint) index < (uint) Pkt_length)
      {
        agdb_pkt[checked ((int) index + 2)] = agdb_api.rxbuf[(int) index];
        checked { ++index; }
      }
      byte num = 0;
      if ((int) agdb_api.rxbuf[2] != (int) agdb_addr & agdb_api.rxbuf[3] != (byte) 0)
      {
        Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} OPTIONAL COMMAND RESPONSE PACKET: IN CORRECT SOURCE ADDRESS");
        num = (byte) 1;
      }
      if (Pkt_length != (ushort) 16 /*0x10*/)
      {
        Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} OPTIONAL COMMAND RESPONSE PACKET: IN VALID DATA LENGTH");
        num = (byte) 1;
      }
      if (agdb_api.rxbuf[7] != (byte) 203)
      {
        Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} OPTIONAL COMMAND RESPONSE PACKET: IN VALID FUNCTION CODE");
        num = (byte) 1;
      }
      switch (agdb_api.rxbuf[8])
      {
        case 0:
          Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} OPTIONAL COMMAND PACKET: Packet Received and Processed Successfully");
          break;
        case 2:
          Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} OPTIONAL COMMAND PACKET: CRC FAIL");
          num = (byte) 1;
          break;
        case 6:
          Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} OPTIONAL COMMAND PACKET: IN VALID FUNCTION CODE");
          num = (byte) 1;
          break;
        case 35:
          Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} OPTIONAL COMMAND PACKET: IN VALID DATA LENGTH");
          num = (byte) 1;
          break;
      }
      manu[0] = agdb_api.rxbuf[9];
      manu[1] = agdb_api.rxbuf[10];
      manu[2] = agdb_api.rxbuf[11];
      manu[3] = agdb_api.rxbuf[12];
      manu[4] = agdb_api.rxbuf[13];
      if (Checksum.Checksum_Calc(ref agdb_api.rxbuf, Pkt_length) == 0)
      {
        Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr}LINK CHECK RESPONSE PACKET: CHECKSUM FAILED ");
        return 9;
      }
      if (num != (byte) 0)
      {
        Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} OPTIONAL COMMAND IS UNSUCCESSFUL");
        return 0;
      }
      Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} OPTIONAL COMMAND IS SUCCESSFUL");
      return 1;
    }
    Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} LINK FAILURE or ADDRESSED AGDB DOESN'T EXIST");
    return 2;
  }

  public static byte agdb_send_test_pkt(
    byte agdb_addr,
    string platform_no,
    ref byte[] agdb_pkt,
    ref short length)
  {
    agdb_api.pkt_buf[0] = (byte) 170;
    agdb_api.pkt_buf[1] = (byte) 204;
    agdb_api.pkt_buf[2] = (byte) 0;
    agdb_api.pkt_buf[3] = (byte) 14;
    agdb_api.pkt_buf[4] = frmMainFormIPIS.ccu_addr;
    agdb_api.pkt_buf[5] = (byte) 0;
    agdb_api.pkt_buf[6] = agdb_addr;
    agdb_api.pkt_buf[7] = (byte) 0;
    agdb_api.pkt_buf[8] = (byte) 0;
    agdb_api.pkt_buf[9] = (byte) 129;
    agdb_api.pkt_buf[10] = (byte) 3;
    agdb_api.pkt_buf[11] = (byte) 0;
    agdb_api.pkt_buf[12] = (byte) 0;
    agdb_api.pkt_buf[13] = (byte) 0;
    ushort length1 = checked ((ushort) (14 + 2));
    Checksum.prepare_checksum(ref agdb_api.pkt_buf, length1);
    length = checked ((short) length1);
    int index = 0;
    while (index < (int) length1)
    {
      agdb_pkt[index] = agdb_api.pkt_buf[index];
      checked { ++index; }
    }
    byte num = 0;
    if (RS232.Serial_Write(ref agdb_api.pkt_buf, (int) length1) != 1)
    {
      Log_file.Log(string.Format("Write to COM port failed, may be COM port deos't exit. Please check the COM PORT settings"));
      num = (byte) 3;
    }
    return num;
  }

  public static byte agdb_send_test_pkt_res(
    byte agdb_addr,
    string Platform_no,
    ref byte[] agdb_pkt,
    ref short length)
  {
    if (RS232.Serial_Read(ref agdb_api.rxbuf) == (byte) 1)
    {
      ushort Pkt_length = checked ((ushort) unchecked ((int) (ushort) ((uint) (ushort) agdb_api.rxbuf[0] << 8) + (int) agdb_api.rxbuf[1]));
      length = checked ((short) ((int) Pkt_length + 2));
      agdb_pkt[0] = (byte) 170;
      agdb_pkt[1] = (byte) 204;
      short index = 0;
      while ((int) index < (int) Pkt_length)
      {
        agdb_pkt[checked ((int) index + 2)] = agdb_api.rxbuf[(int) index];
        checked { ++index; }
      }
      short num = 0;
      if ((int) agdb_api.rxbuf[2] != (int) agdb_addr & agdb_api.rxbuf[3] != (byte) 0)
      {
        Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} DATA RESPONSE PACKET: IN CORRECT SOURCE ADDRESS");
        num = (short) 1;
      }
      if (Pkt_length != (ushort) 11)
      {
        Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} DATA RESPONSE PACKET: IN VALID DATA LENGTH");
        num = (short) 1;
      }
      if (agdb_api.rxbuf[7] != (byte) 193)
      {
        Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} DATA RESPONSE PACKET: IN-VALID RESPONSE PACKET FUNCTION CODE");
        num = (short) 1;
      }
      switch (agdb_api.rxbuf[8])
      {
        case 0:
          Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} DATA PACKET STATUS: Packet Received and proceed successfully ");
          break;
        case 2:
          Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} DATA PACKET STATUS: CRC FAIL");
          num = (short) 1;
          break;
        case 6:
          Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} DATA PACKET STATUS: IN VALID FUNCTION CODE");
          num = (short) 1;
          break;
        case 35:
          Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} DATA PACKET STATUS: IN VALID DATA LENGTH");
          num = (short) 1;
          break;
      }
      if (Checksum.Checksum_Calc(ref agdb_api.rxbuf, Pkt_length) == 0)
      {
        Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr}LINK CHECK RESPONSE PACKET: CHECKSUM FAILED ");
        return 9;
      }
      if (num != (short) 0)
      {
        Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} DATA PACKET IS UNSUCCESSFULL");
        return 0;
      }
      Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} DATA PACKET IS SUCCESSFUL");
      return 1;
    }
    Log_file.Log("PLATFORM NO:{Platform_no} AGDB Address:{agdb_addr} LINK FAILURE or ADDRESSED AGDB DOESN'T EXIST");
    return 2;
  }
}

}