# Dynamic Language Support for Advertisements

## Overview

The advertising system has been updated to support dynamic languages instead of being limited to just Hindi and English. Now the system will automatically detect and support all active languages configured in the Language Management module.

## What Changed

### Database Changes

1. **New Table**: `AdvertisementLanguageWaves`
   - Stores language-specific wave files for each advertisement
   - Supports any number of languages dynamically
   - Maintains backward compatibility with existing Hindi/English data

2. **Migration**: Existing Hindi and English wave files are automatically migrated to the new structure

### Code Changes

1. **Repository Layer**: 
   - Added `GetAdvertisementLanguageWaves()` and `SaveAdvertisementLanguageWaves()` methods
   - Updated delete operations to handle language wave files

2. **Service Layer**:
   - Added `AddAdvertisementWithLanguages()` and `UpdateAdvertisementWithLanguages()` methods
   - Maintains backward compatibility with existing methods

3. **UI Layer**:
   - Dynamic language wave file selectors based on active languages
   - Updated DataGridView to show supported languages
   - Automatic language detection and loading

## How It Works

### Adding a New Language

1. Go to **Settings > Language Management**
2. Add a new language (e.g., "Gujarati", "Tamil", etc.)
3. Set the wave folder path for the language
4. Mark the language as "Active"

### Creating Advertisements with Multiple Languages

1. Open **Advertising Management**
2. Create a new advertisement
3. The system will automatically show wave file selectors for all active languages
4. Browse and select wave files for each language
5. Save the advertisement

### Automatic Language Detection

- The system automatically detects all active languages from the Language Management module
- No code changes required when adding new languages
- Wave file selectors are dynamically generated based on active languages

## Database Migration

The migration is handled automatically when the application starts. The `SQLiteAdvertisingRepository.InitializeDatabase()` method:

1. Creates the new `AdvertisementLanguageWaves` table
2. Migrates existing Hindi and English wave files
3. Creates necessary indexes for performance
4. Sets up triggers for timestamp updates

## Backward Compatibility

- Existing advertisements continue to work without changes
- Hindi and English wave files are preserved during migration
- Old API methods still work for basic operations

## File Structure

```
Database/
├── migrate_add_dynamic_language_support.sql  # Migration script
├── test_migration.sql                        # Test script
└── run_migration.cs                          # Migration utility

Repositories/
├── IAdvertisingRepository.cs                 # Updated interface
└── SQLiteAdvertisingRepository.cs            # Updated implementation

Services/
└── AdvertisingService.cs                     # Updated service layer

Forms/Advertising/
└── AdvertisingForm.cs                        # Updated UI
```

## Testing

To test the migration:

1. Run the application
2. Check that existing advertisements still work
3. Add a new language in Language Management
4. Create a new advertisement and verify the new language appears
5. Check the database to see the new table structure

## Troubleshooting

### Foreign Key Errors
If you encounter foreign key errors during migration:
1. The migration script has been updated to remove foreign key constraints
2. Relationships are handled in the application layer
3. Delete operations properly clean up related language wave files

### Missing Languages
If languages don't appear in the advertising form:
1. Ensure the language is marked as "Active" in Language Management
2. Check that the LanguageManager is properly initialized
3. Verify the wave folder path is set correctly

### Data Migration Issues
If existing data isn't migrated:
1. Check the database logs for migration errors
2. Run the test migration script to verify the process
3. Manually check the `AdvertisementLanguageWaves` table

## Future Enhancements

- Support for language-specific quotas
- Language-specific scheduling
- Multi-language announcement sequences
- Language preference settings per user 