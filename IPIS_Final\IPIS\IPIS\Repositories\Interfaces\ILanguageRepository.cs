using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using IPIS.Models;

namespace IPIS.Repositories.Interfaces
{
    public interface ILanguageRepository
    {
        Task<List<Language>> GetAllAsync();
        Task<List<Language>> GetActiveAsync();
        Task<Language> GetByIdAsync(int id);
        Task<Language> GetByCodeAsync(string code);
        Task<Language> GetDefaultAsync();
        Task<int> AddAsync(Language language);
        Task<bool> UpdateAsync(Language language);
        Task<bool> DeleteAsync(int id);
        Task<bool> SetDefaultAsync(int id);
        Task<bool> ExistsAsync(string code);
        Task<bool> ExistsAsync(int id);
    }
} 