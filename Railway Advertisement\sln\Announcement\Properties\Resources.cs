﻿using System;
using System.CodeDom.Compiler;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Globalization;
using System.Resources;
using System.Runtime.CompilerServices;

namespace Announcement.Properties
{
	// Token: 0x02000012 RID: 18
	[GeneratedCode("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
	[DebuggerNonUserCode]
	[CompilerGenerated]
	internal class Resources
	{
		// Token: 0x060000E5 RID: 229 RVA: 0x00007B30 File Offset: 0x00005D30
		internal Resources()
		{
		}

		// Token: 0x17000016 RID: 22
		// (get) Token: 0x060000E6 RID: 230 RVA: 0x0001F29C File Offset: 0x0001D49C
		[EditorBrowsable(EditorBrowsableState.Advanced)]
		internal static ResourceManager ResourceManager
		{
			get
			{
				bool flag = Resources.resourceMan == null;
				if (flag)
				{
					ResourceManager resourceManager = new ResourceManager("Announcement.Properties.Resources", typeof(Resources).Assembly);
					Resources.resourceMan = resourceManager;
				}
				return Resources.resourceMan;
			}
		}

		// Token: 0x17000017 RID: 23
		// (get) Token: 0x060000E7 RID: 231 RVA: 0x0001F2E4 File Offset: 0x0001D4E4
		// (set) Token: 0x060000E8 RID: 232 RVA: 0x0001F2FB File Offset: 0x0001D4FB
		[EditorBrowsable(EditorBrowsableState.Advanced)]
		internal static CultureInfo Culture
		{
			get
			{
				return Resources.resourceCulture;
			}
			set
			{
				Resources.resourceCulture = value;
			}
		}

		// Token: 0x17000018 RID: 24
		// (get) Token: 0x060000E9 RID: 233 RVA: 0x0001F304 File Offset: 0x0001D504
		internal static Bitmap Del
		{
			get
			{
				object @object = Resources.ResourceManager.GetObject("Del", Resources.resourceCulture);
				return (Bitmap)@object;
			}
		}

		// Token: 0x17000019 RID: 25
		// (get) Token: 0x060000EA RID: 234 RVA: 0x0001F334 File Offset: 0x0001D534
		internal static Bitmap Edit
		{
			get
			{
				object @object = Resources.ResourceManager.GetObject("Edit", Resources.resourceCulture);
				return (Bitmap)@object;
			}
		}

		// Token: 0x1700001A RID: 26
		// (get) Token: 0x060000EB RID: 235 RVA: 0x0001F364 File Offset: 0x0001D564
		internal static Bitmap Exit
		{
			get
			{
				object @object = Resources.ResourceManager.GetObject("Exit", Resources.resourceCulture);
				return (Bitmap)@object;
			}
		}

		// Token: 0x1700001B RID: 27
		// (get) Token: 0x060000EC RID: 236 RVA: 0x0001F394 File Offset: 0x0001D594
		internal static Bitmap Insert
		{
			get
			{
				object @object = Resources.ResourceManager.GetObject("Insert", Resources.resourceCulture);
				return (Bitmap)@object;
			}
		}

		// Token: 0x1700001C RID: 28
		// (get) Token: 0x060000ED RID: 237 RVA: 0x0001F3C4 File Offset: 0x0001D5C4
		internal static Bitmap Save
		{
			get
			{
				object @object = Resources.ResourceManager.GetObject("Save", Resources.resourceCulture);
				return (Bitmap)@object;
			}
		}

		// Token: 0x040001B3 RID: 435
		private static ResourceManager resourceMan;

		// Token: 0x040001B4 RID: 436
		private static CultureInfo resourceCulture;
	}
}
