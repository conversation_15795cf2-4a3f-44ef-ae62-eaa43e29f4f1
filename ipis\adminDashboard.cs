// Decompiled with JetBrains decompiler
// Type: ipis.adminDashboard
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Runtime.CompilerServices;
using System.Windows.Forms;
using System.Xml;

namespace ipis
{

[DesignerGenerated]
public class adminDashboard : Form
{
  private static List<WeakReference> __ENCList = new List<WeakReference>();
  private IContainer components;
  [AccessedThroughProperty("ComboBox1")]
  private ComboBox _ComboBox1;
  [AccessedThroughProperty("Label1")]
  private Label _Label1;
  [AccessedThroughProperty("ComboBox2")]
  private ComboBox _ComboBox2;
  [AccessedThroughProperty("Label2")]
  private Label _Label2;
  [AccessedThroughProperty("TabControl1")]
  private TabControl _TabControl1;
  [AccessedThroughProperty("TabPage1")]
  private TabPage _TabPage1;
  [AccessedThroughProperty("Button1")]
  private Button _Button1;
  [AccessedThroughProperty("TabPage2")]
  private TabPage _TabPage2;
  [AccessedThroughProperty("TabPage3")]
  private TabPage _TabPage3;
  [AccessedThroughProperty("lblCurrentBase")]
  private Label _lblCurrentBase;
  [AccessedThroughProperty("Label3")]
  private Label _Label3;
  [AccessedThroughProperty("lblAdLoop")]
  private Label _lblAdLoop;
  [AccessedThroughProperty("Label4")]
  private Label _Label4;
  [AccessedThroughProperty("lblSetInt")]
  private Label _lblSetInt;
  [AccessedThroughProperty("Label5")]
  private Label _Label5;
  [AccessedThroughProperty("Button2")]
  private Button _Button2;
  [AccessedThroughProperty("TextBox1")]
  private TextBox _TextBox1;
  [AccessedThroughProperty("Button3")]
  private Button _Button3;
  [AccessedThroughProperty("TabPage4")]
  private TabPage _TabPage4;
  [AccessedThroughProperty("txtBufferTime")]
  private TextBox _txtBufferTime;
  [AccessedThroughProperty("Button4")]
  private Button _Button4;
  [AccessedThroughProperty("lblBufferTime")]
  private Label _lblBufferTime;
  [AccessedThroughProperty("Label7")]
  private Label _Label7;
  [AccessedThroughProperty("TabPage5")]
  private TabPage _TabPage5;
  [AccessedThroughProperty("Button5")]
  private Button _Button5;
  [AccessedThroughProperty("ListBox1")]
  private ListBox _ListBox1;
  [AccessedThroughProperty("ListBox2")]
  private ListBox _ListBox2;
  [AccessedThroughProperty("TabPage6")]
  private TabPage _TabPage6;
  [AccessedThroughProperty("Button6")]
  private Button _Button6;
  [AccessedThroughProperty("ListBox4")]
  private ListBox _ListBox4;
  [AccessedThroughProperty("ListBox3")]
  private ListBox _ListBox3;
  private DataSet ds;

  [DebuggerNonUserCode]
  static adminDashboard()
  {
  }

  public adminDashboard()
  {
    this.Load += new EventHandler(this.adminDashboard_Load);
    adminDashboard.__ENCAddToList((object) this);
    this.ds = new DataSet();
    this.InitializeComponent();
  }

  [DebuggerNonUserCode]
  private static void __ENCAddToList(object value)
  {
    lock (adminDashboard.__ENCList)
    {
      if (adminDashboard.__ENCList.Count == adminDashboard.__ENCList.Capacity)
      {
        int index1 = 0;
        int num = checked (adminDashboard.__ENCList.Count - 1);
        int index2 = 0;
        while (index2 <= num)
        {
          if (adminDashboard.__ENCList[index2].IsAlive)
          {
            if (index2 != index1)
              adminDashboard.__ENCList[index1] = adminDashboard.__ENCList[index2];
            checked { ++index1; }
          }
          checked { ++index2; }
        }
        adminDashboard.__ENCList.RemoveRange(index1, checked (adminDashboard.__ENCList.Count - index1));
        adminDashboard.__ENCList.Capacity = adminDashboard.__ENCList.Count;
      }
      adminDashboard.__ENCList.Add(new WeakReference(RuntimeHelpers.GetObjectValue(value)));
    }
  }

  [DebuggerNonUserCode]
  protected override void Dispose(bool disposing)
  {
    try
    {
      if (!disposing || this.components == null)
        return;
      this.components.Dispose();
    }
    finally
    {
      base.Dispose(disposing);
    }
  }

  [DebuggerStepThrough]
  private void InitializeComponent()
  {
    this.ComboBox1 = new ComboBox();
    this.Label1 = new Label();
    this.ComboBox2 = new ComboBox();
    this.Label2 = new Label();
    this.TabControl1 = new TabControl();
    this.TabPage1 = new TabPage();
    this.lblCurrentBase = new Label();
    this.Label3 = new Label();
    this.Button1 = new Button();
    this.TabPage2 = new TabPage();
    this.Button3 = new Button();
    this.lblAdLoop = new Label();
    this.Label4 = new Label();
    this.TabPage3 = new TabPage();
    this.TextBox1 = new TextBox();
    this.Button2 = new Button();
    this.lblSetInt = new Label();
    this.Label5 = new Label();
    this.TabPage4 = new TabPage();
    this.txtBufferTime = new TextBox();
    this.Button4 = new Button();
    this.lblBufferTime = new Label();
    this.Label7 = new Label();
    this.TabPage5 = new TabPage();
    this.ListBox2 = new ListBox();
    this.Button5 = new Button();
    this.ListBox1 = new ListBox();
    this.TabPage6 = new TabPage();
    this.ListBox3 = new ListBox();
    this.ListBox4 = new ListBox();
    this.Button6 = new Button();
    this.TabControl1.SuspendLayout();
    this.TabPage1.SuspendLayout();
    this.TabPage2.SuspendLayout();
    this.TabPage3.SuspendLayout();
    this.TabPage4.SuspendLayout();
    this.TabPage5.SuspendLayout();
    this.TabPage6.SuspendLayout();
    this.SuspendLayout();
    this.ComboBox1.FormattingEnabled = true;
    ComboBox comboBox1_1 = this.ComboBox1;
    Point point1 = new Point(213, 110);
    Point point2 = point1;
    comboBox1_1.Location = point2;
    ComboBox comboBox1_2 = this.ComboBox1;
    Padding padding1 = new Padding(4);
    Padding padding2 = padding1;
    comboBox1_2.Margin = padding2;
    this.ComboBox1.Name = "ComboBox1";
    ComboBox comboBox1_3 = this.ComboBox1;
    Size size1 = new Size(179, 24);
    Size size2 = size1;
    comboBox1_3.Size = size2;
    this.ComboBox1.TabIndex = 0;
    this.Label1.AutoSize = true;
    Label label1_1 = this.Label1;
    point1 = new Point(34, 110);
    Point point3 = point1;
    label1_1.Location = point3;
    Label label1_2 = this.Label1;
    padding1 = new Padding(4, 0, 4, 0);
    Padding padding3 = padding1;
    label1_2.Margin = padding3;
    this.Label1.Name = "Label1";
    Label label1_3 = this.Label1;
    size1 = new Size(96 /*0x60*/, 16 /*0x10*/);
    Size size3 = size1;
    label1_3.Size = size3;
    this.Label1.TabIndex = 1;
    this.Label1.Text = "Base Station";
    this.ComboBox2.FormattingEnabled = true;
    this.ComboBox2.Items.AddRange(new object[10]
    {
      (object) "1",
      (object) "2",
      (object) "3",
      (object) "4",
      (object) "5",
      (object) "6",
      (object) "7",
      (object) "8",
      (object) "9",
      (object) "10"
    });
    ComboBox comboBox2_1 = this.ComboBox2;
    point1 = new Point(117, 155);
    Point point4 = point1;
    comboBox2_1.Location = point4;
    ComboBox comboBox2_2 = this.ComboBox2;
    padding1 = new Padding(4);
    Padding padding4 = padding1;
    comboBox2_2.Margin = padding4;
    this.ComboBox2.Name = "ComboBox2";
    ComboBox comboBox2_3 = this.ComboBox2;
    size1 = new Size(179, 24);
    Size size4 = size1;
    comboBox2_3.Size = size4;
    this.ComboBox2.TabIndex = 2;
    this.Label2.AutoSize = true;
    Label label2_1 = this.Label2;
    point1 = new Point(9, 155);
    Point point5 = point1;
    label2_1.Location = point5;
    Label label2_2 = this.Label2;
    padding1 = new Padding(4, 0, 4, 0);
    Padding padding5 = padding1;
    label2_2.Margin = padding5;
    this.Label2.Name = "Label2";
    Label label2_3 = this.Label2;
    size1 = new Size(93, 16 /*0x10*/);
    Size size5 = size1;
    label2_3.Size = size5;
    this.Label2.TabIndex = 3;
    this.Label2.Text = "Ad Loop Set";
    this.TabControl1.Controls.Add((Control) this.TabPage2);
    this.TabControl1.Controls.Add((Control) this.TabPage3);
    this.TabControl1.Controls.Add((Control) this.TabPage4);
    this.TabControl1.Controls.Add((Control) this.TabPage5);
    this.TabControl1.Controls.Add((Control) this.TabPage6);
    this.TabControl1.Controls.Add((Control) this.TabPage1);
    TabControl tabControl1_1 = this.TabControl1;
    point1 = new Point(4, 2);
    Point point6 = point1;
    tabControl1_1.Location = point6;
    TabControl tabControl1_2 = this.TabControl1;
    padding1 = new Padding(4);
    Padding padding6 = padding1;
    tabControl1_2.Margin = padding6;
    this.TabControl1.Name = "TabControl1";
    this.TabControl1.SelectedIndex = 0;
    TabControl tabControl1_3 = this.TabControl1;
    size1 = new Size(654, 287);
    Size size6 = size1;
    tabControl1_3.Size = size6;
    this.TabControl1.TabIndex = 4;
    this.TabPage1.Controls.Add((Control) this.lblCurrentBase);
    this.TabPage1.Controls.Add((Control) this.Label3);
    this.TabPage1.Controls.Add((Control) this.Button1);
    this.TabPage1.Controls.Add((Control) this.ComboBox1);
    this.TabPage1.Controls.Add((Control) this.Label1);
    TabPage tabPage1_1 = this.TabPage1;
    point1 = new Point(4, 25);
    Point point7 = point1;
    tabPage1_1.Location = point7;
    TabPage tabPage1_2 = this.TabPage1;
    padding1 = new Padding(4);
    Padding padding7 = padding1;
    tabPage1_2.Margin = padding7;
    this.TabPage1.Name = "TabPage1";
    TabPage tabPage1_3 = this.TabPage1;
    padding1 = new Padding(4);
    Padding padding8 = padding1;
    tabPage1_3.Padding = padding8;
    TabPage tabPage1_4 = this.TabPage1;
    size1 = new Size(646, 258);
    Size size7 = size1;
    tabPage1_4.Size = size7;
    this.TabPage1.TabIndex = 0;
    this.TabPage1.Text = "Base Station";
    this.TabPage1.UseVisualStyleBackColor = true;
    this.lblCurrentBase.AutoSize = true;
    Label lblCurrentBase1 = this.lblCurrentBase;
    point1 = new Point(267, 43);
    Point point8 = point1;
    lblCurrentBase1.Location = point8;
    this.lblCurrentBase.Name = "lblCurrentBase";
    Label lblCurrentBase2 = this.lblCurrentBase;
    size1 = new Size(0, 16 /*0x10*/);
    Size size8 = size1;
    lblCurrentBase2.Size = size8;
    this.lblCurrentBase.TabIndex = 4;
    this.Label3.AutoSize = true;
    Label label3_1 = this.Label3;
    point1 = new Point(73, 43);
    Point point9 = point1;
    label3_1.Location = point9;
    this.Label3.Name = "Label3";
    Label label3_2 = this.Label3;
    size1 = new Size(149, 16 /*0x10*/);
    Size size9 = size1;
    label3_2.Size = size9;
    this.Label3.TabIndex = 3;
    this.Label3.Text = "Current Base Station";
    Button button1_1 = this.Button1;
    point1 = new Point(443, 104);
    Point point10 = point1;
    button1_1.Location = point10;
    Button button1_2 = this.Button1;
    padding1 = new Padding(4);
    Padding padding9 = padding1;
    button1_2.Margin = padding9;
    this.Button1.Name = "Button1";
    Button button1_3 = this.Button1;
    size1 = new Size(112 /*0x70*/, 28);
    Size size10 = size1;
    button1_3.Size = size10;
    this.Button1.TabIndex = 2;
    this.Button1.Text = "Update";
    this.Button1.UseVisualStyleBackColor = true;
    this.TabPage2.Controls.Add((Control) this.Button3);
    this.TabPage2.Controls.Add((Control) this.lblAdLoop);
    this.TabPage2.Controls.Add((Control) this.Label4);
    this.TabPage2.Controls.Add((Control) this.Label2);
    this.TabPage2.Controls.Add((Control) this.ComboBox2);
    TabPage tabPage2_1 = this.TabPage2;
    point1 = new Point(4, 25);
    Point point11 = point1;
    tabPage2_1.Location = point11;
    TabPage tabPage2_2 = this.TabPage2;
    padding1 = new Padding(4);
    Padding padding10 = padding1;
    tabPage2_2.Margin = padding10;
    this.TabPage2.Name = "TabPage2";
    TabPage tabPage2_3 = this.TabPage2;
    padding1 = new Padding(4);
    Padding padding11 = padding1;
    tabPage2_3.Padding = padding11;
    TabPage tabPage2_4 = this.TabPage2;
    size1 = new Size(646, 258);
    Size size11 = size1;
    tabPage2_4.Size = size11;
    this.TabPage2.TabIndex = 1;
    this.TabPage2.Text = "Ad Loop Set";
    this.TabPage2.UseVisualStyleBackColor = true;
    Button button3_1 = this.Button3;
    point1 = new Point(344, 155);
    Point point12 = point1;
    button3_1.Location = point12;
    this.Button3.Name = "Button3";
    Button button3_2 = this.Button3;
    size1 = new Size(75, 23);
    Size size12 = size1;
    button3_2.Size = size12;
    this.Button3.TabIndex = 6;
    this.Button3.Text = "Update";
    this.Button3.UseVisualStyleBackColor = true;
    this.lblAdLoop.AutoSize = true;
    Label lblAdLoop1 = this.lblAdLoop;
    point1 = new Point(179, 94);
    Point point13 = point1;
    lblAdLoop1.Location = point13;
    this.lblAdLoop.Name = "lblAdLoop";
    Label lblAdLoop2 = this.lblAdLoop;
    size1 = new Size(0, 16 /*0x10*/);
    Size size13 = size1;
    lblAdLoop2.Size = size13;
    this.lblAdLoop.TabIndex = 5;
    this.Label4.AutoSize = true;
    Label label4_1 = this.Label4;
    point1 = new Point(12, 93);
    Point point14 = point1;
    label4_1.Location = point14;
    this.Label4.Name = "Label4";
    Label label4_2 = this.Label4;
    size1 = new Size(123, 16 /*0x10*/);
    Size size14 = size1;
    label4_2.Size = size14;
    this.Label4.TabIndex = 4;
    this.Label4.Text = "Current Loop Set";
    this.TabPage3.Controls.Add((Control) this.TextBox1);
    this.TabPage3.Controls.Add((Control) this.Button2);
    this.TabPage3.Controls.Add((Control) this.lblSetInt);
    this.TabPage3.Controls.Add((Control) this.Label5);
    TabPage tabPage3_1 = this.TabPage3;
    point1 = new Point(4, 25);
    Point point15 = point1;
    tabPage3_1.Location = point15;
    TabPage tabPage3_2 = this.TabPage3;
    padding1 = new Padding(4);
    Padding padding12 = padding1;
    tabPage3_2.Margin = padding12;
    this.TabPage3.Name = "TabPage3";
    TabPage tabPage3_3 = this.TabPage3;
    padding1 = new Padding(4);
    Padding padding13 = padding1;
    tabPage3_3.Padding = padding13;
    TabPage tabPage3_4 = this.TabPage3;
    size1 = new Size(646, 258);
    Size size15 = size1;
    tabPage3_4.Size = size15;
    this.TabPage3.TabIndex = 2;
    this.TabPage3.Text = "Set Interval";
    this.TabPage3.UseVisualStyleBackColor = true;
    TextBox textBox1_1 = this.TextBox1;
    point1 = new Point(117, 88);
    Point point16 = point1;
    textBox1_1.Location = point16;
    this.TextBox1.Name = "TextBox1";
    TextBox textBox1_2 = this.TextBox1;
    size1 = new Size(100, 22);
    Size size16 = size1;
    textBox1_2.Size = size16;
    this.TextBox1.TabIndex = 3;
    Button button2_1 = this.Button2;
    point1 = new Point(256 /*0x0100*/, 87);
    Point point17 = point1;
    button2_1.Location = point17;
    this.Button2.Name = "Button2";
    Button button2_2 = this.Button2;
    size1 = new Size(75, 23);
    Size size17 = size1;
    button2_2.Size = size17;
    this.Button2.TabIndex = 2;
    this.Button2.Text = "Update";
    this.Button2.UseVisualStyleBackColor = true;
    this.lblSetInt.AutoSize = true;
    Label lblSetInt1 = this.lblSetInt;
    point1 = new Point(200, 34);
    Point point18 = point1;
    lblSetInt1.Location = point18;
    this.lblSetInt.Name = "lblSetInt";
    Label lblSetInt2 = this.lblSetInt;
    size1 = new Size(0, 16 /*0x10*/);
    Size size18 = size1;
    lblSetInt2.Size = size18;
    this.lblSetInt.TabIndex = 1;
    this.Label5.AutoSize = true;
    Label label5_1 = this.Label5;
    point1 = new Point(31 /*0x1F*/, 34);
    Point point19 = point1;
    label5_1.Location = point19;
    this.Label5.Name = "Label5";
    Label label5_2 = this.Label5;
    size1 = new Size(146, 16 /*0x10*/);
    Size size19 = size1;
    label5_2.Size = size19;
    this.Label5.TabIndex = 0;
    this.Label5.Text = "Curent Time Interval";
    this.TabPage4.Controls.Add((Control) this.txtBufferTime);
    this.TabPage4.Controls.Add((Control) this.Button4);
    this.TabPage4.Controls.Add((Control) this.lblBufferTime);
    this.TabPage4.Controls.Add((Control) this.Label7);
    TabPage tabPage4_1 = this.TabPage4;
    point1 = new Point(4, 25);
    Point point20 = point1;
    tabPage4_1.Location = point20;
    this.TabPage4.Name = "TabPage4";
    TabPage tabPage4_2 = this.TabPage4;
    size1 = new Size(646, 258);
    Size size20 = size1;
    tabPage4_2.Size = size20;
    this.TabPage4.TabIndex = 3;
    this.TabPage4.Text = "Buffer Time";
    this.TabPage4.UseVisualStyleBackColor = true;
    TextBox txtBufferTime1 = this.txtBufferTime;
    point1 = new Point(169, 95);
    Point point21 = point1;
    txtBufferTime1.Location = point21;
    this.txtBufferTime.Name = "txtBufferTime";
    TextBox txtBufferTime2 = this.txtBufferTime;
    size1 = new Size(100, 22);
    Size size21 = size1;
    txtBufferTime2.Size = size21;
    this.txtBufferTime.TabIndex = 7;
    Button button4_1 = this.Button4;
    point1 = new Point(308, 94);
    Point point22 = point1;
    button4_1.Location = point22;
    this.Button4.Name = "Button4";
    Button button4_2 = this.Button4;
    size1 = new Size(75, 23);
    Size size22 = size1;
    button4_2.Size = size22;
    this.Button4.TabIndex = 6;
    this.Button4.Text = "Update";
    this.Button4.UseVisualStyleBackColor = true;
    this.lblBufferTime.AutoSize = true;
    Label lblBufferTime1 = this.lblBufferTime;
    point1 = new Point(252, 41);
    Point point23 = point1;
    lblBufferTime1.Location = point23;
    this.lblBufferTime.Name = "lblBufferTime";
    Label lblBufferTime2 = this.lblBufferTime;
    size1 = new Size(0, 16 /*0x10*/);
    Size size23 = size1;
    lblBufferTime2.Size = size23;
    this.lblBufferTime.TabIndex = 5;
    this.Label7.AutoSize = true;
    Label label7_1 = this.Label7;
    point1 = new Point(83, 41);
    Point point24 = point1;
    label7_1.Location = point24;
    this.Label7.Name = "Label7";
    Label label7_2 = this.Label7;
    size1 = new Size(135, 16 /*0x10*/);
    Size size24 = size1;
    label7_2.Size = size24;
    this.Label7.TabIndex = 4;
    this.Label7.Text = "Curent Buffer Time";
    this.TabPage5.Controls.Add((Control) this.ListBox2);
    this.TabPage5.Controls.Add((Control) this.Button5);
    this.TabPage5.Controls.Add((Control) this.ListBox1);
    TabPage tabPage5_1 = this.TabPage5;
    point1 = new Point(4, 25);
    Point point25 = point1;
    tabPage5_1.Location = point25;
    this.TabPage5.Name = "TabPage5";
    TabPage tabPage5_2 = this.TabPage5;
    size1 = new Size(646, 258);
    Size size25 = size1;
    tabPage5_2.Size = size25;
    this.TabPage5.TabIndex = 4;
    this.TabPage5.Text = "Ads";
    this.TabPage5.UseVisualStyleBackColor = true;
    this.ListBox2.FormattingEnabled = true;
    this.ListBox2.ItemHeight = 16 /*0x10*/;
    ListBox listBox2_1 = this.ListBox2;
    point1 = new Point(340, 10);
    Point point26 = point1;
    listBox2_1.Location = point26;
    this.ListBox2.Name = "ListBox2";
    ListBox listBox2_2 = this.ListBox2;
    size1 = new Size(301, 212);
    Size size26 = size1;
    listBox2_2.Size = size26;
    this.ListBox2.TabIndex = 3;
    Button button5_1 = this.Button5;
    point1 = new Point(428, 232);
    Point point27 = point1;
    button5_1.Location = point27;
    this.Button5.Name = "Button5";
    Button button5_2 = this.Button5;
    size1 = new Size(190, 23);
    Size size27 = size1;
    button5_2.Size = size27;
    this.Button5.TabIndex = 2;
    this.Button5.Text = "Generate Playlist";
    this.Button5.UseVisualStyleBackColor = true;
    this.ListBox1.FormattingEnabled = true;
    this.ListBox1.ItemHeight = 16 /*0x10*/;
    ListBox listBox1_1 = this.ListBox1;
    point1 = new Point(8, 10);
    Point point28 = point1;
    listBox1_1.Location = point28;
    this.ListBox1.Name = "ListBox1";
    ListBox listBox1_2 = this.ListBox1;
    size1 = new Size(301, 212);
    Size size28 = size1;
    listBox1_2.Size = size28;
    this.ListBox1.TabIndex = 1;
    this.TabPage6.Controls.Add((Control) this.Button6);
    this.TabPage6.Controls.Add((Control) this.ListBox4);
    this.TabPage6.Controls.Add((Control) this.ListBox3);
    TabPage tabPage6_1 = this.TabPage6;
    point1 = new Point(4, 25);
    Point point29 = point1;
    tabPage6_1.Location = point29;
    this.TabPage6.Name = "TabPage6";
    TabPage tabPage6_2 = this.TabPage6;
    padding1 = new Padding(3);
    Padding padding14 = padding1;
    tabPage6_2.Padding = padding14;
    TabPage tabPage6_3 = this.TabPage6;
    size1 = new Size(646, 258);
    Size size29 = size1;
    tabPage6_3.Size = size29;
    this.TabPage6.TabIndex = 5;
    this.TabPage6.Text = "Special Msgs";
    this.TabPage6.UseVisualStyleBackColor = true;
    this.ListBox3.FormattingEnabled = true;
    this.ListBox3.ItemHeight = 16 /*0x10*/;
    ListBox listBox3_1 = this.ListBox3;
    point1 = new Point(10, 10);
    Point point30 = point1;
    listBox3_1.Location = point30;
    this.ListBox3.Name = "ListBox3";
    ListBox listBox3_2 = this.ListBox3;
    size1 = new Size(301, 212);
    Size size30 = size1;
    listBox3_2.Size = size30;
    this.ListBox3.TabIndex = 0;
    this.ListBox4.FormattingEnabled = true;
    this.ListBox4.ItemHeight = 16 /*0x10*/;
    ListBox listBox4_1 = this.ListBox4;
    point1 = new Point(327, 10);
    Point point31 = point1;
    listBox4_1.Location = point31;
    this.ListBox4.Name = "ListBox4";
    ListBox listBox4_2 = this.ListBox4;
    size1 = new Size(301, 212);
    Size size31 = size1;
    listBox4_2.Size = size31;
    this.ListBox4.TabIndex = 1;
    Button button6_1 = this.Button6;
    point1 = new Point(425, 228);
    Point point32 = point1;
    button6_1.Location = point32;
    this.Button6.Name = "Button6";
    Button button6_2 = this.Button6;
    size1 = new Size(190, 23);
    Size size32 = size1;
    button6_2.Size = size32;
    this.Button6.TabIndex = 3;
    this.Button6.Text = "Generate Playlist";
    this.Button6.UseVisualStyleBackColor = true;
    this.AutoScaleDimensions = new SizeF(9f, 16f);
    this.AutoScaleMode = AutoScaleMode.Font;
    size1 = new Size(661, 293);
    this.ClientSize = size1;
    this.Controls.Add((Control) this.TabControl1);
    this.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    padding1 = new Padding(4);
    this.Margin = padding1;
    this.Name = "adminDashboard";
    this.Text = "NTES Admin(Powered by Ninja Media Creations)";
    this.TabControl1.ResumeLayout(false);
    this.TabPage1.ResumeLayout(false);
    this.TabPage1.PerformLayout();
    this.TabPage2.ResumeLayout(false);
    this.TabPage2.PerformLayout();
    this.TabPage3.ResumeLayout(false);
    this.TabPage3.PerformLayout();
    this.TabPage4.ResumeLayout(false);
    this.TabPage4.PerformLayout();
    this.TabPage5.ResumeLayout(false);
    this.TabPage6.ResumeLayout(false);
    this.ResumeLayout(false);
  }

  internal virtual ComboBox ComboBox1
  {
    [DebuggerNonUserCode] get { return this._ComboBox1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._ComboBox1 = value;
    }
  }

  internal virtual Label Label1
  {
    [DebuggerNonUserCode] get { return this._Label1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Label1 = value; }
  }

  internal virtual ComboBox ComboBox2
  {
    [DebuggerNonUserCode] get { return this._ComboBox2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._ComboBox2 = value;
    }
  }

  internal virtual Label Label2
  {
    [DebuggerNonUserCode] get { return this._Label2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Label2 = value; }
  }

  internal virtual TabControl TabControl1
  {
    [DebuggerNonUserCode] get { return this._TabControl1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._TabControl1 = value;
    }
  }

  internal virtual TabPage TabPage1
  {
    [DebuggerNonUserCode] get { return this._TabPage1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.TabPage1_Click);
      if (this._TabPage1 != null)
        this._TabPage1.Click -= eventHandler;
      this._TabPage1 = value;
      if (this._TabPage1 == null)
        return;
      this._TabPage1.Click += eventHandler;
    }
  }

  internal virtual Button Button1
  {
    [DebuggerNonUserCode] get { return this._Button1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.Button1_Click);
      if (this._Button1 != null)
        this._Button1.Click -= eventHandler;
      this._Button1 = value;
      if (this._Button1 == null)
        return;
      this._Button1.Click += eventHandler;
    }
  }

  internal virtual TabPage TabPage2
  {
    [DebuggerNonUserCode] get { return this._TabPage2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._TabPage2 = value; }
  }

  internal virtual TabPage TabPage3
  {
    [DebuggerNonUserCode] get { return this._TabPage3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._TabPage3 = value; }
  }

  internal virtual Label lblCurrentBase
  {
    [DebuggerNonUserCode] get { return this._lblCurrentBase; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblCurrentBase = value;
    }
  }

  internal virtual Label Label3
  {
    [DebuggerNonUserCode] get { return this._Label3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Label3 = value; }
  }

  internal virtual Label lblAdLoop
  {
    [DebuggerNonUserCode] get { return this._lblAdLoop; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblAdLoop = value;
    }
  }

  internal virtual Label Label4
  {
    [DebuggerNonUserCode] get { return this._Label4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Label4 = value; }
  }

  internal virtual Label lblSetInt
  {
    [DebuggerNonUserCode] get { return this._lblSetInt; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblSetInt = value;
    }
  }

  internal virtual Label Label5
  {
    [DebuggerNonUserCode] get { return this._Label5; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Label5 = value; }
  }

  internal virtual Button Button2
  {
    [DebuggerNonUserCode] get { return this._Button2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.Button2_Click);
      if (this._Button2 != null)
        this._Button2.Click -= eventHandler;
      this._Button2 = value;
      if (this._Button2 == null)
        return;
      this._Button2.Click += eventHandler;
    }
  }

  internal virtual TextBox TextBox1
  {
    [DebuggerNonUserCode] get { return this._TextBox1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._TextBox1 = value; }
  }

  internal virtual Button Button3
  {
    [DebuggerNonUserCode] get { return this._Button3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.Button3_Click);
      if (this._Button3 != null)
        this._Button3.Click -= eventHandler;
      this._Button3 = value;
      if (this._Button3 == null)
        return;
      this._Button3.Click += eventHandler;
    }
  }

  internal virtual TabPage TabPage4
  {
    [DebuggerNonUserCode] get { return this._TabPage4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._TabPage4 = value; }
  }

  internal virtual TextBox txtBufferTime
  {
    [DebuggerNonUserCode] get { return this._txtBufferTime; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtBufferTime = value;
    }
  }

  internal virtual Button Button4
  {
    [DebuggerNonUserCode] get { return this._Button4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.Button4_Click);
      if (this._Button4 != null)
        this._Button4.Click -= eventHandler;
      this._Button4 = value;
      if (this._Button4 == null)
        return;
      this._Button4.Click += eventHandler;
    }
  }

  internal virtual Label lblBufferTime
  {
    [DebuggerNonUserCode] get { return this._lblBufferTime; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblBufferTime = value;
    }
  }

  internal virtual Label Label7
  {
    [DebuggerNonUserCode] get { return this._Label7; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Label7 = value; }
  }

  internal virtual TabPage TabPage5
  {
    [DebuggerNonUserCode] get { return this._TabPage5; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._TabPage5 = value; }
  }

  internal virtual Button Button5
  {
    [DebuggerNonUserCode] get { return this._Button5; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.Button5_Click);
      if (this._Button5 != null)
        this._Button5.Click -= eventHandler;
      this._Button5 = value;
      if (this._Button5 == null)
        return;
      this._Button5.Click += eventHandler;
    }
  }

  internal virtual ListBox ListBox1
  {
    [DebuggerNonUserCode] get { return this._ListBox1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.ListBox1_SelectedIndexChanged);
      if (this._ListBox1 != null)
        this._ListBox1.SelectedIndexChanged -= eventHandler;
      this._ListBox1 = value;
      if (this._ListBox1 == null)
        return;
      this._ListBox1.SelectedIndexChanged += eventHandler;
    }
  }

  internal virtual ListBox ListBox2
  {
    [DebuggerNonUserCode] get { return this._ListBox2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.ListBox2_SelectedIndexChanged);
      if (this._ListBox2 != null)
        this._ListBox2.SelectedIndexChanged -= eventHandler;
      this._ListBox2 = value;
      if (this._ListBox2 == null)
        return;
      this._ListBox2.SelectedIndexChanged += eventHandler;
    }
  }

  internal virtual TabPage TabPage6
  {
    [DebuggerNonUserCode] get { return this._TabPage6; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._TabPage6 = value; }
  }

  internal virtual Button Button6
  {
    [DebuggerNonUserCode] get { return this._Button6; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.Button6_Click);
      if (this._Button6 != null)
        this._Button6.Click -= eventHandler;
      this._Button6 = value;
      if (this._Button6 == null)
        return;
      this._Button6.Click += eventHandler;
    }
  }

  internal virtual ListBox ListBox4
  {
    [DebuggerNonUserCode] get { return this._ListBox4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.ListBox4_SelectedIndexChanged);
      if (this._ListBox4 != null)
        this._ListBox4.SelectedIndexChanged -= eventHandler;
      this._ListBox4 = value;
      if (this._ListBox4 == null)
        return;
      this._ListBox4.SelectedIndexChanged += eventHandler;
    }
  }

  internal virtual ListBox ListBox3
  {
    [DebuggerNonUserCode] get { return this._ListBox3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.ListBox3_SelectedIndexChanged);
      if (this._ListBox3 != null)
        this._ListBox3.SelectedIndexChanged -= eventHandler;
      this._ListBox3 = value;
      if (this._ListBox3 == null)
        return;
      this._ListBox3.SelectedIndexChanged += eventHandler;
    }
  }

  private void adminDashboard_Load(object sender, EventArgs e)
  {
    this.fillData();
  }

  public void fillData()
  {
    ComboBox comboBox1 = this.ComboBox1;
    network_db_read.get_BaseStn(ref comboBox1);
    this.ComboBox1 = comboBox1;
    DataSet ds = new DataSet();
    network_db_read.get_config(ref ds);
    if (ds.Tables[0].Rows.Count > 0)
    {
      this.lblCurrentBase.Text = Conversions.ToString(ds.Tables[0].Rows[0]["basestn"]);
      this.lblAdLoop.Text = Conversions.ToString(ds.Tables[0].Rows[0]["adLoop"]);
      this.lblSetInt.Text = ds.Tables[0].Rows[0]["setInterval"].ToString() + "  Min's";
      this.lblBufferTime.Text = ds.Tables[0].Rows[0]["bufferTime"].ToString() + "  Min's";
    }
    string[] files1 = Directory.GetFiles("C:\\IPIS\\voice\\Ads", "*.wav");
    int index1 = 0;
    while (index1 < files1.Length)
    {
      this.ListBox1.Items.Add((object) files1[index1].Replace("C:\\IPIS\\voice\\Ads\\", ""));
      checked { ++index1; }
    }
    string[] files2 = Directory.GetFiles("C:\\IPIS\\voice\\special_messages\\English\\", "*.wav");
    int index2 = 0;
    while (index2 < files2.Length)
    {
      this.ListBox3.Items.Add((object) files2[index2].Replace("C:\\IPIS\\voice\\special_messages\\English\\", ""));
      checked { ++index2; }
    }
  }

  private void Button1_Click(object sender, EventArgs e)
  {
    network_db_read.set_Update_baseStn(Conversions.ToString(this.ComboBox1.SelectedValue));
    this.fillData();
  }

  private void TabPage1_Click(object sender, EventArgs e)
  {
  }

  private void Button2_Click(object sender, EventArgs e)
  {
    network_db_read.set_Update_setInterval(this.TextBox1.Text);
    this.fillData();
  }

  private void Button3_Click(object sender, EventArgs e)
  {
    network_db_read.set_Update_adLoop(Conversions.ToString(this.ComboBox2.SelectedItem));
    this.fillData();
  }

  private void Button4_Click(object sender, EventArgs e)
  {
    network_db_read.set_Update_BufferTime(this.txtBufferTime.Text);
    this.fillData();
  }

  private void Button5_Click(object sender, EventArgs e)
  {
    XmlDocument xmlDocument = new XmlDocument();
    XmlNode xmlDeclaration = (XmlNode) xmlDocument.CreateXmlDeclaration("1.0", "UTF-8", (string) null);
    xmlDocument.AppendChild(xmlDeclaration);
    XmlNode element1 = (XmlNode) xmlDocument.CreateElement("sounds");
    xmlDocument.AppendChild(element1);
    try
    {
      foreach (object obj in this.ListBox2.Items)
      {
        string str = Conversions.ToString(obj);
        XmlNode element2 = (XmlNode) xmlDocument.CreateElement("Ads");
        element1.AppendChild(element2);
        XmlNode element3 = (XmlNode) xmlDocument.CreateElement("label");
        element3.AppendChild((XmlNode) xmlDocument.CreateTextNode("Ads"));
        element2.AppendChild(element3);
        XmlNode element4 = (XmlNode) xmlDocument.CreateElement("data");
        element4.AppendChild((XmlNode) xmlDocument.CreateTextNode("c:\\IPIS\\voice\\Ads\\" + str));
        element2.AppendChild(element4);
      }
    }
    finally
    {
      IEnumerator enumerator = null;
      if (enumerator is IDisposable)
        (enumerator as IDisposable).Dispose();
    }
    xmlDocument.Save("C:\\IPIS\\voice\\Ads\\voicexml.xml");
  }

  private void ListBox1_SelectedIndexChanged(object sender, EventArgs e)
  {
    this.ListBox2.Items.Add(RuntimeHelpers.GetObjectValue(this.ListBox1.Items[this.ListBox1.SelectedIndex]));
  }

  private void ListBox2_SelectedIndexChanged(object sender, EventArgs e)
  {
    if (this.ListBox2.SelectedIndex < 0)
      return;
    this.ListBox2.Items.Remove(RuntimeHelpers.GetObjectValue(this.ListBox2.Items[this.ListBox2.SelectedIndex]));
  }

  private void Button6_Click(object sender, EventArgs e)
  {
    XmlDocument xmlDocument = new XmlDocument();
    XmlNode xmlDeclaration = (XmlNode) xmlDocument.CreateXmlDeclaration("1.0", "UTF-8", (string) null);
    xmlDocument.AppendChild(xmlDeclaration);
    XmlNode element1 = (XmlNode) xmlDocument.CreateElement("sounds");
    xmlDocument.AppendChild(element1);
    try
    {
      foreach (object obj in this.ListBox2.Items)
      {
        string str = Conversions.ToString(obj);
        XmlNode element2 = (XmlNode) xmlDocument.CreateElement("Spl");
        element1.AppendChild(element2);
        XmlNode element3 = (XmlNode) xmlDocument.CreateElement("label");
        element3.AppendChild((XmlNode) xmlDocument.CreateTextNode("Spl"));
        element2.AppendChild(element3);
        XmlNode element4 = (XmlNode) xmlDocument.CreateElement("data");
        element4.AppendChild((XmlNode) xmlDocument.CreateTextNode("C:\\IPIS\\voice\\special_messages\\English\\" + str));
        element2.AppendChild(element4);
      }
    }
    finally
    {
      IEnumerator enumerator = null;
      if (enumerator is IDisposable)
        (enumerator as IDisposable).Dispose();
    }
    xmlDocument.Save("C:\\IPIS\\voice\\special_messages\\English\\voicexml.xml");
  }

  private void ListBox3_SelectedIndexChanged(object sender, EventArgs e)
  {
    this.ListBox4.Items.Add(RuntimeHelpers.GetObjectValue(this.ListBox3.Items[this.ListBox3.SelectedIndex]));
  }

  private void ListBox4_SelectedIndexChanged(object sender, EventArgs e)
  {
    if (this.ListBox4.SelectedIndex < 0)
      return;
    this.ListBox4.Items.Remove(RuntimeHelpers.GetObjectValue(this.ListBox4.Items[this.ListBox4.SelectedIndex]));
  }
}
}