﻿// Decompiled with JetBrains decompiler
// Type: ipis.My.MyApplication
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using Microsoft.VisualBasic.ApplicationServices;
using Microsoft.VisualBasic.CompilerServices;
using System;
using System.CodeDom.Compiler;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

namespace ipis.My
{

[EditorBrowsable(EditorBrowsableState.Never)]
[GeneratedCode("MyTemplate", "********")]
internal class MyApplication : WindowsFormsApplicationBase
{
  private static List<WeakReference> __ENCList = new List<WeakReference>();

  [DebuggerNonUserCode]
  static MyApplication()
  {
  }

  [DebuggerNonUserCode]
  private static void __ENCAddToList(object value)
  {
    lock (MyApplication.__ENCList)
    {
      if (MyApplication.__ENCList.Count == MyApplication.__ENCList.Capacity)
      {
        int index1 = 0;
        int num = checked (MyApplication.__ENCList.Count - 1);
        int index2 = 0;
        while (index2 <= num)
        {
          if (MyApplication.__ENCList[index2].IsAlive)
          {
            if (index2 != index1)
              MyApplication.__ENCList[index1] = MyApplication.__ENCList[index2];
            checked { ++index1; }
          }
          checked { ++index2; }
        }
        MyApplication.__ENCList.RemoveRange(index1, checked (MyApplication.__ENCList.Count - index1));
        MyApplication.__ENCList.Capacity = MyApplication.__ENCList.Count;
      }
      MyApplication.__ENCList.Add(new WeakReference(RuntimeHelpers.GetObjectValue(value)));
    }
  }

  [EditorBrowsable(EditorBrowsableState.Advanced)]
  [STAThread]
  [DebuggerHidden]
  [MethodImpl(MethodImplOptions.NoInlining | MethodImplOptions.NoOptimization)]
  internal static void Main(string[] Args)
  {
    try
    {
      Application.SetCompatibleTextRenderingDefault(WindowsFormsApplicationBase.UseCompatibleTextRendering);
    }
    finally
    {
    }
    MyProject.Application.Run(Args);
  }

  private void MyApplication_Shutdown(object sender, EventArgs e)
  {
    this.ApplicationContext.ExitThread();
    this.ShutdownStyle = ShutdownMode.AfterAllFormsClose;
    this.ApplicationContext.ExitThread();
    if (Operators.CompareString(Process.GetCurrentProcess().ProcessName, "ipis", false) != 0)
      return;
    Process.GetCurrentProcess().Kill();
  }

  [DebuggerStepThrough]
  public MyApplication()
    : base(AuthenticationMode.Windows)
  {
    this.Shutdown += new ShutdownEventHandler(this.MyApplication_Shutdown);
    MyApplication.__ENCAddToList((object) this);
    this.IsSingleInstance = false;
    this.EnableVisualStyles = true;
    this.SaveMySettingsOnExit = true;
    this.ShutdownStyle = ShutdownMode.AfterMainFormCloses;
  }

  [DebuggerStepThrough]
  protected override void OnCreateMainForm()
  {
    this.MainForm = (Form) MyProject.Forms.frmMainFormIPIS;
  }
}
}
