using System;
using System.Collections.Generic;
using System.Text.Json;
using System.Text.RegularExpressions;
using IPIS.Models;
using IPIS.Services;
using IPIS.Services.Interfaces;
using IPIS.Repositories;

namespace IPIS.Utils
{
    /// <summary>
    /// Static logger utility class for easy logging throughout the application
    /// </summary>
    public static class Logger
    {
        private static readonly Lazy<ILoggingService> _loggingService = new Lazy<ILoggingService>(
            () => new LoggingService(new SQLiteLoggingRepository()));

        private static ILoggingService LoggingService => _loggingService.Value;

        #region System Logging Methods

        public static void LogDebug(LogCategory category, string message, string? details = null, string? source = null)
        {
            try
            {
                LoggingService.LogDebug(category, message, details, source);
            }
            catch (Exception ex)
            {
                // Prevent logging errors from crashing the application
                System.Diagnostics.Debug.WriteLine($"Logging error: {ex.Message}");
            }
        }

        public static void LogInfo(LogCategory category, string message, string? details = null, string? source = null)
        {
            try
            {
                LoggingService.LogInfo(category, message, details, source);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Logging error: {ex.Message}");
            }
        }

        public static void LogWarning(LogCategory category, string message, string? details = null, string? source = null)
        {
            try
            {
                LoggingService.LogWarning(category, message, details, source);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Logging error: {ex.Message}");
            }
        }

        public static void LogError(LogCategory category, string message, string? details = null, string? source = null, Exception? exception = null)
        {
            try
            {
                LoggingService.LogError(category, message, details, source, exception);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Logging error: {ex.Message}");
            }
        }

        public static void LogCritical(LogCategory category, string message, string? details = null, string? source = null, Exception? exception = null)
        {
            try
            {
                LoggingService.LogCritical(category, message, details, source, exception);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Logging error: {ex.Message}");
            }
        }

        #endregion

        #region User Activity Logging Methods

        public static void LogUserActivity(string action, LogCategory category, string? entityType = null, string? entityId = null,
                                          string? oldValues = null, string? newValues = null)
        {
            try
            {
                LoggingService.LogUserActivity(action, category, entityType, entityId, oldValues, newValues);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Logging error: {ex.Message}");
            }
        }

        #endregion

        #region Convenience Methods for Common Operations

        // Flag to control verbose logging during bulk operations
        private static bool _suppressVerboseLogging = false;

        public static void SetVerboseLogging(bool enabled)
        {
            _suppressVerboseLogging = !enabled;
        }

        public static void LogTrainAdded(string trainNo, string trainName)
        {
            if (!_suppressVerboseLogging)
            {
                LogInfo(LogCategory.TrainManagement, $"Train added: {trainNo}",
                       $"Train Name: {trainName}", "TrainManagement");
            }
            LogUserActivity("Add Train", LogCategory.TrainManagement, "Train", trainNo, null,
                           $"Train_No: {trainNo}, Train_Name: {trainName}");
        }

        public static void LogTrainUpdated(string trainNo, string oldValues, string newValues)
        {
            if (!_suppressVerboseLogging)
            {
                LogInfo(LogCategory.TrainManagement, $"Train updated: {trainNo}",
                       "Train details modified", "TrainManagement");
            }
            LogUserActivity("Update Train", LogCategory.TrainManagement, "Train", trainNo, oldValues, newValues);
        }

        public static void LogTrainDeleted(string trainNo, string trainName)
        {
            if (!_suppressVerboseLogging)
            {
                LogInfo(LogCategory.TrainManagement, $"Train deleted: {trainNo}",
                       $"Train Name: {trainName}", "TrainManagement");
            }
            LogUserActivity("Delete Train", LogCategory.TrainManagement, "Train", trainNo,
                           $"Train_No: {trainNo}, Train_Name: {trainName}", null);
        }

        public static void LogOnlineTrainAdded(string trainNo, string trainName, string status, string platform, string? changedPF = null, string? divertedFrom = null)
        {
            if (!_suppressVerboseLogging)
            {
                var details = $"Train Name: {trainName}, Status: {status}, Platform: {platform}";
                if (!string.IsNullOrEmpty(changedPF))
                    details += $", Changed PF: {changedPF}";
                if (!string.IsNullOrEmpty(divertedFrom))
                    details += $", Diverted From: {divertedFrom}";

                LogInfo(LogCategory.TrainManagement, $"Online train added: {trainNo}", details, "TrainService.AddOnlineTrain");
            }

            // Always log user activity
            var newValues = $"Train_No: {trainNo}, Train_Name: {trainName}, Status: {status}, Platform: {platform}";
            if (!string.IsNullOrEmpty(changedPF))
                newValues += $", Changed_PF: {changedPF}";
            if (!string.IsNullOrEmpty(divertedFrom))
                newValues += $", Diverted_From: {divertedFrom}";

            LogUserActivity("Add Online Train", LogCategory.TrainManagement, "OnlineTrain", trainNo, null, newValues);
        }

        public static void LogStationAdded(string stationCode, string stationName)
        {
            LogInfo(LogCategory.StationManagement, $"Station added: {stationCode}",
                   $"Station Name: {stationName}", "StationManagement");
            LogUserActivity("Add Station", LogCategory.StationManagement, "Station", stationCode, null,
                           $"Station_Code: {stationCode}, Station_Name: {stationName}");
        }

        public static void LogStationUpdated(string stationCode, string oldValues, string newValues)
        {
            LogInfo(LogCategory.StationManagement, $"Station updated: {stationCode}",
                   "Station details modified", "StationManagement");
            LogUserActivity("Update Station", LogCategory.StationManagement, "Station", stationCode, oldValues, newValues);
        }

        public static void LogStationDeleted(string stationCode, string stationName)
        {
            LogInfo(LogCategory.StationManagement, $"Station deleted: {stationCode}",
                   $"Station Name: {stationName}", "StationManagement");
            LogUserActivity("Delete Station", LogCategory.StationManagement, "Station", stationCode,
                           $"Station_Code: {stationCode}, Station_Name: {stationName}", null);
        }

        public static void LogUserLogin(string username)
        {
            LogInfo(LogCategory.Security, $"User logged in: {username}",
                   $"Login time: {DateTime.Now:yyyy-MM-dd HH:mm:ss}", "Authentication");
            LogUserActivity("Login", LogCategory.Security, "User", username);
        }

        public static void LogUserLogout(string username)
        {
            LogInfo(LogCategory.Security, $"User logged out: {username}",
                   $"Logout time: {DateTime.Now:yyyy-MM-dd HH:mm:ss}", "Authentication");
            LogUserActivity("Logout", LogCategory.Security, "User", username);
        }

        public static void LogUserCreated(string username, string role)
        {
            LogInfo(LogCategory.UserManagement, $"User created: {username}",
                   $"Role: {role}", "UserManagement");
            LogUserActivity("Create User", LogCategory.UserManagement, "User", username, null,
                           $"Username: {username}, Role: {role}");
        }

        public static void LogUserUpdated(string username, string oldValues, string newValues)
        {
            LogInfo(LogCategory.UserManagement, $"User updated: {username}",
                   "User details modified", "UserManagement");
            LogUserActivity("Update User", LogCategory.UserManagement, "User", username, oldValues, newValues);
        }

        public static void LogUserDeleted(string username, string role)
        {
            LogInfo(LogCategory.UserManagement, $"User deleted: {username}",
                   $"Role: {role}", "UserManagement");
            LogUserActivity("Delete User", LogCategory.UserManagement, "User", username,
                           $"Username: {username}, Role: {role}", null);
        }

        public static void LogAnnouncementMade(string announcementType, string message)
        {
            LogInfo(LogCategory.Announcement, $"Announcement made: {announcementType}",
                   $"Message: {message}", "AnnouncementSystem");
            LogUserActivity("Make Announcement", LogCategory.Announcement, "Announcement", null, null,
                           $"Type: {announcementType}, Message: {message}");
        }

        public static void LogSystemStartup()
        {
            LogInfo(LogCategory.System, "IPIS System started",
                   $"Startup time: {DateTime.Now:yyyy-MM-dd HH:mm:ss}", "System");
        }

        public static void LogSystemShutdown()
        {
            LogInfo(LogCategory.System, "IPIS System shutdown",
                   $"Shutdown time: {DateTime.Now:yyyy-MM-dd HH:mm:ss}", "System");
        }

        #endregion

        #region Batch Logging Methods

        public static void LogBatchOperation(LogCategory category, string operationType, string summary, List<string> details, string? source = null)
        {
            try
            {
                LoggingService.LogBatchOperation(category, operationType, summary, details, source);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Batch logging error: {ex.Message}");
            }
        }

        public static void LogSystemBootSummary(List<string> bootSteps, TimeSpan bootTime)
        {
            try
            {
                LoggingService.LogSystemBootSummary(bootSteps, bootTime);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Boot summary logging error: {ex.Message}");
            }
        }

        public static void LogDataLoadingSummary(string operationType, int totalRecords, int successCount, int failureCount, TimeSpan duration, List<string>? errors = null)
        {
            try
            {
                LoggingService.LogDataLoadingSummary(operationType, totalRecords, successCount, failureCount, duration, errors);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Data loading summary logging error: {ex.Message}");
            }
        }

        public static void LogDatabaseOperation(string operation, string table, bool success, string? details = null)
        {
            // Only log database operations if verbose logging is enabled or if it's a failure
            if (!_suppressVerboseLogging || !success)
            {
                if (success)
                {
                    LogInfo(LogCategory.Database, $"Database operation successful: {operation}",
                           $"Table: {table}, Details: {details}", "Database");
                }
                else
                {
                    LogError(LogCategory.Database, $"Database operation failed: {operation}",
                            $"Table: {table}, Details: {details}", "Database");
                }
            }
        }

        public static void LogAudioPlayback(string audioFile, bool success, string? error = null)
        {
            if (success)
            {
                LogInfo(LogCategory.Audio, $"Audio playback successful: {audioFile}",
                       null, "AudioSystem");
            }
            else
            {
                LogError(LogCategory.Audio, $"Audio playback failed: {audioFile}",
                        $"Error: {error}", "AudioSystem");
            }
        }

        #endregion

        #region Data Masking and JSON Utilities

        /// <summary>
        /// Masks confidential data in parameters
        /// </summary>
        private static string MaskConfidentialData(string input)
        {
            if (string.IsNullOrEmpty(input))
                return input;

            // Mask passwords
            input = Regex.Replace(input, @"(password|pwd|pass)\s*[:=]\s*[^\s,}]+",
                                 "$1: ***MASKED***", RegexOptions.IgnoreCase);

            // Mask credit card numbers
            input = Regex.Replace(input, @"\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b",
                                 "****-****-****-****");

            // Mask phone numbers (partial)
            input = Regex.Replace(input, @"\b(\d{3})[\s-]?(\d{3})[\s-]?(\d{4})\b",
                                 "$1-***-$3");

            // Mask email addresses (partial)
            input = Regex.Replace(input, @"\b([a-zA-Z0-9._%+-]+)@([a-zA-Z0-9.-]+\.[a-zA-Z]{2,})\b",
                                 "***@$2");

            return input;
        }

        /// <summary>
        /// Converts parameters to JSON format with masking
        /// </summary>
        private static string FormatParametersAsJson(Dictionary<string, object> parameters)
        {
            try
            {
                if (parameters == null || parameters.Count == 0)
                    return "{}";

                var maskedParams = new Dictionary<string, object>();
                foreach (var kvp in parameters)
                {
                    var value = kvp.Value?.ToString() ?? "null";
                    maskedParams[kvp.Key] = MaskConfidentialData(value);
                }

                return JsonSerializer.Serialize(maskedParams, new JsonSerializerOptions
                {
                    WriteIndented = false
                });
            }
            catch (Exception ex)
            {
                return $"{{\"error\": \"Failed to serialize parameters: {ex.Message}\"}}";
            }
        }

        /// <summary>
        /// Creates a JSON response object
        /// </summary>
        private static string CreateJsonResponse(bool success, string message, object? data = null, string? error = null)
        {
            try
            {
                var response = new
                {
                    success = success,
                    message = message,
                    timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff"),
                    data = data,
                    error = error
                };

                var json = JsonSerializer.Serialize(response, new JsonSerializerOptions
                {
                    WriteIndented = false
                });

                return MaskConfidentialData(json);
            }
            catch (Exception ex)
            {
                return $"{{\"success\": false, \"message\": \"JSON serialization failed\", \"error\": \"{ex.Message}\"}}";
            }
        }

        #endregion

        #region Enhanced Logging Methods with JSON and Parameters

        /// <summary>
        /// Logs a request with parameters
        /// </summary>
        public static void LogRequest(LogCategory category, string operation, Dictionary<string, object>? parameters = null, string? source = null)
        {
            try
            {
                var parametersJson = FormatParametersAsJson(parameters ?? new Dictionary<string, object>());
                var requestJson = CreateJsonResponse(true, $"Request: {operation}", new { parameters = parametersJson });

                LogInfo(category, $"Request: {operation}", requestJson, source ?? "RequestHandler");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Request logging error: {ex.Message}");
            }
        }

        /// <summary>
        /// Logs a response with data
        /// </summary>
        public static void LogResponse(LogCategory category, string operation, bool success, object? responseData = null, string? error = null, string? source = null)
        {
            try
            {
                var responseJson = CreateJsonResponse(success, $"Response: {operation}", responseData, error);

                if (success)
                {
                    LogInfo(category, $"Response: {operation}", responseJson, source ?? "ResponseHandler");
                }
                else
                {
                    LogError(category, $"Response: {operation}", responseJson, source ?? "ResponseHandler");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Response logging error: {ex.Message}");
            }
        }

        /// <summary>
        /// Logs a complete request-response cycle
        /// </summary>
        public static void LogRequestResponse(LogCategory category, string operation,
            Dictionary<string, object>? requestParams = null,
            bool success = true,
            object? responseData = null,
            string? error = null,
            string? source = null)
        {
            try
            {
                var requestJson = FormatParametersAsJson(requestParams ?? new Dictionary<string, object>());
                var fullResponseJson = CreateJsonResponse(success, $"Operation: {operation}",
                    new
                    {
                        request = requestJson,
                        response = responseData
                    }, error);

                if (success)
                {
                    LogInfo(category, $"Operation completed: {operation}", fullResponseJson, source ?? "OperationHandler");
                }
                else
                {
                    LogError(category, $"Operation failed: {operation}", fullResponseJson, source ?? "OperationHandler");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Request-Response logging error: {ex.Message}");
            }
        }

        #endregion
    }
}
