# Announcement Management System

## Overview
The Announcement Management System is a comprehensive solution for managing train announcement templates, sequences, and audio file sequences in the IPIS (Integrated Passenger Information System). It allows users to create and manage different announcement types for various languages with customizable audio file sequences.

## Features

### 1. Template Management
- Create and manage announcement templates (e.g., "RUNNING RIGHT TIME", "IS ARRIVING ON")
- Support for multiple languages per template
- Active/inactive status management
- Template description and metadata

### 2. Sequence Management
- Language-specific sequences for each template
- Audio file and placeholder support
- Reorderable sequence items
- Individual item management

### 3. Audio File Management
- Add audio files to sequences
- Support for WAV format files
- File path management relative to WAVE directory
- Individual file playback testing

### 4. Placeholder Support
- Dynamic data placeholders for train information
- Test data input for preview
- Common placeholders:
  - `TRAIN_NUMBER` - Train number (e.g., 12345)
  - `TRAIN_NAME` - Train name (e.g., Rajdhani Express)
  - `FROM_STATION` - Source station name
  - `TO_STATION` - Destination station name
  - `VIA_STATION` - Via station name
  - `PLATFORM_NUMBER` - Platform number
  - `EXPECTED_TIME` - Expected arrival/departure time
  - `DELAY_TIME` - Delay time
  - `CURRENT_TIME` - Current time

### 5. Playback Features
- Individual audio file playback
- Complete sequence playback
- Test data integration for placeholders
- Audio file validation

## Database Structure

### Tables

#### 1. AnnouncementTemplates
```sql
CREATE TABLE AnnouncementTemplates (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    Name TEXT NOT NULL UNIQUE,
    Description TEXT,
    IsActive BOOLEAN NOT NULL DEFAULT 1,
    CreatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt DATETIME
);
```

#### 2. AnnouncementSequences
```sql
CREATE TABLE AnnouncementSequences (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    TemplateId INTEGER NOT NULL,
    LanguageId INTEGER NOT NULL,
    Name TEXT NOT NULL,
    IsActive BOOLEAN NOT NULL DEFAULT 1,
    CreatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt DATETIME,
    FOREIGN KEY (TemplateId) REFERENCES AnnouncementTemplates(Id) ON DELETE CASCADE,
    FOREIGN KEY (LanguageId) REFERENCES Languages(Id) ON DELETE CASCADE,
    UNIQUE(TemplateId, LanguageId)
);
```

#### 3. SequenceItems
```sql
CREATE TABLE SequenceItems (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    SequenceId INTEGER NOT NULL,
    OrderIndex INTEGER NOT NULL,
    Type INTEGER NOT NULL, -- 1 = AudioFile, 2 = Placeholder
    Content TEXT NOT NULL, -- Audio file path or placeholder name
    Description TEXT,
    IsActive BOOLEAN NOT NULL DEFAULT 1,
    CreatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt DATETIME,
    FOREIGN KEY (SequenceId) REFERENCES AnnouncementSequences(Id) ON DELETE CASCADE
);
```

### Default Data
The system includes pre-configured templates and sample sequences:

#### Default Templates
- IS ARRIVING ON
- HAS ARRIVED ON
- WILL ARRIVE SHORTLY
- RUNNING RIGHT TIME
- RUNNING LATE
- INDEFINITE LATE
- CANCELLED
- PLATFORM CHANGED
- TERMINATED
- DEPARTING

#### Sample Sequence (RUNNING RIGHT TIME - English)
1. `SPL/TADA.wav` - Attention bell
2. `ENGLISH/STD/STD1.wav` - "May I have your attention please"
3. `TRAIN_NUMBER` - Train number placeholder
4. `TRAIN_NAME` - Train name placeholder
5. `ENGLISH/STD/STD2.wav` - "Is running on time"

## Usage Instructions

### 1. Database Setup
Run the initialization script to create the required tables:
```sql
-- Execute the SQL script from Database/initialize_announcement_tables.sql
```

### 2. Creating a New Template
1. Open the Announcement Management Form
2. Navigate to the Templates tab
3. Click "Add Template"
4. Enter template name and description
5. Save the template

### 3. Creating a Sequence
1. Select a template and language
2. Click "Add Sequence"
3. Enter sequence name
4. Save the sequence

### 4. Managing Sequence Items
1. Select a sequence
2. Click "Manage Items"
3. Add audio files or placeholders
4. Reorder items using Up/Down buttons
5. Test individual items or complete sequence
6. Save changes

### 5. Adding Audio Files
1. Click "Add Audio File"
2. Browse to the WAVE directory
3. Select the desired WAV file
4. The file will be added to the sequence

### 6. Adding Placeholders
1. Click "Add Placeholder"
2. Select from available placeholders
3. The placeholder will be added to the sequence

### 7. Testing Sequences
1. Enter test data for placeholders
2. Click "Play Item" to test individual items
3. Click "Play Complete Sequence" to test the full sequence

## File Structure

```
IPIS/
├── Models/
│   ├── AnnouncementTemplate.cs
│   ├── AnnouncementSequence.cs
│   └── SequenceItem.cs
├── Repositories/
│   ├── Interfaces/
│   │   ├── IAnnouncementTemplateRepository.cs
│   │   ├── IAnnouncementSequenceRepository.cs
│   │   └── ISequenceItemRepository.cs
│   ├── SQLiteAnnouncementTemplateRepository.cs
│   ├── SQLiteAnnouncementSequenceRepository.cs
│   └── SQLiteSequenceItemRepository.cs
├── Services/
│   └── AnnouncementTemplateService.cs
├── Forms/Announcement/
│   ├── AnnouncementManagementForm.cs
│   ├── SequenceItemManagementForm.cs
│   └── TestAnnouncementForm.cs
├── Database/
│   └── initialize_announcement_tables.sql
└── data/WAVE/
    ├── SPL/
    ├── ENGLISH/
    ├── HINDI/
    └── ...
```

## Audio File Organization

The system expects audio files to be organized in the following structure:
```
data/WAVE/
├── SPL/                    # Special audio files
│   ├── TADA.wav           # Attention bell
│   ├── Bell.wav           # Bell sound
│   └── Contact*.wav       # Contact sounds
├── ENGLISH/               # English language files
│   ├── STD/              # Standard messages
│   ├── CITY/             # City names
│   ├── TRNO/             # Train numbers
│   ├── TRNAME/           # Train names
│   ├── PF/               # Platform numbers
│   ├── HOUR/             # Hour pronunciations
│   └── MIN/              # Minute pronunciations
├── HINDI/                 # Hindi language files
│   ├── STD/              # Standard messages
│   ├── CITY/             # City names
│   ├── TRNO/             # Train numbers
│   ├── TRNAME/           # Train names
│   ├── PF/               # Platform numbers
│   ├── HOUR/             # Hour pronunciations
│   └── MIN/              # Minute pronunciations
└── TRAIN TYPE/           # Train type announcements
```

## Integration with Existing System

The announcement management system integrates with the existing IPIS system:

1. **Language System**: Uses existing language configuration
2. **Audio Files**: Leverages existing WAVE file structure
3. **Database**: Extends existing database with new tables
4. **UI**: Follows existing form design patterns

## Future Enhancements

1. **Advanced Playback**: Sequential audio playback with timing control
2. **Audio Mixing**: Combine multiple audio files
3. **Text-to-Speech**: Generate audio from text for placeholders
4. **Scheduling**: Schedule announcements based on train data
5. **Analytics**: Track announcement usage and effectiveness
6. **Export/Import**: Backup and restore announcement configurations
7. **Multi-language Support**: Support for additional languages
8. **Audio Effects**: Volume control, fade in/out effects

## Troubleshooting

### Common Issues

1. **Audio files not found**
   - Ensure files are in the correct WAVE directory structure
   - Check file paths are relative to the WAVE directory
   - Verify file permissions

2. **Database errors**
   - Run the initialization script
   - Check foreign key constraints
   - Verify database connection

3. **Playback issues**
   - Ensure WAV files are valid
   - Check audio device configuration
   - Verify file encoding

### Debug Information
- Check the status bar for operation feedback
- Review error messages in message boxes
- Monitor database operations in logs

## Support

For technical support or questions about the announcement management system, please refer to the main IPIS documentation or contact the development team. 