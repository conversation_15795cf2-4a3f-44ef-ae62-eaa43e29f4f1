// Decompiled with JetBrains decompiler
// Type: ipis.frmNetworkCGDB
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using ipis.My;
using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

namespace ipis
{

[DesignerGenerated]
public class frmNetworkCGDB : Form
{
  private static List<WeakReference> __ENCList = new List<WeakReference>();
  private IContainer components;
  [AccessedThroughProperty("txtCgdbMsgSwDly")]
  private TextBox _txtCgdbMsgSwDly;
  [AccessedThroughProperty("lblMsgSwDly")]
  private Label _lblMsgSwDly;
  [AccessedThroughProperty("lblPfno")]
  private Label _lblPfno;
  [AccessedThroughProperty("txtCgdbDir")]
  private ComboBox _txtCgdbDir;
  [AccessedThroughProperty("lblDir")]
  private Label _lblDir;
  [AccessedThroughProperty("btnExit")]
  private Button _btnExit;
  [AccessedThroughProperty("btnOk")]
  private Button _btnOk;
  [AccessedThroughProperty("txtCgdbAddress")]
  private TextBox _txtCgdbAddress;
  [AccessedThroughProperty("lblAddress")]
  private Label _lblAddress;
  [AccessedThroughProperty("lblName")]
  private Label _lblName;
  [AccessedThroughProperty("txtMultiCastAddress")]
  private TextBox _txtMultiCastAddress;
  [AccessedThroughProperty("lblMultiCastAddress")]
  private Label _lblMultiCastAddress;
  [AccessedThroughProperty("txtCgdbName")]
  private TextBox _txtCgdbName;
  [AccessedThroughProperty("cmbCgdbPfno")]
  private ComboBox _cmbCgdbPfno;
  public static string cgdb_name;
  public static byte cgdb_addr;
  public static string cgdb_platform_no;
  public static string cgdb_direction;
  public static byte cgdb_msg_sw_dly;

  [DebuggerNonUserCode]
  static frmNetworkCGDB()
  {
  }

  [DebuggerNonUserCode]
  public frmNetworkCGDB()
  {
    this.Load += new EventHandler(this.frmNetworkCGDB_Load);
    frmNetworkCGDB.__ENCAddToList((object) this);
    this.InitializeComponent();
  }

  [DebuggerNonUserCode]
  private static void __ENCAddToList(object value)
  {
    lock (frmNetworkCGDB.__ENCList)
    {
      if (frmNetworkCGDB.__ENCList.Count == frmNetworkCGDB.__ENCList.Capacity)
      {
        int index1 = 0;
        int num = checked (frmNetworkCGDB.__ENCList.Count - 1);
        int index2 = 0;
        while (index2 <= num)
        {
          if (frmNetworkCGDB.__ENCList[index2].IsAlive)
          {
            if (index2 != index1)
              frmNetworkCGDB.__ENCList[index1] = frmNetworkCGDB.__ENCList[index2];
            checked { ++index1; }
          }
          checked { ++index2; }
        }
        frmNetworkCGDB.__ENCList.RemoveRange(index1, checked (frmNetworkCGDB.__ENCList.Count - index1));
        frmNetworkCGDB.__ENCList.Capacity = frmNetworkCGDB.__ENCList.Count;
      }
      frmNetworkCGDB.__ENCList.Add(new WeakReference(RuntimeHelpers.GetObjectValue(value)));
    }
  }

  [DebuggerNonUserCode]
  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  [DebuggerStepThrough]
  private void InitializeComponent()
  {
    this.txtCgdbMsgSwDly = new TextBox();
    this.lblMsgSwDly = new Label();
    this.lblPfno = new Label();
    this.txtCgdbDir = new ComboBox();
    this.lblDir = new Label();
    this.btnExit = new Button();
    this.btnOk = new Button();
    this.txtCgdbAddress = new TextBox();
    this.lblAddress = new Label();
    this.lblName = new Label();
    this.txtMultiCastAddress = new TextBox();
    this.lblMultiCastAddress = new Label();
    this.txtCgdbName = new TextBox();
    this.cmbCgdbPfno = new ComboBox();
    this.SuspendLayout();
    this.txtCgdbMsgSwDly.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtCgdbMsgSwDly1 = this.txtCgdbMsgSwDly;
    Point point1 = new Point(265, 298);
    Point point2 = point1;
    txtCgdbMsgSwDly1.Location = point2;
    this.txtCgdbMsgSwDly.MaxLength = 2;
    this.txtCgdbMsgSwDly.Name = "txtCgdbMsgSwDly";
    TextBox txtCgdbMsgSwDly2 = this.txtCgdbMsgSwDly;
    Size size1 = new Size(45, 22);
    Size size2 = size1;
    txtCgdbMsgSwDly2.Size = size2;
    this.txtCgdbMsgSwDly.TabIndex = 6;
    this.txtCgdbMsgSwDly.TextAlign = HorizontalAlignment.Center;
    this.lblMsgSwDly.AutoSize = true;
    this.lblMsgSwDly.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblMsgSwDly1 = this.lblMsgSwDly;
    point1 = new Point(34, 298);
    Point point3 = point1;
    lblMsgSwDly1.Location = point3;
    this.lblMsgSwDly.Name = "lblMsgSwDly";
    Label lblMsgSwDly2 = this.lblMsgSwDly;
    size1 = new Size(186, 16 /*0x10*/);
    Size size3 = size1;
    lblMsgSwDly2.Size = size3;
    this.lblMsgSwDly.TabIndex = 39;
    this.lblMsgSwDly.Text = "Message Switching Delay";
    this.lblPfno.AutoSize = true;
    this.lblPfno.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblPfno1 = this.lblPfno;
    point1 = new Point(131, 200);
    Point point4 = point1;
    lblPfno1.Location = point4;
    this.lblPfno.Name = "lblPfno";
    Label lblPfno2 = this.lblPfno;
    size1 = new Size(89, 16 /*0x10*/);
    Size size4 = size1;
    lblPfno2.Size = size4;
    this.lblPfno.TabIndex = 38;
    this.lblPfno.Text = "Platform No";
    this.txtCgdbDir.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.txtCgdbDir.FormattingEnabled = true;
    this.txtCgdbDir.Items.AddRange(new object[3]
    {
      (object) "UP",
      (object) "DOWN",
      (object) "None"
    });
    ComboBox txtCgdbDir1 = this.txtCgdbDir;
    point1 = new Point(265, 248);
    Point point5 = point1;
    txtCgdbDir1.Location = point5;
    this.txtCgdbDir.Name = "txtCgdbDir";
    ComboBox txtCgdbDir2 = this.txtCgdbDir;
    size1 = new Size(60, 24);
    Size size5 = size1;
    txtCgdbDir2.Size = size5;
    this.txtCgdbDir.TabIndex = 5;
    this.lblDir.AutoSize = true;
    this.lblDir.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblDir1 = this.lblDir;
    point1 = new Point(150, 251);
    Point point6 = point1;
    lblDir1.Location = point6;
    this.lblDir.Name = "lblDir";
    Label lblDir2 = this.lblDir;
    size1 = new Size(70, 16 /*0x10*/);
    Size size6 = size1;
    lblDir2.Size = size6;
    this.lblDir.TabIndex = 37;
    this.lblDir.Text = "Direction";
    this.btnExit.BackColor = Color.SeaShell;
    this.btnExit.DialogResult = DialogResult.Cancel;
    this.btnExit.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnExit1 = this.btnExit;
    point1 = new Point(215, 369);
    Point point7 = point1;
    btnExit1.Location = point7;
    this.btnExit.Name = "btnExit";
    Button btnExit2 = this.btnExit;
    size1 = new Size(50, 25);
    Size size7 = size1;
    btnExit2.Size = size7;
    this.btnExit.TabIndex = 8;
    this.btnExit.Text = "Exit";
    this.btnExit.UseVisualStyleBackColor = false;
    this.btnOk.BackColor = Color.SeaShell;
    this.btnOk.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnOk1 = this.btnOk;
    point1 = new Point(139, 369);
    Point point8 = point1;
    btnOk1.Location = point8;
    this.btnOk.Name = "btnOk";
    Button btnOk2 = this.btnOk;
    size1 = new Size(50, 25);
    Size size8 = size1;
    btnOk2.Size = size8;
    this.btnOk.TabIndex = 7;
    this.btnOk.Text = "Ok";
    this.btnOk.UseVisualStyleBackColor = false;
    this.txtCgdbAddress.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtCgdbAddress1 = this.txtCgdbAddress;
    point1 = new Point(265, 87);
    Point point9 = point1;
    txtCgdbAddress1.Location = point9;
    this.txtCgdbAddress.MaxLength = 3;
    this.txtCgdbAddress.Name = "txtCgdbAddress";
    TextBox txtCgdbAddress2 = this.txtCgdbAddress;
    size1 = new Size(45, 22);
    Size size9 = size1;
    txtCgdbAddress2.Size = size9;
    this.txtCgdbAddress.TabIndex = 2;
    this.lblAddress.AutoSize = true;
    this.lblAddress.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblAddress1 = this.lblAddress;
    point1 = new Point(154, 90);
    Point point10 = point1;
    lblAddress1.Location = point10;
    this.lblAddress.Name = "lblAddress";
    Label lblAddress2 = this.lblAddress;
    size1 = new Size(66, 16 /*0x10*/);
    Size size10 = size1;
    lblAddress2.Size = size10;
    this.lblAddress.TabIndex = 36;
    this.lblAddress.Text = "Address";
    this.lblName.AutoSize = true;
    this.lblName.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblName1 = this.lblName;
    point1 = new Point(171, 39);
    Point point11 = point1;
    lblName1.Location = point11;
    this.lblName.Name = "lblName";
    Label lblName2 = this.lblName;
    size1 = new Size(49, 16 /*0x10*/);
    Size size11 = size1;
    lblName2.Size = size11;
    this.lblName.TabIndex = 35;
    this.lblName.Text = "Name";
    this.txtMultiCastAddress.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox multiCastAddress1 = this.txtMultiCastAddress;
    point1 = new Point(265, 147);
    Point point12 = point1;
    multiCastAddress1.Location = point12;
    this.txtMultiCastAddress.MaxLength = 3;
    this.txtMultiCastAddress.Name = "txtMultiCastAddress";
    TextBox multiCastAddress2 = this.txtMultiCastAddress;
    size1 = new Size(45, 22);
    Size size12 = size1;
    multiCastAddress2.Size = size12;
    this.txtMultiCastAddress.TabIndex = 3;
    this.lblMultiCastAddress.AutoSize = true;
    this.lblMultiCastAddress.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label multiCastAddress3 = this.lblMultiCastAddress;
    point1 = new Point(83, 150);
    Point point13 = point1;
    multiCastAddress3.Location = point13;
    this.lblMultiCastAddress.Name = "lblMultiCastAddress";
    Label multiCastAddress4 = this.lblMultiCastAddress;
    size1 = new Size(137, 16 /*0x10*/);
    Size size13 = size1;
    multiCastAddress4.Size = size13;
    this.lblMultiCastAddress.TabIndex = 74;
    this.lblMultiCastAddress.Text = "Multi Cast Address";
    this.txtCgdbName.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtCgdbName1 = this.txtCgdbName;
    point1 = new Point(265, 36);
    Point point14 = point1;
    txtCgdbName1.Location = point14;
    this.txtCgdbName.MaxLength = 15;
    this.txtCgdbName.Name = "txtCgdbName";
    TextBox txtCgdbName2 = this.txtCgdbName;
    size1 = new Size(100, 22);
    Size size14 = size1;
    txtCgdbName2.Size = size14;
    this.txtCgdbName.TabIndex = 1;
    this.cmbCgdbPfno.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.cmbCgdbPfno.FormattingEnabled = true;
    ComboBox cmbCgdbPfno1 = this.cmbCgdbPfno;
    point1 = new Point(265, 197);
    Point point15 = point1;
    cmbCgdbPfno1.Location = point15;
    this.cmbCgdbPfno.Name = "cmbCgdbPfno";
    ComboBox cmbCgdbPfno2 = this.cmbCgdbPfno;
    size1 = new Size(60, 24);
    Size size15 = size1;
    cmbCgdbPfno2.Size = size15;
    this.cmbCgdbPfno.TabIndex = 4;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.BackColor = Color.PowderBlue;
    size1 = new Size(401, 406);
    this.ClientSize = size1;
    this.Controls.Add((Control) this.cmbCgdbPfno);
    this.Controls.Add((Control) this.txtMultiCastAddress);
    this.Controls.Add((Control) this.lblMultiCastAddress);
    this.Controls.Add((Control) this.txtCgdbMsgSwDly);
    this.Controls.Add((Control) this.lblMsgSwDly);
    this.Controls.Add((Control) this.lblPfno);
    this.Controls.Add((Control) this.txtCgdbDir);
    this.Controls.Add((Control) this.lblDir);
    this.Controls.Add((Control) this.btnExit);
    this.Controls.Add((Control) this.btnOk);
    this.Controls.Add((Control) this.txtCgdbAddress);
    this.Controls.Add((Control) this.txtCgdbName);
    this.Controls.Add((Control) this.lblAddress);
    this.Controls.Add((Control) this.lblName);
    this.FormBorderStyle = FormBorderStyle.FixedToolWindow;
    this.Name = "frmNetworkCGDB";
    this.Text = "CGDB";
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  internal virtual TextBox txtCgdbMsgSwDly
  {
    [DebuggerNonUserCode] get { return this._txtCgdbMsgSwDly; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtCgdbMsgSwDly = value;
    }
  }

  internal virtual Label lblMsgSwDly
  {
    [DebuggerNonUserCode] get { return this._lblMsgSwDly; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblMsgSwDly = value;
    }
  }

  internal virtual Label lblPfno
  {
    [DebuggerNonUserCode] get { return this._lblPfno; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._lblPfno = value; }
  }

  internal virtual ComboBox txtCgdbDir
  {
    [DebuggerNonUserCode] get { return this._txtCgdbDir; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtCgdbDir = value;
    }
  }

  internal virtual Label lblDir
  {
    [DebuggerNonUserCode] get { return this._lblDir; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._lblDir = value; }
  }

  internal virtual Button btnExit
  {
    [DebuggerNonUserCode] get { return this._btnExit; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnExit_Click);
      if (this._btnExit != null)
        this._btnExit.Click -= eventHandler;
      this._btnExit = value;
      if (this._btnExit == null)
        return;
      this._btnExit.Click += eventHandler;
    }
  }

  internal virtual Button btnOk
  {
    [DebuggerNonUserCode] get { return this._btnOk; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnOk_Click);
      if (this._btnOk != null)
        this._btnOk.Click -= eventHandler;
      this._btnOk = value;
      if (this._btnOk == null)
        return;
      this._btnOk.Click += eventHandler;
    }
  }

  internal virtual TextBox txtCgdbAddress
  {
    [DebuggerNonUserCode] get { return this._txtCgdbAddress; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtCgdbAddress = value;
    }
  }

  internal virtual Label lblAddress
  {
    [DebuggerNonUserCode] get { return this._lblAddress; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblAddress = value;
    }
  }

  internal virtual Label lblName
  {
    [DebuggerNonUserCode] get { return this._lblName; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._lblName = value; }
  }

  internal virtual TextBox txtMultiCastAddress
  {
    [DebuggerNonUserCode] get { return this._txtMultiCastAddress; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtMultiCastAddress = value;
    }
  }

  internal virtual Label lblMultiCastAddress
  {
    [DebuggerNonUserCode] get { return this._lblMultiCastAddress; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblMultiCastAddress = value;
    }
  }

  internal virtual TextBox txtCgdbName
  {
    [DebuggerNonUserCode] get { return this._txtCgdbName; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtCgdbName = value;
    }
  }

  internal virtual ComboBox cmbCgdbPfno
  {
    [DebuggerNonUserCode] get { return this._cmbCgdbPfno; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.cmbCgdbPfno_SelectedIndexChanged);
      if (this._cmbCgdbPfno != null)
        this._cmbCgdbPfno.SelectedIndexChanged -= eventHandler;
      this._cmbCgdbPfno = value;
      if (this._cmbCgdbPfno == null)
        return;
      this._cmbCgdbPfno.SelectedIndexChanged += eventHandler;
    }
  }

  private void btnExit_Click(object sender, EventArgs e)
{
  this.Close();
}

  private void btnOk_Click(object sender, EventArgs e)
  {
    try
    {
      if (Operators.CompareString(this.txtCgdbName.Text, "", false) == 0)
      {
        int num1 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Please Enter the Name", "Msg Box", 0, 0, 0);
      }
      else if (Operators.CompareString(this.txtCgdbAddress.Text, "", false) == 0)
      {
        int num2 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Please Enter the Address", "Msg Box", 0, 0, 0);
      }
      else if (Operators.CompareString(this.txtMultiCastAddress.Text, "", false) == 0)
      {
        int num3 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Please Enter the Multicast Address", "Msg Box", 0, 0, 0);
      }
      else if (Operators.CompareString(this.txtCgdbDir.Text, "", false) == 0)
      {
        int num4 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Please Enter the Direction", "Msg Box", 0, 0, 0);
      }
      else if (Operators.CompareString(this.cmbCgdbPfno.Text, "", false) == 0)
      {
        int num5 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Please Select the Platform", "Msg Box", 0, 0, 0);
      }
      else if (Operators.CompareString(this.txtCgdbMsgSwDly.Text, "", false) == 0)
      {
        int num6 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Please Enter the message switching delay", "Msg Box", 0, 0, 0);
      }
      else
      {
        checked { --frmNetworkPDCH.pdch_port_num; }
        checked { --frmNetworkPDCH.pdch_system_num; }
        frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].pdch_port[(int) frmNetworkPDCH.pdch_port_num].dis_board[(int) frmNetworkPDCH.pdch_system_num].dis_board_type = "CGDB";
        frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].pdch_port[(int) frmNetworkPDCH.pdch_port_num].dis_board[(int) frmNetworkPDCH.pdch_system_num].dis_board_name = this.txtCgdbName.Text;
        frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].pdch_port[(int) frmNetworkPDCH.pdch_port_num].dis_board[(int) frmNetworkPDCH.pdch_system_num].dis_board_addr = Conversions.ToByte(this.txtCgdbAddress.Text);
        frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].pdch_port[(int) frmNetworkPDCH.pdch_port_num].dis_board[(int) frmNetworkPDCH.pdch_system_num].multicast_addr = Conversions.ToByte(this.txtMultiCastAddress.Text);
        frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].pdch_port[(int) frmNetworkPDCH.pdch_port_num].dis_board[(int) frmNetworkPDCH.pdch_system_num].platform_no = Operators.CompareString(this.cmbCgdbPfno.Text, "", false) != 0 ? this.cmbCgdbPfno.Text : string.Empty;
        frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].pdch_port[(int) frmNetworkPDCH.pdch_port_num].dis_board[(int) frmNetworkPDCH.pdch_system_num].cgdb_direction = Operators.CompareString(this.txtCgdbDir.Text, "", false) != 0 ? this.txtCgdbDir.Text : "None";
        frmMainFormIPIS.mdch_db.mdch_port[(int) frmNetworkMDCH.mdch_port_num].pdch[(int) frmNetworkMDCH.mdch_system_num].pdch_port[(int) frmNetworkPDCH.pdch_port_num].dis_board[(int) frmNetworkPDCH.pdch_system_num].switching_time = Operators.CompareString(this.txtCgdbMsgSwDly.Text, "", false) != 0 ? Conversions.ToByte(this.txtCgdbMsgSwDly.Text) : (byte) 0;
        this.Close();
        MyProject.Forms.frmNetworkPDCH.BringToFront();
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void cmbCgdbPfno_SelectedIndexChanged(object sender, EventArgs e)
  {
  }

  private void frmNetworkCGDB_Load(object sender, EventArgs e)
  {
    int index = 0;
    this.cmbCgdbPfno.Items.Clear();
    while (index < frmMainFormIPIS.pfno_cnt)
    {
      this.cmbCgdbPfno.Items.Add((object) frmMainFormIPIS.platform_nos[index]);
      checked { ++index; }
    }
  }
}

}