using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using IPIS.Models;

namespace IPIS.Utils
{
    public static class AnnouncementLanguageHelper
    {
        /// <summary>
        /// Gets available languages for announcement configuration
        /// </summary>
        public static async Task<List<Language>> GetAvailableLanguagesForAnnouncementAsync()
        {
            return await LanguageManager.GetLanguagesForAnnouncementAsync();
        }

        /// <summary>
        /// Gets language options for ComboBox or similar controls
        /// </summary>
        public static async Task<List<LanguageOption>> GetLanguageOptionsAsync()
        {
            var languages = await GetAvailableLanguagesForAnnouncementAsync();
            return languages.Select(l => new LanguageOption
            {
                Id = l.Id,
                Code = l.Code,
                DisplayName = string.IsNullOrEmpty(l.NativeName) ? l.Name : $"{l.Name} ({l.NativeName})",
                IsDefault = l.IsDefault
            }).ToList();
        }

        /// <summary>
        /// Gets the default language for announcements
        /// </summary>
        public static async Task<Language> GetDefaultAnnouncementLanguageAsync()
        {
            return await LanguageManager.GetDefaultLanguageAsync();
        }

        /// <summary>
        /// Validates if a language can be used for announcements
        /// </summary>
        public static async Task<bool> CanUseLanguageForAnnouncementAsync(int languageId)
        {
            return await LanguageManager.IsLanguageActiveAsync(languageId) && 
                   await LanguageManager.ValidateWaveFolderAsync(languageId) &&
                   await LanguageManager.HasValidWaveFilesAsync(languageId);
        }

        /// <summary>
        /// Gets available wave files for a specific language
        /// </summary>
        public static async Task<List<string>> GetWaveFilesForLanguageAsync(int languageId)
        {
            return await LanguageManager.GetAvailableWaveFilesAsync(languageId);
        }

        /// <summary>
        /// Gets the full path to a wave file for a specific language
        /// </summary>
        public static async Task<string> GetWaveFilePathAsync(int languageId, string fileName)
        {
            return await LanguageManager.GetFullWaveFilePathAsync(languageId, fileName);
        }

        /// <summary>
        /// Gets language information for display purposes
        /// </summary>
        public static async Task<string> GetLanguageDisplayInfoAsync(int languageId)
        {
            var language = await LanguageManager.GetLanguageByIdAsync(languageId);
            if (language == null)
                return "Unknown Language";

            var status = language.IsActive ? "Active" : "Inactive";
            var defaultStatus = language.IsDefault ? " (Default)" : "";
            var waveFileCount = await LanguageManager.GetAvailableWaveFilesAsync(languageId);
            
            return $"{language.Name} ({language.Code}) - {status}{defaultStatus} - {waveFileCount.Count} wave files";
        }

        /// <summary>
        /// Gets a summary of all available languages for announcements
        /// </summary>
        public static async Task<LanguageSummary> GetLanguageSummaryAsync()
        {
            var languages = await GetAvailableLanguagesForAnnouncementAsync();
            var defaultLanguage = await GetDefaultAnnouncementLanguageAsync();

            return new LanguageSummary
            {
                TotalLanguages = languages.Count,
                ActiveLanguages = languages.Count(l => l.IsActive),
                DefaultLanguage = defaultLanguage?.Name ?? "None",
                LanguagesWithWaveFiles = languages.Count(l => LanguageManager.HasValidWaveFilesAsync(l.Id).Result)
            };
        }

        /// <summary>
        /// Suggests the best language for announcements based on availability and default settings
        /// </summary>
        public static async Task<Language> SuggestBestLanguageAsync()
        {
            // First try to get the default language
            var defaultLanguage = await GetDefaultAnnouncementLanguageAsync();
            if (defaultLanguage != null && await CanUseLanguageForAnnouncementAsync(defaultLanguage.Id))
            {
                return defaultLanguage;
            }

            // If default language is not available, get the first available active language
            var availableLanguages = await GetAvailableLanguagesForAnnouncementAsync();
            return availableLanguages.FirstOrDefault();
        }

        /// <summary>
        /// Gets language statistics for reporting
        /// </summary>
        public static async Task<LanguageStatistics> GetLanguageStatisticsAsync()
        {
            var allLanguages = await LanguageManager.GetAllLanguagesAsync();
            var activeLanguages = allLanguages.Where(l => l.IsActive).ToList();
            var languagesWithWaveFiles = new List<Language>();

            foreach (var language in activeLanguages)
            {
                if (await LanguageManager.HasValidWaveFilesAsync(language.Id))
                {
                    languagesWithWaveFiles.Add(language);
                }
            }

            return new LanguageStatistics
            {
                TotalLanguages = allLanguages.Count,
                ActiveLanguages = activeLanguages.Count,
                LanguagesWithWaveFiles = languagesWithWaveFiles.Count,
                DefaultLanguageId = allLanguages.FirstOrDefault(l => l.IsDefault)?.Id ?? 0,
                DefaultLanguageName = allLanguages.FirstOrDefault(l => l.IsDefault)?.Name ?? "None"
            };
        }
    }

    public class LanguageOption
    {
        public int Id { get; set; }
        public string Code { get; set; }
        public string DisplayName { get; set; }
        public bool IsDefault { get; set; }
    }

    public class LanguageSummary
    {
        public int TotalLanguages { get; set; }
        public int ActiveLanguages { get; set; }
        public string DefaultLanguage { get; set; }
        public int LanguagesWithWaveFiles { get; set; }
    }

    public class LanguageStatistics
    {
        public int TotalLanguages { get; set; }
        public int ActiveLanguages { get; set; }
        public int LanguagesWithWaveFiles { get; set; }
        public int DefaultLanguageId { get; set; }
        public string DefaultLanguageName { get; set; }
    }
} 