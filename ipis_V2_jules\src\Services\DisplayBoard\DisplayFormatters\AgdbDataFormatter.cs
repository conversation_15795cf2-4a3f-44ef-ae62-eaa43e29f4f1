using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using ipis_V2_jules.ApiClients; // For TrainDataErail
// Ensure AgdbLookupTable is accessible. It was created in ipis_V2_jules.DisplayFormatters
using ipis_V2_jules.DisplayFormatters;

// Assuming DisplayBoardConfig keys: "Lines", "CharsPerLine", "BoardId"
// Assuming PlatformInfo keys: "Platform", "ETA", "StatusMessage"

namespace ipis_V2_jules.Services.DisplayBoard.DisplayFormatters
{
    public class AgdbDataFormatter : IDisplayDataFormatter
    {
        private const int AGDB_CHAR_WIDTH_BYTES = 5; // Each AGDB character is 5 bytes wide

        public FormattedDisplayData FormatTrainData(TrainDataErail trainInfo, Dictionary<string, string> platformInfo, Dictionary<string, string> boardConfig)
        {
            Console.WriteLine($"AGDB Formatter: Formatting Train Data for TrainNo: {trainInfo?.TrainNo ?? "N/A"}");

            if (trainInfo == null)
            {
                return FormatMessage("NO TRAIN DATA", boardConfig);
            }

            int lines = boardConfig.TryGetValue("Lines", out string linesStr) && int.TryParse(linesStr, out int l) ? l : 2;
            int charsPerLine = boardConfig.TryGetValue("CharsPerLine", out string charsStr) && int.TryParse(charsStr, out int cpl) ? cpl : 20;
            byte boardId = boardConfig.TryGetValue("BoardId", out string idStr) && byte.TryParse(idStr, out byte id) ? id : (byte)0x01;

            var formattedData = new FormattedDisplayData();

            string line1Text = $"{trainInfo.TrainNo} {trainInfo.TrainName}".ToUpper();

            string platformStr = platformInfo.GetValueOrDefault("Platform", "N/A") ?? "N/A";
            string etaStr = platformInfo.GetValueOrDefault("ETA", trainInfo.StartTime) ?? "N/A";
            string statusMsg = platformInfo.GetValueOrDefault("StatusMessage", "ON TIME") ?? "ON TIME";

            string line2Text = $"PF {platformStr} ETA {etaStr}".ToUpper();
            string line3Text = statusMsg.ToUpper();

            formattedData.Line1 = ConvertTextToAgdbBytes(line1Text, charsPerLine);

            if (lines >= 2)
            {
                formattedData.Line2 = ConvertTextToAgdbBytes(line2Text, charsPerLine);
            }
            else // Ensure Line2 is null if not used
            {
                formattedData.Line2 = null;
            }

            if (lines >= 3)
            {
                formattedData.Line3 = ConvertTextToAgdbBytes(line3Text, charsPerLine);
            }
            else // Ensure Line3 is null if not used
            {
                formattedData.Line3 = null;
            }

            formattedData.AdditionalHeaderBytes = new List<byte> { 0x0B, 0x0C, boardId, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00 };

            Console.WriteLine($"AGDB Formatter: Line 1 Text: \"{line1Text}\", Bytes: {formattedData.Line1?.Length ?? 0}");
            if (formattedData.Line2 != null) Console.WriteLine($"AGDB Formatter: Line 2 Text: \"{line2Text}\", Bytes: {formattedData.Line2.Length}");
            if (formattedData.Line3 != null) Console.WriteLine($"AGDB Formatter: Line 3 Text: \"{line3Text}\", Bytes: {formattedData.Line3.Length}");

            return formattedData;
        }

        public FormattedDisplayData FormatMessage(string message, Dictionary<string, string> boardConfig)
        {
            Console.WriteLine($"AGDB Formatter: Formatting Message: \"{message.Substring(0, Math.Min(message.Length, 20))}...\"");

            int lines = boardConfig.TryGetValue("Lines", out string linesStr) && int.TryParse(linesStr, out int l) ? l : 1;
            int charsPerLine = boardConfig.TryGetValue("CharsPerLine", out string charsStr) && int.TryParse(charsStr, out int cpl) ? cpl : 20;
            byte boardId = boardConfig.TryGetValue("BoardId", out string idStr) && byte.TryParse(idStr, out byte id) ? id : (byte)0x01;

            var formattedData = new FormattedDisplayData();
            message = message.ToUpper();

            List<string> messageLines = new List<string>();
            for (int i = 0; i < message.Length; i += charsPerLine)
            {
                messageLines.Add(message.Substring(i, Math.Min(charsPerLine, message.Length - i)));
            }

            if (messageLines.Count > 0)
            {
                formattedData.Line1 = ConvertTextToAgdbBytes(messageLines[0], charsPerLine);
            }
            if (lines >= 2)
            {
                if (messageLines.Count > 1)
                    formattedData.Line2 = ConvertTextToAgdbBytes(messageLines[1], charsPerLine);
                else
                    formattedData.Line2 = ConvertTextToAgdbBytes("", charsPerLine); // Empty line
            }
            else
            {
                formattedData.Line2 = null;
            }

            if (lines >= 3)
            {
                if (messageLines.Count > 2)
                    formattedData.Line3 = ConvertTextToAgdbBytes(messageLines[2], charsPerLine);
                else
                    formattedData.Line3 = ConvertTextToAgdbBytes("", charsPerLine); // Empty line
            }
            else
            {
                formattedData.Line3 = null;
            }

            formattedData.AdditionalHeaderBytes = new List<byte> { 0x0B, 0x0C, boardId, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00 };

            return formattedData;
        }

        private byte[] ConvertTextToAgdbBytes(string text, int charsPerLine)
        {
            if (text == null) text = "";
            text = text.ToUpper();

            List<byte> lineBytes = new List<byte>();

            for (int i = 0; i < charsPerLine; i++)
            {
                if (i < text.Length)
                {
                    lineBytes.AddRange(GetCharBytes(text[i]));
                }
                else
                {
                    lineBytes.AddRange(ipis_V2_jules.DisplayFormatters.AgdbLookupTable.BYTE_SPACE); // Pad with space
                }
            }
            return lineBytes.ToArray();
        }

        private byte[] GetCharBytes(char c)
        {
            // Using fully qualified name for AgdbLookupTable constants
            switch (c)
            {
                case 'A': return ipis_V2_jules.DisplayFormatters.AgdbLookupTable.A_A;
                case 'B': return ipis_V2_jules.DisplayFormatters.AgdbLookupTable.B_B;
                case 'C': return ipis_V2_jules.DisplayFormatters.AgdbLookupTable.C_C;
                case 'D': return ipis_V2_jules.DisplayFormatters.AgdbLookupTable.D_D;
                case 'E': return ipis_V2_jules.DisplayFormatters.AgdbLookupTable.E_E;
                case 'F': return ipis_V2_jules.DisplayFormatters.AgdbLookupTable.F_F;
                case 'G': return ipis_V2_jules.DisplayFormatters.AgdbLookupTable.G_G;
                case 'H': return ipis_V2_jules.DisplayFormatters.AgdbLookupTable.H_H;
                case 'I': return ipis_V2_jules.DisplayFormatters.AgdbLookupTable.I_I;
                case 'J': return ipis_V2_jules.DisplayFormatters.AgdbLookupTable.J_J;
                case 'K': return ipis_V2_jules.DisplayFormatters.AgdbLookupTable.K_K;
                case 'L': return ipis_V2_jules.DisplayFormatters.AgdbLookupTable.L_L;
                case 'M': return ipis_V2_jules.DisplayFormatters.AgdbLookupTable.M_M;
                case 'N': return ipis_V2_jules.DisplayFormatters.AgdbLookupTable.N_N;
                case 'O': return ipis_V2_jules.DisplayFormatters.AgdbLookupTable.O_O;
                case 'P': return ipis_V2_jules.DisplayFormatters.AgdbLookupTable.P_P;
                case 'Q': return ipis_V2_jules.DisplayFormatters.AgdbLookupTable.Q_Q;
                case 'R': return ipis_V2_jules.DisplayFormatters.AgdbLookupTable.R_R;
                case 'S': return ipis_V2_jules.DisplayFormatters.AgdbLookupTable.S_S;
                case 'T': return ipis_V2_jules.DisplayFormatters.AgdbLookupTable.T_T;
                case 'U': return ipis_V2_jules.DisplayFormatters.AgdbLookupTable.U_U;
                case 'V': return ipis_V2_jules.DisplayFormatters.AgdbLookupTable.V_V;
                case 'W': return ipis_V2_jules.DisplayFormatters.AgdbLookupTable.W_W;
                case 'X': return ipis_V2_jules.DisplayFormatters.AgdbLookupTable.X_X;
                case 'Y': return ipis_V2_jules.DisplayFormatters.AgdbLookupTable.Y_Y;
                case 'Z': return ipis_V2_jules.DisplayFormatters.AgdbLookupTable.Z_Z;
                case '0': return ipis_V2_jules.DisplayFormatters.AgdbLookupTable.BYTE_ZERO_0;
                case '1': return ipis_V2_jules.DisplayFormatters.AgdbLookupTable.BYTE_ONE_1;
                case '2': return ipis_V2_jules.DisplayFormatters.AgdbLookupTable.BYTE_TWO_2;
                case '3': return ipis_V2_jules.DisplayFormatters.AgdbLookupTable.BYTE_THREE_3;
                case '4': return ipis_V2_jules.DisplayFormatters.AgdbLookupTable.BYTE_FOUR_4;
                case '5': return ipis_V2_jules.DisplayFormatters.AgdbLookupTable.BYTE_FIVE_5;
                case '6': return ipis_V2_jules.DisplayFormatters.AgdbLookupTable.BYTE_SIX_6;
                case '7': return ipis_V2_jules.DisplayFormatters.AgdbLookupTable.BYTE_SEVEN_7;
                case '8': return ipis_V2_jules.DisplayFormatters.AgdbLookupTable.BYTE_EIGHT_8;
                case '9': return ipis_V2_jules.DisplayFormatters.AgdbLookupTable.BYTE_NINE_9;
                case ' ': return ipis_V2_jules.DisplayFormatters.AgdbLookupTable.BYTE_SPACE;
                case ':':
                    // Assuming BYTE_COLON in AgdbLookupTable is a single byte constant for control,
                    // and not a 5-byte display pattern. If a 5-byte colon pattern is needed for display,
                    // it should be added to AgdbLookupTable as a byte[].
                    // For now, using space as a fallback for displayable colon.
                    return ipis_V2_jules.DisplayFormatters.AgdbLookupTable.BYTE_SPACE;
                default:
                    return ipis_V2_jules.DisplayFormatters.AgdbLookupTable.BYTE_SPACE;
            }
        }
    }
}
