// Decompiled with JetBrains decompiler
// Type: ipis.frmChangeUserDetails
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

namespace ipis
{

[DesignerGenerated]
public class frmChangeUserDetails : Form
{
  private static List<WeakReference> __ENCList = new List<WeakReference>();
  private IContainer components;
  [AccessedThroughProperty("lblUserName")]
  private Label _lblUserName;
  [AccessedThroughProperty("cmbUsers")]
  private ComboBox _cmbUsers;
  [AccessedThroughProperty("btnExit")]
  private Button _btnExit;
  [AccessedThroughProperty("btnChangePwd")]
  private Button _btnChangePwd;
  [AccessedThroughProperty("btnChangeAccountType")]
  private Button _btnChangeAccountType;
  [AccessedThroughProperty("btnDeleteUser")]
  private Button _btnDeleteUser;
  [AccessedThroughProperty("btnChangeName")]
  private Button _btnChangeName;
  [AccessedThroughProperty("lblPwd")]
  private Label _lblPwd;
  [AccessedThroughProperty("txtPassword")]
  private TextBox _txtPassword;
  [AccessedThroughProperty("btnOk")]
  private Button _btnOk;
  [AccessedThroughProperty("event_deleteuser")]
  private frmDeleteUser _event_deleteuser;
  [AccessedThroughProperty("event_changename")]
  private frmChangeName _event_changename;
  [AccessedThroughProperty("event_changepwd")]
  private frmChangeAnotherUserPwd _event_changepwd;
  [AccessedThroughProperty("event_changeaccounttype")]
  private frmChangeAccountType _event_changeaccounttype;
  [AccessedThroughProperty("event_password")]
  private frmPassword _event_password;
  public static string user_name = string.Empty;
  public static string user_pwd = string.Empty;

  [DebuggerNonUserCode]
  public frmChangeUserDetails()
  {
    this.Load += new EventHandler(this.frmChangeUser_Load);
    frmChangeUserDetails.__ENCAddToList((object) this);
    this.InitializeComponent();
  }

  [DebuggerNonUserCode]
  private static void __ENCAddToList(object value)
  {
    lock (frmChangeUserDetails.__ENCList)
    {
      if (frmChangeUserDetails.__ENCList.Count == frmChangeUserDetails.__ENCList.Capacity)
      {
        int index1 = 0;
        int num = checked (frmChangeUserDetails.__ENCList.Count - 1);
        int index2 = 0;
        while (index2 <= num)
        {
          if (frmChangeUserDetails.__ENCList[index2].IsAlive)
          {
            if (index2 != index1)
              frmChangeUserDetails.__ENCList[index1] = frmChangeUserDetails.__ENCList[index2];
            checked { ++index1; }
          }
          checked { ++index2; }
        }
        frmChangeUserDetails.__ENCList.RemoveRange(index1, checked (frmChangeUserDetails.__ENCList.Count - index1));
        frmChangeUserDetails.__ENCList.Capacity = frmChangeUserDetails.__ENCList.Count;
      }
      frmChangeUserDetails.__ENCList.Add(new WeakReference(RuntimeHelpers.GetObjectValue(value)));
    }
  }

  [DebuggerNonUserCode]
  protected override void Dispose(bool disposing)
  {
    try
    {
      if (!disposing || this.components == null)
        return;
      this.components.Dispose();
    }
    finally
    {
      base.Dispose(disposing);
    }
  }

  [DebuggerStepThrough]
  private void InitializeComponent()
  {
    this.btnChangePwd = new Button();
    this.lblUserName = new Label();
    this.cmbUsers = new ComboBox();
    this.btnChangeAccountType = new Button();
    this.btnDeleteUser = new Button();
    this.btnChangeName = new Button();
    this.btnExit = new Button();
    this.lblPwd = new Label();
    this.txtPassword = new TextBox();
    this.btnOk = new Button();
    this.SuspendLayout();
    this.btnChangePwd.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnChangePwd1 = this.btnChangePwd;
    Point point1 = new Point(129, 117);
    Point point2 = point1;
    btnChangePwd1.Location = point2;
    this.btnChangePwd.Name = "btnChangePwd";
    Button btnChangePwd2 = this.btnChangePwd;
    Size size1 = new Size(156, 23);
    Size size2 = size1;
    btnChangePwd2.Size = size2;
    this.btnChangePwd.TabIndex = 2;
    this.btnChangePwd.Text = "Change Password";
    this.btnChangePwd.UseVisualStyleBackColor = true;
    this.lblUserName.AutoSize = true;
    this.lblUserName.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblUserName1 = this.lblUserName;
    point1 = new Point(0, 26);
    Point point3 = point1;
    lblUserName1.Location = point3;
    this.lblUserName.Name = "lblUserName";
    Label lblUserName2 = this.lblUserName;
    size1 = new Size(159, 16 /*0x10*/);
    Size size3 = size1;
    lblUserName2.Size = size3;
    this.lblUserName.TabIndex = 1;
    this.lblUserName.Text = "Select the User Name";
    this.cmbUsers.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.cmbUsers.FormattingEnabled = true;
    ComboBox cmbUsers1 = this.cmbUsers;
    point1 = new Point(181, 23);
    Point point4 = point1;
    cmbUsers1.Location = point4;
    this.cmbUsers.Name = "cmbUsers";
    ComboBox cmbUsers2 = this.cmbUsers;
    size1 = new Size(140, 24);
    Size size4 = size1;
    cmbUsers2.Size = size4;
    this.cmbUsers.TabIndex = 1;
    this.btnChangeAccountType.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button changeAccountType1 = this.btnChangeAccountType;
    point1 = new Point(130, 211);
    Point point5 = point1;
    changeAccountType1.Location = point5;
    this.btnChangeAccountType.Name = "btnChangeAccountType";
    Button changeAccountType2 = this.btnChangeAccountType;
    size1 = new Size(155, 23);
    Size size5 = size1;
    changeAccountType2.Size = size5;
    this.btnChangeAccountType.TabIndex = 4;
    this.btnChangeAccountType.Text = "Change Account Type";
    this.btnChangeAccountType.UseVisualStyleBackColor = true;
    this.btnDeleteUser.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnDeleteUser1 = this.btnDeleteUser;
    point1 = new Point(129, 259);
    Point point6 = point1;
    btnDeleteUser1.Location = point6;
    this.btnDeleteUser.Name = "btnDeleteUser";
    Button btnDeleteUser2 = this.btnDeleteUser;
    size1 = new Size(156, 23);
    Size size6 = size1;
    btnDeleteUser2.Size = size6;
    this.btnDeleteUser.TabIndex = 5;
    this.btnDeleteUser.Text = "Delete the User Account";
    this.btnDeleteUser.UseVisualStyleBackColor = true;
    this.btnChangeName.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnChangeName1 = this.btnChangeName;
    point1 = new Point(130, 160 /*0xA0*/);
    Point point7 = point1;
    btnChangeName1.Location = point7;
    this.btnChangeName.Name = "btnChangeName";
    Button btnChangeName2 = this.btnChangeName;
    size1 = new Size(155, 23);
    Size size7 = size1;
    btnChangeName2.Size = size7;
    this.btnChangeName.TabIndex = 3;
    this.btnChangeName.Text = "Change Name";
    this.btnChangeName.UseVisualStyleBackColor = true;
    this.btnExit.DialogResult = DialogResult.Cancel;
    this.btnExit.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnExit1 = this.btnExit;
    point1 = new Point(166, 310);
    Point point8 = point1;
    btnExit1.Location = point8;
    this.btnExit.Name = "btnExit";
    Button btnExit2 = this.btnExit;
    size1 = new Size(75, 23);
    Size size8 = size1;
    btnExit2.Size = size8;
    this.btnExit.TabIndex = 6;
    this.btnExit.Text = "Exit";
    this.btnExit.UseVisualStyleBackColor = true;
    this.lblPwd.AutoSize = true;
    this.lblPwd.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblPwd1 = this.lblPwd;
    point1 = new Point(43, 72);
    Point point9 = point1;
    lblPwd1.Location = point9;
    this.lblPwd.Name = "lblPwd";
    Label lblPwd2 = this.lblPwd;
    size1 = new Size(116, 16 /*0x10*/);
    Size size9 = size1;
    lblPwd2.Size = size9;
    this.lblPwd.TabIndex = 7;
    this.lblPwd.Text = "Enter Password";
    this.lblPwd.Visible = false;
    TextBox txtPassword1 = this.txtPassword;
    point1 = new Point(181, 69);
    Point point10 = point1;
    txtPassword1.Location = point10;
    this.txtPassword.Margin = new Padding(4);
    this.txtPassword.MaxLength = 20;
    this.txtPassword.Name = "txtPassword";
    this.txtPassword.PasswordChar = '*';
    TextBox txtPassword2 = this.txtPassword;
    size1 = new Size(140, 20);
    Size size10 = size1;
    txtPassword2.Size = size10;
    this.txtPassword.TabIndex = 8;
    this.txtPassword.UseSystemPasswordChar = true;
    this.txtPassword.Visible = false;
    Button btnOk1 = this.btnOk;
    point1 = new Point(339, 69);
    Point point11 = point1;
    btnOk1.Location = point11;
    this.btnOk.Name = "btnOk";
    Button btnOk2 = this.btnOk;
    size1 = new Size(31 /*0x1F*/, 23);
    Size size11 = size1;
    btnOk2.Size = size11;
    this.btnOk.TabIndex = 9;
    this.btnOk.Text = "Ok";
    this.btnOk.UseVisualStyleBackColor = true;
    this.btnOk.Visible = false;
    this.AutoScaleDimensions = new SizeF(7f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.BackColor = Color.PowderBlue;
    this.CancelButton = (IButtonControl) this.btnExit;
    size1 = new Size(421, 345);
    this.ClientSize = size1;
    this.Controls.Add((Control) this.btnOk);
    this.Controls.Add((Control) this.txtPassword);
    this.Controls.Add((Control) this.lblPwd);
    this.Controls.Add((Control) this.btnExit);
    this.Controls.Add((Control) this.btnChangeName);
    this.Controls.Add((Control) this.btnDeleteUser);
    this.Controls.Add((Control) this.btnChangeAccountType);
    this.Controls.Add((Control) this.cmbUsers);
    this.Controls.Add((Control) this.lblUserName);
    this.Controls.Add((Control) this.btnChangePwd);
    this.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.FormBorderStyle = FormBorderStyle.FixedToolWindow;
    this.Name = "frmChangeUserDetails";
    this.Text = "Change User";
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  internal virtual Label lblUserName
  {
    [DebuggerNonUserCode] get { return this._lblUserName; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblUserName = value;
    }
  }

  internal virtual ComboBox cmbUsers
  {
    [DebuggerNonUserCode] get { return this._cmbUsers; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler1 = new EventHandler(this.cmbUsers_SelectedIndexChanged);
      EventHandler eventHandler2 = new EventHandler(this.cmbUsers_DropDown);
      if (this._cmbUsers != null)
      {
        this._cmbUsers.SelectedIndexChanged -= eventHandler1;
        this._cmbUsers.DropDown -= eventHandler2;
      }
      this._cmbUsers = value;
      if (this._cmbUsers == null)
        return;
      this._cmbUsers.SelectedIndexChanged += eventHandler1;
      this._cmbUsers.DropDown += eventHandler2;
    }
  }

  internal virtual Button btnExit
  {
    [DebuggerNonUserCode] get { return this._btnExit; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.Button1_Click);
      if (this._btnExit != null)
        this._btnExit.Click -= eventHandler;
      this._btnExit = value;
      if (this._btnExit == null)
        return;
      this._btnExit.Click += eventHandler;
    }
  }

  public virtual Button btnChangePwd
  {
    [DebuggerNonUserCode] get { return this._btnChangePwd; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnChangePwd_Click);
      if (this._btnChangePwd != null)
        this._btnChangePwd.Click -= eventHandler;
      this._btnChangePwd = value;
      if (this._btnChangePwd == null)
        return;
      this._btnChangePwd.Click += eventHandler;
    }
  }

  public virtual Button btnChangeAccountType
  {
    [DebuggerNonUserCode] get { return this._btnChangeAccountType; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnChangeAccountType_Click);
      if (this._btnChangeAccountType != null)
        this._btnChangeAccountType.Click -= eventHandler;
      this._btnChangeAccountType = value;
      if (this._btnChangeAccountType == null)
        return;
      this._btnChangeAccountType.Click += eventHandler;
    }
  }

  public virtual Button btnDeleteUser
  {
    [DebuggerNonUserCode] get { return this._btnDeleteUser; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.Button3_Click);
      if (this._btnDeleteUser != null)
        this._btnDeleteUser.Click -= eventHandler;
      this._btnDeleteUser = value;
      if (this._btnDeleteUser == null)
        return;
      this._btnDeleteUser.Click += eventHandler;
    }
  }

  public virtual Button btnChangeName
  {
    [DebuggerNonUserCode] get { return this._btnChangeName; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.Button4_Click);
      if (this._btnChangeName != null)
        this._btnChangeName.Click -= eventHandler;
      this._btnChangeName = value;
      if (this._btnChangeName == null)
        return;
      this._btnChangeName.Click += eventHandler;
    }
  }

  internal virtual Label lblPwd
  {
    [DebuggerNonUserCode] get { return this._lblPwd; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._lblPwd = value; }
  }

  internal virtual TextBox txtPassword
  {
    [DebuggerNonUserCode] get { return this._txtPassword; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtPassword = value;
    }
  }

  internal virtual Button btnOk
  {
    [DebuggerNonUserCode] get { return this._btnOk; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnOk_Click);
      if (this._btnOk != null)
        this._btnOk.Click -= eventHandler;
      this._btnOk = value;
      if (this._btnOk == null)
        return;
      this._btnOk.Click += eventHandler;
    }
  }

  protected virtual frmDeleteUser event_deleteuser
  {
    [DebuggerNonUserCode] get { return this._event_deleteuser; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._event_deleteuser = value;
    }
  }

  protected virtual frmChangeName event_changename
  {
    [DebuggerNonUserCode] get { return this._event_changename; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._event_changename = value;
    }
  }

  protected virtual frmChangeAnotherUserPwd event_changepwd
  {
    [DebuggerNonUserCode] get { return this._event_changepwd; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._event_changepwd = value;
    }
  }

  protected virtual frmChangeAccountType event_changeaccounttype
  {
    [DebuggerNonUserCode] get { return this._event_changeaccounttype; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._event_changeaccounttype = value;
    }
  }

  protected virtual frmPassword event_password
  {
    [DebuggerNonUserCode] get { return this._event_password; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._event_password = value;
    }
  }

  private void frmChangeUser_Load(object sender, EventArgs e)
  {
    int index = 0;
    network_db_read.user_info();
    while (index < (int) frmMainFormIPIS.user_cnt.cnt)
    {
      if (Operators.CompareString(Strings.Trim(frmMainFormIPIS.user_details[index].user_name), Strings.Trim(frmMainFormIPIS.user_login_details.user_name), false) == 0)
      {
        checked { ++index; }
      }
      else
      {
        this.cmbUsers.Items.Add((object) frmMainFormIPIS.user_details[index].user_name);
        checked { ++index; }
      }
    }
  }

  private void Button3_Click(object sender, EventArgs e)
  {
    if (Interaction.MsgBox((object) "Do you Want to Delete User ( Y/N) ", MsgBoxStyle.YesNo) != MsgBoxResult.Yes)
      return;
    this.delete_user();
  }

  private void delete_user()
  {
    bool found = false;
    network_db_read.delete_pwd(Strings.Trim(this.cmbUsers.Text), ref found);
    if (found)
    {
      int num1 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Deleted Successfully", "Msg Box", 0, 0, 0);
      int index = 0;
      while (index < (int) frmMainFormIPIS.user_cnt.cnt)
      {
        if (Operators.CompareString(Strings.Trim(frmMainFormIPIS.user_details[index].user_name), Strings.Trim(this.cmbUsers.Text), false) == 0)
        {
          while (index < (int) frmMainFormIPIS.user_cnt.cnt)
          {
            frmMainFormIPIS.user_details[index].user_name = frmMainFormIPIS.user_details[checked (index + 1)].user_name;
            frmMainFormIPIS.user_details[index].user_id = frmMainFormIPIS.user_details[checked (index + 1)].user_id;
            frmMainFormIPIS.user_details[index].pwd = frmMainFormIPIS.user_details[checked (index + 1)].pwd;
            frmMainFormIPIS.user_details[index].pwd_length = frmMainFormIPIS.user_details[checked (index + 1)].pwd_length;
            checked { ++index; }
          }
          int num2 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Users Deleted ", "Msg Box", 0, 0, 0);
          this.btnChangeName.Enabled = false;
          this.btnChangePwd.Enabled = false;
          this.btnChangeAccountType.Enabled = false;
          this.btnDeleteUser.Enabled = false;
          this.cmbUsers.Items.Remove((object) Strings.Trim(this.cmbUsers.Text));
          break;
        }
        checked { ++index; }
      }
    }
    else
    {
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Users already Deleted ", "Msg Box", 0, 0, 0);
    }
  }

  private void Button4_Click(object sender, EventArgs e)
  {
    if (!Information.IsNothing((object) this.event_changename))
    {
      if (!this.event_changename.IsDisposed)
      {
        this.event_changename.WindowState = FormWindowState.Normal;
        this.event_changename.BringToFront();
      }
      else
      {
        this.event_changename = new frmChangeName();
        this.event_changename.Show();
      }
    }
    else
    {
      this.event_changename = new frmChangeName();
      this.event_changename.Show();
    }
  }

  private void btnChangePwd_Click(object sender, EventArgs e)
  {
    if (!Information.IsNothing((object) this.event_changepwd))
    {
      if (!this.event_changepwd.IsDisposed)
      {
        this.event_changepwd.WindowState = FormWindowState.Normal;
        this.event_changepwd.BringToFront();
      }
      else
      {
        this.event_changepwd = new frmChangeAnotherUserPwd();
        this.event_changepwd.Show();
      }
    }
    else
    {
      this.event_changepwd = new frmChangeAnotherUserPwd();
      this.event_changepwd.Show();
    }
  }

  private void btnChangeAccountType_Click(object sender, EventArgs e)
  {
    if (!Information.IsNothing((object) this.event_changeaccounttype))
    {
      if (!this.event_changeaccounttype.IsDisposed)
      {
        this.event_changeaccounttype.WindowState = FormWindowState.Normal;
        this.event_changeaccounttype.BringToFront();
      }
      else
      {
        this.event_changeaccounttype = new frmChangeAccountType();
        this.event_changeaccounttype.Show();
      }
    }
    else
    {
      this.event_changeaccounttype = new frmChangeAccountType();
      this.event_changeaccounttype.Show();
    }
  }

  private void cmbUsers_DropDown(object sender, EventArgs e)
  {
    int index = 0;
    network_db_read.user_info();
    this.cmbUsers.Items.Clear();
    while (index < (int) frmMainFormIPIS.user_cnt.cnt)
    {
      if (Operators.CompareString(Strings.Trim(frmMainFormIPIS.user_details[index].user_name), Strings.Trim(frmMainFormIPIS.user_login_details.user_name), false) != 0)
        this.cmbUsers.Items.Add((object) frmMainFormIPIS.user_details[index].user_name);
      checked { ++index; }
    }
  }

  private void cmbUsers_SelectedIndexChanged(object sender, EventArgs e)
  {
    int index = 0;
    network_db_read.user_info();
    while (index < (int) frmMainFormIPIS.user_cnt.cnt)
    {
      if (Operators.CompareString(Strings.Trim(frmMainFormIPIS.user_details[index].user_name), Strings.Trim(this.cmbUsers.Text), false) == 0)
      {
        frmChangeUserDetails.user_name = Strings.Trim(frmMainFormIPIS.user_details[index].user_name);
        if (Operators.CompareString(Strings.Trim(frmMainFormIPIS.user_details[index].group), "Admin", false) == 0)
        {
          this.btnChangePwd.Enabled = false;
          this.btnChangeName.Enabled = false;
          this.btnChangeAccountType.Enabled = false;
          this.btnDeleteUser.Enabled = false;
          this.lblPwd.Visible = true;
          this.txtPassword.Visible = true;
          this.btnOk.Visible = true;
          break;
        }
        this.btnChangePwd.Enabled = true;
        this.btnChangeName.Enabled = true;
        this.btnChangeAccountType.Enabled = true;
        this.btnDeleteUser.Enabled = true;
        this.lblPwd.Visible = false;
        this.txtPassword.Visible = false;
        this.btnOk.Visible = false;
        break;
      }
      checked { ++index; }
    }
  }

  private void Button1_Click(object sender, EventArgs e)
{
  this.Close();
}

  private void btnOk_Click(object sender, EventArgs e)
  {
    network_db_read.user_info();
    int index = 0;
    string empty1 = string.Empty;
    string empty2 = string.Empty;
    while (index < (int) frmMainFormIPIS.user_cnt.cnt)
    {
      if (Operators.CompareString(Strings.Trim(frmChangeUserDetails.user_name), frmMainFormIPIS.user_details[index].user_name, false) == 0)
      {
        network_db_read.dec_pwd(frmMainFormIPIS.user_details[index].pwd, ref empty2, Conversions.ToString(frmMainFormIPIS.user_details[index].pwd_length));
        if (Operators.CompareString(this.txtPassword.Text, empty2, false) == 0)
        {
          this.btnChangeAccountType.Enabled = true;
          this.btnChangeName.Enabled = true;
          this.btnChangePwd.Enabled = true;
          this.btnDeleteUser.Enabled = true;
          this.lblPwd.Visible = false;
          this.btnOk.Visible = false;
          this.txtPassword.Visible = false;
          this.txtPassword.Text = string.Empty;
          return;
        }
        int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Incorrect Password", "Msg Box", 0, 0, 0);
        this.txtPassword.Text = string.Empty;
        return;
      }
      checked { ++index; }
    }
    int num1 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Incorrect Password", "Msg Box", 0, 0, 0);
    this.txtPassword.Text = string.Empty;
  }
}

}