# PowerShell script to fix missing closing braces in C# files

# Get all .cs files in the current directory and subdirectories
$csFiles = Get-ChildItem -Path "." -Filter "*.cs" -Recurse

Write-Host "Found $($csFiles.Count) C# files to process for missing braces..."

foreach ($file in $csFiles) {
    Write-Host "Processing: $($file.Name)"
    
    # Read the file content as lines
    $lines = Get-Content $file.FullName
    
    # Skip if file is empty
    if ($lines -eq $null -or $lines.Count -eq 0) {
        continue
    }
    
    $modified = $false
    $lastLine = $lines[$lines.Count - 1]
    
    # Check if file ends with namespace declaration but no closing brace
    if ($lastLine -match '^\s*$' -and $lines.Count -gt 1) {
        # Check if second to last line is also empty or doesn't end with }
        $secondLastLine = $lines[$lines.Count - 2]
        if ($secondLastLine -notmatch '^\s*\}\s*$') {
            # Check if file contains namespace declaration
            $hasNamespace = $false
            foreach ($line in $lines) {
                if ($line -match 'namespace\s+ipis\s*\{') {
                    $hasNamespace = $true
                    break
                }
            }
            
            if ($hasNamespace) {
                # Add closing brace for namespace
                $lines += "}"
                $modified = $true
                Write-Host "  - Added missing namespace closing brace"
            }
        }
    }
    elseif ($lastLine -notmatch '^\s*\}\s*$') {
        # Check if file contains namespace declaration
        $hasNamespace = $false
        foreach ($line in $lines) {
            if ($line -match 'namespace\s+ipis\s*\{') {
                $hasNamespace = $true
                break
            }
        }
        
        if ($hasNamespace) {
            # Add closing brace for namespace
            $lines += "}"
            $modified = $true
            Write-Host "  - Added missing namespace closing brace"
        }
    }
    
    # Save the file if modified
    if ($modified) {
        Set-Content -Path $file.FullName -Value $lines
        Write-Host "  - File updated"
    }
}

Write-Host "Missing braces processing complete!"
