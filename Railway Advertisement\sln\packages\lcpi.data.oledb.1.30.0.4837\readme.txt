README - LCPI ADO.NET Data Provider For OLE DB.

=================================================================
Description:

* This package contains ADO.NET data provider, which fully implements
  the System.Data.OleDb features and adds many own, unique features.

* Primary targets of this ADO.NET provider: Firebird/InterBase DBMS and
  LCPI.IBProvider.5 [Native OLE DB Provider For FB/IB].

Functionality:
 - Pool of connections (standard and custom)
 - Nested transactions
 - Multiple transactions within one connection
 - TransactionScope support
 - Automatic formation of parameter descriptions
 - IN, OUT and IN-OUT parameters
 - Named and unnamed parameters in queries
 - Execution of scripts with parameters
 - MARS
 - Support of OLEDB schemas and ADO.NET metadata collections
 - Support of all types of the IB/FB data
 - Support of stream reading and writing of blobs
 - Support of databases with the first and the third dialects

Technologies:
 - Integration with ADODB and OLE DB.
 - Support of .NET FW 3.5/4.0/4.5/4.5.1/4.6/4.6.2/4.7.2/4.8
 - Support of .NET Standard 2.0/2.1
 - Support of .NET 5.0/6.0/7.0/8.0 (Windows)
 - Unified support of 32-bit and 64-bit programs
 - Support of multithreaded applications
 - Reliable management of resources
 - Aggressive management of the .NET component lifetime
 - Localization of error messages
 - Advanced installer (MSI and EXE)
 - Installation to GAC
 - Registration in machine.config
 - DDEX for VS2008-VS2022
 - Total code testing

Home Page:
 https://www.ibprovider.com

ADO.NET Provider Installer:
 https://www.ibprovider.com/tools/download.php?filename=lcpi.oledb.net.exe&lang=1

IBProvider Installers [TRIAL]:
 https://www.ibprovider.com/tools/download.php?filename=ibprovider_trial_64bit.msi&lang=1
 https://www.ibprovider.com/tools/download.php?filename=ibprovider_trial_32bit.msi&lang=1

Manuals:
 https://www.ibprovider.com/eng/documentation/lcpi_oledb_net__using_adonet_with_firebird.html
 https://www.ibprovider.com/eng/documentation/lcpi_oledb_net__installer_msi.html

Samples:
 https://www.ibprovider.com/eng/documentation/examples.html#examples__lcpi_ado_net

News Archive:
 https://www.ibprovider.com/eng/news/n_230125.html
 https://www.ibprovider.com/eng/news/n_211220-lcpi_efcore_lcpioledb.html
 https://www.ibprovider.com/eng/news/n_200421.html
 https://www.ibprovider.com/eng/news/n_200326.html
 https://www.ibprovider.com/eng/news/n_190923.html
 https://www.ibprovider.com/eng/news/n_181030.html
 https://www.ibprovider.com/eng/news/n_180608.html
 https://www.ibprovider.com/eng/news/n_171114.html
 https://www.ibprovider.com/eng/news/n_171016.html
 https://www.ibprovider.com/eng/news/n_170622.html
 https://www.ibprovider.com/eng/news/n_170514__lcpi_data_oledb__v1_0_8.html
 https://www.ibprovider.com/eng/news/n_170511.html
 https://www.ibprovider.com/eng/news/n_150408_adonet_provider_release.html
 https://www.ibprovider.com/eng/news/n_141014.html
 https://www.ibprovider.com/eng/news/n_131120.html
 https://www.ibprovider.com/eng/news/n_131017.html
 https://www.ibprovider.com/eng/news/n_130614.html
 https://www.ibprovider.com/eng/news/n_120608.html

Contact E-Mail:
 <EMAIL>

=================================================================
ChangeLog:

v1.30.0
* A support of IBProvider initialization property "ctype_system" is added.

All the changes was done in OleDbConnectionStringBuilder.

Data provider was not changed.

v1.29.0
* The support of .NET 8.0 (Windows) was added
* The support of IBProvider property "blob_send_mode" was added

v1.28.0
* The support of .NET 7.0 (Windows) was added

v1.27.1
* The mistake with getting of DBTIMESTAMPOFFSET-array was fixed
  - provider used System.DateTime type instead System.DateTimeOffset

v1.27.0
The support of the following IBProvider properties was added:
 - decfloat16_rules
 - decfloat34_rules
 - int128_rules
 - dbtimestamp_with_tz_rules
 - dbtime_with_tz_rules
 - timezones_source
 - icuin_library
 - icuin_library_64
 - sql_cache__scope
 - sql_cache__instance_id
 - sql_cache__size
 - sql_cache__time
 - sql_cache__max_len

All the changes were made only in OleDbConnectionStringBuilder.

Data provider was not changed.

v1.26.0
This release replaces direct using Windows COM API with lcpi.lib.com.IComApiProvider interface.

* OleDbServices
  - [add] static: CreateWithComApiProvider(comApiProvider)
  - [add] static: CreateWithComApiProvider(comApiProvider,classID)
  - [add] static: CreateWithComApiProvider(comApiProvider,progID)

* OleDbConnectionStringBuilder
  - [add] static: CreateWithComApiProvider(comApiProvider)
  - [add] static: CreateWithComApiProvider(comApiProvider, connectionString)

* OleDbConnection
  - [add] static: CreateWithComApiProvider(comApiProvider)
  - [add] static: CreateWithComApiProvider(comApiProvider, connectionString)

It allows to use IBProvider through "LCPI Easy COM", which provides the alternative implementation for COM API subset.

v1.25.4
* OleDbConnection.Services
  - Prevents to change while connection is open
  - Publishes an object which is used by an open connection

* OleDbConnection.CloneSession
  - Copies FireInfoMessageEventOnUserErrors to the new object

* OleDbServices [global instance]
  - Ignores the call of Dispose method

v1.25.3
This is a servicing update:
 - The external packages of VS2019/VS2022 DDEX providers were updated
 - The code of EXE-installer was refreshed

Data providers weren't touched.

v1.25.2
* Switching to usage lcpi.lib v1.14.0
* Correction of TFM for net5, net6 in nuget-package:
   - net5-windows7.0
   - net6-windows7.0

Data providers not changed.

v1.25.1
* Assemblies for .NET6
  - Rebuilding with usage VS2022 CE [Release]

v1.25
* Preparing for Visual Studio 2022
  - .NET6 assemblies were compiled by VS2022 Preview 7
  - New DDEX providers for VS2022 [included into main installer]

v1.24.0
* OleDbDataReader
 - GetGuid can work with BINARY(16) columns
 - GetBytes can work with GUID-columns

Assembiles for .NET6 were compiled by VS2019 CE 16.11.3 Preview 1.0.

v1.23.0
* OleDbDataReader
 - [added] char[] GetChars(int ordinal)
 - T GetFieldValue<T>(int ordinal) got its own implementation and is available on all platforms (since from FW3.5)

Assembiles for .NET6 were compiled by VS2019 CE 16.11.3 Preview 1.0.

v1.22.1
* Cleanup
  - Removing "lcpi.data.oledb.core.sdk.ibprovider.IBP_DBPROPSET" from public surface

Assembiles for .NET6 were compiled by VS2019 CE 16.11.2 Preview 1.0.

v1.22.0
* New assemblies for .NET6
  - [added] OleDbDataReader.GetDateOnly
  - [added] OleDbDataReader.GetTimeOnly
  - [added] OleDbDataReader.GetFieldValue<T>
     - for support DateOnly, TimeOnly
  - OleDbCommand [OleDbParameter] supports usage DateOnly, TimeOnly as values of INPUT-parameters.

* Common improvement of code

Assembiles for .NET6 were compiled by VS2019 CE 16.11.0 Preview 2.0.

v1.21.1
* OleDbDataReader
  - [Fix] Processing of column with empty name.
    Previous version of ADO.NET provider may return null instead empty string.
    Current version will always return empty string.

    Sample query: "select COL1 IS NULL from TABLE1"

v1.21.0
* Added support for:
 - .NET Standard 2.1
 - .NET 5.0 (Windows)

* Minimal version of IBProvider: 5.23.0.36396

v1.20.1
* Fixed issues in debug assemblies:
  - There were problems with the enum-based command parameter values.

* Minor code improvement for generating error messages

v1.20.0
* New connection string property "NetProv:ParameterMarkerFormat"
  - This property designed to solve the problem with incorrect SQL generation in "ADO.NET Destination" of SSIS
* Connection property "NetProv: NestedTransRules" was renamed to "NetProv:NestedTransRules"
  - Old name has been disabled for use
* New connection string parameter parser
  - More stringent text processing

v1.19.0
Optimization of reconnecting to the database. Now the ADO.NET provider (OleDbConnection) caches and reuses the following data:
 - OLE DB providers class identifiers (CLSIDs)
 - Processed connection strings
 - Description of OLE DB provider initialization properties

Note that this release requires lcpi.lib v2.8.

v1.18.0
* [new] OleDbCommand.Properties
   - returns object of OleDbCommandProperties class.

v1.17.0.4032
* Minumal version of IBProvider: v5.12.
* New DDEX providers for VS2017, VS2019 and data provider for .NET Standard 2.0

DDEX provider packed into VSIX and distributed in primary installation kit.

---
Changes in NUGET package:
* Built-in package icon

v1.16.0.3991
Changes in OleDbDataReader [SchemaOnly].
* Allows usage of:
   - Read method. Always returns false.
   - HasRows property. Always returns false.
   - RecordsAffected property. Always returns -1.
 
Previous implementations of these methods throw an exception.

Equivalent behavior is implemented in System.Data.OleDb.

v1.15.0.3988
* Added a support for new IBProvider v5.11 properties:
  - remote:wire_compression
  - remote:wire_compression_type
  - remote:wire_compression_library
  - remote:wire_compression_library_64

All changes affected only OleDbConnectionStringBuilder.

Other components of provider not changed.

v1.14.1.3980
* Assemblies for .NET FW4.8 are now compiled in VS2017
  - was: VS2019

v1.14.0.3959
* Added support for FW4.8

v1.13.1.3934
* Fixed a problem in VSIX installers.

Data providers not changed.

v1.13.0.3920
* Revision and improving implementation of OleDbConnection.AttachToNativeSession method:
 - Detecting of OLE DB provider CLSID (DataSourceInformation.LCPI_OleDbProviderClassID).
 - Restoring of connection string (OleDbConnection.ConnectionString).
 - After release (close) an attached connection OleDbConnection.ConnectionString will be reset.

v1.12.1.3883
* New columns in the DataSourceInformation metadata schema:
- ISC:DatabaseOdsVersion
- ISC:DatabasePageSize

Available when connecting to Firebird, InterBase and Yaffil databases.

v1.12.0.3876
* New DDEX providers for VS2019 (available in the main installer).

Data providers not changed.

v1.11.0.3673
* New columns in the DataSourceInformation metadata schema:
- ISC:DatabaseDialect
- ISC:ConnectionDialect

Available when connecting to Firebird, InterBase and Yaffil databases.

v1.10.0.3652
This update improves compliance with the OLE DB specification.

* Changes in OleDbConnection:
 - Creating an OLE DB session on demand.
 - DropDatabase requires the release of all connection's child objects. Otherwise, this method throws an exception.
 - DropDatabase releases an OLE DB session object before calling IDBDataSourceAdmin::DestroyDataSource.

Note that the IDBDataSourceAdmin (OLE DB) interface is not available when connecting to the database through a pool of connections.

v1.9.1.3601
* Improved a support of IBProvider properties:
  - first_week_day
  - auto_commit
  - nested_trans
  - inner_trans

All changes affected only OleDbConnectionStringBuilder and targeted to improving DDEX UI.

Other components of provider not changed.

v1.9.0.3592
* No changes in Data providers. But they were recompiled.

v1.8.0.3545
* Added support for FW4.7.2
* Added support for new IBProvider property "schema_ldr_cfg__descriptions"
* Internal improvements

v1.7.3.3440
* Fixed a little problem, which detected by PVS-Studio
  - mistake in internal code: ThrowBugCheck::unexpected_type_of_providerProgID

v1.7.2.3437
* Improved OleDbDataReader::GetDecimal
  - Allowed reading of columns with datatypes: SByte, Int16-Int64, Byte, UInt16-UInt64, Single, Double
* Usage of error code DB_E_UNSUPPORTEDCONVERSION instead DB_E_CANTCONVERTVALUE

v1.7.1.3418
* Correction of error messages
* Code normalization

v1.7.0.3395
* Synchronization with IBProvider behaviour
  - Usage DB_E_DATAOVERFLOW instead DISP_E_OVERFLOW
* Minimal IBProvider version: 3.50

v1.6.0.3379
* Internal changes
   - redesign of the instrumental library
* OleDbConnectionStringBuilder
   - support of new IBProvider property: "guid_rules"

Data provider not changed.

v1.5.0.3103
* [new] OleDbDataReaderSchemaColumnNames
  - names of table columns from OleDbDataReader.GetSchemaTable
* Internal changes

v1.4.0.3036
* Publication of instrumental library (lcpi.lib) in separated nuget package

  Please inform us about any problems. Thanks.

* OleDbConnection::GetSchema
  - added support of schema "PrimaryKeys"
  - added support of schema "ForeignKeys"

  In previous releases these schemas were available only through GetOleDbSchema method.

v1.3.0.2965
* New names of DDEX providers modules (DLLs) for VS2008-VS2015
  - Unification with names of DDEX provider for VS2017

Data provider not changed.

v1.2.0.2951
* Support .NET Standard 2.0
* Minimal IBProvider version: 3.47.0.27224 [please update]

v1.1.2.2915
* OleDbCommandBuilder
  - resolved a problem with quoting of object identifiers for FB/IB databases with 1 dialect
* OleDbConnectionStringBuilder
  - added a support for new IBProvider property "schema_restrictions"

v1.1.1.2906
* Added a support for new IBProvider property:
  - wchars_in_utf8_symbol

All changes affected only OleDbConnectionStringBuilder.

Other components of provider not changed.

v1.1.0.2901
* ADO.NET provider
 - No changes
* New DDEX provider (v2)
 - For VS2017 and FW 4.6.2 only

v1.0.10.2887
* ADO.NET provider
 - No changes
* DDEX provider
 - Resolved a problem with drag&drop operation
 - Improved "Connection Configuration" dialogue

v1.0.9.2872 [deleted]

v1.0.8.2633
* [added] OleDbConnectionStringBuilder::IsNetProviderProperty
  - required in updated DDEX provider for correct processing own properties of .Net provider

v1.0.7.2597
* [added] OleDbServices
  - works with COM objects with IDataInitialize interface
  - this an auxiliary component for OleDbConnection objects
  - might be used for replacing "MSDASC.MSDAINITIALIZE.1"
    on the "LCPI.OleDbServices.DataInitManager.Global.1"
* [added] OleDbConnection::Services
  - R/W property to associate an OleDbConnection object with an OleDbServices object
* Fixed errors
* Improved debug code

v1.0.6.2568
* Added a support for new IBProvider properties:
  - remote:auth
  - remote:wire_crypt [corrected]

* Improved a support of IBProvider properties:
  - remote:protocol

All changes affected only OleDbConnectionStringBuilder.

Other components of provider not changed.

v1.0.5.2564 [deleted]

v1.0.4.2558
* Added a support for new IBProvider properties:
  - check_cn_status
  - remote:protocol
* Improved a support of IBProvider properties:
  - array_rw_mode
  - remote:protocol_type

All changes affected only OleDbConnectionStringBuilder.

Other components of provider not changed.

v1.0.3.2526
* The support of ADO.NET infrastructure improved:
 - [added] protected OleDbConnection::DbProviderFactory

v1.0.2.2502
* Added a nominal support for new IBProvider properties:
  - dbdate_rules

v1.0.2.2500
* Added a nominal support for new IBProvider properties:
  - array_rw_mode
  - remote:protocol_arch
  - remote:protocol_type
* Removed a support of deprecated IBProvider properties:
  - force_param_describe

v1.0.2.2498
* Added a support for "dbclient_type" [IBProvider]
* FW4.6: recompiled by VS2015 Upd1

v1.0.1.2491
* No changes in primary assemblies
* Small updates in tests

v1.0.1.2490
* Added a support for .NET FW 4.6

v1.0.0.2476
* Release of product

v1.0.0.2475
* OleDbConnection.AttachToNativeSession
  - uses lcpi.lib.com.t_com_ptr_wrapper

v1.0.0.2470
* Instrumental Library (lcpi.lib)
  - [new] lcpi.lib.adodb.AdoDbConstructor
* ADO.NET provider
  - Little internal reorganization

v1.0.0.2465
* This is a pre final build.
* [added] Bridge into the world of ADODB/OLEDB/COM:
  - OleDbConnection::GetNativeSession
  - OleDbConnection::AttachToNativeSession

v1.0.0.2457
* [added] OleDbConnection.ModifyDatabase
  - support for OLEDB interface IDBDataSourceAdmin::ModifyDataSource

v1.0.0.2436
* Revision and improvement of error messages

v1.0.0.2412
* OleDbConnection.Open
* OleDbConnection.CreateDatabase
  - [added] support of notification through OleDbConnection.InfoMessage

v1.0.0.2408
* [new] OleDbDataReader.GetArray

v1.0.0.2353
* [new] OleDbConnectionStringBuilder.IBProvider
* [new] OleDbConnectionStringBuilder.UserID
* [new] OleDbConnectionStringBuilder.Password
* [new] OleDbConnectionStringBuilder.IntegratedSecurity
* other changes in OleDbConnectionStringBuilder

v1.0.0.2308
* [new] OleDbConnection.CreateDatabase
* [new] OleDbConnection.DropDatabase
* IBProvider minimal version: 3.23.0.17468
