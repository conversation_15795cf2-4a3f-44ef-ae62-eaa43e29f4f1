// Decompiled with JetBrains decompiler
// Type: ipis.RS232
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Windows.Forms;

namespace ipis
{

public class RS232 : MarshalByRefObject
{
  public static DateTime stTime;
  public static Timer tim = new Timer();
  private const int OPEN_EXISTING = 3;
  private const int GENERIC_READ = -2147483648 /*0x80000000*/;
  private const int GENERIC_WRITE = 1073741824 /*0x40000000*/;
  private const string PARITYSTRING = "NOE";
  private static int hPort;
  private RS232.DCB dcbPort;
  private static bool m_opened;
  private string m_port;
  private int m_speed;
  private RS232.enumParity m_parity;
  private RS232.enumStopBits m_stop;
  private int m_databits;

  [DebuggerNonUserCode]
  public RS232()
  {
  }

  [DllImport("kernel32.dll")]
  private static extern int CreateFile(
    [MarshalAs(UnmanagedType.LPStr)] string lpFileName,
    int dwDesiredAccess,
    int dwShareMode,
    int lpSecurityAttributes,
    int dwCreationDisposition,
    int dwFlagsAndAttributes,
    int hTemplateFile);

  [DllImport("kernel32.dll")]
  private static extern int GetCommState(int hCommDev, ref RS232.DCB lpDCB);

  [DllImport("kernel32.dll")]
  private static extern int ReadFile(
    int hFile,
    byte[] Buffer,
    int nNumberOfBytesToRead,
    ref int lpNumberOfBytesRead,
    ref RS232.OVERLAPPED lpOverlapped);

  [DllImport("kernel32.dll")]
  private static extern int SetCommState(int hCommDev, ref RS232.DCB lpDCB);

  [DllImport("kernel32.dll")]
  private static extern bool SetCommMask(int hCommDev, int mash);

  [DllImport("kernel32.dll")]
  private static extern int WriteFile(
    int hFile,
    byte[] Buffer,
    int nNumberOfBytesToWrite,
    ref int lpNumberOfBytesWritten,
    ref RS232.OVERLAPPED lpOverlapped);

  [DllImport("kernel32.dll")]
  private static extern bool WaitCommEvent(
    int hCommDev,
    ref int dwCommModemStatus,
    ref RS232.OVERLAPPED lpOverlapped);

  [DllImport("kernel32.dll")]
  private static extern int CloseHandle(int hObject);

  [DllImport("kernel32.dll")]
  private static extern int GetLastError();

  [DllImport("kernel32.dll")]
  private static extern int BuildCommDCB(string lpDef, ref RS232.DCB lpDCB);

  [DllImport("kernel32.dll")]
  private static extern int PurgeComm(IntPtr hFile, int dwflags);

  [DllImport("kernel32.dll", SetLastError = true)]
  private static extern bool GetCommTimeouts(IntPtr hFile, ref RS232.COMMTIMEOUTS lpCommTimeouts);

  [DllImport("kernel32.dll", SetLastError = true)]
  private static extern bool SetCommTimeouts(IntPtr hFile, ref RS232.COMMTIMEOUTS lpCommTimeouts);

  public int Serial_Open(
    string portname,
    int Spd,
    RS232.enumParity Pty,
    int Dtb,
    RS232.enumStopBits Stp)
  {
    int num = 0;
    try
    {
      if (RS232.m_opened)
        this.Serial_Close();
      RS232.hPort = RS232.CreateFile(portname, -1073741824 /*0xC0000000*/, 0, 0, 3, 0, 0);
      if (RS232.hPort < 1)
      {
        num = 0;
      }
      else
      {
        RS232.BuildCommDCB("baud=" + Spd.ToString() + " parity=" + "NOE".Substring((int) Pty, 1) + " data=" + Dtb.ToString() + " stop=" + ((ValueType) (byte) Stp).ToString(), ref this.dcbPort);
        RS232.COMMTIMEOUTS lpCommTimeouts = new RS232.COMMTIMEOUTS();
        RS232.GetCommTimeouts((IntPtr) RS232.hPort, ref lpCommTimeouts);
        lpCommTimeouts.ReadIntervalTimeout = 0;
        lpCommTimeouts.ReadTotalTimeoutMultiplier = 0;
        lpCommTimeouts.WriteTotalTimeoutConstant = 0;
        lpCommTimeouts.WriteTotalTimeoutMultiplier = 0;
        lpCommTimeouts.ReadTotalTimeoutConstant = 8000;
        RS232.SetCommTimeouts((IntPtr) RS232.hPort, ref lpCommTimeouts);
        if (RS232.SetCommState(RS232.hPort, ref this.dcbPort) == 0)
        {
          num = 0;
        }
        else
        {
          RS232.m_opened = true;
          num = 1;
        }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
    return num;
  }

  public void Open()
  {
    this.Serial_Open(this.m_port, this.m_speed, this.m_parity, this.m_databits, this.m_stop);
  }

  public void Serial_Close()
  {
    RS232.CloseHandle(RS232.hPort);
    RS232.hPort = -1;
    RS232.m_opened = false;
  }

  public static void Write(byte data)
  {
    try
    {
      byte[] instance = (byte[]) Array.CreateInstance(typeof (byte), 1);
      instance[0] = data;
      int hPort = RS232.hPort;
      byte[] Buffer = instance;
      int num = 0;
      RS232.OVERLAPPED overlapped1 = new RS232.OVERLAPPED();
      RS232.WriteFile(hPort, Buffer, 1, ref num, ref overlapped1);
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
  }

  public static int Serial_Write(ref byte[] str, int length)
  {
    int index = 0;
    int num = 0;
    try
    {
      RS232.PurgeComm((IntPtr) RS232.hPort, 8);
      if (RS232.Opened)
      {
        while (index < length)
        {
          RS232.Write(str[index]);
          checked { ++index; }
        }
        num = 1;
      }
      else
        num = 0;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
    return num;
  }

  public static byte Serial_Read(ref byte[] rxbuf)
  {
    byte num1 = 0;
    byte num2 = 1;
    short index = 0;
    short num3 = 0;
    RS232.OVERLAPPED overlapped = new RS232.OVERLAPPED();
    byte num4 = 0;
    try
    {
      byte[] instance = (byte[]) Array.CreateInstance(typeof (byte), 1);
label_15:
      if (RS232.Opened)
      {
        byte num5 = 0;
        do
        {
          int hPort = RS232.hPort;
          byte[] Buffer = instance;
          int num6 = (int) num5;
          int num7 = RS232.ReadFile(hPort, Buffer, 1, ref num6, ref overlapped);
          num5 = checked ((byte) num6);
          if (checked ((byte) num7) == (byte) 1 & num5 == (byte) 0)
          {
            num4 = (byte) 0;
            goto label_18;
          }
          if (num1 == (byte) 1)
          {
            rxbuf[(int) index] = instance[0];
            checked { ++index; }
            if ((int) index == (int) num3)
            {
              num4 = (byte) 1;
              goto label_18;
            }
          }
          else
          {
            switch ((byte) ((int) num2 - 1))
            {
              case 0:
                num2 = instance[0] != (byte) 170 ? (byte) 1 : (byte) 2;
                break;
              case 1:
                num2 = instance[0] != (byte) 204 ? (byte) 1 : (byte) 3;
                break;
              case 2:
                rxbuf[(int) index] = instance[0];
                checked { ++index; }
                if (index == (short) 2)
                {
                  num3 = checked ((short) unchecked ((int) (short) ((int) (short) rxbuf[0] << 8) + (int) rxbuf[1]));
                  num1 = (byte) 1;
                }
                break;
            }
          }
        }
        while (num5 != (byte) 1);
        goto label_15;
      }
      num4 = (byte) 0;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
label_18:
    return num4;
  }

  public string Port
  {
    get { return this.m_port; }
    set { this.m_port = value; }
  }

  public int Speed
  {
    get { return this.m_speed; }
    set { this.m_speed = value; }
  }

  public RS232.enumParity Parity
  {
    get { return this.m_parity; }
    set { this.m_parity = value; }
  }

  public RS232.enumStopBits StopBits
  {
    get { return this.m_stop; }
    set { this.m_stop = value; }
  }

  public int DataBits
  {
    get { return this.m_databits; }
    set { this.m_databits = value; }
  }

  public static bool Opened { get { return RS232.m_opened; } }

  private struct COMMTIMEOUTS
  {
    public int ReadIntervalTimeout;
    public int ReadTotalTimeoutMultiplier;
    public int ReadTotalTimeoutConstant;
    public int WriteTotalTimeoutMultiplier;
    public int WriteTotalTimeoutConstant;
  }

  public struct DCB
  {
    public int DCBlength;
    public int BaudRate;
    public int fBitFields;
    public int wReserved;
    public int XonLim;
    public int XoffLim;
    public byte ByteSize;
    public byte Parity;
    public byte StopBits;
    public byte XonChar;
    public byte XoffChar;
    public byte ErrorChar;
    public byte EofChar;
    public byte EvtChar;
    public int wReserved1;
  }

  public struct OVERLAPPED
  {
    public int ternal;
    public int ternalHigh;
    public int offset;
    public int OffsetHigh;
    public int hEvent;
  }

  public enum enumStopBits : byte
  {
    One = 1,
    One5 = 2,
    Two = 2,
  }

  public enum enumParity
  {
    None,
    Odd,
    Even,
  }
}

}