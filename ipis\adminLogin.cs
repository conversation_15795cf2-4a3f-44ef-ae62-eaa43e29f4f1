// Decompiled with JetBrains decompiler
// Type: ipis.adminLogin
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

namespace ipis
{

[DesignerGenerated]
public class adminLogin : Form
{
  private static List<WeakReference> __ENCList = new List<WeakReference>();
  private IContainer components;
  [AccessedThroughProperty("PictureBox1")]
  private PictureBox _PictureBox1;
  [AccessedThroughProperty("btnExit")]
  private Button _btnExit;
  [AccessedThroughProperty("btnOk")]
  private Button _btnOk;
  [AccessedThroughProperty("txtPassword")]
  private TextBox _txtPassword;
  [AccessedThroughProperty("txtUserName")]
  private TextBox _txtUserName;
  [AccessedThroughProperty("PasswordLabel")]
  private Label _PasswordLabel;
  [AccessedThroughProperty("UsernameLabel")]
  private Label _UsernameLabel;
  [AccessedThroughProperty("Label2")]
  private Label _Label2;
  [AccessedThroughProperty("admin_Login")]
  private adminLogin _admin_Login;
  [AccessedThroughProperty("admin_Dashboard")]
  private adminDashboard _admin_Dashboard;

  [DebuggerNonUserCode]
  static adminLogin()
  {
  }

  [DebuggerNonUserCode]
  public adminLogin()
  {
    adminLogin.__ENCAddToList((object) this);
    this.InitializeComponent();
  }

  [DebuggerNonUserCode]
  private static void __ENCAddToList(object value)
  {
    lock (adminLogin.__ENCList)
    {
      if (adminLogin.__ENCList.Count == adminLogin.__ENCList.Capacity)
      {
        int index1 = 0;
        int num = checked (adminLogin.__ENCList.Count - 1);
        int index2 = 0;
        while (index2 <= num)
        {
          if (adminLogin.__ENCList[index2].IsAlive)
          {
            if (index2 != index1)
              adminLogin.__ENCList[index1] = adminLogin.__ENCList[index2];
            checked { ++index1; }
          }
          checked { ++index2; }
        }
        adminLogin.__ENCList.RemoveRange(index1, checked (adminLogin.__ENCList.Count - index1));
        adminLogin.__ENCList.Capacity = adminLogin.__ENCList.Count;
      }
      adminLogin.__ENCList.Add(new WeakReference(RuntimeHelpers.GetObjectValue(value)));
    }
  }

  [DebuggerNonUserCode]
  protected override void Dispose(bool disposing)
  {
    try
    {
      if (!disposing || this.components == null)
        return;
      this.components.Dispose();
    }
    finally
    {
      base.Dispose(disposing);
    }
  }

  [DebuggerStepThrough]
  private void InitializeComponent()
  {
    ComponentResourceManager componentResourceManager = new ComponentResourceManager(typeof (adminLogin));
    this.PictureBox1 = new PictureBox();
    this.btnExit = new Button();
    this.btnOk = new Button();
    this.txtPassword = new TextBox();
    this.txtUserName = new TextBox();
    this.PasswordLabel = new Label();
    this.UsernameLabel = new Label();
    this.Label2 = new Label();
    ((ISupportInitialize) this.PictureBox1).BeginInit();
    this.SuspendLayout();
    this.PictureBox1.ErrorImage = (Image) componentResourceManager.GetObject("PictureBox1.ErrorImage");
    this.PictureBox1.Image = (Image) componentResourceManager.GetObject("PictureBox1.Image");
    PictureBox pictureBox1_1 = this.PictureBox1;
    Point point1 = new Point(7, 66);
    Point point2 = point1;
    pictureBox1_1.Location = point2;
    this.PictureBox1.Name = "PictureBox1";
    PictureBox pictureBox1_2 = this.PictureBox1;
    Size size1 = new Size(337, 165);
    Size size2 = size1;
    pictureBox1_2.Size = size2;
    this.PictureBox1.TabIndex = 0;
    this.PictureBox1.TabStop = false;
    this.btnExit.BackColor = Color.SeaShell;
    this.btnExit.DialogResult = DialogResult.Cancel;
    this.btnExit.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnExit1 = this.btnExit;
    point1 = new Point(522, 252);
    Point point3 = point1;
    btnExit1.Location = point3;
    this.btnExit.Name = "btnExit";
    Button btnExit2 = this.btnExit;
    size1 = new Size(67, 23);
    Size size3 = size1;
    btnExit2.Size = size3;
    this.btnExit.TabIndex = 13;
    this.btnExit.Text = "&Exit";
    this.btnExit.UseVisualStyleBackColor = false;
    this.btnOk.BackColor = Color.SeaShell;
    this.btnOk.DialogResult = DialogResult.OK;
    this.btnOk.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnOk1 = this.btnOk;
    point1 = new Point(401, 252);
    Point point4 = point1;
    btnOk1.Location = point4;
    this.btnOk.Name = "btnOk";
    Button btnOk2 = this.btnOk;
    size1 = new Size(65, 23);
    Size size4 = size1;
    btnOk2.Size = size4;
    this.btnOk.TabIndex = 12;
    this.btnOk.Text = "&OK";
    this.btnOk.UseVisualStyleBackColor = false;
    this.txtPassword.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtPassword1 = this.txtPassword;
    point1 = new Point(395, 179);
    Point point5 = point1;
    txtPassword1.Location = point5;
    this.txtPassword.Name = "txtPassword";
    this.txtPassword.PasswordChar = '*';
    TextBox txtPassword2 = this.txtPassword;
    size1 = new Size(220, 22);
    Size size5 = size1;
    txtPassword2.Size = size5;
    this.txtPassword.TabIndex = 11;
    this.txtUserName.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtUserName1 = this.txtUserName;
    point1 = new Point(395, 86);
    Point point6 = point1;
    txtUserName1.Location = point6;
    this.txtUserName.Name = "txtUserName";
    TextBox txtUserName2 = this.txtUserName;
    size1 = new Size(220, 22);
    Size size6 = size1;
    txtUserName2.Size = size6;
    this.txtUserName.TabIndex = 10;
    this.PasswordLabel.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label passwordLabel1 = this.PasswordLabel;
    point1 = new Point(395, 142);
    Point point7 = point1;
    passwordLabel1.Location = point7;
    this.PasswordLabel.Name = "PasswordLabel";
    Label passwordLabel2 = this.PasswordLabel;
    size1 = new Size(84, 23);
    Size size7 = size1;
    passwordLabel2.Size = size7;
    this.PasswordLabel.TabIndex = 15;
    this.PasswordLabel.Text = "Password";
    this.PasswordLabel.TextAlign = ContentAlignment.MiddleLeft;
    this.UsernameLabel.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label usernameLabel1 = this.UsernameLabel;
    point1 = new Point(395, 44);
    Point point8 = point1;
    usernameLabel1.Location = point8;
    this.UsernameLabel.Name = "UsernameLabel";
    Label usernameLabel2 = this.UsernameLabel;
    size1 = new Size(93, 23);
    Size size8 = size1;
    usernameLabel2.Size = size8;
    this.UsernameLabel.TabIndex = 14;
    this.UsernameLabel.Text = "User Name";
    this.UsernameLabel.TextAlign = ContentAlignment.MiddleLeft;
    this.Label2.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label label2_1 = this.Label2;
    point1 = new Point(7, 243);
    Point point9 = point1;
    label2_1.Location = point9;
    this.Label2.Name = "Label2";
    Label label2_2 = this.Label2;
    size1 = new Size(269, 23);
    Size size9 = size1;
    label2_2.Size = size9;
    this.Label2.TabIndex = 17;
    this.Label2.Text = "Powered by Ninja Media Creations";
    this.Label2.TextAlign = ContentAlignment.MiddleLeft;
    this.AcceptButton = (IButtonControl) this.btnOk;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.BackColor = Color.LightBlue;
    this.CancelButton = (IButtonControl) this.btnExit;
    size1 = new Size(670, 318);
    this.ClientSize = size1;
    this.Controls.Add((Control) this.Label2);
    this.Controls.Add((Control) this.btnExit);
    this.Controls.Add((Control) this.btnOk);
    this.Controls.Add((Control) this.txtPassword);
    this.Controls.Add((Control) this.txtUserName);
    this.Controls.Add((Control) this.PasswordLabel);
    this.Controls.Add((Control) this.UsernameLabel);
    this.Controls.Add((Control) this.PictureBox1);
    this.Name = "adminLogin";
    this.Text = "adminLogin";
    ((ISupportInitialize) this.PictureBox1).EndInit();
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  internal virtual PictureBox PictureBox1
  {
    [DebuggerNonUserCode] get { return this._PictureBox1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._PictureBox1 = value;
    }
  }

  internal virtual Button btnExit
  {
    [DebuggerNonUserCode] get { return this._btnExit; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnExit_Click);
      if (this._btnExit != null)
        this._btnExit.Click -= eventHandler;
      this._btnExit = value;
      if (this._btnExit == null)
        return;
      this._btnExit.Click += eventHandler;
    }
  }

  internal virtual Button btnOk
  {
    [DebuggerNonUserCode] get { return this._btnOk; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnOk_Click);
      if (this._btnOk != null)
        this._btnOk.Click -= eventHandler;
      this._btnOk = value;
      if (this._btnOk == null)
        return;
      this._btnOk.Click += eventHandler;
    }
  }

  internal virtual TextBox txtPassword
  {
    [DebuggerNonUserCode] get { return this._txtPassword; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtPassword = value;
    }
  }

  internal virtual TextBox txtUserName
  {
    [DebuggerNonUserCode] get { return this._txtUserName; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtUserName = value;
    }
  }

  internal virtual Label PasswordLabel
  {
    [DebuggerNonUserCode] get { return this._PasswordLabel; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._PasswordLabel = value;
    }
  }

  internal virtual Label UsernameLabel
  {
    [DebuggerNonUserCode] get { return this._UsernameLabel; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._UsernameLabel = value;
    }
  }

  internal virtual Label Label2
  {
    [DebuggerNonUserCode] get { return this._Label2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Label2 = value; }
  }

  protected virtual adminLogin admin_Login
  {
    [DebuggerNonUserCode] get { return this._admin_Login; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._admin_Login = value;
    }
  }

  protected virtual adminDashboard admin_Dashboard
  {
    [DebuggerNonUserCode] get { return this._admin_Dashboard; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._admin_Dashboard = value;
    }
  }

  private void btnOk_Click(object sender, EventArgs e)
  {
    if (!(Operators.CompareString(Strings.Trim(this.txtUserName.Text), "admin", false) == 0 & Operators.CompareString(Strings.Trim(this.txtPassword.Text), "password", false) == 0))
      return;
    this.Close();
    this.admin_Dashboard = new adminDashboard();
    this.admin_Dashboard.Show();
  }

  private void btnExit_Click(object sender, EventArgs e)
{
  this.Close();
}
}
}
