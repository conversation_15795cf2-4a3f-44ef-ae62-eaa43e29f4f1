using System;
using System.Data;
using System.Data.SQLite;
using IPIS.Repositories.Interfaces;
using IPIS.Utils;

namespace IPIS.Repositories
{
    public class SQLiteTrainRepository : ITrainRepository
    {
        private readonly string connectionString;

        public SQLiteTrainRepository()
        {
            connectionString = Database.ConnectionString;
            InitializeDatabase();
        }

        private void InitializeDatabase()
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                using (var command = new SQLiteCommand(connection))
                {
                    // Create Train_Data table if it doesn't exist
                    command.CommandText = @"
                        CREATE TABLE IF NOT EXISTS Train_Data (
                            Train_No TEXT PRIMARY KEY,
                            Train_NameEng TEXT,
                            Train_Type TEXT,
                            Train_AD TEXT,
                            Sch_AT TEXT,
                            Sch_DT TEXT,
                            Sch_PF TEXT,
                            Src_Stn TEXT,
                            Desti_Stn TEXT,
                            Via1 TEXT,
                            Via2 TEXT,
                            Via3 TEXT,
                            Via4 TEXT,
                            All_Days INTEGER,
                            Chk_Sun INTEGER,
                            Chk_Mon INTEGER,
                            Chk_Tue INTEGER,
                            Chk_Wed INTEGER,
                            Chk_Thu INTEGER,
                            Chk_Fri INTEGER,
                            Chk_Sat INTEGER
                        )";
                    command.ExecuteNonQuery();

                    // Create Online_Trains table if it doesn't exist
                    command.CommandText = @"
                        CREATE TABLE IF NOT EXISTS Online_Trains (
                            Sl_No INTEGER,
                            Train_No TEXT PRIMARY KEY,
                            Train_NameEng TEXT,
                            Train_AD TEXT,
                            Train_Status TEXT,
                            Sch_AT TEXT,
                            Sch_DT TEXT,
                            Late TEXT,
                            Exp_AT TEXT,
                            Exp_DT TEXT,
                            Sch_PF TEXT,
                            AN TEXT,
                            Div_City TEXT,
                            Del_Train INTEGER DEFAULT 0
                        )";
                    command.ExecuteNonQuery();

                    // Update existing records that don't have Sl_No
                    command.CommandText = @"
                        UPDATE Online_Trains 
                        SET Sl_No = (
                            SELECT COUNT(*) + 1 
                            FROM Online_Trains ot2 
                            WHERE ot2.Train_No < Online_Trains.Train_No
                        )
                        WHERE Sl_No IS NULL";
                    command.ExecuteNonQuery();
                }
            }
        }

        public DataTable GetAllTrains()
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = "SELECT * FROM Train_Data";
                using (var command = new SQLiteCommand(query, connection))
                {
                    using (var adapter = new SQLiteDataAdapter(command))
                    {
                        var dataTable = new DataTable();
                        adapter.Fill(dataTable);
                        return dataTable;
                    }
                }
            }
        }

        public DataTable GetOnlineTrains()
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = @"
                    SELECT 
                        ot.Sl_No,
                        ot.Train_No,
                        COALESCE(ot.Train_NameEng, td.Train_NameEng) as Train_NameEng,
                        COALESCE(ot.Train_AD, td.Train_AD) as Train_AD,
                        ot.Train_Status,
                        COALESCE(td.Sch_AT, ot.Exp_AT) as Sch_AT,
                        COALESCE(td.Sch_DT, ot.Exp_DT) as Sch_DT,
                        ot.Late,
                        ot.Exp_AT,
                        ot.Exp_DT,
                        ot.Sch_PF,
                        ot.AN,
                        ot.Changed_PF,
                        ot.Diverted_From
                    FROM Online_Trains ot
                    LEFT JOIN Train_Data td ON ot.Train_No = td.Train_No
                    ORDER BY ot.Sl_No";
                using (var command = new SQLiteCommand(query, connection))
                {
                    using (var adapter = new SQLiteDataAdapter(command))
                    {
                        var dataTable = new DataTable();
                        adapter.Fill(dataTable);
                        return dataTable;
                    }
                }
            }
        }

        public void AddTrain(string trainNo, string trainName, string trainType, string trainAD, string schAT, string schDT, string schPF, string srcStn, string destiStn, string[] viaStations, bool[] operatingDays)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = "INSERT INTO Train_Data (Train_No, Train_NameEng, Train_Type, Train_AD, Sch_AT, Sch_DT, Sch_PF, Src_Stn, Desti_Stn, Via1, Via2, Via3, Via4, All_Days, Chk_Mon, Chk_Tue, Chk_Wed, Chk_Thu, Chk_Fri, Chk_Sat, Chk_Sun) VALUES (@TrainNo, @TrainNameEng, @TrainType, @TrainAD, @SchAT, @SchDT, @SchPF, @SrcStn, @DestiStn, @Via1, @Via2, @Via3, @Via4, @AllDays, @ChkMon, @ChkTue, @ChkWed, @ChkThu, @ChkFri, @ChkSat, @ChkSun)";
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@TrainNo", trainNo);
                    command.Parameters.AddWithValue("@TrainNameEng", trainName);
                    command.Parameters.AddWithValue("@TrainType", trainType);
                    command.Parameters.AddWithValue("@TrainAD", trainAD);
                    command.Parameters.AddWithValue("@SchAT", schAT);
                    command.Parameters.AddWithValue("@SchDT", schDT);
                    command.Parameters.AddWithValue("@SchPF", schPF);
                    command.Parameters.AddWithValue("@SrcStn", srcStn);
                    command.Parameters.AddWithValue("@DestiStn", destiStn);
                    command.Parameters.AddWithValue("@Via1", viaStations.Length > 0 ? viaStations[0] : DBNull.Value);
                    command.Parameters.AddWithValue("@Via2", viaStations.Length > 1 ? viaStations[1] : DBNull.Value);
                    command.Parameters.AddWithValue("@Via3", viaStations.Length > 2 ? viaStations[2] : DBNull.Value);
                    command.Parameters.AddWithValue("@Via4", viaStations.Length > 3 ? viaStations[3] : DBNull.Value);
                    command.Parameters.AddWithValue("@AllDays", operatingDays[0]);
                    command.Parameters.AddWithValue("@ChkSun", operatingDays[1]);
                    command.Parameters.AddWithValue("@ChkMon", operatingDays[2]);
                    command.Parameters.AddWithValue("@ChkTue", operatingDays[3]);
                    command.Parameters.AddWithValue("@ChkWed", operatingDays[4]);
                    command.Parameters.AddWithValue("@ChkThu", operatingDays[5]);
                    command.Parameters.AddWithValue("@ChkFri", operatingDays[6]);
                    command.Parameters.AddWithValue("@ChkSat", operatingDays[7]);
                    command.ExecuteNonQuery();
                }
            }
        }

        public void UpdateTrain(string trainNo, string trainName, string trainType, string trainAD, string schAT, string schDT, string schPF, string srcStn, string destiStn, string[] viaStations, bool[] operatingDays)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = "UPDATE Train_Data SET Train_NameEng = @TrainNameEng, Train_Type = @TrainType, Train_AD = @TrainAD, Sch_AT = @SchAT, Sch_DT = @SchDT, Sch_PF = @SchPF, Src_Stn = @SrcStn, Desti_Stn = @DestiStn, Via1 = @Via1, Via2 = @Via2, Via3 = @Via3, Via4 = @Via4, All_Days = @AllDays, Chk_Mon = @ChkMon, Chk_Tue = @ChkTue, Chk_Wed = @ChkWed, Chk_Thu = @ChkThu, Chk_Fri = @ChkFri, Chk_Sat = @ChkSat, Chk_Sun = @ChkSun WHERE Train_No = @TrainNo";
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@TrainNo", trainNo);
                    command.Parameters.AddWithValue("@TrainNameEng", trainName);
                    command.Parameters.AddWithValue("@TrainType", trainType);
                    command.Parameters.AddWithValue("@TrainAD", trainAD);
                    command.Parameters.AddWithValue("@SchAT", schAT);
                    command.Parameters.AddWithValue("@SchDT", schDT);
                    command.Parameters.AddWithValue("@SchPF", schPF);
                    command.Parameters.AddWithValue("@SrcStn", srcStn);
                    command.Parameters.AddWithValue("@DestiStn", destiStn);
                    command.Parameters.AddWithValue("@Via1", viaStations.Length > 0 ? viaStations[0] : DBNull.Value);
                    command.Parameters.AddWithValue("@Via2", viaStations.Length > 1 ? viaStations[1] : DBNull.Value);
                    command.Parameters.AddWithValue("@Via3", viaStations.Length > 2 ? viaStations[2] : DBNull.Value);
                    command.Parameters.AddWithValue("@Via4", viaStations.Length > 3 ? viaStations[3] : DBNull.Value);
                    command.Parameters.AddWithValue("@AllDays", operatingDays[0]);
                    command.Parameters.AddWithValue("@ChkSun", operatingDays[1]);
                    command.Parameters.AddWithValue("@ChkMon", operatingDays[2]);
                    command.Parameters.AddWithValue("@ChkTue", operatingDays[3]);
                    command.Parameters.AddWithValue("@ChkWed", operatingDays[4]);
                    command.Parameters.AddWithValue("@ChkThu", operatingDays[5]);
                    command.Parameters.AddWithValue("@ChkFri", operatingDays[6]);
                    command.Parameters.AddWithValue("@ChkSat", operatingDays[7]);
                    command.ExecuteNonQuery();
                }
            }
        }

        public void DeleteTrain(string trainNo)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = "DELETE FROM Train_Data WHERE Train_No = @TrainNo";
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@TrainNo", trainNo);
                    command.ExecuteNonQuery();
                }
            }
        }

        public void UpdateOnlineTrain(string trainNo, string trainName, string status, string late, string expAT, string expDT, string platform, string announcement)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                
                // First check if the train exists in Online_Trains
                string checkQuery = "SELECT COUNT(*) FROM Online_Trains WHERE Train_No = @TrainNo";
                int trainExists;
                using (var checkCommand = new SQLiteCommand(checkQuery, connection))
                {
                    checkCommand.Parameters.AddWithValue("@TrainNo", trainNo);
                    trainExists = Convert.ToInt32(checkCommand.ExecuteScalar());
                }

                if (trainExists > 0)
                {
                    // Update existing record
                    string updateQuery = @"
                        UPDATE Online_Trains 
                        SET Train_NameEng = @TrainNameEng,
                            Train_Status = @Status,
                            Late = @Late,
                            Exp_AT = @ExpAT,
                            Exp_DT = @ExpDT,
                            Sch_PF = @Platform,
                            AN = @Announcement
                        WHERE Train_No = @TrainNo";

                    using (var command = new SQLiteCommand(updateQuery, connection))
                    {
                        command.Parameters.AddWithValue("@TrainNo", trainNo);
                        command.Parameters.AddWithValue("@TrainNameEng", trainName);
                        command.Parameters.AddWithValue("@Status", status);
                        command.Parameters.AddWithValue("@Late", late);
                        command.Parameters.AddWithValue("@ExpAT", expAT);
                        command.Parameters.AddWithValue("@ExpDT", expDT);
                        command.Parameters.AddWithValue("@Platform", platform);
                        command.Parameters.AddWithValue("@Announcement", announcement);
                        command.ExecuteNonQuery();
                    }
                }
                else
                {
                    // Insert new record
                    string getMaxSlNoQuery = "SELECT COALESCE(MAX(Sl_No), 0) + 1 FROM Online_Trains";
                    int nextSlNo;
                    using (var command = new SQLiteCommand(getMaxSlNoQuery, connection))
                    {
                        nextSlNo = Convert.ToInt32(command.ExecuteScalar());
                    }

                    string insertQuery = @"
                        INSERT INTO Online_Trains (
                            Sl_No, Train_No, Train_NameEng, Train_Status, Late, 
                            Exp_AT, Exp_DT, Sch_PF, AN
                        )
                        VALUES (
                            @SlNo, @TrainNo, @TrainNameEng, @Status, @Late,
                            @ExpAT, @ExpDT, @Platform, @Announcement
                        )";

                    using (var command = new SQLiteCommand(insertQuery, connection))
                    {
                        command.Parameters.AddWithValue("@SlNo", nextSlNo);
                        command.Parameters.AddWithValue("@TrainNo", trainNo);
                        command.Parameters.AddWithValue("@TrainNameEng", trainName);
                        command.Parameters.AddWithValue("@Status", status);
                        command.Parameters.AddWithValue("@Late", late);
                        command.Parameters.AddWithValue("@ExpAT", expAT);
                        command.Parameters.AddWithValue("@ExpDT", expDT);
                        command.Parameters.AddWithValue("@Platform", platform);
                        command.Parameters.AddWithValue("@Announcement", announcement);
                        command.ExecuteNonQuery();
                    }
                }
            }
        }

        public void UpdateOnlineTrain(string trainNo, string trainName, string status, string late, string expAT, string expDT, string platform, string announcement, string changedPF = "", string divertedFrom = "")
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                
                // First check if the train exists in Online_Trains
                string checkQuery = "SELECT COUNT(*) FROM Online_Trains WHERE Train_No = @TrainNo";
                int trainExists;
                using (var checkCommand = new SQLiteCommand(checkQuery, connection))
                {
                    checkCommand.Parameters.AddWithValue("@TrainNo", trainNo);
                    trainExists = Convert.ToInt32(checkCommand.ExecuteScalar());
                }

                if (trainExists > 0)
                {
                    // Update existing record
                    string updateQuery = @"
                        UPDATE Online_Trains 
                        SET Train_NameEng = @TrainNameEng,
                            Train_Status = @Status,
                            Late = @Late,
                            Exp_AT = @ExpAT,
                            Exp_DT = @ExpDT,
                            Sch_PF = @Platform,
                            AN = @Announcement,
                            Changed_PF = @ChangedPF,
                            Diverted_From = @DivertedFrom
                        WHERE Train_No = @TrainNo";

                    using (var command = new SQLiteCommand(updateQuery, connection))
                    {
                        command.Parameters.AddWithValue("@TrainNo", trainNo);
                        command.Parameters.AddWithValue("@TrainNameEng", trainName);
                        command.Parameters.AddWithValue("@Status", status);
                        command.Parameters.AddWithValue("@Late", late);
                        command.Parameters.AddWithValue("@ExpAT", expAT);
                        command.Parameters.AddWithValue("@ExpDT", expDT);
                        command.Parameters.AddWithValue("@Platform", platform);
                        command.Parameters.AddWithValue("@Announcement", announcement);
                        command.Parameters.AddWithValue("@ChangedPF", changedPF ?? "");
                        command.Parameters.AddWithValue("@DivertedFrom", divertedFrom ?? "");
                        command.ExecuteNonQuery();
                    }
                }
                else
                {
                    // Insert new record
                    string getMaxSlNoQuery = "SELECT COALESCE(MAX(Sl_No), 0) + 1 FROM Online_Trains";
                    int nextSlNo;
                    using (var command = new SQLiteCommand(getMaxSlNoQuery, connection))
                    {
                        nextSlNo = Convert.ToInt32(command.ExecuteScalar());
                    }

                    string insertQuery = @"
                        INSERT INTO Online_Trains (
                            Sl_No, Train_No, Train_NameEng, Train_Status, Late, 
                            Exp_AT, Exp_DT, Sch_PF, AN, Changed_PF, Diverted_From
                        )
                        VALUES (
                            @SlNo, @TrainNo, @TrainNameEng, @Status, @Late,
                            @ExpAT, @ExpDT, @Platform, @Announcement, @ChangedPF, @DivertedFrom
                        )";

                    using (var command = new SQLiteCommand(insertQuery, connection))
                    {
                        command.Parameters.AddWithValue("@SlNo", nextSlNo);
                        command.Parameters.AddWithValue("@TrainNo", trainNo);
                        command.Parameters.AddWithValue("@TrainNameEng", trainName);
                        command.Parameters.AddWithValue("@Status", status);
                        command.Parameters.AddWithValue("@Late", late);
                        command.Parameters.AddWithValue("@ExpAT", expAT);
                        command.Parameters.AddWithValue("@ExpDT", expDT);
                        command.Parameters.AddWithValue("@Platform", platform);
                        command.Parameters.AddWithValue("@Announcement", announcement);
                        command.Parameters.AddWithValue("@ChangedPF", changedPF ?? "");
                        command.Parameters.AddWithValue("@DivertedFrom", divertedFrom ?? "");
                        command.ExecuteNonQuery();
                    }
                }
            }
        }

        public void DeleteOnlineTrain(string trainNo)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                using (var command = new SQLiteCommand(connection))
                {
                    command.CommandText = "DELETE FROM Online_Trains WHERE Train_No = @TrainNo";
                    command.Parameters.AddWithValue("@TrainNo", trainNo);
                    command.ExecuteNonQuery();
                }
            }
        }

        public void AddOnlineTrain(string trainNo, string trainName, string trainAD, string status, string late, string eat, string edt, string pf, string an)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                
                // Get the next Sl_No
                string getMaxSlNoQuery = "SELECT COALESCE(MAX(Sl_No), 0) + 1 FROM Online_Trains";
                int nextSlNo;
                using (var command = new SQLiteCommand(getMaxSlNoQuery, connection))
                {
                    nextSlNo = Convert.ToInt32(command.ExecuteScalar());
                }
                
                using (var command = new SQLiteCommand(connection))
                {
                    command.CommandText = @"
                        INSERT INTO Online_Trains 
                        (Sl_No, Train_No, Train_NameEng, Train_AD, Train_Status, Late, Exp_AT, Exp_DT, Sch_PF, AN, Changed_PF, Diverted_From)
                        VALUES (@SlNo, @TrainNo, @TrainName, @TrainAD, @Status, @Late, @EAT, @EDT, @PF, @AN, @ChangedPF, @DivertedFrom)";
                    
                    command.Parameters.AddWithValue("@SlNo", nextSlNo);
                    command.Parameters.AddWithValue("@TrainNo", trainNo);
                    command.Parameters.AddWithValue("@TrainName", trainName);
                    command.Parameters.AddWithValue("@TrainAD", trainAD);
                    command.Parameters.AddWithValue("@Status", status);
                    command.Parameters.AddWithValue("@Late", late);
                    command.Parameters.AddWithValue("@EAT", eat);
                    command.Parameters.AddWithValue("@EDT", edt);
                    command.Parameters.AddWithValue("@PF", pf);
                    command.Parameters.AddWithValue("@AN", an);
                    command.Parameters.AddWithValue("@ChangedPF", "");
                    command.Parameters.AddWithValue("@DivertedFrom", "");
                    
                    command.ExecuteNonQuery();
                }
            }
        }

        public void AddOnlineTrain(string trainNo, string trainName, string trainAD, string status, string late, string eat, string edt, string pf, string an, string changedPF = "", string divertedFrom = "")
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                
                // Get the next Sl_No
                string getMaxSlNoQuery = "SELECT COALESCE(MAX(Sl_No), 0) + 1 FROM Online_Trains";
                int nextSlNo;
                using (var command = new SQLiteCommand(getMaxSlNoQuery, connection))
                {
                    nextSlNo = Convert.ToInt32(command.ExecuteScalar());
                }
                
                using (var command = new SQLiteCommand(connection))
                {
                    command.CommandText = @"
                        INSERT INTO Online_Trains 
                        (Sl_No, Train_No, Train_NameEng, Train_AD, Train_Status, Late, Exp_AT, Exp_DT, Sch_PF, AN, Changed_PF, Diverted_From)
                        VALUES (@SlNo, @TrainNo, @TrainName, @TrainAD, @Status, @Late, @EAT, @EDT, @PF, @AN, @ChangedPF, @DivertedFrom)";
                    
                    command.Parameters.AddWithValue("@SlNo", nextSlNo);
                    command.Parameters.AddWithValue("@TrainNo", trainNo);
                    command.Parameters.AddWithValue("@TrainName", trainName);
                    command.Parameters.AddWithValue("@TrainAD", trainAD);
                    command.Parameters.AddWithValue("@Status", status);
                    command.Parameters.AddWithValue("@Late", late);
                    command.Parameters.AddWithValue("@EAT", eat);
                    command.Parameters.AddWithValue("@EDT", edt);
                    command.Parameters.AddWithValue("@PF", pf);
                    command.Parameters.AddWithValue("@AN", an);
                    command.Parameters.AddWithValue("@ChangedPF", changedPF ?? "");
                    command.Parameters.AddWithValue("@DivertedFrom", divertedFrom ?? "");
                    
                    command.ExecuteNonQuery();
                }
            }
        }

        public DataTable GetTrainsForCurrentDay()
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                
                // Get current day of week (0 = Sunday, 1 = Monday, ..., 6 = Saturday)
                DayOfWeek currentDay = DateTime.Now.DayOfWeek;
                string dayColumn = GetDayColumn(currentDay);
                
                string query = $@"
                    SELECT * FROM Train_Data 
                    WHERE All_Days = 1 OR {dayColumn} = 1
                    ORDER BY Sch_AT";
                
                using (var command = new SQLiteCommand(query, connection))
                {
                    using (var adapter = new SQLiteDataAdapter(command))
                    {
                        var dataTable = new DataTable();
                        adapter.Fill(dataTable);
                        return dataTable;
                    }
                }
            }
        }

        public DataTable GetTrainsForCurrentTimeWindow(int timeWindowMinutes = 120)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                
                // Calculate time window
                DateTime now = DateTime.Now;
                TimeSpan currentTime = now.TimeOfDay;
                TimeSpan windowStart = currentTime.Add(TimeSpan.FromMinutes(-timeWindowMinutes / 2));
                TimeSpan windowEnd = currentTime.Add(TimeSpan.FromMinutes(timeWindowMinutes / 2));
                
                // Handle time window crossing midnight
                if (windowStart < TimeSpan.Zero)
                {
                    windowStart = TimeSpan.Zero;
                }
                if (windowEnd > TimeSpan.FromHours(24))
                {
                    windowEnd = TimeSpan.FromHours(24);
                }
                
                string query = @"
                    SELECT * FROM Train_Data 
                    WHERE (Sch_AT >= @WindowStart AND Sch_AT <= @WindowEnd)
                       OR (Sch_DT >= @WindowStart AND Sch_DT <= @WindowEnd)
                    ORDER BY Sch_AT";
                
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@WindowStart", windowStart.ToString(@"hh\:mm"));
                    command.Parameters.AddWithValue("@WindowEnd", windowEnd.ToString(@"hh\:mm"));
                    
                    using (var adapter = new SQLiteDataAdapter(command))
                    {
                        var dataTable = new DataTable();
                        adapter.Fill(dataTable);
                        return dataTable;
                    }
                }
            }
        }

        public DataTable GetTrainsForCurrentDayAndTime(int timeWindowMinutes = 120)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                
                // Get current day of week
                DayOfWeek currentDay = DateTime.Now.DayOfWeek;
                string dayColumn = GetDayColumn(currentDay);
                
                // Calculate time window: from 30 minutes ago to end of day
                DateTime now = DateTime.Now;
                TimeSpan currentTime = now.TimeOfDay;
                TimeSpan windowStart = currentTime.Add(TimeSpan.FromMinutes(-30)); // 30 minutes ago
                TimeSpan windowEnd = TimeSpan.FromHours(23).Add(TimeSpan.FromMinutes(59)); // End of day (23:59)
                
                // Handle time window crossing midnight
                if (windowStart < TimeSpan.Zero)
                {
                    windowStart = TimeSpan.Zero;
                }
                
                string query = $@"
                    SELECT * FROM Train_Data 
                    WHERE (All_Days = 1 OR {dayColumn} = 1)
                      AND ((Sch_AT >= @WindowStart AND Sch_AT <= @WindowEnd)
                       OR (Sch_DT >= @WindowStart AND Sch_DT <= @WindowEnd))
                    ORDER BY Sch_AT";
                
                // Debug logging
                Console.WriteLine($"[DEBUG] Current day: {currentDay}, Day column: {dayColumn}");
                Console.WriteLine($"[DEBUG] Current time: {currentTime:hh\\:mm}");
                Console.WriteLine($"[DEBUG] Time window: {windowStart:hh\\:mm} - {windowEnd:hh\\:mm}");
                Console.WriteLine($"[DEBUG] Query: {query}");
                
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@WindowStart", windowStart.ToString(@"hh\:mm"));
                    command.Parameters.AddWithValue("@WindowEnd", windowEnd.ToString(@"hh\:mm"));
                    
                    // Debug parameter values
                    Console.WriteLine($"[DEBUG] WindowStart parameter: {windowStart:hh\\:mm}");
                    Console.WriteLine($"[DEBUG] WindowEnd parameter: {windowEnd:hh\\:mm}");
                    
                    using (var adapter = new SQLiteDataAdapter(command))
                    {
                        var dataTable = new DataTable();
                        adapter.Fill(dataTable);
                        
                        // Debug result count
                        Console.WriteLine($"[DEBUG] Query returned {dataTable.Rows.Count} rows");
                        
                        return dataTable;
                    }
                }
            }
        }

        private string GetDayColumn(DayOfWeek dayOfWeek)
        {
            switch (dayOfWeek)
            {
                case DayOfWeek.Sunday: return "Chk_Sun";
                case DayOfWeek.Monday: return "Chk_Mon";
                case DayOfWeek.Tuesday: return "Chk_Tue";
                case DayOfWeek.Wednesday: return "Chk_Wed";
                case DayOfWeek.Thursday: return "Chk_Thu";
                case DayOfWeek.Friday: return "Chk_Fri";
                case DayOfWeek.Saturday: return "Chk_Sat";
                default: return "Chk_Mon";
            }
        }

        public void ClearAllOnlineTrains()
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = "DELETE FROM Online_Trains";
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.ExecuteNonQuery();
                }
            }
        }

        public void ClearAllTrains()
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                
                // First clear Online_Trains table to avoid foreign key constraint violation
                string clearOnlineTrainsQuery = "DELETE FROM Online_Trains";
                using (var command = new SQLiteCommand(clearOnlineTrainsQuery, connection))
                {
                    command.ExecuteNonQuery();
                }
                
                // Then clear Train_Data table
                string clearTrainDataQuery = "DELETE FROM Train_Data";
                using (var command = new SQLiteCommand(clearTrainDataQuery, connection))
                {
                    command.ExecuteNonQuery();
                }
            }
        }
    }
} 