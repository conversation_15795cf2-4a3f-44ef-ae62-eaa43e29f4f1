using System;
using IPIS.Models;

namespace IPIS.Utils
{
    public static class SessionManager
    {
        private static User _currentUser;
        private static DateTime _loginTime;

        public static User CurrentUser
        {
            get { return _currentUser; }
        }

        public static bool IsLoggedIn
        {
            get { return _currentUser != null && _currentUser.IsActive(); }
        }

        public static DateTime LoginTime
        {
            get { return _loginTime; }
        }

        public static TimeSpan SessionDuration
        {
            get { return DateTime.Now - _loginTime; }
        }

        public static void Login(User user)
        {
            _currentUser = user;
            _loginTime = DateTime.Now;

            // Log the login event
            try
            {
                Logger.LogUserLogin(user.Username);
            }
            catch (Exception ex)
            {
                // Don't let logging errors affect login
                System.Diagnostics.Debug.WriteLine($"Login logging error: {ex.Message}");
            }
        }

        public static void Logout()
        {
            // Log the logout event before clearing the user
            try
            {
                if (_currentUser != null)
                {
                    Logger.LogUserLogout(_currentUser.Username);
                }
            }
            catch (Exception ex)
            {
                // Don't let logging errors affect logout
                System.Diagnostics.Debug.WriteLine($"Logout logging error: {ex.Message}");
            }

            _currentUser = null;
            _loginTime = DateTime.MinValue;
        }

        public static bool HasPermission(string permission)
        {
            if (!IsLoggedIn || _currentUser == null) return false;

            // For legacy users, check individual permissions
            if (_currentUser.Id == -1) // Legacy user
            {
                return _currentUser.HasPermission(permission);
            }

            // For new users, get permissions from their role
            try
            {
                var roleService = new Services.RoleService(new Repositories.SQLiteRoleRepository());
                var role = roleService.GetRoleByName(_currentUser.Role);
                return role?.HasPermission(permission) == true;
            }
            catch
            {
                return false;
            }
        }

        public static bool HasRole(string role)
        {
            return IsLoggedIn && _currentUser.Role?.Equals(role, StringComparison.OrdinalIgnoreCase) == true;
        }

        public static bool IsAdministrator()
        {
            return HasRole("Administrator");
        }

        public static bool IsSupervisor()
        {
            return HasRole("Supervisor");
        }

        public static bool IsOperator()
        {
            return HasRole("Operator");
        }

        public static bool IsUser()
        {
            return HasRole("User");
        }
    }
}