using System.Collections.Generic;
using System.Threading.Tasks;
using IPIS.Models;

namespace IPIS.Repositories.Interfaces
{
    public interface IAnnouncementSequenceRepository
    {
        Task<IEnumerable<AnnouncementSequence>> GetAllSequencesAsync();
        Task<IEnumerable<AnnouncementSequence>> GetSequencesByTemplateAsync(int templateId);
        Task<IEnumerable<AnnouncementSequence>> GetSequencesByLanguageAsync(int languageId);
        Task<AnnouncementSequence> GetSequenceByIdAsync(int id);
        Task<AnnouncementSequence> GetSequenceByTemplateAndLanguageAsync(int templateId, int languageId);
        Task<int> AddSequenceAsync(AnnouncementSequence sequence);
        Task<bool> UpdateSequenceAsync(AnnouncementSequence sequence);
        Task<bool> DeleteSequenceAsync(int id);
        Task<bool> ToggleSequenceStatusAsync(int id);
    }
} 