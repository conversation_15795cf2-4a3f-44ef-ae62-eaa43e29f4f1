// Decompiled with JetBrains decompiler
// Type: ipis.My.Resources.Resources
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;
using System.CodeDom.Compiler;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Globalization;
using System.Resources;
using System.Runtime.CompilerServices;

namespace ipis.My.Resources
{

[StandardModule]
[CompilerGenerated]
[DebuggerNonUserCode]
[GeneratedCode("System.Resources.Tools.StronglyTypedResourceBuilder", "*******")]
[HideModuleName]
internal sealed class Resources
{
  private static ResourceManager resourceMan;
  private static CultureInfo resourceCulture;

  [EditorBrowsable(EditorBrowsableState.Advanced)]
  internal static ResourceManager ResourceManager
  {
    get
    {
      if (object.ReferenceEquals((object) ipis.My.Resources.Resources.resourceMan, (object) null))
        ipis.My.Resources.Resources.resourceMan = new ResourceManager("ipis.Resources", typeof (ipis.My.Resources.Resources).Assembly);
      return ipis.My.Resources.Resources.resourceMan;
    }
  }

  [EditorBrowsable(EditorBrowsableState.Advanced)]
  internal static CultureInfo Culture
  {
    get { return ipis.My.Resources.Resources.resourceCulture; }
    set { ipis.My.Resources.Resources.resourceCulture = value; }
  }

  internal static Bitmap nmc
  {
    get
    {
      return (Bitmap) RuntimeHelpers.GetObjectValue(ipis.My.Resources.Resources.ResourceManager.GetObject("nmc", ipis.My.Resources.Resources.resourceCulture));
    }
  }

  internal static Bitmap NMC_Mush
  {
    get
    {
      return (Bitmap) RuntimeHelpers.GetObjectValue(ipis.My.Resources.Resources.ResourceManager.GetObject("NMC_Mush", ipis.My.Resources.Resources.resourceCulture));
    }
  }

  internal static Bitmap s_logo1
  {
    get
    {
      return (Bitmap) RuntimeHelpers.GetObjectValue(ipis.My.Resources.Resources.ResourceManager.GetObject("s_logo1", ipis.My.Resources.Resources.resourceCulture));
    }
  }

  internal static Bitmap s_logo2
  {
    get
    {
      return (Bitmap) RuntimeHelpers.GetObjectValue(ipis.My.Resources.Resources.ResourceManager.GetObject("s_logo2", ipis.My.Resources.Resources.resourceCulture));
    }
  }

  internal static Icon surya_icon
  {
    get
    {
      return (Icon) RuntimeHelpers.GetObjectValue(ipis.My.Resources.Resources.ResourceManager.GetObject("surya_icon", ipis.My.Resources.Resources.resourceCulture));
    }
  }

  internal static Bitmap suryalogo
  {
    get
    {
      return (Bitmap) RuntimeHelpers.GetObjectValue(ipis.My.Resources.Resources.ResourceManager.GetObject("suryalogo", ipis.My.Resources.Resources.resourceCulture));
    }
  }

  internal static Bitmap train_picture
  {
    get
    {
      return (Bitmap) RuntimeHelpers.GetObjectValue(ipis.My.Resources.Resources.ResourceManager.GetObject("train_picture", ipis.My.Resources.Resources.resourceCulture));
    }
  }
}

}