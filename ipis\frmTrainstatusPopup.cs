// Decompiled with JetBrains decompiler
// Type: ipis.frmTrainstatusPopup
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using ipis.My;
using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

namespace ipis
{

[DesignerGenerated]
public class frmTrainstatusPopup : Form
{
  private static List<WeakReference> __ENCList = new List<WeakReference>();
  private IContainer components;
  [AccessedThroughProperty("cmbStationCode")]
  private ComboBox _cmbStationCode;
  [AccessedThroughProperty("StationCode")]
  private Label _StationCode;
  [AccessedThroughProperty("btnOk")]
  private Button _btnOk;
  [AccessedThroughProperty("cmbStationName")]
  private ComboBox _cmbStationName;
  [AccessedThroughProperty("Label1")]
  private Label _Label1;
  public static string[] sc_codes = new string[601];
  public static string[] sc_names = new string[601];

  [DebuggerNonUserCode]
  public frmTrainstatusPopup()
  {
    this.Load += new EventHandler(this.frmTrainstatusPopup_Load);
    frmTrainstatusPopup.__ENCAddToList((object) this);
    this.InitializeComponent();
  }

  [DebuggerNonUserCode]
  private static void __ENCAddToList(object value)
  {
    lock (frmTrainstatusPopup.__ENCList)
    {
      if (frmTrainstatusPopup.__ENCList.Count == frmTrainstatusPopup.__ENCList.Capacity)
      {
        int index1 = 0;
        int num = checked (frmTrainstatusPopup.__ENCList.Count - 1);
        int index2 = 0;
        while (index2 <= num)
        {
          if (frmTrainstatusPopup.__ENCList[index2].IsAlive)
          {
            if (index2 != index1)
              frmTrainstatusPopup.__ENCList[index1] = frmTrainstatusPopup.__ENCList[index2];
            checked { ++index1; }
          }
          checked { ++index2; }
        }
        frmTrainstatusPopup.__ENCList.RemoveRange(index1, checked (frmTrainstatusPopup.__ENCList.Count - index1));
        frmTrainstatusPopup.__ENCList.Capacity = frmTrainstatusPopup.__ENCList.Count;
      }
      frmTrainstatusPopup.__ENCList.Add(new WeakReference(RuntimeHelpers.GetObjectValue(value)));
    }
  }

  [DebuggerNonUserCode]
  protected override void Dispose(bool disposing)
  {
    try
    {
      if (!disposing || this.components == null)
        return;
      this.components.Dispose();
    }
    finally
    {
      base.Dispose(disposing);
    }
  }

  [DebuggerStepThrough]
  private void InitializeComponent()
  {
    this.cmbStationCode = new ComboBox();
    this.StationCode = new Label();
    this.btnOk = new Button();
    this.cmbStationName = new ComboBox();
    this.Label1 = new Label();
    this.SuspendLayout();
    this.cmbStationCode.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.cmbStationCode.FormattingEnabled = true;
    ComboBox cmbStationCode1 = this.cmbStationCode;
    Point point1 = new Point(42, 45);
    Point point2 = point1;
    cmbStationCode1.Location = point2;
    this.cmbStationCode.Name = "cmbStationCode";
    ComboBox cmbStationCode2 = this.cmbStationCode;
    Size size1 = new Size(84, 21);
    Size size2 = size1;
    cmbStationCode2.Size = size2;
    this.cmbStationCode.TabIndex = 0;
    this.StationCode.AutoSize = true;
    this.StationCode.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label stationCode1 = this.StationCode;
    point1 = new Point(29, 20);
    Point point3 = point1;
    stationCode1.Location = point3;
    this.StationCode.Name = "StationCode";
    Label stationCode2 = this.StationCode;
    size1 = new Size(97, 16 /*0x10*/);
    Size size3 = size1;
    stationCode2.Size = size3;
    this.StationCode.TabIndex = 3;
    this.StationCode.Text = "Station Code";
    this.btnOk.BackColor = SystemColors.ButtonFace;
    this.btnOk.DialogResult = DialogResult.Cancel;
    this.btnOk.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnOk1 = this.btnOk;
    point1 = new Point(138, 92);
    Point point4 = point1;
    btnOk1.Location = point4;
    this.btnOk.Name = "btnOk";
    Button btnOk2 = this.btnOk;
    size1 = new Size(52, 23);
    Size size4 = size1;
    btnOk2.Size = size4;
    this.btnOk.TabIndex = 2;
    this.btnOk.Text = "Ok";
    this.btnOk.UseVisualStyleBackColor = false;
    this.cmbStationName.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.cmbStationName.FormattingEnabled = true;
    ComboBox cmbStationName1 = this.cmbStationName;
    point1 = new Point(177, 45);
    Point point5 = point1;
    cmbStationName1.Location = point5;
    this.cmbStationName.Name = "cmbStationName";
    ComboBox cmbStationName2 = this.cmbStationName;
    size1 = new Size(140, 21);
    Size size5 = size1;
    cmbStationName2.Size = size5;
    this.cmbStationName.TabIndex = 5;
    this.Label1.AutoSize = true;
    this.Label1.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label label1_1 = this.Label1;
    point1 = new Point(198, 20);
    Point point6 = point1;
    label1_1.Location = point6;
    this.Label1.Name = "Label1";
    Label label1_2 = this.Label1;
    size1 = new Size(101, 16 /*0x10*/);
    Size size6 = size1;
    label1_2.Size = size6;
    this.Label1.TabIndex = 6;
    this.Label1.Text = "Station Name";
    this.AcceptButton = (IButtonControl) this.btnOk;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.BackColor = Color.PowderBlue;
    this.CancelButton = (IButtonControl) this.btnOk;
    size1 = new Size(361, 117);
    this.ClientSize = size1;
    this.Controls.Add((Control) this.Label1);
    this.Controls.Add((Control) this.cmbStationName);
    this.Controls.Add((Control) this.StationCode);
    this.Controls.Add((Control) this.btnOk);
    this.Controls.Add((Control) this.cmbStationCode);
    this.FormBorderStyle = FormBorderStyle.FixedToolWindow;
    this.Name = "frmTrainstatusPopup";
    this.Text = "Station Name";
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  internal virtual ComboBox cmbStationCode
  {
    [DebuggerNonUserCode] get { return this._cmbStationCode; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.cmbStationcode_SelectedIndexChanged);
      if (this._cmbStationCode != null)
        this._cmbStationCode.SelectedIndexChanged -= eventHandler;
      this._cmbStationCode = value;
      if (this._cmbStationCode == null)
        return;
      this._cmbStationCode.SelectedIndexChanged += eventHandler;
    }
  }

  internal virtual Label StationCode
  {
    [DebuggerNonUserCode] get { return this._StationCode; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._StationCode = value;
    }
  }

  internal virtual Button btnOk
  {
    [DebuggerNonUserCode] get { return this._btnOk; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnOk_Click);
      if (this._btnOk != null)
        this._btnOk.Click -= eventHandler;
      this._btnOk = value;
      if (this._btnOk == null)
        return;
      this._btnOk.Click += eventHandler;
    }
  }

  internal virtual ComboBox cmbStationName
  {
    [DebuggerNonUserCode] get { return this._cmbStationName; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.CmbStationName_SelectedIndexChanged);
      if (this._cmbStationName != null)
        this._cmbStationName.SelectedIndexChanged -= eventHandler;
      this._cmbStationName = value;
      if (this._cmbStationName == null)
        return;
      this._cmbStationName.SelectedIndexChanged += eventHandler;
    }
  }

  internal virtual Label Label1
  {
    [DebuggerNonUserCode] get { return this._Label1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Label1 = value; }
  }

  private void btnOk_Click(object sender, EventArgs e)
  {
    network_db_read.get_Station_code(this.cmbStationCode.Text, ref frmMainFormIPIS.eng_sc_name, ref frmMainFormIPIS.hin_sc_name, ref frmMainFormIPIS.reg_sc_name);
    this.cmbStationName.Text = frmMainFormIPIS.eng_sc_name;
    frmMainFormIPIS.sc_code[frmMainFormIPIS.sc_code_row_no] = this.cmbStationCode.Text;
    MyProject.Forms.frmMainFormIPIS.Enabled = true;
    this.Close();
  }

  private void frmTrainstatusPopup_Load(object sender, EventArgs e)
  {
    int index = 0;
    this.cmbStationCode.Items.Clear();
    this.cmbStationName.Items.Clear();
    int num = 0;
    string str = Conversions.ToString(num);
    network_db_read.get_Station_codes(ref frmTrainstatusPopup.sc_codes, ref frmTrainstatusPopup.sc_names, ref str);
    int integer = Conversions.ToInteger(str);
    while (index < integer)
    {
      this.cmbStationCode.Items.Add((object) frmTrainstatusPopup.sc_codes[index]);
      this.cmbStationName.Items.Add((object) frmTrainstatusPopup.sc_names[index]);
      checked { ++index; }
    }
    this.cmbStationName.Text = string.Empty;
    this.cmbStationCode.Text = string.Empty;
  }

  private void cmbStationcode_SelectedIndexChanged(object sender, EventArgs e)
  {
    if (Operators.CompareString(this.cmbStationCode.Text, string.Empty, false) == 0)
      return;
    network_db_read.get_Station_code(this.cmbStationCode.Text, ref frmMainFormIPIS.eng_sc_name, ref frmMainFormIPIS.hin_sc_name, ref frmMainFormIPIS.reg_sc_name);
    this.cmbStationName.Text = frmMainFormIPIS.eng_sc_name;
  }

  private void CmbStationName_SelectedIndexChanged(object sender, EventArgs e)
  {
    if (Operators.CompareString(this.cmbStationName.Text, string.Empty, false) == 0)
      return;
    string text1 = this.cmbStationName.Text;
    ComboBox cmbStationCode = this.cmbStationCode;
    string text2 = cmbStationCode.Text;
    network_db_read.get_Station_code_name(text1, ref text2);
    cmbStationCode.Text = text2;
  }
}

}