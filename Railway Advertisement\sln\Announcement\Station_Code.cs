﻿using System;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Windows.Forms;

namespace Announcement
{
	// Token: 0x0200000C RID: 12
	public partial class Station_Code : Form
	{
		// Token: 0x06000052 RID: 82 RVA: 0x0000C541 File Offset: 0x0000A741
		public Station_Code()
		{
			this.InitializeComponent();
		}

		// Token: 0x06000053 RID: 83 RVA: 0x000025C4 File Offset: 0x000007C4
		private void BTN_Exit_Click(object sender, EventArgs e)
		{
			base.Close();
		}

		// Token: 0x06000054 RID: 84 RVA: 0x0000C57C File Offset: 0x0000A77C
		private void BTN_New_Click(object sender, EventArgs e)
		{
			this.TB_SName.Enabled = true;
			this.GB_Wave.Enabled = true;
			this.BTN_Save.Enabled = true;
			this.BTN_Edit.Enabled = false;
			this.Btn_CancelStn.Enabled = true;
			this.CB_SCode.Text = "";
			this.TB_SName.Text = "";
			this.TB_Hwf.Text = "";
			this.TB_Ewf.Text = "";
		}

		// Token: 0x06000055 RID: 85 RVA: 0x0000C610 File Offset: 0x0000A810
		private void BTN_Save_Click(object sender, EventArgs e)
		{
			DataTable dataTable = new DataTable();
			dataTable = this.DB.Read_Database("SELECT * FROM Station_Code WHERE Stn_Code = '" + this.CB_SCode.Text + "'");
			bool flag = dataTable.Rows.Count > 0 && !this.Flag_Edit;
			if (flag)
			{
				MessageBox.Show("Station Code Already Exsist in Database");
			}
			else
			{
				bool flag2 = this.CB_SCode.Text == "" || this.CB_SCode.Text.Contains(' ');
				if (flag2)
				{
					MessageBox.Show("Enter Valid Station Code Details");
				}
				else
				{
					this.CB_SCode.Text = this.CB_SCode.Text.ToUpper();
					try
					{
						bool flag_Edit = this.Flag_Edit;
						if (flag_Edit)
						{
							this.Flag_Edit = false;
							string text = this.CB_SCode.Text;
							this.CB_SCode.Items.Remove(this.CB_SCode.Text);
							this.CB_SCode.Items.Add(text);
							this.DB.Insert_Database("UPDATE Station_Code  SET Stn_EngName = '" + this.TB_SName.Text + "' WHERE ID = " + this.Edit_ID.ToString());
							this.DB.Insert_Database("UPDATE Station_Code  SET EngWave_File = '" + this.TB_Ewf.Text + "' WHERE ID = " + this.Edit_ID.ToString());
							this.DB.Insert_Database("UPDATE Station_Code  SET HindiWave_File = '" + this.TB_Hwf.Text + "' WHERE ID = " + this.Edit_ID.ToString());
							this.Edit_ID = 0;
						}
						else
						{
							bool flag3 = this.TB_SName.Text == "";
							if (flag3)
							{
								MessageBox.Show("Enter Valid Station Name");
								return;
							}
							bool flag4 = this.TB_Hwf.Text == "";
							if (flag4)
							{
								MessageBox.Show("Select Hindi Wave file");
								return;
							}
							bool flag5 = this.TB_Ewf.Text == "";
							if (flag5)
							{
								MessageBox.Show("Select English Wave file");
								return;
							}
							this.CB_SCode.Items.Add(this.CB_SCode.Text);
							string text2 = "\\WAVE\\ENGLISH\\CITY\\" + this.CB_SCode.Text + ".wav";
							string text3 = "\\WAVE\\HINDI\\CITY\\" + this.CB_SCode.Text + ".wav";
							this.DB.Insert_Database(string.Concat(new string[]
							{
								"INSERT INTO Station_Code (Stn_Code,Stn_EngName,EngWave_File,HindiWave_File)VALUES('",
								this.CB_SCode.Text,
								"','",
								this.TB_SName.Text,
								"','",
								this.TB_Ewf.Text,
								"','",
								this.TB_Hwf.Text,
								"')"
							}));
						}
						MessageBox.Show("Data Saved Sucessfully");
					}
					catch (Exception)
					{
						MessageBox.Show("Unable to Save Data Try Again...");
					}
					this.BTN_Save.Enabled = false;
					this.BTN_Edit.Enabled = true;
					this.TB_SName.Enabled = false;
					this.GB_Wave.Enabled = false;
					this.BTN_Del.Enabled = false;
					this.BTN_New.Enabled = true;
				}
			}
		}

		// Token: 0x06000056 RID: 86 RVA: 0x0000C9A8 File Offset: 0x0000ABA8
		private void Station_Code_Load(object sender, EventArgs e)
		{
			this.StnTable = new DataTable();
			this.StnTable = this.DB.Read_Database("Select * From Station_Code");
			this.StnTable = new DataTable();
			this.StnTable = this.DB.Read_Database("Select * From Station_Code");
			this.CB_SCode.Items.Clear();
			bool flag = this.StnTable.Rows.Count > 0;
			if (flag)
			{
				for (int i = 0; i < this.StnTable.Rows.Count; i++)
				{
					bool flag2 = (string)this.StnTable.Rows[i]["Stn_Code"] != null;
					if (flag2)
					{
						this.CB_SCode.Items.Add(this.StnTable.Rows[i]["Stn_Code"].ToString());
					}
				}
				this.CB_SCode.Text = this.StnTable.Rows[0]["Stn_Code"].ToString();
			}
		}

		// Token: 0x06000057 RID: 87 RVA: 0x0000CAD4 File Offset: 0x0000ACD4
		private void BTN_Ewf_Click(object sender, EventArgs e)
		{
			try
			{
				this.Open_WaveFile.InitialDirectory = Application.StartupPath + "\\Data\\WAVE\\HINDI\\CITY\\";
				this.Open_WaveFile.FilterIndex = 2;
				this.Open_WaveFile.Filter = "Wave files (*.wav)| *.txt |All files (*.*)|*.*";
				this.Open_WaveFile.RestoreDirectory = false;
				bool flag = this.Open_WaveFile.ShowDialog() == DialogResult.OK;
				if (flag)
				{
					this.TB_Ewf.Text = this.Open_WaveFile.FileName;
					bool flag2 = this.Open_WaveFile.FileName.Length > 0;
					if (flag2)
					{
						string text = Application.StartupPath + "\\Data\\WAVE\\HINDI\\CITY\\";
						bool flag3 = !Directory.Exists(text);
						if (flag3)
						{
							Directory.CreateDirectory(text);
						}
						string text2 = text + this.CB_SCode.Text + "wav";
						bool flag4 = !File.Exists(text2);
						if (flag4)
						{
							File.Copy(this.Open_WaveFile.FileName, text2);
						}
					}
				}
			}
			catch (Exception ex)
			{
				MessageBox.Show("Unable To Open Wave File" + ex.Message.ToString());
			}
		}

		// Token: 0x06000058 RID: 88 RVA: 0x0000CC08 File Offset: 0x0000AE08
		private void BTN_Hwf_Click(object sender, EventArgs e)
		{
			try
			{
				this.Open_WaveFile.InitialDirectory = Application.StartupPath + "\\Data\\WAVE\\HINDI\\CITY\\";
				this.Open_WaveFile.FilterIndex = 2;
				this.Open_WaveFile.Filter = "Wave files (*.wav)| *.txt |All files (*.*)|*.*";
				this.Open_WaveFile.RestoreDirectory = false;
				bool flag = this.Open_WaveFile.ShowDialog() == DialogResult.OK;
				if (flag)
				{
					this.TB_Hwf.Text = this.Open_WaveFile.FileName;
					bool flag2 = this.Open_WaveFile.FileName.Length > 0;
					if (flag2)
					{
						string text = Application.StartupPath + "\\Data\\WAVE\\HINDI\\CITY\\";
						bool flag3 = !Directory.Exists(text);
						if (flag3)
						{
							Directory.CreateDirectory(text);
						}
						string text2 = text + this.CB_SCode.Text + "wav";
						bool flag4 = !File.Exists(text2);
						if (flag4)
						{
							File.Copy(this.Open_WaveFile.FileName, text2);
						}
					}
				}
			}
			catch (Exception ex)
			{
				MessageBox.Show("Unable To Open Wave File" + ex.Message.ToString());
			}
		}

		// Token: 0x06000059 RID: 89 RVA: 0x0000CD3C File Offset: 0x0000AF3C
		private void CB_SCode_SelectedIndexChanged(object sender, EventArgs e)
		{
			this.StnTable = new DataTable();
			this.StnTable = this.DB.Read_Database("Select * From Station_Code WHERE Stn_Code = '" + this.CB_SCode.Text + "'");
			this.TB_SName.Text = this.StnTable.Rows[0]["Stn_EngName"].ToString();
			this.TB_Hwf.Text = this.StnTable.Rows[0]["HindiWave_File"].ToString();
			this.TB_Ewf.Text = this.StnTable.Rows[0]["EngWave_File"].ToString();
		}

		// Token: 0x0600005A RID: 90 RVA: 0x000025C1 File Offset: 0x000007C1
		private void TB_SName_TextChanged(object sender, EventArgs e)
		{
		}

		// Token: 0x0600005B RID: 91 RVA: 0x0000CE04 File Offset: 0x0000B004
		private void BTN_Edit_Click(object sender, EventArgs e)
		{
			this.BTN_New.Enabled = false;
			this.BTN_Del.Enabled = true;
			this.BTN_Save.Enabled = true;
			this.TB_SName.Enabled = true;
			this.GB_Wave.Enabled = true;
			this.Btn_CancelStn.Enabled = true;
			this.Flag_Edit = true;
			this.StnTable = new DataTable();
			bool flag = this.CB_SCode.Text != "";
			if (flag)
			{
				this.StnTable = this.DB.Read_Database("Select ID From Station_Code WHERE Stn_Code = '" + this.CB_SCode.Text + "'");
				this.Edit_ID = (int)this.StnTable.Rows[0]["ID"];
			}
			else
			{
				MessageBox.Show("Select Station Code First");
			}
			this.TB_SName.Focus();
		}

		// Token: 0x0600005C RID: 92 RVA: 0x0000CEFC File Offset: 0x0000B0FC
		private void BTN_Del_Click(object sender, EventArgs e)
		{
			this.StnTable = new DataTable();
			this.DB.Insert_Database("DELETE * From Station_Code WHERE Stn_Code = '" + this.CB_SCode.Text + "'");
			MessageBox.Show("Data Delete Sucessfully");
			this.CB_SCode.Items.Remove(this.CB_SCode.Text);
			this.CB_SCode.Text = "";
			this.TB_SName.Text = "";
			this.TB_Hwf.Text = "";
			this.TB_Ewf.Text = "";
			this.BTN_Del.Enabled = false;
			this.StnTable = new DataTable();
			this.StnTable = this.DB.Read_Database("Select * From Station_Code");
			this.StnTable = new DataTable();
			this.StnTable = this.DB.Read_Database("Select * From Station_Code");
			this.CB_SCode.Items.Clear();
			bool flag = this.StnTable.Rows.Count > 0;
			if (flag)
			{
				for (int i = 0; i < this.StnTable.Rows.Count; i++)
				{
					bool flag2 = (string)this.StnTable.Rows[i]["Stn_Code"] != null;
					if (flag2)
					{
						this.CB_SCode.Items.Add(this.StnTable.Rows[i]["Stn_Code"].ToString());
					}
				}
				this.CB_SCode.Text = this.StnTable.Rows[0]["Stn_Code"].ToString();
			}
			this.TB_SName.Enabled = false;
			this.GB_Wave.Enabled = false;
			this.BTN_Del.Enabled = false;
			this.BTN_Save.Enabled = false;
			this.BTN_New.Enabled = true;
		}

		// Token: 0x0600005D RID: 93 RVA: 0x000025C1 File Offset: 0x000007C1
		private void Open_WaveFile_FileOk(object sender, CancelEventArgs e)
		{
		}

		// Token: 0x0600005E RID: 94 RVA: 0x0000D110 File Offset: 0x0000B310
		private void Btn_CancelStn_Click(object sender, EventArgs e)
		{
			this.BTN_Save.Enabled = false;
			this.BTN_Edit.Enabled = true;
			this.TB_SName.Enabled = false;
			this.GB_Wave.Enabled = false;
			this.BTN_Del.Enabled = false;
			this.BTN_New.Enabled = true;
			this.Station_Code_Load(sender, e);
			this.BTN_Save.Enabled = false;
			this.Btn_CancelStn.Enabled = false;
		}

		// Token: 0x04000063 RID: 99
		private Class_Database DB = new Class_Database();

		// Token: 0x04000064 RID: 100
		private DataTable TrainTable;

		// Token: 0x04000065 RID: 101
		private DataTable StnTable;

		// Token: 0x04000066 RID: 102
		private bool Flag_Edit = false;

		// Token: 0x04000067 RID: 103
		private bool Flag_New = false;

		// Token: 0x04000068 RID: 104
		private int GP_Counter;

		// Token: 0x04000069 RID: 105
		private int Edit_ID = 0;
	}
}
