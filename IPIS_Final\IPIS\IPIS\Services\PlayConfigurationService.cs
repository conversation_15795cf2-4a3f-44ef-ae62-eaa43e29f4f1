using System.Data;
using IPIS.Repositories.Interfaces;

namespace IPIS.Services
{
    public class PlayConfigurationService
    {
        private readonly IPlayConfigurationRepository _repository;

        public PlayConfigurationService(IPlayConfigurationRepository repository)
        {
            _repository = repository;
        }

        public DataTable GetPlayConfiguration(string trainStatus)
        {
            return _repository.GetPlayConfiguration(trainStatus);
        }

        public void AddPlayConfiguration(string trainStatus, string trainType, bool pfAvl, bool[] languages)
        {
            if (string.IsNullOrEmpty(trainStatus))
                throw new ArgumentException("Train status cannot be empty", nameof(trainStatus));

            if (languages == null || languages.Length != 10)
                throw new ArgumentException("Languages array must contain exactly 10 elements", nameof(languages));

            _repository.AddPlayConfiguration(trainStatus, trainType, pfAvl, languages);
        }

        public void UpdatePlayConfiguration(string trainStatus, string trainType, bool pfAvl, bool[] languages)
        {
            if (string.IsNullOrEmpty(trainStatus))
                throw new ArgumentException("Train status cannot be empty", nameof(trainStatus));

            if (languages == null || languages.Length != 10)
                throw new ArgumentException("Languages array must contain exactly 10 elements", nameof(languages));

            _repository.UpdatePlayConfiguration(trainStatus, trainType, pfAvl, languages);
        }

        public void DeletePlayConfiguration(string trainStatus)
        {
            if (string.IsNullOrEmpty(trainStatus))
                throw new ArgumentException("Train status cannot be empty", nameof(trainStatus));

            _repository.DeletePlayConfiguration(trainStatus);
        }

        public DataTable GetAllPlayConfigurations()
        {
            return _repository.GetAllPlayConfigurations();
        }
    }
} 