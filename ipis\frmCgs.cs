// Decompiled with JetBrains decompiler
// Type: ipis.frmCgs
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

namespace ipis
{

[DesignerGenerated]
public class frmCgs : Form
{
  private static List<WeakReference> __ENCList = new List<WeakReference>();
  private IContainer components;
  [AccessedThroughProperty("btnExit")]
  private Button _btnExit;
  [AccessedThroughProperty("btnClear")]
  private Button _btnClear;
  [AccessedThroughProperty("btnOk")]
  private Button _btnOk;
  [AccessedThroughProperty("Panel1")]
  private Panel _Panel1;
  [AccessedThroughProperty("t26")]
  private TextBox _t26;
  [AccessedThroughProperty("Label2")]
  private Label _Label2;
  [AccessedThroughProperty("t25")]
  private TextBox _t25;
  [AccessedThroughProperty("t24")]
  private TextBox _t24;
  [AccessedThroughProperty("t23")]
  private TextBox _t23;
  [AccessedThroughProperty("t22")]
  private TextBox _t22;
  [AccessedThroughProperty("t21")]
  private TextBox _t21;
  [AccessedThroughProperty("t20")]
  private TextBox _t20;
  [AccessedThroughProperty("t19")]
  private TextBox _t19;
  [AccessedThroughProperty("t18")]
  private TextBox _t18;
  [AccessedThroughProperty("t17")]
  private TextBox _t17;
  [AccessedThroughProperty("t16")]
  private TextBox _t16;
  [AccessedThroughProperty("t15")]
  private TextBox _t15;
  [AccessedThroughProperty("t14")]
  private TextBox _t14;
  [AccessedThroughProperty("t13")]
  private TextBox _t13;
  [AccessedThroughProperty("t12")]
  private TextBox _t12;
  [AccessedThroughProperty("t11")]
  private TextBox _t11;
  [AccessedThroughProperty("t10")]
  private TextBox _t10;
  [AccessedThroughProperty("t9")]
  private TextBox _t9;
  [AccessedThroughProperty("t8")]
  private TextBox _t8;
  [AccessedThroughProperty("t7")]
  private TextBox _t7;
  [AccessedThroughProperty("t6")]
  private TextBox _t6;
  [AccessedThroughProperty("t5")]
  private TextBox _t5;
  [AccessedThroughProperty("t4")]
  private TextBox _t4;
  [AccessedThroughProperty("t3")]
  private TextBox _t3;
  [AccessedThroughProperty("t2")]
  private TextBox _t2;
  [AccessedThroughProperty("t1")]
  private TextBox _t1;
  [AccessedThroughProperty("Label1")]
  private Label _Label1;
  public static readonly DBNull Value;
  public static string[] a = new string[27];

  [DebuggerNonUserCode]
  public frmCgs()
  {
    frmCgs.__ENCAddToList((object) this);
    this.InitializeComponent();
  }

  [DebuggerNonUserCode]
  private static void __ENCAddToList(object value)
  {
    lock (frmCgs.__ENCList)
    {
      if (frmCgs.__ENCList.Count == frmCgs.__ENCList.Capacity)
      {
        int index1 = 0;
        int num = checked (frmCgs.__ENCList.Count - 1);
        int index2 = 0;
        while (index2 <= num)
        {
          if (frmCgs.__ENCList[index2].IsAlive)
          {
            if (index2 != index1)
              frmCgs.__ENCList[index1] = frmCgs.__ENCList[index2];
            checked { ++index1; }
          }
          checked { ++index2; }
        }
        frmCgs.__ENCList.RemoveRange(index1, checked (frmCgs.__ENCList.Count - index1));
        frmCgs.__ENCList.Capacity = frmCgs.__ENCList.Count;
      }
      frmCgs.__ENCList.Add(new WeakReference(RuntimeHelpers.GetObjectValue(value)));
    }
  }

  [DebuggerNonUserCode]
  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  [DebuggerStepThrough]
  private void InitializeComponent()
  {
    this.btnExit = new Button();
    this.btnClear = new Button();
    this.btnOk = new Button();
    this.Panel1 = new Panel();
    this.t26 = new TextBox();
    this.Label2 = new Label();
    this.t25 = new TextBox();
    this.t24 = new TextBox();
    this.t23 = new TextBox();
    this.t22 = new TextBox();
    this.t21 = new TextBox();
    this.t20 = new TextBox();
    this.t19 = new TextBox();
    this.t18 = new TextBox();
    this.t17 = new TextBox();
    this.t16 = new TextBox();
    this.t15 = new TextBox();
    this.t14 = new TextBox();
    this.t13 = new TextBox();
    this.t12 = new TextBox();
    this.t11 = new TextBox();
    this.t10 = new TextBox();
    this.t9 = new TextBox();
    this.t8 = new TextBox();
    this.t7 = new TextBox();
    this.t6 = new TextBox();
    this.t5 = new TextBox();
    this.t4 = new TextBox();
    this.t3 = new TextBox();
    this.t2 = new TextBox();
    this.t1 = new TextBox();
    this.Label1 = new Label();
    this.Panel1.SuspendLayout();
    this.SuspendLayout();
    this.btnExit.BackColor = SystemColors.ButtonFace;
    this.btnExit.DialogResult = DialogResult.Cancel;
    this.btnExit.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnExit1 = this.btnExit;
    Point point1 = new Point(356, 140);
    Point point2 = point1;
    btnExit1.Location = point2;
    this.btnExit.Name = "btnExit";
    Button btnExit2 = this.btnExit;
    Size size1 = new Size(60, 25);
    Size size2 = size1;
    btnExit2.Size = size2;
    this.btnExit.TabIndex = 29;
    this.btnExit.Text = "Exit";
    this.btnExit.UseVisualStyleBackColor = false;
    this.btnClear.BackColor = SystemColors.ButtonFace;
    this.btnClear.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnClear1 = this.btnClear;
    point1 = new Point(272, 140);
    Point point3 = point1;
    btnClear1.Location = point3;
    this.btnClear.Name = "btnClear";
    Button btnClear2 = this.btnClear;
    size1 = new Size(60, 25);
    Size size3 = size1;
    btnClear2.Size = size3;
    this.btnClear.TabIndex = 28;
    this.btnClear.Text = "&Clear";
    this.btnClear.UseVisualStyleBackColor = false;
    this.btnOk.BackColor = SystemColors.ButtonFace;
    this.btnOk.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnOk1 = this.btnOk;
    point1 = new Point(190, 140);
    Point point4 = point1;
    btnOk1.Location = point4;
    this.btnOk.Name = "btnOk";
    Button btnOk2 = this.btnOk;
    size1 = new Size(60, 25);
    Size size4 = size1;
    btnOk2.Size = size4;
    this.btnOk.TabIndex = 27;
    this.btnOk.Text = "Ok";
    this.btnOk.UseVisualStyleBackColor = false;
    this.Panel1.BackColor = SystemColors.ButtonHighlight;
    this.Panel1.BorderStyle = BorderStyle.Fixed3D;
    this.Panel1.Controls.Add((Control) this.t26);
    this.Panel1.Controls.Add((Control) this.Label2);
    this.Panel1.Controls.Add((Control) this.t25);
    this.Panel1.Controls.Add((Control) this.t24);
    this.Panel1.Controls.Add((Control) this.t23);
    this.Panel1.Controls.Add((Control) this.t22);
    this.Panel1.Controls.Add((Control) this.t21);
    this.Panel1.Controls.Add((Control) this.t20);
    this.Panel1.Controls.Add((Control) this.t19);
    this.Panel1.Controls.Add((Control) this.t18);
    this.Panel1.Controls.Add((Control) this.t17);
    this.Panel1.Controls.Add((Control) this.t16);
    this.Panel1.Controls.Add((Control) this.t15);
    this.Panel1.Controls.Add((Control) this.t14);
    this.Panel1.Controls.Add((Control) this.t13);
    this.Panel1.Controls.Add((Control) this.t12);
    this.Panel1.Controls.Add((Control) this.t11);
    this.Panel1.Controls.Add((Control) this.t10);
    this.Panel1.Controls.Add((Control) this.t9);
    this.Panel1.Controls.Add((Control) this.t8);
    this.Panel1.Controls.Add((Control) this.t7);
    this.Panel1.Controls.Add((Control) this.t6);
    this.Panel1.Controls.Add((Control) this.t5);
    this.Panel1.Controls.Add((Control) this.t4);
    this.Panel1.Controls.Add((Control) this.t3);
    this.Panel1.Controls.Add((Control) this.t2);
    this.Panel1.Controls.Add((Control) this.t1);
    this.Panel1.Controls.Add((Control) this.Label1);
    Panel panel1_1 = this.Panel1;
    point1 = new Point(12, 12);
    Point point5 = point1;
    panel1_1.Location = point5;
    this.Panel1.Name = "Panel1";
    Panel panel1_2 = this.Panel1;
    size1 = new Size(615, 99);
    Size size5 = size1;
    panel1_2.Size = size5;
    this.Panel1.TabIndex = 4;
    this.t26.BackColor = SystemColors.ButtonFace;
    TextBox t26_1 = this.t26;
    point1 = new Point(532, 46);
    Point point6 = point1;
    t26_1.Location = point6;
    this.t26.MaxLength = 3;
    this.t26.Name = "t26";
    TextBox t26_2 = this.t26;
    size1 = new Size(35, 20);
    Size size6 = size1;
    t26_2.Size = size6;
    this.t26.TabIndex = 27;
    this.Label2.AutoSize = true;
    this.Label2.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label label2_1 = this.Label2;
    point1 = new Point(572, 49);
    Point point7 = point1;
    label2_1.Location = point7;
    this.Label2.Name = "Label2";
    Label label2_2 = this.Label2;
    size1 = new Size(39, 13);
    Size size7 = size1;
    label2_2.Size = size7;
    this.Label2.TabIndex = 26;
    this.Label2.Text = "Down";
    this.t25.BackColor = SystemColors.ButtonFace;
    TextBox t25_1 = this.t25;
    point1 = new Point(491, 46);
    Point point8 = point1;
    t25_1.Location = point8;
    this.t25.MaxLength = 3;
    this.t25.Name = "t25";
    TextBox t25_2 = this.t25;
    size1 = new Size(35, 20);
    Size size8 = size1;
    t25_2.Size = size8;
    this.t25.TabIndex = 25;
    this.t24.BackColor = SystemColors.ButtonFace;
    TextBox t24_1 = this.t24;
    point1 = new Point(450, 46);
    Point point9 = point1;
    t24_1.Location = point9;
    this.t24.MaxLength = 3;
    this.t24.Name = "t24";
    TextBox t24_2 = this.t24;
    size1 = new Size(35, 20);
    Size size9 = size1;
    t24_2.Size = size9;
    this.t24.TabIndex = 24;
    this.t23.BackColor = SystemColors.ButtonFace;
    TextBox t23_1 = this.t23;
    point1 = new Point(408, 46);
    Point point10 = point1;
    t23_1.Location = point10;
    this.t23.MaxLength = 3;
    this.t23.Name = "t23";
    TextBox t23_2 = this.t23;
    size1 = new Size(35, 20);
    Size size10 = size1;
    t23_2.Size = size10;
    this.t23.TabIndex = 23;
    this.t22.BackColor = SystemColors.ButtonFace;
    TextBox t22_1 = this.t22;
    point1 = new Point(367, 46);
    Point point11 = point1;
    t22_1.Location = point11;
    this.t22.MaxLength = 3;
    this.t22.Name = "t22";
    TextBox t22_2 = this.t22;
    size1 = new Size(35, 20);
    Size size11 = size1;
    t22_2.Size = size11;
    this.t22.TabIndex = 22;
    this.t21.BackColor = SystemColors.ButtonFace;
    TextBox t21_1 = this.t21;
    point1 = new Point(326, 46);
    Point point12 = point1;
    t21_1.Location = point12;
    this.t21.MaxLength = 3;
    this.t21.Name = "t21";
    TextBox t21_2 = this.t21;
    size1 = new Size(35, 20);
    Size size12 = size1;
    t21_2.Size = size12;
    this.t21.TabIndex = 21;
    this.t20.BackColor = SystemColors.ButtonFace;
    TextBox t20_1 = this.t20;
    point1 = new Point(283, 46);
    Point point13 = point1;
    t20_1.Location = point13;
    this.t20.MaxLength = 3;
    this.t20.Name = "t20";
    TextBox t20_2 = this.t20;
    size1 = new Size(35, 20);
    Size size13 = size1;
    t20_2.Size = size13;
    this.t20.TabIndex = 20;
    this.t19.BackColor = SystemColors.ButtonFace;
    TextBox t19_1 = this.t19;
    point1 = new Point(242, 46);
    Point point14 = point1;
    t19_1.Location = point14;
    this.t19.MaxLength = 3;
    this.t19.Name = "t19";
    TextBox t19_2 = this.t19;
    size1 = new Size(35, 20);
    Size size14 = size1;
    t19_2.Size = size14;
    this.t19.TabIndex = 19;
    this.t18.BackColor = SystemColors.ButtonFace;
    TextBox t18_1 = this.t18;
    point1 = new Point(201, 46);
    Point point15 = point1;
    t18_1.Location = point15;
    this.t18.MaxLength = 3;
    this.t18.Name = "t18";
    TextBox t18_2 = this.t18;
    size1 = new Size(35, 20);
    Size size15 = size1;
    t18_2.Size = size15;
    this.t18.TabIndex = 18;
    this.t17.BackColor = SystemColors.ButtonFace;
    TextBox t17_1 = this.t17;
    point1 = new Point(157, 46);
    Point point16 = point1;
    t17_1.Location = point16;
    this.t17.MaxLength = 3;
    this.t17.Name = "t17";
    TextBox t17_2 = this.t17;
    size1 = new Size(35, 20);
    Size size16 = size1;
    t17_2.Size = size16;
    this.t17.TabIndex = 17;
    this.t16.BackColor = SystemColors.ButtonFace;
    TextBox t16_1 = this.t16;
    point1 = new Point(116, 46);
    Point point17 = point1;
    t16_1.Location = point17;
    this.t16.MaxLength = 3;
    this.t16.Name = "t16";
    TextBox t16_2 = this.t16;
    size1 = new Size(35, 20);
    Size size17 = size1;
    t16_2.Size = size17;
    this.t16.TabIndex = 16 /*0x10*/;
    this.t15.BackColor = SystemColors.ButtonFace;
    TextBox t15_1 = this.t15;
    point1 = new Point(75, 46);
    Point point18 = point1;
    t15_1.Location = point18;
    this.t15.MaxLength = 3;
    this.t15.Name = "t15";
    TextBox t15_2 = this.t15;
    size1 = new Size(35, 20);
    Size size18 = size1;
    t15_2.Size = size18;
    this.t15.TabIndex = 15;
    this.t14.BackColor = SystemColors.ButtonFace;
    TextBox t14_1 = this.t14;
    point1 = new Point(34, 46);
    Point point19 = point1;
    t14_1.Location = point19;
    this.t14.MaxLength = 3;
    this.t14.Name = "t14";
    TextBox t14_2 = this.t14;
    size1 = new Size(37, 20);
    Size size19 = size1;
    t14_2.Size = size19;
    this.t14.TabIndex = 14;
    this.t13.BackColor = SystemColors.ButtonFace;
    TextBox t13_1 = this.t13;
    point1 = new Point(532, 17);
    Point point20 = point1;
    t13_1.Location = point20;
    this.t13.MaxLength = 3;
    this.t13.Name = "t13";
    TextBox t13_2 = this.t13;
    size1 = new Size(35, 20);
    Size size20 = size1;
    t13_2.Size = size20;
    this.t13.TabIndex = 13;
    this.t12.BackColor = SystemColors.ButtonFace;
    TextBox t12_1 = this.t12;
    point1 = new Point(491, 17);
    Point point21 = point1;
    t12_1.Location = point21;
    this.t12.MaxLength = 3;
    this.t12.Name = "t12";
    TextBox t12_2 = this.t12;
    size1 = new Size(35, 20);
    Size size21 = size1;
    t12_2.Size = size21;
    this.t12.TabIndex = 12;
    this.t11.BackColor = SystemColors.ButtonFace;
    TextBox t11_1 = this.t11;
    point1 = new Point(450, 17);
    Point point22 = point1;
    t11_1.Location = point22;
    this.t11.MaxLength = 3;
    this.t11.Name = "t11";
    TextBox t11_2 = this.t11;
    size1 = new Size(35, 20);
    Size size22 = size1;
    t11_2.Size = size22;
    this.t11.TabIndex = 11;
    this.t10.BackColor = SystemColors.ButtonFace;
    TextBox t10_1 = this.t10;
    point1 = new Point(408, 17);
    Point point23 = point1;
    t10_1.Location = point23;
    this.t10.MaxLength = 3;
    this.t10.Name = "t10";
    TextBox t10_2 = this.t10;
    size1 = new Size(35, 20);
    Size size23 = size1;
    t10_2.Size = size23;
    this.t10.TabIndex = 10;
    this.t9.BackColor = SystemColors.ButtonFace;
    TextBox t9_1 = this.t9;
    point1 = new Point(367, 17);
    Point point24 = point1;
    t9_1.Location = point24;
    this.t9.MaxLength = 3;
    this.t9.Name = "t9";
    TextBox t9_2 = this.t9;
    size1 = new Size(35, 20);
    Size size24 = size1;
    t9_2.Size = size24;
    this.t9.TabIndex = 9;
    this.t8.BackColor = SystemColors.ButtonFace;
    TextBox t8_1 = this.t8;
    point1 = new Point(326, 17);
    Point point25 = point1;
    t8_1.Location = point25;
    this.t8.MaxLength = 3;
    this.t8.Name = "t8";
    TextBox t8_2 = this.t8;
    size1 = new Size(35, 20);
    Size size25 = size1;
    t8_2.Size = size25;
    this.t8.TabIndex = 8;
    this.t7.BackColor = SystemColors.ButtonFace;
    TextBox t7_1 = this.t7;
    point1 = new Point(283, 17);
    Point point26 = point1;
    t7_1.Location = point26;
    this.t7.MaxLength = 3;
    this.t7.Name = "t7";
    TextBox t7_2 = this.t7;
    size1 = new Size(35, 20);
    Size size26 = size1;
    t7_2.Size = size26;
    this.t7.TabIndex = 7;
    this.t6.BackColor = SystemColors.ButtonFace;
    TextBox t6_1 = this.t6;
    point1 = new Point(242, 17);
    Point point27 = point1;
    t6_1.Location = point27;
    this.t6.MaxLength = 3;
    this.t6.Name = "t6";
    TextBox t6_2 = this.t6;
    size1 = new Size(35, 20);
    Size size27 = size1;
    t6_2.Size = size27;
    this.t6.TabIndex = 6;
    this.t5.BackColor = SystemColors.ButtonFace;
    TextBox t5_1 = this.t5;
    point1 = new Point(201, 17);
    Point point28 = point1;
    t5_1.Location = point28;
    this.t5.MaxLength = 3;
    this.t5.Name = "t5";
    TextBox t5_2 = this.t5;
    size1 = new Size(35, 20);
    Size size28 = size1;
    t5_2.Size = size28;
    this.t5.TabIndex = 5;
    this.t4.BackColor = SystemColors.ButtonFace;
    TextBox t4_1 = this.t4;
    point1 = new Point(157, 17);
    Point point29 = point1;
    t4_1.Location = point29;
    this.t4.MaxLength = 3;
    this.t4.Name = "t4";
    TextBox t4_2 = this.t4;
    size1 = new Size(35, 20);
    Size size29 = size1;
    t4_2.Size = size29;
    this.t4.TabIndex = 4;
    this.t3.BackColor = SystemColors.ButtonFace;
    TextBox t3_1 = this.t3;
    point1 = new Point(116, 17);
    Point point30 = point1;
    t3_1.Location = point30;
    this.t3.MaxLength = 3;
    this.t3.Name = "t3";
    TextBox t3_2 = this.t3;
    size1 = new Size(35, 20);
    Size size30 = size1;
    t3_2.Size = size30;
    this.t3.TabIndex = 3;
    this.t2.BackColor = SystemColors.ButtonFace;
    TextBox t2_1 = this.t2;
    point1 = new Point(75, 17);
    Point point31 = point1;
    t2_1.Location = point31;
    this.t2.MaxLength = 3;
    this.t2.Name = "t2";
    TextBox t2_2 = this.t2;
    size1 = new Size(35, 20);
    Size size31 = size1;
    t2_2.Size = size31;
    this.t2.TabIndex = 2;
    this.t1.BackColor = SystemColors.ButtonFace;
    TextBox t1_1 = this.t1;
    point1 = new Point(34, 17);
    Point point32 = point1;
    t1_1.Location = point32;
    this.t1.MaxLength = 3;
    this.t1.Name = "t1";
    TextBox t1_2 = this.t1;
    size1 = new Size(35, 20);
    Size size32 = size1;
    t1_2.Size = size32;
    this.t1.TabIndex = 1;
    this.Label1.AutoSize = true;
    this.Label1.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label label1_1 = this.Label1;
    point1 = new Point(3, 20);
    Point point33 = point1;
    label1_1.Location = point33;
    this.Label1.Name = "Label1";
    Label label1_2 = this.Label1;
    size1 = new Size(24, 13);
    Size size33 = size1;
    label1_2.Size = size33;
    this.Label1.TabIndex = 0;
    this.Label1.Text = "UP";
    this.AcceptButton = (IButtonControl) this.btnOk;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.BackColor = Color.PowderBlue;
    this.CancelButton = (IButtonControl) this.btnExit;
    size1 = new Size(638, 190);
    this.ClientSize = size1;
    this.Controls.Add((Control) this.btnExit);
    this.Controls.Add((Control) this.btnClear);
    this.Controls.Add((Control) this.btnOk);
    this.Controls.Add((Control) this.Panel1);
    this.FormBorderStyle = FormBorderStyle.FixedToolWindow;
    this.Name = "frmCgs";
    this.Text = "frmCgs";
    this.Panel1.ResumeLayout(false);
    this.Panel1.PerformLayout();
    this.ResumeLayout(false);
  }

  internal virtual Button btnExit
  {
    [DebuggerNonUserCode] get { return this._btnExit; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnExit_Click);
      if (this._btnExit != null)
        this._btnExit.Click -= eventHandler;
      this._btnExit = value;
      if (this._btnExit == null)
        return;
      this._btnExit.Click += eventHandler;
    }
  }

  internal virtual Button btnClear
  {
    [DebuggerNonUserCode] get { return this._btnClear; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.Button2_Click);
      if (this._btnClear != null)
        this._btnClear.Click -= eventHandler;
      this._btnClear = value;
      if (this._btnClear == null)
        return;
      this._btnClear.Click += eventHandler;
    }
  }

  internal virtual Button btnOk
  {
    [DebuggerNonUserCode] get { return this._btnOk; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnOk_Click);
      if (this._btnOk != null)
        this._btnOk.Click -= eventHandler;
      this._btnOk = value;
      if (this._btnOk == null)
        return;
      this._btnOk.Click += eventHandler;
    }
  }

  internal virtual Panel Panel1
  {
    [DebuggerNonUserCode] get { return this._Panel1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Panel1 = value; }
  }

  internal virtual TextBox t26
  {
    [DebuggerNonUserCode] get { return this._t26; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._t26 = value; }
  }

  internal virtual Label Label2
  {
    [DebuggerNonUserCode] get { return this._Label2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Label2 = value; }
  }

  internal virtual TextBox t25
  {
    [DebuggerNonUserCode] get { return this._t25; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._t25 = value; }
  }

  internal virtual TextBox t24
  {
    [DebuggerNonUserCode] get { return this._t24; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._t24 = value; }
  }

  internal virtual TextBox t23
  {
    [DebuggerNonUserCode] get { return this._t23; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._t23 = value; }
  }

  internal virtual TextBox t22
  {
    [DebuggerNonUserCode] get { return this._t22; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._t22 = value; }
  }

  internal virtual TextBox t21
  {
    [DebuggerNonUserCode] get { return this._t21; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._t21 = value; }
  }

  internal virtual TextBox t20
  {
    [DebuggerNonUserCode] get { return this._t20; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._t20 = value; }
  }

  internal virtual TextBox t19
  {
    [DebuggerNonUserCode] get { return this._t19; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._t19 = value; }
  }

  internal virtual TextBox t18
  {
    [DebuggerNonUserCode] get { return this._t18; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._t18 = value; }
  }

  internal virtual TextBox t17
  {
    [DebuggerNonUserCode] get { return this._t17; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._t17 = value; }
  }

  internal virtual TextBox t16
  {
    [DebuggerNonUserCode] get { return this._t16; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._t16 = value; }
  }

  internal virtual TextBox t15
  {
    [DebuggerNonUserCode] get { return this._t15; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._t15 = value; }
  }

  internal virtual TextBox t14
  {
    [DebuggerNonUserCode] get { return this._t14; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._t14 = value; }
  }

  internal virtual TextBox t13
  {
    [DebuggerNonUserCode] get { return this._t13; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._t13 = value; }
  }

  internal virtual TextBox t12
  {
    [DebuggerNonUserCode] get { return this._t12; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._t12 = value; }
  }

  internal virtual TextBox t11
  {
    [DebuggerNonUserCode] get { return this._t11; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._t11 = value; }
  }

  internal virtual TextBox t10
  {
    [DebuggerNonUserCode] get { return this._t10; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._t10 = value; }
  }

  internal virtual TextBox t9
  {
    [DebuggerNonUserCode] get { return this._t9; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._t9 = value; }
  }

  internal virtual TextBox t8
  {
    [DebuggerNonUserCode] get { return this._t8; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._t8 = value; }
  }

  internal virtual TextBox t7
  {
    [DebuggerNonUserCode] get { return this._t7; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._t7 = value; }
  }

  internal virtual TextBox t6
  {
    [DebuggerNonUserCode] get { return this._t6; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._t6 = value; }
  }

  internal virtual TextBox t5
  {
    [DebuggerNonUserCode] get { return this._t5; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._t5 = value; }
  }

  internal virtual TextBox t4
  {
    [DebuggerNonUserCode] get { return this._t4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._t4 = value; }
  }

  internal virtual TextBox t3
  {
    [DebuggerNonUserCode] get { return this._t3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._t3 = value; }
  }

  internal virtual TextBox t2
  {
    [DebuggerNonUserCode] get { return this._t2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._t2 = value; }
  }

  internal virtual TextBox t1
  {
    [DebuggerNonUserCode] get { return this._t1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._t1 = value; }
  }

  internal virtual Label Label1
  {
    [DebuggerNonUserCode] get { return this._Label1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Label1 = value; }
  }

  public string TT1
  {
    get { return frmCgs.a[0]; }
    set { frmCgs.a[0] = value; }
  }

  public string TT2
  {
    get { return frmCgs.a[1]; }
    set { frmCgs.a[1] = value; }
  }

  public string TT3
  {
    get { return frmCgs.a[2]; }
    set { frmCgs.a[2] = value; }
  }

  public string TT4
  {
    get { return frmCgs.a[3]; }
    set { frmCgs.a[3] = value; }
  }

  public string TT5
  {
    get { return frmCgs.a[4]; }
    set { frmCgs.a[4] = value; }
  }

  public string TT6
  {
    get { return frmCgs.a[5]; }
    set { frmCgs.a[5] = value; }
  }

  public string TT7
  {
    get { return frmCgs.a[6]; }
    set { frmCgs.a[6] = value; }
  }

  public string TT8
  {
    get { return frmCgs.a[7]; }
    set { frmCgs.a[7] = value; }
  }

  public string TT9
  {
    get { return frmCgs.a[8]; }
    set { frmCgs.a[8] = value; }
  }

  public string TT10
  {
    get { return frmCgs.a[9]; }
    set { frmCgs.a[9] = value; }
  }

  public string TT11
  {
    get { return frmCgs.a[10]; }
    set { frmCgs.a[10] = value; }
  }

  public string TT12
  {
    get { return frmCgs.a[11]; }
    set { frmCgs.a[11] = value; }
  }

  public string TT13
  {
    get { return frmCgs.a[12]; }
    set { frmCgs.a[12] = value; }
  }

  public string TT14
  {
    get { return frmCgs.a[13]; }
    set { frmCgs.a[13] = value; }
  }

  public string TT15
  {
    get { return frmCgs.a[14]; }
    set { frmCgs.a[14] = value; }
  }

  public string TT16
  {
    get { return frmCgs.a[15]; }
    set { frmCgs.a[15] = value; }
  }

  public string TT17
  {
    get { return frmCgs.a[16 /*0x10*/]; }
    set { frmCgs.a[16 /*0x10*/] = value; }
  }

  public string TT18
  {
    get { return frmCgs.a[17]; }
    set { frmCgs.a[17] = value; }
  }

  public string TT19
  {
    get { return frmCgs.a[18]; }
    set { frmCgs.a[18] = value; }
  }

  public string TT20
  {
    get { return frmCgs.a[19]; }
    set { frmCgs.a[19] = value; }
  }

  public string TT21
  {
    get { return frmCgs.a[20]; }
    set { frmCgs.a[20] = value; }
  }

  public string TT22
  {
    get { return frmCgs.a[21]; }
    set { frmCgs.a[21] = value; }
  }

  public string TT23
  {
    get { return frmCgs.a[22]; }
    set { frmCgs.a[22] = value; }
  }

  public string TT24
  {
    get { return frmCgs.a[23]; }
    set { frmCgs.a[23] = value; }
  }

  public string TT25
  {
    get { return frmCgs.a[24]; }
    set { frmCgs.a[24] = value; }
  }

  public string TT26
  {
    get { return frmCgs.a[25]; }
    set { frmCgs.a[25] = value; }
  }

  private void btnOk_Click(object sender, EventArgs e)
  {
    int num1 = 0;
    frmTrainConfig.cgs_form_enter = true;
    string[] strArray = new string[27];
    num1 = 0;
    strArray[0] = this.t1.Text;
    strArray[1] = this.t2.Text;
    strArray[2] = this.t3.Text;
    strArray[3] = this.t4.Text;
    strArray[4] = this.t5.Text;
    strArray[5] = this.t6.Text;
    strArray[6] = this.t7.Text;
    strArray[7] = this.t8.Text;
    strArray[8] = this.t9.Text;
    strArray[9] = this.t10.Text;
    strArray[10] = this.t11.Text;
    strArray[11] = this.t12.Text;
    strArray[12] = this.t13.Text;
    strArray[13] = this.t14.Text;
    strArray[14] = this.t15.Text;
    strArray[15] = this.t16.Text;
    strArray[16 /*0x10*/] = this.t17.Text;
    strArray[17] = this.t18.Text;
    strArray[18] = this.t19.Text;
    strArray[19] = this.t20.Text;
    strArray[20] = this.t21.Text;
    strArray[21] = this.t22.Text;
    strArray[22] = this.t23.Text;
    strArray[23] = this.t24.Text;
    strArray[24] = this.t25.Text;
    strArray[25] = this.t26.Text;
    num1 = 0;
    try
    {
      frmCgs.a[0] = Operators.CompareString(frmCgs.a[0], "NULL", false) != 0 ? this.t1.Text : DBNull.Value.ToString();
      frmCgs.a[1] = Operators.CompareString(frmCgs.a[1], "NULL", false) != 0 ? this.t2.Text : DBNull.Value.ToString();
      frmCgs.a[2] = Operators.CompareString(frmCgs.a[2], "NULL", false) != 0 ? this.t3.Text : DBNull.Value.ToString();
      frmCgs.a[3] = Operators.CompareString(frmCgs.a[3], "NULL", false) != 0 ? this.t4.Text : DBNull.Value.ToString();
      frmCgs.a[4] = Operators.CompareString(frmCgs.a[4], "NULL", false) != 0 ? this.t5.Text : DBNull.Value.ToString();
      frmCgs.a[5] = Operators.CompareString(frmCgs.a[5], "NULL", false) != 0 ? this.t6.Text : DBNull.Value.ToString();
      frmCgs.a[6] = Operators.CompareString(frmCgs.a[6], "NULL", false) != 0 ? this.t7.Text : DBNull.Value.ToString();
      frmCgs.a[7] = Operators.CompareString(frmCgs.a[7], "NULL", false) != 0 ? this.t8.Text : DBNull.Value.ToString();
      frmCgs.a[8] = Operators.CompareString(frmCgs.a[8], "NULL", false) != 0 ? this.t9.Text : DBNull.Value.ToString();
      frmCgs.a[9] = Operators.CompareString(frmCgs.a[9], "NULL", false) != 0 ? this.t10.Text : DBNull.Value.ToString();
      frmCgs.a[10] = Operators.CompareString(frmCgs.a[10], "NULL", false) != 0 ? this.t11.Text : DBNull.Value.ToString();
      frmCgs.a[11] = Operators.CompareString(frmCgs.a[11], "NULL", false) != 0 ? this.t12.Text : DBNull.Value.ToString();
      frmCgs.a[12] = Operators.CompareString(frmCgs.a[12], "NULL", false) != 0 ? this.t13.Text : DBNull.Value.ToString();
      frmCgs.a[13] = Operators.CompareString(frmCgs.a[13], "NULL", false) != 0 ? this.t14.Text : DBNull.Value.ToString();
      frmCgs.a[14] = Operators.CompareString(frmCgs.a[14], "NULL", false) != 0 ? this.t15.Text : DBNull.Value.ToString();
      frmCgs.a[15] = Operators.CompareString(frmCgs.a[15], "NULL", false) != 0 ? this.t16.Text : DBNull.Value.ToString();
      frmCgs.a[16 /*0x10*/] = Operators.CompareString(frmCgs.a[16 /*0x10*/], "NULL", false) != 0 ? this.t17.Text : DBNull.Value.ToString();
      frmCgs.a[17] = Operators.CompareString(frmCgs.a[17], "NULL", false) != 0 ? this.t18.Text : DBNull.Value.ToString();
      frmCgs.a[18] = Operators.CompareString(frmCgs.a[18], "NULL", false) != 0 ? this.t19.Text : DBNull.Value.ToString();
      frmCgs.a[19] = Operators.CompareString(frmCgs.a[19], "NULL", false) != 0 ? this.t20.Text : DBNull.Value.ToString();
      frmCgs.a[20] = Operators.CompareString(frmCgs.a[20], "NULL", false) != 0 ? this.t21.Text : DBNull.Value.ToString();
      frmCgs.a[21] = Operators.CompareString(frmCgs.a[21], "NULL", false) != 0 ? this.t22.Text : DBNull.Value.ToString();
      frmCgs.a[22] = Operators.CompareString(frmCgs.a[22], "NULL", false) != 0 ? this.t23.Text : DBNull.Value.ToString();
      frmCgs.a[23] = Operators.CompareString(frmCgs.a[23], "NULL", false) != 0 ? this.t24.Text : DBNull.Value.ToString();
      frmCgs.a[24] = Operators.CompareString(frmCgs.a[24], "NULL", false) != 0 ? this.t25.Text : DBNull.Value.ToString();
      frmCgs.a[25] = Operators.CompareString(frmCgs.a[25], "NULL", false) != 0 ? this.t26.Text : DBNull.Value.ToString();
      this.Close();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num2 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void Button2_Click(object sender, EventArgs e)
  {
    int index = 0;
    while (index < 26)
    {
      frmCgs.a[index] = "";
      checked { ++index; }
    }
  }

  private void btnExit_Click(object sender, EventArgs e)
  {
    frmTrainConfig.cgs_form_enter = false;
    this.Close();
  }
}

}