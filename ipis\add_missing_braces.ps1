# PowerShell script to add missing closing braces to specific files

$filesToFix = @(
    "agdb_byte_construct.cs",
    "agdb_lookup_table.cs", 
    "connection_Database.cs",
    "common_def.cs",
    "defines.cs",
    "LinkCheckSetting.cs",
    "taddb_msg.cs",
    "WriteDisplayMessageStruct.cs",
    "frmDisplayBoardSettings.cs",
    "frmMdchCfgGetDisplay.cs",
    "frmPdchCfgGetDisplay.cs",
    "frmPortConfig.cs",
    "agdb_api.cs",
    "cgdb_api.cs",
    "Checksum.cs",
    "mdch_api.cs",
    "mldb_api.cs",
    "pdb_api.cs",
    "pdch_api.cs",
    "frm_CgsOnlineForm.cs",
    "frmAddMsg.cs",
    "frmAddPfno.cs",
    "frmCfgInt.cs",
    "frmCom.cs",
    "frmMainFormIPIS.cs",
    "frmAbout.cs",
    "frmContacts.cs",
    "frmHelp.cs",
    "intensity_Setting.cs",
    "frmFont.cs",
    "frmLanguage.cs",
    "Led_byte_Display.cs",
    "led_msg_display.cs",
    "Log_file.cs",
    "basMsgBoxEx.cs",
    "frmMsgBoxEx.cs",
    "frmNetworkAGDB.cs",
    "frmNetworkCGDB.cs",
    "frmNetworkMDCH.cs",
    "frmNetworkMLDB.cs",
    "frmNetworkPDB.cs",
    "frmNetworkPDCH.cs",
    "network_db_read.cs",
    "online_trains.cs",
    "pkt_construct.cs",
    "RS232.cs",
    "frmPlatformNo.cs",
    "frmStationCode.cs",
    "frmStationDetails.cs",
    "frmStationNameVoice.cs",
    "struct_file.cs",
    "frmCgs.cs",
    "frmTrainConfig.cs",
    "frmTrainDetails.cs",
    "frmTrainStatusMsg.cs",
    "frmTrainstatusPopup.cs",
    "frmTrainTimings.cs",
    "frmAddNewUser.cs",
    "frmChangeAccount.cs",
    "frmChangeAccountType.cs",
    "frmChangeAnotherUserPwd.cs",
    "frmChangeName.cs",
    "frmChangePassword.cs",
    "frmChangeUserDetails.cs",
    "frmChangeUserPwd.cs",
    "frmDeleteUser.cs",
    "frmLogin.cs",
    "frmPassword.cs",
    "frmUser.cs",
    "frmUserNamePassword.cs",
    "CCTV_NewForm.cs",
    "frmCCTV.cs",
    "frmSuryaLogo.cs",
    "frmAddTrainNameVoice.cs",
    "frmAddVoiceSplMsg.cs",
    "frmDeleteTrainNameVoice.cs",
    "frmRecordPlay.cs",
    "frmVoice.cs",
    "frmVoice_Special_Messages.cs",
    "voice_xml_files.cs"
)

Write-Host "Adding missing closing braces to specific files..."

foreach ($fileName in $filesToFix) {
    if (Test-Path $fileName) {
        Write-Host "Processing: $fileName"
        
        # Read the file content as lines
        $lines = Get-Content $fileName
        
        # Skip if file is empty
        if ($lines -eq $null -or $lines.Count -eq 0) {
            continue
        }
        
        # Add closing brace if the last line is not a closing brace
        $lastLine = $lines[$lines.Count - 1]
        if ($lastLine -notmatch '^\s*\}\s*$') {
            $lines += "}"
            Set-Content -Path $fileName -Value $lines
            Write-Host "  - Added missing closing brace"
        }
    }
}

Write-Host "Missing braces addition complete!"
