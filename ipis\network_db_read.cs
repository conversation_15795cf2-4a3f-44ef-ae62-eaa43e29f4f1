// Decompiled with JetBrains decompiler
// Type: ipis.network_db_read
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using ipis.My;
using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Data;
using System.Data.OleDb;
using System.IO;
using System.Text;
using System.Windows.Forms;

namespace ipis
{

public class network_db_read
{
  private const byte FAILURE = 0;
  private const byte SUCCESS = 1;
  private int cgdb_address_cnt;

  public network_db_read()
  {
    this.cgdb_address_cnt = 0;
  }

  public static void get_nw_database()
  {
    OleDbCommand oleDbCommand1 = new OleDbCommand();
    OleDbCommand oleDbCommand2 = new OleDbCommand();
    frmMainFormIPIS.mdch_db_struc_init();
    if (connection_Database.con2.State == ConnectionState.Closed && connection_Database.con2.State == ConnectionState.Closed)
      connection_Database.con2.Open();
    try
    {
      oleDbCommand1.CommandText = "select * from tbl_CCUMDCH";
      oleDbCommand1.CommandType = CommandType.Text;
      oleDbCommand1.Connection = connection_Database.con2;
      OleDbDataReader oleDbDataReader1 = oleDbCommand1.ExecuteReader();
      if (oleDbDataReader1.HasRows)
      {
        while (oleDbDataReader1.Read())
        {
          frmMainFormIPIS.mdch_db.mdch_name = Conversions.ToString(oleDbDataReader1[2]);
          frmMainFormIPIS.mdch_db.mdch_addr = Conversions.ToByte(oleDbDataReader1[3]);
        }
      }
      oleDbDataReader1.Close();
      oleDbCommand1.CommandText = "select * from tbl_MDCH";
      oleDbCommand1.CommandType = CommandType.Text;
      oleDbCommand1.Connection = connection_Database.con2;
      OleDbDataReader oleDbDataReader2 = oleDbCommand1.ExecuteReader();
      if (oleDbDataReader2.HasRows)
      {
        while (oleDbDataReader2.Read())
        {
          byte index1 = Conversions.ToByte(Microsoft.VisualBasic.CompilerServices.Operators.SubtractObject(oleDbDataReader2[0], (object) 1));
          byte index2 = Conversions.ToByte(Microsoft.VisualBasic.CompilerServices.Operators.SubtractObject(oleDbDataReader2[1], (object) 1));
          frmMainFormIPIS.mdch_db.port_status[(int) index1] = Conversions.ToBoolean(oleDbDataReader2[18]);
          frmMainFormIPIS.mdch_db.mdch_port[(int) index1].system_type[(int) index2] = Conversions.ToString(oleDbDataReader2[2]);
          checked { ++frmMainFormIPIS.mdch_db.mdch_port[(int) index1].no_of_systems; }
          if (Microsoft.VisualBasic.CompilerServices.Operators.ConditionalCompareObjectEqual(oleDbDataReader2[2], (object) "MLDB", false))
          {
            frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].dis_board_addr = Conversions.ToByte(oleDbDataReader2[3]);
            frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].dis_board_name = Conversions.ToString(oleDbDataReader2[4]);
            frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].dis_board_type = Conversions.ToString(oleDbDataReader2[2]);
            frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].line1effect = Conversions.ToByte(oleDbDataReader2[13]);
            frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].line2effect = Conversions.ToByte(oleDbDataReader2[14]);
            frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].line3effect = Conversions.ToByte(oleDbDataReader2[15]);
            frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].line4effect = Conversions.ToByte(oleDbDataReader2[16 /*0x10*/]);
            frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].line5effect = Conversions.ToByte(oleDbDataReader2[17]);
            frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].mldb_no_of_lines = Conversions.ToByte(oleDbDataReader2[12]);
            frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].mldb_type = Conversions.ToString(oleDbDataReader2[11]);
            frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].platform_no = Conversions.ToString(oleDbDataReader2[5]);
            frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].switching_time = Conversions.ToByte(oleDbDataReader2[9]);
            frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].video_type = Conversions.ToByte(oleDbDataReader2[10]);
            frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].effect_speed = oleDbDataReader2[21] != DBNull.Value ? Conversions.ToByte(oleDbDataReader2[21]) : (byte) 60;
          }
          else if (Microsoft.VisualBasic.CompilerServices.Operators.ConditionalCompareObjectEqual(oleDbDataReader2[2], (object) "AGDB", false))
          {
            frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].dis_board_addr = Conversions.ToByte(oleDbDataReader2[3]);
            frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].dis_board_name = Conversions.ToString(oleDbDataReader2[4]);
            frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].dis_board_type = Conversions.ToString(oleDbDataReader2[2]);
            frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].all_platfroms = Conversions.ToBoolean(oleDbDataReader2[8]);
            frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].platform_no = Conversions.ToString(oleDbDataReader2[5]);
            frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].shared_platform = Conversions.ToBoolean(oleDbDataReader2[6]);
            frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].shared_platform_no = Conversions.ToString(oleDbDataReader2[7]);
            frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].switching_time = Conversions.ToByte(oleDbDataReader2[9]);
            frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].video_type = Conversions.ToByte(oleDbDataReader2[10]);
            frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].agdb_sign = Conversions.ToByte(oleDbDataReader2[20]);
          }
          else if (Microsoft.VisualBasic.CompilerServices.Operators.ConditionalCompareObjectEqual(oleDbDataReader2[2], (object) "PDB", false))
          {
            frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].dis_board_addr = Conversions.ToByte(oleDbDataReader2[3]);
            frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].dis_board_name = Conversions.ToString(oleDbDataReader2[4]);
            frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].dis_board_type = Conversions.ToString(oleDbDataReader2[2]);
            frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].line1effect = Conversions.ToByte(oleDbDataReader2[13]);
            frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].platform_no = Conversions.ToString(oleDbDataReader2[5]);
            frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].shared_platform = Conversions.ToBoolean(oleDbDataReader2[6]);
            frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].shared_platform_no = Conversions.ToString(oleDbDataReader2[7]);
            frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].switching_time = Conversions.ToByte(oleDbDataReader2[9]);
            frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].video_type = Conversions.ToByte(oleDbDataReader2[10]);
            frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].multicast_addr = Conversions.ToByte(oleDbDataReader2[19]);
            frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].effect_speed = oleDbDataReader2[21] != DBNull.Value ? Conversions.ToByte(oleDbDataReader2[21]) : (byte) 60;
          }
          else
          {
            frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].pdch_addr = Conversions.ToByte(oleDbDataReader2[3]);
            frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].pdch_name = Conversions.ToString(oleDbDataReader2[4]);
            frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].platform_no = Conversions.ToString(oleDbDataReader2[5]);
            frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].shared_platform = Conversions.ToBoolean(oleDbDataReader2[6]);
            frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].shared_platform_no = Conversions.ToString(oleDbDataReader2[7]);
            oleDbCommand2.CommandText = "Select * from tbl_PDCH where MDCHPortno = {Conversions.ToString(checked ((int) index1 + 1))} and MDCHSerialNo = {Conversions.ToString(checked ((int) index2 + 1))} ";
            oleDbCommand2.CommandType = CommandType.Text;
            oleDbCommand2.Connection = connection_Database.con2;
            OleDbDataReader oleDbDataReader3 = oleDbCommand2.ExecuteReader();
            if (oleDbDataReader3.HasRows)
            {
              while (oleDbDataReader3.Read())
              {
                byte index3 = Conversions.ToByte(Microsoft.VisualBasic.CompilerServices.Operators.SubtractObject(oleDbDataReader3[2], (object) 1));
                byte index4 = Conversions.ToByte(Microsoft.VisualBasic.CompilerServices.Operators.SubtractObject(oleDbDataReader3[3], (object) 1));
                frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].port_status[(int) index3] = Conversions.ToBoolean(oleDbDataReader3[15]);
                checked { ++frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].pdch_port[(int) index3].no_of_systems; }
                if (Microsoft.VisualBasic.CompilerServices.Operators.ConditionalCompareObjectEqual(oleDbDataReader3[4], (object) "CGDB", false))
                {
                  frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].pdch_port[(int) index3].dis_board[(int) index4].dis_board_type = Conversions.ToString(oleDbDataReader3[4]);
                  frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].pdch_port[(int) index3].dis_board[(int) index4].dis_board_addr = Conversions.ToByte(oleDbDataReader3[5]);
                  frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].pdch_port[(int) index3].dis_board[(int) index4].dis_board_name = Conversions.ToString(oleDbDataReader3[6]);
                  frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].pdch_port[(int) index3].dis_board[(int) index4].platform_no = Conversions.ToString(oleDbDataReader3[7]);
                  frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].pdch_port[(int) index3].dis_board[(int) index4].switching_time = Conversions.ToByte(oleDbDataReader3[11]);
                  frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].pdch_port[(int) index3].dis_board[(int) index4].video_type = Conversions.ToByte(oleDbDataReader3[12]);
                  frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].pdch_port[(int) index3].dis_board[(int) index4].cgdb_direction = Conversions.ToString(oleDbDataReader3[13]);
                  frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].pdch_port[(int) index3].dis_board[(int) index4].multicast_addr = Conversions.ToByte(oleDbDataReader3[16 /*0x10*/]);
                }
                else if (Microsoft.VisualBasic.CompilerServices.Operators.ConditionalCompareObjectEqual(oleDbDataReader3[4], (object) "PDB", false))
                {
                  frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].pdch_port[(int) index3].dis_board[(int) index4].dis_board_type = Conversions.ToString(oleDbDataReader3[4]);
                  frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].pdch_port[(int) index3].dis_board[(int) index4].dis_board_addr = Conversions.ToByte(oleDbDataReader3[5]);
                  frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].pdch_port[(int) index3].dis_board[(int) index4].dis_board_name = Conversions.ToString(oleDbDataReader3[6]);
                  frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].pdch_port[(int) index3].dis_board[(int) index4].platform_no = Conversions.ToString(oleDbDataReader3[7]);
                  frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].pdch_port[(int) index3].dis_board[(int) index4].shared_platform = Conversions.ToBoolean(oleDbDataReader3[8]);
                  frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].pdch_port[(int) index3].dis_board[(int) index4].shared_platform_no = Conversions.ToString(oleDbDataReader3[9]);
                  frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].pdch_port[(int) index3].dis_board[(int) index4].switching_time = Conversions.ToByte(oleDbDataReader3[11]);
                  frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].pdch_port[(int) index3].dis_board[(int) index4].video_type = Conversions.ToByte(oleDbDataReader3[12]);
                  frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].pdch_port[(int) index3].dis_board[(int) index4].line1effect = Conversions.ToByte(oleDbDataReader3[14]);
                  frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].pdch_port[(int) index3].dis_board[(int) index4].multicast_addr = Conversions.ToByte(oleDbDataReader3[16 /*0x10*/]);
                  frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].pdch_port[(int) index3].dis_board[(int) index4].effect_speed = oleDbDataReader3[18] != DBNull.Value ? Conversions.ToByte(oleDbDataReader3[18]) : (byte) 60;
                }
                else if (Microsoft.VisualBasic.CompilerServices.Operators.ConditionalCompareObjectEqual(oleDbDataReader3[4], (object) "AGDB", false))
                {
                  frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].pdch_port[(int) index3].dis_board[(int) index4].dis_board_type = Conversions.ToString(oleDbDataReader3[4]);
                  frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].pdch_port[(int) index3].dis_board[(int) index4].dis_board_addr = Conversions.ToByte(oleDbDataReader3[5]);
                  frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].pdch_port[(int) index3].dis_board[(int) index4].dis_board_name = Conversions.ToString(oleDbDataReader3[6]);
                  frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].pdch_port[(int) index3].dis_board[(int) index4].platform_no = Conversions.ToString(oleDbDataReader3[7]);
                  frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].pdch_port[(int) index3].dis_board[(int) index4].shared_platform = Conversions.ToBoolean(oleDbDataReader3[8]);
                  frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].pdch_port[(int) index3].dis_board[(int) index4].shared_platform_no = Conversions.ToString(oleDbDataReader3[9]);
                  frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].pdch_port[(int) index3].dis_board[(int) index4].all_platfroms = Conversions.ToBoolean(oleDbDataReader3[10]);
                  frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].pdch_port[(int) index3].dis_board[(int) index4].switching_time = Conversions.ToByte(oleDbDataReader3[11]);
                  frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].pdch_port[(int) index3].dis_board[(int) index4].video_type = Conversions.ToByte(oleDbDataReader3[12]);
                  frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].pdch_port[(int) index3].dis_board[(int) index4].agdb_sign = Conversions.ToByte(oleDbDataReader3[17]);
                }
              }
            }
            oleDbDataReader3.Close();
          }
        }
      }
      oleDbDataReader2.Close();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con2.State != ConnectionState.Open)
      return;
    connection_Database.con2.Close();
  }

  public static void set_nw_database()
  {
    OleDbDataAdapter oleDbDataAdapter1 = new OleDbDataAdapter();
    OleDbCommand oleDbCommand1 = new OleDbCommand();
    OleDbCommand oleDbCommand2 = new OleDbCommand();
    OleDbDataAdapter oleDbDataAdapter2 = new OleDbDataAdapter();
    OleDbCommand oleDbCommand3 = new OleDbCommand();
    OleDbDataAdapter oleDbDataAdapter3 = new OleDbDataAdapter();
    OleDbCommand oleDbCommand4 = new OleDbCommand();
    if (connection_Database.con2.State == ConnectionState.Closed)
      connection_Database.con2.Open();
    try
    {
      new OleDbCommand("delete * from tbl_CCUMDCH", connection_Database.con2).ExecuteNonQuery();
      new OleDbCommand("delete * from tbl_MDCH", connection_Database.con2).ExecuteNonQuery();
      new OleDbCommand("delete * from tbl_PDCH", connection_Database.con2).ExecuteNonQuery();
      oleDbCommand1.CommandText = "insert into tbl_CCUMDCH(CCUName,CCUAddress,MDCHName,MDCHAddress) values ('{frmMainFormIPIS.ccu_name}','{Conversions.ToString(frmMainFormIPIS.ccu_addr)}','{frmMainFormIPIS.mdch_db.mdch_name}','{Conversions.ToString(frmMainFormIPIS.mdch_db.mdch_addr)}') ";
      oleDbCommand1.Connection = connection_Database.con2;
      oleDbDataAdapter1.InsertCommand = oleDbCommand1;
      oleDbDataAdapter1.InsertCommand.ExecuteNonQuery();
      int index1 = 0;
      while (index1 < 16 /*0x10*/)
      {
        if (frmMainFormIPIS.mdch_db.port_status[index1])
        {
          int index2 = 0;
          while (index2 < (int) frmMainFormIPIS.mdch_db.mdch_port[index1].no_of_systems)
          {
            if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.mdch_db.mdch_port[index1].system_type[index2], "PDCH", false) != 0)
              oleDbCommand2.CommandText = " (insert into tbl_MDCH(PortNo, SerialNo, SystemType, SystemAddress, SystemName, PlatformNo, SharedPlatform, SharedPlatformNo, AllPlatforms, SwitchingTime, MLDBType, MLDBNoOfLines, Status,VideoType,Line1Effect,Line2Effect,Line3Effect,Line4Effect,Line5Effect,MultiCastAddress,AgdbSign,EffectSpeed) values({Conversions.ToString(checked (index1 + 1))}, {Conversions.ToString(checked (index2 + 1))}, '{frmMainFormIPIS.mdch_db.mdch_port[index1].system_type[index2]}', {Conversions.ToString(frmMainFormIPIS.mdch_db.mdch_port[index1].dis_board[index2].dis_board_addr)}, '{frmMainFormIPIS.mdch_db.mdch_port[index1].dis_board[index2].dis_board_name}', '{frmMainFormIPIS.mdch_db.mdch_port[index1].dis_board[index2].platform_no}', {Conversions.ToString(frmMainFormIPIS.mdch_db.mdch_port[index1].dis_board[index2].shared_platform)}, '{frmMainFormIPIS.mdch_db.mdch_port[index1].dis_board[index2].shared_platform_no}', {Conversions.ToString(frmMainFormIPIS.mdch_db.mdch_port[index1].dis_board[index2].all_platfroms)}, {Conversions.ToString(frmMainFormIPIS.mdch_db.mdch_port[index1].dis_board[index2].switching_time)}, '{frmMainFormIPIS.mdch_db.mdch_port[index1].dis_board[index2].mldb_type}', {Conversions.ToString(frmMainFormIPIS.mdch_db.mdch_port[index1].dis_board[index2].mldb_no_of_lines)}, {Conversions.ToString(frmMainFormIPIS.mdch_db.port_status[index1])},{Conversions.ToString(frmMainFormIPIS.mdch_db.mdch_port[index1].dis_board[index2].video_type)},{Conversions.ToString(frmMainFormIPIS.mdch_db.mdch_port[index1].dis_board[index2].line1effect)},{Conversions.ToString(frmMainFormIPIS.mdch_db.mdch_port[index1].dis_board[index2].line2effect)},{Conversions.ToString(frmMainFormIPIS.mdch_db.mdch_port[index1].dis_board[index2].line3effect)},{Conversions.ToString(frmMainFormIPIS.mdch_db.mdch_port[index1].dis_board[index2].line4effect)} , {Conversions.ToString(frmMainFormIPIS.mdch_db.mdch_port[index1].dis_board[index2].line5effect)}, {Conversions.ToString(frmMainFormIPIS.mdch_db.mdch_port[index1].dis_board[index2].multicast_addr)} , {Conversions.ToString(frmMainFormIPIS.mdch_db.mdch_port[index1].dis_board[index2].agdb_sign)}, {Conversions.ToString(frmMainFormIPIS.mdch_db.mdch_port[index1].dis_board[index2].effect_speed)}))";
            else
              oleDbCommand2.CommandText = " (insert into tbl_MDCH(PortNo, SerialNo, SystemType, SystemAddress, SystemName, PlatformNo, SharedPlatform, SharedPlatformNo, Status,MultiCastAddress,AgdbSign) values({Conversions.ToString(checked (index1 + 1))}, {Conversions.ToString(checked (index2 + 1))}, '{frmMainFormIPIS.mdch_db.mdch_port[index1].system_type[index2]}', {Conversions.ToString(frmMainFormIPIS.mdch_db.mdch_port[index1].pdch[index2].pdch_addr)}, '{frmMainFormIPIS.mdch_db.mdch_port[index1].pdch[index2].pdch_name}', '{frmMainFormIPIS.mdch_db.mdch_port[index1].pdch[index2].platform_no}', {Conversions.ToString(frmMainFormIPIS.mdch_db.mdch_port[index1].pdch[index2].shared_platform)}, '{frmMainFormIPIS.mdch_db.mdch_port[index1].pdch[index2].shared_platform_no}', {Conversions.ToString(frmMainFormIPIS.mdch_db.port_status[index1])},{Conversions.ToString(frmMainFormIPIS.mdch_db.mdch_port[index1].pdch[index2].multicast_addr)},{Conversions.ToString(frmMainFormIPIS.mdch_db.mdch_port[index1].pdch[index2].agdb_sign)}))";
            oleDbCommand2.Connection = connection_Database.con2;
            oleDbDataAdapter2.InsertCommand = oleDbCommand2;
            oleDbDataAdapter2.InsertCommand.ExecuteNonQuery();
            checked { ++index2; }
          }
        }
        checked { ++index1; }
      }
      int index3 = 0;
      while (index3 < 16 /*0x10*/)
      {
        if (frmMainFormIPIS.mdch_db.port_status[index3])
        {
          int index4 = 0;
          while (index4 < (int) frmMainFormIPIS.mdch_db.mdch_port[index3].no_of_systems)
          {
            if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.mdch_db.mdch_port[index3].system_type[index4], "PDCH", false) == 0)
            {
              int index5 = 0;
              while (index5 < 16 /*0x10*/)
              {
                if (frmMainFormIPIS.mdch_db.mdch_port[index3].pdch[index4].port_status[index5])
                {
                  int index6 = 0;
                  while (index6 < (int) frmMainFormIPIS.mdch_db.mdch_port[index3].pdch[index4].pdch_port[index5].no_of_systems)
                  {
                    oleDbCommand3.CommandText = "(insert into tbl_PDCH(MDCHPortNo, MDCHSerialNo, PortNo, SerialNo, SystemType, SystemAddress, SystemName, PlatformNo, SharedPlatform, SharedPlatformNo, AllPlatforms, SwitchingTime, Direction, Status,VideoType, Effect,MultiCastAddress,AgdbSign,EffectSpeed ) values ({Conversions.ToString(checked (index3 + 1))}, {Conversions.ToString(checked (index4 + 1))}, {Conversions.ToString(checked (index5 + 1))}, {Conversions.ToString(checked (index6 + 1))}, '{frmMainFormIPIS.mdch_db.mdch_port[index3].pdch[index4].pdch_port[index5].dis_board[index6].dis_board_type}', {Conversions.ToString(frmMainFormIPIS.mdch_db.mdch_port[index3].pdch[index4].pdch_port[index5].dis_board[index6].dis_board_addr)}, '{frmMainFormIPIS.mdch_db.mdch_port[index3].pdch[index4].pdch_port[index5].dis_board[index6].dis_board_name}',' {frmMainFormIPIS.mdch_db.mdch_port[index3].pdch[index4].pdch_port[index5].dis_board[index6].platform_no}', {Conversions.ToString(frmMainFormIPIS.mdch_db.mdch_port[index3].pdch[index4].pdch_port[index5].dis_board[index6].shared_platform)},' {frmMainFormIPIS.mdch_db.mdch_port[index3].pdch[index4].pdch_port[index5].dis_board[index6].shared_platform_no}', {Conversions.ToString(frmMainFormIPIS.mdch_db.mdch_port[index3].pdch[index4].pdch_port[index5].dis_board[index6].all_platfroms)}, {Conversions.ToString(frmMainFormIPIS.mdch_db.mdch_port[index3].pdch[index4].pdch_port[index5].dis_board[index6].switching_time)} , '{frmMainFormIPIS.mdch_db.mdch_port[index3].pdch[index4].pdch_port[index5].dis_board[index6].cgdb_direction}', {Conversions.ToString(frmMainFormIPIS.mdch_db.mdch_port[index3].pdch[index4].port_status[index5])},{Conversions.ToString(frmMainFormIPIS.mdch_db.mdch_port[index3].pdch[index4].pdch_port[index5].dis_board[index6].video_type)},{Conversions.ToString(frmMainFormIPIS.mdch_db.mdch_port[index3].pdch[index4].pdch_port[index5].dis_board[index6].line1effect)}, {Conversions.ToString(frmMainFormIPIS.mdch_db.mdch_port[index3].pdch[index4].pdch_port[index5].dis_board[index6].multicast_addr)} ,{Conversions.ToString(frmMainFormIPIS.mdch_db.mdch_port[index3].pdch[index4].pdch_port[index5].dis_board[index6].agdb_sign)},{Conversions.ToString(frmMainFormIPIS.mdch_db.mdch_port[index3].pdch[index4].pdch_port[index5].dis_board[index6].effect_speed)})) ";
                    oleDbCommand3.Connection = connection_Database.con2;
                    oleDbDataAdapter3.InsertCommand = oleDbCommand3;
                    oleDbDataAdapter3.InsertCommand.ExecuteNonQuery();
                    checked { ++index6; }
                  }
                }
                checked { ++index5; }
              }
            }
            checked { ++index4; }
          }
        }
        checked { ++index3; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con2.State == ConnectionState.Open)
      connection_Database.con2.Close();
    frmMainFormIPIS.mdch_db_struc_init();
    // TODO: Implement missing methods in taddb_msg class
    // taddb_msg.mldb_dis_brd_init();
    // taddb_msg.pdb_dis_brd_init();
    // taddb_msg.agdb_dis_brd_init();
    cgdb_dis.cgdb_dis_brd_init();
    frmMainFormIPIS.hub_init();
    network_db_read.get_nw_database();
    network_db_read.get_mldb_dis_brd_info();
    network_db_read.get_pdb_dis_brd_info();
    network_db_read.get_agdb_dis_brd_info();
    network_db_read.get_cgdb_dis_brd_info();
    network_db_read.get_mdch_port_info();
    network_db_read.get_pdch_port_info();
    try
    {
      string str = "Z:\\Database\\network.mdb";
      string sourceFileName = "C:\\IPIS\\Database\\network.mdb";
      if (!File.Exists(str))
        File.Create(str);
      bool overwrite = true;
      MyProject.Computer.FileSystem.CopyFile(sourceFileName, str, overwrite);
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
  }

  public static void get_ccu_info()
  {
    OleDbCommand oleDbCommand = new OleDbCommand();
    if (connection_Database.con2.State == ConnectionState.Closed)
      connection_Database.con2.Open();
    try
    {
      oleDbCommand.CommandText = "select * from tbl_CCUMDCH";
      oleDbCommand.CommandType = CommandType.Text;
      oleDbCommand.Connection = connection_Database.con2;
      OleDbDataReader oleDbDataReader = oleDbCommand.ExecuteReader();
      while (oleDbDataReader.Read())
      {
        frmMainFormIPIS.ccu_addr = Conversions.ToByte(oleDbDataReader[1]);
        frmMainFormIPIS.ccu_name = Conversions.ToString(oleDbDataReader[0]);
      }
      oleDbDataReader.Close();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con2.State != ConnectionState.Open)
      return;
    connection_Database.con2.Close();
  }

  public static int get_Live_count(string s)
  {
    OleDbCommand oleDbCommand = new OleDbCommand();
    if (connection_Database.con14.State == ConnectionState.Closed)
      connection_Database.con14.Open();
    int liveCount = 0;
    try
    {
      oleDbCommand.CommandText = "select count(*) from live where request='{s}'";
      oleDbCommand.CommandType = CommandType.Text;
      oleDbCommand.Connection = connection_Database.con14;
      OleDbDataReader oleDbDataReader = oleDbCommand.ExecuteReader();
      int integer = 0;
      while (oleDbDataReader.Read())
        integer = Conversions.ToInteger(oleDbDataReader[0]);
      liveCount = integer;
      goto label_11;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con14.State == ConnectionState.Open)
      connection_Database.con14.Close();
label_11:
    return liveCount;
  }

  public static string get_Live_info(string s)
  {
    OleDbCommand oleDbCommand = new OleDbCommand();
    if (connection_Database.con14.State == ConnectionState.Closed)
      connection_Database.con14.Open();
    string reqData = "";
    try
    {
      oleDbCommand.CommandText = "select * from live where request='{s}'";
      oleDbCommand.CommandType = CommandType.Text;
      oleDbCommand.Connection = connection_Database.con14;
      OleDbDataReader oleDbDataReader = oleDbCommand.ExecuteReader();
      while (oleDbDataReader.Read())
        frmMainFormIPIS.req_data = Conversions.ToString(oleDbDataReader[2]);
      reqData = frmMainFormIPIS.req_data;
      goto label_11;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con14.State == ConnectionState.Open)
      connection_Database.con14.Close();
label_11:
    return reqData;
  }

  public static void get_mldb_dis_brd_info()
  {
    try
    {
      int index1 = 0;
      taddb_msg.mldb_dis_brd.no_of_mldbs = (byte) 0;
      while (index1 < 16 /*0x10*/)
      {
        if (frmMainFormIPIS.mdch_db.port_status[index1])
        {
          int index2 = 0;
          while (index2 < (int) frmMainFormIPIS.mdch_db.mdch_port[index1].no_of_systems)
          {
            if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.mdch_db.mdch_port[index1].system_type[index2], "MLDB", false) == 0)
            {
              taddb_msg.mldb_dis_brd.mdlb[(int) taddb_msg.mldb_dis_brd.no_of_mldbs].mldb_addr = frmMainFormIPIS.mdch_db.mdch_port[index1].dis_board[index2].dis_board_addr;
              taddb_msg.mldb_dis_brd.mdlb[(int) taddb_msg.mldb_dis_brd.no_of_mldbs].mldb_name = frmMainFormIPIS.mdch_db.mdch_port[index1].dis_board[index2].dis_board_name;
              taddb_msg.mldb_dis_brd.mdlb[(int) taddb_msg.mldb_dis_brd.no_of_mldbs].mldb_type = frmMainFormIPIS.mdch_db.mdch_port[index1].dis_board[index2].mldb_type;
              taddb_msg.mldb_dis_brd.mdlb[(int) taddb_msg.mldb_dis_brd.no_of_mldbs].no_of_lines = frmMainFormIPIS.mdch_db.mdch_port[index1].dis_board[index2].mldb_no_of_lines;
              taddb_msg.mldb_dis_brd.mdlb[(int) taddb_msg.mldb_dis_brd.no_of_mldbs].swithcing_time = frmMainFormIPIS.mdch_db.mdch_port[index1].dis_board[index2].switching_time;
              taddb_msg.mldb_dis_brd.mdlb[(int) taddb_msg.mldb_dis_brd.no_of_mldbs].video_type = frmMainFormIPIS.mdch_db.mdch_port[index1].dis_board[index2].video_type;
              taddb_msg.mldb_dis_brd.mdlb[(int) taddb_msg.mldb_dis_brd.no_of_mldbs].effect[0] = frmMainFormIPIS.mdch_db.mdch_port[index1].dis_board[index2].line1effect;
              taddb_msg.mldb_dis_brd.mdlb[(int) taddb_msg.mldb_dis_brd.no_of_mldbs].effect[1] = frmMainFormIPIS.mdch_db.mdch_port[index1].dis_board[index2].line2effect;
              taddb_msg.mldb_dis_brd.mdlb[(int) taddb_msg.mldb_dis_brd.no_of_mldbs].effect[2] = frmMainFormIPIS.mdch_db.mdch_port[index1].dis_board[index2].line3effect;
              taddb_msg.mldb_dis_brd.mdlb[(int) taddb_msg.mldb_dis_brd.no_of_mldbs].effect[3] = frmMainFormIPIS.mdch_db.mdch_port[index1].dis_board[index2].line4effect;
              taddb_msg.mldb_dis_brd.mdlb[(int) taddb_msg.mldb_dis_brd.no_of_mldbs].effect[4] = frmMainFormIPIS.mdch_db.mdch_port[index1].dis_board[index2].line5effect;
              taddb_msg.mldb_dis_brd.mdlb[(int) taddb_msg.mldb_dis_brd.no_of_mldbs].effect_speed = frmMainFormIPIS.mdch_db.mdch_port[index1].dis_board[index2].effect_speed;
              checked { ++taddb_msg.mldb_dis_brd.no_of_mldbs; }
            }
            checked { ++index2; }
            if (index2 == 4)
              break;
          }
        }
        checked { ++index1; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  public static void get_pdb_dis_brd_info()
  {
    try
    {
      int index1 = 0;
      while (index1 < 25)
      {
        taddb_msg.no_of_pdbs_pfno[index1] = (byte) 0;
        checked { ++index1; }
      }
      int index2 = 0;
      while (index2 < 16 /*0x10*/)
      {
        if (frmMainFormIPIS.mdch_db.port_status[index2])
        {
          int index3 = 0;
          while (index3 < (int) frmMainFormIPIS.mdch_db.mdch_port[index2].no_of_systems)
          {
            if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.mdch_db.mdch_port[index2].system_type[index3], "PDB", false) == 0)
            {
              byte pfnoInt1 = network_db_read.get_pfno_int(frmMainFormIPIS.mdch_db.mdch_port[index2].dis_board[index3].platform_no);
              if (pfnoInt1 != (byte) 0)
                checked { --pfnoInt1; }
              taddb_msg.pdb_pf_status[(int) pfnoInt1] = true;
              taddb_msg.pdb_dis_brd[(int) pfnoInt1, (int) taddb_msg.no_of_pdbs_pfno[(int) pfnoInt1]].pdb_addr = frmMainFormIPIS.mdch_db.mdch_port[index2].dis_board[index3].dis_board_addr;
              taddb_msg.pdb_dis_brd[(int) pfnoInt1, (int) taddb_msg.no_of_pdbs_pfno[(int) pfnoInt1]].pdb_name = frmMainFormIPIS.mdch_db.mdch_port[index2].dis_board[index3].dis_board_name;
              taddb_msg.pdb_dis_brd[(int) pfnoInt1, (int) taddb_msg.no_of_pdbs_pfno[(int) pfnoInt1]].swithcing_time = frmMainFormIPIS.mdch_db.mdch_port[index2].dis_board[index3].switching_time;
              taddb_msg.pdb_dis_brd[(int) pfnoInt1, (int) taddb_msg.no_of_pdbs_pfno[(int) pfnoInt1]].video_type = frmMainFormIPIS.mdch_db.mdch_port[index2].dis_board[index3].video_type;
              taddb_msg.pdb_dis_brd[(int) pfnoInt1, (int) taddb_msg.no_of_pdbs_pfno[(int) pfnoInt1]].effect = frmMainFormIPIS.mdch_db.mdch_port[index2].dis_board[index3].line1effect;
              taddb_msg.pdb_dis_brd[(int) pfnoInt1, (int) taddb_msg.no_of_pdbs_pfno[(int) pfnoInt1]].effect_speed = frmMainFormIPIS.mdch_db.mdch_port[index2].dis_board[index3].effect_speed;
              taddb_msg.pdb_dis_brd[(int) pfnoInt1, (int) taddb_msg.no_of_pdbs_pfno[(int) pfnoInt1]].multicast_addr = frmMainFormIPIS.mdch_db.mdch_port[index2].dis_board[index3].multicast_addr;
              if (frmMainFormIPIS.mdch_db.mdch_port[index2].dis_board[index3].shared_platform)
              {
                taddb_msg.pdb_dis_brd[(int) pfnoInt1, (int) taddb_msg.no_of_pdbs_pfno[(int) pfnoInt1]].shared_platform = true;
                taddb_msg.pdb_dis_brd[(int) pfnoInt1, (int) taddb_msg.no_of_pdbs_pfno[(int) pfnoInt1]].shared_platform_no = frmMainFormIPIS.mdch_db.mdch_port[index2].dis_board[index3].shared_platform_no;
              }
              taddb_msg.no_of_pdbs_pfno[(int) pfnoInt1] = checked ((byte) ((int) taddb_msg.no_of_pdbs_pfno[(int) pfnoInt1] + 1));
              // TODO: Implement missing property total_no_pdbs in taddb_msg class
              // checked { ++taddb_msg.total_no_pdbs; }
              if (frmMainFormIPIS.mdch_db.mdch_port[index2].dis_board[index3].shared_platform)
              {
                byte pfnoInt2 = network_db_read.get_pfno_int(frmMainFormIPIS.mdch_db.mdch_port[index2].dis_board[index3].shared_platform_no);
                if (pfnoInt2 != (byte) 0)
                  checked { --pfnoInt2; }
                taddb_msg.pdb_pf_status[(int) pfnoInt2] = true;
                taddb_msg.pdb_dis_brd[(int) pfnoInt2, (int) taddb_msg.no_of_pdbs_pfno[(int) pfnoInt2]].pdb_addr = frmMainFormIPIS.mdch_db.mdch_port[index2].dis_board[index3].dis_board_addr;
                taddb_msg.pdb_dis_brd[(int) pfnoInt2, (int) taddb_msg.no_of_pdbs_pfno[(int) pfnoInt2]].pdb_name = frmMainFormIPIS.mdch_db.mdch_port[index2].dis_board[index3].dis_board_name;
                taddb_msg.pdb_dis_brd[(int) pfnoInt2, (int) taddb_msg.no_of_pdbs_pfno[(int) pfnoInt2]].swithcing_time = frmMainFormIPIS.mdch_db.mdch_port[index2].dis_board[index3].switching_time;
                taddb_msg.pdb_dis_brd[(int) pfnoInt2, (int) taddb_msg.no_of_pdbs_pfno[(int) pfnoInt2]].video_type = frmMainFormIPIS.mdch_db.mdch_port[index2].dis_board[index3].video_type;
                taddb_msg.pdb_dis_brd[(int) pfnoInt2, (int) taddb_msg.no_of_pdbs_pfno[(int) pfnoInt2]].effect = frmMainFormIPIS.mdch_db.mdch_port[index2].dis_board[index3].line1effect;
                taddb_msg.pdb_dis_brd[(int) pfnoInt2, (int) taddb_msg.no_of_pdbs_pfno[(int) pfnoInt2]].effect_speed = frmMainFormIPIS.mdch_db.mdch_port[index2].dis_board[index3].effect_speed;
                taddb_msg.pdb_dis_brd[(int) pfnoInt2, (int) taddb_msg.no_of_pdbs_pfno[(int) pfnoInt2]].shared_platform = true;
                taddb_msg.pdb_dis_brd[(int) pfnoInt2, (int) taddb_msg.no_of_pdbs_pfno[(int) pfnoInt2]].shared_platform_no = frmMainFormIPIS.mdch_db.mdch_port[index2].dis_board[index3].platform_no;
                taddb_msg.pdb_dis_brd[(int) pfnoInt2, (int) taddb_msg.no_of_pdbs_pfno[(int) pfnoInt2]].multicast_addr = frmMainFormIPIS.mdch_db.mdch_port[index2].dis_board[index3].multicast_addr;
                taddb_msg.no_of_pdbs_pfno[(int) pfnoInt2] = checked ((byte) ((int) taddb_msg.no_of_pdbs_pfno[(int) pfnoInt2] + 1));
              }
            }
            else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.mdch_db.mdch_port[index2].system_type[index3], "PDCH", false) == 0)
            {
              int index4 = 0;
              while (index4 < 16 /*0x10*/)
              {
                if (frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].port_status[index4])
                {
                  int index5 = 0;
                  while (index5 < (int) frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_port[index4].no_of_systems)
                  {
                    if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_port[index4].dis_board[index5].dis_board_type, "PDB", false) == 0)
                    {
                      byte pfnoInt3 = network_db_read.get_pfno_int(frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_port[index4].dis_board[index5].platform_no);
                      if (pfnoInt3 != (byte) 0)
                        checked { --pfnoInt3; }
                      taddb_msg.pdb_pf_status[(int) pfnoInt3] = true;
                      taddb_msg.pdb_dis_brd[(int) pfnoInt3, (int) taddb_msg.no_of_pdbs_pfno[(int) pfnoInt3]].pdb_addr = frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_port[index4].dis_board[index5].dis_board_addr;
                      taddb_msg.pdb_dis_brd[(int) pfnoInt3, (int) taddb_msg.no_of_pdbs_pfno[(int) pfnoInt3]].pdb_name = frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_port[index4].dis_board[index5].dis_board_name;
                      taddb_msg.pdb_dis_brd[(int) pfnoInt3, (int) taddb_msg.no_of_pdbs_pfno[(int) pfnoInt3]].swithcing_time = frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_port[index4].dis_board[index5].switching_time;
                      taddb_msg.pdb_dis_brd[(int) pfnoInt3, (int) taddb_msg.no_of_pdbs_pfno[(int) pfnoInt3]].video_type = frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_port[index4].dis_board[index5].video_type;
                      taddb_msg.pdb_dis_brd[(int) pfnoInt3, (int) taddb_msg.no_of_pdbs_pfno[(int) pfnoInt3]].effect = frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_port[index4].dis_board[index5].line1effect;
                      taddb_msg.pdb_dis_brd[(int) pfnoInt3, (int) taddb_msg.no_of_pdbs_pfno[(int) pfnoInt3]].effect_speed = frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_port[index4].dis_board[index5].effect_speed;
                      taddb_msg.pdb_dis_brd[(int) pfnoInt3, (int) taddb_msg.no_of_pdbs_pfno[(int) pfnoInt3]].multicast_addr = frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_port[index4].dis_board[index5].multicast_addr;
                      if (frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_port[index4].dis_board[index5].shared_platform)
                      {
                        taddb_msg.pdb_dis_brd[(int) pfnoInt3, (int) taddb_msg.no_of_pdbs_pfno[(int) pfnoInt3]].shared_platform = true;
                        taddb_msg.pdb_dis_brd[(int) pfnoInt3, (int) taddb_msg.no_of_pdbs_pfno[(int) pfnoInt3]].shared_platform_no = frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_port[index4].dis_board[index5].shared_platform_no;
                      }
                      taddb_msg.no_of_pdbs_pfno[(int) pfnoInt3] = checked ((byte) ((int) taddb_msg.no_of_pdbs_pfno[(int) pfnoInt3] + 1));
                      // TODO: Implement missing property total_no_pdbs in taddb_msg class
                      // checked { ++taddb_msg.total_no_pdbs; }
                      if (frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_port[index4].dis_board[index5].shared_platform)
                      {
                        byte pfnoInt4 = network_db_read.get_pfno_int(frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_port[index4].dis_board[index5].shared_platform_no);
                        if (pfnoInt4 != (byte) 0)
                          checked { --pfnoInt4; }
                        taddb_msg.pdb_pf_status[(int) pfnoInt4] = true;
                        taddb_msg.pdb_dis_brd[(int) pfnoInt4, (int) taddb_msg.no_of_pdbs_pfno[(int) pfnoInt4]].pdb_addr = frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_port[index4].dis_board[index5].dis_board_addr;
                        taddb_msg.pdb_dis_brd[(int) pfnoInt4, (int) taddb_msg.no_of_pdbs_pfno[(int) pfnoInt4]].pdb_name = frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_port[index4].dis_board[index5].dis_board_name;
                        taddb_msg.pdb_dis_brd[(int) pfnoInt4, (int) taddb_msg.no_of_pdbs_pfno[(int) pfnoInt4]].swithcing_time = frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_port[index4].dis_board[index5].switching_time;
                        taddb_msg.pdb_dis_brd[(int) pfnoInt4, (int) taddb_msg.no_of_pdbs_pfno[(int) pfnoInt4]].video_type = frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_port[index4].dis_board[index5].video_type;
                        taddb_msg.pdb_dis_brd[(int) pfnoInt4, (int) taddb_msg.no_of_pdbs_pfno[(int) pfnoInt4]].effect = frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_port[index4].dis_board[index5].line1effect;
                        taddb_msg.pdb_dis_brd[(int) pfnoInt4, (int) taddb_msg.no_of_pdbs_pfno[(int) pfnoInt4]].effect_speed = frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_port[index4].dis_board[index5].effect_speed;
                        taddb_msg.pdb_dis_brd[(int) pfnoInt4, (int) taddb_msg.no_of_pdbs_pfno[(int) pfnoInt4]].shared_platform = true;
                        taddb_msg.pdb_dis_brd[(int) pfnoInt4, (int) taddb_msg.no_of_pdbs_pfno[(int) pfnoInt4]].shared_platform_no = frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_port[index4].dis_board[index5].platform_no;
                        taddb_msg.pdb_dis_brd[(int) pfnoInt4, (int) taddb_msg.no_of_pdbs_pfno[(int) pfnoInt4]].multicast_addr = frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_port[index4].dis_board[index5].multicast_addr;
                        taddb_msg.no_of_pdbs_pfno[(int) pfnoInt4] = checked ((byte) ((int) taddb_msg.no_of_pdbs_pfno[(int) pfnoInt4] + 1));
                      }
                    }
                    checked { ++index5; }
                    if (index5 == 8)
                      break;
                  }
                }
                checked { ++index4; }
              }
            }
            checked { ++index3; }
            if (index3 == 4)
              break;
          }
        }
        checked { ++index2; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  public static void get_agdb_dis_brd_info()
  {
    // TODO: Fix type conversion - no_of_com_agdbs appears to be byte[] but being assigned byte
    // taddb_msg.no_of_com_agdbs = (byte) 0;
    try
    {
      int index1 = 0;
      while (index1 < 25)
      {
        taddb_msg.no_of_agdbs_pfno[index1] = (byte) 0;
        checked { ++index1; }
      }
      int index2 = 0;
      while (index2 < 16 /*0x10*/)
      {
        if (frmMainFormIPIS.mdch_db.port_status[index2])
        {
          int index3 = 0;
          while (index3 < (int) frmMainFormIPIS.mdch_db.mdch_port[index2].no_of_systems)
          {
            if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.mdch_db.mdch_port[index2].system_type[index3], "AGDB", false) == 0)
            {
              if (!frmMainFormIPIS.mdch_db.mdch_port[index2].dis_board[index3].all_platfroms)
              {
                byte pfnoInt1 = network_db_read.get_pfno_int(frmMainFormIPIS.mdch_db.mdch_port[index2].dis_board[index3].platform_no);
                if (pfnoInt1 != (byte) 0)
                  checked { --pfnoInt1; }
                taddb_msg.agdb_dis_brd[(int) pfnoInt1, (int) taddb_msg.no_of_agdbs_pfno[(int) pfnoInt1]].agdb_addr = frmMainFormIPIS.mdch_db.mdch_port[index2].dis_board[index3].dis_board_addr;
                taddb_msg.agdb_dis_brd[(int) pfnoInt1, (int) taddb_msg.no_of_agdbs_pfno[(int) pfnoInt1]].agdb_name = frmMainFormIPIS.mdch_db.mdch_port[index2].dis_board[index3].dis_board_name;
                taddb_msg.agdb_dis_brd[(int) pfnoInt1, (int) taddb_msg.no_of_agdbs_pfno[(int) pfnoInt1]].swithcing_time = frmMainFormIPIS.mdch_db.mdch_port[index2].dis_board[index3].switching_time;
                taddb_msg.agdb_dis_brd[(int) pfnoInt1, (int) taddb_msg.no_of_agdbs_pfno[(int) pfnoInt1]].video_type = frmMainFormIPIS.mdch_db.mdch_port[index2].dis_board[index3].video_type;
                taddb_msg.agdb_dis_brd[(int) pfnoInt1, (int) taddb_msg.no_of_agdbs_pfno[(int) pfnoInt1]].shared_platform = frmMainFormIPIS.mdch_db.mdch_port[index2].dis_board[index3].shared_platform;
                taddb_msg.agdb_dis_brd[(int) pfnoInt1, (int) taddb_msg.no_of_agdbs_pfno[(int) pfnoInt1]].shared_platform_no = frmMainFormIPIS.mdch_db.mdch_port[index2].dis_board[index3].shared_platform_no;
                // TODO: Implement missing property agdb_sign in agdb_dis_structure
                // taddb_msg.agdb_dis_brd[(int) pfnoInt1, (int) taddb_msg.no_of_agdbs_pfno[(int) pfnoInt1]].agdb_sign = frmMainFormIPIS.mdch_db.mdch_port[index2].dis_board[index3].agdb_sign;
                taddb_msg.no_of_agdbs_pfno[(int) pfnoInt1] = checked ((byte) ((int) taddb_msg.no_of_agdbs_pfno[(int) pfnoInt1] + 1));
                if (frmMainFormIPIS.mdch_db.mdch_port[index2].dis_board[index3].shared_platform)
                {
                  byte pfnoInt2 = network_db_read.get_pfno_int(frmMainFormIPIS.mdch_db.mdch_port[index2].dis_board[index3].shared_platform_no);
                  if (pfnoInt2 != (byte) 0)
                    checked { --pfnoInt2; }
                  taddb_msg.agdb_dis_brd[(int) pfnoInt2, (int) taddb_msg.no_of_agdbs_pfno[(int) pfnoInt2]].agdb_addr = frmMainFormIPIS.mdch_db.mdch_port[index2].dis_board[index3].dis_board_addr;
                  taddb_msg.agdb_dis_brd[(int) pfnoInt2, (int) taddb_msg.no_of_agdbs_pfno[(int) pfnoInt2]].agdb_name = frmMainFormIPIS.mdch_db.mdch_port[index2].dis_board[index3].dis_board_name;
                  taddb_msg.agdb_dis_brd[(int) pfnoInt2, (int) taddb_msg.no_of_agdbs_pfno[(int) pfnoInt2]].swithcing_time = frmMainFormIPIS.mdch_db.mdch_port[index2].dis_board[index3].switching_time;
                  taddb_msg.agdb_dis_brd[(int) pfnoInt2, (int) taddb_msg.no_of_agdbs_pfno[(int) pfnoInt2]].video_type = frmMainFormIPIS.mdch_db.mdch_port[index2].dis_board[index3].video_type;
                  taddb_msg.agdb_dis_brd[(int) pfnoInt2, (int) taddb_msg.no_of_agdbs_pfno[(int) pfnoInt2]].shared_platform = frmMainFormIPIS.mdch_db.mdch_port[index2].dis_board[index3].shared_platform;
                  // TODO: Implement missing property agdb_sign in agdb_dis_structure
                  // taddb_msg.agdb_dis_brd[(int) pfnoInt2, (int) taddb_msg.no_of_agdbs_pfno[(int) pfnoInt2]].agdb_sign = frmMainFormIPIS.mdch_db.mdch_port[index2].dis_board[index3].agdb_sign;
                  taddb_msg.agdb_dis_brd[(int) pfnoInt2, (int) taddb_msg.no_of_agdbs_pfno[(int) pfnoInt2]].shared_platform_no = frmMainFormIPIS.mdch_db.mdch_port[index2].dis_board[index3].platform_no;
                  taddb_msg.no_of_agdbs_pfno[(int) pfnoInt2] = checked ((byte) ((int) taddb_msg.no_of_agdbs_pfno[(int) pfnoInt2] + 1));
                }
              }
              else
              {
                // TODO: Fix type conversion errors - agdb_com_dis_brd appears to be byte[] but being used as int
                // taddb_msg.agdb_com_dis_brd[(int) taddb_msg.no_of_com_agdbs].agdb_addr = frmMainFormIPIS.mdch_db.mdch_port[index2].dis_board[index3].dis_board_addr;
                // taddb_msg.agdb_com_dis_brd[(int) taddb_msg.no_of_com_agdbs].agdb_name = frmMainFormIPIS.mdch_db.mdch_port[index2].dis_board[index3].dis_board_name;
                // taddb_msg.agdb_com_dis_brd[(int) taddb_msg.no_of_com_agdbs].swithcing_time = frmMainFormIPIS.mdch_db.mdch_port[index2].dis_board[index3].switching_time;
                // taddb_msg.agdb_com_dis_brd[(int) taddb_msg.no_of_com_agdbs].video_type = frmMainFormIPIS.mdch_db.mdch_port[index2].dis_board[index3].video_type;
                // taddb_msg.agdb_com_dis_brd[(int) taddb_msg.no_of_com_agdbs].agdb_sign = frmMainFormIPIS.mdch_db.mdch_port[index2].dis_board[index3].agdb_sign;
                // checked { ++taddb_msg.no_of_com_agdbs; }
              }
            }
            else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.mdch_db.mdch_port[index2].system_type[index3], "PDCH", false) == 0)
            {
              int index4 = 0;
              while (index4 < 16 /*0x10*/)
              {
                if (frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].port_status[index4])
                {
                  int index5 = 0;
                  while (index5 < (int) frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_port[index4].no_of_systems)
                  {
                    if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_port[index4].dis_board[index5].dis_board_type, "AGDB", false) == 0)
                    {
                      if (!frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_port[index4].dis_board[index5].all_platfroms)
                      {
                        byte pfnoInt3 = network_db_read.get_pfno_int(frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_port[index4].dis_board[index5].platform_no);
                        if (pfnoInt3 != (byte) 0)
                          checked { --pfnoInt3; }
                        taddb_msg.agdb_dis_brd[(int) pfnoInt3, (int) taddb_msg.no_of_agdbs_pfno[(int) pfnoInt3]].agdb_addr = frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_port[index4].dis_board[index5].dis_board_addr;
                        taddb_msg.agdb_dis_brd[(int) pfnoInt3, (int) taddb_msg.no_of_agdbs_pfno[(int) pfnoInt3]].agdb_name = frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_port[index4].dis_board[index5].dis_board_name;
                        taddb_msg.agdb_dis_brd[(int) pfnoInt3, (int) taddb_msg.no_of_agdbs_pfno[(int) pfnoInt3]].swithcing_time = frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_port[index4].dis_board[index5].switching_time;
                        taddb_msg.agdb_dis_brd[(int) pfnoInt3, (int) taddb_msg.no_of_agdbs_pfno[(int) pfnoInt3]].video_type = frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_port[index4].dis_board[index5].video_type;
                        taddb_msg.agdb_dis_brd[(int) pfnoInt3, (int) taddb_msg.no_of_agdbs_pfno[(int) pfnoInt3]].shared_platform = frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_port[index4].dis_board[index5].shared_platform;
                        taddb_msg.agdb_dis_brd[(int) pfnoInt3, (int) taddb_msg.no_of_agdbs_pfno[(int) pfnoInt3]].shared_platform_no = frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_port[index4].dis_board[index5].shared_platform_no;
                        // TODO: Implement missing property agdb_sign in agdb_dis_structure
                        // taddb_msg.agdb_dis_brd[(int) pfnoInt3, (int) taddb_msg.no_of_agdbs_pfno[(int) pfnoInt3]].agdb_sign = frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_port[index4].dis_board[index5].agdb_sign;
                        taddb_msg.no_of_agdbs_pfno[(int) pfnoInt3] = checked ((byte) ((int) taddb_msg.no_of_agdbs_pfno[(int) pfnoInt3] + 1));
                        if (frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_port[index4].dis_board[index5].shared_platform)
                        {
                          byte pfnoInt4 = network_db_read.get_pfno_int(frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_port[index4].dis_board[index5].shared_platform_no);
                          if (pfnoInt4 != (byte) 0)
                            checked { --pfnoInt4; }
                          taddb_msg.agdb_dis_brd[(int) pfnoInt4, (int) taddb_msg.no_of_agdbs_pfno[(int) pfnoInt4]].agdb_addr = frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_port[index4].dis_board[index5].dis_board_addr;
                          taddb_msg.agdb_dis_brd[(int) pfnoInt4, (int) taddb_msg.no_of_agdbs_pfno[(int) pfnoInt4]].agdb_name = frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_port[index4].dis_board[index5].dis_board_name;
                          taddb_msg.agdb_dis_brd[(int) pfnoInt4, (int) taddb_msg.no_of_agdbs_pfno[(int) pfnoInt4]].swithcing_time = frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_port[index4].dis_board[index5].switching_time;
                          taddb_msg.agdb_dis_brd[(int) pfnoInt4, (int) taddb_msg.no_of_agdbs_pfno[(int) pfnoInt4]].video_type = frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_port[index4].dis_board[index5].video_type;
                          taddb_msg.agdb_dis_brd[(int) pfnoInt4, (int) taddb_msg.no_of_agdbs_pfno[(int) pfnoInt4]].shared_platform = frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_port[index4].dis_board[index5].shared_platform;
                          taddb_msg.agdb_dis_brd[(int) pfnoInt4, (int) taddb_msg.no_of_agdbs_pfno[(int) pfnoInt4]].shared_platform_no = frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_port[index4].dis_board[index5].platform_no;
                          // TODO: Implement missing property agdb_sign in agdb_dis_structure
                          // taddb_msg.agdb_dis_brd[(int) pfnoInt4, (int) taddb_msg.no_of_agdbs_pfno[(int) pfnoInt4]].agdb_sign = frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_port[index4].dis_board[index5].agdb_sign;
                          taddb_msg.no_of_agdbs_pfno[(int) pfnoInt4] = checked ((byte) ((int) taddb_msg.no_of_agdbs_pfno[(int) pfnoInt4] + 1));
                        }
                      }
                      else
                      {
                        // TODO: Fix type conversion errors - agdb_com_dis_brd appears to be byte[] but being used as int
                        // taddb_msg.agdb_com_dis_brd[(int) taddb_msg.no_of_com_agdbs].agdb_addr = frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_port[index4].dis_board[index5].dis_board_addr;
                        // taddb_msg.agdb_com_dis_brd[(int) taddb_msg.no_of_com_agdbs].agdb_name = frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_port[index4].dis_board[index5].dis_board_name;
                        // taddb_msg.agdb_com_dis_brd[(int) taddb_msg.no_of_com_agdbs].swithcing_time = frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_port[index4].dis_board[index5].switching_time;
                        // taddb_msg.agdb_com_dis_brd[(int) taddb_msg.no_of_com_agdbs].video_type = frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_port[index4].dis_board[index5].video_type;
                        // taddb_msg.agdb_com_dis_brd[(int) taddb_msg.no_of_com_agdbs].agdb_sign = frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_port[index4].dis_board[index5].agdb_sign;
                        // checked { ++taddb_msg.no_of_com_agdbs; }
                      }
                    }
                    checked { ++index5; }
                    if (index5 == 8)
                      break;
                  }
                }
                checked { ++index4; }
              }
            }
            checked { ++index3; }
            if (index3 == 4)
              break;
          }
        }
        checked { ++index2; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  public static void get_cgdb_dis_brd_info()
  {
    try
    {
      int index1 = 0;
      while (index1 < 25)
      {
        cgdb_dis.no_of_cgdb[index1] = (byte) 0;
        checked { ++index1; }
      }
      int index2 = 0;
      while (index2 < 16 /*0x10*/)
      {
        if (frmMainFormIPIS.mdch_db.port_status[index2])
        {
          int index3 = 0;
          while (index3 < (int) frmMainFormIPIS.mdch_db.mdch_port[index2].no_of_systems)
          {
            if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.mdch_db.mdch_port[index2].system_type[index3], "PDCH", false) == 0)
            {
              int index4 = 0;
              while (index4 < 16 /*0x10*/)
              {
                if (frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].port_status[index4])
                {
                  int index5 = 0;
                  while (index5 < (int) frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_port[index4].no_of_systems)
                  {
                    if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_port[index4].dis_board[index5].dis_board_type, "CGDB", false) == 0)
                    {
                      byte pfnoInt = network_db_read.get_pfno_int(frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_port[index4].dis_board[index5].platform_no);
                      if (pfnoInt != (byte) 0)
                        checked { --pfnoInt; }
                      cgdb_dis.cgdb_dis_brd[(int) pfnoInt, (int) cgdb_dis.no_of_cgdb[(int) pfnoInt]].cgdb_addr = frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_port[index4].dis_board[index5].dis_board_addr;
                      cgdb_dis.cgdb_dis_brd[(int) pfnoInt, (int) cgdb_dis.no_of_cgdb[(int) pfnoInt]].cgdb_name = frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_port[index4].dis_board[index5].dis_board_name;
                      cgdb_dis.cgdb_dis_brd[(int) pfnoInt, (int) cgdb_dis.no_of_cgdb[(int) pfnoInt]].swithcing_time = frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_port[index4].dis_board[index5].switching_time;
                      cgdb_dis.cgdb_dis_brd[(int) pfnoInt, (int) cgdb_dis.no_of_cgdb[(int) pfnoInt]].video_type = frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_port[index4].dis_board[index5].video_type;
                      cgdb_dis.cgdb_dis_brd[(int) pfnoInt, (int) cgdb_dis.no_of_cgdb[(int) pfnoInt]].direction = frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_port[index4].dis_board[index5].cgdb_direction;
                      cgdb_dis.cgdb_dis_brd[(int) pfnoInt, (int) cgdb_dis.no_of_cgdb[(int) pfnoInt]].pdch_addr = frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_addr;
                      cgdb_dis.cgdb_dis_brd[(int) pfnoInt, (int) cgdb_dis.no_of_cgdb[(int) pfnoInt]].pdch_name = frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_name;
                      cgdb_dis.cgdb_dis_brd[(int) pfnoInt, (int) cgdb_dis.no_of_cgdb[(int) pfnoInt]].multicast_addr = frmMainFormIPIS.mdch_db.mdch_port[index2].pdch[index3].pdch_port[index4].dis_board[index5].multicast_addr;
                      cgdb_dis.no_of_cgdb[(int) pfnoInt] = checked ((byte) ((int) cgdb_dis.no_of_cgdb[(int) pfnoInt] + 1));
                    }
                    checked { ++index5; }
                    if (index5 == 8)
                      break;
                  }
                }
                checked { ++index4; }
              }
            }
            checked { ++index3; }
            if (index3 == 4)
              break;
          }
        }
        checked { ++index2; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  public static byte check_mdch_multicast_addr(byte addr, byte port_no)
  {
    if (addr == (byte) 0)
      return 1;
    int index = 0;
    while (index < (int) frmMainFormIPIS.mdch_port_info[(int) port_no].no_of_system_ids)
    {
      if ((int) frmMainFormIPIS.mdch_port_info[(int) port_no].system_id[index] == (int) addr)
        return 1;
      checked { ++index; }
    }
    return 0;
  }

  public static byte check_pdch_multicast_addr(byte platform_no, byte addr, byte port_no)
  {
    if (addr == (byte) 0)
      return 1;
    int index = 0;
    while (index < (int) frmMainFormIPIS.pdch_port_info[(int) platform_no].pdch_port[(int) port_no].no_of_system_ids)
    {
      if ((int) frmMainFormIPIS.pdch_port_info[(int) platform_no].pdch_port[(int) port_no].system_id[index] == (int) addr)
        return 1;
      checked { ++index; }
    }
    return 0;
  }

  public static void get_mdch_port_info()
  {
    try
    {
      int index1 = 0;
      while (index1 < 16 /*0x10*/)
      {
        frmMainFormIPIS.mdch_port_info[index1].no_of_system_ids = (byte) 0;
        checked { ++index1; }
      }
      int port_no = 0;
      while (port_no < 16 /*0x10*/)
      {
        if (frmMainFormIPIS.mdch_db.port_status[port_no])
        {
          int index2 = 0;
          while (index2 < (int) frmMainFormIPIS.mdch_db.mdch_port[port_no].no_of_systems)
          {
            if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.mdch_db.mdch_port[port_no].system_type[index2], "PDB", false) == 0)
            {
              frmMainFormIPIS.mdch_port_info[port_no].system_id[(int) frmMainFormIPIS.mdch_port_info[port_no].no_of_system_ids] = frmMainFormIPIS.mdch_db.mdch_port[port_no].dis_board[index2].dis_board_addr;
              frmMainFormIPIS.mdch_port_info[port_no].system_name[(int) frmMainFormIPIS.mdch_port_info[port_no].no_of_system_ids] = frmMainFormIPIS.mdch_db.mdch_port[port_no].dis_board[index2].dis_board_name;
              checked { ++frmMainFormIPIS.mdch_port_info[port_no].no_of_system_ids; }
              if (network_db_read.check_mdch_multicast_addr(frmMainFormIPIS.mdch_db.mdch_port[port_no].dis_board[index2].multicast_addr, checked ((byte) port_no)) == (byte) 0)
              {
                frmMainFormIPIS.mdch_port_info[port_no].system_id[(int) frmMainFormIPIS.mdch_port_info[port_no].no_of_system_ids] = frmMainFormIPIS.mdch_db.mdch_port[port_no].dis_board[index2].multicast_addr;
                frmMainFormIPIS.mdch_port_info[port_no].system_name[(int) frmMainFormIPIS.mdch_port_info[port_no].no_of_system_ids] = frmMainFormIPIS.mdch_db.mdch_port[port_no].dis_board[index2].dis_board_name;
                checked { ++frmMainFormIPIS.mdch_port_info[port_no].no_of_system_ids; }
              }
            }
            else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.mdch_db.mdch_port[port_no].system_type[index2], "AGDB", false) == 0)
            {
              frmMainFormIPIS.mdch_port_info[port_no].system_id[(int) frmMainFormIPIS.mdch_port_info[port_no].no_of_system_ids] = frmMainFormIPIS.mdch_db.mdch_port[port_no].dis_board[index2].dis_board_addr;
              frmMainFormIPIS.mdch_port_info[port_no].system_name[(int) frmMainFormIPIS.mdch_port_info[port_no].no_of_system_ids] = frmMainFormIPIS.mdch_db.mdch_port[port_no].dis_board[index2].dis_board_name;
              checked { ++frmMainFormIPIS.mdch_port_info[port_no].no_of_system_ids; }
            }
            else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.mdch_db.mdch_port[port_no].system_type[index2], "MLDB", false) == 0)
            {
              frmMainFormIPIS.mdch_port_info[port_no].system_id[(int) frmMainFormIPIS.mdch_port_info[port_no].no_of_system_ids] = frmMainFormIPIS.mdch_db.mdch_port[port_no].dis_board[index2].dis_board_addr;
              frmMainFormIPIS.mdch_port_info[port_no].system_name[(int) frmMainFormIPIS.mdch_port_info[port_no].no_of_system_ids] = frmMainFormIPIS.mdch_db.mdch_port[port_no].dis_board[index2].dis_board_name;
              checked { ++frmMainFormIPIS.mdch_port_info[port_no].no_of_system_ids; }
            }
            else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.mdch_db.mdch_port[port_no].system_type[index2], "PDCH", false) == 0)
            {
              frmMainFormIPIS.mdch_port_info[port_no].system_id[(int) frmMainFormIPIS.mdch_port_info[port_no].no_of_system_ids] = frmMainFormIPIS.mdch_db.mdch_port[port_no].pdch[index2].pdch_addr;
              frmMainFormIPIS.mdch_port_info[port_no].system_name[(int) frmMainFormIPIS.mdch_port_info[port_no].no_of_system_ids] = frmMainFormIPIS.mdch_db.mdch_port[port_no].pdch[index2].pdch_name;
              checked { ++frmMainFormIPIS.mdch_port_info[port_no].no_of_system_ids; }
              int index3 = 0;
              while (index3 < 16 /*0x10*/)
              {
                if (frmMainFormIPIS.mdch_db.mdch_port[port_no].pdch[index2].port_status[index3])
                {
                  int index4 = 0;
                  while (index4 < (int) frmMainFormIPIS.mdch_db.mdch_port[port_no].pdch[index2].pdch_port[index3].no_of_systems)
                  {
                    if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.mdch_db.mdch_port[port_no].pdch[index2].pdch_port[index3].dis_board[index4].dis_board_type, "PDB", false) == 0)
                    {
                      frmMainFormIPIS.mdch_port_info[port_no].system_id[(int) frmMainFormIPIS.mdch_port_info[port_no].no_of_system_ids] = frmMainFormIPIS.mdch_db.mdch_port[port_no].pdch[index2].pdch_port[index3].dis_board[index4].dis_board_addr;
                      frmMainFormIPIS.mdch_port_info[port_no].system_name[(int) frmMainFormIPIS.mdch_port_info[port_no].no_of_system_ids] = frmMainFormIPIS.mdch_db.mdch_port[port_no].pdch[index2].pdch_port[index3].dis_board[index4].dis_board_name;
                      checked { ++frmMainFormIPIS.mdch_port_info[port_no].no_of_system_ids; }
                      if (network_db_read.check_mdch_multicast_addr(frmMainFormIPIS.mdch_db.mdch_port[port_no].pdch[index2].pdch_port[index3].dis_board[index4].multicast_addr, checked ((byte) port_no)) == (byte) 0)
                      {
                        frmMainFormIPIS.mdch_port_info[port_no].system_id[(int) frmMainFormIPIS.mdch_port_info[port_no].no_of_system_ids] = frmMainFormIPIS.mdch_db.mdch_port[port_no].pdch[index2].pdch_port[index3].dis_board[index4].multicast_addr;
                        frmMainFormIPIS.mdch_port_info[port_no].system_name[(int) frmMainFormIPIS.mdch_port_info[port_no].no_of_system_ids] = frmMainFormIPIS.mdch_db.mdch_port[port_no].pdch[index2].pdch_port[index3].dis_board[index4].dis_board_name;
                        checked { ++frmMainFormIPIS.mdch_port_info[port_no].no_of_system_ids; }
                      }
                    }
                    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.mdch_db.mdch_port[port_no].pdch[index2].pdch_port[index3].dis_board[index4].dis_board_type, "AGDB", false) == 0)
                    {
                      frmMainFormIPIS.mdch_port_info[port_no].system_id[(int) frmMainFormIPIS.mdch_port_info[port_no].no_of_system_ids] = frmMainFormIPIS.mdch_db.mdch_port[port_no].pdch[index2].pdch_port[index3].dis_board[index4].dis_board_addr;
                      frmMainFormIPIS.mdch_port_info[port_no].system_name[(int) frmMainFormIPIS.mdch_port_info[port_no].no_of_system_ids] = frmMainFormIPIS.mdch_db.mdch_port[port_no].pdch[index2].pdch_port[index3].dis_board[index4].dis_board_name;
                      checked { ++frmMainFormIPIS.mdch_port_info[port_no].no_of_system_ids; }
                    }
                    checked { ++index4; }
                    if (index4 == 8)
                      break;
                  }
                }
                checked { ++index3; }
              }
            }
            checked { ++index2; }
            if (index2 == 4)
              break;
          }
        }
        if (frmMainFormIPIS.mdch_port_info[port_no].no_of_system_ids > (byte) 1)
          frmMainFormIPIS.mdch_port_info[port_no].multi_drop = (byte) 1;
        checked { ++port_no; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  public static void get_pdch_port_info()
  {
    try
    {
      int index1 = 0;
      while (index1 < 25)
      {
        int index2 = 0;
        while (index2 < 16 /*0x10*/)
        {
          frmMainFormIPIS.pdch_port_info[index1].pdch_port[index2].no_of_system_ids = (byte) 0;
          checked { ++index2; }
        }
        checked { ++index1; }
      }
      int index3 = 0;
      while (index3 < 16 /*0x10*/)
      {
        if (frmMainFormIPIS.mdch_db.port_status[index3])
        {
          int index4 = 0;
          while (index4 < (int) frmMainFormIPIS.mdch_db.mdch_port[index3].no_of_systems)
          {
            if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.mdch_db.mdch_port[index3].system_type[index4], "PDCH", false) == 0)
            {
              byte pfnoInt1 = network_db_read.get_pfno_int(frmMainFormIPIS.mdch_db.mdch_port[index3].pdch[index4].platform_no);
              if (pfnoInt1 != (byte) 0)
                checked { --pfnoInt1; }
              byte pfnoInt2 = 0;
              if (frmMainFormIPIS.mdch_db.mdch_port[index3].pdch[index4].shared_platform)
              {
                frmMainFormIPIS.pdch_port_info[(int) pfnoInt1].shared_platform = true;
                frmMainFormIPIS.pdch_port_info[(int) pfnoInt1].shared_platform_no = frmMainFormIPIS.mdch_db.mdch_port[index3].pdch[index4].shared_platform_no;
                pfnoInt2 = network_db_read.get_pfno_int(frmMainFormIPIS.mdch_db.mdch_port[index3].pdch[index4].shared_platform_no);
                if (pfnoInt2 != (byte) 0)
                {
                  checked { --pfnoInt2; }
                  frmMainFormIPIS.pdch_port_info[(int) pfnoInt2].shared_platform_no = frmMainFormIPIS.mdch_db.mdch_port[index3].pdch[index4].platform_no;
                  frmMainFormIPIS.pdch_port_info[(int) pfnoInt2].available = true;
                  frmMainFormIPIS.pdch_port_info[(int) pfnoInt2].pdch_addr = frmMainFormIPIS.mdch_db.mdch_port[index3].pdch[index4].pdch_addr;
                  frmMainFormIPIS.pdch_port_info[(int) pfnoInt2].pdch_name = frmMainFormIPIS.mdch_db.mdch_port[index3].pdch[index4].pdch_name;
                  frmMainFormIPIS.pdch_port_info[(int) pfnoInt2].shared_platform = true;
                }
                else
                  goto label_44;
              }
              frmMainFormIPIS.pdch_port_info[(int) pfnoInt1].available = true;
              frmMainFormIPIS.pdch_port_info[(int) pfnoInt1].pdch_addr = frmMainFormIPIS.mdch_db.mdch_port[index3].pdch[index4].pdch_addr;
              frmMainFormIPIS.pdch_port_info[(int) pfnoInt1].pdch_name = frmMainFormIPIS.mdch_db.mdch_port[index3].pdch[index4].pdch_name;
              int port_no = 0;
              while (port_no < 16 /*0x10*/)
              {
                frmMainFormIPIS.pdch_port_info[(int) pfnoInt1].pdch_port[port_no].no_of_system_ids = (byte) 0;
                if (frmMainFormIPIS.mdch_db.mdch_port[index3].pdch[index4].port_status[port_no])
                {
                  int index5 = 0;
                  while (index5 < (int) frmMainFormIPIS.mdch_db.mdch_port[index3].pdch[index4].pdch_port[port_no].no_of_systems)
                  {
                    if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.mdch_db.mdch_port[index3].pdch[index4].pdch_port[port_no].dis_board[index5].dis_board_type, "PDB", false) == 0)
                    {
                      frmMainFormIPIS.pdch_port_info[(int) pfnoInt1].pdch_port[port_no].system_id[(int) frmMainFormIPIS.pdch_port_info[(int) pfnoInt1].pdch_port[port_no].no_of_system_ids] = frmMainFormIPIS.mdch_db.mdch_port[index3].pdch[index4].pdch_port[port_no].dis_board[index5].dis_board_addr;
                      frmMainFormIPIS.pdch_port_info[(int) pfnoInt1].pdch_port[port_no].system_name[(int) frmMainFormIPIS.pdch_port_info[(int) pfnoInt1].pdch_port[port_no].no_of_system_ids] = frmMainFormIPIS.mdch_db.mdch_port[index3].pdch[index4].pdch_port[port_no].dis_board[index5].dis_board_name;
                      checked { ++frmMainFormIPIS.pdch_port_info[(int) pfnoInt1].pdch_port[port_no].no_of_system_ids; }
                      if (frmMainFormIPIS.pdch_port_info[(int) pfnoInt1].shared_platform)
                      {
                        frmMainFormIPIS.pdch_port_info[(int) pfnoInt2].pdch_port[port_no].system_id[(int) frmMainFormIPIS.pdch_port_info[(int) pfnoInt2].pdch_port[port_no].no_of_system_ids] = frmMainFormIPIS.mdch_db.mdch_port[index3].pdch[index4].pdch_port[port_no].dis_board[index5].dis_board_addr;
                        frmMainFormIPIS.pdch_port_info[(int) pfnoInt2].pdch_port[port_no].system_name[(int) frmMainFormIPIS.pdch_port_info[(int) pfnoInt2].pdch_port[port_no].no_of_system_ids] = frmMainFormIPIS.mdch_db.mdch_port[index3].pdch[index4].pdch_port[port_no].dis_board[index5].dis_board_name;
                        checked { ++frmMainFormIPIS.pdch_port_info[(int) pfnoInt2].pdch_port[port_no].no_of_system_ids; }
                      }
                      if (network_db_read.check_pdch_multicast_addr(pfnoInt1, frmMainFormIPIS.mdch_db.mdch_port[index3].pdch[index4].pdch_port[port_no].dis_board[index5].multicast_addr, checked ((byte) port_no)) == (byte) 0)
                      {
                        frmMainFormIPIS.pdch_port_info[(int) pfnoInt1].pdch_port[port_no].system_id[(int) frmMainFormIPIS.pdch_port_info[(int) pfnoInt1].pdch_port[port_no].no_of_system_ids] = frmMainFormIPIS.mdch_db.mdch_port[index3].pdch[index4].pdch_port[port_no].dis_board[index5].multicast_addr;
                        frmMainFormIPIS.pdch_port_info[(int) pfnoInt1].pdch_port[port_no].system_name[(int) frmMainFormIPIS.pdch_port_info[(int) pfnoInt1].pdch_port[port_no].no_of_system_ids] = frmMainFormIPIS.mdch_db.mdch_port[index3].pdch[index4].pdch_port[port_no].dis_board[index5].dis_board_name;
                        checked { ++frmMainFormIPIS.pdch_port_info[(int) pfnoInt1].pdch_port[port_no].no_of_system_ids; }
                        if (frmMainFormIPIS.pdch_port_info[(int) pfnoInt1].shared_platform)
                        {
                          frmMainFormIPIS.pdch_port_info[(int) pfnoInt2].pdch_port[port_no].system_id[(int) frmMainFormIPIS.pdch_port_info[(int) pfnoInt2].pdch_port[port_no].no_of_system_ids] = frmMainFormIPIS.mdch_db.mdch_port[index3].pdch[index4].pdch_port[port_no].dis_board[index5].multicast_addr;
                          frmMainFormIPIS.pdch_port_info[(int) pfnoInt2].pdch_port[port_no].system_name[(int) frmMainFormIPIS.pdch_port_info[(int) pfnoInt2].pdch_port[port_no].no_of_system_ids] = frmMainFormIPIS.mdch_db.mdch_port[index3].pdch[index4].pdch_port[port_no].dis_board[index5].dis_board_name;
                          checked { ++frmMainFormIPIS.pdch_port_info[(int) pfnoInt2].pdch_port[port_no].no_of_system_ids; }
                        }
                      }
                    }
                    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.mdch_db.mdch_port[index3].pdch[index4].pdch_port[port_no].dis_board[index5].dis_board_type, "AGDB", false) == 0)
                    {
                      frmMainFormIPIS.pdch_port_info[(int) pfnoInt1].pdch_port[port_no].system_id[(int) frmMainFormIPIS.pdch_port_info[(int) pfnoInt1].pdch_port[port_no].no_of_system_ids] = frmMainFormIPIS.mdch_db.mdch_port[index3].pdch[index4].pdch_port[port_no].dis_board[index5].dis_board_addr;
                      frmMainFormIPIS.pdch_port_info[(int) pfnoInt1].pdch_port[port_no].system_name[(int) frmMainFormIPIS.pdch_port_info[(int) pfnoInt1].pdch_port[port_no].no_of_system_ids] = frmMainFormIPIS.mdch_db.mdch_port[index3].pdch[index4].pdch_port[port_no].dis_board[index5].dis_board_name;
                      checked { ++frmMainFormIPIS.pdch_port_info[(int) pfnoInt1].pdch_port[port_no].no_of_system_ids; }
                      if (frmMainFormIPIS.pdch_port_info[(int) pfnoInt1].shared_platform)
                      {
                        frmMainFormIPIS.pdch_port_info[(int) pfnoInt2].pdch_port[port_no].system_id[(int) frmMainFormIPIS.pdch_port_info[(int) pfnoInt2].pdch_port[port_no].no_of_system_ids] = frmMainFormIPIS.mdch_db.mdch_port[index3].pdch[index4].pdch_port[port_no].dis_board[index5].dis_board_addr;
                        frmMainFormIPIS.pdch_port_info[(int) pfnoInt2].pdch_port[port_no].system_name[(int) frmMainFormIPIS.pdch_port_info[(int) pfnoInt2].pdch_port[port_no].no_of_system_ids] = frmMainFormIPIS.mdch_db.mdch_port[index3].pdch[index4].pdch_port[port_no].dis_board[index5].dis_board_name;
                        checked { ++frmMainFormIPIS.pdch_port_info[(int) pfnoInt2].pdch_port[port_no].no_of_system_ids; }
                      }
                    }
                    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.mdch_db.mdch_port[index3].pdch[index4].pdch_port[port_no].dis_board[index5].dis_board_type, "CGDB", false) == 0)
                    {
                      frmMainFormIPIS.pdch_port_info[(int) pfnoInt1].pdch_port[port_no].system_id[(int) frmMainFormIPIS.pdch_port_info[(int) pfnoInt1].pdch_port[port_no].no_of_system_ids] = frmMainFormIPIS.mdch_db.mdch_port[index3].pdch[index4].pdch_port[port_no].dis_board[index5].dis_board_addr;
                      frmMainFormIPIS.pdch_port_info[(int) pfnoInt1].pdch_port[port_no].system_name[(int) frmMainFormIPIS.pdch_port_info[(int) pfnoInt1].pdch_port[port_no].no_of_system_ids] = frmMainFormIPIS.mdch_db.mdch_port[index3].pdch[index4].pdch_port[port_no].dis_board[index5].dis_board_name;
                      checked { ++frmMainFormIPIS.pdch_port_info[(int) pfnoInt1].pdch_port[port_no].no_of_system_ids; }
                      if (frmMainFormIPIS.pdch_port_info[(int) pfnoInt1].shared_platform)
                      {
                        frmMainFormIPIS.pdch_port_info[(int) pfnoInt2].pdch_port[port_no].system_id[(int) frmMainFormIPIS.pdch_port_info[(int) pfnoInt2].pdch_port[port_no].no_of_system_ids] = frmMainFormIPIS.mdch_db.mdch_port[index3].pdch[index4].pdch_port[port_no].dis_board[index5].dis_board_addr;
                        frmMainFormIPIS.pdch_port_info[(int) pfnoInt2].pdch_port[port_no].system_name[(int) frmMainFormIPIS.pdch_port_info[(int) pfnoInt2].pdch_port[port_no].no_of_system_ids] = frmMainFormIPIS.mdch_db.mdch_port[index3].pdch[index4].pdch_port[port_no].dis_board[index5].dis_board_name;
                        checked { ++frmMainFormIPIS.pdch_port_info[(int) pfnoInt2].pdch_port[port_no].no_of_system_ids; }
                      }
                      if (network_db_read.check_pdch_multicast_addr(pfnoInt1, frmMainFormIPIS.mdch_db.mdch_port[index3].pdch[index4].pdch_port[port_no].dis_board[index5].multicast_addr, checked ((byte) port_no)) == (byte) 0)
                      {
                        frmMainFormIPIS.pdch_port_info[(int) pfnoInt1].pdch_port[port_no].system_id[(int) frmMainFormIPIS.pdch_port_info[(int) pfnoInt1].pdch_port[port_no].no_of_system_ids] = frmMainFormIPIS.mdch_db.mdch_port[index3].pdch[index4].pdch_port[port_no].dis_board[index5].multicast_addr;
                        frmMainFormIPIS.pdch_port_info[(int) pfnoInt1].pdch_port[port_no].system_name[(int) frmMainFormIPIS.pdch_port_info[(int) pfnoInt1].pdch_port[port_no].no_of_system_ids] = frmMainFormIPIS.mdch_db.mdch_port[index3].pdch[index4].pdch_port[port_no].dis_board[index5].dis_board_name;
                        checked { ++frmMainFormIPIS.pdch_port_info[(int) pfnoInt1].pdch_port[port_no].no_of_system_ids; }
                      }
                    }
                    checked { ++index5; }
                    if (index5 == 8)
                      break;
                  }
                }
                if (frmMainFormIPIS.pdch_port_info[(int) pfnoInt1].pdch_port[port_no].no_of_system_ids > (byte) 1)
                  frmMainFormIPIS.pdch_port_info[(int) pfnoInt1].pdch_port[port_no].multi_drop = (byte) 1;
                if (frmMainFormIPIS.pdch_port_info[(int) pfnoInt1].shared_platform && frmMainFormIPIS.pdch_port_info[(int) pfnoInt2].pdch_port[port_no].no_of_system_ids > (byte) 1)
                  frmMainFormIPIS.pdch_port_info[(int) pfnoInt2].pdch_port[port_no].multi_drop = (byte) 1;
                checked { ++port_no; }
              }
            }
label_44:
            checked { ++index4; }
            if (index4 == 4)
              break;
          }
        }
        checked { ++index3; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  public static void mdch_cfg_msg_fill(ref byte[] mdch_cfg_msg)
  {
    try
    {
      int index1 = 0;
      while (index1 < 368)
      {
        mdch_cfg_msg[index1] = (byte) 0;
        checked { ++index1; }
      }
      int index2 = 0;
      int index3 = 0;
      while (index2 < 16 /*0x10*/)
      {
        mdch_cfg_msg[index3] = checked ((byte) (index2 + 1));
        int index4 = checked (index3 + 1);
        mdch_cfg_msg[index4] = frmMainFormIPIS.mdch_port_info[index2].multi_drop;
        int index5 = checked (index4 + 1);
        mdch_cfg_msg[index5] = frmMainFormIPIS.mdch_port_info[index2].no_of_system_ids;
        index3 = checked (index5 + 1);
        int index6 = 0;
        while (index6 < (int) frmMainFormIPIS.mdch_port_info[index2].no_of_system_ids)
        {
          mdch_cfg_msg[index3] = frmMainFormIPIS.mdch_port_info[index2].system_id[index6];
          checked { ++index3; }
          checked { ++index6; }
        }
        while (index6 < 20)
        {
          mdch_cfg_msg[index3] = (byte) 0;
          checked { ++index3; }
          checked { ++index6; }
        }
        checked { ++index2; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  public static void pdch_cfg_msg_fill(byte pdch_addr, ref byte[] pdch_cfg_msg)
  {
    try
    {
      int index1 = 0;
      while (index1 < 368)
      {
        pdch_cfg_msg[index1] = (byte) 0;
        checked { ++index1; }
      }
      int index2 = 0;
      while (index2 < 25)
      {
        if ((int) frmMainFormIPIS.pdch_port_info[index2].pdch_addr == (int) pdch_addr)
        {
          int index3 = 0;
          int index4 = 0;
          while (index3 < 16 /*0x10*/)
          {
            pdch_cfg_msg[index4] = checked ((byte) (index3 + 1));
            int index5 = checked (index4 + 1);
            pdch_cfg_msg[index5] = frmMainFormIPIS.pdch_port_info[index2].pdch_port[index3].multi_drop;
            int index6 = checked (index5 + 1);
            pdch_cfg_msg[index6] = frmMainFormIPIS.pdch_port_info[index2].pdch_port[index3].no_of_system_ids;
            index4 = checked (index6 + 1);
            int index7 = 0;
            while (index7 < (int) frmMainFormIPIS.pdch_port_info[index2].pdch_port[index3].no_of_system_ids)
            {
              pdch_cfg_msg[index4] = frmMainFormIPIS.pdch_port_info[index2].pdch_port[index3].system_id[index7];
              checked { ++index4; }
              checked { ++index7; }
            }
            while (index7 < 20)
            {
              pdch_cfg_msg[index4] = (byte) 0;
              checked { ++index4; }
              checked { ++index7; }
            }
            checked { ++index3; }
          }
        }
        checked { ++index2; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  public static byte pdch_info_data(
    string platform_no,
    ref string pdch_name,
    ref byte pdch_addr,
    ref bool pdch_shared,
    ref string pdch_shared_platform_no)
  {
    try
    {
      byte index1 = checked ((byte) ((int) network_db_read.get_pfno_int(platform_no) - 1));
      if (frmMainFormIPIS.pdch_port_info[(int) index1].available)
      {
        pdch_name = frmMainFormIPIS.pdch_port_info[(int) index1].pdch_name;
        pdch_addr = frmMainFormIPIS.pdch_port_info[(int) index1].pdch_addr;
        if (frmMainFormIPIS.pdch_port_info[(int) index1].shared_platform)
        {
          pdch_shared = true;
          pdch_shared_platform_no = frmMainFormIPIS.pdch_port_info[(int) index1].shared_platform_no;
        }
        return 1;
      }
      int index2 = 0;
      while (index2 < frmMainFormIPIS.pfno_cnt)
      {
        byte index3 = checked ((byte) ((int) network_db_read.get_pfno_int(frmMainFormIPIS.platform_nos[index2]) - 1));
        byte index4 = checked ((byte) ((int) network_db_read.get_pfno_int(platform_no) - 1));
        if (frmMainFormIPIS.pdch_port_info[(int) index3].available && frmMainFormIPIS.pdch_port_info[(int) index3].shared_platform && Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Strings.Trim(frmMainFormIPIS.pdch_port_info[(int) index3].shared_platform_no), Strings.Trim(platform_no), false) == 0)
        {
          pdch_name = frmMainFormIPIS.pdch_port_info[(int) index3].pdch_name;
          pdch_addr = frmMainFormIPIS.pdch_port_info[(int) index3].pdch_addr;
          pdch_shared = true;
          pdch_shared_platform_no = frmMainFormIPIS.pdch_port_info[(int) index4].shared_platform_no;
          return 1;
        }
        checked { ++index2; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    return 0;
  }

  public static void pdch_addr_data(ref string pdch_name, string pdch_addr)
  {
    try
    {
      int index = 0;
      while (index < 25)
      {
        if (Conversions.ToDouble(pdch_addr) == (double) frmMainFormIPIS.pdch_port_info[index].pdch_addr)
        {
          pdch_name = frmMainFormIPIS.pdch_port_info[index].pdch_name;
          break;
        }
        checked { ++index; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  public static void pdch_name_data(string pdch_name, ref string pdch_addr)
  {
    int index = 0;
    try
    {
      while (index < 25)
      {
        if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(pdch_name, frmMainFormIPIS.pdch_port_info[index].pdch_name, false) == 0)
        {
          pdch_addr = Conversions.ToString(frmMainFormIPIS.pdch_port_info[index].pdch_addr);
          break;
        }
        checked { ++index; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  public static byte cgdb_info_data(
    string platform_no,
    ref string[] cgdb_name,
    ref byte[] cgdb_addr,
    ref byte[] pdch_addr,
    ref string[] pdch_name,
    ref byte cgdb_cnt)
  {
    byte num1 = 0;
    try
    {
      byte index1 = checked ((byte) ((int) network_db_read.get_pfno_int(platform_no) - 1));
      cgdb_cnt = cgdb_dis.no_of_cgdb[(int) index1];
      if (cgdb_cnt == (byte) 0)
      {
        num1 = (byte) 0;
      }
      else
      {
        int index2 = 0;
        while (index2 < (int) cgdb_dis.no_of_cgdb[(int) index1])
        {
          cgdb_name[index2] = cgdb_dis.cgdb_dis_brd[(int) index1, index2].cgdb_name;
          cgdb_addr[index2] = cgdb_dis.cgdb_dis_brd[(int) index1, index2].cgdb_addr;
          pdch_addr[index2] = cgdb_dis.cgdb_dis_brd[(int) index1, index2].pdch_addr;
          pdch_name[index2] = cgdb_dis.cgdb_dis_brd[(int) index1, index2].pdch_name;
          checked { ++index2; }
        }
        num1 = (byte) 1;
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num2 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    return num1;
  }

  public static void cgdb_name_data(
    string cgdb_name,
    ref byte cgdb_addr,
    string platform_no,
    ref byte pdch_addr,
    ref string pdch_name,
    ref byte video,
    ref string direction)
  {
    try
    {
      byte index1 = 0;
      byte index2 = checked ((byte) ((int) network_db_read.get_pfno_int(platform_no) - 1));
      while ((uint) index1 < (uint) cgdb_dis.no_of_cgdb[(int) index2])
      {
        if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(cgdb_name, cgdb_dis.cgdb_dis_brd[(int) index2, (int) index1].cgdb_name, false) == 0)
        {
          cgdb_addr = cgdb_dis.cgdb_dis_brd[(int) index2, (int) index1].cgdb_addr;
          pdch_addr = cgdb_dis.cgdb_dis_brd[(int) index2, (int) index1].pdch_addr;
          pdch_name = cgdb_dis.cgdb_dis_brd[(int) index2, (int) index1].pdch_name;
          video = cgdb_dis.cgdb_dis_brd[(int) index2, (int) index1].video_type;
          direction = cgdb_dis.cgdb_dis_brd[(int) index2, (int) index1].direction;
          break;
        }
        checked { ++index1; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  public static void cgdb_addr_data(
    ref string cgdb_name,
    string cgdb_addr,
    string platform_no,
    ref byte pdch_addr,
    ref string pdch_name,
    ref byte video,
    ref string direction)
  {
    try
    {
      int index1 = 0;
      byte index2 = checked ((byte) ((int) network_db_read.get_pfno_int(platform_no) - 1));
      while (index1 < (int) cgdb_dis.no_of_cgdb[(int) index2])
      {
        if (Conversions.ToDouble(cgdb_addr) == (double) cgdb_dis.cgdb_dis_brd[(int) index2, index1].cgdb_addr)
        {
          cgdb_name = cgdb_dis.cgdb_dis_brd[(int) index2, index1].cgdb_name;
          pdch_addr = cgdb_dis.cgdb_dis_brd[(int) index2, index1].pdch_addr;
          pdch_name = cgdb_dis.cgdb_dis_brd[(int) index2, index1].pdch_name;
          video = cgdb_dis.cgdb_dis_brd[(int) index2, index1].video_type;
          direction = cgdb_dis.cgdb_dis_brd[(int) index2, index1].direction;
          break;
        }
        checked { ++index1; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  public static void cgdb_get_details_db(
    byte addr,
    string name,
    ref bool hub_type,
    ref byte arg1,
    ref byte arg2,
    ref byte arg3,
    ref byte arg4)
  {
    try
    {
      byte index1 = 0;
      while (index1 < (byte) 16 /*0x10*/)
      {
        byte index2 = 0;
        while ((uint) index2 < (uint) frmMainFormIPIS.mdch_db.mdch_port[(int) index1].no_of_systems)
        {
          if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.mdch_db.mdch_port[(int) index1].system_type[(int) index2], "PDCH", false) == 0)
          {
            byte index3 = 0;
            while (index3 < (byte) 16 /*0x10*/)
            {
              if (frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].port_status[(int) index3])
              {
                byte index4 = 0;
                while ((uint) index4 < (uint) frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].pdch_port[(int) index3].no_of_systems)
                {
                  if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].pdch_port[(int) index3].dis_board[(int) index4].dis_board_type, "CGDB", false) == 0 & (int) frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].pdch_port[(int) index3].dis_board[(int) index4].dis_board_addr == (int) addr & Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].pdch_port[(int) index3].dis_board[(int) index4].dis_board_name, name, false) == 0)
                  {
                    arg1 = index1;
                    arg2 = index2;
                    arg3 = index3;
                    arg4 = index4;
                    return;
                  }
                  checked { ++index4; }
                  if (index4 == (byte) 8)
                    break;
                }
              }
              checked { ++index3; }
            }
          }
          checked { ++index2; }
          if (index2 == (byte) 4)
            break;
        }
        checked { ++index1; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  public static byte agdb_info_data(
    string agdb_pfno,
    ref string[] agdb_names,
    ref byte[] agdb_addrs,
    ref byte cunt)
  {
    int index1 = 0;
    byte num1 = 0;
    try
    {
      byte index2 = checked ((byte) ((int) network_db_read.get_pfno_int(agdb_pfno) - 1));
      if (taddb_msg.no_of_agdbs_pfno[(int) index2] == (byte) 0)
      {
        num1 = (byte) 0;
      }
      else
      {
        while (index1 < (int) taddb_msg.no_of_agdbs_pfno[(int) index2])
        {
          agdb_names[index1] = taddb_msg.agdb_dis_brd[(int) index2, index1].agdb_name;
          agdb_addrs[index1] = taddb_msg.agdb_dis_brd[(int) index2, index1].agdb_addr;
          checked { ++index1; }
        }
        cunt = taddb_msg.no_of_agdbs_pfno[(int) index2];
        num1 = (byte) 1;
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num2 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    return num1;
  }

  public static void agdb_name_data(
    string agdb_name,
    ref byte agdb_addr,
    string agdb_pfno,
    ref byte video,
    ref bool shared_platform,
    ref string shared_platform_no)
  {
    int index1 = 0;
    try
    {
      byte index2 = checked ((byte) ((int) network_db_read.get_pfno_int(agdb_pfno) - 1));
      while (index1 < (int) taddb_msg.no_of_agdbs_pfno[(int) index2])
      {
        if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(agdb_name, taddb_msg.agdb_dis_brd[(int) index2, index1].agdb_name, false) == 0)
        {
          agdb_addr = taddb_msg.agdb_dis_brd[(int) index2, index1].agdb_addr;
          video = taddb_msg.agdb_dis_brd[(int) index2, index1].video_type;
          shared_platform = taddb_msg.agdb_dis_brd[(int) index2, index1].shared_platform;
          shared_platform_no = taddb_msg.agdb_dis_brd[(int) index2, index1].shared_platform_no;
          break;
        }
        checked { ++index1; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  public static void agdb_addr_data(
    ref string agdb_name,
    byte agdb_addr,
    string agdb_pfno,
    ref byte video,
    ref bool shared_platform,
    ref string shared_platform_no)
  {
    int index1 = 0;
    try
    {
      byte index2 = checked ((byte) ((int) network_db_read.get_pfno_int(agdb_pfno) - 1));
      while (index1 < (int) taddb_msg.no_of_agdbs_pfno[(int) index2])
      {
        if ((int) agdb_addr == (int) taddb_msg.agdb_dis_brd[(int) index2, index1].agdb_addr)
        {
          agdb_name = taddb_msg.agdb_dis_brd[(int) index2, index1].agdb_name;
          video = taddb_msg.agdb_dis_brd[(int) index2, index1].video_type;
          shared_platform = taddb_msg.agdb_dis_brd[(int) index2, index1].shared_platform;
          shared_platform_no = taddb_msg.agdb_dis_brd[(int) index2, index1].shared_platform_no;
          break;
        }
        checked { ++index1; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  public static void agdb_get_details_db(
    byte addr,
    string name,
    ref byte arg1,
    ref byte arg2,
    ref byte arg3,
    ref byte arg4,
    ref bool hub_type)
  {
    try
    {
      byte index1 = 0;
      while (index1 < (byte) 16 /*0x10*/)
      {
        if (frmMainFormIPIS.mdch_db.port_status[(int) index1])
        {
          byte index2 = 0;
          while ((uint) index2 < (uint) frmMainFormIPIS.mdch_db.mdch_port[(int) index1].no_of_systems)
          {
            if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.mdch_db.mdch_port[(int) index1].system_type[(int) index2], "AGDB", false) == 0)
            {
              if ((int) frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].dis_board_addr == (int) addr & Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].dis_board_name, name, false) == 0)
              {
                arg1 = index1;
                arg2 = index2;
                hub_type = true;
                return;
              }
            }
            else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.mdch_db.mdch_port[(int) index1].system_type[(int) index2], "PDCH", false) == 0)
            {
              byte index3 = 0;
              while (index3 < (byte) 16 /*0x10*/)
              {
                if (frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].port_status[(int) index3])
                {
                  byte index4 = 0;
                  while ((uint) index4 < (uint) frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].pdch_port[(int) index3].no_of_systems)
                  {
                    if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].pdch_port[(int) index3].dis_board[(int) index4].dis_board_type, "AGDB", false) == 0 & (int) frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].pdch_port[(int) index3].dis_board[(int) index4].dis_board_addr == (int) addr & Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].pdch_port[(int) index3].dis_board[(int) index4].dis_board_name, name, false) == 0)
                    {
                      arg1 = index1;
                      arg2 = index2;
                      arg3 = index3;
                      arg4 = index4;
                      return;
                    }
                    checked { ++index4; }
                    if (index4 == (byte) 8)
                      break;
                  }
                }
                checked { ++index3; }
              }
            }
            checked { ++index2; }
            if (index2 == (byte) 4)
              break;
          }
        }
        checked { ++index1; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  public static byte agdb_com_info_data(
    ref string[] agdb_names,
    ref byte[] agdb_addrs,
    ref byte cunt)
  {
    int index = 0;
    byte num1 = 0;
    try
    {
      // TODO: Fix type conversion - no_of_com_agdbs and agdb_com_dis_brd have type mismatches
      // if (taddb_msg.no_of_com_agdbs == (byte) 0)
      // {
        num1 = (byte) 0;
      // }
      // else
      // {
      //   while (index < (int) taddb_msg.no_of_com_agdbs)
      //   {
      //     agdb_names[index] = taddb_msg.agdb_com_dis_brd[index].agdb_name;
      //     agdb_addrs[index] = taddb_msg.agdb_com_dis_brd[index].agdb_addr;
      //     checked { ++index; }
      //   }
      //   cunt = taddb_msg.no_of_com_agdbs;
      //   num1 = (byte) 1;
      // }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num2 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    return num1;
  }

  public static void agdb_com_name_data(string agdb_name, ref string agdb_addr, ref byte video)
  {
    int index = 0;
    try
    {
      // TODO: Fix type conversion - no_of_com_agdbs and agdb_com_dis_brd have type mismatches
      // while (index < (int) taddb_msg.no_of_com_agdbs)
      // {
      //   if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(agdb_name, taddb_msg.agdb_com_dis_brd[index].agdb_name, false) == 0)
      //   {
      //     agdb_addr = Conversions.ToString(taddb_msg.agdb_com_dis_brd[index].agdb_addr);
      //     video = taddb_msg.agdb_com_dis_brd[index].video_type;
      //     break;
      //   }
      //   checked { ++index; }
      // }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  public static void agdb_com_addr_data(ref string agdb_name, string agdb_addr, ref byte video)
  {
    int index = 0;
    try
    {
      // TODO: Fix type conversion - no_of_com_agdbs and agdb_com_dis_brd have type mismatches
      // while (index < (int) taddb_msg.no_of_com_agdbs)
      // {
      //   if (Conversions.ToDouble(agdb_addr) == (double) taddb_msg.agdb_com_dis_brd[index].agdb_addr)
      //   {
      //     agdb_name = taddb_msg.agdb_com_dis_brd[index].agdb_name;
      //     video = taddb_msg.agdb_com_dis_brd[index].video_type;
      //     break;
      //   }
      //   checked { ++index; }
      // }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  public static void agdb_com_get_details_db(
    byte addr,
    string name,
    ref byte arg1,
    ref byte arg2,
    ref byte arg3,
    ref byte arg4,
    ref bool hub_type)
  {
    try
    {
      byte index1 = 0;
      while (index1 < (byte) 16 /*0x10*/)
      {
        if (frmMainFormIPIS.mdch_db.port_status[(int) index1])
        {
          byte index2 = 0;
          while ((uint) index2 < (uint) frmMainFormIPIS.mdch_db.mdch_port[(int) index1].no_of_systems)
          {
            if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.mdch_db.mdch_port[(int) index1].system_type[(int) index2], "AGDB", false) == 0)
            {
              if ((int) frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].dis_board_addr == (int) addr & Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].dis_board_name, name, false) == 0)
              {
                arg1 = index1;
                arg2 = index2;
                hub_type = true;
                return;
              }
            }
            else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.mdch_db.mdch_port[(int) index1].system_type[(int) index2], "PDCH", false) == 0)
            {
              byte index3 = 0;
              while (index3 < (byte) 16 /*0x10*/)
              {
                if (frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].port_status[(int) index3])
                {
                  byte index4 = 0;
                  while ((uint) index4 < (uint) frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].pdch_port[(int) index3].no_of_systems)
                  {
                    if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].pdch_port[(int) index3].dis_board[(int) index4].dis_board_type, "AGDB", false) == 0 & (int) frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].pdch_port[(int) index3].dis_board[(int) index4].dis_board_addr == (int) addr & Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].pdch_port[(int) index3].dis_board[(int) index4].dis_board_name, name, false) == 0)
                    {
                      arg1 = index1;
                      arg2 = index2;
                      arg3 = index3;
                      arg4 = index4;
                      return;
                    }
                    checked { ++index4; }
                    if (index4 == (byte) 8)
                      break;
                  }
                }
                checked { ++index3; }
              }
            }
            checked { ++index2; }
            if (index2 == (byte) 4)
              break;
          }
        }
        checked { ++index1; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  public static byte pdb_info_data(
    string platform_no,
    ref byte[] addr,
    ref string[] name,
    ref byte cunt,
    ref bool[] shared_platform,
    ref string[] shared_plaform_no,
    ref byte[] effect,
    ref byte[] video)
  {
    int index1 = 0;
    byte num1 = 0;
    try
    {
      byte index2 = checked ((byte) ((int) network_db_read.get_pfno_int(platform_no) - 1));
      if (taddb_msg.pdb_pf_status[(int) index2])
      {
        while (index1 < (int) taddb_msg.no_of_pdbs_pfno[(int) index2])
        {
          name[index1] = taddb_msg.pdb_dis_brd[(int) index2, index1].pdb_name;
          addr[index1] = taddb_msg.pdb_dis_brd[(int) index2, index1].pdb_addr;
          shared_platform[index1] = taddb_msg.pdb_dis_brd[(int) index2, index1].shared_platform;
          shared_plaform_no[index1] = taddb_msg.pdb_dis_brd[(int) index2, index1].shared_platform_no;
          effect[index1] = taddb_msg.pdb_dis_brd[(int) index2, index1].effect;
          video[index1] = taddb_msg.pdb_dis_brd[(int) index2, index1].video_type;
          checked { ++index1; }
        }
        cunt = taddb_msg.no_of_pdbs_pfno[(int) index2];
        num1 = (byte) 1;
      }
      else
        num1 = (byte) 0;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num2 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    return num1;
  }

  public static void pdb_name_data(
    string pdb_name,
    ref string pdb_addr,
    string pdb_pfno,
    ref byte effect,
    ref byte video,
    ref bool shared_platform,
    ref string shared_platform_no)
  {
    int index1 = 0;
    try
    {
      byte index2 = checked ((byte) ((int) network_db_read.get_pfno_int(pdb_pfno) - 1));
      while (index1 < (int) taddb_msg.no_of_pdbs_pfno[(int) index2])
      {
        if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(pdb_name, taddb_msg.pdb_dis_brd[(int) index2, index1].pdb_name, false) == 0)
        {
          pdb_addr = Conversions.ToString(taddb_msg.pdb_dis_brd[(int) index2, index1].pdb_addr);
          effect = taddb_msg.pdb_dis_brd[(int) index2, index1].effect;
          video = taddb_msg.pdb_dis_brd[(int) index2, index1].video_type;
          shared_platform = taddb_msg.pdb_dis_brd[(int) index2, index1].shared_platform;
          shared_platform_no = taddb_msg.pdb_dis_brd[(int) index2, index1].shared_platform_no;
          break;
        }
        checked { ++index1; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  public static void get_pdb_name(byte pdb_addr, ref string pdb_name, string pdb_pfno)
  {
    int index1 = 0;
    try
    {
      byte index2 = checked ((byte) ((int) network_db_read.get_pfno_int(pdb_pfno) - 1));
      while (index1 < (int) taddb_msg.no_of_pdbs_pfno[(int) index2])
      {
        if ((int) pdb_addr == (int) taddb_msg.pdb_dis_brd[(int) index2, index1].pdb_addr)
        {
          pdb_name = taddb_msg.pdb_dis_brd[(int) index2, index1].pdb_name;
          break;
        }
        checked { ++index1; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  public static void get_pdb_addr(string pdb_name, ref byte pdb_addr, string pdb_pfno)
  {
    int index1 = 0;
    try
    {
      byte index2 = checked ((byte) ((int) network_db_read.get_pfno_int(pdb_pfno) - 1));
      while (index1 < (int) taddb_msg.no_of_pdbs_pfno[(int) index2])
      {
        if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(pdb_name, taddb_msg.pdb_dis_brd[(int) index2, index1].pdb_name, false) == 0)
        {
          pdb_addr = taddb_msg.pdb_dis_brd[(int) index2, index1].pdb_addr;
          break;
        }
        checked { ++index1; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  public static void pdb_details(
    ref string pdb_addr,
    string pdb_pfno,
    ref byte effect,
    ref byte video,
    ref bool shared_platform,
    ref string shared_platform_no)
  {
    byte index = checked ((byte) ((int) network_db_read.get_pfno_int(pdb_pfno) - 1));
    if (taddb_msg.no_of_pdbs_pfno[(int) index] == (byte) 0)
      return;
    effect = taddb_msg.pdb_dis_brd[(int) index, 0].effect;
    video = taddb_msg.pdb_dis_brd[(int) index, 0].video_type;
    shared_platform = taddb_msg.pdb_dis_brd[(int) index, 0].shared_platform;
    shared_platform_no = taddb_msg.pdb_dis_brd[(int) index, 0].shared_platform_no;
    pdb_addr = Conversions.ToString(taddb_msg.pdb_dis_brd[(int) index, 0].multicast_addr);
  }

  public static void pdb_addr_data(
    ref string pdb_name,
    string pdb_addr,
    string pdb_pfno,
    ref byte effect,
    ref byte video,
    ref bool shared_platform,
    ref string shared_platform_no)
  {
    int index1 = 0;
    byte index2 = checked ((byte) ((int) network_db_read.get_pfno_int(pdb_pfno) - 1));
    try
    {
      while (index1 < (int) taddb_msg.no_of_pdbs_pfno[(int) index2])
      {
        if (Conversions.ToDouble(pdb_addr) == (double) taddb_msg.pdb_dis_brd[(int) index2, index1].pdb_addr)
        {
          pdb_name = taddb_msg.pdb_dis_brd[(int) index2, index1].pdb_name;
          effect = taddb_msg.pdb_dis_brd[(int) index2, index1].effect;
          video = taddb_msg.pdb_dis_brd[(int) index2, index1].video_type;
          shared_platform = taddb_msg.pdb_dis_brd[(int) index2, index1].shared_platform;
          shared_platform_no = taddb_msg.pdb_dis_brd[(int) index2, index1].shared_platform_no;
          break;
        }
        checked { ++index1; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  public static void pdb_name_addr_data(
    ref string[] pdb_name,
    ref byte[] pdb_addr,
    string pdb_pfno,
    ref byte cnt)
  {
    int index1 = 0;
    byte index2 = checked ((byte) ((int) network_db_read.get_pfno_int(pdb_pfno) - 1));
    cnt = taddb_msg.no_of_pdbs_pfno[(int) index2];
    try
    {
      while (index1 < (int) taddb_msg.no_of_pdbs_pfno[(int) index2])
      {
        pdb_addr[index1] = taddb_msg.pdb_dis_brd[(int) index2, index1].pdb_addr;
        pdb_name[index1] = taddb_msg.pdb_dis_brd[(int) index2, index1].pdb_name;
        checked { ++index1; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  public static void pdb_get_details_db(
    byte addr,
    string name,
    ref bool hub_type,
    ref byte arg1,
    ref byte arg2,
    ref byte arg3,
    ref byte arg4)
  {
    byte index1 = 0;
    try
    {
      while (index1 < (byte) 16 /*0x10*/)
      {
        if (frmMainFormIPIS.mdch_db.port_status[(int) index1])
        {
          byte index2 = 0;
          while ((uint) index2 < (uint) frmMainFormIPIS.mdch_db.mdch_port[(int) index1].no_of_systems)
          {
            if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.mdch_db.mdch_port[(int) index1].system_type[(int) index2], "PDB", false) == 0)
            {
              if ((int) frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].multicast_addr == (int) addr & Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].dis_board_name, name, false) == 0)
              {
                arg1 = index1;
                arg2 = index2;
                hub_type = true;
                return;
              }
            }
            else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.mdch_db.mdch_port[(int) index1].system_type[(int) index2], "PDCH", false) == 0)
            {
              byte index3 = 0;
              while (index3 < (byte) 16 /*0x10*/)
              {
                if (frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].port_status[(int) index3])
                {
                  byte index4 = 0;
                  while ((uint) index4 < (uint) frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].pdch_port[(int) index3].no_of_systems)
                  {
                    if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].pdch_port[(int) index3].dis_board[(int) index4].dis_board_type, "PDB", false) == 0 & (int) frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].pdch_port[(int) index3].dis_board[(int) index4].multicast_addr == (int) addr & Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].pdch_port[(int) index3].dis_board[(int) index4].dis_board_name, name, false) == 0)
                    {
                      arg1 = index1;
                      arg2 = index2;
                      arg3 = index3;
                      arg4 = index4;
                      return;
                    }
                    checked { ++index4; }
                    if (index4 == (byte) 8)
                      break;
                  }
                }
                checked { ++index3; }
              }
            }
            checked { ++index2; }
            if (index2 == (byte) 4)
              break;
          }
        }
        checked { ++index1; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  public static byte mldb_info_data(ref string[] mldb_name, ref byte[] mldb_addr, ref byte cnt)
  {
    int index = 0;
    byte num1 = 0;
    try
    {
      while (index < (int) taddb_msg.mldb_dis_brd.no_of_mldbs)
      {
        mldb_name[index] = taddb_msg.mldb_dis_brd.mdlb[index].mldb_name;
        mldb_addr[index] = taddb_msg.mldb_dis_brd.mdlb[index].mldb_addr;
        checked { ++index; }
      }
      cnt = taddb_msg.mldb_dis_brd.no_of_mldbs;
      num1 = index != 0 ? (byte) 1 : (byte) 0;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num2 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    return num1;
  }

  public static void mldb_addr_data(ref string mldb_name, string mldb_addr)
  {
    int index = 0;
    try
    {
      while (index < (int) taddb_msg.mldb_dis_brd.no_of_mldbs)
      {
        if (Conversions.ToDouble(mldb_addr) == (double) taddb_msg.mldb_dis_brd.mdlb[index].mldb_addr)
        {
          mldb_name = taddb_msg.mldb_dis_brd.mdlb[index].mldb_name;
          break;
        }
        checked { ++index; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  public static void mldb_name_data(string mldb_name, ref string mldb_addr)
  {
    int index = 0;
    try
    {
      while (index < (int) taddb_msg.mldb_dis_brd.no_of_mldbs)
      {
        if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(mldb_name, taddb_msg.mldb_dis_brd.mdlb[index].mldb_name, false) == 0)
        {
          mldb_addr = Conversions.ToString(taddb_msg.mldb_dis_brd.mdlb[index].mldb_addr);
          break;
        }
        checked { ++index; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  public static void mldb_get_details_db(byte addr, string name, ref byte arg1, ref byte arg2)
  {
    try
    {
      byte index1 = 0;
      while (index1 < (byte) 16 /*0x10*/)
      {
        if (frmMainFormIPIS.mdch_db.port_status[(int) index1])
        {
          byte index2 = 0;
          while ((uint) index2 < (uint) frmMainFormIPIS.mdch_db.mdch_port[(int) index1].no_of_systems)
          {
            if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.mdch_db.mdch_port[(int) index1].system_type[(int) index2], "MLDB", false) == 0 && (int) frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].dis_board_addr == (int) addr & Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].dis_board_name, name, false) == 0)
            {
              arg1 = index1;
              arg2 = index2;
              return;
            }
            checked { ++index2; }
          }
        }
        checked { ++index1; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  public static void enc_pwd(string pwd, ref string encrypted)
  {
    try
    {
      int hashCode = pwd.GetHashCode();
      byte[] bytes = Encoding.BigEndianUnicode.GetBytes(pwd + Conversions.ToString(hashCode));
      encrypted = Convert.ToBase64String(bytes);
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  public static void dec_pwd(string encrypted_pwd, ref string decrypted_pwd, string pwd_length)
  {
    try
    {
      byte[] bytes = Convert.FromBase64String(encrypted_pwd);
      decrypted_pwd = Encoding.BigEndianUnicode.GetString(bytes);
      decrypted_pwd = decrypted_pwd.Substring(0, Conversions.ToInteger(pwd_length));
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  public static void get_train_info()
  {
    OleDbCommand oleDbCommand1 = new OleDbCommand();
    OleDbCommand oleDbCommand2 = new OleDbCommand();
    try
    {
      if (connection_Database.con1.State == ConnectionState.Closed)
        connection_Database.con1.Open();
      oleDbCommand1.CommandText = " (select * from trainconfigtable )";
      oleDbCommand1.CommandType = CommandType.Text;
      oleDbCommand1.Connection = connection_Database.con1;
      OleDbDataReader oleDbDataReader1 = oleDbCommand1.ExecuteReader();
      frmMainFormIPIS.train_cnt = 0;
      if (oleDbDataReader1.HasRows)
      {
        while (oleDbDataReader1.Read())
        {
          frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].train_no = Strings.Trim(Conversions.ToString(oleDbDataReader1[0]));
          frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].train_name = Strings.Trim(Conversions.ToString(oleDbDataReader1[1]));
          frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].train_name_reg = Strings.Trim(Conversions.ToString(oleDbDataReader1[2]));
          frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].train_name_hin = Strings.Trim(Conversions.ToString(oleDbDataReader1[3]));
          frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].start_station = Strings.Trim(Conversions.ToString(oleDbDataReader1[4]));
          frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].end_Station = Strings.Trim(Conversions.ToString(oleDbDataReader1[5]));
          frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].arr_time = Strings.Trim(Conversions.ToString(oleDbDataReader1[6]));
          frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].dep_time = Strings.Trim(Conversions.ToString(oleDbDataReader1[7]));
          frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].pf_no = Strings.Trim(Conversions.ToString(oleDbDataReader1[8]));
          frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].language_name = Strings.Trim(Conversions.ToString(oleDbDataReader1[9]));
          if (oleDbDataReader1[10] != DBNull.Value)
            frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].dir = Strings.Trim(Conversions.ToString(oleDbDataReader1[10]));
          if (oleDbDataReader1[11] != DBNull.Value)
            frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].station_pos = Strings.Trim(Conversions.ToString(oleDbDataReader1[11]));
          oleDbCommand2.CommandText = "select * from daysofweek where  Trainno = '{frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].train_no}' ";
          oleDbCommand2.CommandType = CommandType.Text;
          oleDbCommand2.Connection = connection_Database.con1;
          OleDbDataReader oleDbDataReader2 = oleDbCommand2.ExecuteReader();
          if (oleDbDataReader2.HasRows)
          {
            while (oleDbDataReader2.Read())
            {
              frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].daily = Conversions.ToInteger(oleDbDataReader2[8]);
              frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].specificdays = Conversions.ToInteger(oleDbDataReader2[9]);
              int index = 0;
              while (index < 7)
              {
                frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].days[index] = Conversions.ToString(oleDbDataReader2[checked (index + 1)]);
                checked { ++index; }
              }
            }
          }
          oleDbDataReader2.Close();
          oleDbCommand2.CommandText = "select * from ftdate where  Trainno = '{frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].train_no}' ";
          oleDbCommand2.CommandType = CommandType.Text;
          oleDbCommand2.Connection = connection_Database.con1;
          OleDbDataReader oleDbDataReader3 = oleDbCommand2.ExecuteReader();
          if (oleDbDataReader3.HasRows)
          {
            while (oleDbDataReader3.Read())
            {
              frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].fromdt = Conversions.ToDate(oleDbDataReader3[1]);
              frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].todt = Conversions.ToDate(oleDbDataReader3[2]);
              frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].period = Conversions.ToInteger(oleDbDataReader3[3]);
            }
          }
          oleDbDataReader3.Close();
          oleDbCommand2.CommandText = "select * from specificdate where  Trainno = '{frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].train_no}' ";
          oleDbCommand2.CommandType = CommandType.Text;
          oleDbCommand2.Connection = connection_Database.con1;
          OleDbDataReader oleDbDataReader4 = oleDbCommand2.ExecuteReader();
          if (oleDbDataReader4.HasRows)
          {
            while (oleDbDataReader4.Read())
            {
              int index = 0;
              frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].specificdate = Conversions.ToInteger(oleDbDataReader4[11]);
              while (index < 10)
              {
                frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].specificdates[index] = Conversions.ToDate(oleDbDataReader4[checked (index + 1)]);
                checked { ++index; }
              }
            }
          }
          oleDbDataReader4.Close();
          oleDbCommand2.CommandText = "select * from cgstble where  Trainno = '{frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].train_no}' ";
          oleDbCommand2.CommandType = CommandType.Text;
          oleDbCommand2.Connection = connection_Database.con1;
          OleDbDataReader oleDbDataReader5 = oleDbCommand2.ExecuteReader();
          if (oleDbDataReader5.HasRows)
          {
            while (oleDbDataReader5.Read())
            {
              int index = 0;
              while (index < 26)
              {
                frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].cgs_inf[index] = Strings.Trim(Conversions.ToString(oleDbDataReader5[checked (index + 1)]));
                checked { ++index; }
              }
            }
          }
          oleDbDataReader5.Close();
          checked { ++frmMainFormIPIS.train_cnt; }
        }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con1.State != ConnectionState.Open)
      return;
    connection_Database.con1.Close();
  }

  public static void set_train_info()
  {
    int num1 = 0;
    OleDbDataAdapter oleDbDataAdapter1 = new OleDbDataAdapter();
    OleDbDataAdapter oleDbDataAdapter2 = new OleDbDataAdapter();
    OleDbDataAdapter oleDbDataAdapter3 = new OleDbDataAdapter();
    OleDbDataAdapter oleDbDataAdapter4 = new OleDbDataAdapter();
    OleDbDataAdapter oleDbDataAdapter5 = new OleDbDataAdapter();
    OleDbDataAdapter oleDbDataAdapter6 = new OleDbDataAdapter();
    OleDbCommand oleDbCommand1 = new OleDbCommand();
    OleDbCommand oleDbCommand2 = new OleDbCommand();
    OleDbCommand oleDbCommand3 = new OleDbCommand();
    OleDbCommand oleDbCommand4 = new OleDbCommand();
    OleDbCommand oleDbCommand5 = new OleDbCommand();
    OleDbCommand oleDbCommand6 = new OleDbCommand();
    try
    {
      if (connection_Database.con1.State == ConnectionState.Closed)
        connection_Database.con1.Open();
      oleDbCommand1.CommandText = " (insert into trainconfigtable(Trainno,Trainname,Trainnametel,Trainnamehin,StartStation,Endstation,Arrivaltime,Departuretime,PFno,language_name,Direction,StationPosition) values ('{frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].train_no} ','{frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].train_name} ', '{frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].train_name_reg} ' ,'{frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].train_name_hin}', '{frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].start_station} ', ' {frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].end_Station} ', '{frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].arr_time} ', '{frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].dep_time} ', '{frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].pf_no} ' , '{frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].language_name} ','{frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].dir}','{frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].station_pos}') )";
      oleDbCommand2.CommandText = "insert into ftdate(Trainno,fromdt,todt,period) Values('{frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].train_no} ' , '{Conversions.ToString(frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].fromdt)}' ,'{Conversions.ToString(frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].todt)}' ,'{Conversions.ToString(frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].period)}')";
      oleDbCommand3.CommandText = "insert into daysofweek(Trainno,Sunday,Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,daily,specificdays) values ('{frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].train_no} ' ,'{frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].days[0]}', '{frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].days[1]}','{frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].days[2]}','{frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].days[3]}','{frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].days[4]} ' , '{frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].days[5]}','{frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].days[6]}','{Conversions.ToString(frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].daily)}','{Conversions.ToString(frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].specificdays)}')";
      oleDbCommand4.CommandText = "insert into specificdate(Trainno,d1,d2,d3,d4,d5,d6,d7,d8,d9,d10,specificdates) values ('{frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].train_no} ' ,'{Conversions.ToString(frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].specificdates[0])}' ,'{Conversions.ToString(frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].specificdates[1])}' ,'{Conversions.ToString(frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].specificdates[2])}' ,'{Conversions.ToString(frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].specificdates[3])}' ,'{Conversions.ToString(frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].specificdates[4])}' ,'{Conversions.ToString(frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].specificdates[5])}','{Conversions.ToString(frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].specificdates[6])}' ,'{Conversions.ToString(frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].specificdates[7])}' ,'{Conversions.ToString(frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].specificdates[8])}' , '{Conversions.ToString(frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].specificdates[9])}' , '{Conversions.ToString(frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].specificdate)}')";
      oleDbCommand5.CommandText = "insert into cgstble(Trainno,t1,t2,t3,t4,t5,t6,t7,t8,t9,t10,t11,t12,t13,t14,t15,t16,t17,t18,t19,t20,t21,t22,t23,t24,t25,t26) values ('{frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].train_no} ' , '{frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].cgs_inf[0]}', '{frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].cgs_inf[1]}', '{frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].cgs_inf[2]}', '{frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].cgs_inf[3]}', '{frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].cgs_inf[4]}', '{frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].cgs_inf[5]}', '{frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].cgs_inf[6]}', '{frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].cgs_inf[7]}', '{frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].cgs_inf[8]}', '{frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].cgs_inf[9]}', '{frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].cgs_inf[10]}', '{frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].cgs_inf[11]}', '{frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].cgs_inf[12]}', '{frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].cgs_inf[13]}', '{frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].cgs_inf[14]}', '{frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].cgs_inf[15]}', '{frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].cgs_inf[16 /*0x10*/]}', '{frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].cgs_inf[17]}', '{frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].cgs_inf[18]}', '{frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].cgs_inf[19]}', '{frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].cgs_inf[20]}', '{frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].cgs_inf[21]}', '{frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].cgs_inf[22]}', '{frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].cgs_inf[23]}', '{frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].cgs_inf[24]}', '{frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].cgs_inf[25]}')";
      oleDbCommand1.Connection = connection_Database.con1;
      oleDbCommand2.Connection = connection_Database.con1;
      oleDbCommand3.Connection = connection_Database.con1;
      oleDbCommand4.Connection = connection_Database.con1;
      oleDbCommand5.Connection = connection_Database.con1;
      OleDbTransaction oleDbTransaction = connection_Database.con1.BeginTransaction();
      oleDbCommand1.Transaction = oleDbTransaction;
      oleDbCommand2.Transaction = oleDbTransaction;
      oleDbCommand3.Transaction = oleDbTransaction;
      oleDbCommand4.Transaction = oleDbTransaction;
      oleDbCommand5.Transaction = oleDbTransaction;
      num1 = 0;
      oleDbDataAdapter1.InsertCommand = oleDbCommand1;
      num1 = oleDbDataAdapter1.InsertCommand.ExecuteNonQuery();
      num1 = 0;
      oleDbDataAdapter2.InsertCommand = oleDbCommand2;
      num1 = oleDbDataAdapter2.InsertCommand.ExecuteNonQuery();
      num1 = 0;
      oleDbDataAdapter3.InsertCommand = oleDbCommand3;
      num1 = oleDbDataAdapter3.InsertCommand.ExecuteNonQuery();
      num1 = 0;
      oleDbDataAdapter4.InsertCommand = oleDbCommand4;
      num1 = oleDbDataAdapter4.InsertCommand.ExecuteNonQuery();
      num1 = 0;
      oleDbDataAdapter5.InsertCommand = oleDbCommand5;
      num1 = oleDbDataAdapter5.InsertCommand.ExecuteNonQuery();
      int num2 = (int) MessageBox.Show("Train Information saved");
      MyProject.Forms.frmMainFormIPIS.dgvTrainno.Items.Add((object) frmMainFormIPIS.train_details[frmMainFormIPIS.train_cnt].train_no);
      try
      {
        oleDbTransaction.Commit();
      }
      catch (Exception ex)
      {
        ProjectData.SetProjectError(ex);
        oleDbTransaction.Rollback();
        ProjectData.ClearProjectError();
      }
      checked { ++frmMainFormIPIS.train_cnt; }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num3 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con1.State != ConnectionState.Open)
      return;
    connection_Database.con1.Close();
  }

  public static void delete_train_info(string train_no)
  {
    OleDbCommand oleDbCommand1 = new OleDbCommand();
    OleDbCommand oleDbCommand2 = new OleDbCommand();
    OleDbCommand oleDbCommand3 = new OleDbCommand();
    OleDbCommand oleDbCommand4 = new OleDbCommand();
    OleDbCommand oleDbCommand5 = new OleDbCommand();
    try
    {
      if (connection_Database.con1.State == ConnectionState.Closed)
        connection_Database.con1.Open();
      oleDbCommand1.CommandText = "Delete from trainconfigtable  where  trainconfigtable.Trainno = '{Strings.Trim(train_no)}' ";
      oleDbCommand2.CommandText = "Delete from cgstble where Trainno = '{Strings.Trim(train_no)} '";
      oleDbCommand3.CommandText = "Delete from daysofweek where Trainno ='{Strings.Trim(train_no)}' ";
      oleDbCommand4.CommandText = "Delete  from ftdate where Trainno = '{Strings.Trim(train_no)}' ";
      oleDbCommand5.CommandText = "Delete  from specificdate where Trainno = '{Strings.Trim(train_no)} '";
      oleDbCommand1.Connection = connection_Database.con1;
      oleDbCommand2.Connection = connection_Database.con1;
      oleDbCommand3.Connection = connection_Database.con1;
      oleDbCommand4.Connection = connection_Database.con1;
      oleDbCommand5.Connection = connection_Database.con1;
      OleDbTransaction oleDbTransaction = connection_Database.con1.BeginTransaction();
      oleDbCommand1.Transaction = oleDbTransaction;
      oleDbCommand2.Transaction = oleDbTransaction;
      oleDbCommand3.Transaction = oleDbTransaction;
      oleDbCommand4.Transaction = oleDbTransaction;
      oleDbCommand5.Transaction = oleDbTransaction;
      oleDbCommand1.ExecuteNonQuery();
      oleDbCommand2.ExecuteNonQuery();
      oleDbCommand3.ExecuteNonQuery();
      oleDbCommand4.ExecuteNonQuery();
      oleDbCommand5.ExecuteNonQuery();
      int num = (int) MessageBox.Show("trainno {train_no}deleted");
      try
      {
        oleDbTransaction.Commit();
      }
      catch (Exception ex)
      {
        ProjectData.SetProjectError(ex);
        oleDbTransaction.Rollback();
        ProjectData.ClearProjectError();
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con1.State != ConnectionState.Open)
      return;
    connection_Database.con1.Close();
  }

  public static void update_train_info(int i)
  {
    int num1 = 0;
    int num2 = 0;
    int num3 = 0;
    int num4 = 0;
    int num5 = 0;
    OleDbDataAdapter oleDbDataAdapter = new OleDbDataAdapter();
    OleDbCommand oleDbCommand1 = new OleDbCommand();
    try
    {
      if (connection_Database.con1.State == ConnectionState.Closed)
        connection_Database.con1.Open();
      if (DateTime.Compare(frmTrainDetails.ftdate[0], DateTime.MinValue) == 0)
        frmTrainDetails.ftdate[0] = DateTime.MinValue;
      if (DateTime.Compare(frmTrainDetails.ftdate[1], DateTime.MinValue) == 0)
        frmTrainDetails.ftdate[1] = DateTime.MinValue;
      int index = 0;
      while (index < 10)
      {
        if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmTrainDetails.specificdate[index], (string) null, false) == 0)
          frmTrainDetails.specificdate[index] = Conversions.ToString(DateTime.MinValue);
        checked { ++index; }
      }
      OleDbCommand oleDbCommand2 = new OleDbCommand("(Update trainconfigtable Set Trainname ='{frmMainFormIPIS.train_details[i].train_name} ' ,Trainnametel = '{frmMainFormIPIS.train_details[i].train_name_reg} ',Trainnamehin = '{frmMainFormIPIS.train_details[i].train_name_hin} ', StartStation = '{frmMainFormIPIS.train_details[i].start_station} ' , Endstation = '{frmMainFormIPIS.train_details[i].end_Station} ', Arrivaltime = '{frmMainFormIPIS.train_details[i].arr_time} ', Departuretime = '{frmMainFormIPIS.train_details[i].dep_time} ', PFno = '{frmMainFormIPIS.train_details[i].pf_no}' , language_name = '{frmMainFormIPIS.train_details[i].language_name} ',Direction = '{frmMainFormIPIS.train_details[i].dir}',StationPosition = '{frmMainFormIPIS.train_details[i].station_pos}'  where Trainno = '{frmMainFormIPIS.train_details[i].train_no}' )", connection_Database.con1);
      OleDbCommand oleDbCommand3 = new OleDbCommand("(Update  cgstble Set  t1 = '{frmMainFormIPIS.train_details[i].cgs_inf[0]}',  t2 = '{frmMainFormIPIS.train_details[i].cgs_inf[1]}', t3 = '{frmMainFormIPIS.train_details[i].cgs_inf[2]}', t4 = '{frmMainFormIPIS.train_details[i].cgs_inf[3]}', t5 = '{frmMainFormIPIS.train_details[i].cgs_inf[4]}', t6 = '{frmMainFormIPIS.train_details[i].cgs_inf[5]}', t7 = '{frmMainFormIPIS.train_details[i].cgs_inf[6]}', t8 = '{frmMainFormIPIS.train_details[i].cgs_inf[7]}', t9 = '{frmMainFormIPIS.train_details[i].cgs_inf[8]}', t10 ='{frmMainFormIPIS.train_details[i].cgs_inf[9]}', t11 = '{frmMainFormIPIS.train_details[i].cgs_inf[10]}', t12 = '{frmMainFormIPIS.train_details[i].cgs_inf[11]}', t13 = '{frmMainFormIPIS.train_details[i].cgs_inf[12]}', t14 = '{frmMainFormIPIS.train_details[i].cgs_inf[13]}', t15 = '{frmMainFormIPIS.train_details[i].cgs_inf[14]}', t16 = '{frmMainFormIPIS.train_details[i].cgs_inf[15]}', t17 = '{frmMainFormIPIS.train_details[i].cgs_inf[16 /*0x10*/]}', t18 = '{frmMainFormIPIS.train_details[i].cgs_inf[17]}', t19 = '{frmMainFormIPIS.train_details[i].cgs_inf[18]}', t20 = '{frmMainFormIPIS.train_details[i].cgs_inf[19]}', t21 = '{frmMainFormIPIS.train_details[i].cgs_inf[20]}', t22 = '{frmMainFormIPIS.train_details[i].cgs_inf[21]}', t23 = '{frmMainFormIPIS.train_details[i].cgs_inf[22]}', t24 = '{frmMainFormIPIS.train_details[i].cgs_inf[23]}', t25 = '{frmMainFormIPIS.train_details[i].cgs_inf[24]}', t26 = '{frmMainFormIPIS.train_details[i].cgs_inf[25]}' where Trainno = '{frmMainFormIPIS.train_details[i].train_no}' )", connection_Database.con1);
      OleDbCommand oleDbCommand4 = new OleDbCommand("(Update daysofweek Set Sunday = '{frmMainFormIPIS.train_details[i].days[0]}',  Monday = '{frmMainFormIPIS.train_details[i].days[1]}',  Tuesday = '{frmMainFormIPIS.train_details[i].days[2]}',  Wednesday = '{frmMainFormIPIS.train_details[i].days[3]}', Thursday = '{frmMainFormIPIS.train_details[i].days[4]} ' ,  Friday = '{frmMainFormIPIS.train_details[i].days[5]}', Saturday = '{frmMainFormIPIS.train_details[i].days[6]}',daily =  '{Conversions.ToString(frmMainFormIPIS.train_details[i].daily)}' ,specificdays =  '{Conversions.ToString(frmMainFormIPIS.train_details[i].specificdays)}' where daysofweek.Trainno = '{frmMainFormIPIS.train_details[i].train_no}' )", connection_Database.con1);
      OleDbCommand oleDbCommand5 = new OleDbCommand("(Update ftdate Set fromdt = '{Conversions.ToString(frmMainFormIPIS.train_details[i].fromdt)}',  todt = '{Conversions.ToString(frmMainFormIPIS.train_details[i].todt)}',period=  '{Conversions.ToString(frmMainFormIPIS.train_details[i].period)}' where Trainno ='{frmMainFormIPIS.train_details[i].train_no}' )", connection_Database.con1);
      OleDbCommand oleDbCommand6 = new OleDbCommand("(Update specificdate Set d1 = '{Conversions.ToString(frmMainFormIPIS.train_details[i].specificdates[0])}', d2 =  '{Conversions.ToString(frmMainFormIPIS.train_details[i].specificdates[1])}',  d3 = '{Conversions.ToString(frmMainFormIPIS.train_details[i].specificdates[2])}' , d4 = '{Conversions.ToString(frmMainFormIPIS.train_details[i].specificdates[3])}' ,  d5 = '{Conversions.ToString(frmMainFormIPIS.train_details[i].specificdates[4])} ' ,  d6 = '{Conversions.ToString(frmMainFormIPIS.train_details[i].specificdates[5])}',  d7 = '{Conversions.ToString(frmMainFormIPIS.train_details[i].specificdates[6])}' , d8 = '{Conversions.ToString(frmMainFormIPIS.train_details[i].specificdates[7])} ' , d9 = '{Conversions.ToString(frmMainFormIPIS.train_details[i].specificdates[8])}', d10 = '{Conversions.ToString(frmMainFormIPIS.train_details[i].specificdates[9])}',specificdates = '{Conversions.ToString(frmMainFormIPIS.train_details[i].specificdate)}' where Trainno = '{frmMainFormIPIS.train_details[i].train_no}' )", connection_Database.con1);
      OleDbTransaction oleDbTransaction = connection_Database.con1.BeginTransaction();
      oleDbCommand2.Transaction = oleDbTransaction;
      oleDbCommand3.Transaction = oleDbTransaction;
      oleDbCommand4.Transaction = oleDbTransaction;
      oleDbCommand5.Transaction = oleDbTransaction;
      oleDbCommand6.Transaction = oleDbTransaction;
      num1 = oleDbCommand2.ExecuteNonQuery();
      num2 = oleDbCommand3.ExecuteNonQuery();
      num3 = oleDbCommand4.ExecuteNonQuery();
      num4 = oleDbCommand5.ExecuteNonQuery();
      num5 = oleDbCommand6.ExecuteNonQuery();
      int num6 = (int) MessageBox.Show("Records updated ");
      try
      {
        oleDbTransaction.Commit();
      }
      catch (Exception ex)
      {
        ProjectData.SetProjectError(ex);
        oleDbTransaction.Rollback();
        ProjectData.ClearProjectError();
      }
      if (connection_Database.con1.State != ConnectionState.Open)
        return;
      connection_Database.con1.Close();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num7 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      if (connection_Database.con1.State == ConnectionState.Open)
        connection_Database.con1.Close();
      ProjectData.ClearProjectError();
    }
  }

  public static void get_train_name(
    string train_no,
    ref string train_name,
    ref string AD,
    ref string adtime)
  {
    int index = 0;
    while (index < frmMainFormIPIS.online_train_cnt)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.online_train_data[index].train_no, Strings.Trim(train_no), false) == 0)
      {
        train_name = frmMainFormIPIS.online_train_data[index].train_name;
        if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.online_train_data[index].AD, "A", false) == 0)
        {
          AD = "A";
          adtime = frmMainFormIPIS.online_train_data[index].exp_arr_time;
          break;
        }
        AD = "D";
        adtime = frmMainFormIPIS.online_train_data[index].exp_dep_time;
        break;
      }
      checked { ++index; }
    }
  }

  public static void user_info()
  {
    int index1 = 0;
    OleDbCommand oleDbCommand = new OleDbCommand();
    try
    {
      if (connection_Database.con5.State == ConnectionState.Closed)
        connection_Database.con5.Open();
      frmMainFormIPIS.user_cnt.cnt = (byte) 0;
      while (index1 < 50)
      {
        frmMainFormIPIS.user_details[index1].user_id = string.Empty;
        frmMainFormIPIS.user_details[index1].user_name = string.Empty;
        frmMainFormIPIS.user_details[index1].pwd = string.Empty;
        frmMainFormIPIS.user_details[index1].pwd_length = (byte) 0;
        frmMainFormIPIS.user_details[index1].group = string.Empty;
        checked { ++index1; }
      }
      oleDbCommand.CommandText = " (select * from logintable )";
      oleDbCommand.CommandType = CommandType.Text;
      oleDbCommand.Connection = connection_Database.con5;
      OleDbDataReader oleDbDataReader = oleDbCommand.ExecuteReader();
      int index2 = 0;
      if (oleDbDataReader.HasRows)
      {
        while (oleDbDataReader.Read())
        {
          frmMainFormIPIS.user_details[index2].user_id = Conversions.ToString(oleDbDataReader[0]);
          frmMainFormIPIS.user_details[index2].user_name = Conversions.ToString(oleDbDataReader[1]);
          frmMainFormIPIS.user_details[index2].pwd = Conversions.ToString(oleDbDataReader[2]);
          frmMainFormIPIS.user_details[index2].pwd_length = Conversions.ToByte(oleDbDataReader[3]);
          frmMainFormIPIS.user_details[index2].group = oleDbDataReader[4] != DBNull.Value ? Conversions.ToString(oleDbDataReader[4]) : string.Empty;
          checked { ++index2; }
        }
      }
      frmMainFormIPIS.user_cnt.cnt = checked ((byte) index2);
      oleDbDataReader.Close();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con5.State != ConnectionState.Open)
      return;
    connection_Database.con5.Close();
  }

  public static bool check_com_info()
  {
    OleDbCommand oleDbCommand = new OleDbCommand();
    string portname = "COM1";
    int Spd = 57600;
    RS232 rs232 = new RS232();
    bool flag = false;
    try
    {
      if (connection_Database.con3.State == ConnectionState.Closed)
        connection_Database.con3.Open();
      oleDbCommand.CommandText = "select * from stationdetails";
      oleDbCommand.CommandType = CommandType.Text;
      oleDbCommand.Connection = connection_Database.con3;
      OleDbDataReader oleDbDataReader = oleDbCommand.ExecuteReader();
      while (oleDbDataReader.Read())
      {
        portname = oleDbDataReader[6].ToString();
        Spd = Conversions.ToInteger(oleDbDataReader[7]);
      }
      oleDbDataReader.Close();
      int num = rs232.Serial_Open(portname, Spd, RS232.enumParity.None, 8, RS232.enumStopBits.One);
      if (connection_Database.con3.State == ConnectionState.Open)
        connection_Database.con3.Close();
      flag = num == 1;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    return flag;
  }

  public static void intensity_info()
  {
    OleDbCommand oleDbCommand = new OleDbCommand();
    try
    {
      if (connection_Database.con3.State == ConnectionState.Closed)
        connection_Database.con3.Open();
      oleDbCommand.CommandText = "select * from stationdetails";
      oleDbCommand.CommandType = CommandType.Text;
      oleDbCommand.Connection = connection_Database.con3;
      OleDbDataReader oleDbDataReader = oleDbCommand.ExecuteReader();
      while (oleDbDataReader.Read())
      {
        frmMainFormIPIS.day_intensity = Conversions.ToByte(oleDbDataReader[5]);
        if (oleDbDataReader[11] != DBNull.Value)
          frmMainFormIPIS.night_intensity = Conversions.ToByte(oleDbDataReader[11]);
        frmMainFormIPIS.agdb_message_video = Conversions.ToString(oleDbDataReader[8]);
      }
      oleDbDataReader.Close();
      string Left = Strings.Format((object) DateAndTime.TimeOfDay, "HH:mm");
      frmMainFormIPIS.intensity = !(Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Left, "06:00", false) >= 0 & Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Left, "18:00", false) <= 0) ? frmMainFormIPIS.night_intensity : frmMainFormIPIS.day_intensity;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con3.State != ConnectionState.Open)
      return;
    connection_Database.con3.Close();
  }

  public static void set_intensity(
    byte day_intensity,
    byte night_intensity,
    DateTime day_time,
    DateTime night_time)
  {
    OleDbDataAdapter oleDbDataAdapter = new OleDbDataAdapter();
    OleDbCommand oleDbCommand1 = new OleDbCommand();
    OleDbCommand oleDbCommand2 = new OleDbCommand();
    int num1 = 0;
    try
    {
      if (connection_Database.con3.State == ConnectionState.Closed)
        connection_Database.con3.Open();
      day_time = Conversions.ToDate(Strings.Format((object) day_time, "HH:mm"));
      night_time = Conversions.ToDate(Strings.Format((object) night_time, "HH:mm"));
      oleDbCommand1.CommandText = "(update  stationdetails set day_Intensity =  {Conversions.ToString(day_intensity)}   , night_Intensity =  {Conversions.ToString(night_intensity)} , day_time =  '{Conversions.ToString(day_time)}', night_time =  '{Conversions.ToString(night_time)} '  )";
      oleDbCommand1.Connection = connection_Database.con3;
      oleDbDataAdapter.UpdateCommand = oleDbCommand1;
      if (oleDbDataAdapter.UpdateCommand.ExecuteNonQuery() == 0)
      {
        oleDbCommand2.CommandText = "insert into stationdetails(day_Intensity,night_Intensity,day_time,night_time) values ( {Conversions.ToString(day_intensity)}  ,  {Conversions.ToString(night_intensity)}, '{Conversions.ToString(day_time)}', '{Conversions.ToString(night_time)}')";
        oleDbCommand2.CommandType = CommandType.Text;
        oleDbCommand2.Connection = connection_Database.con12;
        oleDbDataAdapter.InsertCommand = oleDbCommand2;
        num1 = oleDbDataAdapter.InsertCommand.ExecuteNonQuery();
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num2 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con3.State == ConnectionState.Open)
      connection_Database.con3.Close();
    Strings.Format((object) DateTime.Now.Date, "dd-MM-yyyy");
    try
    {
      string path = "Z:\\Database\\StationDetails.mdb";
      if (!File.Exists(path))
        File.Create(path);
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
  }

  public static void get_intensity(
    ref byte day_intensity,
    ref byte night_intensity,
    ref DateTime day_time,
    ref DateTime night_time)
  {
    OleDbCommand oleDbCommand = new OleDbCommand();
    try
    {
      if (connection_Database.con3.State == ConnectionState.Closed)
        connection_Database.con3.Open();
      oleDbCommand.CommandText = "(select * from stationdetails  )";
      oleDbCommand.CommandType = CommandType.Text;
      oleDbCommand.Connection = connection_Database.con3;
      OleDbDataReader oleDbDataReader = oleDbCommand.ExecuteReader();
      if (oleDbDataReader.HasRows)
      {
        while (oleDbDataReader.Read())
        {
          day_intensity = Conversions.ToByte(oleDbDataReader[5]);
          night_intensity = Conversions.ToByte(oleDbDataReader[11]);
          if (oleDbDataReader[13] != DBNull.Value)
          {
            day_time = Conversions.ToDate(Strings.Format((object) Conversions.ToDate(oleDbDataReader[13]), "HH:mm"));
            night_time = Conversions.ToDate(Strings.Format((object) Conversions.ToDate(oleDbDataReader[14]), "HH:mm"));
          }
        }
      }
      else
      {
        day_intensity = (byte) 0;
        night_intensity = (byte) 3;
      }
      oleDbDataReader.Close();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con3.State != ConnectionState.Open)
      return;
    connection_Database.con3.Close();
  }

  public static object agdb_default_data()
  {
    OleDbCommand oleDbCommand = new OleDbCommand();
    try
    {
      if (connection_Database.con3.State == ConnectionState.Closed)
        connection_Database.con3.Open();
      oleDbCommand.CommandText = "select * from stationdetails";
      oleDbCommand.CommandType = CommandType.Text;
      oleDbCommand.Connection = connection_Database.con3;
      OleDbDataReader oleDbDataReader = oleDbCommand.ExecuteReader();
      while (oleDbDataReader.Read())
      {
        frmMainFormIPIS.agdb_default_message_line1 = Conversions.ToString(oleDbDataReader[8]);
        frmMainFormIPIS.agdb_default_message_line2 = Conversions.ToString(oleDbDataReader[9]);
        frmMainFormIPIS.agdb_default_message_line3 = Conversions.ToString(oleDbDataReader[10]);
      }
      oleDbDataReader.Close();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con3.State == ConnectionState.Open)
      connection_Database.con3.Close();
    return (object) frmMainFormIPIS.agdb_default_message_line1 == (object) DBNull.Value | (object) frmMainFormIPIS.agdb_default_message_line2 == (object) DBNull.Value | (object) frmMainFormIPIS.agdb_default_message_line3 == (object) DBNull.Value ? (object) (byte) 0 : (object) (byte) 1;
  }

  public static void update_train_timings(
    string time_from,
    string to_time,
    string time_dur,
    string flag)
  {
    OleDbCommand oleDbCommand = new OleDbCommand();
    try
    {
      if (connection_Database.con3.State == ConnectionState.Closed)
        connection_Database.con3.Open();
      if (time_from == null)
      {
        time_from = string.Empty;
        to_time = string.Empty;
      }
      else if (time_dur == null)
        time_dur = string.Empty;
      oleDbCommand.CommandText = "update tbl_train_timings set from_time = '{time_from} ', to_time ='{to_time}', next_time = '{time_dur}' , time_flag = '{flag}' ";
      oleDbCommand.CommandType = CommandType.Text;
      oleDbCommand.Connection = connection_Database.con3;
      if (oleDbCommand.ExecuteNonQuery() == 0)
      {
        oleDbCommand.CommandText = "insert into  tbl_train_timings(from_time,to_time,next_time,time_flag) values ( '{time_from} ','{to_time}',  '{time_dur}' ,  '{flag}') ";
        oleDbCommand.CommandType = CommandType.Text;
        oleDbCommand.Connection = connection_Database.con3;
        oleDbCommand.ExecuteNonQuery();
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con3.State != ConnectionState.Open)
      return;
    connection_Database.con3.Close();
  }

  public static void get_train_timings(
    ref string time_from,
    ref string to_time,
    ref string time_dur,
    ref string flag)
  {
    OleDbCommand oleDbCommand = new OleDbCommand();
    oleDbCommand.CommandText = "select * from tbl_train_timings";
    oleDbCommand.CommandType = CommandType.Text;
    oleDbCommand.Connection = connection_Database.con3;
    try
    {
      if (connection_Database.con3.State == ConnectionState.Closed)
        connection_Database.con3.Open();
      OleDbDataReader oleDbDataReader = oleDbCommand.ExecuteReader();
      if (oleDbDataReader.HasRows)
      {
        while (oleDbDataReader.Read())
        {
          time_from = Conversions.ToString(oleDbDataReader[0]);
          to_time = Conversions.ToString(oleDbDataReader[1]);
          time_dur = Conversions.ToString(oleDbDataReader[2]);
          flag = Conversions.ToString(oleDbDataReader[3]);
        }
      }
      oleDbDataReader.Close();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con3.State != ConnectionState.Open)
      return;
    connection_Database.con3.Close();
  }

  public static void set_station_details(
    string station_name,
    string station_code,
    string reg_name,
    string reg_code,
    string agdb_msg1,
    string agdb_msg2,
    string agdb_msg3,
    string pdb_msg,
    string mldb_msg1,
    string mldb_msg2,
    string mldb_msg3)
  {
    OleDbDataAdapter oleDbDataAdapter = new OleDbDataAdapter();
    OleDbCommand oleDbCommand = new OleDbCommand();
    try
    {
      if (connection_Database.con3.State == ConnectionState.Closed)
        connection_Database.con3.Open();
      oleDbCommand.CommandText = "(update stationdetails set StationName = '{station_name} ',  StationCode= '{station_code}' ,  RegionName='{reg_name} ' ,  RegionCode='{reg_code} ',Agdb_msg_line1 = '{agdb_msg1}',Agdb_msg_line2 = '{agdb_msg2}',Agdb_msg_line3 = '{agdb_msg3}',Pdb_msg_line = '{pdb_msg}',Mldb_msg_line1 = '{mldb_msg1}',Mldb_msg_line2 = '{mldb_msg2}',Mldb_msg_line3 = '{mldb_msg3}')";
      oleDbCommand.Connection = connection_Database.con3;
      oleDbDataAdapter.UpdateCommand = oleDbCommand;
      oleDbDataAdapter.UpdateCommand.ExecuteNonQuery();
      int num = (int) MessageBox.Show("Station Details Saved");
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con3.State == ConnectionState.Open)
      connection_Database.con3.Close();
    try
    {
      int num = (int) network_db_read.station_data();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    try
    {
      network_db_read.agdb_default_data();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  public static void get_station_details(ref string station_name, ref string station_code, ref string reg_name, ref string reg_code, ref string agdb_msg1,
    ref string agdb_msg2,
    ref string agdb_msg3,
    ref string pdb_msg,
    ref string mldb_msg1,
    ref string mldb_msg2,
    ref string mldb_msg3)
  {
    OleDbCommand oleDbCommand = new OleDbCommand();
    try
    {
      if (connection_Database.con3.State == ConnectionState.Closed)
        connection_Database.con3.Open();
      oleDbCommand.CommandText = "select * from stationdetails";
      oleDbCommand.CommandType = CommandType.Text;
      oleDbCommand.Connection = connection_Database.con3;
      OleDbDataReader oleDbDataReader = oleDbCommand.ExecuteReader();
      if (oleDbDataReader.HasRows && oleDbDataReader.Read())
      {
        station_name = Conversions.ToString(oleDbDataReader[1]);
        station_code = Conversions.ToString(oleDbDataReader[2]);
        reg_name = Conversions.ToString(oleDbDataReader[3]);
        reg_code = Conversions.ToString(oleDbDataReader[4]);
        agdb_msg1 = Conversions.ToString(oleDbDataReader[8]);
        agdb_msg2 = Conversions.ToString(oleDbDataReader[9]);
        agdb_msg3 = Conversions.ToString(oleDbDataReader[10]);
        pdb_msg = Conversions.ToString(oleDbDataReader[15]);
        mldb_msg1 = Conversions.ToString(oleDbDataReader[16 /*0x10*/]);
        mldb_msg2 = Conversions.ToString(oleDbDataReader[17]);
        mldb_msg3 = Conversions.ToString(oleDbDataReader[18]);
        oleDbDataReader.Close();
        if (connection_Database.con3.State != ConnectionState.Open)
          return;
        connection_Database.con3.Close();
        return;
      }
      oleDbDataReader.Close();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con3.State == ConnectionState.Open)
      connection_Database.con3.Close();
  }

  public static byte station_data()
  {
    string s1 = string.Empty;
    string s2 = string.Empty;
    byte[] numArray1 = new byte[4];
    byte[] numArray2 = new byte[4];
    OleDbCommand oleDbCommand = new OleDbCommand();
    try
    {
      if (connection_Database.con3.State == ConnectionState.Closed)
        connection_Database.con3.Open();
      oleDbCommand.CommandText = "select * from stationdetails";
      oleDbCommand.CommandType = CommandType.Text;
      oleDbCommand.Connection = connection_Database.con3;
      OleDbDataReader oleDbDataReader = oleDbCommand.ExecuteReader();
      if (oleDbDataReader.Read())
      {
        s1 = Strings.Trim(Conversions.ToString(oleDbDataReader[2]));
        s2 = Strings.Trim(Conversions.ToString(oleDbDataReader[4]));
      }
      if ((object) s1 == (object) DBNull.Value | (object) s2 == (object) DBNull.Value)
      {
        if (connection_Database.con3.State == ConnectionState.Open)
          connection_Database.con3.Close();
        return 0;
      }
      byte[] bytes1 = new ASCIIEncoding().GetBytes(s1);
      int length1 = s1.Length;
      int index1 = 0;
      while (index1 < 4)
      {
        frmMainFormIPIS.station_name[index1] = index1 >= length1 ? (byte) 0 : bytes1[index1];
        checked { ++index1; }
      }
      byte[] bytes2 = new ASCIIEncoding().GetBytes(s2);
      int index2 = 0;
      int length2 = bytes2.Length;
      while (index2 < 4)
      {
        frmMainFormIPIS.region_name[index2] = index2 >= length2 ? (byte) 0 : bytes2[index2];
        checked { ++index2; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con3.State == ConnectionState.Open)
      connection_Database.con3.Close();
    return 1;
  }

  public static void update_platform_details()
  {
    MyProject.Forms.frmMainFormIPIS.dgvPFno.Items.Clear();
    network_db_read.get_platform_details();
    int index = 0;
    if (frmMainFormIPIS.pfno_cnt <= 0)
      return;
    MyProject.Forms.frmMainFormIPIS.dgvPFno.Items.Add((object) "--");
    while (index < frmMainFormIPIS.pfno_cnt)
    {
      MyProject.Forms.frmMainFormIPIS.dgvPFno.Items.Add((object) Strings.Trim(frmMainFormIPIS.platform_nos[index]));
      checked { ++index; }
    }
  }

  public static void get_platform_details()
  {
    OleDbCommand oleDbCommand = new OleDbCommand();
    int index = 0;
    try
    {
      if (connection_Database.con10.State == ConnectionState.Closed)
        connection_Database.con10.Open();
      frmMainFormIPIS.platform_no_init();
      oleDbCommand.CommandText = "select * from tbl_pfno";
      oleDbCommand.CommandType = CommandType.Text;
      oleDbCommand.Connection = connection_Database.con10;
      OleDbDataReader oleDbDataReader = oleDbCommand.ExecuteReader();
      if (oleDbDataReader.HasRows)
      {
        while (oleDbDataReader.Read())
        {
          frmMainFormIPIS.platform_nos[index] = Conversions.ToString(oleDbDataReader[1]);
          checked { ++index; }
        }
      }
      frmMainFormIPIS.pfno_cnt = index;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con10.State != ConnectionState.Open)
      return;
    connection_Database.con10.Close();
  }

  public static void set_platform_details(string platform_no, int cnt)
  {
    OleDbDataAdapter oleDbDataAdapter = new OleDbDataAdapter();
    OleDbCommand oleDbCommand1 = new OleDbCommand();
    OleDbCommand oleDbCommand2 = new OleDbCommand();
    try
    {
      if (connection_Database.con10.State == ConnectionState.Closed)
        connection_Database.con10.Open();
      platform_no = Strings.Trim(platform_no);
      oleDbCommand2.CommandText = "insert into  tbl_pfno(PlatformNo,PfnoCnt) values ( '{platform_no} ',{Conversions.ToString(cnt)}) ";
      oleDbCommand2.CommandType = CommandType.Text;
      oleDbCommand2.Connection = connection_Database.con10;
      oleDbCommand2.ExecuteNonQuery();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con10.State != ConnectionState.Open)
      return;
    connection_Database.con10.Close();
  }

  public static void set_Automatic_details(string setval)
  {
    OleDbDataAdapter oleDbDataAdapter = new OleDbDataAdapter();
    OleDbCommand oleDbCommand1 = new OleDbCommand();
    OleDbCommand oleDbCommand2 = new OleDbCommand();
    try
    {
      if (connection_Database.con15.State == ConnectionState.Closed)
        connection_Database.con15.Open();
      oleDbCommand2.CommandText = "update   automatic set val = '{setval}' where ID=1";
      oleDbCommand2.CommandType = CommandType.Text;
      oleDbCommand2.Connection = connection_Database.con15;
      oleDbCommand2.ExecuteNonQuery();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con15.State != ConnectionState.Open)
      return;
    connection_Database.con15.Close();
  }

  public static void delete_platform_details()
  {
    int index = 0;
    try
    {
      if (connection_Database.con10.State == ConnectionState.Closed)
        connection_Database.con10.Open();
      new OleDbCommand()
      {
        CommandText = "delete * from tbl_pfno",
        CommandType = CommandType.Text,
        Connection = connection_Database.con10
      }.ExecuteNonQuery();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con10.State == ConnectionState.Open)
      connection_Database.con10.Close();
    while (index < frmMainFormIPIS.online_train_cnt)
    {
      frmMainFormIPIS.online_train_data[index].pfno = string.Empty;
      checked { ++index; }
    }
    online_trains.update_dgv();
    network_db_read.update_platform_details();
    network_db_read.get_platform_details();
  }

  public static void language_name_data(
    string trainno,
    ref string hinname,
    ref string regional_lang_name)
  {
    int index = 0;
    try
    {
      while (index < frmMainFormIPIS.train_cnt)
      {
        if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(trainno, frmMainFormIPIS.train_details[index].train_no, false) == 0)
        {
          hinname = frmMainFormIPIS.train_details[index].train_name_hin;
          regional_lang_name = frmMainFormIPIS.train_details[index].train_name_reg;
          break;
        }
        checked { ++index; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  public static void get_lang_info()
  {
    OleDbCommand oleDbCommand = new OleDbCommand();
    try
    {
      if (connection_Database.con6.State == ConnectionState.Closed)
        connection_Database.con6.Open();
      oleDbCommand.CommandText = "select * from tbl_language ";
      oleDbCommand.CommandType = CommandType.Text;
      oleDbCommand.Connection = connection_Database.con6;
      OleDbDataReader oleDbDataReader = oleDbCommand.ExecuteReader();
      int index1 = 0;
      frmMainFormIPIS.lang_cnt = (byte) 0;
      if (oleDbDataReader.HasRows)
      {
        while (oleDbDataReader.Read())
        {
          frmMainFormIPIS.language_id[index1] = Conversions.ToString(oleDbDataReader[0]);
          frmMainFormIPIS.language_name[index1] = Conversions.ToString(oleDbDataReader[1]);
          frmMainFormIPIS.language_font_name[index1] = Conversions.ToString(oleDbDataReader[2]);
          frmMainFormIPIS.language_font_type[index1] = Conversions.ToString(oleDbDataReader[3]);
          frmMainFormIPIS.language_font_size[index1] = Conversions.ToString(oleDbDataReader[4]);
          checked { ++frmMainFormIPIS.lang_cnt; }
          checked { ++index1; }
        }
      }
      int index2 = 0;
      int num1 = 0;
      while (index2 < 3)
      {
        if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.language_name[index2], "English", false) == 0)
        {
          frmMainFormIPIS.eng_font_name = frmMainFormIPIS.language_font_name[index2];
          frmMainFormIPIS.eng_font_type = frmMainFormIPIS.language_font_type[index2];
          frmMainFormIPIS.eng_font_size = frmMainFormIPIS.language_font_size[index2];
          num1 = 1;
          break;
        }
        checked { ++index2; }
      }
      if (num1 == 0)
      {
        frmMainFormIPIS.eng_font_name = "Garamond";
        frmMainFormIPIS.eng_font_type = "Regular";
        frmMainFormIPIS.eng_font_size = "12";
      }
      int index3 = 0;
      int num2 = 0;
      while (index3 < 3)
      {
        if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.language_name[index3], "Hindi", false) == 0)
        {
          frmMainFormIPIS.hindi_font_name = frmMainFormIPIS.language_font_name[index3];
          frmMainFormIPIS.hindi_font_type = frmMainFormIPIS.language_font_type[index3];
          frmMainFormIPIS.hindi_font_size = frmMainFormIPIS.language_font_size[index3];
          num2 = 1;
          break;
        }
        checked { ++index3; }
      }
      if (num2 == 0)
      {
        frmMainFormIPIS.hindi_font_name = "Arial Unicode MS";
        frmMainFormIPIS.hindi_font_type = "Regular";
        frmMainFormIPIS.hindi_font_size = "12";
      }
      int index4 = 0;
      int num3 = 0;
      while (index4 < 3)
      {
        if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.language_name[index4], "Hindi", false) != 0 & Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.language_name[index4], "English", false) != 0)
        {
          frmMainFormIPIS.reg_font_name = frmMainFormIPIS.language_font_name[index4];
          frmMainFormIPIS.reg_font_type = frmMainFormIPIS.language_font_type[index4];
          frmMainFormIPIS.reg_font_size = frmMainFormIPIS.language_font_size[index4];
          frmMainFormIPIS.reg_lang_name = frmMainFormIPIS.language_name[index4];
          num3 = 1;
          break;
        }
        checked { ++index4; }
      }
      if (num3 == 0)
      {
        frmMainFormIPIS.reg_font_name = "Gautami";
        frmMainFormIPIS.reg_font_type = "Regular";
        frmMainFormIPIS.reg_font_size = "10";
        frmMainFormIPIS.reg_lang_name = "Telugu";
      }
      oleDbDataReader.Close();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con6.State != ConnectionState.Open)
      return;
    connection_Database.con6.Close();
  }

  public static void delete_lang(string lang_id, ref bool result)
  {
    OleDbCommand oleDbCommand = new OleDbCommand();
    try
    {
      if (connection_Database.con6.State == ConnectionState.Closed)
        connection_Database.con6.Open();
      oleDbCommand.CommandText = "delete * from tbl_language where lang_id = '{lang_id} '";
      oleDbCommand.CommandType = CommandType.Text;
      oleDbCommand.Connection = connection_Database.con6;
      checked { --frmMainFormIPIS.lang_cnt; }
      result = oleDbCommand.ExecuteNonQuery() != 0;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con6.State != ConnectionState.Open)
      return;
    connection_Database.con6.Close();
  }

  public static void set_lang(
    string lang_id,
    string lang_name,
    string font_name,
    string font_type,
    int font_size)
  {
    OleDbDataAdapter oleDbDataAdapter = new OleDbDataAdapter();
    OleDbCommand oleDbCommand = new OleDbCommand();
    try
    {
      if (connection_Database.con6.State == ConnectionState.Closed)
        connection_Database.con6.Open();
      oleDbCommand.CommandText = "insert into tbl_language(lang_id,lang_name,font_name,font_type,font_size) values ('{lang_id}','{lang_name}','{font_name}','{font_type}',{Conversions.ToString(font_size)})";
      oleDbCommand.CommandType = CommandType.Text;
      oleDbCommand.Connection = connection_Database.con6;
      oleDbDataAdapter.InsertCommand = oleDbCommand;
      oleDbDataAdapter.InsertCommand.ExecuteNonQuery();
      checked { ++frmMainFormIPIS.lang_cnt; }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con6.State != ConnectionState.Open)
      return;
    connection_Database.con6.Close();
  }

  public static void update_lang(
    string lang_id,
    string lang_name,
    string font_name,
    string font_type,
    int font_size,
    ref bool result)
  {
    OleDbDataAdapter oleDbDataAdapter = new OleDbDataAdapter();
    OleDbCommand oleDbCommand = new OleDbCommand();
    try
    {
      if (connection_Database.con6.State == ConnectionState.Closed)
        connection_Database.con6.Open();
      oleDbCommand.CommandText = " update tbl_language set lang_name = '{lang_name}', font_name = '{font_name}' , font_type= '{font_type}',font_size= {Conversions.ToString(font_size)} where  lang_id = '{lang_id}' ";
      oleDbCommand.CommandType = CommandType.Text;
      oleDbCommand.Connection = connection_Database.con6;
      result = oleDbCommand.ExecuteNonQuery() != 0;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con6.State != ConnectionState.Open)
      return;
    connection_Database.con6.Close();
  }

  public static void get_train_status_info(
    ref string reg_lang_status_msg,
    ref string hindi_status_msg,
    ref string effect,
    string english_status_msg,
    ref string reg_lang)
  {
    OleDbCommand oleDbCommand = new OleDbCommand();
    try
    {
      if (connection_Database.con4.State == ConnectionState.Closed)
        connection_Database.con4.Open();
      oleDbCommand.CommandText = "select * from tbl_train_status where english_status_name = '{english_status_msg}' ";
      oleDbCommand.CommandType = CommandType.Text;
      oleDbCommand.Connection = connection_Database.con4;
      OleDbDataReader oleDbDataReader = oleDbCommand.ExecuteReader();
      if (oleDbDataReader.HasRows)
      {
        while (oleDbDataReader.Read())
        {
          reg_lang_status_msg = Conversions.ToString(oleDbDataReader[2]);
          hindi_status_msg = Conversions.ToString(oleDbDataReader[1]);
          reg_lang = oleDbDataReader[4] != DBNull.Value ? Conversions.ToString(oleDbDataReader[4]) : string.Empty;
          switch (Conversions.ToByte(oleDbDataReader[3]))
          {
            case 1:
              effect = "Curtain Left to Right";
              break;
            case 2:
              effect = "Curtain Top to Bottom";
              break;
            case 3:
              effect = "Curtain Bottom to Top";
              break;
            case 4:
              effect = "Typing Left to Right";
              break;
            case 5:
              effect = "Running Right to Left";
              break;
            case 6:
              effect = "Running Top to Bottom";
              break;
            case 7:
              effect = "Running Bottom to Top";
              break;
            case 8:
              effect = "Flashing";
              break;
            case 9:
              effect = "Normal";
              break;
          }
        }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con4.State != ConnectionState.Open)
      return;
    connection_Database.con4.Close();
  }

  public static void set_train_status(
    string reg_lang_status_msg,
    string hindi_status_msg,
    byte effect,
    string english_status_msg)
  {
    OleDbCommand oleDbCommand = new OleDbCommand();
    try
    {
      if (connection_Database.con4.State == ConnectionState.Closed)
        connection_Database.con4.Open();
      oleDbCommand.CommandText = "update tbl_train_status set hindi_status_name = '{hindi_status_msg}' , reg_lang_status_name = '{reg_lang_status_msg}',status_msg_effect = {Conversions.ToString(effect)} where english_status_name = '{english_status_msg}' ";
      oleDbCommand.CommandType = CommandType.Text;
      oleDbCommand.Connection = connection_Database.con4;
      if (oleDbCommand.ExecuteNonQuery() == 0)
        new OleDbDataAdapter()
        {
          InsertCommand = new OleDbCommand()
          {
            CommandText = "(insert into tbl_train_status(english_status_name,Hindi_status_name,reg_lang_status_name,status_msg_effect) values ('{english_status_msg}' , '{hindi_status_msg}','{reg_lang_status_msg}','{Conversions.ToString(effect)}')) ",
            CommandType = CommandType.Text,
            Connection = connection_Database.con4
          }
        }.InsertCommand.ExecuteNonQuery();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con4.State != ConnectionState.Open)
      return;
    connection_Database.con4.Close();
  }

  public static byte train_status_language(
    string english_status_msg,
    ref string reg_lang_status_msg,
    ref string hindi_status_msg,
    ref byte effect)
  {
    OleDbCommand oleDbCommand = new OleDbCommand();
    try
    {
      if (connection_Database.con4.State == ConnectionState.Closed)
        connection_Database.con4.Open();
      oleDbCommand.CommandText = "select * from tbl_train_status where english_status_name = '{english_status_msg}' ";
      oleDbCommand.CommandType = CommandType.Text;
      oleDbCommand.Connection = connection_Database.con4;
      OleDbDataReader oleDbDataReader = oleDbCommand.ExecuteReader();
      if (oleDbDataReader.HasRows)
      {
        while (oleDbDataReader.Read())
        {
          reg_lang_status_msg = Conversions.ToString(oleDbDataReader[2]);
          hindi_status_msg = Conversions.ToString(oleDbDataReader[1]);
          effect = Conversions.ToByte(oleDbDataReader[3]);
        }
      }
      else
      {
        if (connection_Database.con4.State == ConnectionState.Open)
          connection_Database.con4.Close();
        return 0;
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con4.State == ConnectionState.Open)
      connection_Database.con4.Close();
    return 1;
  }

  public static void get_voice_msgs()
  {
    OleDbDataAdapter oleDbDataAdapter = new OleDbDataAdapter();
    OleDbCommand oleDbCommand = new OleDbCommand();
    int index = 0;
    try
    {
      if (connection_Database.con8.State == ConnectionState.Closed)
        connection_Database.con8.Open();
      oleDbCommand.CommandText = "select * from tbl_voice_msg ";
      oleDbCommand.CommandType = CommandType.Text;
      oleDbCommand.Connection = connection_Database.con8;
      OleDbDataReader oleDbDataReader = oleDbCommand.ExecuteReader();
      if (oleDbDataReader.HasRows)
      {
        while (oleDbDataReader.Read())
        {
          frmVoice_Special_Messages.voice_var[index].msg_id = Conversions.ToString(oleDbDataReader[0]);
          frmVoice_Special_Messages.voice_var[index].msg_name = Conversions.ToString(oleDbDataReader[1]);
          frmVoice_Special_Messages.voice_var[index].eng_path = Conversions.ToString(oleDbDataReader[2]);
          frmVoice_Special_Messages.voice_var[index].reg_path = Conversions.ToString(oleDbDataReader[3]);
          frmVoice_Special_Messages.voice_var[index].hin_path = Conversions.ToString(oleDbDataReader[4]);
          checked { ++index; }
        }
        frmVoice_Special_Messages.voice_cnt = Conversions.ToString(index);
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con8.State != ConnectionState.Open)
      return;
    connection_Database.con8.Close();
  }

  public static void get_voice_msg(
    string msg_id,
    ref string msg_name,
    ref string eng_path,
    ref string reg_path,
    ref string hin_path)
  {
    OleDbDataAdapter oleDbDataAdapter = new OleDbDataAdapter();
    OleDbCommand oleDbCommand = new OleDbCommand();
    try
    {
      if (connection_Database.con8.State == ConnectionState.Closed)
        connection_Database.con8.Open();
      oleDbCommand.CommandText = "select * from tbl_voice_msg where msg_id = '{msg_id} '";
      oleDbCommand.CommandType = CommandType.Text;
      oleDbCommand.Connection = connection_Database.con8;
      OleDbDataReader oleDbDataReader = oleDbCommand.ExecuteReader();
      if (oleDbDataReader.HasRows && oleDbDataReader.Read())
      {
        msg_name = Conversions.ToString(oleDbDataReader[1]);
        eng_path = Conversions.ToString(oleDbDataReader[2]);
        reg_path = Conversions.ToString(oleDbDataReader[3]);
        hin_path = Conversions.ToString(oleDbDataReader[4]);
      }
      oleDbDataReader.Close();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con8.State != ConnectionState.Open)
      return;
    connection_Database.con8.Close();
  }

  public static void set_voice_msg(
    string message_id,
    string msg_name,
    string eng_path,
    string reg_path,
    string hin_path,
    ref bool success)
  {
    try
    {
      if (connection_Database.con8.State == ConnectionState.Closed)
        connection_Database.con8.Open();
      success = new OleDbDataAdapter()
      {
        InsertCommand = new OleDbCommand()
        {
          CommandText = "insert into tbl_voice_msg(msg_id,message_name,eng_path,reg_path,hin_path) values ('{message_id}','{msg_name}','{eng_path}','{reg_path}','{hin_path}')",
          CommandType = CommandType.Text,
          Connection = connection_Database.con8
        }
      }.InsertCommand.ExecuteNonQuery() != 0;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con8.State != ConnectionState.Open)
      return;
    connection_Database.con8.Close();
  }

  public static void update_voice_msg(
    string message_id,
    string msg_name,
    string eng_path,
    string reg_path,
    string hin_path,
    ref bool success)
  {
    try
    {
      if (connection_Database.con8.State == ConnectionState.Closed)
        connection_Database.con8.Open();
      OleDbDataAdapter oleDbDataAdapter = new OleDbDataAdapter();
      success = new OleDbCommand()
      {
        CommandText = "update tbl_voice_msg  set message_name ='{msg_name}', eng_path = '{eng_path}' , reg_path = '{reg_path}',hin_path ='{hin_path}' where  msg_id = '{message_id}'",
        CommandType = CommandType.Text,
        Connection = connection_Database.con8
      }.ExecuteNonQuery() != 0;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con8.State != ConnectionState.Open)
      return;
    connection_Database.con8.Close();
  }

  public static void delete_voice_msg(string msg_id, ref bool success)
  {
    OleDbCommand oleDbCommand = new OleDbCommand();
    try
    {
      if (connection_Database.con8.State == ConnectionState.Closed)
        connection_Database.con8.Open();
      oleDbCommand.CommandText = "delete * from tbl_voice_msg where trim(msg_id )= '{Strings.Trim(msg_id)}' ";
      oleDbCommand.CommandType = CommandType.Text;
      oleDbCommand.Connection = connection_Database.con8;
      success = oleDbCommand.ExecuteNonQuery() != 0;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con8.State != ConnectionState.Open)
      return;
    connection_Database.con8.Close();
  }

  public static void get_msg(ref DataSet ds)
  {
    OleDbCommand oleDbCommand = new OleDbCommand();
    try
    {
      if (connection_Database.con4.State == ConnectionState.Closed)
        connection_Database.con4.Open();
      oleDbCommand.CommandText = "select * from tbl_message";
      oleDbCommand.CommandType = CommandType.Text;
      oleDbCommand.Connection = connection_Database.con4;
      new OleDbDataAdapter(oleDbCommand.CommandText, connection_Database.con4).Fill(ds, "msg_table");
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con4.State != ConnectionState.Open)
      return;
    connection_Database.con4.Close();
  }

  public static void set_Update_BufferTime(string bTime)
  {
    OleDbDataAdapter oleDbDataAdapter = new OleDbDataAdapter();
    OleDbCommand oleDbCommand = new OleDbCommand();
    try
    {
      if (connection_Database.con15.State == ConnectionState.Closed)
        connection_Database.con15.Open();
      new OleDbCommand("Update configuration set bufferTime = {bTime} where  ID = 1", connection_Database.con15).ExecuteNonQuery();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con15.State != ConnectionState.Open)
      return;
    connection_Database.con15.Close();
  }

  public static void set_Update_adLoop(string adLoop)
  {
    OleDbDataAdapter oleDbDataAdapter = new OleDbDataAdapter();
    OleDbCommand oleDbCommand = new OleDbCommand();
    try
    {
      if (connection_Database.con15.State == ConnectionState.Closed)
        connection_Database.con15.Open();
      new OleDbCommand("Update configuration set adLoop = {adLoop} where  ID = 1", connection_Database.con15).ExecuteNonQuery();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con15.State != ConnectionState.Open)
      return;
    connection_Database.con15.Close();
  }

  public static void set_Update_setInterval(string setInterval)
  {
    OleDbDataAdapter oleDbDataAdapter = new OleDbDataAdapter();
    OleDbCommand oleDbCommand = new OleDbCommand();
    try
    {
      if (connection_Database.con15.State == ConnectionState.Closed)
        connection_Database.con15.Open();
      new OleDbCommand("Update configuration set setInterval = {setInterval} where  ID = 1", connection_Database.con15).ExecuteNonQuery();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con15.State != ConnectionState.Open)
      return;
    connection_Database.con15.Close();
  }

  public static void set_Update_baseStn(string stn_name)
  {
    OleDbDataAdapter oleDbDataAdapter = new OleDbDataAdapter();
    OleDbCommand oleDbCommand = new OleDbCommand();
    try
    {
      if (connection_Database.con15.State == ConnectionState.Closed)
        connection_Database.con15.Open();
      new OleDbCommand("Update configuration set basestn = '{stn_name}' where  ID = 1", connection_Database.con15).ExecuteNonQuery();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con15.State != ConnectionState.Open)
      return;
    connection_Database.con15.Close();
  }

  public static void get_BaseStn(ref ComboBox cmdStn)
  {
    OleDbCommand oleDbCommand = new OleDbCommand();
    try
    {
      DataSet dataSet = new DataSet();
      if (connection_Database.con15.State == ConnectionState.Closed)
        connection_Database.con15.Open();
      oleDbCommand.CommandText = "select * from BaseStations";
      oleDbCommand.CommandType = CommandType.Text;
      oleDbCommand.Connection = connection_Database.con15;
      new OleDbDataAdapter(oleDbCommand.CommandText, connection_Database.con15).Fill(dataSet, "msg_table");
      cmdStn.DataSource = (object) dataSet.Tables[0];
      cmdStn.DisplayMember = "basestn";
      cmdStn.ValueMember = "stnCode";
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con15.State != ConnectionState.Open)
      return;
    connection_Database.con15.Close();
  }

  public static void get_msg()
  {
    OleDbCommand oleDbCommand = new OleDbCommand();
    byte index = 0;
    try
    {
      if (connection_Database.con4.State == ConnectionState.Closed)
        connection_Database.con4.Open();
      oleDbCommand.CommandText = "select * from tbl_message";
      oleDbCommand.CommandType = CommandType.Text;
      oleDbCommand.Connection = connection_Database.con4;
      OleDbDataReader oleDbDataReader = oleDbCommand.ExecuteReader();
      if (oleDbDataReader.HasRows)
      {
        while (oleDbDataReader.Read())
        {
          frmMainFormIPIS.msg_struct[(int) index].msg_id = Conversions.ToString(oleDbDataReader[1]);
          frmMainFormIPIS.msg_struct[(int) index].language_name = Conversions.ToString(oleDbDataReader[2]);
          frmMainFormIPIS.msg_struct[(int) index].msg_name = Conversions.ToString(oleDbDataReader[3]);
          checked { ++index; }
        }
      }
      frmMainFormIPIS.addmsg_count = (int) index;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con4.State != ConnectionState.Open)
      return;
    connection_Database.con4.Close();
  }

  public static void set_msg(
    string msg_id,
    string lang_name,
    string msg_name,
    string row_no,
    ref bool result)
  {
    OleDbDataAdapter oleDbDataAdapter = new OleDbDataAdapter();
    OleDbCommand oleDbCommand = new OleDbCommand();
    try
    {
      if (connection_Database.con4.State == ConnectionState.Closed)
        connection_Database.con4.Open();
      oleDbCommand.CommandText = "insert into tbl_message(message_id,language_name,message_name,row_no) values ('{msg_id}','{lang_name}','{msg_name}',{row_no})";
      oleDbCommand.CommandType = CommandType.Text;
      oleDbCommand.Connection = connection_Database.con4;
      oleDbDataAdapter.InsertCommand = oleDbCommand;
      result = oleDbDataAdapter.InsertCommand.ExecuteNonQuery() != 0;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con4.State != ConnectionState.Open)
      return;
    connection_Database.con4.Close();
  }

  public static void set_Update_msg(
    string msg_id,
    string lang_name,
    string msg_name,
    string row_no,
    ref bool result)
  {
    OleDbDataAdapter oleDbDataAdapter = new OleDbDataAdapter();
    OleDbCommand oleDbCommand = new OleDbCommand();
    try
    {
      if (connection_Database.con4.State == ConnectionState.Closed)
        connection_Database.con4.Open();
      string[] strArray = new string[9]
      {
        "Update tbl_message set language_name = '",
        lang_name,
        "', message_name = '",
        msg_name,
        "', row_no = ",
        row_no,
        " where  (message_id = '",
        msg_id,
        "') "
      };
      result = new OleDbCommand(string.Concat(strArray), connection_Database.con4).ExecuteNonQuery() != 0;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con4.State != ConnectionState.Open)
      return;
    connection_Database.con4.Close();
  }

  public static void delete_msg(string msg_id, ref bool result)
  {
    try
    {
      if (connection_Database.con4.State == ConnectionState.Closed)
        connection_Database.con4.Open();
      result = new OleDbCommand()
      {
        CommandText = "delete * from tbl_message where message_id = '{msg_id}' ",
        CommandType = CommandType.Text,
        Connection = connection_Database.con4
      }.ExecuteNonQuery() != 0;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    connection_Database.con4.Close();
  }

  public static void delete_video_db()
  {
    OleDbCommand oleDbCommand1 = new OleDbCommand();
    OleDbDataAdapter oleDbDataAdapter = new OleDbDataAdapter();
    int num1 = 0;
    OleDbCommand oleDbCommand2 = new OleDbCommand();
    try
    {
      if (connection_Database.con7.State == ConnectionState.Closed)
        connection_Database.con7.Open();
      oleDbCommand1.CommandText = "delete * from tbl_Daily_Trains";
      oleDbCommand1.CommandType = CommandType.Text;
      oleDbCommand1.Connection = connection_Database.con7;
      num1 = oleDbCommand1.ExecuteNonQuery();
      oleDbCommand2.CommandText = "delete * from tbl_Train_Coaches";
      oleDbCommand2.CommandType = CommandType.Text;
      oleDbCommand2.Connection = connection_Database.con7;
      oleDbCommand2.ExecuteNonQuery();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num2 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con7.State != ConnectionState.Open)
      return;
    connection_Database.con7.Close();
  }

  public static void insert_video_db(
    int i,
    string coach_inf,
    string arrival_time,
    string departure_time,
    string Platform_no)
  {
    OleDbCommand oleDbCommand1 = new OleDbCommand();
    OleDbDataAdapter oleDbDataAdapter1 = new OleDbDataAdapter();
    OleDbCommand oleDbCommand2 = new OleDbCommand();
    OleDbDataAdapter oleDbDataAdapter2 = new OleDbDataAdapter();
    try
    {
      if (connection_Database.con7.State == ConnectionState.Closed)
        connection_Database.con7.Open();
      oleDbCommand1.CommandText = "insert into tbl_Daily_trains(TrainNo,TrainNameLang1,TrainNameLang2,TrainNameLang3,ExpArrTime,ExpDepTime,ExpPlatformNo) values ('{frmMainFormIPIS.online_train_data[i].train_no}' , '{frmMainFormIPIS.online_train_data[i].train_name} ' , '{frmMainFormIPIS.online_train_data[i].train_name_hin}', '{frmMainFormIPIS.online_train_data[i].train_name_reg}' ,'{arrival_time}','{departure_time}','{Platform_no} ' )";
      oleDbCommand1.CommandType = CommandType.Text;
      oleDbCommand1.Connection = connection_Database.con7;
      oleDbDataAdapter1.InsertCommand = oleDbCommand1;
      oleDbDataAdapter1.InsertCommand.ExecuteNonQuery();
      oleDbCommand2.CommandText = "insert into tbl_Train_Coaches(TrainNumber,Coach) values ('{frmMainFormIPIS.online_train_data[i].train_no}','{coach_inf}') ";
      oleDbCommand2.CommandType = CommandType.Text;
      oleDbCommand2.Connection = connection_Database.con7;
      oleDbDataAdapter2.InsertCommand = oleDbCommand2;
      oleDbDataAdapter2.InsertCommand.ExecuteNonQuery();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con7.State != ConnectionState.Open)
      return;
    connection_Database.con7.Close();
  }

  public static void get_video_db(ref OleDbDataAdapter adt1, ref OleDbDataAdapter adt2)
  {
    try
    {
      if (connection_Database.con7.State == ConnectionState.Closed)
        connection_Database.con7.Open();
      adt1 = new OleDbDataAdapter("Select * from tbl_Daily_Trains ", connection_Database.con7);
      adt2 = new OleDbDataAdapter("Select * from tbl_Train_Coaches ", connection_Database.con7);
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con7.State != ConnectionState.Open)
      return;
    connection_Database.con7.Close();
  }

  public static void set_video_messages(string header_msg, string footer_msg, bool video_chk)
  {
    OleDbDataAdapter oleDbDataAdapter = new OleDbDataAdapter();
    OleDbCommand oleDbCommand1 = new OleDbCommand();
    OleDbCommand oleDbCommand2 = new OleDbCommand();
    int num1 = 0;
    try
    {
      if (connection_Database.con12.State == ConnectionState.Closed)
        connection_Database.con12.Open();
      oleDbCommand1.CommandText = "(update  tbl_video_db set header_txt = '{header_msg} ',  footer_txt= '{footer_msg}' ,video_chk ={Conversions.ToString(video_chk)}  )";
      oleDbCommand1.Connection = connection_Database.con12;
      oleDbDataAdapter.UpdateCommand = oleDbCommand1;
      if (oleDbDataAdapter.UpdateCommand.ExecuteNonQuery() == 0)
      {
        oleDbCommand2.CommandText = "insert into tbl_video_db(header_txt,footer_txt,video_chk) values ('{header_msg}' , '{footer_msg} ' ,{Conversions.ToString(video_chk)} )";
        oleDbCommand2.CommandType = CommandType.Text;
        oleDbCommand2.Connection = connection_Database.con12;
        oleDbDataAdapter.InsertCommand = oleDbCommand2;
        num1 = oleDbDataAdapter.InsertCommand.ExecuteNonQuery();
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num2 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con12.State != ConnectionState.Open)
      return;
    connection_Database.con12.Close();
  }

  public static void get_video_messages(
    ref string header_msg,
    ref string footer_msg,
    ref bool video_chk)
  {
    OleDbCommand oleDbCommand = new OleDbCommand();
    try
    {
      if (connection_Database.con12.State == ConnectionState.Closed)
        connection_Database.con12.Open();
      oleDbCommand.CommandText = "(select * from tbl_video_db  )";
      oleDbCommand.CommandType = CommandType.Text;
      oleDbCommand.Connection = connection_Database.con12;
      OleDbDataReader oleDbDataReader = oleDbCommand.ExecuteReader();
      if (oleDbDataReader.Read())
      {
        header_msg = Conversions.ToString(oleDbDataReader[0]);
        footer_msg = Conversions.ToString(oleDbDataReader[1]);
        video_chk = Conversions.ToBoolean(oleDbDataReader[2]);
      }
      oleDbDataReader.Close();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con12.State != ConnectionState.Open)
      return;
    connection_Database.con12.Close();
  }

  public static void set_new_user(
    string user_id,
    string user_name,
    string user_pwd,
    int pwd_length,
    string user_group)
  {
    try
    {
      if (connection_Database.con5.State == ConnectionState.Closed)
        connection_Database.con5.Open();
      OleDbCommand oleDbCommand = new OleDbCommand();
      OleDbDataAdapter oleDbDataAdapter = new OleDbDataAdapter();
      string empty = string.Empty;
      network_db_read.enc_pwd(user_pwd, ref empty);
      oleDbCommand.CommandText = " insert into logintable(User_ID,User_Name,User_Password,p_length,User_Group) values ('{user_id}', '{user_name}','{empty}',{Conversions.ToString(pwd_length)},'{user_group} ')";
      oleDbCommand.CommandType = CommandType.Text;
      oleDbCommand.Connection = connection_Database.con5;
      oleDbDataAdapter.UpdateCommand = oleDbCommand;
      if (oleDbDataAdapter.UpdateCommand.ExecuteNonQuery() > 0)
      {
        int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "New Name Added Successfully", "Msg Box", 0, 0, 0);
        frmMainFormIPIS.user_details[(int) frmMainFormIPIS.user_cnt.cnt].user_id = user_id;
        frmMainFormIPIS.user_details[(int) frmMainFormIPIS.user_cnt.cnt].user_name = user_name;
        frmMainFormIPIS.user_details[(int) frmMainFormIPIS.user_cnt.cnt].pwd = empty;
        frmMainFormIPIS.user_details[(int) frmMainFormIPIS.user_cnt.cnt].pwd_length = checked ((byte) pwd_length);
        frmMainFormIPIS.user_details[(int) frmMainFormIPIS.user_cnt.cnt].group = user_group;
        checked { ++frmMainFormIPIS.user_cnt.cnt; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con5.State != ConnectionState.Open)
      return;
    connection_Database.con5.Close();
  }

  public static void set_user_name(string new_name, string old_name, ref bool result)
  {
    OleDbCommand oleDbCommand = new OleDbCommand();
    OleDbDataAdapter oleDbDataAdapter = new OleDbDataAdapter();
    try
    {
      if (connection_Database.con5.State == ConnectionState.Closed)
        connection_Database.con5.Open();
      oleDbCommand.CommandText = "update logintable set User_Name = '{new_name}' where User_Name = '{old_name}'";
      oleDbCommand.CommandType = CommandType.Text;
      oleDbCommand.Connection = connection_Database.con5;
      oleDbDataAdapter.UpdateCommand = oleDbCommand;
      result = oleDbDataAdapter.UpdateCommand.ExecuteNonQuery() != 0;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con5.State != ConnectionState.Open)
      return;
    connection_Database.con5.Close();
  }

  public static void delete_pwd(string usr_name, ref bool found)
  {
    OleDbCommand oleDbCommand = new OleDbCommand();
    if (connection_Database.con5.State == ConnectionState.Closed)
      connection_Database.con5.Open();
    oleDbCommand.CommandText = "delete from logintable where (User_Name) = '{usr_name}' ";
    oleDbCommand.CommandType = CommandType.Text;
    oleDbCommand.Connection = connection_Database.con5;
    int num = oleDbCommand.ExecuteNonQuery();
    if (connection_Database.con5.State == ConnectionState.Open)
      connection_Database.con5.Close();
    if (num == 0)
      found = false;
    else
      found = true;
  }

  public static void set_user_pwd(
    string encrypted_pwd,
    string pwd_length,
    string user_name,
    ref bool result)
  {
    OleDbCommand oleDbCommand1 = new OleDbCommand();
    try
    {
      if (connection_Database.con5.State == ConnectionState.Closed)
        connection_Database.con5.Open();
      OleDbCommand oleDbCommand2 = new OleDbCommand("Update logintable  SET User_Password = '{encrypted_pwd}', p_length = {pwd_length}  WHERE (User_Name =  '{user_name}')", connection_Database.con5);
      result = oleDbCommand2.ExecuteNonQuery() != 0;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con5.State != ConnectionState.Open)
      return;
    connection_Database.con5.Close();
  }

  public static void set_user_group(ref string group, string user_name)
  {
    OleDbCommand oleDbCommand = new OleDbCommand();
    int index = 0;
    try
    {
      if (connection_Database.con5.State == ConnectionState.Closed)
        connection_Database.con5.Open();
      new OleDbCommand("Update logintable  SET User_Group = '{Strings.Trim(group)}'  WHERE (User_Name =  '{user_name}')", connection_Database.con5).ExecuteNonQuery();
      while (index < (int) frmMainFormIPIS.user_cnt.cnt)
      {
        if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Strings.Trim(user_name), Strings.Trim(frmMainFormIPIS.user_details[index].user_name), false) == 0)
        {
          frmMainFormIPIS.user_details[index].group = Strings.Trim(group);
          break;
        }
        checked { ++index; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con5.State != ConnectionState.Open)
      return;
    connection_Database.con5.Close();
  }

  public static void set_com_baud_values(string com_value, int baud_value, ref bool result)
  {
    OleDbDataAdapter oleDbDataAdapter = new OleDbDataAdapter();
    OleDbCommand oleDbCommand = new OleDbCommand();
    try
    {
      if (connection_Database.con3.State == ConnectionState.Closed)
        connection_Database.con3.Open();
      oleDbCommand.CommandText = " ( update stationdetails set Comport = '{com_value} ' ,  Baudrate = '{Conversions.ToString(baud_value)}' )";
      oleDbCommand.Connection = connection_Database.con3;
      oleDbDataAdapter.UpdateCommand = oleDbCommand;
      result = oleDbDataAdapter.UpdateCommand.ExecuteNonQuery() != 0;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con3.State != ConnectionState.Open)
      return;
    connection_Database.con3.Close();
  }

  public static void get_com_baud_values(ref string com_value, ref int baud_value)
  {
    OleDbCommand oleDbCommand = new OleDbCommand();
    try
    {
      if (connection_Database.con3.State == ConnectionState.Closed)
        connection_Database.con3.Open();
      oleDbCommand.CommandText = "select * from stationdetails";
      oleDbCommand.CommandType = CommandType.Text;
      oleDbCommand.Connection = connection_Database.con3;
      OleDbDataReader oleDbDataReader = oleDbCommand.ExecuteReader();
      while (oleDbDataReader.Read())
      {
        com_value = Conversions.ToString(oleDbDataReader[6]);
        baud_value = Conversions.ToInteger(oleDbDataReader[7]);
      }
      oleDbDataReader.Close();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con3.State != ConnectionState.Open)
      return;
    connection_Database.con3.Close();
  }

  public static void get_language(ref bool voice_eng, ref bool voice_hin, ref bool voice_reg, ref bool dis_eng, ref bool dis_hin,
    ref bool dis_reg)
  {
    OleDbCommand oleDbCommand = new OleDbCommand();
    try
    {
      if (connection_Database.con9.State == ConnectionState.Closed)
        connection_Database.con9.Open();
      oleDbCommand.CommandText = "select * from language";
      oleDbCommand.CommandType = CommandType.Text;
      oleDbCommand.Connection = connection_Database.con9;
      OleDbDataReader oleDbDataReader = oleDbCommand.ExecuteReader();
      while (oleDbDataReader.Read())
      {
        voice_eng = Conversions.ToBoolean(oleDbDataReader[0]);
        voice_hin = Conversions.ToBoolean(oleDbDataReader[1]);
        voice_reg = Conversions.ToBoolean(oleDbDataReader[2]);
        dis_eng = Conversions.ToBoolean(oleDbDataReader[3]);
        dis_hin = Conversions.ToBoolean(oleDbDataReader[4]);
        dis_reg = Conversions.ToBoolean(oleDbDataReader[5]);
      }
      oleDbDataReader.Close();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con9.State != ConnectionState.Open)
      return;
    connection_Database.con9.Close();
  }

  public static void set_language_details()
  {
    OleDbDataAdapter oleDbDataAdapter1 = new OleDbDataAdapter();
    OleDbCommand oleDbCommand1 = new OleDbCommand();
    OleDbDataAdapter oleDbDataAdapter2 = new OleDbDataAdapter();
    OleDbCommand oleDbCommand2 = new OleDbCommand();
    OleDbCommand oleDbCommand3 = new OleDbCommand();
    int num1 = 0;
    try
    {
      if (connection_Database.con9.State == ConnectionState.Closed)
        connection_Database.con9.Open();
      oleDbCommand3.CommandText = "select * from languages";
      oleDbCommand3.CommandType = CommandType.Text;
      oleDbCommand3.Connection = connection_Database.con9;
      OleDbDataReader oleDbDataReader = oleDbCommand3.ExecuteReader();
      if (oleDbDataReader.HasRows)
      {
        oleDbCommand2.CommandText = " ( update languages set regional_language_select = {Conversions.ToString(frmMainFormIPIS.language_selection.regional_language_selected)}  ,  language_name = '{frmMainFormIPIS.language_selection.regional_language_name}'  )";
        oleDbCommand2.Connection = connection_Database.con9;
        oleDbDataAdapter2.UpdateCommand = oleDbCommand2;
        num1 = oleDbDataAdapter2.UpdateCommand.ExecuteNonQuery();
      }
      else
      {
        oleDbCommand1.CommandText = "insert into languages(regional_language_select,language_name) values ({Conversions.ToString(frmMainFormIPIS.language_selection.regional_language_selected)},'{frmMainFormIPIS.language_selection.regional_language_name}')";
        oleDbCommand1.CommandType = CommandType.Text;
        oleDbCommand1.Connection = connection_Database.con9;
        oleDbDataAdapter1.InsertCommand = oleDbCommand1;
        num1 = oleDbDataAdapter1.InsertCommand.ExecuteNonQuery();
      }
      oleDbDataReader.Close();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num2 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con9.State != ConnectionState.Open)
      return;
    connection_Database.con9.Close();
  }

  public static void get_config(ref DataSet ds)
  {
    OleDbCommand oleDbCommand = new OleDbCommand();
    try
    {
      if (connection_Database.con15.State == ConnectionState.Closed)
        connection_Database.con15.Open();
      oleDbCommand.CommandText = "select * from Configuration";
      oleDbCommand.CommandType = CommandType.Text;
      oleDbCommand.Connection = connection_Database.con15;
      new OleDbDataAdapter(oleDbCommand.CommandText, connection_Database.con15).Fill(ds, "msg_table");
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con15.State != ConnectionState.Open)
      return;
    connection_Database.con15.Close();
  }

  public static void get_language_details()
  {
    OleDbCommand oleDbCommand = new OleDbCommand();
    try
    {
      if (connection_Database.con9.State == ConnectionState.Closed)
        connection_Database.con9.Open();
      oleDbCommand.CommandText = "select * from languages";
      oleDbCommand.CommandType = CommandType.Text;
      oleDbCommand.Connection = connection_Database.con9;
      OleDbDataReader oleDbDataReader = oleDbCommand.ExecuteReader();
      if (oleDbDataReader.HasRows)
      {
        if (oleDbDataReader.Read())
        {
          frmMainFormIPIS.language_selection.regional_language_selected = Conversions.ToBoolean(oleDbDataReader[0]);
          frmMainFormIPIS.language_selection.regional_language_name = Conversions.ToString(oleDbDataReader[1]);
        }
      }
      else
      {
        frmMainFormIPIS.language_selection.regional_language_selected = true;
        frmMainFormIPIS.language_selection.regional_language_name = "Telugu";
      }
      oleDbDataReader.Close();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con9.State != ConnectionState.Open)
      return;
    connection_Database.con9.Close();
  }

  public static byte get_pfno_int(string text)
  {
    OleDbCommand oleDbCommand = new OleDbCommand();
    byte pfnoInt = 0;
    try
    {
      if (connection_Database.con10.State == ConnectionState.Closed)
        connection_Database.con10.Open();
      oleDbCommand.CommandText = "select * from tbl_pfno where PlatformNo = '{Strings.Trim(text)}'";
      oleDbCommand.CommandType = CommandType.Text;
      oleDbCommand.Connection = connection_Database.con10;
      OleDbDataReader oleDbDataReader = oleDbCommand.ExecuteReader();
      if (oleDbDataReader.HasRows)
      {
        while (oleDbDataReader.Read())
          pfnoInt = Conversions.ToByte(oleDbDataReader[2]);
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con10.State == ConnectionState.Open)
      connection_Database.con10.Close();
    return pfnoInt;
  }

  public static void get_pfno(string train_no, ref string pfno)
  {
    int index = 0;
    while (index < frmMainFormIPIS.train_cnt)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Strings.Trim(train_no), Strings.Trim(frmMainFormIPIS.train_details[index].train_no), false) == 0)
      {
        pfno = frmMainFormIPIS.train_details[index].pf_no;
        break;
      }
      checked { ++index; }
    }
  }

  public static bool set_StationCode(
    string sc_code,
    string eng_sc_name,
    string hindi_sc_name,
    string reg_sc_name)
  {
    OleDbDataAdapter oleDbDataAdapter1 = new OleDbDataAdapter();
    OleDbCommand oleDbCommand1 = new OleDbCommand();
    OleDbDataAdapter oleDbDataAdapter2 = new OleDbDataAdapter();
    OleDbCommand oleDbCommand2 = new OleDbCommand();
    int num1 = 0;
    try
    {
      sc_code = Strings.Trim(sc_code);
      eng_sc_name = Strings.Trim(eng_sc_name);
      hindi_sc_name = Strings.Trim(hindi_sc_name);
      reg_sc_name = Strings.Trim(reg_sc_name);
      if (connection_Database.con11.State == ConnectionState.Closed)
        connection_Database.con11.Open();
      oleDbCommand1.CommandText = "insert into tbl_stationCode(StationCode,EnglishStationName,HindiStationName,RegionalStationName,RegionalLanguage) values ('{sc_code}','{eng_sc_name}','{hindi_sc_name}','{reg_sc_name}','{frmMainFormIPIS.language_selection.regional_language_name}')";
      oleDbCommand1.CommandType = CommandType.Text;
      oleDbCommand1.Connection = connection_Database.con11;
      oleDbDataAdapter1.InsertCommand = oleDbCommand1;
      num1 = oleDbDataAdapter1.InsertCommand.ExecuteNonQuery();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num2 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con11.State == ConnectionState.Open)
      connection_Database.con11.Close();
    return num1 != 0;
  }

  public static bool edit_StationCode(
    string sc_code,
    string eng_sc_name,
    string hindi_sc_name,
    string reg_sc_name)
  {
    OleDbDataAdapter oleDbDataAdapter1 = new OleDbDataAdapter();
    OleDbCommand oleDbCommand1 = new OleDbCommand();
    OleDbDataAdapter oleDbDataAdapter2 = new OleDbDataAdapter();
    OleDbCommand oleDbCommand2 = new OleDbCommand();
    int num1 = 0;
    try
    {
      sc_code = Strings.Trim(sc_code);
      eng_sc_name = Strings.Trim(eng_sc_name);
      hindi_sc_name = Strings.Trim(hindi_sc_name);
      reg_sc_name = Strings.Trim(reg_sc_name);
      if (connection_Database.con11.State == ConnectionState.Closed)
        connection_Database.con11.Open();
      oleDbCommand1.CommandText = "update tbl_stationCode set EnglishStationName = '{eng_sc_name}',HindiStationName ='{hindi_sc_name}', RegionalStationName = '{reg_sc_name}',RegionalLanguage = '{frmMainFormIPIS.language_selection.regional_language_name}' where StationCode ='{sc_code}' ";
      oleDbCommand1.CommandType = CommandType.Text;
      oleDbCommand1.Connection = connection_Database.con11;
      oleDbDataAdapter1.InsertCommand = oleDbCommand1;
      num1 = oleDbDataAdapter1.InsertCommand.ExecuteNonQuery();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num2 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con11.State == ConnectionState.Open)
      connection_Database.con11.Close();
    return num1 != 0;
  }

  public static bool Delete_StationCode(string sc_code)
  {
    OleDbDataAdapter oleDbDataAdapter1 = new OleDbDataAdapter();
    OleDbCommand oleDbCommand1 = new OleDbCommand();
    OleDbDataAdapter oleDbDataAdapter2 = new OleDbDataAdapter();
    OleDbCommand oleDbCommand2 = new OleDbCommand();
    int num1 = 0;
    try
    {
      sc_code = Strings.Trim(sc_code);
      if (connection_Database.con11.State == ConnectionState.Closed)
        connection_Database.con11.Open();
      oleDbCommand1.CommandText = "Delete * from tbl_stationCode where StationCode ='{sc_code}' ";
      oleDbCommand1.CommandType = CommandType.Text;
      oleDbCommand1.Connection = connection_Database.con11;
      oleDbDataAdapter1.InsertCommand = oleDbCommand1;
      num1 = oleDbDataAdapter1.InsertCommand.ExecuteNonQuery();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num2 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con11.State == ConnectionState.Open)
      connection_Database.con11.Close();
    return num1 != 0;
  }

  public static void get_Station_code(
    string station_code,
    ref string eng_sc_name,
    ref string hindi_sc_name,
    ref string reg_sc_name)
  {
    OleDbCommand oleDbCommand = new OleDbCommand();
    string empty = string.Empty;
    try
    {
      if (connection_Database.con11.State == ConnectionState.Closed)
        connection_Database.con11.Open();
      oleDbCommand.CommandText = "select * from tbl_StationCode where StationCode = '{Strings.Trim(station_code)}'";
      oleDbCommand.CommandType = CommandType.Text;
      oleDbCommand.Connection = connection_Database.con11;
      OleDbDataReader oleDbDataReader = oleDbCommand.ExecuteReader();
      if (oleDbDataReader.HasRows)
      {
        if (oleDbDataReader.Read())
        {
          eng_sc_name = Strings.Trim(Conversions.ToString(oleDbDataReader[1]));
          hindi_sc_name = Strings.Trim(Conversions.ToString(oleDbDataReader[2]));
          string Right = oleDbDataReader[4] != DBNull.Value ? Conversions.ToString(oleDbDataReader[4]) : string.Empty;
          reg_sc_name = Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Strings.Trim(frmMainFormIPIS.language_selection.regional_language_name), Right, false) != 0 ? string.Empty : Strings.Trim(Conversions.ToString(oleDbDataReader[3]));
        }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con11.State != ConnectionState.Open)
      return;
    connection_Database.con11.Close();
  }

  public static void get_ComPort(ref string comport)
  {
    OleDbCommand oleDbCommand = new OleDbCommand();
    string empty = string.Empty;
    try
    {
      if (connection_Database.con15.State == ConnectionState.Closed)
        connection_Database.con15.Open();
      oleDbCommand.CommandText = "select comport from configuration where ID = 1";
      oleDbCommand.CommandType = CommandType.Text;
      oleDbCommand.Connection = connection_Database.con15;
      comport = Conversions.ToString(oleDbCommand.ExecuteScalar());
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con15.State != ConnectionState.Open)
      return;
    connection_Database.con15.Close();
  }

  public static void get_Auto(ref string reg_auto)
  {
    OleDbCommand oleDbCommand = new OleDbCommand();
    string empty = string.Empty;
    try
    {
      if (connection_Database.con15.State == ConnectionState.Closed)
        connection_Database.con15.Open();
      oleDbCommand.CommandText = "select val from automatic where ID = 1";
      oleDbCommand.CommandType = CommandType.Text;
      oleDbCommand.Connection = connection_Database.con15;
      reg_auto = Conversions.ToString(oleDbCommand.ExecuteScalar());
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con15.State != ConnectionState.Open)
      return;
    connection_Database.con15.Close();
  }

  public static void get_Station_codes(
    ref string[] sc_codes,
    ref string[] sc_names,
    ref string length)
  {
    OleDbCommand oleDbCommand = new OleDbCommand();
    int index = 0;
    try
    {
      if (connection_Database.con11.State == ConnectionState.Closed)
        connection_Database.con11.Open();
      oleDbCommand.CommandText = "select * from tbl_StationCode";
      oleDbCommand.CommandType = CommandType.Text;
      oleDbCommand.Connection = connection_Database.con11;
      OleDbDataReader oleDbDataReader = oleDbCommand.ExecuteReader();
      if (oleDbDataReader.HasRows)
      {
        while (oleDbDataReader.Read())
        {
          sc_codes[index] = Conversions.ToString(oleDbDataReader[0]);
          sc_names[index] = Conversions.ToString(oleDbDataReader[1]);
          checked { ++index; }
        }
      }
      length = Conversions.ToString(index);
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con11.State != ConnectionState.Open)
      return;
    connection_Database.con11.Close();
  }

  public static void get_Station_code_name(string station_name, ref string station_code)
  {
    OleDbCommand oleDbCommand = new OleDbCommand();
    try
    {
      if (connection_Database.con11.State == ConnectionState.Closed)
        connection_Database.con11.Open();
      oleDbCommand.CommandText = "select * from tbl_StationCode where EnglishStationName = '{Strings.Trim(station_name)}'";
      oleDbCommand.CommandType = CommandType.Text;
      oleDbCommand.Connection = connection_Database.con11;
      OleDbDataReader oleDbDataReader = oleDbCommand.ExecuteReader();
      if (oleDbDataReader.HasRows)
      {
        while (oleDbDataReader.Read())
          station_code = Conversions.ToString(oleDbDataReader[0]);
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con11.State != ConnectionState.Open)
      return;
    connection_Database.con11.Close();
  }

  public static void get_Station_name(string station_code, ref string station_name)
  {
    OleDbCommand oleDbCommand = new OleDbCommand();
    try
    {
      if (connection_Database.con11.State == ConnectionState.Closed)
        connection_Database.con11.Open();
      oleDbCommand.CommandText = "select * from tbl_StationCode where EnglishStationName = '{Strings.Trim(station_code)}'";
      oleDbCommand.CommandType = CommandType.Text;
      oleDbCommand.Connection = connection_Database.con11;
      OleDbDataReader oleDbDataReader = oleDbCommand.ExecuteReader();
      if (oleDbDataReader.HasRows)
      {
        while (oleDbDataReader.Read())
          station_name = Conversions.ToString(oleDbDataReader[1]);
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    if (connection_Database.con11.State != ConnectionState.Open)
      return;
    connection_Database.con11.Close();
  }

  public static void get_trainname(string train_no, ref string train_name)
  {
    int index = 0;
    while (index < frmMainFormIPIS.train_cnt)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_no, frmMainFormIPIS.train_details[index].train_no, false) == 0)
      {
        train_name = frmMainFormIPIS.train_details[index].train_name;
        break;
      }
      checked { ++index; }
    }
  }

  public static void get_name_station(
    string train_no,
    ref string eng_name,
    ref string hin_name,
    ref string reg_name)
  {
    byte index = 0;
    while (index < (byte) 50)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.name_station[(int) index].train_no, train_no, false) == 0)
      {
        eng_name = frmMainFormIPIS.name_station[(int) index].eng_station;
        hin_name = frmMainFormIPIS.name_station[(int) index].hin_station;
        reg_name = frmMainFormIPIS.name_station[(int) index].reg_station;
        break;
      }
      checked { ++index; }
    }
  }
}

}