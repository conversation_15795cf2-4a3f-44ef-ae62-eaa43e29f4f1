// Decompiled with JetBrains decompiler
// Type: ipis.frmLanguage
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using ipis.My;
using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

namespace ipis
{

[DesignerGenerated]
public class frmLanguage : Form
{
  private static List<WeakReference> __ENCList = new List<WeakReference>();
  private IContainer components;
  [AccessedThroughProperty("btnSave")]
  private Button _btnSave;
  [AccessedThroughProperty("btnExit")]
  private Button _btnExit;
  [AccessedThroughProperty("Label7")]
  private Label _Label7;
  [AccessedThroughProperty("rad2Lang")]
  private RadioButton _rad2Lang;
  [AccessedThroughProperty("rad3Lang")]
  private RadioButton _rad3Lang;
  [AccessedThroughProperty("cmbRegLanguage")]
  private ComboBox _cmbRegLanguage;
  [AccessedThroughProperty("Label1")]
  private Label _Label1;

  [DebuggerNonUserCode]
  static frmLanguage()
  {
  }

  [DebuggerNonUserCode]
  public frmLanguage()
  {
    this.Load += new EventHandler(this.frmLanguage_Load);
    frmLanguage.__ENCAddToList((object) this);
    this.InitializeComponent();
  }

  [DebuggerNonUserCode]
  private static void __ENCAddToList(object value)
  {
    lock (frmLanguage.__ENCList)
    {
      if (frmLanguage.__ENCList.Count == frmLanguage.__ENCList.Capacity)
      {
        int index1 = 0;
        int num = checked (frmLanguage.__ENCList.Count - 1);
        int index2 = 0;
        while (index2 <= num)
        {
          if (frmLanguage.__ENCList[index2].IsAlive)
          {
            if (index2 != index1)
              frmLanguage.__ENCList[index1] = frmLanguage.__ENCList[index2];
            checked { ++index1; }
          }
          checked { ++index2; }
        }
        frmLanguage.__ENCList.RemoveRange(index1, checked (frmLanguage.__ENCList.Count - index1));
        frmLanguage.__ENCList.Capacity = frmLanguage.__ENCList.Count;
      }
      frmLanguage.__ENCList.Add(new WeakReference(RuntimeHelpers.GetObjectValue(value)));
    }
  }

  [DebuggerNonUserCode]
  protected override void Dispose(bool disposing)
  {
    try
    {
      if (!disposing || this.components == null)
        return;
      this.components.Dispose();
    }
    finally
    {
      base.Dispose(disposing);
    }
  }

  [DebuggerStepThrough]
  private void InitializeComponent()
  {
    this.btnSave = new Button();
    this.btnExit = new Button();
    this.Label7 = new Label();
    this.rad2Lang = new RadioButton();
    this.rad3Lang = new RadioButton();
    this.cmbRegLanguage = new ComboBox();
    this.Label1 = new Label();
    this.SuspendLayout();
    this.btnSave.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnSave1 = this.btnSave;
    Point point1 = new Point(179, 164);
    Point point2 = point1;
    btnSave1.Location = point2;
    Button btnSave2 = this.btnSave;
    Padding padding1 = new Padding(5);
    Padding padding2 = padding1;
    btnSave2.Margin = padding2;
    this.btnSave.Name = "btnSave";
    Button btnSave3 = this.btnSave;
    Size size1 = new Size(97, 35);
    Size size2 = size1;
    btnSave3.Size = size2;
    this.btnSave.TabIndex = 8;
    this.btnSave.Text = "Save";
    this.btnSave.UseVisualStyleBackColor = true;
    this.btnExit.DialogResult = DialogResult.Cancel;
    this.btnExit.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnExit1 = this.btnExit;
    point1 = new Point(344, 164);
    Point point3 = point1;
    btnExit1.Location = point3;
    Button btnExit2 = this.btnExit;
    padding1 = new Padding(5);
    Padding padding3 = padding1;
    btnExit2.Margin = padding3;
    this.btnExit.Name = "btnExit";
    Button btnExit3 = this.btnExit;
    size1 = new Size(88, 35);
    Size size3 = size1;
    btnExit3.Size = size3;
    this.btnExit.TabIndex = 9;
    this.btnExit.Text = "Exit";
    this.btnExit.UseVisualStyleBackColor = true;
    this.Label7.AutoSize = true;
    this.Label7.Font = new Font("Microsoft Sans Serif", 12f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label label7_1 = this.Label7;
    point1 = new Point(258, 96 /*0x60*/);
    Point point4 = point1;
    label7_1.Location = point4;
    Label label7_2 = this.Label7;
    padding1 = new Padding(5, 0, 5, 0);
    Padding padding4 = padding1;
    label7_2.Margin = padding4;
    this.Label7.Name = "Label7";
    Label label7_3 = this.Label7;
    size1 = new Size(0, 20);
    Size size4 = size1;
    label7_3.Size = size4;
    this.Label7.TabIndex = 22;
    this.rad2Lang.AutoSize = true;
    this.rad2Lang.Font = new Font("Microsoft Sans Serif", 12f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    RadioButton rad2Lang1 = this.rad2Lang;
    point1 = new Point(78, 56);
    Point point5 = point1;
    rad2Lang1.Location = point5;
    RadioButton rad2Lang2 = this.rad2Lang;
    padding1 = new Padding(5);
    Padding padding5 = padding1;
    rad2Lang2.Margin = padding5;
    this.rad2Lang.Name = "rad2Lang";
    RadioButton rad2Lang3 = this.rad2Lang;
    size1 = new Size(269, 24);
    Size size5 = size1;
    rad2Lang3.Size = size5;
    this.rad2Lang.TabIndex = 23;
    this.rad2Lang.TabStop = true;
    this.rad2Lang.Text = "2 Lanuage (English and Hindi)";
    this.rad2Lang.UseVisualStyleBackColor = true;
    this.rad3Lang.AutoSize = true;
    this.rad3Lang.Checked = true;
    RadioButton rad3Lang1 = this.rad3Lang;
    point1 = new Point(78, 102);
    Point point6 = point1;
    rad3Lang1.Location = point6;
    RadioButton rad3Lang2 = this.rad3Lang;
    padding1 = new Padding(5);
    Padding padding6 = padding1;
    rad3Lang2.Margin = padding6;
    this.rad3Lang.Name = "rad3Lang";
    RadioButton rad3Lang3 = this.rad3Lang;
    size1 = new Size(372, 24);
    Size size6 = size1;
    rad3Lang3.Size = size6;
    this.rad3Lang.TabIndex = 24;
    this.rad3Lang.TabStop = true;
    this.rad3Lang.Text = "3 Language (English , Hindi, And Regional)";
    this.rad3Lang.UseVisualStyleBackColor = true;
    this.cmbRegLanguage.FormattingEnabled = true;
    this.cmbRegLanguage.Items.AddRange(new object[19]
    {
      (object) "Assamese",
      (object) "Bengali",
      (object) "Bhilli",
      (object) "Bhojpuri",
      (object) "Bihari",
      (object) "Devanagiri",
      (object) "Gujarati",
      (object) "Kannada",
      (object) "Kashmiri",
      (object) "Konkani",
      (object) "Marathi",
      (object) "Marwari",
      (object) "Nepali",
      (object) "Oriya",
      (object) "Pahari",
      (object) "Punjabi",
      (object) "Santhali",
      (object) "Tamil",
      (object) "Telugu"
    });
    ComboBox cmbRegLanguage1 = this.cmbRegLanguage;
    point1 = new Point(470, 102);
    Point point7 = point1;
    cmbRegLanguage1.Location = point7;
    this.cmbRegLanguage.Name = "cmbRegLanguage";
    ComboBox cmbRegLanguage2 = this.cmbRegLanguage;
    size1 = new Size(121, 28);
    Size size7 = size1;
    cmbRegLanguage2.Size = size7;
    this.cmbRegLanguage.Sorted = true;
    this.cmbRegLanguage.TabIndex = 25;
    this.Label1.AutoSize = true;
    Label label1_1 = this.Label1;
    point1 = new Point(200, 9);
    Point point8 = point1;
    label1_1.Location = point8;
    this.Label1.Name = "Label1";
    Label label1_2 = this.Label1;
    size1 = new Size(243, 20);
    Size size8 = size1;
    label1_2.Size = size8;
    this.Label1.TabIndex = 26;
    this.Label1.Text = "Select the Languages Option";
    this.AcceptButton = (IButtonControl) this.btnSave;
    this.AutoScaleDimensions = new SizeF(10f, 20f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.BackColor = Color.PowderBlue;
    this.CancelButton = (IButtonControl) this.btnExit;
    size1 = new Size(672, 223);
    this.ClientSize = size1;
    this.Controls.Add((Control) this.Label1);
    this.Controls.Add((Control) this.cmbRegLanguage);
    this.Controls.Add((Control) this.rad3Lang);
    this.Controls.Add((Control) this.rad2Lang);
    this.Controls.Add((Control) this.Label7);
    this.Controls.Add((Control) this.btnExit);
    this.Controls.Add((Control) this.btnSave);
    this.Font = new Font("Microsoft Sans Serif", 12f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.FormBorderStyle = FormBorderStyle.FixedToolWindow;
    padding1 = new Padding(5);
    this.Margin = padding1;
    this.Name = "frmLanguage";
    this.Text = "Language";
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  internal virtual Button btnSave
  {
    [DebuggerNonUserCode] get { return this._btnSave; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnSave_Click);
      if (this._btnSave != null)
        this._btnSave.Click -= eventHandler;
      this._btnSave = value;
      if (this._btnSave == null)
        return;
      this._btnSave.Click += eventHandler;
    }
  }

  internal virtual Button btnExit
  {
    [DebuggerNonUserCode] get { return this._btnExit; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnExit_Click);
      if (this._btnExit != null)
        this._btnExit.Click -= eventHandler;
      this._btnExit = value;
      if (this._btnExit == null)
        return;
      this._btnExit.Click += eventHandler;
    }
  }

  internal virtual Label Label7
  {
    [DebuggerNonUserCode] get { return this._Label7; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Label7 = value; }
  }

  internal virtual RadioButton rad2Lang
  {
    [DebuggerNonUserCode] get { return this._rad2Lang; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._rad2Lang = value; }
  }

  internal virtual RadioButton rad3Lang
  {
    [DebuggerNonUserCode] get { return this._rad3Lang; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.rad3Lang_CheckedChanged);
      if (this._rad3Lang != null)
        this._rad3Lang.CheckedChanged -= eventHandler;
      this._rad3Lang = value;
      if (this._rad3Lang == null)
        return;
      this._rad3Lang.CheckedChanged += eventHandler;
    }
  }

  internal virtual ComboBox cmbRegLanguage
  {
    [DebuggerNonUserCode] get { return this._cmbRegLanguage; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._cmbRegLanguage = value;
    }
  }

  internal virtual Label Label1
  {
    [DebuggerNonUserCode] get { return this._Label1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Label1 = value; }
  }

  private void frmLanguage_Load(object sender, EventArgs e)
  {
    network_db_read.get_language_details();
    if (frmMainFormIPIS.language_selection.regional_language_selected)
    {
      this.rad3Lang.Checked = true;
      this.rad2Lang.Checked = false;
      this.cmbRegLanguage.Text = frmMainFormIPIS.language_selection.regional_language_name;
    }
    else
    {
      this.rad3Lang.Checked = false;
      this.rad2Lang.Checked = true;
      this.cmbRegLanguage.Text = string.Empty;
    }
  }

  private void btnSave_Click(object sender, EventArgs e)
  {
    if (this.rad2Lang.Checked)
    {
      frmMainFormIPIS.language_selection.regional_language_selected = false;
      frmMainFormIPIS.language_selection.regional_language_name = string.Empty;
    }
    if (this.rad3Lang.Checked)
    {
      frmMainFormIPIS.language_selection.regional_language_selected = true;
      frmMainFormIPIS.language_selection.regional_language_name = this.cmbRegLanguage.Text;
    }
    network_db_read.set_language_details();
    int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "languages information saved", "Msg Box", 0, 0, 0);
    Strings.Format((object) DateTime.Now.Date, "dd-MM-yyyy");
    try
    {
      string str = "Z:\\Database\\tbl_language.mdb";
      string sourceFileName = "C:\\IPIS\\Database\\tbl_language.mdb";
      if (!File.Exists(str))
        File.Create(str);
      bool overwrite = true;
      MyProject.Computer.FileSystem.CopyFile(sourceFileName, str, overwrite);
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
    this.Close();
  }

  private void btnExit_Click(object sender, EventArgs e)
{
  this.Close();
}

  private void rad3Lang_CheckedChanged(object sender, EventArgs e)
  {
    if (this.rad3Lang.Checked)
      this.cmbRegLanguage.Enabled = true;
    else
      this.cmbRegLanguage.Enabled = false;
  }
}

}