﻿namespace Announcement
{
	// Token: 0x0200000B RID: 11
	public partial class Reports : global::System.Windows.Forms.Form
	{
		// Token: 0x06000050 RID: 80 RVA: 0x0000BA74 File Offset: 0x00009C74
		protected override void Dispose(bool disposing)
		{
			bool flag = disposing && this.components != null;
			if (flag)
			{
				this.components.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06000051 RID: 81 RVA: 0x0000BAAC File Offset: 0x00009CAC
		private void InitializeComponent()
		{
			global::System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle = new global::System.Windows.Forms.DataGridViewCellStyle();
			this.Grp_Report = new global::System.Windows.Forms.GroupBox();
			this.BTN_Exit = new global::System.Windows.Forms.Button();
			this.BTN_Save = new global::System.Windows.Forms.Button();
			this.BTN_Gen = new global::System.Windows.Forms.Button();
			this.CB_RType = new global::System.Windows.Forms.ComboBox();
			this.LB_RT = new global::System.Windows.Forms.Label();
			this.DTP_TDate = new global::System.Windows.Forms.DateTimePicker();
			this.LB_Dateto = new global::System.Windows.Forms.Label();
			this.LB_Date = new global::System.Windows.Forms.Label();
			this.DTP_FDate = new global::System.Windows.Forms.DateTimePicker();
			this.Dgv_Report = new global::System.Windows.Forms.DataGridView();
			this.Save_FileDialog = new global::System.Windows.Forms.SaveFileDialog();
			this.Grp_Report.SuspendLayout();
			((global::System.ComponentModel.ISupportInitialize)this.Dgv_Report).BeginInit();
			base.SuspendLayout();
			this.Grp_Report.BackColor = global::System.Drawing.Color.FromArgb(255, 192, 192);
			this.Grp_Report.Controls.Add(this.BTN_Exit);
			this.Grp_Report.Controls.Add(this.BTN_Save);
			this.Grp_Report.Controls.Add(this.BTN_Gen);
			this.Grp_Report.Controls.Add(this.CB_RType);
			this.Grp_Report.Controls.Add(this.LB_RT);
			this.Grp_Report.Controls.Add(this.DTP_TDate);
			this.Grp_Report.Controls.Add(this.LB_Dateto);
			this.Grp_Report.Controls.Add(this.LB_Date);
			this.Grp_Report.Controls.Add(this.DTP_FDate);
			this.Grp_Report.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 11.25f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 0);
			this.Grp_Report.ForeColor = global::System.Drawing.Color.Red;
			this.Grp_Report.Location = new global::System.Drawing.Point(2, 12);
			this.Grp_Report.Name = "Grp_Report";
			this.Grp_Report.Size = new global::System.Drawing.Size(222, 429);
			this.Grp_Report.TabIndex = 0;
			this.Grp_Report.TabStop = false;
			this.Grp_Report.Text = "Report Details";
			this.Grp_Report.Enter += new global::System.EventHandler(this.Grp_Report_Enter);
			this.BTN_Exit.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 14.25f, global::System.Drawing.FontStyle.Bold, global::System.Drawing.GraphicsUnit.Point, 0);
			this.BTN_Exit.ForeColor = global::System.Drawing.Color.FromArgb(192, 0, 0);
			this.BTN_Exit.Location = new global::System.Drawing.Point(6, 387);
			this.BTN_Exit.Name = "BTN_Exit";
			this.BTN_Exit.Size = new global::System.Drawing.Size(205, 36);
			this.BTN_Exit.TabIndex = 8;
			this.BTN_Exit.Text = "Exit";
			this.BTN_Exit.UseVisualStyleBackColor = true;
			this.BTN_Exit.Click += new global::System.EventHandler(this.BTN_Exit_Click);
			this.BTN_Save.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 14.25f, global::System.Drawing.FontStyle.Bold, global::System.Drawing.GraphicsUnit.Point, 0);
			this.BTN_Save.ForeColor = global::System.Drawing.Color.Blue;
			this.BTN_Save.Location = new global::System.Drawing.Point(6, 345);
			this.BTN_Save.Name = "BTN_Save";
			this.BTN_Save.Size = new global::System.Drawing.Size(205, 36);
			this.BTN_Save.TabIndex = 7;
			this.BTN_Save.Text = "Save";
			this.BTN_Save.UseVisualStyleBackColor = true;
			this.BTN_Save.Click += new global::System.EventHandler(this.BTN_Save_Click);
			this.BTN_Gen.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 14.25f, global::System.Drawing.FontStyle.Bold, global::System.Drawing.GraphicsUnit.Point, 0);
			this.BTN_Gen.ForeColor = global::System.Drawing.Color.FromArgb(0, 192, 0);
			this.BTN_Gen.Location = new global::System.Drawing.Point(9, 303);
			this.BTN_Gen.Name = "BTN_Gen";
			this.BTN_Gen.Size = new global::System.Drawing.Size(205, 36);
			this.BTN_Gen.TabIndex = 6;
			this.BTN_Gen.Text = "Generate";
			this.BTN_Gen.UseVisualStyleBackColor = true;
			this.BTN_Gen.Click += new global::System.EventHandler(this.BTN_Gen_Click);
			this.CB_RType.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 12f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 0);
			this.CB_RType.FormattingEnabled = true;
			this.CB_RType.Items.AddRange(new object[]
			{
				"Announcment",
				"Advertisement",
				"Slogans",
				"User Login"
			});
			this.CB_RType.Location = new global::System.Drawing.Point(6, 228);
			this.CB_RType.Name = "CB_RType";
			this.CB_RType.Size = new global::System.Drawing.Size(205, 28);
			this.CB_RType.TabIndex = 5;
			this.CB_RType.Text = "Select Report Type";
			this.CB_RType.SelectedIndexChanged += new global::System.EventHandler(this.CB_RType_SelectedIndexChanged);
			this.LB_RT.AutoSize = true;
			this.LB_RT.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 12f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 0);
			this.LB_RT.ForeColor = global::System.Drawing.Color.FromArgb(64, 0, 0);
			this.LB_RT.Location = new global::System.Drawing.Point(10, 205);
			this.LB_RT.Name = "LB_RT";
			this.LB_RT.Size = new global::System.Drawing.Size(96, 20);
			this.LB_RT.TabIndex = 4;
			this.LB_RT.Text = "Report Type";
			this.DTP_TDate.CustomFormat = "dd-MMM-yyyy HH:mm:ss";
			this.DTP_TDate.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 12f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 0);
			this.DTP_TDate.Format = global::System.Windows.Forms.DateTimePickerFormat.Custom;
			this.DTP_TDate.Location = new global::System.Drawing.Point(6, 149);
			this.DTP_TDate.Name = "DTP_TDate";
			this.DTP_TDate.Size = new global::System.Drawing.Size(205, 26);
			this.DTP_TDate.TabIndex = 3;
			this.DTP_TDate.ValueChanged += new global::System.EventHandler(this.dateTimePicker2_ValueChanged);
			this.LB_Dateto.AutoSize = true;
			this.LB_Dateto.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 12f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 0);
			this.LB_Dateto.ForeColor = global::System.Drawing.Color.FromArgb(64, 0, 0);
			this.LB_Dateto.Location = new global::System.Drawing.Point(10, 126);
			this.LB_Dateto.Name = "LB_Dateto";
			this.LB_Dateto.Size = new global::System.Drawing.Size(66, 20);
			this.LB_Dateto.TabIndex = 2;
			this.LB_Dateto.Text = "To Date";
			this.LB_Date.AutoSize = true;
			this.LB_Date.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 12f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 0);
			this.LB_Date.ForeColor = global::System.Drawing.Color.FromArgb(64, 0, 0);
			this.LB_Date.Location = new global::System.Drawing.Point(10, 42);
			this.LB_Date.Name = "LB_Date";
			this.LB_Date.Size = new global::System.Drawing.Size(85, 20);
			this.LB_Date.TabIndex = 1;
			this.LB_Date.Text = "From Date";
			this.DTP_FDate.CustomFormat = "dd-MMM-yyyy HH:mm:ss";
			this.DTP_FDate.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 12f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 0);
			this.DTP_FDate.Format = global::System.Windows.Forms.DateTimePickerFormat.Custom;
			this.DTP_FDate.Location = new global::System.Drawing.Point(9, 65);
			this.DTP_FDate.Name = "DTP_FDate";
			this.DTP_FDate.Size = new global::System.Drawing.Size(202, 26);
			this.DTP_FDate.TabIndex = 0;
			this.DTP_FDate.Value = new global::System.DateTime(2023, 3, 31, 0, 0, 0, 0);
			this.DTP_FDate.ValueChanged += new global::System.EventHandler(this.DTP_FDate_ValueChanged);
			this.Dgv_Report.AllowUserToAddRows = false;
			this.Dgv_Report.AutoSizeColumnsMode = global::System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
			this.Dgv_Report.CellBorderStyle = global::System.Windows.Forms.DataGridViewCellBorderStyle.Raised;
			dataGridViewCellStyle.Alignment = global::System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
			dataGridViewCellStyle.BackColor = global::System.Drawing.SystemColors.Control;
			dataGridViewCellStyle.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 8.25f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 0);
			dataGridViewCellStyle.ForeColor = global::System.Drawing.SystemColors.WindowText;
			dataGridViewCellStyle.SelectionBackColor = global::System.Drawing.SystemColors.Highlight;
			dataGridViewCellStyle.SelectionForeColor = global::System.Drawing.SystemColors.HighlightText;
			dataGridViewCellStyle.WrapMode = global::System.Windows.Forms.DataGridViewTriState.True;
			this.Dgv_Report.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle;
			this.Dgv_Report.ColumnHeadersHeightSizeMode = global::System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
			this.Dgv_Report.Location = new global::System.Drawing.Point(230, 12);
			this.Dgv_Report.Name = "Dgv_Report";
			this.Dgv_Report.ReadOnly = true;
			this.Dgv_Report.RowHeadersVisible = false;
			this.Dgv_Report.ScrollBars = global::System.Windows.Forms.ScrollBars.Vertical;
			this.Dgv_Report.SelectionMode = global::System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
			this.Dgv_Report.Size = new global::System.Drawing.Size(1093, 445);
			this.Dgv_Report.TabIndex = 37;
			this.Dgv_Report.CellContentClick += new global::System.Windows.Forms.DataGridViewCellEventHandler(this.Dgv_Report_CellContentClick);
			base.AutoScaleDimensions = new global::System.Drawing.SizeF(6f, 13f);
			base.AutoScaleMode = global::System.Windows.Forms.AutoScaleMode.Font;
			this.BackColor = global::System.Drawing.Color.FromArgb(192, 255, 192);
			base.ClientSize = new global::System.Drawing.Size(1327, 463);
			base.ControlBox = false;
			base.Controls.Add(this.Grp_Report);
			base.Controls.Add(this.Dgv_Report);
			base.Name = "Reports";
			base.StartPosition = global::System.Windows.Forms.FormStartPosition.CenterScreen;
			this.Text = "Reports";
			base.Load += new global::System.EventHandler(this.Reports_Load);
			this.Grp_Report.ResumeLayout(false);
			this.Grp_Report.PerformLayout();
			((global::System.ComponentModel.ISupportInitialize)this.Dgv_Report).EndInit();
			base.ResumeLayout(false);
		}

		// Token: 0x04000056 RID: 86
		private global::System.ComponentModel.IContainer components = null;

		// Token: 0x04000057 RID: 87
		private global::System.Windows.Forms.GroupBox Grp_Report;

		// Token: 0x04000058 RID: 88
		private global::System.Windows.Forms.Button BTN_Save;

		// Token: 0x04000059 RID: 89
		private global::System.Windows.Forms.Button BTN_Gen;

		// Token: 0x0400005A RID: 90
		private global::System.Windows.Forms.ComboBox CB_RType;

		// Token: 0x0400005B RID: 91
		private global::System.Windows.Forms.Label LB_RT;

		// Token: 0x0400005C RID: 92
		private global::System.Windows.Forms.DateTimePicker DTP_TDate;

		// Token: 0x0400005D RID: 93
		private global::System.Windows.Forms.Label LB_Dateto;

		// Token: 0x0400005E RID: 94
		private global::System.Windows.Forms.Label LB_Date;

		// Token: 0x0400005F RID: 95
		private global::System.Windows.Forms.DateTimePicker DTP_FDate;

		// Token: 0x04000060 RID: 96
		private global::System.Windows.Forms.Button BTN_Exit;

		// Token: 0x04000061 RID: 97
		private global::System.Windows.Forms.DataGridView Dgv_Report;

		// Token: 0x04000062 RID: 98
		private global::System.Windows.Forms.SaveFileDialog Save_FileDialog;
	}
}
