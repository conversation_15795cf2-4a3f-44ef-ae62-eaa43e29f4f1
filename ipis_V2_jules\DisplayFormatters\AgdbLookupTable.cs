using System;

namespace ipis_V2_jules.DisplayFormatters
{
    /// <summary>
    /// Contains static byte arrays and constants for character patterns and control codes
    /// for AGDB (Alpha-Numeric Graphic Display Board) displays.
    /// The values are based on the original agdb_lookup_table.cs.
    /// </summary>
    public static class AgdbLookupTable
    {
        // --- Character Byte Patterns (Typically 5-byte arrays) ---
        public static readonly byte[] BYTE_ZERO_0 = new byte[5] { 0x7E, 0x91, 0x89, 0x85, 0x7E };
        public static readonly byte[] BYTE_ONE_1 = new byte[5] { 0x80, 0x82, 0xFF, 0x80, 0x80 };
        public static readonly byte[] BYTE_TWO_2 = new byte[5] { 0xC2, 0xA1, 0x91, 0x89, 0x86 };
        public static readonly byte[] BYTE_THREE_3 = new byte[5] { 0x42, 0x89, 0x89, 0x89, 0x76 };
        public static readonly byte[] BYTE_FOUR_4 = new byte[5] { 0x38, 0x24, 0x22, 0xFF, 0x20 };
        public static readonly byte[] BYTE_FIVE_5 = new byte[5] { 0x8F, 0x89, 0x89, 0x89, 0x71 };
        public static readonly byte[] BYTE_SIX_6 = new byte[5] { 0x7E, 0x89, 0x89, 0x89, 0x72 };
        public static readonly byte[] BYTE_SEVEN_7 = new byte[5] { 0x01, 0x01, 0xF1, 0x09, 0x06 };
        public static readonly byte[] BYTE_EIGHT_8 = new byte[5] { 0x76, 0x89, 0x89, 0x89, 0x76 };
        public static readonly byte[] BYTE_NINE_9 = new byte[5] { 0x46, 0x89, 0x89, 0x89, 0x7E };

        public static readonly byte[] A_A = new byte[5] { 0xFC, 0x12, 0x11, 0x12, 0xFC };
        public static readonly byte[] B_B = new byte[5] { 0xFF, 0x89, 0x89, 0x89, 0x76 };
        public static readonly byte[] C_C = new byte[5] { 0x7E, 0x81, 0x81, 0x81, 0x81 };
        public static readonly byte[] D_D = new byte[5] { 0xFF, 0x81, 0x81, 0x81, 0x7E };
        public static readonly byte[] E_E = new byte[5] { 0xFF, 0x89, 0x89, 0x89, 0x81 };
        public static readonly byte[] F_F = new byte[5] { 0xFF, 0x09, 0x09, 0x09, 0x01 };
        public static readonly byte[] G_G = new byte[5] { 0x7E, 0x81, 0x89, 0x89, 0x7A };
        public static readonly byte[] H_H = new byte[5] { 0xFF, 0x08, 0x08, 0x08, 0xFF };
        public static readonly byte[] I_I = new byte[5] { 0x81, 0x81, 0xFF, 0x81, 0x81 };
        public static readonly byte[] J_J = new byte[5] { 0x61, 0x81, 0x81, 0x7F, 0x01 };
        public static readonly byte[] K_K = new byte[5] { 0xFF, 0x18, 0x24, 0x42, 0x81 };
        public static readonly byte[] L_L = new byte[5] { 0xFF, 0x80, 0x80, 0x80, 0x80 };
        public static readonly byte[] M_M = new byte[5] { 0xFF, 0x04, 0x08, 0x04, 0xFF };
        public static readonly byte[] N_N = new byte[5] { 0xFF, 0x04, 0x08, 0x10, 0xFF };
        public static readonly byte[] O_O = new byte[5] { 0x7E, 0x81, 0x81, 0x81, 0x7E };
        public static readonly byte[] P_P = new byte[5] { 0xFF, 0x09, 0x09, 0x09, 0x06 };
        public static readonly byte[] Q_Q = new byte[5] { 0x7E, 0x81, 0xA1, 0xC1, 0xFE };
        public static readonly byte[] R_R = new byte[5] { 0xFF, 0x19, 0x29, 0x49, 0x86 };
        public static readonly byte[] S_S = new byte[5] { 0x46, 0x89, 0x89, 0x89, 0x72 };
        public static readonly byte[] T_T = new byte[5] { 0x01, 0x01, 0xFF, 0x01, 0x01 };
        public static readonly byte[] U_U = new byte[5] { 0x7F, 0x80, 0x80, 0x80, 0x7F };
        public static readonly byte[] V_V = new byte[5] { 0x3F, 0x40, 0x80, 0x40, 0x3F };
        public static readonly byte[] W_W = new byte[5] { 0xFF, 0x40, 0x20, 0x40, 0xFF };
        public static readonly byte[] X_X = new byte[5] { 0xE3, 0x14, 0x08, 0x14, 0xE3 };
        public static readonly byte[] Y_Y = new byte[5] { 0x07, 0x08, 0xF0, 0x08, 0x07 };
        public static readonly byte[] Z_Z = new byte[5] { 0xE1, 0x91, 0x89, 0x85, 0x83 };

        public static readonly byte[] BYTE_SPACE = new byte[5] { 0x00, 0x00, 0x00, 0x00, 0x00 };

        // Note: The prompt has "public static readonly byte BYTE_COLON = 0x24;" which is a single byte.
        // If a 5-byte pattern for colon is needed, it would be an array. Assuming the single byte is correct.
        // For example, a 5-byte pattern for colon might be:
        // public static readonly byte[] BYTE_COLON_PATTERN = new byte[5] { 0x00, 0x24, 0x00, 0x24, 0x00 };

        public static readonly byte[] SIGN_RIGHT = new byte[5] { 0x10, 0x10, 0x54, 0x38, 0x10 };
        public static readonly byte[] SIGN_LEFT = new byte[5] { 0x10, 0x38, 0x54, 0x10, 0x10 };

        public static readonly byte[] SIGN_LINE = new byte[2] { 0xFF, 0xFF };

        // --- Single Byte Constants ---
        public const byte BYTE_COLON = 0x24; // ASCII for ':', was 36 decimal in original comment

        public const byte TRAIN_NO1 = 0;
        public const byte TRAIN_NO2 = 6;
        public const byte TRAIN_NO3 = 12;
        public const byte TRAIN_NO4 = 18;
        public const byte TRAIN_NO5 = 24;

        public const byte TRAIN_NO_CUNT = 6; // Likely "COUNT", refers to number of characters for train number
        public const byte TRAIN_NAME_CUNT = 32; // Likely "COUNT", refers to max characters for train name

        public const byte TRAIN_TIME = 181; // Starting position/offset for time display?
        public const byte TRAIN_TYPE = 210; // Starting position/offset for train type display?
        public const byte TRAIN_PF_NO = 222; // Starting position/offset for platform number display?

        public const byte MAX_TRAIN_NAME_LENGTH = 24;

        // --- Further Notes ---
        // This table is now populated with values provided from the original file.
        // Ensure any other necessary characters or constants from the original
        // ipis/agdb_lookup_table.cs are also transcribed if they were missed in the prompt.
    }
}
