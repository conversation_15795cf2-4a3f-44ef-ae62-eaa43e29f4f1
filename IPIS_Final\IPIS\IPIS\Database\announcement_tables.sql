-- SQLite Database Tables for Announcement Management System

-- Table: AnnouncementTemplates
CREATE TABLE IF NOT EXISTS AnnouncementTemplates (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    Name TEXT NOT NULL UNIQUE,
    Description TEXT,
    ArrivalDeparture TEXT NOT NULL DEFAULT 'A', -- A for Arrival, D for Departure
    IsActive BOOLEAN NOT NULL DEFAULT 1,
    CreatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt DATETIME
);

-- Table: AnnouncementSequences
CREATE TABLE IF NOT EXISTS AnnouncementSequences (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    TemplateId INTEGER NOT NULL,
    LanguageId INTEGER NOT NULL,
    Name TEXT NOT NULL,
    IsActive BOOLEAN NOT NULL DEFAULT 1,
    CreatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt DATETIME,
    FOREIGN KEY (TemplateId) REFERENCES AnnouncementTemplates(Id) ON DELETE CASCADE,
    <PERSON>OR<PERSON><PERSON><PERSON> KEY (LanguageId) REFERENCES Languages(Id) ON DELETE CASCADE,
    UNIQUE(TemplateId, LanguageId)
);

-- Table: SequenceItems
CREATE TABLE IF NOT EXISTS SequenceItems (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    SequenceId INTEGER NOT NULL,
    OrderIndex INTEGER NOT NULL,
    Type INTEGER NOT NULL, -- 1 = AudioFile, 2 = Placeholder
    Content TEXT NOT NULL, -- Audio file path or placeholder name
    Description TEXT,
    IsActive BOOLEAN NOT NULL DEFAULT 1,
    CreatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt DATETIME,
    FOREIGN KEY (SequenceId) REFERENCES AnnouncementSequences(Id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_announcement_templates_active ON AnnouncementTemplates(IsActive);
CREATE INDEX IF NOT EXISTS idx_announcement_templates_ad ON AnnouncementTemplates(ArrivalDeparture);
CREATE INDEX IF NOT EXISTS idx_announcement_sequences_template ON AnnouncementSequences(TemplateId);
CREATE INDEX IF NOT EXISTS idx_announcement_sequences_language ON AnnouncementSequences(LanguageId);
CREATE INDEX IF NOT EXISTS idx_announcement_sequences_active ON AnnouncementSequences(IsActive);
CREATE INDEX IF NOT EXISTS idx_sequence_items_sequence ON SequenceItems(SequenceId);
CREATE INDEX IF NOT EXISTS idx_sequence_items_order ON SequenceItems(SequenceId, OrderIndex);
CREATE INDEX IF NOT EXISTS idx_sequence_items_active ON SequenceItems(IsActive);

-- Insert some default announcement templates
INSERT OR IGNORE INTO AnnouncementTemplates (Name, Description, ArrivalDeparture) VALUES
('IS ARRIVING ON', 'Train is arriving on platform', 'A'),
('HAS ARRIVED ON', 'Train has arrived on platform', 'A'),
('WILL ARRIVE SHORTLY', 'Train will arrive shortly on platform', 'A'),
('RUNNING RIGHT TIME', 'Train is running on time', 'A'),
('RUNNING LATE', 'Train is running late', 'A'),
('INDEFINITE LATE', 'Train is indefinitely late', 'A'),
('CANCELLED', 'Train has been cancelled', 'A'),
('PLATFORM CHANGED', 'Platform has been changed', 'A'),
('TERMINATED', 'Train has been terminated', 'A'),
('DEPARTING', 'Train is departing from platform', 'D'),
('WILL DEPART SHORTLY', 'Train will depart shortly from platform', 'D'),
('HAS DEPARTED', 'Train has departed from platform', 'D'),
('DEPARTURE DELAYED', 'Train departure is delayed', 'D');

-- Insert some default placeholders
-- These will be used as predefined placeholders for dynamic data
INSERT OR IGNORE INTO SequenceItems (SequenceId, OrderIndex, Type, Content, Description) VALUES
(0, 1, 2, 'TRAIN_NUMBER', 'Train number (e.g., 12345)'),
(0, 2, 2, 'TRAIN_NAME', 'Train name (e.g., Rajdhani Express)'),
(0, 3, 2, 'FROM_STATION', 'Source station name'),
(0, 4, 2, 'TO_STATION', 'Destination station name'),
(0, 5, 2, 'VIA_STATION', 'Via station name'),
(0, 6, 2, 'PLATFORM_NUMBER', 'Platform number'),
(0, 7, 2, 'EXPECTED_TIME', 'Expected arrival/departure time'),
(0, 8, 2, 'DELAY_TIME', 'Delay time'),
(0, 9, 2, 'CURRENT_TIME', 'Current time'); 