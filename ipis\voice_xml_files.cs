// Decompiled with JetBrains decompiler
// Type: ipis.voice_xml_files
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using ipis.My;
using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Data;
using System.Data.OleDb;
using System.Diagnostics;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading;
using System.Xml;

namespace ipis
{

public class voice_xml_files
{
  private static int globalVal = 0;
  private static int globalSplMsg = 0;
  private static int NewVal = 1;
  [SpecialName]
  private static int _DOLLAR_STATIC_DOLLAR_train_no_voice_DOLLAR_041E128151128151128151_DOLLAR__i;
  [SpecialName]
  private static StaticLocalInitFlag _DOLLAR_STATIC_DOLLAR_train_no_voice_DOLLAR_041E128151128151128151_DOLLAR__i_DOLLAR_Init = new StaticLocalInitFlag();

  [DebuggerNonUserCode]
  public voice_xml_files()
  {
  }

  public static void train_status_voice(string train_status, XmlNodeList strtrainstatus)
  {
    try
    {
      int num = 0;
      num = strtrainstatus.Count;
      int index = 0;
      while (index < strtrainstatus.Count)
      {
        if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, strtrainstatus.Item(index).FirstChild.InnerText, false) == 0)
        {
          string innerText = strtrainstatus.Item(index).LastChild.InnerText;
          voice_xml_files.PlayVoice(strtrainstatus.Item(index).LastChild.InnerText, AudioPlayMode.WaitToComplete);
          break;
        }
        checked { ++index; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
  }

  public static void late_time_voice(string late, XmlNodeList num_name, XmlNodeList strtime_type)
  {
    string[] strArray1 = new string[2];
    string[] strArray2 = late.Split(':');
    int index1 = 0;
    while (index1 < 2)
    {
      if (strArray2[index1].StartsWith("0"))
        strArray2[index1] = strArray2[index1].Trim('0');
      checked { ++index1; }
    }
    int index2 = 0;
    int num = 0;
    if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(strArray2[0], "", false) != 0)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(strArray2[0], "1", false) == 0)
      {
        voice_xml_files.PlayVoice(strtime_type.Item(4).LastChild.InnerText, AudioPlayMode.WaitToComplete);
      }
      else
      {
        while (index2 < num_name.Count)
        {
          if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(strArray2[0], num_name.Item(index2).FirstChild.InnerText, false) == 0)
          {
            voice_xml_files.PlayVoice(num_name.Item(index2).LastChild.InnerText, AudioPlayMode.WaitToComplete);
            break;
          }
          checked { ++index2; }
        }
        int index3 = 0;
        num = 0;
        while (index3 < strtime_type.Count)
        {
          if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString("HOURS", strtime_type.Item(index3).FirstChild.InnerText, false) == 0)
          {
            voice_xml_files.PlayVoice(strtime_type.Item(index3).LastChild.InnerText, AudioPlayMode.WaitToComplete);
            break;
          }
          checked { ++index3; }
        }
      }
    }
    int index4 = 0;
    if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(strArray2[1], "", false) == 0)
      return;
    while (index4 < num_name.Count)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(strArray2[1], num_name.Item(index4).FirstChild.InnerText, false) == 0)
      {
        voice_xml_files.PlayVoice(num_name.Item(index4).LastChild.InnerText, AudioPlayMode.WaitToComplete);
        break;
      }
      checked { ++index4; }
    }
    int index5 = 0;
    num = 0;
    while (index5 < strtime_type.Count)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString("MINS", strtime_type.Item(index5).FirstChild.InnerText, false) == 0)
      {
        voice_xml_files.PlayVoice(strtime_type.Item(index5).LastChild.InnerText, AudioPlayMode.WaitToComplete);
        break;
      }
      checked { ++index5; }
    }
  }

  public static void train_no_voice(
    string train_no,
    XmlNodeList strstrno,
    XmlNodeList strtrno,
    XmlNodeList alphabet_name)
  {
    int[] numArray = new int[4];
    int num = 3;
    try
    {
      StringBuilder stringBuilder = new StringBuilder(train_no);
      num = 0;
      bool lockTaken = false;
      try
      {
        Monitor.Enter((object) voice_xml_files._DOLLAR_STATIC_DOLLAR_train_no_voice_DOLLAR_041E128151128151128151_DOLLAR__i_DOLLAR_Init, ref lockTaken);
        if (voice_xml_files._DOLLAR_STATIC_DOLLAR_train_no_voice_DOLLAR_041E128151128151128151_DOLLAR__i_DOLLAR_Init.State == (short) 0)
        {
          voice_xml_files._DOLLAR_STATIC_DOLLAR_train_no_voice_DOLLAR_041E128151128151128151_DOLLAR__i_DOLLAR_Init.State = (short) 2;
          voice_xml_files._DOLLAR_STATIC_DOLLAR_train_no_voice_DOLLAR_041E128151128151128151_DOLLAR__i = 0;
        }
        else if (voice_xml_files._DOLLAR_STATIC_DOLLAR_train_no_voice_DOLLAR_041E128151128151128151_DOLLAR__i_DOLLAR_Init.State == (short) 2)
          throw new IncompleteInitialization();
      }
      finally
      {
        voice_xml_files._DOLLAR_STATIC_DOLLAR_train_no_voice_DOLLAR_041E128151128151128151_DOLLAR__i_DOLLAR_Init.State = (short) 1;
        if (lockTaken)
          Monitor.Exit((object) voice_xml_files._DOLLAR_STATIC_DOLLAR_train_no_voice_DOLLAR_041E128151128151128151_DOLLAR__i_DOLLAR_Init);
      }
      int index1 = 0;
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(strstrno.Item(0).FirstChild.InnerText, "trainno", false) == 0)
        voice_xml_files.PlayVoice(strstrno.Item(0).LastChild.InnerText, AudioPlayMode.WaitToComplete);
      while (index1 < train_no.Length)
      {
        int index2 = 0;
        if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Conversions.ToString(stringBuilder[index1]), "0", false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Conversions.ToString(stringBuilder[index1]), "1", false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Conversions.ToString(stringBuilder[index1]), "2", false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Conversions.ToString(stringBuilder[index1]), "3", false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Conversions.ToString(stringBuilder[index1]), "4", false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Conversions.ToString(stringBuilder[index1]), "5", false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Conversions.ToString(stringBuilder[index1]), "6", false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Conversions.ToString(stringBuilder[index1]), "7", false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Conversions.ToString(stringBuilder[index1]), "8", false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Conversions.ToString(stringBuilder[index1]), "9", false) == 0)
        {
          while (index2 <= strtrno.Count)
          {
            if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Conversions.ToString(stringBuilder[index1]), strtrno.Item(index2).FirstChild.InnerText, false) == 0)
            {
              voice_xml_files.PlayVoice(strtrno.Item(index2).LastChild.InnerText, AudioPlayMode.WaitToComplete);
              break;
            }
            checked { ++index2; }
          }
        }
        else
        {
          while (index2 <= alphabet_name.Count)
          {
            if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Conversions.ToString(stringBuilder[index1]), alphabet_name.Item(index2).FirstChild.InnerText, false) == 0)
            {
              voice_xml_files.PlayVoice(alphabet_name.Item(index2).LastChild.InnerText, AudioPlayMode.WaitToComplete);
              break;
            }
            checked { ++index2; }
          }
        }
        checked { ++index1; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
  }

  public static bool train_name_voice_check(string trainno, XmlNodeList strtrname)
  {
    int index = 0;
    int num = 0;
    num = strtrname.Count;
    trainno = Strings.Trim(trainno);
    while (index < strtrname.Count)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(trainno, strtrname.Item(index).FirstChild.InnerText, false) == 0)
        return true;
      checked { ++index; }
    }
    return false;
  }

  public static void train_name_voice(string trainno, XmlNodeList strtrname)
  {
    int index = 0;
    int num = 0;
    num = strtrname.Count;
    trainno = Strings.Trim(trainno);
    while (index < strtrname.Count)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(trainno, strtrname.Item(index).FirstChild.InnerText, false) == 0)
      {
        voice_xml_files.PlayVoice(strtrname.Item(index).LastChild.InnerText, AudioPlayMode.WaitToComplete);
        break;
      }
      checked { ++index; }
    }
  }

  public static void station_name_voice(string station_name, XmlNodeList stationnamenode)
  {
    int index = 0;
    int num = 0;
    num = stationnamenode.Count;
    while (index < stationnamenode.Count)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(station_name, stationnamenode.Item(index).FirstChild.InnerText, false) == 0)
      {
        voice_xml_files.PlayVoice(stationnamenode.Item(index).LastChild.InnerText, AudioPlayMode.WaitToComplete);
        break;
      }
      checked { ++index; }
    }
  }

  public static void arr_dep_time_voice(
    string arr_dep_time,
    XmlNodeList num_name,
    XmlNodeList strtime_type)
  {
    string[] strArray1 = new string[3];
    arr_dep_time = Conversions.ToString(Conversions.ToDate(arr_dep_time));
    string str = arr_dep_time.Split(':')[1];
    string[] strArray2 = new string[2]{ "HOURS", null };
    string[] strArray3 = new string[2];
    string[] strArray4 = arr_dep_time.Split(':');
    int index1 = 0;
    while (index1 < 2)
    {
      int index2 = 0;
      while (index2 < num_name.Count)
      {
        if ((double) Conversions.ToInteger(strArray4[index1]) == Conversions.ToDouble(num_name.Item(index2).FirstChild.InnerText))
          voice_xml_files.PlayVoice(num_name.Item(index2).LastChild.InnerText, AudioPlayMode.WaitToComplete);
        checked { ++index2; }
      }
      int index3 = 0;
      while (index3 < strtime_type.Count)
      {
        if (index1 == 0)
        {
          if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(strArray2[index1], strtime_type.Item(index3).FirstChild.InnerText, false) == 0)
          {
            string innerText = strtime_type.Item(index3).FirstChild.InnerText;
            voice_xml_files.PlayVoice(strtime_type.Item(index3).LastChild.InnerText, AudioPlayMode.WaitToComplete);
            break;
          }
        }
        else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString("MINS", strtime_type.Item(index3).FirstChild.InnerText, false) == 0)
        {
          voice_xml_files.PlayVoice(strtime_type.Item(index3).LastChild.InnerText, AudioPlayMode.WaitToComplete);
          break;
        }
        checked { ++index3; }
      }
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(strArray4[1], "", false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(strArray4[1], "00", false) == 0)
        break;
      checked { ++index1; }
    }
  }

  public static void reg_arr_dep_time_voice(
    string arr_dep_time,
    XmlNodeList num_name,
    XmlNodeList strtime_type,
    XmlNodeList reg_num_name)
  {
    string[] strArray1 = new string[3];
    arr_dep_time = Conversions.ToString(Conversions.ToDate(arr_dep_time));
    string[] strArray2 = new string[3];
    strArray2 = arr_dep_time.Split(':');
    strArray1[0] = "HOURS";
    strArray1[1] = "MINS";
    string[] strArray3 = new string[2];
    string[] strArray4 = arr_dep_time.Split(':');
    int index1 = 0;
    while (index1 < 3)
    {
      if (strArray4[index1].StartsWith("0"))
        strArray4[index1] = strArray4[index1].Remove(0, 1);
      checked { ++index1; }
    }
    int index2 = 0;
    while (index2 < num_name.Count)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(strArray4[0], num_name.Item(index2).FirstChild.InnerText, false) == 0)
      {
        voice_xml_files.PlayVoice(num_name.Item(index2).LastChild.InnerText, AudioPlayMode.WaitToComplete);
        break;
      }
      checked { ++index2; }
    }
    if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(strArray4[1], "00", false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(strArray4[1], "0", false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(strArray4[1], "", false) == 0)
      return;
    int index3 = 0;
    while (index3 < reg_num_name.Count)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(strArray4[1], reg_num_name.Item(index3).FirstChild.InnerText, false) == 0)
      {
        voice_xml_files.PlayVoice(reg_num_name.Item(index3).LastChild.InnerText, AudioPlayMode.WaitToComplete);
        break;
      }
      checked { ++index3; }
    }
    int index4 = 0;
    while (index4 < strtime_type.Count)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString("MINS", strtime_type.Item(index4).FirstChild.InnerText, false) == 0)
      {
        voice_xml_files.PlayVoice(strtime_type.Item(index4).LastChild.InnerText, AudioPlayMode.WaitToComplete);
        break;
      }
      checked { ++index4; }
    }
  }

  public static void hindi_arr_dep_time_voice(
    string arr_dep_time,
    XmlNodeList num_name,
    XmlNodeList strtime_type,
    string minute_type)
  {
    string[] strArray1 = new string[3];
    arr_dep_time = Conversions.ToString(Conversions.ToDate(arr_dep_time));
    string[] strArray2 = new string[3];
    strArray2 = arr_dep_time.Split(':');
    strArray1[0] = "BAJE";
    strArray1[1] = "MINS";
    string[] strArray3 = new string[2];
    string[] strArray4 = arr_dep_time.Split(':');
    int index1 = 0;
    while (index1 < 3)
    {
      if (strArray4[index1].StartsWith("0"))
        strArray4[index1] = strArray4[index1].Remove(0, 1);
      checked { ++index1; }
    }
    int index2 = 0;
    while (index2 < num_name.Count)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(strArray4[0], num_name.Item(index2).FirstChild.InnerText, false) == 0)
      {
        voice_xml_files.PlayVoice(num_name.Item(index2).LastChild.InnerText, AudioPlayMode.WaitToComplete);
        break;
      }
      checked { ++index2; }
    }
    int index3 = 0;
    while (index3 < strtime_type.Count)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(strArray1[0], strtime_type.Item(index3).FirstChild.InnerText, false) == 0)
      {
        voice_xml_files.PlayVoice(strtime_type.Item(index3).LastChild.InnerText, AudioPlayMode.WaitToComplete);
        break;
      }
      checked { ++index3; }
    }
    int index4 = 0;
    while (index4 < num_name.Count)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(strArray4[1], num_name.Item(index4).FirstChild.InnerText, false) == 0)
      {
        voice_xml_files.PlayVoice(num_name.Item(index4).LastChild.InnerText, AudioPlayMode.WaitToComplete);
        break;
      }
      checked { ++index4; }
    }
    int index5 = 0;
    while (index5 < strtime_type.Count)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString("MINUTE", strtime_type.Item(index5).FirstChild.InnerText, false) == 0)
      {
        voice_xml_files.PlayVoice(strtime_type.Item(index5).LastChild.InnerText, AudioPlayMode.WaitToComplete);
        break;
      }
      checked { ++index5; }
    }
  }

  public static void marathi_arr_dep_time_voice(
    string arr_dep_time,
    XmlNodeList num_name,
    XmlNodeList strtime_type,
    string minute_type)
  {
    string[] strArray1 = new string[3];
    arr_dep_time = Conversions.ToString(Conversions.ToDate(arr_dep_time));
    string[] strArray2 = new string[3];
    strArray2 = arr_dep_time.Split(':');
    strArray1[0] = "BAJE";
    strArray1[1] = "MINS";
    string[] strArray3 = new string[2];
    string[] strArray4 = arr_dep_time.Split(':');
    int index1 = 0;
    while (index1 < 3)
    {
      if (strArray4[index1].StartsWith("0"))
        strArray4[index1] = strArray4[index1].Remove(0, 1);
      checked { ++index1; }
    }
    int index2 = 0;
    while (index2 < num_name.Count)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(strArray4[0], num_name.Item(index2).FirstChild.InnerText, false) == 0)
      {
        voice_xml_files.PlayVoice(num_name.Item(index2).LastChild.InnerText, AudioPlayMode.WaitToComplete);
        break;
      }
      checked { ++index2; }
    }
    int index3 = 0;
    while (index3 < strtime_type.Count)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(strArray1[0], strtime_type.Item(index3).FirstChild.InnerText, false) == 0)
      {
        voice_xml_files.PlayVoice(strtime_type.Item(index3).LastChild.InnerText, AudioPlayMode.WaitToComplete);
        break;
      }
      checked { ++index3; }
    }
    int index4 = 0;
    while (index4 < num_name.Count)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(strArray4[1], num_name.Item(index4).FirstChild.InnerText, false) == 0)
      {
        voice_xml_files.PlayVoice(num_name.Item(index4).LastChild.InnerText, AudioPlayMode.WaitToComplete);
        break;
      }
      checked { ++index4; }
    }
    int index5 = 0;
    while (index5 < strtime_type.Count)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString("MIN", strtime_type.Item(index5).FirstChild.InnerText, false) == 0)
      {
        voice_xml_files.PlayVoice(strtime_type.Item(index5).LastChild.InnerText, AudioPlayMode.WaitToComplete);
        break;
      }
      checked { ++index5; }
    }
  }

  private static void platform_no_voice(
    string pfno,
    XmlNodeList num_name,
    XmlNodeList alphabet_name)
  {
    StringBuilder stringBuilder = new StringBuilder(pfno);
    try
    {
      int index1 = 0;
      while (index1 < stringBuilder.Length)
      {
        int index2 = 0;
        if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Conversions.ToString(stringBuilder[index1]), "0", false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Conversions.ToString(stringBuilder[index1]), "1", false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Conversions.ToString(stringBuilder[index1]), "2", false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Conversions.ToString(stringBuilder[index1]), "3", false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Conversions.ToString(stringBuilder[index1]), "4", false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Conversions.ToString(stringBuilder[index1]), "5", false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Conversions.ToString(stringBuilder[index1]), "6", false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Conversions.ToString(stringBuilder[index1]), "7", false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Conversions.ToString(stringBuilder[index1]), "8", false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Conversions.ToString(stringBuilder[index1]), "9", false) == 0)
        {
          if (checked (index1 + 1) < stringBuilder.Length)
          {
            if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Conversions.ToString(stringBuilder[checked (index1 + 1)]), "0", false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Conversions.ToString(stringBuilder[checked (index1 + 1)]), "1", false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Conversions.ToString(stringBuilder[checked (index1 + 1)]), "2", false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Conversions.ToString(stringBuilder[checked (index1 + 1)]), "3", false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Conversions.ToString(stringBuilder[checked (index1 + 1)]), "4", false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Conversions.ToString(stringBuilder[checked (index1 + 1)]), "5", false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Conversions.ToString(stringBuilder[checked (index1 + 1)]), "6", false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Conversions.ToString(stringBuilder[checked (index1 + 1)]), "7", false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Conversions.ToString(stringBuilder[checked (index1 + 1)]), "8", false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Conversions.ToString(stringBuilder[checked (index1 + 1)]), "9", false) == 0)
            {
              string Left = Conversions.ToString(stringBuilder[0]) + Conversions.ToString(stringBuilder[1]);
              while (index2 < num_name.Count)
              {
                if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Left, num_name.Item(index2).FirstChild.InnerText, false) == 0)
                {
                  voice_xml_files.PlayVoice(num_name.Item(index2).LastChild.InnerText, AudioPlayMode.WaitToComplete);
                  break;
                }
                checked { ++index2; }
              }
              checked { ++index1; }
            }
            else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Conversions.ToString(stringBuilder[checked (index1 + 1)]), "A", false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Conversions.ToString(stringBuilder[checked (index1 + 1)]), "B", false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Conversions.ToString(stringBuilder[checked (index1 + 1)]), "C", false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Conversions.ToString(stringBuilder[checked (index1 + 1)]), "D", false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Conversions.ToString(stringBuilder[checked (index1 + 1)]), "E", false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Conversions.ToString(stringBuilder[checked (index1 + 1)]), "a", false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Conversions.ToString(stringBuilder[checked (index1 + 1)]), "b", false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Conversions.ToString(stringBuilder[checked (index1 + 1)]), "c", false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Conversions.ToString(stringBuilder[checked (index1 + 1)]), "d", false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Conversions.ToString(stringBuilder[checked (index1 + 1)]), "e", false) == 0)
            {
              while (index2 < num_name.Count)
              {
                if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Conversions.ToString(stringBuilder[index1]), num_name.Item(index2).FirstChild.InnerText, false) == 0)
                {
                  voice_xml_files.PlayVoice(num_name.Item(index2).LastChild.InnerText, AudioPlayMode.WaitToComplete);
                  break;
                }
                checked { ++index2; }
              }
            }
          }
          else
          {
            while (index2 < num_name.Count)
            {
              if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Conversions.ToString(stringBuilder[index1]), num_name.Item(index2).FirstChild.InnerText, false) == 0)
              {
                voice_xml_files.PlayVoice(num_name.Item(index2).LastChild.InnerText, AudioPlayMode.WaitToComplete);
                break;
              }
              checked { ++index2; }
            }
          }
        }
        else
        {
          while (index2 <= alphabet_name.Count)
          {
            if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(Conversions.ToString(stringBuilder[index1]), alphabet_name.Item(index2).FirstChild.InnerText, false) == 0)
            {
              voice_xml_files.PlayVoice(alphabet_name.Item(index2).LastChild.InnerText, AudioPlayMode.WaitToComplete);
              break;
            }
            checked { ++index2; }
          }
        }
        checked { ++index1; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
  }

  public static void reg_time_hour_voice(
    string late,
    XmlNodeList strtime_hour,
    XmlNodeList strtime_type,
    XmlNodeList str_no)
  {
    string[] strArray1 = new string[2];
    string[] strArray2 = late.Split(':');
    int index1 = 0;
    while (index1 < 2)
    {
      if (strArray2[index1].StartsWith("0"))
        strArray2[index1] = strArray2[index1].Trim('0');
      checked { ++index1; }
    }
    if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(strArray2[0], "00", false) != 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(strArray2[0], "", false) == 0)
    {
      int index2 = 0;
      while (index2 < strtime_hour.Count)
      {
        if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(strArray2[0], strtime_hour.Item(index2).FirstChild.InnerText, false) == 0)
        {
          voice_xml_files.PlayVoice(strtime_hour.Item(index2).LastChild.InnerText, AudioPlayMode.WaitToComplete);
          break;
        }
        checked { ++index2; }
      }
    }
    if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(strArray2[1], "00", false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(strArray2[1], "", false) == 0)
      return;
    int index3 = 0;
    while (index3 < str_no.Count)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(strArray2[1], str_no.Item(index3).FirstChild.InnerText, false) == 0)
      {
        voice_xml_files.PlayVoice(str_no.Item(index3).LastChild.InnerText, AudioPlayMode.WaitToComplete);
        break;
      }
      checked { ++index3; }
    }
    int index4 = 0;
    while (index4 < strtime_type.Count)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString("MINS", strtime_type.Item(index4).FirstChild.InnerText, false) == 0)
      {
        voice_xml_files.PlayVoice(strtime_type.Item(index4).LastChild.InnerText, AudioPlayMode.WaitToComplete);
        break;
      }
      checked { ++index4; }
    }
  }

  public static void hindi_time_hour_voice(string late, XmlNodeList strtime_hour)
  {
    string[] strArray1 = new string[2];
    string[] strArray2 = late.Split(':');
    int index1 = 0;
    int index2 = 0;
    while (index1 < strtime_hour.Count)
    {
      while (index1 < 25)
      {
        if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(strArray2[0], strtime_hour.Item(index1).FirstChild.InnerText, false) == 0)
        {
          voice_xml_files.PlayVoice(strtime_hour.Item(index1).LastChild.InnerText, AudioPlayMode.WaitToComplete);
          break;
        }
        checked { ++index1; }
      }
      index1 = 0;
      if (Conversions.ToDouble(strArray2[1]) > 0.0)
      {
        while (index1 < strtime_hour.Count)
        {
          if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(strArray2[1], strtime_hour.Item(index2).FirstChild.InnerText, false) == 0)
          {
            voice_xml_files.PlayVoice(strtime_hour.Item(index2).LastChild.InnerText, AudioPlayMode.WaitToComplete);
            break;
          }
          checked { ++index1; }
        }
        index2 = 0;
        index1 = 0;
        while (index2 < strtime_hour.Count)
        {
          if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString("MINUTE", strtime_hour.Item(index2).FirstChild.InnerText, false) == 0)
          {
            voice_xml_files.PlayVoice(strtime_hour.Item(index2).LastChild.InnerText, AudioPlayMode.WaitToComplete);
            break;
          }
          checked { ++index2; }
        }
      }
    }
  }

  public static void hindi_late_time_voice(
    string late,
    XmlNodeList num_name,
    XmlNodeList strtime_type)
  {
    string[] strArray1 = new string[2];
    string[] strArray2 = late.Split(':');
    int index1 = 0;
    while (index1 < 2)
    {
      if (strArray2[index1].StartsWith("0"))
        strArray2[index1] = strArray2[index1].Trim('0');
      checked { ++index1; }
    }
    int index2 = 0;
    int num = 0;
    if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(strArray2[0], "", false) != 0 & Microsoft.VisualBasic.CompilerServices.Operators.CompareString(strArray2[0], "00", false) != 0)
    {
      while (index2 < num_name.Count)
      {
        if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(strArray2[0], num_name.Item(index2).FirstChild.InnerText, false) == 0)
        {
          voice_xml_files.PlayVoice(num_name.Item(index2).LastChild.InnerText, AudioPlayMode.WaitToComplete);
          break;
        }
        checked { ++index2; }
      }
      int index3 = 0;
      num = 0;
      while (index3 < strtime_type.Count)
      {
        if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString("HOURS", strtime_type.Item(index3).FirstChild.InnerText, false) == 0)
        {
          voice_xml_files.PlayVoice(strtime_type.Item(index3).LastChild.InnerText, AudioPlayMode.WaitToComplete);
          break;
        }
        checked { ++index3; }
      }
    }
    int index4 = 0;
    if (!(Microsoft.VisualBasic.CompilerServices.Operators.CompareString(strArray2[1], "", false) != 0 & Microsoft.VisualBasic.CompilerServices.Operators.CompareString(strArray2[0], "00", false) != 0))
      return;
    while (index4 < num_name.Count)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(strArray2[1], num_name.Item(index4).FirstChild.InnerText, false) == 0)
      {
        voice_xml_files.PlayVoice(num_name.Item(index4).LastChild.InnerText, AudioPlayMode.WaitToComplete);
        break;
      }
      checked { ++index4; }
    }
    int index5 = 0;
    num = 0;
    while (index5 < strtime_type.Count)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString("MINUTE", strtime_type.Item(index5).FirstChild.InnerText, false) == 0)
      {
        voice_xml_files.PlayVoice(strtime_type.Item(index5).LastChild.InnerText, AudioPlayMode.WaitToComplete);
        break;
      }
      checked { ++index5; }
    }
  }

  public static void oriya_late_time_voice(
    string late,
    XmlNodeList num_name,
    XmlNodeList strtime_type)
  {
    string[] strArray1 = new string[2];
    string[] strArray2 = late.Split(':');
    int index1 = 0;
    while (index1 < 2)
    {
      if (strArray2[index1].StartsWith("0"))
        strArray2[index1] = strArray2[index1].Trim('0');
      checked { ++index1; }
    }
    int index2 = 0;
    int num = 0;
    if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(strArray2[0], "", false) != 0 & Microsoft.VisualBasic.CompilerServices.Operators.CompareString(strArray2[0], "00", false) != 0)
    {
      while (index2 < num_name.Count)
      {
        if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(strArray2[0], num_name.Item(index2).FirstChild.InnerText, false) == 0)
        {
          voice_xml_files.PlayVoice(num_name.Item(index2).LastChild.InnerText, AudioPlayMode.WaitToComplete);
          break;
        }
        checked { ++index2; }
      }
      int index3 = 0;
      num = 0;
      while (index3 < strtime_type.Count)
      {
        if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString("HOUR", strtime_type.Item(index3).FirstChild.InnerText, false) == 0)
        {
          voice_xml_files.PlayVoice(strtime_type.Item(index3).LastChild.InnerText, AudioPlayMode.WaitToComplete);
          break;
        }
        checked { ++index3; }
      }
    }
    int index4 = 0;
    if (!(Microsoft.VisualBasic.CompilerServices.Operators.CompareString(strArray2[1], "", false) != 0 & Microsoft.VisualBasic.CompilerServices.Operators.CompareString(strArray2[0], "00", false) != 0))
      return;
    while (index4 < num_name.Count)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(strArray2[1], num_name.Item(index4).FirstChild.InnerText, false) == 0)
      {
        voice_xml_files.PlayVoice(num_name.Item(index4).LastChild.InnerText, AudioPlayMode.WaitToComplete);
        break;
      }
      checked { ++index4; }
    }
    int index5 = 0;
    num = 0;
    while (index5 < strtime_type.Count)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString("MIN", strtime_type.Item(index5).FirstChild.InnerText, false) == 0)
      {
        voice_xml_files.PlayVoice(strtime_type.Item(index5).LastChild.InnerText, AudioPlayMode.WaitToComplete);
        break;
      }
      checked { ++index5; }
    }
  }

  public static void marathi_late_time_voice(
    string late,
    XmlNodeList num_name,
    XmlNodeList strtime_type)
  {
    string[] strArray1 = new string[2];
    string[] strArray2 = late.Split(':');
    int index1 = 0;
    while (index1 < 2)
    {
      if (strArray2[index1].StartsWith("0"))
        strArray2[index1] = strArray2[index1].Trim('0');
      checked { ++index1; }
    }
    int index2 = 0;
    int num = 0;
    if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(strArray2[0], "", false) != 0 & Microsoft.VisualBasic.CompilerServices.Operators.CompareString(strArray2[0], "00", false) != 0)
    {
      while (index2 < num_name.Count)
      {
        if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(strArray2[0], num_name.Item(index2).FirstChild.InnerText, false) == 0)
        {
          voice_xml_files.PlayVoice(num_name.Item(index2).LastChild.InnerText, AudioPlayMode.WaitToComplete);
          break;
        }
        checked { ++index2; }
      }
      int index3 = 0;
      num = 0;
      while (index3 < strtime_type.Count)
      {
        if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString("HOUR", strtime_type.Item(index3).FirstChild.InnerText, false) == 0)
        {
          voice_xml_files.PlayVoice(strtime_type.Item(index3).LastChild.InnerText, AudioPlayMode.WaitToComplete);
          break;
        }
        checked { ++index3; }
      }
    }
    int index4 = 0;
    if (!(Microsoft.VisualBasic.CompilerServices.Operators.CompareString(strArray2[1], "", false) != 0 & Microsoft.VisualBasic.CompilerServices.Operators.CompareString(strArray2[0], "00", false) != 0))
      return;
    while (index4 < num_name.Count)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(strArray2[1], num_name.Item(index4).FirstChild.InnerText, false) == 0)
      {
        voice_xml_files.PlayVoice(num_name.Item(index4).LastChild.InnerText, AudioPlayMode.WaitToComplete);
        break;
      }
      checked { ++index4; }
    }
    int index5 = 0;
    num = 0;
    while (index5 < strtime_type.Count)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString("MIN", strtime_type.Item(index5).FirstChild.InnerText, false) == 0)
      {
        voice_xml_files.PlayVoice(strtime_type.Item(index5).LastChild.InnerText, AudioPlayMode.WaitToComplete);
        break;
      }
      checked { ++index5; }
    }
  }

  public static bool CheckVoiceTrainNames(string train_name_xmlText, string train_no)
  {
    XmlDocument xmlDocument = new XmlDocument();
    xmlDocument.LoadXml(train_name_xmlText);
    XmlNodeList elementsByTagName = xmlDocument.GetElementsByTagName("Trainname");
    return voice_xml_files.train_name_voice_check(train_no, elementsByTagName);
  }

  public static void ProcessAds()
  {
    DataSet dataSet = new DataSet();
    int num = (int) dataSet.ReadXml("C:\\IPIS\\voice\\Ads\\voicexml.xml");
    string xml = dataSet.GetXml();
    XmlDocument xmlDocument = new XmlDocument();
    xmlDocument.LoadXml(xml);
    XmlNodeList elementsByTagName = xmlDocument.GetElementsByTagName("Ads");
    if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(elementsByTagName.Item(voice_xml_files.globalVal).FirstChild.InnerText, "Ads", false) == 0)
    {
      do
        ;
      while (frmMainFormIPIS.ann_pause);
      voice_xml_files.PlayVoice(elementsByTagName.Item(voice_xml_files.globalVal).LastChild.InnerText, AudioPlayMode.WaitToComplete);
      voice_xml_files.adregister(elementsByTagName.Item(voice_xml_files.globalVal).LastChild.InnerText);
    }
    if (voice_xml_files.globalVal < checked (elementsByTagName.Count - 1))
      checked { ++voice_xml_files.globalVal; }
    else
      voice_xml_files.globalVal = 0;
  }

  public static void ProcessSplMsg()
  {
    DataSet dataSet = new DataSet();
    int num = (int) dataSet.ReadXml("C:\\IPIS\\voice\\special_messages\\English\\voicexml.xml");
    string xml = dataSet.GetXml();
    XmlDocument xmlDocument = new XmlDocument();
    xmlDocument.LoadXml(xml);
    XmlNodeList elementsByTagName = xmlDocument.GetElementsByTagName("Spl");
    if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(elementsByTagName.Item(voice_xml_files.globalVal).FirstChild.InnerText, "Spl", false) == 0)
    {
      do
        ;
      while (frmMainFormIPIS.ann_pause);
      voice_xml_files.PlayVoice(elementsByTagName.Item(voice_xml_files.globalSplMsg).LastChild.InnerText, AudioPlayMode.WaitToComplete);
    }
    if (voice_xml_files.globalSplMsg < checked (elementsByTagName.Count - 1))
      checked { ++voice_xml_files.globalSplMsg; }
    else
      voice_xml_files.globalSplMsg = 0;
  }

  public static void ProcessAllAds()
  {
    DataSet dataSet = new DataSet();
    int num1 = (int) dataSet.ReadXml("C:\\IPIS\\voice\\Ads\\voicexml.xml");
    string xml = dataSet.GetXml();
    XmlDocument xmlDocument = new XmlDocument();
    xmlDocument.LoadXml(xml);
    DataSet ds = new DataSet();
    network_db_read.get_config(ref ds);
    int int16_1 = 0;
    int int16_2 = 0;
    int int16_3 = 0;
    if (ds.Tables[0].Rows.Count > 0)
    {
      int16_1 = (int) Convert.ToInt16(ds.Tables[0].Rows[0]["SplMSgset"].ToString());
      int16_2 = (int) Convert.ToInt16(ds.Tables[0].Rows[0]["adLoop"].ToString());
      int16_3 = (int) Convert.ToInt16(ds.Tables[0].Rows[0]["setInterval"].ToString());
    }
    xmlDocument.GetElementsByTagName("Ads");
    frmMainFormIPIS.ann_stop = false;
    int num2 = 0;
    while (num2 < int16_1)
    {
      Thread.Sleep(checked (int16_3 * 1000 * 60));
      int num3 = 0;
      while (num3 < int16_2)
      {
        voice_xml_files.ProcessAds();
        voice_xml_files.ProcessSplMsg();
        voice_xml_files.ProcessAds();
        checked { ++num3; }
      }
      checked { ++num2; }
    }
  }

  public static void adregister(string strpath)
  {
    try
    {
      OleDbCommand oleDbCommand = new OleDbCommand();
      OleDbConnection oleDbConnection = new OleDbConnection("provider=Microsoft.Jet.OLEDB.4.0; Data Source='d:\\New.xls';Extended Properties=Excel 8.0;");
      oleDbConnection.Open();
      oleDbCommand.Connection = oleDbConnection;
      string str = "Insert into [Sheet1$] values('" + Conversions.ToString(voice_xml_files.NewVal) + "','" + strpath.Replace("C:\\IPIS\\voice\\Ads\\", "") + "','" + DateAndTime.Now.ToString("dd/MM/yyyy") + "','" + DateAndTime.Now.ToString("HH:mm:ss") + "','" + DateAndTime.Now.DayOfWeek.ToString() + "')";
      oleDbCommand.CommandText = str;
      oleDbCommand.ExecuteNonQuery();
      oleDbConnection.Close();
      checked { ++voice_xml_files.NewVal; }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      int num = (int) Interaction.MsgBox((object) ex.ToString());
      ProjectData.ClearProjectError();
    }
  }

  public static void ProcessXML(
    string xmlText,
    string train_name_xmlText,
    string station_name_xmlText,
    string train_no,
    string train_name,
    string ad,
    string PFno,
    string train_status,
    string arrival_time,
    string departure_time,
    string late,
    string sch_arr,
    string sch_dep,
    string english_station_name)
  {
    XmlDocument xmlDocument1 = new XmlDocument();
    xmlDocument1.LoadXml(xmlText);
    XmlDocument xmlDocument2 = new XmlDocument();
    xmlDocument2.LoadXml(train_name_xmlText);
    XmlDocument xmlDocument3 = new XmlDocument();
    xmlDocument3.LoadXml(station_name_xmlText);
    XmlNodeList elementsByTagName1 = xmlDocument1.GetElementsByTagName("Kind_attn");
    XmlNodeList elementsByTagName2 = xmlDocument1.GetElementsByTagName("Trainnovoice");
    XmlNodeList elementsByTagName3 = xmlDocument1.GetElementsByTagName("Trainno");
    XmlNodeList elementsByTagName4 = xmlDocument1.GetElementsByTagName("Trainstatus");
    xmlDocument1.GetElementsByTagName("Pfno");
    XmlNodeList elementsByTagName5 = xmlDocument1.GetElementsByTagName("Time_name");
    XmlNodeList elementsByTagName6 = xmlDocument1.GetElementsByTagName("Number_name");
    XmlNodeList elementsByTagName7 = xmlDocument1.GetElementsByTagName("alpha_name");
    XmlNodeList elementsByTagName8 = xmlDocument2.GetElementsByTagName("Trainname");
    XmlNodeList elementsByTagName9 = xmlDocument3.GetElementsByTagName("Stationname");
    string[] strArray = new string[2];
    if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(PFno, (string) null, false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(PFno, "", false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(PFno, "--", false) == 0)
      PFno = "0";
    string str = Microsoft.VisualBasic.CompilerServices.Operators.CompareString(ad, "A", false) != 0 ? departure_time : arrival_time;
    strArray = late.Split(':');
    string arr_dep_time = Microsoft.VisualBasic.CompilerServices.Operators.CompareString(ad, "A", false) != 0 ? departure_time : arrival_time;
    string station_name = string.Empty;
    int index = 0;
    while (index < frmMainFormIPIS.train_cnt)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.train_details[index].train_no, train_no, false) == 0)
      {
        station_name = frmMainFormIPIS.train_details[index].end_Station;
        break;
      }
      checked { ++index; }
    }
    if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(elementsByTagName1.Item(0).FirstChild.InnerText, "Kind_attn", false) == 0)
      voice_xml_files.PlayVoice(elementsByTagName1.Item(0).LastChild.InnerText, AudioPlayMode.WaitToComplete);
    voice_xml_files.train_no_voice(train_no, elementsByTagName2, elementsByTagName3, elementsByTagName7);
    voice_xml_files.train_name_voice(train_no, elementsByTagName8);
    if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "ARRIVING ON", false) == 0)
    {
      voice_xml_files.train_status_voice(train_status, elementsByTagName4);
      voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "ARRIVED ON", false) == 0)
    {
      voice_xml_files.train_status_voice(train_status, elementsByTagName4);
      voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "RUNNING ON TIME", false) == 0)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(PFno, "0", false) == 0)
      {
        voice_xml_files.train_status_voice("SCHEDULED TO ARRIVE", elementsByTagName4);
        voice_xml_files.arr_dep_time_voice(sch_arr, elementsByTagName6, elementsByTagName5);
        voice_xml_files.train_status_voice("RUNNING ON TIME", elementsByTagName4);
      }
      else
      {
        voice_xml_files.train_status_voice("SCHEDULED TO ARRIVE", elementsByTagName4);
        voice_xml_files.arr_dep_time_voice(sch_arr, elementsByTagName6, elementsByTagName5);
        voice_xml_files.train_status_voice("ON PFNO", elementsByTagName4);
        voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
        voice_xml_files.train_status_voice("RUNNING ON TIME", elementsByTagName4);
      }
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "EXPECTED SHORTLY", false) == 0)
    {
      voice_xml_files.train_status_voice(train_status, elementsByTagName4);
      voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "LEAVE SHORTLY", false) == 0)
    {
      voice_xml_files.train_status_voice("SHORTLY", elementsByTagName4);
      voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "RUNNING LATE", false) == 0)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(PFno, "0", false) == 0)
      {
        voice_xml_files.train_status_voice("SCHEDULED TO ARRIVE", elementsByTagName4);
        voice_xml_files.arr_dep_time_voice(sch_arr, elementsByTagName6, elementsByTagName5);
        voice_xml_files.train_status_voice(train_status, elementsByTagName4);
        voice_xml_files.late_time_voice(late, elementsByTagName6, elementsByTagName5);
        voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
      }
      else
      {
        voice_xml_files.train_status_voice("SCHEDULED TO ARRIVE", elementsByTagName4);
        voice_xml_files.arr_dep_time_voice(sch_arr, elementsByTagName6, elementsByTagName5);
        voice_xml_files.train_status_voice("RUNNING LATE", elementsByTagName4);
        voice_xml_files.late_time_voice(late, elementsByTagName6, elementsByTagName5);
        voice_xml_files.train_status_voice("EXPECTED TO ARRIVE AT", elementsByTagName4);
        voice_xml_files.arr_dep_time_voice(arr_dep_time, elementsByTagName6, elementsByTagName5);
        voice_xml_files.train_status_voice("ON PFNO", elementsByTagName4);
        voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
        voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
      }
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "INDEFINITE LATE", false) == 0)
    {
      voice_xml_files.train_status_voice(train_status, elementsByTagName4);
      voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "READY TO LEAVE", false) == 0)
    {
      voice_xml_files.train_status_voice(train_status, elementsByTagName4);
      voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "ON PLATFORM", false) == 0)
    {
      voice_xml_files.train_status_voice("IS", elementsByTagName4);
      voice_xml_files.train_status_voice("ON PFNO", elementsByTagName4);
      voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "SCHEDULED DEPARTURE", false) == 0)
    {
      voice_xml_files.train_status_voice("WILL LEAVE FROM", elementsByTagName4);
      voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
      voice_xml_files.train_status_voice(train_status, elementsByTagName4);
      voice_xml_files.arr_dep_time_voice(sch_dep, elementsByTagName6, elementsByTagName5);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "RESCHEDULED", false) == 0)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(PFno, "0", false) == 0)
      {
        voice_xml_files.train_status_voice(train_status, elementsByTagName4);
        voice_xml_files.arr_dep_time_voice(arr_dep_time, elementsByTagName6, elementsByTagName5);
        voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
      }
      else
      {
        voice_xml_files.train_status_voice(train_status, elementsByTagName4);
        voice_xml_files.arr_dep_time_voice(arr_dep_time, elementsByTagName6, elementsByTagName5);
        voice_xml_files.train_status_voice("FROM", elementsByTagName4);
        voice_xml_files.train_status_voice("PLATFORM_NO", elementsByTagName4);
        voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
        voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
      }
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "REGULATED", false) == 0)
    {
      if (Conversions.ToDouble(PFno) == 0.0)
      {
        voice_xml_files.train_status_voice("IS SCHEDULED LEAVE", elementsByTagName4);
        voice_xml_files.arr_dep_time_voice(sch_dep, elementsByTagName6, elementsByTagName5);
        voice_xml_files.train_status_voice("TODAY LEAVE", elementsByTagName4);
        voice_xml_files.arr_dep_time_voice(arr_dep_time, elementsByTagName6, elementsByTagName5);
        voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
      }
      else
      {
        voice_xml_files.train_status_voice("IS SCHEDULED LEAVE", elementsByTagName4);
        voice_xml_files.arr_dep_time_voice(sch_dep, elementsByTagName6, elementsByTagName5);
        voice_xml_files.train_status_voice("TODAY LEAVE", elementsByTagName4);
        voice_xml_files.arr_dep_time_voice(arr_dep_time, elementsByTagName6, elementsByTagName5);
        voice_xml_files.train_status_voice("FROM PFNO", elementsByTagName4);
        voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
        voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
      }
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "DIVERTED ROUTE", false) == 0)
    {
      voice_xml_files.train_status_voice(train_status, elementsByTagName4);
      voice_xml_files.train_status_voice("TODAY", elementsByTagName4);
      voice_xml_files.train_status_voice("THIS_TRAIN_WILL", elementsByTagName4);
      voice_xml_files.train_status_voice("TRAVEL_VIA", elementsByTagName4);
      voice_xml_files.station_name_voice(english_station_name, elementsByTagName9);
      voice_xml_files.train_status_voice("AND_REACH", elementsByTagName4);
      voice_xml_files.station_name_voice(station_name, elementsByTagName9);
      voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "PLATFORM CHANGE", false) == 0)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(ad, "A", false) == 0)
      {
        voice_xml_files.train_status_voice(train_status, elementsByTagName4);
        voice_xml_files.train_status_voice("NOW_THIS_TRAIN_WILL_ARRIVE_ON", elementsByTagName4);
        voice_xml_files.train_status_voice("PLATFORM_NO", elementsByTagName4);
        voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
        voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
      }
      else
      {
        voice_xml_files.train_status_voice(train_status, elementsByTagName4);
        voice_xml_files.train_status_voice("THIS_TRAIN", elementsByTagName4);
        voice_xml_files.train_status_voice("WILL LEAVE FROM", elementsByTagName4);
        voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
        voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
      }
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "CANCELLED", false) == 0)
    {
      voice_xml_files.train_status_voice(train_status, elementsByTagName4);
      voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "HAS LEFT", false) == 0)
    {
      voice_xml_files.train_status_voice(train_status, elementsByTagName4);
    }
    else
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "TERMINATED", false) != 0)
        return;
      voice_xml_files.train_status_voice("WILL_BE_TERMINATED_AT", elementsByTagName4);
      voice_xml_files.station_name_voice(english_station_name, elementsByTagName9);
      voice_xml_files.train_status_voice("INSTEAD_OF", elementsByTagName4);
      voice_xml_files.station_name_voice(station_name, elementsByTagName9);
      voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
    }
  }

  public static void telugu_ProcessXML(
    string xmlText,
    string train_name_xmlText,
    string station_name_xmlText,
    string train_no,
    string train_name,
    string ad,
    string PFno,
    string train_status,
    string arrival_time,
    string departure_time,
    string late,
    string sch_arr,
    string sch_dep,
    string reg_station_name)
  {
    XmlDocument xmlDocument1 = new XmlDocument();
    xmlDocument1.LoadXml(xmlText);
    XmlDocument xmlDocument2 = new XmlDocument();
    xmlDocument2.LoadXml(train_name_xmlText);
    XmlDocument xmlDocument3 = new XmlDocument();
    xmlDocument3.LoadXml(station_name_xmlText);
    XmlNodeList elementsByTagName1 = xmlDocument1.GetElementsByTagName("Kind_attn");
    XmlNodeList elementsByTagName2 = xmlDocument1.GetElementsByTagName("Trainnovoice");
    XmlNodeList elementsByTagName3 = xmlDocument1.GetElementsByTagName("Trainno");
    XmlNodeList elementsByTagName4 = xmlDocument1.GetElementsByTagName("Trainstatus");
    XmlNodeList elementsByTagName5 = xmlDocument1.GetElementsByTagName("Time_name");
    XmlNodeList elementsByTagName6 = xmlDocument1.GetElementsByTagName("TrainHour");
    XmlNodeList elementsByTagName7 = xmlDocument1.GetElementsByTagName("Number_name");
    XmlNodeList elementsByTagName8 = xmlDocument1.GetElementsByTagName("reg_Number_name");
    XmlNodeList elementsByTagName9 = xmlDocument1.GetElementsByTagName("alpha_name");
    XmlNodeList elementsByTagName10 = xmlDocument2.GetElementsByTagName("Trainname");
    XmlNodeList elementsByTagName11 = xmlDocument3.GetElementsByTagName("Stationname");
    string[] strArray = new string[2];
    string station_name = string.Empty;
    int index = 0;
    while (index < frmMainFormIPIS.train_cnt)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.train_details[index].train_no, train_no, false) == 0)
      {
        station_name = frmMainFormIPIS.train_details[index].end_Station;
        break;
      }
      checked { ++index; }
    }
    string str = Microsoft.VisualBasic.CompilerServices.Operators.CompareString(ad, "A", false) != 0 ? departure_time : arrival_time;
    strArray = late.Split(':');
    if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(PFno, (string) null, false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(PFno, "", false) == 0)
      PFno = "0";
    string arr_dep_time = Microsoft.VisualBasic.CompilerServices.Operators.CompareString(ad, "A", false) != 0 ? departure_time : arrival_time;
    if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "RUNNING LATE", false) == 0 && Microsoft.VisualBasic.CompilerServices.Operators.CompareString(late, "00:00", false) == 0 || Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, string.Empty, false) == 0)
      return;
    if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(elementsByTagName1.Item(0).FirstChild.InnerText, "Kind_attn", false) == 0)
      voice_xml_files.PlayVoice(elementsByTagName1.Item(0).LastChild.InnerText, AudioPlayMode.WaitToComplete);
    if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "ARRIVING ON", false) == 0)
    {
      voice_xml_files.train_no_voice(train_no, elementsByTagName2, elementsByTagName3, elementsByTagName9);
      voice_xml_files.train_name_voice(train_no, elementsByTagName10);
      voice_xml_files.platform_no_voice(PFno, elementsByTagName7, elementsByTagName9);
      voice_xml_files.train_status_voice(train_status, elementsByTagName4);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "ARRIVED ON", false) == 0)
    {
      voice_xml_files.train_no_voice(train_no, elementsByTagName2, elementsByTagName3, elementsByTagName9);
      voice_xml_files.train_name_voice(train_no, elementsByTagName10);
      voice_xml_files.platform_no_voice(PFno, elementsByTagName7, elementsByTagName9);
      voice_xml_files.train_status_voice(train_status, elementsByTagName4);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "RUNNING ON TIME", false) == 0)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(PFno, "0", false) == 0)
      {
        voice_xml_files.reg_arr_dep_time_voice(sch_arr, elementsByTagName6, elementsByTagName5, elementsByTagName8);
        voice_xml_files.train_status_voice("EXPECTED TO ARRIVE AT", elementsByTagName4);
        voice_xml_files.train_no_voice(train_no, elementsByTagName2, elementsByTagName3, elementsByTagName9);
        voice_xml_files.train_name_voice(train_no, elementsByTagName10);
        voice_xml_files.train_status_voice(train_status, elementsByTagName4);
      }
      else
      {
        voice_xml_files.reg_arr_dep_time_voice(sch_arr, elementsByTagName6, elementsByTagName5, elementsByTagName8);
        voice_xml_files.train_status_voice("EXPECTED TO ARRIVE AT", elementsByTagName4);
        voice_xml_files.train_no_voice(train_no, elementsByTagName2, elementsByTagName3, elementsByTagName9);
        voice_xml_files.train_name_voice(train_no, elementsByTagName10);
        voice_xml_files.platform_no_voice(PFno, elementsByTagName7, elementsByTagName9);
        voice_xml_files.train_status_voice("ARRIVING ON", elementsByTagName4);
        voice_xml_files.train_status_voice("RUNNING ON TIME", elementsByTagName4);
      }
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "EXPECTED SHORTLY", false) == 0)
    {
      voice_xml_files.train_no_voice(train_no, elementsByTagName2, elementsByTagName3, elementsByTagName9);
      voice_xml_files.train_name_voice(train_no, elementsByTagName10);
      voice_xml_files.train_status_voice("MarigodhiSepatlo", elementsByTagName4);
      voice_xml_files.platform_no_voice(PFno, elementsByTagName7, elementsByTagName9);
      voice_xml_files.train_status_voice("ARRIVING ON", elementsByTagName4);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "LEAVE SHORTLY", false) == 0)
    {
      voice_xml_files.train_no_voice(train_no, elementsByTagName2, elementsByTagName3, elementsByTagName9);
      voice_xml_files.train_name_voice(train_no, elementsByTagName10);
      voice_xml_files.train_status_voice("MarigodhiSepatlo", elementsByTagName4);
      voice_xml_files.platform_no_voice(PFno, elementsByTagName7, elementsByTagName9);
      voice_xml_files.train_status_voice("LEAVE FROM", elementsByTagName4);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "RUNNING LATE", false) == 0)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(PFno, "0", false) == 0)
      {
        voice_xml_files.reg_arr_dep_time_voice(sch_arr, elementsByTagName6, elementsByTagName5, elementsByTagName8);
        voice_xml_files.train_status_voice("EXPECTED TO ARRIVE AT", elementsByTagName4);
        voice_xml_files.train_no_voice(train_no, elementsByTagName2, elementsByTagName3, elementsByTagName9);
        voice_xml_files.train_name_voice(train_no, elementsByTagName10);
        voice_xml_files.reg_time_hour_voice(late, elementsByTagName6, elementsByTagName5, elementsByTagName8);
        voice_xml_files.train_status_voice(train_status, elementsByTagName4);
        voice_xml_files.reg_arr_dep_time_voice(arr_dep_time, elementsByTagName6, elementsByTagName5, elementsByTagName8);
        voice_xml_files.train_status_voice("RANUNNADI", elementsByTagName4);
        voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
      }
      else
      {
        voice_xml_files.reg_arr_dep_time_voice(sch_arr, elementsByTagName6, elementsByTagName5, elementsByTagName8);
        voice_xml_files.train_status_voice("EXPECTED TO ARRIVE AT", elementsByTagName4);
        voice_xml_files.train_no_voice(train_no, elementsByTagName2, elementsByTagName3, elementsByTagName9);
        voice_xml_files.train_name_voice(train_no, elementsByTagName10);
        voice_xml_files.reg_time_hour_voice(late, elementsByTagName6, elementsByTagName5, elementsByTagName8);
        voice_xml_files.train_status_voice("RUNNING LATE", elementsByTagName4);
        voice_xml_files.reg_arr_dep_time_voice(arr_dep_time, elementsByTagName6, elementsByTagName5, elementsByTagName8);
        voice_xml_files.platform_no_voice(PFno, elementsByTagName7, elementsByTagName9);
        voice_xml_files.train_status_voice("ARRIVING ON", elementsByTagName4);
        voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
      }
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "INDEFINITE LATE", false) == 0)
    {
      voice_xml_files.train_no_voice(train_no, elementsByTagName2, elementsByTagName3, elementsByTagName9);
      voice_xml_files.train_name_voice(train_no, elementsByTagName10);
      voice_xml_files.train_status_voice(train_status, elementsByTagName4);
      voice_xml_files.train_status_voice("VISIT ENQUIRY", elementsByTagName4);
      voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "READY TO LEAVE", false) == 0)
    {
      voice_xml_files.train_no_voice(train_no, elementsByTagName2, elementsByTagName3, elementsByTagName9);
      voice_xml_files.train_name_voice(train_no, elementsByTagName10);
      voice_xml_files.platform_no_voice(PFno, elementsByTagName7, elementsByTagName9);
      voice_xml_files.train_status_voice(train_status, elementsByTagName4);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "ON PLATFORM", false) == 0)
    {
      voice_xml_files.train_no_voice(train_no, elementsByTagName2, elementsByTagName3, elementsByTagName9);
      voice_xml_files.train_name_voice(train_no, elementsByTagName10);
      voice_xml_files.platform_no_voice(PFno, elementsByTagName7, elementsByTagName9);
      voice_xml_files.train_status_voice(train_status, elementsByTagName4);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "SCHEDULED DEPARTURE", false) == 0)
    {
      voice_xml_files.train_no_voice(train_no, elementsByTagName2, elementsByTagName3, elementsByTagName9);
      voice_xml_files.train_name_voice(train_no, elementsByTagName10);
      voice_xml_files.reg_arr_dep_time_voice(sch_dep, elementsByTagName6, elementsByTagName5, elementsByTagName8);
      voice_xml_files.platform_no_voice(PFno, elementsByTagName7, elementsByTagName9);
      voice_xml_files.train_status_voice("SCHEDULED RIGHT TIME", elementsByTagName4);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "REGULATED", false) == 0)
    {
      if (Conversions.ToDouble(PFno) == 0.0)
      {
        voice_xml_files.train_status_voice("VARIOUS REASONS", elementsByTagName4);
        voice_xml_files.reg_arr_dep_time_voice(sch_dep, elementsByTagName6, elementsByTagName5, elementsByTagName8);
        voice_xml_files.train_status_voice("TO DEPART", elementsByTagName4);
        voice_xml_files.train_no_voice(train_no, elementsByTagName2, elementsByTagName3, elementsByTagName9);
        voice_xml_files.train_name_voice(train_no, elementsByTagName10);
        voice_xml_files.reg_arr_dep_time_voice(arr_dep_time, elementsByTagName6, elementsByTagName5, elementsByTagName8);
        voice_xml_files.train_status_voice("BAYALUDERUNU", elementsByTagName4);
        voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
      }
      else
      {
        voice_xml_files.train_status_voice("VARIOUS REASONS", elementsByTagName4);
        voice_xml_files.reg_arr_dep_time_voice(sch_dep, elementsByTagName6, elementsByTagName5, elementsByTagName8);
        voice_xml_files.train_status_voice("TO DEPART", elementsByTagName4);
        voice_xml_files.train_no_voice(train_no, elementsByTagName2, elementsByTagName3, elementsByTagName9);
        voice_xml_files.train_name_voice(train_no, elementsByTagName10);
        voice_xml_files.reg_arr_dep_time_voice(arr_dep_time, elementsByTagName6, elementsByTagName5, elementsByTagName8);
        voice_xml_files.platform_no_voice(PFno, elementsByTagName7, elementsByTagName9);
        voice_xml_files.train_status_voice("LEAVE FROM", elementsByTagName4);
        voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
      }
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "RESCHEDULED", false) == 0)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(PFno, "0", false) == 0)
      {
        voice_xml_files.train_status_voice("VARIOUS REASONS", elementsByTagName4);
        voice_xml_files.train_no_voice(train_no, elementsByTagName2, elementsByTagName3, elementsByTagName9);
        voice_xml_files.train_name_voice(train_no, elementsByTagName10);
        voice_xml_files.reg_arr_dep_time_voice(arr_dep_time, elementsByTagName6, elementsByTagName5, elementsByTagName8);
        voice_xml_files.train_status_voice("LEAVE FROM", elementsByTagName4);
        voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
      }
      else
      {
        voice_xml_files.train_status_voice("VARIOUS REASONS", elementsByTagName4);
        voice_xml_files.train_no_voice(train_no, elementsByTagName2, elementsByTagName3, elementsByTagName9);
        voice_xml_files.train_name_voice(train_no, elementsByTagName10);
        voice_xml_files.reg_arr_dep_time_voice(arr_dep_time, elementsByTagName6, elementsByTagName5, elementsByTagName8);
        voice_xml_files.platform_no_voice(PFno, elementsByTagName7, elementsByTagName9);
        voice_xml_files.train_status_voice("LEAVE FROM", elementsByTagName4);
        voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
      }
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "CANCELLED", false) == 0)
    {
      voice_xml_files.reg_arr_dep_time_voice(departure_time, elementsByTagName6, elementsByTagName5, elementsByTagName8);
      voice_xml_files.train_status_voice("TO DEPART", elementsByTagName4);
      voice_xml_files.train_no_voice(train_no, elementsByTagName2, elementsByTagName3, elementsByTagName9);
      voice_xml_files.train_name_voice(train_no, elementsByTagName10);
      voice_xml_files.train_status_voice(train_status, elementsByTagName4);
      voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "PLATFORM CHANGE", false) == 0)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(ad, "A", false) == 0)
      {
        voice_xml_files.train_no_voice(train_no, elementsByTagName2, elementsByTagName3, elementsByTagName9);
        voice_xml_files.train_name_voice(train_no, elementsByTagName10);
        voice_xml_files.platform_no_voice(PFno, elementsByTagName7, elementsByTagName9);
        voice_xml_files.train_status_voice("NO_PLATFORM", elementsByTagName4);
        voice_xml_files.train_status_voice("NAKU", elementsByTagName4);
        voice_xml_files.train_status_voice("MARCHADAMAINADI", elementsByTagName4);
        voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
      }
      else
      {
        voice_xml_files.train_no_voice(train_no, elementsByTagName2, elementsByTagName3, elementsByTagName9);
        voice_xml_files.train_name_voice(train_no, elementsByTagName10);
        voice_xml_files.platform_no_voice(PFno, elementsByTagName7, elementsByTagName9);
        voice_xml_files.train_status_voice("NO_PLATFORM", elementsByTagName4);
        voice_xml_files.train_status_voice("NAKU", elementsByTagName4);
        voice_xml_files.train_status_voice("MARCHADAMAINADI", elementsByTagName4);
        voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
      }
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "DIVERTED ROUTE", false) == 0)
    {
      voice_xml_files.train_status_voice("VARIOUS REASONS", elementsByTagName4);
      voice_xml_files.train_no_voice(train_no, elementsByTagName2, elementsByTagName3, elementsByTagName9);
      voice_xml_files.train_name_voice(train_no, elementsByTagName10);
      voice_xml_files.train_status_voice("ROUTE", elementsByTagName4);
      voice_xml_files.train_status_voice("MARCHADAMAINADI", elementsByTagName4);
      voice_xml_files.station_name_voice(reg_station_name, elementsByTagName11);
      voice_xml_files.train_status_voice("NUNDI", elementsByTagName4);
      voice_xml_files.station_name_voice(station_name, elementsByTagName11);
      voice_xml_files.train_status_voice("BAYALUDERUNU", elementsByTagName4);
      voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "HAS LEFT", false) == 0)
    {
      voice_xml_files.train_no_voice(train_no, elementsByTagName2, elementsByTagName3, elementsByTagName9);
      voice_xml_files.train_name_voice(train_no, elementsByTagName10);
      voice_xml_files.train_status_voice("BAYALUDERI", elementsByTagName4);
      voice_xml_files.train_status_voice("NADI", elementsByTagName4);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "TERMINATED", false) == 0)
    {
      voice_xml_files.train_status_voice("VARIOUS REASONS", elementsByTagName4);
      voice_xml_files.train_no_voice(train_no, elementsByTagName2, elementsByTagName3, elementsByTagName9);
      voice_xml_files.train_name_voice(train_no, elementsByTagName10);
      voice_xml_files.station_name_voice(reg_station_name, elementsByTagName11);
      voice_xml_files.train_status_voice("NAKU", elementsByTagName4);
      voice_xml_files.train_status_voice("CANCELLED", elementsByTagName4);
      voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
    }
  }

  public static void hindi_ProcessXML(
    string xmlText,
    string train_name_xmlText,
    string station_name_xmlText,
    string train_no,
    string train_name,
    string ad,
    string PFno,
    string train_status,
    string arrival_time,
    string departure_time,
    string late,
    string sch_arr,
    string sch_dep,
    string hin_station_name)
  {
    XmlDocument xmlDocument1 = new XmlDocument();
    xmlDocument1.LoadXml(xmlText);
    XmlDocument xmlDocument2 = new XmlDocument();
    xmlDocument2.LoadXml(train_name_xmlText);
    XmlDocument xmlDocument3 = new XmlDocument();
    xmlDocument3.LoadXml(station_name_xmlText);
    XmlNodeList elementsByTagName1 = xmlDocument1.GetElementsByTagName("Kind_attn");
    XmlNodeList elementsByTagName2 = xmlDocument1.GetElementsByTagName("Trainnovoice");
    XmlNodeList elementsByTagName3 = xmlDocument1.GetElementsByTagName("Trainno");
    XmlNodeList elementsByTagName4 = xmlDocument1.GetElementsByTagName("Trainstatus");
    xmlDocument1.GetElementsByTagName("Pfno");
    XmlNodeList elementsByTagName5 = xmlDocument1.GetElementsByTagName("Time_name");
    XmlNodeList elementsByTagName6 = xmlDocument1.GetElementsByTagName("Number_name");
    XmlNodeList elementsByTagName7 = xmlDocument1.GetElementsByTagName("alpha_name");
    XmlNodeList elementsByTagName8 = xmlDocument2.GetElementsByTagName("Trainname");
    XmlNodeList elementsByTagName9 = xmlDocument3.GetElementsByTagName("Stationname");
    string[] strArray = new string[2];
    string str = Microsoft.VisualBasic.CompilerServices.Operators.CompareString(ad, "A", false) != 0 ? departure_time : arrival_time;
    string station_name = string.Empty;
    int index = 0;
    while (index < frmMainFormIPIS.train_cnt)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.train_details[index].train_no, train_no, false) == 0)
      {
        station_name = frmMainFormIPIS.train_details[index].end_Station;
        break;
      }
      checked { ++index; }
    }
    strArray = late.Split(':');
    if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(PFno, (string) null, false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(PFno, "--", false) == 0)
      PFno = "0";
    string arr_dep_time = Microsoft.VisualBasic.CompilerServices.Operators.CompareString(ad, "A", false) != 0 ? departure_time : arrival_time;
    if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(elementsByTagName1.Item(0).FirstChild.InnerText, "Kind_attn", false) == 0)
      voice_xml_files.PlayVoice(elementsByTagName1.Item(0).LastChild.InnerText, AudioPlayMode.WaitToComplete);
    voice_xml_files.train_no_voice(train_no, elementsByTagName2, elementsByTagName3, elementsByTagName7);
    voice_xml_files.train_name_voice(train_no, elementsByTagName8);
    if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "ARRIVING ON", false) == 0)
    {
      voice_xml_files.train_status_voice("PLATFORM NO", elementsByTagName4);
      voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
      voice_xml_files.train_status_voice("PER", elementsByTagName4);
      voice_xml_files.train_status_voice(train_status, elementsByTagName4);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "ARRIVED ON", false) == 0)
    {
      voice_xml_files.train_status_voice("PLATFORM NO", elementsByTagName4);
      voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
      voice_xml_files.train_status_voice("PER", elementsByTagName4);
      voice_xml_files.train_status_voice(train_status, elementsByTagName4);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "RUNNING ON TIME", false) == 0)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(PFno, "0", false) == 0)
      {
        voice_xml_files.train_status_voice("SCHEDULED TIME", elementsByTagName4);
        voice_xml_files.hindi_arr_dep_time_voice(sch_arr, elementsByTagName6, elementsByTagName5, "MINUTE");
        voice_xml_files.train_status_voice(train_status, elementsByTagName4);
      }
      else
      {
        voice_xml_files.train_status_voice("SCHEDULED TIME", elementsByTagName4);
        voice_xml_files.hindi_arr_dep_time_voice(sch_arr, elementsByTagName6, elementsByTagName5, "MIN");
        voice_xml_files.train_status_voice("PLATFORM NO", elementsByTagName4);
        voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
        voice_xml_files.train_status_voice("COME", elementsByTagName4);
        voice_xml_files.train_status_voice("THIS_TRAIN", elementsByTagName4);
        voice_xml_files.train_status_voice("RUNNING ON TIME", elementsByTagName4);
      }
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "EXPECTED SHORTLY", false) == 0)
    {
      voice_xml_files.train_status_voice(train_status, elementsByTagName4);
      voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
      voice_xml_files.train_status_voice("COME", elementsByTagName4);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "LEAVE SHORTLY", false) == 0)
    {
      voice_xml_files.train_status_voice("PLATFORM NO", elementsByTagName4);
      voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
      voice_xml_files.train_status_voice("SHORTLY", elementsByTagName4);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "RUNNING LATE", false) == 0)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(PFno, "0", false) != 0)
      {
        voice_xml_files.train_status_voice("SCHEDULED TIME", elementsByTagName4);
        voice_xml_files.hindi_arr_dep_time_voice(sch_arr, elementsByTagName6, elementsByTagName5, "MIN");
        voice_xml_files.hindi_late_time_voice(late, elementsByTagName6, elementsByTagName5);
        voice_xml_files.train_status_voice("RUNNING LATE", elementsByTagName4);
        voice_xml_files.train_status_voice("THIS_TRAIN", elementsByTagName4);
        voice_xml_files.hindi_arr_dep_time_voice(arr_dep_time, elementsByTagName6, elementsByTagName5, "MINUTE");
        voice_xml_files.train_status_voice("PER", elementsByTagName4);
        voice_xml_files.train_status_voice("PLATFORM NO", elementsByTagName4);
        voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
        voice_xml_files.train_status_voice("MIGHT COME", elementsByTagName4);
        voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
      }
      else
      {
        voice_xml_files.train_status_voice("SCHEDULED TIME", elementsByTagName4);
        voice_xml_files.hindi_arr_dep_time_voice(sch_arr, elementsByTagName6, elementsByTagName5, "MIN");
        voice_xml_files.hindi_late_time_voice(late, elementsByTagName6, elementsByTagName5);
        voice_xml_files.train_status_voice(train_status, elementsByTagName4);
        voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
      }
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "INDEFINITE LATE", false) == 0)
    {
      voice_xml_files.train_status_voice(train_status, elementsByTagName4);
      voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "READY TO LEAVE", false) == 0)
    {
      voice_xml_files.train_status_voice("PLATFORM NO", elementsByTagName4);
      voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
      voice_xml_files.train_status_voice(train_status, elementsByTagName4);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "ON PLATFORM", false) == 0)
    {
      voice_xml_files.train_status_voice("PLATFORM NO", elementsByTagName4);
      voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
      voice_xml_files.train_status_voice("PER", elementsByTagName4);
      voice_xml_files.train_status_voice("IS_THERE", elementsByTagName4);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "SCHEDULED DEPARTURE", false) == 0)
    {
      voice_xml_files.train_status_voice(train_status, elementsByTagName4);
      voice_xml_files.hindi_arr_dep_time_voice(sch_dep, elementsByTagName6, elementsByTagName5, "MIN");
      voice_xml_files.train_status_voice("PLATFORM NO", elementsByTagName4);
      voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
      voice_xml_files.train_status_voice("WILL LEAVE", elementsByTagName4);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "RESCHEDULED", false) == 0)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(PFno, "0", false) == 0)
      {
        voice_xml_files.train_status_voice("CHANGE_TIME", elementsByTagName4);
        voice_xml_files.train_status_voice("NOW_THIS_TRAIN", elementsByTagName4);
        voice_xml_files.hindi_arr_dep_time_voice(arr_dep_time, elementsByTagName6, elementsByTagName5, "MINUTE");
        voice_xml_files.train_status_voice("WILL LEAVE", elementsByTagName4);
        voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
      }
      else
      {
        voice_xml_files.train_status_voice("CHANGE_TIME", elementsByTagName4);
        voice_xml_files.train_status_voice("NOW_THIS_TRAIN", elementsByTagName4);
        voice_xml_files.hindi_arr_dep_time_voice(arr_dep_time, elementsByTagName6, elementsByTagName5, "MINUTE");
        voice_xml_files.train_status_voice("PER", elementsByTagName4);
        voice_xml_files.train_status_voice("PLATFORM NO", elementsByTagName4);
        voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
        voice_xml_files.train_status_voice("WILL LEAVE", elementsByTagName4);
        voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
      }
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "REGULATED", false) == 0)
    {
      if (Conversions.ToDouble(PFno) == 0.0)
      {
        voice_xml_files.train_status_voice("SCHEDULED DEPARTURE", elementsByTagName4);
        voice_xml_files.hindi_arr_dep_time_voice(sch_dep, elementsByTagName6, elementsByTagName5, "MIN");
        voice_xml_files.hindi_arr_dep_time_voice(arr_dep_time, elementsByTagName6, elementsByTagName5, "MINUTE");
        voice_xml_files.train_status_voice("PER", elementsByTagName4);
        voice_xml_files.train_status_voice("RAVANA", elementsByTagName4);
        voice_xml_files.train_status_voice("HOGI", elementsByTagName4);
        voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
      }
      else
      {
        voice_xml_files.train_status_voice("SCHEDULED DEPARTURE", elementsByTagName4);
        voice_xml_files.hindi_arr_dep_time_voice(sch_dep, elementsByTagName6, elementsByTagName5, "MIN");
        voice_xml_files.hindi_arr_dep_time_voice(arr_dep_time, elementsByTagName6, elementsByTagName5, "MINUTE");
        voice_xml_files.train_status_voice("PER", elementsByTagName4);
        voice_xml_files.train_status_voice("PLATFORM NO", elementsByTagName4);
        voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
        voice_xml_files.train_status_voice("WILL LEAVE", elementsByTagName4);
        voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
      }
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "DIVERTED ROUTE", false) == 0)
    {
      voice_xml_files.train_status_voice("CHANGE_WAY", elementsByTagName4);
      voice_xml_files.train_status_voice("TODAY_THIS_TRAIN", elementsByTagName4);
      voice_xml_files.station_name_voice(hin_station_name, elementsByTagName9);
      voice_xml_files.train_status_voice("SE", elementsByTagName4);
      voice_xml_files.station_name_voice(station_name, elementsByTagName9);
      voice_xml_files.train_status_voice("RAVANA", elementsByTagName4);
      voice_xml_files.train_status_voice("HOGI", elementsByTagName4);
      voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "CANCELLED", false) == 0)
    {
      voice_xml_files.train_status_voice(train_status, elementsByTagName4);
      voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "PLATFORM CHANGE", false) == 0)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(ad, "A", false) == 0)
      {
        voice_xml_files.train_status_voice("PLATFORM_CHANGE", elementsByTagName4);
        voice_xml_files.train_status_voice("NOW_THIS_TRAIN", elementsByTagName4);
        voice_xml_files.train_status_voice("PLATFORM NO", elementsByTagName4);
        voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
        voice_xml_files.train_status_voice("COME", elementsByTagName4);
        voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
      }
      else
      {
        voice_xml_files.train_status_voice("PLATFORM_CHANGE", elementsByTagName4);
        voice_xml_files.train_status_voice("NOW_THIS_TRAIN", elementsByTagName4);
        voice_xml_files.train_status_voice("PLATFORM NO", elementsByTagName4);
        voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
        voice_xml_files.train_status_voice("WILL LEAVE", elementsByTagName4);
        voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
      }
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "HAS LEFT", false) == 0)
    {
      voice_xml_files.train_status_voice(train_status, elementsByTagName4);
    }
    else
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "TERMINATED", false) != 0)
        return;
      voice_xml_files.station_name_voice(station_name, elementsByTagName9);
      voice_xml_files.train_status_voice("THAK", elementsByTagName4);
      voice_xml_files.train_status_voice("NAJAAKAR", elementsByTagName4);
      voice_xml_files.station_name_voice(hin_station_name, elementsByTagName9);
      voice_xml_files.train_status_voice("THAK", elementsByTagName4);
      voice_xml_files.train_status_voice("JAYEGI", elementsByTagName4);
      voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
    }
  }

  public static void oriya_ProcessXML(
    string xmlText,
    string train_name_xmlText,
    string station_name_xmlText,
    string train_no,
    string train_name,
    string ad,
    string PFno,
    string train_status,
    string arrival_time,
    string departure_time,
    string late,
    string sch_arr,
    string sch_dep,
    string hin_station_name)
  {
    XmlDocument xmlDocument1 = new XmlDocument();
    xmlDocument1.LoadXml(xmlText);
    XmlDocument xmlDocument2 = new XmlDocument();
    xmlDocument2.LoadXml(train_name_xmlText);
    XmlDocument xmlDocument3 = new XmlDocument();
    xmlDocument3.LoadXml(station_name_xmlText);
    XmlNodeList elementsByTagName1 = xmlDocument1.GetElementsByTagName("Kind_attn");
    XmlNodeList elementsByTagName2 = xmlDocument1.GetElementsByTagName("Trainnovoice");
    XmlNodeList elementsByTagName3 = xmlDocument1.GetElementsByTagName("Trainno");
    XmlNodeList elementsByTagName4 = xmlDocument1.GetElementsByTagName("Trainstatus");
    xmlDocument1.GetElementsByTagName("Pfno");
    XmlNodeList elementsByTagName5 = xmlDocument1.GetElementsByTagName("Time_name");
    XmlNodeList elementsByTagName6 = xmlDocument1.GetElementsByTagName("Number_name");
    XmlNodeList elementsByTagName7 = xmlDocument1.GetElementsByTagName("alpha_name");
    XmlNodeList elementsByTagName8 = xmlDocument2.GetElementsByTagName("Trainname");
    XmlNodeList elementsByTagName9 = xmlDocument3.GetElementsByTagName("Stationname");
    string[] strArray = new string[2];
    string str = Microsoft.VisualBasic.CompilerServices.Operators.CompareString(ad, "A", false) != 0 ? departure_time : arrival_time;
    string station_name = string.Empty;
    int index = 0;
    while (index < frmMainFormIPIS.train_cnt)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.train_details[index].train_no, train_no, false) == 0)
      {
        station_name = frmMainFormIPIS.train_details[index].end_Station;
        break;
      }
      checked { ++index; }
    }
    strArray = late.Split(':');
    if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(PFno, (string) null, false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(PFno, "--", false) == 0)
      PFno = "0";
    string arr_dep_time = Microsoft.VisualBasic.CompilerServices.Operators.CompareString(ad, "A", false) != 0 ? departure_time : arrival_time;
    if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(elementsByTagName1.Item(0).FirstChild.InnerText, "Kind_attn", false) == 0)
      voice_xml_files.PlayVoice(elementsByTagName1.Item(0).LastChild.InnerText, AudioPlayMode.WaitToComplete);
    voice_xml_files.train_no_voice(train_no, elementsByTagName2, elementsByTagName3, elementsByTagName7);
    voice_xml_files.train_name_voice(train_no, elementsByTagName8);
    if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "ARRIVING ON", false) == 0)
    {
      voice_xml_files.train_status_voice("PLATFORM NO", elementsByTagName4);
      voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
      voice_xml_files.train_status_voice(train_status, elementsByTagName4);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "ARRIVED ON", false) == 0)
    {
      voice_xml_files.train_status_voice("PLATFORM NO", elementsByTagName4);
      voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
      voice_xml_files.train_status_voice("ON", elementsByTagName4);
      voice_xml_files.train_status_voice(train_status, elementsByTagName4);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "RUNNING ON TIME", false) == 0)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(PFno, "0", false) == 0)
      {
        voice_xml_files.train_status_voice("SCHEDULED TIME", elementsByTagName4);
        voice_xml_files.hindi_arr_dep_time_voice(sch_arr, elementsByTagName6, elementsByTagName5, "MIN");
        voice_xml_files.train_status_voice(train_status, elementsByTagName4);
      }
      else
      {
        voice_xml_files.train_status_voice("SCHEDULED TIME", elementsByTagName4);
        voice_xml_files.hindi_arr_dep_time_voice(sch_arr, elementsByTagName6, elementsByTagName5, "MIN");
        voice_xml_files.train_status_voice("PLATFORM NO", elementsByTagName4);
        voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
        voice_xml_files.train_status_voice("ON", elementsByTagName4);
        voice_xml_files.train_status_voice("COME", elementsByTagName4);
        voice_xml_files.train_status_voice("THIS_TRAIN", elementsByTagName4);
        voice_xml_files.train_status_voice("RUNNING ON TIME", elementsByTagName4);
      }
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "EXPECTED SHORTLY", false) == 0)
    {
      voice_xml_files.train_status_voice("SCHEDULED TIME", elementsByTagName4);
      voice_xml_files.hindi_arr_dep_time_voice(sch_arr, elementsByTagName6, elementsByTagName5, "MIN");
      voice_xml_files.train_status_voice("AFTER_SOMETIME", elementsByTagName4);
      voice_xml_files.train_status_voice("PLATFORM NO", elementsByTagName4);
      voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
      voice_xml_files.train_status_voice("ON", elementsByTagName4);
      voice_xml_files.train_status_voice("COME", elementsByTagName4);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "RUNNING LATE", false) == 0)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(PFno, "0", false) != 0)
      {
        voice_xml_files.train_status_voice("SCHEDULED TIME", elementsByTagName4);
        voice_xml_files.hindi_arr_dep_time_voice(sch_arr, elementsByTagName6, elementsByTagName5, "MIN");
        voice_xml_files.oriya_late_time_voice(late, elementsByTagName6, elementsByTagName5);
        voice_xml_files.train_status_voice("RUNNING LATE", elementsByTagName4);
        voice_xml_files.train_status_voice("THIS_TRAIN", elementsByTagName4);
        voice_xml_files.hindi_arr_dep_time_voice(arr_dep_time, elementsByTagName6, elementsByTagName5, "MIN");
        voice_xml_files.train_status_voice("ON", elementsByTagName4);
        voice_xml_files.train_status_voice("PLATFORM NO", elementsByTagName4);
        voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
        voice_xml_files.train_status_voice("ON", elementsByTagName4);
        voice_xml_files.train_status_voice("COME", elementsByTagName4);
        voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
      }
      else
      {
        voice_xml_files.train_status_voice("SCHEDULED TIME", elementsByTagName4);
        voice_xml_files.hindi_arr_dep_time_voice(sch_arr, elementsByTagName6, elementsByTagName5, "MIN");
        voice_xml_files.oriya_late_time_voice(late, elementsByTagName6, elementsByTagName5);
        voice_xml_files.train_status_voice(train_status, elementsByTagName4);
        voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
      }
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "INDEFINITE LATE", false) == 0)
    {
      voice_xml_files.train_status_voice(train_status, elementsByTagName4);
      voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "READY TO LEAVE", false) == 0)
    {
      voice_xml_files.train_status_voice("PLATFORM NO", elementsByTagName4);
      voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
      voice_xml_files.train_status_voice(train_status, elementsByTagName4);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "ON PLATFORM", false) == 0)
    {
      voice_xml_files.train_status_voice("PLATFORM NO", elementsByTagName4);
      voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
      voice_xml_files.train_status_voice("ON", elementsByTagName4);
      voice_xml_files.train_status_voice("IS_THERE", elementsByTagName4);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "SCHEDULED DEPARTURE", false) == 0)
    {
      voice_xml_files.train_status_voice(train_status, elementsByTagName4);
      voice_xml_files.hindi_arr_dep_time_voice(sch_dep, elementsByTagName6, elementsByTagName5, "MIN");
      voice_xml_files.train_status_voice("PLATFORM NO", elementsByTagName4);
      voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
      voice_xml_files.train_status_voice("READY TO LEAVE", elementsByTagName4);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "RESCHEDULED", false) == 0)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(PFno, "0", false) == 0)
      {
        voice_xml_files.train_status_voice("THE", elementsByTagName4);
        voice_xml_files.train_status_voice("CHANGE_TIME", elementsByTagName4);
        voice_xml_files.train_status_voice("NOW_THIS_TRAIN", elementsByTagName4);
        voice_xml_files.hindi_arr_dep_time_voice(arr_dep_time, elementsByTagName6, elementsByTagName5, "MIN");
        voice_xml_files.train_status_voice("READY TO LEAVE", elementsByTagName4);
        voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
      }
      else
      {
        voice_xml_files.train_status_voice("THE", elementsByTagName4);
        voice_xml_files.train_status_voice("CHANGE_TIME", elementsByTagName4);
        voice_xml_files.train_status_voice("NOW_THIS_TRAIN", elementsByTagName4);
        voice_xml_files.hindi_arr_dep_time_voice(arr_dep_time, elementsByTagName6, elementsByTagName5, "MIN");
        voice_xml_files.train_status_voice("PLATFORM NO", elementsByTagName4);
        voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
        voice_xml_files.train_status_voice("READY TO LEAVE", elementsByTagName4);
        voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
      }
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "REGULATED", false) == 0)
    {
      if (Conversions.ToDouble(PFno) == 0.0)
      {
        voice_xml_files.train_status_voice("SCHEDULED DEPARTURE", elementsByTagName4);
        voice_xml_files.hindi_arr_dep_time_voice(sch_dep, elementsByTagName6, elementsByTagName5, "MIN");
        voice_xml_files.train_status_voice("TODAY", elementsByTagName4);
        voice_xml_files.hindi_arr_dep_time_voice(arr_dep_time, elementsByTagName6, elementsByTagName5, "MIN");
        voice_xml_files.train_status_voice("READY TO LEAVE", elementsByTagName4);
        voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
      }
      else
      {
        voice_xml_files.train_status_voice("SCHEDULED DEPARTURE", elementsByTagName4);
        voice_xml_files.hindi_arr_dep_time_voice(sch_dep, elementsByTagName6, elementsByTagName5, "MIN");
        voice_xml_files.train_status_voice("TODAY", elementsByTagName4);
        voice_xml_files.hindi_arr_dep_time_voice(arr_dep_time, elementsByTagName6, elementsByTagName5, "MIN");
        voice_xml_files.train_status_voice("SE", elementsByTagName4);
        voice_xml_files.train_status_voice("PLATFORM NO", elementsByTagName4);
        voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
        voice_xml_files.train_status_voice("READY TO LEAVE", elementsByTagName4);
        voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
      }
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "DIVERTED ROUTE", false) == 0)
    {
      voice_xml_files.train_status_voice("CHANGE_WAY", elementsByTagName4);
      voice_xml_files.train_status_voice("TODAY_THIS_TRAIN", elementsByTagName4);
      voice_xml_files.station_name_voice(hin_station_name, elementsByTagName9);
      voice_xml_files.station_name_voice(station_name, elementsByTagName9);
      voice_xml_files.train_status_voice("GOING_FROM", elementsByTagName4);
      voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "CANCELLED", false) == 0)
    {
      voice_xml_files.train_status_voice("Today", elementsByTagName4);
      voice_xml_files.train_status_voice(train_status, elementsByTagName4);
      voice_xml_files.train_status_voice("TRAIN_CANCELLED", elementsByTagName4);
      voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "PLATFORM CHANGE", false) == 0)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(ad, "A", false) == 0)
      {
        voice_xml_files.train_status_voice("PLATFORM_CHANGE", elementsByTagName4);
        voice_xml_files.train_status_voice("NOW_THIS_TRAIN", elementsByTagName4);
        voice_xml_files.train_status_voice("PLATFORM NO", elementsByTagName4);
        voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
        voice_xml_files.train_status_voice("COME", elementsByTagName4);
        voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
      }
      else
      {
        voice_xml_files.train_status_voice("PLATFORM_CHANGE", elementsByTagName4);
        voice_xml_files.train_status_voice("NOW_THIS_TRAIN", elementsByTagName4);
        voice_xml_files.train_status_voice("PLATFORM NO", elementsByTagName4);
        voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
        voice_xml_files.train_status_voice("READY TO LEAVE", elementsByTagName4);
        voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
      }
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "HAS LEFT", false) == 0)
    {
      voice_xml_files.train_status_voice(train_status, elementsByTagName4);
    }
    else
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "TERMINATED", false) != 0)
        return;
      voice_xml_files.station_name_voice(station_name, elementsByTagName9);
      voice_xml_files.station_name_voice(hin_station_name, elementsByTagName9);
      voice_xml_files.train_status_voice("ON", elementsByTagName4);
      voice_xml_files.train_status_voice("NO_JAYEGI", elementsByTagName4);
      voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
    }
  }

  public static void Marathi_ProcessXML(
    string xmlText,
    string train_name_xmlText,
    string station_name_xmlText,
    string train_no,
    string train_name,
    string ad,
    string PFno,
    string train_status,
    string arrival_time,
    string departure_time,
    string late,
    string sch_arr,
    string sch_dep,
    string marathi_station_name)
  {
    XmlDocument xmlDocument1 = new XmlDocument();
    xmlDocument1.LoadXml(xmlText);
    XmlDocument xmlDocument2 = new XmlDocument();
    xmlDocument2.LoadXml(train_name_xmlText);
    XmlDocument xmlDocument3 = new XmlDocument();
    xmlDocument3.LoadXml(station_name_xmlText);
    XmlNodeList elementsByTagName1 = xmlDocument1.GetElementsByTagName("Kind_attn");
    XmlNodeList elementsByTagName2 = xmlDocument1.GetElementsByTagName("Trainnovoice");
    XmlNodeList elementsByTagName3 = xmlDocument1.GetElementsByTagName("Trainno");
    XmlNodeList elementsByTagName4 = xmlDocument1.GetElementsByTagName("Trainstatus");
    xmlDocument1.GetElementsByTagName("Pfno");
    XmlNodeList elementsByTagName5 = xmlDocument1.GetElementsByTagName("Time_name");
    XmlNodeList elementsByTagName6 = xmlDocument1.GetElementsByTagName("Number_name");
    XmlNodeList elementsByTagName7 = xmlDocument1.GetElementsByTagName("alpha_name");
    XmlNodeList elementsByTagName8 = xmlDocument2.GetElementsByTagName("Trainname");
    XmlNodeList elementsByTagName9 = xmlDocument3.GetElementsByTagName("Stationname");
    string[] strArray = new string[2];
    string str1 = Microsoft.VisualBasic.CompilerServices.Operators.CompareString(ad, "A", false) != 0 ? departure_time : arrival_time;
    string str2 = string.Empty;
    int index = 0;
    while (index < frmMainFormIPIS.train_cnt)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.train_details[index].train_no, train_no, false) == 0)
      {
        str2 = frmMainFormIPIS.train_details[index].end_Station;
        break;
      }
      checked { ++index; }
    }
    strArray = late.Split(':');
    if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(PFno, (string) null, false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(PFno, "--", false) == 0)
      PFno = "0";
    string arr_dep_time = Microsoft.VisualBasic.CompilerServices.Operators.CompareString(ad, "A", false) != 0 ? departure_time : arrival_time;
    if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(elementsByTagName1.Item(0).FirstChild.InnerText, "Kind_attn", false) == 0)
      voice_xml_files.PlayVoice(elementsByTagName1.Item(0).LastChild.InnerText, AudioPlayMode.WaitToComplete);
    voice_xml_files.train_no_voice(train_no, elementsByTagName2, elementsByTagName3, elementsByTagName7);
    voice_xml_files.train_name_voice(train_no, elementsByTagName8);
    if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "ARRIVING ON", false) == 0)
    {
      voice_xml_files.train_status_voice("PLATFORM NO", elementsByTagName4);
      voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
      voice_xml_files.train_status_voice(train_status, elementsByTagName4);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "ARRIVED ON", false) == 0)
    {
      voice_xml_files.train_status_voice("PLATFORM NO", elementsByTagName4);
      voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
      voice_xml_files.train_status_voice(train_status, elementsByTagName4);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "RUNNING ON TIME", false) == 0)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(PFno, "0", false) == 0)
      {
        voice_xml_files.train_status_voice("SCHEDULED TIME", elementsByTagName4);
        voice_xml_files.marathi_arr_dep_time_voice(sch_arr, elementsByTagName6, elementsByTagName5, "MIN");
        voice_xml_files.train_status_voice("COME", elementsByTagName4);
        voice_xml_files.train_status_voice(train_status, elementsByTagName4);
      }
      else
      {
        voice_xml_files.train_status_voice("RUNNING_TIME", elementsByTagName4);
        voice_xml_files.train_status_voice("NOW_THIS_TRAIN", elementsByTagName4);
        voice_xml_files.marathi_arr_dep_time_voice(sch_arr, elementsByTagName6, elementsByTagName5, "MIN");
        voice_xml_files.train_status_voice("PLATFORM NO", elementsByTagName4);
        voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
        voice_xml_files.train_status_voice("PER", elementsByTagName4);
        voice_xml_files.train_status_voice("COME", elementsByTagName4);
      }
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "EXPECTED SHORTLY", false) == 0)
    {
      voice_xml_files.train_status_voice("ALREADY_ANNOUNCE", elementsByTagName4);
      voice_xml_files.train_status_voice("AFTER_SOMETIME", elementsByTagName4);
      voice_xml_files.train_status_voice("PLATFORM NO", elementsByTagName4);
      voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
      voice_xml_files.train_status_voice(train_status, elementsByTagName4);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "RUNNING LATE", false) == 0)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(PFno, "0", false) != 0)
      {
        voice_xml_files.train_status_voice("SCHEDULED TIME", elementsByTagName4);
        voice_xml_files.marathi_late_time_voice(late, elementsByTagName6, elementsByTagName5);
        voice_xml_files.train_status_voice(train_status, elementsByTagName4);
        voice_xml_files.train_status_voice("NOW_THIS_TRAIN", elementsByTagName4);
        voice_xml_files.marathi_arr_dep_time_voice(arr_dep_time, elementsByTagName6, elementsByTagName5, "MIN");
        voice_xml_files.train_status_voice("PLATFORM NO", elementsByTagName4);
        voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
        voice_xml_files.train_status_voice("PER", elementsByTagName4);
        voice_xml_files.train_status_voice("COME", elementsByTagName4);
        voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
      }
      else
      {
        voice_xml_files.train_status_voice("SCHEDULED TIME", elementsByTagName4);
        voice_xml_files.marathi_arr_dep_time_voice(sch_arr, elementsByTagName6, elementsByTagName5, "MIN");
        voice_xml_files.train_status_voice("COME", elementsByTagName4);
        voice_xml_files.marathi_late_time_voice(late, elementsByTagName6, elementsByTagName5);
        voice_xml_files.train_status_voice(train_status, elementsByTagName4);
        voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
      }
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "INDEFINITE LATE", false) == 0)
      voice_xml_files.train_status_voice(train_status, elementsByTagName4);
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "READY TO LEAVE", false) == 0)
    {
      voice_xml_files.train_status_voice("PLATFORM NO", elementsByTagName4);
      voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
      voice_xml_files.train_status_voice(train_status, elementsByTagName4);
      voice_xml_files.train_status_voice("COMFORTABLE WISH", elementsByTagName4);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "ON PLATFORM", false) == 0)
    {
      voice_xml_files.train_status_voice("PLATFORM NO", elementsByTagName4);
      voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
      voice_xml_files.train_status_voice("PER", elementsByTagName4);
      voice_xml_files.train_status_voice("ON_PLATFORM", elementsByTagName4);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "SCHEDULED DEPARTURE", false) == 0)
    {
      voice_xml_files.train_status_voice(train_status, elementsByTagName4);
      voice_xml_files.marathi_arr_dep_time_voice(sch_dep, elementsByTagName6, elementsByTagName5, "MIN");
      voice_xml_files.train_status_voice("PLATFORM NO", elementsByTagName4);
      voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
      voice_xml_files.train_status_voice("PER", elementsByTagName4);
      voice_xml_files.train_status_voice("WILL LEAVE", elementsByTagName4);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "RESCHEDULED", false) == 0)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(PFno, "0", false) == 0)
      {
        voice_xml_files.train_status_voice("CHANGE_TIME", elementsByTagName4);
        voice_xml_files.train_status_voice("NOW_THIS_TRAIN", elementsByTagName4);
        voice_xml_files.marathi_arr_dep_time_voice(arr_dep_time, elementsByTagName6, elementsByTagName5, "MIN");
        voice_xml_files.train_status_voice("WILL LEAVE", elementsByTagName4);
        voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
      }
      else
      {
        voice_xml_files.train_status_voice(train_status, elementsByTagName4);
        voice_xml_files.marathi_arr_dep_time_voice(sch_dep, elementsByTagName6, elementsByTagName5, "MIN");
        voice_xml_files.train_status_voice("CHANGE_TIME", elementsByTagName4);
        voice_xml_files.train_status_voice("NOW_THIS_TRAIN", elementsByTagName4);
        voice_xml_files.marathi_arr_dep_time_voice(arr_dep_time, elementsByTagName6, elementsByTagName5, "MIN");
        voice_xml_files.train_status_voice("PLATFORM NO", elementsByTagName4);
        voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
        voice_xml_files.train_status_voice("PER", elementsByTagName4);
        voice_xml_files.train_status_voice("WILL LEAVE", elementsByTagName4);
        voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
      }
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "REGULATED", false) == 0)
    {
      if (Conversions.ToDouble(PFno) == 0.0)
      {
        voice_xml_files.train_status_voice(train_status, elementsByTagName4);
        voice_xml_files.marathi_arr_dep_time_voice(sch_dep, elementsByTagName6, elementsByTagName5, "MIN");
        voice_xml_files.train_status_voice("COME", elementsByTagName4);
        voice_xml_files.train_status_voice("NOW_THIS_TRAIN", elementsByTagName4);
        voice_xml_files.marathi_arr_dep_time_voice(arr_dep_time, elementsByTagName6, elementsByTagName5, "MIN");
        voice_xml_files.train_status_voice("VAROON", elementsByTagName4);
        voice_xml_files.train_status_voice("WILL LEAVE", elementsByTagName4);
        voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
      }
      else
      {
        voice_xml_files.train_status_voice(train_status, elementsByTagName4);
        voice_xml_files.marathi_arr_dep_time_voice(sch_dep, elementsByTagName6, elementsByTagName5, "MIN");
        voice_xml_files.train_status_voice("COME", elementsByTagName4);
        voice_xml_files.train_status_voice("NOW_THIS_TRAIN", elementsByTagName4);
        voice_xml_files.marathi_arr_dep_time_voice(arr_dep_time, elementsByTagName6, elementsByTagName5, "MIN");
        voice_xml_files.train_status_voice("PLATFORM NO", elementsByTagName4);
        voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
        voice_xml_files.train_status_voice("VAROON", elementsByTagName4);
        voice_xml_files.train_status_voice("WILL LEAVE", elementsByTagName4);
        voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
      }
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "DIVERTED ROUTE", false) == 0)
    {
      voice_xml_files.train_status_voice("CHANGE_WAY", elementsByTagName4);
      voice_xml_files.station_name_voice(marathi_station_name, elementsByTagName9);
      voice_xml_files.train_status_voice("JAYEGI", elementsByTagName4);
      voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "CANCELLED", false) == 0)
    {
      voice_xml_files.train_status_voice("TRAIN_CANCELLED", elementsByTagName4);
      voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "PLATFORM CHANGE", false) == 0)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(ad, "A", false) == 0)
      {
        voice_xml_files.train_status_voice("PLATFORM_CHANGE", elementsByTagName4);
        voice_xml_files.train_status_voice("NOW_THIS_TRAIN", elementsByTagName4);
        voice_xml_files.train_status_voice("PLATFORM NO", elementsByTagName4);
        voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
        voice_xml_files.train_status_voice("PER", elementsByTagName4);
        voice_xml_files.train_status_voice("WILL ARRIVE", elementsByTagName4);
        voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
      }
      else
      {
        voice_xml_files.train_status_voice("PLATFORM_CHANGE", elementsByTagName4);
        voice_xml_files.train_status_voice("NOW_THIS_TRAIN", elementsByTagName4);
        voice_xml_files.train_status_voice("PLATFORM NO", elementsByTagName4);
        voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
        voice_xml_files.train_status_voice("PER", elementsByTagName4);
        voice_xml_files.train_status_voice("WILL LEAVE", elementsByTagName4);
        voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
      }
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "HAS LEFT", false) == 0)
    {
      voice_xml_files.train_status_voice(train_status, elementsByTagName4);
    }
    else
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "TERMINATED", false) != 0)
        return;
      voice_xml_files.train_status_voice("TODAY_TERMINATED", elementsByTagName4);
      voice_xml_files.station_name_voice(marathi_station_name, elementsByTagName9);
      voice_xml_files.train_status_voice("NO_JAYEGI", elementsByTagName4);
      voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
    }
  }

  public static void Bengali_ProcessXML(
    string xmlText,
    string train_name_xmlText,
    string station_name_xmlText,
    string train_no,
    string train_name,
    string ad,
    string PFno,
    string train_status,
    string arrival_time,
    string departure_time,
    string late,
    string sch_arr,
    string sch_dep,
    string reg_station_name)
  {
    XmlDocument xmlDocument1 = new XmlDocument();
    xmlDocument1.LoadXml(xmlText);
    XmlDocument xmlDocument2 = new XmlDocument();
    xmlDocument2.LoadXml(train_name_xmlText);
    XmlDocument xmlDocument3 = new XmlDocument();
    xmlDocument3.LoadXml(station_name_xmlText);
    XmlNodeList elementsByTagName1 = xmlDocument1.GetElementsByTagName("Kind_attn");
    XmlNodeList elementsByTagName2 = xmlDocument1.GetElementsByTagName("Trainnovoice");
    XmlNodeList elementsByTagName3 = xmlDocument1.GetElementsByTagName("Trainno");
    XmlNodeList elementsByTagName4 = xmlDocument1.GetElementsByTagName("Trainstatus");
    XmlNodeList elementsByTagName5 = xmlDocument1.GetElementsByTagName("Time_name");
    xmlDocument1.GetElementsByTagName("TrainHour");
    XmlNodeList elementsByTagName6 = xmlDocument1.GetElementsByTagName("Number_name");
    xmlDocument1.GetElementsByTagName("reg_Number_name");
    XmlNodeList elementsByTagName7 = xmlDocument1.GetElementsByTagName("alpha_name");
    XmlNodeList elementsByTagName8 = xmlDocument2.GetElementsByTagName("Trainname");
    XmlNodeList elementsByTagName9 = xmlDocument3.GetElementsByTagName("Stationname");
    string[] strArray = new string[2];
    string station_name = string.Empty;
    int index = 0;
    while (index < frmMainFormIPIS.train_cnt)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(frmMainFormIPIS.train_details[index].train_no, train_no, false) == 0)
      {
        station_name = frmMainFormIPIS.train_details[index].end_Station;
        break;
      }
      checked { ++index; }
    }
    string str = Microsoft.VisualBasic.CompilerServices.Operators.CompareString(ad, "A", false) != 0 ? departure_time : arrival_time;
    strArray = late.Split(':');
    if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(PFno, (string) null, false) == 0 | Microsoft.VisualBasic.CompilerServices.Operators.CompareString(PFno, "", false) == 0)
      PFno = "0";
    string arr_dep_time = Microsoft.VisualBasic.CompilerServices.Operators.CompareString(ad, "A", false) != 0 ? departure_time : arrival_time;
    if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "RUNNING LATE", false) == 0 && Microsoft.VisualBasic.CompilerServices.Operators.CompareString(late, "00:00", false) == 0 || Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, string.Empty, false) == 0)
      return;
    if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(elementsByTagName1.Item(0).FirstChild.InnerText, "Kind_attn", false) == 0)
      voice_xml_files.PlayVoice(elementsByTagName1.Item(0).LastChild.InnerText, AudioPlayMode.WaitToComplete);
    if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "ARRIVING ON", false) == 0)
    {
      voice_xml_files.train_no_voice(train_no, elementsByTagName2, elementsByTagName3, elementsByTagName7);
      voice_xml_files.train_name_voice(train_no, elementsByTagName8);
      voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
      voice_xml_files.train_status_voice("NO_PLATFORM", elementsByTagName4);
      voice_xml_files.train_status_voice(train_status, elementsByTagName4);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "ARRIVED ON", false) == 0)
    {
      voice_xml_files.train_no_voice(train_no, elementsByTagName2, elementsByTagName3, elementsByTagName7);
      voice_xml_files.train_name_voice(train_no, elementsByTagName8);
      voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
      voice_xml_files.train_status_voice("NO_PLATFORM", elementsByTagName4);
      voice_xml_files.train_status_voice(train_status, elementsByTagName4);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "RUNNING ON TIME", false) == 0)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(PFno, "0", false) == 0)
      {
        voice_xml_files.train_no_voice(train_no, elementsByTagName2, elementsByTagName3, elementsByTagName7);
        voice_xml_files.train_name_voice(train_no, elementsByTagName8);
        voice_xml_files.hindi_arr_dep_time_voice(sch_arr, elementsByTagName6, elementsByTagName5, "MINUTE");
        voice_xml_files.train_status_voice("ARRIVING ON", elementsByTagName4);
        voice_xml_files.train_status_voice(train_status, elementsByTagName4);
      }
      else
      {
        voice_xml_files.train_no_voice(train_no, elementsByTagName2, elementsByTagName3, elementsByTagName7);
        voice_xml_files.train_name_voice(train_no, elementsByTagName8);
        voice_xml_files.hindi_arr_dep_time_voice(sch_arr, elementsByTagName6, elementsByTagName5, "MINUTE");
        voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
        voice_xml_files.train_status_voice("NO_PLATFORM", elementsByTagName4);
        voice_xml_files.train_status_voice("ARRIVING ON", elementsByTagName4);
        voice_xml_files.train_status_voice("RUNNING ON TIME", elementsByTagName4);
      }
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "EXPECTED SHORTLY", false) == 0)
    {
      voice_xml_files.train_no_voice(train_no, elementsByTagName2, elementsByTagName3, elementsByTagName7);
      voice_xml_files.train_name_voice(train_no, elementsByTagName8);
      voice_xml_files.hindi_arr_dep_time_voice(sch_arr, elementsByTagName6, elementsByTagName5, "MINUTE");
      voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
      voice_xml_files.train_status_voice("NO_PLATFORM", elementsByTagName4);
      voice_xml_files.train_status_voice(train_status, elementsByTagName4);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "RUNNING LATE", false) == 0)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(PFno, "0", false) == 0)
      {
        voice_xml_files.train_no_voice(train_no, elementsByTagName2, elementsByTagName3, elementsByTagName7);
        voice_xml_files.train_name_voice(train_no, elementsByTagName8);
        voice_xml_files.hindi_arr_dep_time_voice(sch_arr, elementsByTagName6, elementsByTagName5, "MIN");
        voice_xml_files.train_status_voice("COME", elementsByTagName4);
        voice_xml_files.hindi_late_time_voice(late, elementsByTagName6, elementsByTagName5);
        voice_xml_files.train_status_voice(train_status, elementsByTagName4);
        voice_xml_files.hindi_arr_dep_time_voice(arr_dep_time, elementsByTagName6, elementsByTagName5, "MINUTE");
        voice_xml_files.train_status_voice("WILL_COME", elementsByTagName4);
        voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
      }
      else
      {
        voice_xml_files.train_no_voice(train_no, elementsByTagName2, elementsByTagName3, elementsByTagName7);
        voice_xml_files.train_name_voice(train_no, elementsByTagName8);
        voice_xml_files.hindi_arr_dep_time_voice(sch_arr, elementsByTagName6, elementsByTagName5, "MIN");
        voice_xml_files.train_status_voice("COME", elementsByTagName4);
        voice_xml_files.hindi_late_time_voice(late, elementsByTagName6, elementsByTagName5);
        voice_xml_files.train_status_voice("RUNNING LATE", elementsByTagName4);
        voice_xml_files.hindi_arr_dep_time_voice(arr_dep_time, elementsByTagName6, elementsByTagName5, "MINUTE");
        voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
        voice_xml_files.train_status_voice("NO_PLATFORM", elementsByTagName4);
        voice_xml_files.train_status_voice("WILL_COME", elementsByTagName4);
        voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
      }
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "INDEFINITE LATE", false) == 0)
    {
      voice_xml_files.train_no_voice(train_no, elementsByTagName2, elementsByTagName3, elementsByTagName7);
      voice_xml_files.train_name_voice(train_no, elementsByTagName8);
      voice_xml_files.train_status_voice(train_status, elementsByTagName4);
      voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "READY TO LEAVE", false) == 0)
    {
      voice_xml_files.train_no_voice(train_no, elementsByTagName2, elementsByTagName3, elementsByTagName7);
      voice_xml_files.train_name_voice(train_no, elementsByTagName8);
      voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
      voice_xml_files.train_status_voice("NO_PLATFORM", elementsByTagName4);
      voice_xml_files.train_status_voice(train_status, elementsByTagName4);
      voice_xml_files.train_status_voice("COMFORTABLE WISH", elementsByTagName4);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "ON PLATFORM", false) == 0)
    {
      voice_xml_files.train_no_voice(train_no, elementsByTagName2, elementsByTagName3, elementsByTagName7);
      voice_xml_files.train_name_voice(train_no, elementsByTagName8);
      voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
      voice_xml_files.train_status_voice(train_status, elementsByTagName4);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "SCHEDULED DEPARTURE", false) == 0)
    {
      voice_xml_files.train_no_voice(train_no, elementsByTagName2, elementsByTagName3, elementsByTagName7);
      voice_xml_files.train_name_voice(train_no, elementsByTagName8);
      voice_xml_files.hindi_arr_dep_time_voice(sch_dep, elementsByTagName6, elementsByTagName5, "MINUTE");
      voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
      voice_xml_files.train_status_voice("NO_PLATFORM", elementsByTagName4);
      voice_xml_files.train_status_voice(train_status, elementsByTagName4);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "REGULATED", false) == 0)
    {
      if (Conversions.ToDouble(PFno) == 0.0)
      {
        voice_xml_files.train_no_voice(train_no, elementsByTagName2, elementsByTagName3, elementsByTagName7);
        voice_xml_files.train_name_voice(train_no, elementsByTagName8);
        voice_xml_files.hindi_arr_dep_time_voice(sch_dep, elementsByTagName6, elementsByTagName5, "MINUTE");
        voice_xml_files.train_status_voice("TO DEPART", elementsByTagName4);
        voice_xml_files.hindi_arr_dep_time_voice(arr_dep_time, elementsByTagName6, elementsByTagName5, "MINUTE");
        voice_xml_files.train_status_voice("WILL LEAVE", elementsByTagName4);
        voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
      }
      else
      {
        voice_xml_files.train_no_voice(train_no, elementsByTagName2, elementsByTagName3, elementsByTagName7);
        voice_xml_files.train_name_voice(train_no, elementsByTagName8);
        voice_xml_files.hindi_arr_dep_time_voice(sch_dep, elementsByTagName6, elementsByTagName5, "MINUTE");
        voice_xml_files.train_status_voice("TO DEPART", elementsByTagName4);
        voice_xml_files.hindi_arr_dep_time_voice(arr_dep_time, elementsByTagName6, elementsByTagName5, "MINUTE");
        voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
        voice_xml_files.train_status_voice("NO_PLATFORM", elementsByTagName4);
        voice_xml_files.train_status_voice("WILL LEAVE", elementsByTagName4);
        voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
      }
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "RESCHEDULED", false) == 0)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(PFno, "0", false) == 0)
      {
        voice_xml_files.train_no_voice(train_no, elementsByTagName2, elementsByTagName3, elementsByTagName7);
        voice_xml_files.train_name_voice(train_no, elementsByTagName8);
        voice_xml_files.train_status_voice(train_status, elementsByTagName4);
        voice_xml_files.train_status_voice("NOW_THIS_TRAIN", elementsByTagName4);
        voice_xml_files.hindi_arr_dep_time_voice(arr_dep_time, elementsByTagName6, elementsByTagName5, "MINUTE");
        voice_xml_files.train_status_voice("WILL LEAVE", elementsByTagName4);
        voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
      }
      else
      {
        voice_xml_files.train_no_voice(train_no, elementsByTagName2, elementsByTagName3, elementsByTagName7);
        voice_xml_files.train_name_voice(train_no, elementsByTagName8);
        voice_xml_files.train_status_voice(train_status, elementsByTagName4);
        voice_xml_files.train_status_voice("NOW_THIS_TRAIN", elementsByTagName4);
        voice_xml_files.hindi_arr_dep_time_voice(arr_dep_time, elementsByTagName6, elementsByTagName5, "MINUTE");
        voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
        voice_xml_files.train_status_voice("NO_PLATFORM", elementsByTagName4);
        voice_xml_files.train_status_voice("WILL LEAVE", elementsByTagName4);
        voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
      }
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "CANCELLED", false) == 0)
    {
      voice_xml_files.train_no_voice(train_no, elementsByTagName2, elementsByTagName3, elementsByTagName7);
      voice_xml_files.train_name_voice(train_no, elementsByTagName8);
      voice_xml_files.train_status_voice(train_status, elementsByTagName4);
      voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "PLATFORM CHANGE", false) == 0)
    {
      if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(ad, "A", false) == 0)
      {
        voice_xml_files.train_no_voice(train_no, elementsByTagName2, elementsByTagName3, elementsByTagName7);
        voice_xml_files.train_name_voice(train_no, elementsByTagName8);
        voice_xml_files.train_status_voice("PLATFORM_CHANGE", elementsByTagName4);
        voice_xml_files.train_status_voice("NOW_THIS_TRAIN", elementsByTagName4);
        voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
        voice_xml_files.train_status_voice("NO_PLATFORM", elementsByTagName4);
        voice_xml_files.train_status_voice("WILL_COME", elementsByTagName4);
        voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
      }
      else
      {
        voice_xml_files.train_no_voice(train_no, elementsByTagName2, elementsByTagName3, elementsByTagName7);
        voice_xml_files.train_name_voice(train_no, elementsByTagName8);
        voice_xml_files.train_status_voice("PLATFORM_CHANGE", elementsByTagName4);
        voice_xml_files.train_status_voice("NOW_THIS_TRAIN", elementsByTagName4);
        voice_xml_files.platform_no_voice(PFno, elementsByTagName6, elementsByTagName7);
        voice_xml_files.train_status_voice("NO_PLATFORM", elementsByTagName4);
        voice_xml_files.train_status_voice("WILL_COME", elementsByTagName4);
        voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
      }
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "DIVERTED ROUTE", false) == 0)
    {
      voice_xml_files.train_no_voice(train_no, elementsByTagName2, elementsByTagName3, elementsByTagName7);
      voice_xml_files.train_name_voice(train_no, elementsByTagName8);
      voice_xml_files.train_status_voice("CHANGE_WAY", elementsByTagName4);
      voice_xml_files.train_status_voice("NOW_THIS_TRAIN", elementsByTagName4);
      voice_xml_files.station_name_voice(reg_station_name, elementsByTagName9);
      voice_xml_files.train_status_voice("FROM", elementsByTagName4);
      voice_xml_files.station_name_voice(station_name, elementsByTagName9);
      voice_xml_files.train_status_voice("TO", elementsByTagName4);
      voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "HAS LEFT", false) == 0)
    {
      voice_xml_files.train_no_voice(train_no, elementsByTagName2, elementsByTagName3, elementsByTagName7);
      voice_xml_files.train_name_voice(train_no, elementsByTagName8);
      voice_xml_files.train_status_voice(train_status, elementsByTagName4);
    }
    else if (Microsoft.VisualBasic.CompilerServices.Operators.CompareString(train_status, "TERMINATED", false) == 0)
    {
      voice_xml_files.train_no_voice(train_no, elementsByTagName2, elementsByTagName3, elementsByTagName7);
      voice_xml_files.train_name_voice(train_no, elementsByTagName8);
      voice_xml_files.station_name_voice(reg_station_name, elementsByTagName9);
      voice_xml_files.train_status_voice("TERMINATED2", elementsByTagName4);
      voice_xml_files.station_name_voice(station_name, elementsByTagName9);
      voice_xml_files.train_status_voice(train_status, elementsByTagName4);
      voice_xml_files.train_status_voice("INCONVENIENCE", elementsByTagName4);
    }
  }

  public static void PlayVoice(string file, AudioPlayMode waitMode)
  {
    do
      ;
    while (frmMainFormIPIS.ann_pause);
    MyProject.Computer.Audio.Play(file, waitMode);
  }
}

}