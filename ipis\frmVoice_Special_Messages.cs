// Decompiled with JetBrains decompiler
// Type: ipis.frmVoice_Special_Messages
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using ipis.My;
using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Media;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Windows.Forms;

namespace ipis
{

[DesignerGenerated]
public class frmVoice_Special_Messages : Form
{
  private static List<WeakReference> __ENCList = new List<WeakReference>();
  private IContainer components;
  [AccessedThroughProperty("dgv_voice_msg")]
  private DataGridView _dgv_voice_msg;
  [AccessedThroughProperty("btnExit")]
  private Button _btnExit;
  [AccessedThroughProperty("btnAdd")]
  private Button _btnAdd;
  [AccessedThroughProperty("btnStop")]
  private Button _btnStop;
  [AccessedThroughProperty("btnVoice")]
  private Button _btnVoice;
  [AccessedThroughProperty("row_no")]
  private DataGridViewTextBoxColumn _row_no;
  [AccessedThroughProperty("msg")]
  private DataGridViewTextBoxColumn _msg;
  [AccessedThroughProperty("chk")]
  private DataGridViewCheckBoxColumn _chk;
  [AccessedThroughProperty("spl_voice_timer")]
  private System.Windows.Forms.Timer _spl_voice_timer;
  [AccessedThroughProperty("Panel1")]
  private Panel _Panel1;
  [AccessedThroughProperty("lblVoiceSpl")]
  private Label _lblVoiceSpl;
  [AccessedThroughProperty("btnRecStop")]
  private Button _btnRecStop;
  [AccessedThroughProperty("btnPlay")]
  private Button _btnPlay;
  [AccessedThroughProperty("btnRecord")]
  private Button _btnRecord;
  [AccessedThroughProperty("Panel2")]
  private Panel _Panel2;
  [AccessedThroughProperty("Label1")]
  private Label _Label1;
  public static int[] chk_array = new int[601];
  [AccessedThroughProperty("event_spl_voice")]
  private frmAddVoiceSplMsg _event_spl_voice;
  private SoundPlayer Player;
  private Thread spl_voice_thread;
  private bool spl_voice_status;
  public static bool spl_voice_msg;
  private string announcefile;
  private Thread rec_voice_thread;
  private Process rec_play_process;
  private int id;
  public static string voice_cnt;
  public static frmVoice_Special_Messages.voice_message[] voice_var = new frmVoice_Special_Messages.voice_message[101];

  public frmVoice_Special_Messages()
  {
    this.Load += new EventHandler(this.frmVoice_Special_Messages_Load);
    frmVoice_Special_Messages.__ENCAddToList((object) this);
    this.Player = new SoundPlayer();
    this.spl_voice_status = false;
    this.announcefile = string.Empty;
    this.InitializeComponent();
  }

  [DebuggerNonUserCode]
  private static void __ENCAddToList(object value)
  {
    lock (frmVoice_Special_Messages.__ENCList)
    {
      if (frmVoice_Special_Messages.__ENCList.Count == frmVoice_Special_Messages.__ENCList.Capacity)
      {
        int index1 = 0;
        int num = checked (frmVoice_Special_Messages.__ENCList.Count - 1);
        int index2 = 0;
        while (index2 <= num)
        {
          if (frmVoice_Special_Messages.__ENCList[index2].IsAlive)
          {
            if (index2 != index1)
              frmVoice_Special_Messages.__ENCList[index1] = frmVoice_Special_Messages.__ENCList[index2];
            checked { ++index1; }
          }
          checked { ++index2; }
        }
        frmVoice_Special_Messages.__ENCList.RemoveRange(index1, checked (frmVoice_Special_Messages.__ENCList.Count - index1));
        frmVoice_Special_Messages.__ENCList.Capacity = frmVoice_Special_Messages.__ENCList.Count;
      }
      frmVoice_Special_Messages.__ENCList.Add(new WeakReference(RuntimeHelpers.GetObjectValue(value)));
    }
  }

  [DebuggerNonUserCode]
  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  [DebuggerStepThrough]
  private void InitializeComponent()
  {
    this.components = (IContainer) new System.ComponentModel.Container();
    DataGridViewCellStyle gridViewCellStyle1 = new DataGridViewCellStyle();
    DataGridViewCellStyle gridViewCellStyle2 = new DataGridViewCellStyle();
    ComponentResourceManager componentResourceManager = new ComponentResourceManager(typeof (frmVoice_Special_Messages));
    this.dgv_voice_msg = new DataGridView();
    this.row_no = new DataGridViewTextBoxColumn();
    this.msg = new DataGridViewTextBoxColumn();
    this.chk = new DataGridViewCheckBoxColumn();
    this.btnExit = new Button();
    this.btnAdd = new Button();
    this.btnStop = new Button();
    this.btnVoice = new Button();
    this.spl_voice_timer = new System.Windows.Forms.Timer(this.components);
    this.Panel1 = new Panel();
    this.lblVoiceSpl = new Label();
    this.btnRecStop = new Button();
    this.btnPlay = new Button();
    this.btnRecord = new Button();
    this.Panel2 = new Panel();
    this.Label1 = new Label();
    ((ISupportInitialize) this.dgv_voice_msg).BeginInit();
    this.Panel1.SuspendLayout();
    this.Panel2.SuspendLayout();
    this.SuspendLayout();
    this.dgv_voice_msg.AllowDrop = true;
    this.dgv_voice_msg.AllowUserToAddRows = false;
    this.dgv_voice_msg.AllowUserToDeleteRows = false;
    this.dgv_voice_msg.AllowUserToResizeColumns = false;
    this.dgv_voice_msg.AllowUserToResizeRows = false;
    this.dgv_voice_msg.BackgroundColor = SystemColors.ButtonHighlight;
    this.dgv_voice_msg.Columns.AddRange((DataGridViewColumn) this.row_no, (DataGridViewColumn) this.msg, (DataGridViewColumn) this.chk);
    DataGridView dgvVoiceMsg1 = this.dgv_voice_msg;
    Point point1 = new Point(0, 12);
    Point point2 = point1;
    dgvVoiceMsg1.Location = point2;
    this.dgv_voice_msg.Name = "dgv_voice_msg";
    gridViewCellStyle1.Alignment = DataGridViewContentAlignment.MiddleLeft;
    gridViewCellStyle1.BackColor = SystemColors.Control;
    gridViewCellStyle1.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    gridViewCellStyle1.ForeColor = SystemColors.WindowText;
    gridViewCellStyle1.SelectionBackColor = SystemColors.Highlight;
    gridViewCellStyle1.SelectionForeColor = SystemColors.HighlightText;
    gridViewCellStyle1.WrapMode = DataGridViewTriState.True;
    this.dgv_voice_msg.RowHeadersDefaultCellStyle = gridViewCellStyle1;
    gridViewCellStyle2.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.dgv_voice_msg.RowsDefaultCellStyle = gridViewCellStyle2;
    DataGridView dgvVoiceMsg2 = this.dgv_voice_msg;
    Size size1 = new Size(595, 363);
    Size size2 = size1;
    dgvVoiceMsg2.Size = size2;
    this.dgv_voice_msg.TabIndex = 5;
    this.row_no.HeaderText = "ID";
    this.row_no.Name = "row_no";
    this.msg.HeaderText = "Message";
    this.msg.Name = "msg";
    this.msg.Width = 350;
    this.chk.HeaderText = "Check";
    this.chk.Name = "chk";
    this.chk.Resizable = DataGridViewTriState.True;
    this.chk.SortMode = DataGridViewColumnSortMode.Automatic;
    this.btnExit.BackColor = SystemColors.ButtonFace;
    this.btnExit.DialogResult = DialogResult.Cancel;
    this.btnExit.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnExit1 = this.btnExit;
    point1 = new Point(478, 425);
    Point point3 = point1;
    btnExit1.Location = point3;
    this.btnExit.Name = "btnExit";
    Button btnExit2 = this.btnExit;
    size1 = new Size(76, 27);
    Size size3 = size1;
    btnExit2.Size = size3;
    this.btnExit.TabIndex = 3;
    this.btnExit.Text = "E&xit";
    this.btnExit.UseVisualStyleBackColor = false;
    this.btnAdd.BackColor = SystemColors.ButtonFace;
    this.btnAdd.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.btnAdd.ForeColor = SystemColors.ControlText;
    Button btnAdd1 = this.btnAdd;
    point1 = new Point(15, 78);
    Point point4 = point1;
    btnAdd1.Location = point4;
    this.btnAdd.Name = "btnAdd";
    Button btnAdd2 = this.btnAdd;
    size1 = new Size(153, 27);
    Size size4 = size1;
    btnAdd2.Size = size4;
    this.btnAdd.TabIndex = 4;
    this.btnAdd.Text = "&Add New Message";
    this.btnAdd.UseVisualStyleBackColor = false;
    this.btnStop.BackColor = SystemColors.ButtonFace;
    this.btnStop.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnStop1 = this.btnStop;
    point1 = new Point(93, 47);
    Point point5 = point1;
    btnStop1.Location = point5;
    this.btnStop.Name = "btnStop";
    Button btnStop2 = this.btnStop;
    size1 = new Size(73, 23);
    Size size5 = size1;
    btnStop2.Size = size5;
    this.btnStop.TabIndex = 2;
    this.btnStop.Text = "&Stop";
    this.btnStop.UseVisualStyleBackColor = false;
    this.btnVoice.BackColor = SystemColors.ButtonFace;
    this.btnVoice.BackgroundImage = (Image) componentResourceManager.GetObject("btnVoice.BackgroundImage");
    this.btnVoice.BackgroundImageLayout = ImageLayout.None;
    this.btnVoice.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.btnVoice.ForeColor = SystemColors.ControlText;
    this.btnVoice.ImageKey = "(none)";
    Button btnVoice1 = this.btnVoice;
    point1 = new Point(15, 47);
    Point point6 = point1;
    btnVoice1.Location = point6;
    this.btnVoice.Name = "btnVoice";
    Button btnVoice2 = this.btnVoice;
    size1 = new Size(59, 25);
    Size size6 = size1;
    btnVoice2.Size = size6;
    this.btnVoice.TabIndex = 1;
    this.btnVoice.Tag = (object) "";
    this.btnVoice.Text = "&Play";
    this.btnVoice.TextAlign = ContentAlignment.MiddleRight;
    this.btnVoice.UseVisualStyleBackColor = true;
    this.spl_voice_timer.Interval = 1000;
    this.Panel1.BorderStyle = BorderStyle.Fixed3D;
    this.Panel1.Controls.Add((Control) this.lblVoiceSpl);
    this.Panel1.Controls.Add((Control) this.btnVoice);
    this.Panel1.Controls.Add((Control) this.btnAdd);
    this.Panel1.Controls.Add((Control) this.btnStop);
    Panel panel1_1 = this.Panel1;
    point1 = new Point(37, 381);
    Point point7 = point1;
    panel1_1.Location = point7;
    this.Panel1.Name = "Panel1";
    Panel panel1_2 = this.Panel1;
    size1 = new Size(185, 121);
    Size size7 = size1;
    panel1_2.Size = size7;
    this.Panel1.TabIndex = 6;
    this.lblVoiceSpl.AutoSize = true;
    this.lblVoiceSpl.Font = new Font("Microsoft Sans Serif", 12f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.lblVoiceSpl.ForeColor = Color.Indigo;
    Label lblVoiceSpl1 = this.lblVoiceSpl;
    point1 = new Point(14, 12);
    Point point8 = point1;
    lblVoiceSpl1.Location = point8;
    this.lblVoiceSpl.Name = "lblVoiceSpl";
    Label lblVoiceSpl2 = this.lblVoiceSpl;
    size1 = new Size(154, 20);
    Size size8 = size1;
    lblVoiceSpl2.Size = size8;
    this.lblVoiceSpl.TabIndex = 7;
    this.lblVoiceSpl.Text = "Special Messages";
    this.btnRecStop.BackColor = SystemColors.ButtonFace;
    this.btnRecStop.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnRecStop1 = this.btnRecStop;
    point1 = new Point(112 /*0x70*/, 73);
    Point point9 = point1;
    btnRecStop1.Location = point9;
    this.btnRecStop.Name = "btnRecStop";
    Button btnRecStop2 = this.btnRecStop;
    size1 = new Size(63 /*0x3F*/, 25);
    Size size9 = size1;
    btnRecStop2.Size = size9;
    this.btnRecStop.TabIndex = 28;
    this.btnRecStop.Text = "Stop";
    this.btnRecStop.UseVisualStyleBackColor = false;
    this.btnPlay.BackColor = SystemColors.ButtonFace;
    this.btnPlay.BackgroundImage = (Image) componentResourceManager.GetObject("btnPlay.BackgroundImage");
    this.btnPlay.BackgroundImageLayout = ImageLayout.None;
    this.btnPlay.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.btnPlay.ForeColor = Color.Black;
    this.btnPlay.ImageKey = "(none)";
    Button btnPlay1 = this.btnPlay;
    point1 = new Point(13, 73);
    Point point10 = point1;
    btnPlay1.Location = point10;
    this.btnPlay.Name = "btnPlay";
    Button btnPlay2 = this.btnPlay;
    size1 = new Size(64 /*0x40*/, 25);
    Size size10 = size1;
    btnPlay2.Size = size10;
    this.btnPlay.TabIndex = 2;
    this.btnPlay.Text = "P&lay";
    this.btnPlay.TextAlign = ContentAlignment.MiddleRight;
    this.btnPlay.UseVisualStyleBackColor = true;
    this.btnRecord.BackColor = SystemColors.ButtonFace;
    this.btnRecord.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnRecord1 = this.btnRecord;
    point1 = new Point(55, 39);
    Point point11 = point1;
    btnRecord1.Location = point11;
    this.btnRecord.Name = "btnRecord";
    Button btnRecord2 = this.btnRecord;
    size1 = new Size(75, 25);
    Size size11 = size1;
    btnRecord2.Size = size11;
    this.btnRecord.TabIndex = 27;
    this.btnRecord.Text = "&Record";
    this.btnRecord.UseVisualStyleBackColor = false;
    this.Panel2.BorderStyle = BorderStyle.Fixed3D;
    this.Panel2.Controls.Add((Control) this.Label1);
    this.Panel2.Controls.Add((Control) this.btnPlay);
    this.Panel2.Controls.Add((Control) this.btnRecord);
    this.Panel2.Controls.Add((Control) this.btnRecStop);
    Panel panel2_1 = this.Panel2;
    point1 = new Point(246, 386);
    Point point12 = point1;
    panel2_1.Location = point12;
    this.Panel2.Name = "Panel2";
    Panel panel2_2 = this.Panel2;
    size1 = new Size(200, 116);
    Size size12 = size1;
    panel2_2.Size = size12;
    this.Panel2.TabIndex = 31 /*0x1F*/;
    this.Label1.AutoSize = true;
    this.Label1.Font = new Font("Microsoft Sans Serif", 12f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.Label1.ForeColor = Color.Indigo;
    Label label1_1 = this.Label1;
    point1 = new Point(9, 7);
    Point point13 = point1;
    label1_1.Location = point13;
    this.Label1.Name = "Label1";
    Label label1_2 = this.Label1;
    size1 = new Size(177, 20);
    Size size13 = size1;
    label1_2.Size = size13;
    this.Label1.TabIndex = 29;
    this.Label1.Text = "Recording Messages";
    this.AcceptButton = (IButtonControl) this.btnVoice;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.BackColor = Color.PowderBlue;
    this.CancelButton = (IButtonControl) this.btnExit;
    size1 = new Size(592, 514);
    this.ClientSize = size1;
    this.Controls.Add((Control) this.Panel2);
    this.Controls.Add((Control) this.Panel1);
    this.Controls.Add((Control) this.btnExit);
    this.Controls.Add((Control) this.dgv_voice_msg);
    this.FormBorderStyle = FormBorderStyle.FixedToolWindow;
    this.Name = "frmVoice_Special_Messages";
    this.Text = "frmVoice_Special_Messages";
    ((ISupportInitialize) this.dgv_voice_msg).EndInit();
    this.Panel1.ResumeLayout(false);
    this.Panel1.PerformLayout();
    this.Panel2.ResumeLayout(false);
    this.Panel2.PerformLayout();
    this.ResumeLayout(false);
  }

  internal virtual DataGridView dgv_voice_msg
  {
    [DebuggerNonUserCode] get { return this._dgv_voice_msg; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      DataGridViewCellEventHandler cellEventHandler = new DataGridViewCellEventHandler(this.msg_dgv_CellContentClick);
      if (this._dgv_voice_msg != null)
        this._dgv_voice_msg.CellContentClick -= cellEventHandler;
      this._dgv_voice_msg = value;
      if (this._dgv_voice_msg == null)
        return;
      this._dgv_voice_msg.CellContentClick += cellEventHandler;
    }
  }

  internal virtual Button btnExit
  {
    [DebuggerNonUserCode] get { return this._btnExit; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnExit_Click);
      if (this._btnExit != null)
        this._btnExit.Click -= eventHandler;
      this._btnExit = value;
      if (this._btnExit == null)
        return;
      this._btnExit.Click += eventHandler;
    }
  }

  internal virtual Button btnAdd
  {
    [DebuggerNonUserCode] get { return this._btnAdd; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnAdd_Click);
      if (this._btnAdd != null)
        this._btnAdd.Click -= eventHandler;
      this._btnAdd = value;
      if (this._btnAdd == null)
        return;
      this._btnAdd.Click += eventHandler;
    }
  }

  internal virtual Button btnStop
  {
    [DebuggerNonUserCode] get { return this._btnStop; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnStop_Click);
      if (this._btnStop != null)
        this._btnStop.Click -= eventHandler;
      this._btnStop = value;
      if (this._btnStop == null)
        return;
      this._btnStop.Click += eventHandler;
    }
  }

  internal virtual Button btnVoice
  {
    [DebuggerNonUserCode] get { return this._btnVoice; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnVoice_Click);
      if (this._btnVoice != null)
        this._btnVoice.Click -= eventHandler;
      this._btnVoice = value;
      if (this._btnVoice == null)
        return;
      this._btnVoice.Click += eventHandler;
    }
  }

  internal virtual DataGridViewTextBoxColumn row_no
  {
    [DebuggerNonUserCode] get { return this._row_no; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._row_no = value; }
  }

  internal virtual DataGridViewTextBoxColumn msg
  {
    [DebuggerNonUserCode] get { return this._msg; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._msg = value; }
  }

  internal virtual DataGridViewCheckBoxColumn chk
  {
    [DebuggerNonUserCode] get { return this._chk; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._chk = value; }
  }

  internal virtual System.Windows.Forms.Timer spl_voice_timer
  {
    [DebuggerNonUserCode] get { return this._spl_voice_timer; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.spl_voice_timer_Tick);
      if (this._spl_voice_timer != null)
        this._spl_voice_timer.Tick -= eventHandler;
      this._spl_voice_timer = value;
      if (this._spl_voice_timer == null)
        return;
      this._spl_voice_timer.Tick += eventHandler;
    }
  }

  internal virtual Panel Panel1
  {
    [DebuggerNonUserCode] get { return this._Panel1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Panel1 = value; }
  }

  internal virtual Label lblVoiceSpl
  {
    [DebuggerNonUserCode] get { return this._lblVoiceSpl; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblVoiceSpl = value;
    }
  }

  internal virtual Button btnRecStop
  {
    [DebuggerNonUserCode] get { return this._btnRecStop; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnRecStop_Click);
      if (this._btnRecStop != null)
        this._btnRecStop.Click -= eventHandler;
      this._btnRecStop = value;
      if (this._btnRecStop == null)
        return;
      this._btnRecStop.Click += eventHandler;
    }
  }

  internal virtual Button btnPlay
  {
    [DebuggerNonUserCode] get { return this._btnPlay; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnPlay_Click);
      if (this._btnPlay != null)
        this._btnPlay.Click -= eventHandler;
      this._btnPlay = value;
      if (this._btnPlay == null)
        return;
      this._btnPlay.Click += eventHandler;
    }
  }

  internal virtual Button btnRecord
  {
    [DebuggerNonUserCode] get { return this._btnRecord; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnRecord_Click);
      if (this._btnRecord != null)
        this._btnRecord.Click -= eventHandler;
      this._btnRecord = value;
      if (this._btnRecord == null)
        return;
      this._btnRecord.Click += eventHandler;
    }
  }

  internal virtual Panel Panel2
  {
    [DebuggerNonUserCode] get { return this._Panel2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Panel2 = value; }
  }

  internal virtual Label Label1
  {
    [DebuggerNonUserCode] get { return this._Label1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Label1 = value; }
  }

  protected virtual frmAddVoiceSplMsg event_spl_voice
  {
    [DebuggerNonUserCode] get { return this._event_spl_voice; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._event_spl_voice = value;
    }
  }

  private void msg_dgv_CellContentClick(object sender, DataGridViewCellEventArgs e)
  {
    string Left = this.dgv_voice_msg.Rows[e.RowIndex].Cells[e.ColumnIndex].EditedFormattedValue.ToString();
    try
    {
      if (e.ColumnIndex != 2)
        return;
      if (Operators.CompareString(Left, "True", false) == 0)
      {
        frmVoice_Special_Messages.chk_array[e.RowIndex] = 1;
        this.dgv_voice_msg.Rows[e.RowIndex].Cells[e.ColumnIndex].Value = (object) "True";
      }
      else if (Operators.CompareString(Left, "False", false) == 0)
      {
        frmVoice_Special_Messages.chk_array[e.RowIndex] = 0;
        this.dgv_voice_msg.Rows[e.RowIndex].Cells[e.ColumnIndex].Value = (object) "False";
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(this.Handle.ToInt32(), exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void btnExit_Click(object sender, EventArgs e)
{
  this.Close();
}

  private void frmVoice_Special_Messages_Load(object sender, EventArgs e)
  {
    this.update_voice_spl_msg_dgv();
    this.rec_play_process = new Process();
  }

  private void btnVoice_Click(object sender, EventArgs e)
  {
    int index = 0;
    bool flag = false;
    while (index < this.dgv_voice_msg.Rows.Count)
    {
      if (frmVoice_Special_Messages.chk_array[index] == 1)
        flag = true;
      checked { ++index; }
    }
    if (flag)
    {
      this.btnVoice.BackColor = Color.Green;
      this.spl_voice_thread = new Thread(new ThreadStart(this.special_voice));
      this.spl_voice_thread.Start();
    }
    else
    {
      int num = (int) basMsgBoxEx.MsgBoxMove(this.Handle.ToInt32(), "Select Special Announcement Message", "Msg Box", 0, 0, 0);
      this.btnVoice.BackColor = Color.SeaShell;
    }
  }

  public void special_voice()
  {
    int index = 0;
    try
    {
      this.spl_voice_status = true;
      network_db_read.get_language_details();
      while (index < this.dgv_voice_msg.Rows.Count)
      {
        if (frmVoice_Special_Messages.chk_array[index] == 1)
        {
          try
          {
            MyProject.Computer.Audio.Play(frmVoice_Special_Messages.voice_var[index].eng_path, AudioPlayMode.WaitToComplete);
          }
          catch (Exception ex)
          {
            ProjectData.SetProjectError(ex);
            ProjectData.ClearProjectError();
          }
          if (this.spl_voice_thread.IsAlive)
          {
            try
            {
              if (frmMainFormIPIS.language_selection.regional_language_selected)
                MyProject.Computer.Audio.Play(frmVoice_Special_Messages.voice_var[index].reg_path, AudioPlayMode.WaitToComplete);
            }
            catch (Exception ex)
            {
              ProjectData.SetProjectError(ex);
              ProjectData.ClearProjectError();
            }
          }
          try
          {
            MyProject.Computer.Audio.Play(frmVoice_Special_Messages.voice_var[index].hin_path, AudioPlayMode.WaitToComplete);
          }
          catch (Exception ex)
          {
            ProjectData.SetProjectError(ex);
            ProjectData.ClearProjectError();
          }
        }
        checked { ++index; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
    this.btnVoice.BackColor = Color.SeaShell;
    this.spl_voice_status = false;
    this.spl_voice_thread.Abort();
  }

  private void btnAdd_Click(object sender, EventArgs e)
  {
    frmVoice_Special_Messages.spl_voice_msg = false;
    this.spl_voice_timer.Start();
    if (!Information.IsNothing((object) this.event_spl_voice))
    {
      if (!this.event_spl_voice.IsDisposed)
      {
        this.event_spl_voice.WindowState = FormWindowState.Normal;
        this.event_spl_voice.BringToFront();
      }
      else
      {
        this.event_spl_voice = new frmAddVoiceSplMsg();
        this.event_spl_voice.Show();
      }
    }
    else
    {
      this.event_spl_voice = new frmAddVoiceSplMsg();
      this.event_spl_voice.Show();
    }
  }

  private void btnStop_Click(object sender, EventArgs e)
  {
    if (!(this.btnVoice.BackColor == Color.Green) || !this.spl_voice_thread.IsAlive)
      return;
    this.spl_voice_status = false;
    this.btnVoice.BackColor = Color.SeaShell;
    this.spl_voice_thread.Abort();
  }

  public void update_voice_spl_msg_dgv()
  {
    int index = 0;
    network_db_read.get_voice_msgs();
    this.dgv_voice_msg.Rows.Clear();
    try
    {
      while ((double) index < Conversions.ToDouble(frmVoice_Special_Messages.voice_cnt))
      {
        this.dgv_voice_msg.Rows.Add((object) frmVoice_Special_Messages.voice_var[index].msg_id, (object) frmVoice_Special_Messages.voice_var[index].msg_name, (object) false);
        checked { ++index; }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(this.Handle.ToInt32(), exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void spl_voice_timer_Tick(object sender, EventArgs e)
  {
    if (!frmVoice_Special_Messages.spl_voice_msg)
      return;
    this.update_voice_spl_msg_dgv();
    frmVoice_Special_Messages.spl_voice_msg = false;
    this.spl_voice_timer.Stop();
  }

  private void btnRecord_Click(object sender, EventArgs e)
  {
    Process process = new Process();
    try
    {
      string str = "c:\\ipis\\voice\\voicetest.bat";
      ProcessStartInfo processStartInfo = !File.Exists(str) ? new ProcessStartInfo("C:\\WINDOWS\\system32\\sndrec32.exe") : new ProcessStartInfo(str);
      process.StartInfo = processStartInfo;
      process.Start();
      process.WaitForExit();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void btnPlay_Click(object sender, EventArgs e)
  {
    OpenFileDialog openFileDialog = new OpenFileDialog();
    try
    {
      openFileDialog.InitialDirectory = "C:\\IPIS\\record_play_msg";
      openFileDialog.Filter = "All files (*.*)|*.*";
      openFileDialog.FilterIndex = 2;
      openFileDialog.RestoreDirectory = true;
      if (openFileDialog.ShowDialog() != DialogResult.OK)
        return;
      this.announcefile = openFileDialog.FileName;
      openFileDialog.OpenFile();
      this.rec_play_process.StartInfo.FileName = this.announcefile;
      this.rec_play_process.Start();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void btnRecStop_Click(object sender, EventArgs e)
  {
    Process[] processes = Process.GetProcesses();
    int index = 0;
    while (index < processes.Length)
    {
      Process process = processes[index];
      string processName = process.ProcessName;
      if (process.ProcessName.StartsWith("wmplayer"))
        process.Kill();
      checked { ++index; }
    }
  }

  public void rec_play()
  {
    try
    {
      this.rec_play_process.StartInfo.FileName = this.announcefile;
      this.rec_play_process.Start();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, this.announcefile + " is not a voice file", "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    this.rec_voice_thread.Abort();
  }

  public struct voice_message
  {
    public string msg_id;
    public string msg_name;
    public string eng_path;
    public string reg_path;
    public string hin_path;
  }
}

}