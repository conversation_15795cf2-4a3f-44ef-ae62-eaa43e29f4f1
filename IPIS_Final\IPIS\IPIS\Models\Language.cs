using System;

namespace IPIS.Models
{
    public class Language
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Code { get; set; }
        public string NativeName { get; set; }
        public bool IsActive { get; set; }
        public bool IsDefault { get; set; }
        public string WaveFolderPath { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }

        public Language()
        {
            IsActive = true;
            IsDefault = false;
            CreatedAt = DateTime.Now;
        }

        public override string ToString()
        {
            return $"{Name} ({Code})";
        }
    }
} 