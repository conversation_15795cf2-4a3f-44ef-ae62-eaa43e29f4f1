﻿using System;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Windows.Forms;
using Announcement.Properties;

namespace Announcement
{
	// Token: 0x0200000E RID: 14
	public partial class TrainData : Form
	{
		// Token: 0x06000072 RID: 114 RVA: 0x00011F55 File Offset: 0x00010155
		public TrainData()
		{
			this.InitializeComponent();
		}

		// Token: 0x06000073 RID: 115 RVA: 0x000025C1 File Offset: 0x000007C1
		private void label7_Click(object sender, EventArgs e)
		{
		}

		// Token: 0x06000074 RID: 116 RVA: 0x000025C1 File Offset: 0x000007C1
		private void CB_PF_SelectedIndexChanged(object sender, EventArgs e)
		{
		}

		// Token: 0x06000075 RID: 117 RVA: 0x00011F88 File Offset: 0x00010188
		private void CB_TrainNo_SelectedIndexChanged(object sender, EventArgs e)
		{
			this.TrainTable = this.DB.Read_Database("Select * From Train_Data WHERE Train_No = '" + this.CB_TrainNo.Text + "' ");
			this.TB_TrainName.Text = this.TrainTable.Rows[0]["Train_NameEng"].ToString();
			this.CB_SStation.Text = this.TrainTable.Rows[0]["Src_Stn"].ToString();
			this.CB_DStation.Text = this.TrainTable.Rows[0]["Desti_Stn"].ToString();
			this.CB_V1.Text = this.TrainTable.Rows[0]["Via1"].ToString();
			this.CB_V2.Text = this.TrainTable.Rows[0]["Via2"].ToString();
			this.CB_V3.Text = this.TrainTable.Rows[0]["Via3"].ToString();
			this.CB_V4.Text = this.TrainTable.Rows[0]["Via4"].ToString();
			this.CB_TrainAD.Text = this.TrainTable.Rows[0]["Train_AD"].ToString();
			this.CB_PF.Text = this.TrainTable.Rows[0]["Sch_PF"].ToString();
			this.CB_ATime.Text = this.TrainTable.Rows[0]["Sch_AT"].ToString();
			this.CB_DTime.Text = this.TrainTable.Rows[0]["Sch_DT"].ToString();
			this.CB_TrainType.Text = this.TrainTable.Rows[0]["Train_Type"].ToString();
			try
			{
			}
			catch
			{
			}
			bool flag = this.TrainTable.Rows[0]["All_Days"].ToString() == "True";
			if (flag)
			{
				this.CHH_All.Checked = true;
			}
			else
			{
				this.CHH_All.Checked = false;
			}
			bool flag2 = this.TrainTable.Rows[0]["Chk_Mon"].ToString() == "True";
			if (flag2)
			{
				this.CHH_Mon.Checked = true;
			}
			else
			{
				this.CHH_Mon.Checked = false;
			}
			bool flag3 = this.TrainTable.Rows[0]["Chk_Tue"].ToString() == "True";
			if (flag3)
			{
				this.CHH_Tue.Checked = true;
			}
			else
			{
				this.CHH_Tue.Checked = false;
			}
			bool flag4 = this.TrainTable.Rows[0]["Chk_Wed"].ToString() == "True";
			if (flag4)
			{
				this.CHH_Wed.Checked = true;
			}
			else
			{
				this.CHH_Wed.Checked = false;
			}
			bool flag5 = this.TrainTable.Rows[0]["Chk_Thu"].ToString() == "True";
			if (flag5)
			{
				this.CHH_Thu.Checked = true;
			}
			else
			{
				this.CHH_Thu.Checked = false;
			}
			bool flag6 = this.TrainTable.Rows[0]["Chk_Fri"].ToString() == "True";
			if (flag6)
			{
				this.CHH_Fri.Checked = true;
			}
			else
			{
				this.CHH_Fri.Checked = false;
			}
			bool flag7 = this.TrainTable.Rows[0]["Chk_Sat"].ToString() == "True";
			if (flag7)
			{
				this.CHH_Sat.Checked = true;
			}
			else
			{
				this.CHH_Sat.Checked = false;
			}
			bool flag8 = this.TrainTable.Rows[0]["Chk_Sun"].ToString() == "True";
			if (flag8)
			{
				this.CHH_Sun.Checked = true;
			}
			else
			{
				this.CHH_Sun.Checked = false;
			}
			this.GB_TD.Enabled = false;
		}

		// Token: 0x06000076 RID: 118 RVA: 0x000025C4 File Offset: 0x000007C4
		private void button2_Click(object sender, EventArgs e)
		{
			base.Close();
		}

		// Token: 0x06000077 RID: 119 RVA: 0x00012450 File Offset: 0x00010650
		private void BTN_Save_Click(object sender, EventArgs e)
		{
			DataTable dataTable = new DataTable();
			dataTable = this.DB.Read_Database("SELECT * FROM Train_Data WHERE Train_No = '" + this.CB_TrainNo.Text + "'");
			bool flag = dataTable.Rows.Count > 0 && !this.Flag_Edit;
			if (flag)
			{
				MessageBox.Show("Train Already Exsist in Database");
			}
			else
			{
				bool flag2 = this.CB_TrainNo.Text == "";
				if (flag2)
				{
					MessageBox.Show("Enter Valid Train No Details");
				}
				else
				{
					bool flag3 = this.CB_SStation.Text == "";
					if (flag3)
					{
						MessageBox.Show("Enter Valid Source Station");
					}
					else
					{
						bool flag4 = this.CB_DStation.Text == "";
						if (flag4)
						{
							MessageBox.Show("Enter Valid Destination Station");
						}
						else
						{
							try
							{
								bool flag_Edit = this.Flag_Edit;
								if (flag_Edit)
								{
									this.Flag_Edit = false;
									this.DB.Insert_Database(string.Concat(new string[]
									{
										"UPDATE Train_Data SET Train_NameEng = '",
										this.TB_TrainName.Text,
										"',Train_Type = '",
										this.CB_TrainType.Text,
										"',Train_AD = '",
										this.CB_TrainAD.Text,
										"', WHERE Train_No = '",
										this.CB_TrainNo.Text,
										"'"
									}));
								}
								else
								{
									this.DB.Insert_Database(string.Concat(new string[]
									{
										"INSERT INTO Train_Data (Train_No,Train_NameEng,Train_Type,Train_AD,Sch_AT,Sch_DT,Sch_PF) VALUES('",
										this.CB_TrainNo.Text,
										"','",
										this.TB_TrainName.Text,
										"','",
										this.CB_TrainType.Text,
										"','",
										this.CB_TrainAD.Text,
										"','",
										this.CB_ATime.Text,
										"','",
										this.CB_DTime.Text,
										"','",
										this.CB_PF.Text,
										"')"
									}));
									this.CB_TrainNo.Items.Add(this.CB_TrainNo.Text);
								}
								this.DB.Insert_Database(string.Concat(new string[]
								{
									"UPDATE Train_Data SET Src_Stn = '",
									this.CB_SStation.Text,
									"',Desti_Stn = '",
									this.CB_DStation.Text,
									"',Via1 = '",
									this.CB_V1.Text,
									"',Via2 = '",
									this.CB_V2.Text,
									"',Via3 = '",
									this.CB_V3.Text,
									"',Via4='",
									this.CB_V4.Text,
									"' WHERE Train_No = '",
									this.CB_TrainNo.Text,
									"'"
								}));
								this.DB.Insert_Database(string.Concat(new string[]
								{
									"UPDATE Train_Data SET Sch_AT = '",
									this.CB_ATime.Text,
									"', Sch_DT = '",
									this.CB_DTime.Text,
									"', Sch_PF = '",
									this.CB_PF.Text,
									"',Train_NameEng = '",
									this.TB_TrainName.Text,
									"',Train_Type = '",
									this.CB_TrainType.Text,
									"',Train_AD = '",
									this.CB_TrainAD.Text,
									"' WHERE Train_No = '",
									this.CB_TrainNo.Text,
									"'"
								}));
								this.DB.Insert_Database(string.Concat(new string[]
								{
									"UPDATE Train_Data SET All_Days = '",
									this.CHH_All.Checked.ToString(),
									"',Chk_Mon = '",
									this.CHH_Mon.Checked.ToString(),
									"',Chk_Tue = '",
									this.CHH_Tue.Checked.ToString(),
									"',Chk_Wed = '",
									this.CHH_Wed.Checked.ToString(),
									"',Chk_Thu = '",
									this.CHH_Thu.Checked.ToString(),
									"',Chk_Fri = '",
									this.CHH_Fri.Checked.ToString(),
									"',Chk_Sat = '",
									this.CHH_Sat.Checked.ToString(),
									"',Chk_Sun = '",
									this.CHH_Sun.Checked.ToString(),
									"' WHERE Train_No = '",
									this.CB_TrainNo.Text,
									"'"
								}));
								MessageBox.Show("Data Saved Sucessfully");
							}
							catch (Exception ex)
							{
								MessageBox.Show("Unable to Save Data Try Again...");
							}
							this.GB_TD.Enabled = false;
							this.GB_Via.Enabled = false;
							this.GB_RD.Enabled = false;
							this.BTN_Edit.Enabled = true;
							this.BTN_Save.Enabled = false;
							this.BTN_Add.Enabled = true;
						}
					}
				}
			}
		}

		// Token: 0x06000078 RID: 120 RVA: 0x000129F4 File Offset: 0x00010BF4
		private void BTN_Add_Click(object sender, EventArgs e)
		{
			this.CB_TrainNo.Text = "";
			this.StnTable = new DataTable();
			this.StnTable = this.DB.Read_Database("Select * From Station_Code");
			this.StnTable = new DataTable();
			this.StnTable = this.DB.Read_Database("Select * From Station_Code");
			this.CB_SStation.Items.Clear();
			this.CB_DStation.Items.Clear();
			this.CB_V1.Items.Clear();
			this.CB_V2.Items.Clear();
			this.CB_V3.Items.Clear();
			this.CB_V4.Items.Clear();
			bool flag = this.StnTable.Rows.Count > 0;
			if (flag)
			{
				for (int i = 0; i < this.StnTable.Rows.Count; i++)
				{
					bool flag2 = (string)this.StnTable.Rows[i]["Stn_Code"] != null;
					if (flag2)
					{
						this.CB_SStation.Items.Add(this.StnTable.Rows[i]["Stn_Code"].ToString());
						this.CB_DStation.Items.Add(this.StnTable.Rows[i]["Stn_Code"].ToString());
						this.CB_V1.Items.Add(this.StnTable.Rows[i]["Stn_Code"].ToString());
						this.CB_V2.Items.Add(this.StnTable.Rows[i]["Stn_Code"].ToString());
						this.CB_V3.Items.Add(this.StnTable.Rows[i]["Stn_Code"].ToString());
						this.CB_V4.Items.Add(this.StnTable.Rows[i]["Stn_Code"].ToString());
					}
				}
				this.CB_SStation.Text = this.StnTable.Rows[0]["Stn_Code"].ToString();
				this.CB_DStation.Text = this.StnTable.Rows[0]["Stn_Code"].ToString();
				this.CB_V1.Text = this.StnTable.Rows[0]["Stn_Code"].ToString();
				this.CB_V2.Text = this.StnTable.Rows[0]["Stn_Code"].ToString();
				this.CB_V3.Text = this.StnTable.Rows[0]["Stn_Code"].ToString();
				this.CB_V4.Text = this.StnTable.Rows[0]["Stn_Code"].ToString();
			}
			this.BTN_Save.Enabled = true;
			this.BTN_Edit.Enabled = false;
			this.GB_TD.Enabled = true;
			this.GB_RD.Enabled = true;
			this.GB_Via.Enabled = true;
			this.TB_TrainName.Text = "";
			this.CB_TrainType.Text = "";
			this.CB_SStation.Text = "";
			this.CB_DStation.Text = "";
			this.CB_TrainAD.Text = "";
			this.CB_ATime.Text = "";
			this.CB_DTime.Text = "";
			this.CB_PF.Text = "";
			this.CB_V1.Text = "";
			this.CB_V2.Text = "";
			this.CB_V3.Text = "";
			this.CB_V4.Text = "";
		}

		// Token: 0x06000079 RID: 121 RVA: 0x00012E6C File Offset: 0x0001106C
		private void BTN_Edit_Click(object sender, EventArgs e)
		{
			this.Flag_Edit = true;
			this.BTN_Save.Enabled = true;
			this.BTN_Add.Enabled = false;
			this.GB_TD.Enabled = true;
			this.GB_RD.Enabled = true;
			this.GB_Via.Enabled = true;
			this.BTN_Edit.Enabled = true;
			this.StnTable = new DataTable();
			this.StnTable = this.DB.Read_Database("Select * From Station_Code");
			this.StnTable = new DataTable();
			this.StnTable = this.DB.Read_Database("Select * From Station_Code");
			this.CB_SStation.Items.Clear();
			this.CB_DStation.Items.Clear();
			this.CB_V1.Items.Clear();
			this.CB_V2.Items.Clear();
			this.CB_V3.Items.Clear();
			this.CB_V4.Items.Clear();
			bool flag = this.StnTable.Rows.Count > 0;
			if (flag)
			{
				for (int i = 0; i < this.StnTable.Rows.Count; i++)
				{
					bool flag2 = (string)this.StnTable.Rows[i]["Stn_Code"] != null;
					if (flag2)
					{
						this.CB_SStation.Items.Add(this.StnTable.Rows[i]["Stn_Code"].ToString());
						this.CB_DStation.Items.Add(this.StnTable.Rows[i]["Stn_Code"].ToString());
						this.CB_V1.Items.Add(this.StnTable.Rows[i]["Stn_Code"].ToString());
						this.CB_V2.Items.Add(this.StnTable.Rows[i]["Stn_Code"].ToString());
						this.CB_V3.Items.Add(this.StnTable.Rows[i]["Stn_Code"].ToString());
						this.CB_V4.Items.Add(this.StnTable.Rows[i]["Stn_Code"].ToString());
					}
				}
			}
		}

		// Token: 0x0600007A RID: 122 RVA: 0x00013114 File Offset: 0x00011314
		private void TrainData_Load(object sender, EventArgs e)
		{
			this.StnTable = new DataTable();
			this.StnTable = this.DB.Read_Database("Select * From Station_Codes");
			this.CB_SStation.Items.Clear();
			this.CB_DStation.Items.Clear();
			this.CB_V1.Items.Clear();
			this.CB_V2.Items.Clear();
			this.CB_V3.Items.Clear();
			this.CB_V4.Items.Clear();
			bool flag = this.StnTable.Rows.Count > 0;
			if (flag)
			{
				this.GP_Counter = 0;
				while (this.GP_Counter < this.StnTable.Rows.Count)
				{
					bool flag2 = (string)this.StnTable.Rows[this.GP_Counter]["Station_Code"] != null;
					if (flag2)
					{
						this.CB_SStation.Items.Add(this.StnTable.Rows[this.GP_Counter]["Stn_Code"].ToString());
						this.CB_DStation.Items.Add(this.StnTable.Rows[this.GP_Counter]["Stn_Code"].ToString());
						this.CB_V1.Items.Add(this.StnTable.Rows[this.GP_Counter]["Stn_Code"].ToString());
						this.CB_V2.Items.Add(this.StnTable.Rows[this.GP_Counter]["Stn_Code"].ToString());
						this.CB_V3.Items.Add(this.StnTable.Rows[this.GP_Counter]["Stn_Code"].ToString());
						this.CB_V4.Items.Add(this.StnTable.Rows[this.GP_Counter]["Stn_Code"].ToString());
					}
					this.GP_Counter++;
				}
				this.CB_SStation.Text = this.StnTable.Rows[0]["Station_Code"].ToString();
				this.CB_DStation.Text = this.StnTable.Rows[0]["Station_Code"].ToString();
				this.CB_V1.Items.Add("");
				this.CB_V2.Items.Add("");
				this.CB_V3.Items.Add("");
				this.CB_V4.Items.Add("");
				this.CB_V1.Text = "";
				this.CB_V2.Text = "";
				this.CB_V3.Text = "";
				this.CB_V4.Text = "";
			}
			this.TrainTable = new DataTable();
			this.TrainTable = this.DB.Read_Database("Select * From Train_Data ORDER BY Train_No");
			this.CB_PF.Items.Clear();
			ComboBox.ObjectCollection items = this.CB_PF.Items;
			object[] pf_Names = Main.PF_Names;
			items.AddRange(pf_Names);
			bool flag3 = this.TrainTable.Rows.Count > 0;
			if (flag3)
			{
				this.GP_Counter = 0;
				while (this.GP_Counter < this.TrainTable.Rows.Count)
				{
					this.CB_TrainNo.Items.Add(this.TrainTable.Rows[this.GP_Counter]["Train_No"].ToString());
					this.CB_TrainNo.AutoCompleteCustomSource.Add(this.TrainTable.Rows[this.GP_Counter]["Train_No"].ToString());
					this.GP_Counter++;
				}
				this.CB_TrainNo.Text = this.TrainTable.Rows[0]["Train_No"].ToString();
			}
		}

		// Token: 0x0600007B RID: 123 RVA: 0x000135A6 File Offset: 0x000117A6
		private void Btn_Select_Click(object sender, EventArgs e)
		{
			Main.SelectedTrain = this.CB_TrainNo.Text;
			base.Close();
		}

		// Token: 0x0600007C RID: 124 RVA: 0x000025C1 File Offset: 0x000007C1
		private void CB_SStation_SelectedIndexChanged(object sender, EventArgs e)
		{
		}

		// Token: 0x0600007D RID: 125 RVA: 0x000135C0 File Offset: 0x000117C0
		private void BTN_Del_Click(object sender, EventArgs e)
		{
			try
			{
				this.DB.Insert_Database("DELETE * FROM Train_Data WHERE Train_No = '" + this.CB_TrainNo.Text + "'");
				MessageBox.Show("Train " + this.CB_TrainNo.Text + " Deleted sucessfully");
				this.CB_TrainNo.Items.Remove(this.CB_TrainNo.Text);
				this.CB_TrainNo.Text = "";
				this.TB_TrainName.Text = "";
				this.CB_TrainType.Text = "";
				this.CB_V1.Text = "";
				this.CB_V2.Text = "";
				this.CB_V3.Text = "";
				this.CB_V4.Text = "";
				this.CB_PF.Text = "";
				this.CB_SStation.Text = "";
				this.CB_DStation.Text = "";
				this.CB_ATime.Text = "00:00";
				this.CB_DTime.Text = "00:00";
				this.CB_TrainAD.Text = "";
			}
			catch (Exception ex)
			{
				MessageBox.Show("Unable to Delete Train " + this.CB_TrainNo.Text);
			}
		}

		// Token: 0x040000B5 RID: 181
		private Class_Database DB = new Class_Database();

		// Token: 0x040000B6 RID: 182
		private DataTable TrainTable;

		// Token: 0x040000B7 RID: 183
		private DataTable StnTable;

		// Token: 0x040000B8 RID: 184
		private bool Flag_Edit = false;

		// Token: 0x040000B9 RID: 185
		private bool Flag_New = false;

		// Token: 0x040000BA RID: 186
		private int GP_Counter;
	}
}
