using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using IPIS.Models;
using IPIS.Services;
using IPIS.Utils;

namespace IPIS.Forms.Settings
{
    // Remove the main management UI from LanguageForm, keep only LanguageAddEditForm for add/edit dialogs.
    public partial class LanguageManagementControl : UserControl
    {
        // ... existing code ...
    }

    public class LanguageAddEditForm : Form
    {
        private readonly LanguageService _languageService;
        private Language _currentLanguage;
        private bool _isEditMode;

        private Label lblName;
        private TextBox txtName;
        private Label lblCode;
        private TextBox txtCode;
        private Label lblNativeName;
        private TextBox txtNativeName;
        private Label lblWaveFolder;
        private TextBox txtWaveFolder;
        private CheckBox chkIsActive;
        private CheckBox chkIsDefault;
        private Button btnSave;
        private Button btnCancel;
        private Button btnBrowse;

        public LanguageAddEditForm(LanguageService languageService, Language language = null)
        {
            _languageService = languageService;
            _currentLanguage = language;
            _isEditMode = language != null;
            InitializeComponent();
            LoadLanguageData();
        }

        private void InitializeComponent()
        {
            this.lblName = new Label();
            this.txtName = new TextBox();
            this.lblCode = new Label();
            this.txtCode = new TextBox();
            this.lblNativeName = new Label();
            this.txtNativeName = new TextBox();
            this.lblWaveFolder = new Label();
            this.txtWaveFolder = new TextBox();
            this.chkIsActive = new CheckBox();
            this.chkIsDefault = new CheckBox();
            this.btnSave = new Button();
            this.btnCancel = new Button();
            this.btnBrowse = new Button();

            // Form
            this.ClientSize = new Size(450, 350);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.StartPosition = FormStartPosition.CenterParent;
            this.Text = _isEditMode ? "Edit Language" : "Add Language";

            // Name Label
            this.lblName.AutoSize = true;
            this.lblName.Location = new Point(20, 20);
            this.lblName.Size = new Size(80, 20);
            this.lblName.Text = "Name:";
            this.lblName.Font = new Font("Segoe UI", 9F, FontStyle.Regular);

            // Name TextBox
            this.txtName.Location = new Point(120, 20);
            this.txtName.Size = new Size(250, 25);
            this.txtName.Font = new Font("Segoe UI", 9F, FontStyle.Regular);

            // Code Label
            this.lblCode.AutoSize = true;
            this.lblCode.Location = new Point(20, 60);
            this.lblCode.Size = new Size(80, 20);
            this.lblCode.Text = "Code:";
            this.lblCode.Font = new Font("Segoe UI", 9F, FontStyle.Regular);

            // Code TextBox
            this.txtCode.Location = new Point(120, 60);
            this.txtCode.Size = new Size(100, 25);
            this.txtCode.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
            this.txtCode.MaxLength = 10;

            // Native Name Label
            this.lblNativeName.AutoSize = true;
            this.lblNativeName.Location = new Point(20, 100);
            this.lblNativeName.Size = new Size(80, 20);
            this.lblNativeName.Text = "Native Name:";
            this.lblNativeName.Font = new Font("Segoe UI", 9F, FontStyle.Regular);

            // Native Name TextBox
            this.txtNativeName.Location = new Point(120, 100);
            this.txtNativeName.Size = new Size(250, 25);
            this.txtNativeName.Font = new Font("Segoe UI", 9F, FontStyle.Regular);

            // Wave Folder Label
            this.lblWaveFolder.AutoSize = true;
            this.lblWaveFolder.Location = new Point(20, 140);
            this.lblWaveFolder.Size = new Size(80, 20);
            this.lblWaveFolder.Text = "Wave Folder:";
            this.lblWaveFolder.Font = new Font("Segoe UI", 9F, FontStyle.Regular);

            // Wave Folder TextBox
            this.txtWaveFolder.Location = new Point(120, 140);
            this.txtWaveFolder.Size = new Size(200, 25);
            this.txtWaveFolder.Font = new Font("Segoe UI", 9F, FontStyle.Regular);

            // Browse Button
            this.btnBrowse.Location = new Point(330, 140);
            this.btnBrowse.Size = new Size(40, 25);
            this.btnBrowse.Text = "...";
            this.btnBrowse.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
            this.btnBrowse.Click += new EventHandler(this.btnBrowse_Click);

            // Is Active CheckBox
            this.chkIsActive.AutoSize = true;
            this.chkIsActive.Location = new Point(120, 180);
            this.chkIsActive.Size = new Size(100, 20);
            this.chkIsActive.Text = "Is Active";
            this.chkIsActive.Font = new Font("Segoe UI", 9F, FontStyle.Regular);

            // Is Default CheckBox
            this.chkIsDefault.AutoSize = true;
            this.chkIsDefault.Location = new Point(120, 210);
            this.chkIsDefault.Size = new Size(100, 20);
            this.chkIsDefault.Text = "Is Default";
            this.chkIsDefault.Font = new Font("Segoe UI", 9F, FontStyle.Regular);

            // Save Button
            this.btnSave.Location = new Point(120, 260);
            this.btnSave.Size = new Size(80, 30);
            this.btnSave.Text = "Save";
            this.btnSave.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
            this.btnSave.Click += new EventHandler(this.btnSave_Click);

            // Cancel Button
            this.btnCancel.Location = new Point(220, 260);
            this.btnCancel.Size = new Size(80, 30);
            this.btnCancel.Text = "Cancel";
            this.btnCancel.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
            this.btnCancel.Click += new EventHandler(this.btnCancel_Click);

            // Add controls to form
            this.Controls.AddRange(new Control[] {
                this.lblName, this.txtName,
                this.lblCode, this.txtCode,
                this.lblNativeName, this.txtNativeName,
                this.lblWaveFolder, this.txtWaveFolder, this.btnBrowse,
                this.chkIsActive, this.chkIsDefault,
                this.btnSave, this.btnCancel
            });
        }

        private void LoadLanguageData()
        {
            if (_isEditMode && _currentLanguage != null)
            {
                txtName.Text = _currentLanguage.Name;
                txtCode.Text = _currentLanguage.Code;
                txtNativeName.Text = _currentLanguage.NativeName;
                txtWaveFolder.Text = _currentLanguage.WaveFolderPath;
                chkIsActive.Checked = _currentLanguage.IsActive;
                chkIsDefault.Checked = _currentLanguage.IsDefault;
                txtCode.Enabled = false;
            }
            else
            {
                chkIsActive.Checked = true;
                chkIsDefault.Checked = false;
            }
        }

        private async void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (ValidateInput())
                {
                    if (_isEditMode)
                    {
                        _currentLanguage.Name = txtName.Text.Trim();
                        _currentLanguage.NativeName = txtNativeName.Text.Trim();
                        _currentLanguage.WaveFolderPath = txtWaveFolder.Text.Trim();
                        _currentLanguage.IsActive = chkIsActive.Checked;
                        _currentLanguage.IsDefault = chkIsDefault.Checked;
                        await _languageService.UpdateLanguageAsync(_currentLanguage);
                        MessageBox.Show("Language updated successfully!", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    else
                    {
                        var newLanguage = new Language
                        {
                            Name = txtName.Text.Trim(),
                            Code = txtCode.Text.Trim().ToUpper(),
                            NativeName = txtNativeName.Text.Trim(),
                            WaveFolderPath = txtWaveFolder.Text.Trim(),
                            IsActive = chkIsActive.Checked,
                            IsDefault = chkIsDefault.Checked
                        };
                        await _languageService.AddLanguageAsync(newLanguage);
                        MessageBox.Show("Language added successfully!", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void btnBrowse_Click(object sender, EventArgs e)
        {
            using (var folderDialog = new FolderBrowserDialog())
            {
                folderDialog.Description = "Select Wave Files Folder";
                folderDialog.SelectedPath = Application.StartupPath + "\\data\\WAVE";
                if (folderDialog.ShowDialog() == DialogResult.OK)
                {
                    txtWaveFolder.Text = folderDialog.SelectedPath;
                }
            }
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(txtName.Text))
            {
                MessageBox.Show("Please enter a language name.", "Validation Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtName.Focus();
                return false;
            }
            if (string.IsNullOrWhiteSpace(txtCode.Text))
            {
                MessageBox.Show("Please enter a language code.", "Validation Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtCode.Focus();
                return false;
            }
            if (txtCode.Text.Length < 2)
            {
                MessageBox.Show("Language code must be at least 2 characters long.", "Validation Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtCode.Focus();
                return false;
            }
            return true;
        }
    }
} 