// Decompiled with JetBrains decompiler
// Type: ipis.frmDisplayBoardSettings
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

namespace ipis
{

[DesignerGenerated]
public class frmDisplayBoardSettings : Form
{
  private static List<WeakReference> __ENCList = new List<WeakReference>();
  private IContainer components;
  [AccessedThroughProperty("Label5")]
  private Label _Label5;
  [AccessedThroughProperty("radPdb")]
  private RadioButton _radPdb;
  [AccessedThroughProperty("radMldb")]
  private RadioButton _radMldb;
  [AccessedThroughProperty("radCgdb")]
  private RadioButton _radCgdb;
  [AccessedThroughProperty("radAgdb")]
  private RadioButton _radAgdb;
  [AccessedThroughProperty("Panel1")]
  private Panel _Panel1;
  [AccessedThroughProperty("cmbName")]
  private ComboBox _cmbName;
  [AccessedThroughProperty("cmbVideo")]
  private ComboBox _cmbVideo;
  [AccessedThroughProperty("lblVideo")]
  private Label _lblVideo;
  [AccessedThroughProperty("txtType")]
  private TextBox _txtType;
  [AccessedThroughProperty("btnExit")]
  private Button _btnExit;
  [AccessedThroughProperty("cmbAddress")]
  private ComboBox _cmbAddress;
  [AccessedThroughProperty("lblType")]
  private Label _lblType;
  [AccessedThroughProperty("lblLine5")]
  private Label _lblLine5;
  [AccessedThroughProperty("lblLine4")]
  private Label _lblLine4;
  [AccessedThroughProperty("lblLine3")]
  private Label _lblLine3;
  [AccessedThroughProperty("lblLine2")]
  private Label _lblLine2;
  [AccessedThroughProperty("lblLine1")]
  private Label _lblLine1;
  [AccessedThroughProperty("lblDisEff")]
  private Label _lblDisEff;
  [AccessedThroughProperty("cmbEffect5")]
  private ComboBox _cmbEffect5;
  [AccessedThroughProperty("cmbEffect4")]
  private ComboBox _cmbEffect4;
  [AccessedThroughProperty("cmbEffect3")]
  private ComboBox _cmbEffect3;
  [AccessedThroughProperty("cmbEffect2")]
  private ComboBox _cmbEffect2;
  [AccessedThroughProperty("cmbEffect1")]
  private ComboBox _cmbEffect1;
  [AccessedThroughProperty("txtNoLines")]
  private TextBox _txtNoLines;
  [AccessedThroughProperty("lblNoLines")]
  private Label _lblNoLines;
  [AccessedThroughProperty("lblAddress")]
  private Label _lblAddress;
  [AccessedThroughProperty("lblName")]
  private Label _lblName;
  [AccessedThroughProperty("cmbPfno")]
  private ComboBox _cmbPfno;
  [AccessedThroughProperty("lblPfno")]
  private Label _lblPfno;
  [AccessedThroughProperty("radFlase")]
  private RadioButton _radFlase;
  [AccessedThroughProperty("chkAllPfno")]
  private CheckBox _chkAllPfno;
  [AccessedThroughProperty("lblAllPfno")]
  private Label _lblAllPfno;
  [AccessedThroughProperty("txtSharedPfno")]
  private TextBox _txtSharedPfno;
  [AccessedThroughProperty("lblSharedPfno")]
  private Label _lblSharedPfno;
  [AccessedThroughProperty("txtPDCHAddr")]
  private TextBox _txtPDCHAddr;
  [AccessedThroughProperty("txtPDCHName")]
  private TextBox _txtPDCHName;
  [AccessedThroughProperty("lblCgdb")]
  private Label _lblCgdb;
  [AccessedThroughProperty("lblPdch")]
  private Label _lblPdch;
  [AccessedThroughProperty("txtDir")]
  private TextBox _txtDir;
  [AccessedThroughProperty("lbldir")]
  private Label _lbldir;
  [AccessedThroughProperty("lblEffectSpeed")]
  private Label _lblEffectSpeed;
  [AccessedThroughProperty("lblMs")]
  private Label _lblMs;
  [AccessedThroughProperty("numEffectSpeed")]
  private NumericUpDown _numEffectSpeed;
  [AccessedThroughProperty("btnOk")]
  private Button _btnOk;
  private static string platform_no;
  private static byte[] cgdb_addrs = new byte[27];
  private static byte cgdb_cnt;

  [DebuggerNonUserCode]
  public frmDisplayBoardSettings()
  {
    this.Load += new EventHandler(this.frmDisplayBoardSettings_Load);
    frmDisplayBoardSettings.__ENCAddToList((object) this);
    this.InitializeComponent();
  }

  [DebuggerNonUserCode]
  private static void __ENCAddToList(object value)
  {
    lock (frmDisplayBoardSettings.__ENCList)
    {
      if (frmDisplayBoardSettings.__ENCList.Count == frmDisplayBoardSettings.__ENCList.Capacity)
      {
        int index1 = 0;
        int num = checked (frmDisplayBoardSettings.__ENCList.Count - 1);
        int index2 = 0;
        while (index2 <= num)
        {
          if (frmDisplayBoardSettings.__ENCList[index2].IsAlive)
          {
            if (index2 != index1)
              frmDisplayBoardSettings.__ENCList[index1] = frmDisplayBoardSettings.__ENCList[index2];
            checked { ++index1; }
          }
          checked { ++index2; }
        }
        frmDisplayBoardSettings.__ENCList.RemoveRange(index1, checked (frmDisplayBoardSettings.__ENCList.Count - index1));
        frmDisplayBoardSettings.__ENCList.Capacity = frmDisplayBoardSettings.__ENCList.Count;
      }
      frmDisplayBoardSettings.__ENCList.Add(new WeakReference(RuntimeHelpers.GetObjectValue(value)));
    }
  }

  [DebuggerNonUserCode]
  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  [DebuggerStepThrough]
  private void InitializeComponent()
  {
    this.Label5 = new Label();
    this.radPdb = new RadioButton();
    this.radMldb = new RadioButton();
    this.radCgdb = new RadioButton();
    this.radAgdb = new RadioButton();
    this.Panel1 = new Panel();
    this.lblMs = new Label();
    this.numEffectSpeed = new NumericUpDown();
    this.lblEffectSpeed = new Label();
    this.txtDir = new TextBox();
    this.lbldir = new Label();
    this.lblPdch = new Label();
    this.lblCgdb = new Label();
    this.txtPDCHAddr = new TextBox();
    this.txtPDCHName = new TextBox();
    this.chkAllPfno = new CheckBox();
    this.lblAllPfno = new Label();
    this.txtSharedPfno = new TextBox();
    this.lblSharedPfno = new Label();
    this.cmbPfno = new ComboBox();
    this.lblPfno = new Label();
    this.cmbName = new ComboBox();
    this.cmbVideo = new ComboBox();
    this.lblVideo = new Label();
    this.txtType = new TextBox();
    this.cmbAddress = new ComboBox();
    this.lblType = new Label();
    this.lblLine5 = new Label();
    this.lblLine4 = new Label();
    this.lblLine3 = new Label();
    this.lblLine2 = new Label();
    this.lblLine1 = new Label();
    this.lblDisEff = new Label();
    this.cmbEffect5 = new ComboBox();
    this.cmbEffect4 = new ComboBox();
    this.cmbEffect3 = new ComboBox();
    this.cmbEffect2 = new ComboBox();
    this.cmbEffect1 = new ComboBox();
    this.txtNoLines = new TextBox();
    this.lblNoLines = new Label();
    this.lblAddress = new Label();
    this.lblName = new Label();
    this.btnExit = new Button();
    this.radFlase = new RadioButton();
    this.btnOk = new Button();
    this.Panel1.SuspendLayout();
    this.numEffectSpeed.BeginInit();
    this.SuspendLayout();
    this.Label5.AutoSize = true;
    this.Label5.Font = new Font("Microsoft Sans Serif", 14.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.Label5.ForeColor = Color.Red;
    Label label5_1 = this.Label5;
    Point point1 = new Point(12, 19);
    Point point2 = point1;
    label5_1.Location = point2;
    this.Label5.Name = "Label5";
    Label label5_2 = this.Label5;
    Size size1 = new Size(138, 24);
    Size size2 = size1;
    label5_2.Size = size2;
    this.Label5.TabIndex = 18;
    this.Label5.Text = "Display Board";
    this.radPdb.AutoSize = true;
    this.radPdb.Font = new Font("Arial", 14.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    RadioButton radPdb1 = this.radPdb;
    point1 = new Point(43, 399);
    Point point3 = point1;
    radPdb1.Location = point3;
    this.radPdb.Name = "radPdb";
    RadioButton radPdb2 = this.radPdb;
    size1 = new Size(69, 26);
    Size size3 = size1;
    radPdb2.Size = size3;
    this.radPdb.TabIndex = 4;
    this.radPdb.Text = "PDB";
    this.radPdb.UseVisualStyleBackColor = true;
    this.radMldb.AutoSize = true;
    this.radMldb.Font = new Font("Arial", 14.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    RadioButton radMldb1 = this.radMldb;
    point1 = new Point(43, 300);
    Point point4 = point1;
    radMldb1.Location = point4;
    this.radMldb.Name = "radMldb";
    RadioButton radMldb2 = this.radMldb;
    size1 = new Size(85, 26);
    Size size4 = size1;
    radMldb2.Size = size4;
    this.radMldb.TabIndex = 3;
    this.radMldb.Text = "MLDB";
    this.radMldb.UseVisualStyleBackColor = true;
    this.radCgdb.AutoSize = true;
    this.radCgdb.Font = new Font("Arial", 14.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    RadioButton radCgdb1 = this.radCgdb;
    point1 = new Point(43, 210);
    Point point5 = point1;
    radCgdb1.Location = point5;
    this.radCgdb.Name = "radCgdb";
    RadioButton radCgdb2 = this.radCgdb;
    size1 = new Size(85, 26);
    Size size5 = size1;
    radCgdb2.Size = size5;
    this.radCgdb.TabIndex = 2;
    this.radCgdb.Text = "CGDB";
    this.radCgdb.UseVisualStyleBackColor = true;
    this.radAgdb.AutoSize = true;
    this.radAgdb.Font = new Font("Arial", 14.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    RadioButton radAgdb1 = this.radAgdb;
    point1 = new Point(43, 120);
    Point point6 = point1;
    radAgdb1.Location = point6;
    this.radAgdb.Name = "radAgdb";
    RadioButton radAgdb2 = this.radAgdb;
    size1 = new Size(84, 26);
    Size size6 = size1;
    radAgdb2.Size = size6;
    this.radAgdb.TabIndex = 1;
    this.radAgdb.Text = "AGDB";
    this.radAgdb.UseVisualStyleBackColor = true;
    this.Panel1.BackColor = SystemColors.ButtonHighlight;
    this.Panel1.BorderStyle = BorderStyle.Fixed3D;
    this.Panel1.Controls.Add((Control) this.btnOk);
    this.Panel1.Controls.Add((Control) this.lblMs);
    this.Panel1.Controls.Add((Control) this.numEffectSpeed);
    this.Panel1.Controls.Add((Control) this.lblEffectSpeed);
    this.Panel1.Controls.Add((Control) this.txtDir);
    this.Panel1.Controls.Add((Control) this.lbldir);
    this.Panel1.Controls.Add((Control) this.lblPdch);
    this.Panel1.Controls.Add((Control) this.lblCgdb);
    this.Panel1.Controls.Add((Control) this.txtPDCHAddr);
    this.Panel1.Controls.Add((Control) this.txtPDCHName);
    this.Panel1.Controls.Add((Control) this.chkAllPfno);
    this.Panel1.Controls.Add((Control) this.lblAllPfno);
    this.Panel1.Controls.Add((Control) this.txtSharedPfno);
    this.Panel1.Controls.Add((Control) this.lblSharedPfno);
    this.Panel1.Controls.Add((Control) this.cmbPfno);
    this.Panel1.Controls.Add((Control) this.lblPfno);
    this.Panel1.Controls.Add((Control) this.cmbName);
    this.Panel1.Controls.Add((Control) this.cmbVideo);
    this.Panel1.Controls.Add((Control) this.lblVideo);
    this.Panel1.Controls.Add((Control) this.txtType);
    this.Panel1.Controls.Add((Control) this.cmbAddress);
    this.Panel1.Controls.Add((Control) this.lblType);
    this.Panel1.Controls.Add((Control) this.lblLine5);
    this.Panel1.Controls.Add((Control) this.lblLine4);
    this.Panel1.Controls.Add((Control) this.lblLine3);
    this.Panel1.Controls.Add((Control) this.lblLine2);
    this.Panel1.Controls.Add((Control) this.lblLine1);
    this.Panel1.Controls.Add((Control) this.lblDisEff);
    this.Panel1.Controls.Add((Control) this.cmbEffect5);
    this.Panel1.Controls.Add((Control) this.cmbEffect4);
    this.Panel1.Controls.Add((Control) this.cmbEffect3);
    this.Panel1.Controls.Add((Control) this.cmbEffect2);
    this.Panel1.Controls.Add((Control) this.cmbEffect1);
    this.Panel1.Controls.Add((Control) this.txtNoLines);
    this.Panel1.Controls.Add((Control) this.lblNoLines);
    this.Panel1.Controls.Add((Control) this.lblAddress);
    this.Panel1.Controls.Add((Control) this.lblName);
    Panel panel1_1 = this.Panel1;
    point1 = new Point(156, 1);
    Point point7 = point1;
    panel1_1.Location = point7;
    this.Panel1.Name = "Panel1";
    Panel panel1_2 = this.Panel1;
    size1 = new Size(331, 649);
    Size size7 = size1;
    panel1_2.Size = size7;
    this.Panel1.TabIndex = 20;
    this.lblMs.AutoSize = true;
    this.lblMs.Font = new Font("Microsoft Sans Serif", 12f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.lblMs.ForeColor = SystemColors.ControlText;
    Label lblMs1 = this.lblMs;
    point1 = new Point(243, 371);
    Point point8 = point1;
    lblMs1.Location = point8;
    this.lblMs.Name = "lblMs";
    Label lblMs2 = this.lblMs;
    size1 = new Size(32 /*0x20*/, 20);
    Size size8 = size1;
    lblMs2.Size = size8;
    this.lblMs.TabIndex = 287;
    this.lblMs.Text = "ms";
    this.lblMs.Visible = false;
    this.numEffectSpeed.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    NumericUpDown numEffectSpeed1 = this.numEffectSpeed;
    point1 = new Point(191, 372);
    Point point9 = point1;
    numEffectSpeed1.Location = point9;
    NumericUpDown numEffectSpeed2 = this.numEffectSpeed;
    Decimal num1 = new Decimal(new int[4]
    {
      (int) byte.MaxValue,
      0,
      0,
      0
    });
    Decimal num2 = num1;
    numEffectSpeed2.Maximum = num2;
    this.numEffectSpeed.Name = "numEffectSpeed";
    NumericUpDown numEffectSpeed3 = this.numEffectSpeed;
    size1 = new Size(51, 22);
    Size size9 = size1;
    numEffectSpeed3.Size = size9;
    this.numEffectSpeed.TabIndex = 286;
    NumericUpDown numEffectSpeed4 = this.numEffectSpeed;
    num1 = new Decimal(new int[4]{ 60, 0, 0, 0 });
    Decimal num3 = num1;
    numEffectSpeed4.Value = num3;
    this.lblEffectSpeed.AutoSize = true;
    this.lblEffectSpeed.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.lblEffectSpeed.ForeColor = SystemColors.ControlText;
    Label lblEffectSpeed1 = this.lblEffectSpeed;
    point1 = new Point(-1, 378);
    Point point10 = point1;
    lblEffectSpeed1.Location = point10;
    this.lblEffectSpeed.Name = "lblEffectSpeed";
    Label lblEffectSpeed2 = this.lblEffectSpeed;
    size1 = new Size(161, 16 /*0x10*/);
    Size size10 = size1;
    lblEffectSpeed2.Size = size10;
    this.lblEffectSpeed.TabIndex = 285;
    this.lblEffectSpeed.Text = " Running Effect Speed";
    this.txtDir.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtDir1 = this.txtDir;
    point1 = new Point(132, 221);
    Point point11 = point1;
    txtDir1.Location = point11;
    this.txtDir.MaxLength = 10;
    this.txtDir.Name = "txtDir";
    this.txtDir.ReadOnly = true;
    TextBox txtDir2 = this.txtDir;
    size1 = new Size(84, 22);
    Size size11 = size1;
    txtDir2.Size = size11;
    this.txtDir.TabIndex = 12;
    this.txtDir.Visible = false;
    this.lbldir.AutoSize = true;
    this.lbldir.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.lbldir.ForeColor = SystemColors.WindowText;
    Label lbldir1 = this.lbldir;
    point1 = new Point(33, 227);
    Point point12 = point1;
    lbldir1.Location = point12;
    this.lbldir.Name = "lbldir";
    Label lbldir2 = this.lbldir;
    size1 = new Size(70, 16 /*0x10*/);
    Size size12 = size1;
    lbldir2.Size = size12;
    this.lbldir.TabIndex = 284;
    this.lbldir.Text = "Direction";
    this.lbldir.Visible = false;
    this.lblPdch.AutoSize = true;
    this.lblPdch.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblPdch1 = this.lblPdch;
    point1 = new Point(239, 123);
    Point point13 = point1;
    lblPdch1.Location = point13;
    this.lblPdch.Name = "lblPdch";
    Label lblPdch2 = this.lblPdch;
    size1 = new Size(50, 16 /*0x10*/);
    Size size13 = size1;
    lblPdch2.Size = size13;
    this.lblPdch.TabIndex = 282;
    this.lblPdch.Text = "PDCH";
    this.lblPdch.Visible = false;
    this.lblCgdb.AutoSize = true;
    this.lblCgdb.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblCgdb1 = this.lblCgdb;
    point1 = new Point(150, 120);
    Point point14 = point1;
    lblCgdb1.Location = point14;
    this.lblCgdb.Name = "lblCgdb";
    Label lblCgdb2 = this.lblCgdb;
    size1 = new Size(50, 16 /*0x10*/);
    Size size14 = size1;
    lblCgdb2.Size = size14;
    this.lblCgdb.TabIndex = 281;
    this.lblCgdb.Text = "CGDB";
    this.lblCgdb.Visible = false;
    this.txtPDCHAddr.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtPdchAddr1 = this.txtPDCHAddr;
    point1 = new Point(239, 183);
    Point point15 = point1;
    txtPdchAddr1.Location = point15;
    this.txtPDCHAddr.Name = "txtPDCHAddr";
    this.txtPDCHAddr.ReadOnly = true;
    TextBox txtPdchAddr2 = this.txtPDCHAddr;
    size1 = new Size(85, 22);
    Size size15 = size1;
    txtPdchAddr2.Size = size15;
    this.txtPDCHAddr.TabIndex = 11;
    this.txtPDCHAddr.Visible = false;
    this.txtPDCHName.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtPdchName1 = this.txtPDCHName;
    point1 = new Point(239, 144 /*0x90*/);
    Point point16 = point1;
    txtPdchName1.Location = point16;
    this.txtPDCHName.Name = "txtPDCHName";
    this.txtPDCHName.ReadOnly = true;
    TextBox txtPdchName2 = this.txtPDCHName;
    size1 = new Size(85, 22);
    Size size16 = size1;
    txtPdchName2.Size = size16;
    this.txtPDCHName.TabIndex = 10;
    this.txtPDCHName.Visible = false;
    this.chkAllPfno.AutoSize = true;
    this.chkAllPfno.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    CheckBox chkAllPfno1 = this.chkAllPfno;
    point1 = new Point(151, 18);
    Point point17 = point1;
    chkAllPfno1.Location = point17;
    this.chkAllPfno.Name = "chkAllPfno";
    CheckBox chkAllPfno2 = this.chkAllPfno;
    size1 = new Size(15, 14);
    Size size17 = size1;
    chkAllPfno2.Size = size17;
    this.chkAllPfno.TabIndex = 5;
    this.chkAllPfno.UseVisualStyleBackColor = true;
    this.chkAllPfno.Visible = false;
    this.lblAllPfno.AutoSize = true;
    this.lblAllPfno.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.lblAllPfno.ForeColor = SystemColors.WindowText;
    Label lblAllPfno1 = this.lblAllPfno;
    point1 = new Point(19, 16 /*0x10*/);
    Point point18 = point1;
    lblAllPfno1.Location = point18;
    this.lblAllPfno.Name = "lblAllPfno";
    Label lblAllPfno2 = this.lblAllPfno;
    size1 = new Size(119, 16 /*0x10*/);
    Size size18 = size1;
    lblAllPfno2.Size = size18;
    this.lblAllPfno.TabIndex = 277;
    this.lblAllPfno.Text = "All Platform Nos";
    this.lblAllPfno.Visible = false;
    this.txtSharedPfno.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtSharedPfno1 = this.txtSharedPfno;
    point1 = new Point(151, 83);
    Point point19 = point1;
    txtSharedPfno1.Location = point19;
    this.txtSharedPfno.MaxLength = 2;
    this.txtSharedPfno.Name = "txtSharedPfno";
    this.txtSharedPfno.ReadOnly = true;
    TextBox txtSharedPfno2 = this.txtSharedPfno;
    size1 = new Size(51, 22);
    Size size19 = size1;
    txtSharedPfno2.Size = size19;
    this.txtSharedPfno.TabIndex = 7;
    this.txtSharedPfno.Visible = false;
    this.lblSharedPfno.AutoSize = true;
    this.lblSharedPfno.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.lblSharedPfno.ForeColor = SystemColors.WindowText;
    Label lblSharedPfno1 = this.lblSharedPfno;
    point1 = new Point(3, 83);
    Point point20 = point1;
    lblSharedPfno1.Location = point20;
    this.lblSharedPfno.Name = "lblSharedPfno";
    Label lblSharedPfno2 = this.lblSharedPfno;
    size1 = new Size(143, 16 /*0x10*/);
    Size size20 = size1;
    lblSharedPfno2.Size = size20;
    this.lblSharedPfno.TabIndex = 276;
    this.lblSharedPfno.Text = "Shared Platform No";
    this.lblSharedPfno.Visible = false;
    this.cmbPfno.DropDownStyle = ComboBoxStyle.DropDownList;
    this.cmbPfno.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.cmbPfno.FormattingEnabled = true;
    ComboBox cmbPfno1 = this.cmbPfno;
    point1 = new Point(151, 48 /*0x30*/);
    Point point21 = point1;
    cmbPfno1.Location = point21;
    this.cmbPfno.Name = "cmbPfno";
    ComboBox cmbPfno2 = this.cmbPfno;
    size1 = new Size(49, 24);
    Size size21 = size1;
    cmbPfno2.Size = size21;
    this.cmbPfno.TabIndex = 6;
    this.lblPfno.AutoSize = true;
    this.lblPfno.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.lblPfno.ForeColor = SystemColors.WindowText;
    Label lblPfno1 = this.lblPfno;
    point1 = new Point(47, 51);
    Point point22 = point1;
    lblPfno1.Location = point22;
    this.lblPfno.Name = "lblPfno";
    Label lblPfno2 = this.lblPfno;
    size1 = new Size(89, 16 /*0x10*/);
    Size size22 = size1;
    lblPfno2.Size = size22;
    this.lblPfno.TabIndex = 274;
    this.lblPfno.Text = "Platform No";
    this.cmbName.DropDownStyle = ComboBoxStyle.DropDownList;
    this.cmbName.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.cmbName.FormattingEnabled = true;
    ComboBox cmbName1 = this.cmbName;
    point1 = new Point(131, 140);
    Point point23 = point1;
    cmbName1.Location = point23;
    this.cmbName.Name = "cmbName";
    ComboBox cmbName2 = this.cmbName;
    size1 = new Size(97, 24);
    Size size23 = size1;
    cmbName2.Size = size23;
    this.cmbName.Sorted = true;
    this.cmbName.TabIndex = 8;
    this.cmbVideo.DropDownStyle = ComboBoxStyle.DropDownList;
    this.cmbVideo.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.cmbVideo.FormattingEnabled = true;
    this.cmbVideo.Items.AddRange(new object[2]
    {
      (object) "Normal",
      (object) "Reverse"
    });
    ComboBox cmbVideo1 = this.cmbVideo;
    point1 = new Point(131, 256 /*0x0100*/);
    Point point24 = point1;
    cmbVideo1.Location = point24;
    this.cmbVideo.Name = "cmbVideo";
    ComboBox cmbVideo2 = this.cmbVideo;
    size1 = new Size(85, 24);
    Size size24 = size1;
    cmbVideo2.Size = size24;
    this.cmbVideo.TabIndex = 13;
    this.lblVideo.AutoSize = true;
    this.lblVideo.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.lblVideo.ForeColor = SystemColors.WindowText;
    Label lblVideo1 = this.lblVideo;
    point1 = new Point(52, 259);
    Point point25 = point1;
    lblVideo1.Location = point25;
    this.lblVideo.Name = "lblVideo";
    Label lblVideo2 = this.lblVideo;
    size1 = new Size(49, 16 /*0x10*/);
    Size size25 = size1;
    lblVideo2.Size = size25;
    this.lblVideo.TabIndex = 272;
    this.lblVideo.Text = "Video";
    this.txtType.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtType1 = this.txtType;
    point1 = new Point(132, 334);
    Point point26 = point1;
    txtType1.Location = point26;
    this.txtType.Name = "txtType";
    this.txtType.ReadOnly = true;
    TextBox txtType2 = this.txtType;
    size1 = new Size(90, 22);
    Size size26 = size1;
    txtType2.Size = size26;
    this.txtType.TabIndex = 15;
    this.txtType.Visible = false;
    this.cmbAddress.DropDownStyle = ComboBoxStyle.DropDownList;
    this.cmbAddress.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.cmbAddress.FormattingEnabled = true;
    ComboBox cmbAddress1 = this.cmbAddress;
    point1 = new Point(131, 180);
    Point point27 = point1;
    cmbAddress1.Location = point27;
    this.cmbAddress.Name = "cmbAddress";
    ComboBox cmbAddress2 = this.cmbAddress;
    size1 = new Size(97, 24);
    Size size27 = size1;
    cmbAddress2.Size = size27;
    this.cmbAddress.Sorted = true;
    this.cmbAddress.TabIndex = 9;
    this.lblType.AutoSize = true;
    this.lblType.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.lblType.ForeColor = SystemColors.WindowText;
    Label lblType1 = this.lblType;
    point1 = new Point(53, 340);
    Point point28 = point1;
    lblType1.Location = point28;
    this.lblType.Name = "lblType";
    Label lblType2 = this.lblType;
    size1 = new Size(44, 16 /*0x10*/);
    Size size28 = size1;
    lblType2.Size = size28;
    this.lblType.TabIndex = 271;
    this.lblType.Text = "Type";
    this.lblType.Visible = false;
    this.lblLine5.AutoSize = true;
    this.lblLine5.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.lblLine5.ForeColor = SystemColors.WindowText;
    Label lblLine5_1 = this.lblLine5;
    point1 = new Point(65, 573);
    Point point29 = point1;
    lblLine5_1.Location = point29;
    this.lblLine5.Name = "lblLine5";
    Label lblLine5_2 = this.lblLine5;
    size1 = new Size(45, 16 /*0x10*/);
    Size size29 = size1;
    lblLine5_2.Size = size29;
    this.lblLine5.TabIndex = 270;
    this.lblLine5.Text = "Line5";
    this.lblLine5.Visible = false;
    this.lblLine4.AutoSize = true;
    this.lblLine4.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.lblLine4.ForeColor = SystemColors.WindowText;
    Label lblLine4_1 = this.lblLine4;
    point1 = new Point(65, 543);
    Point point30 = point1;
    lblLine4_1.Location = point30;
    this.lblLine4.Name = "lblLine4";
    Label lblLine4_2 = this.lblLine4;
    size1 = new Size(45, 16 /*0x10*/);
    Size size30 = size1;
    lblLine4_2.Size = size30;
    this.lblLine4.TabIndex = 269;
    this.lblLine4.Text = "Line4";
    this.lblLine4.Visible = false;
    this.lblLine3.AutoSize = true;
    this.lblLine3.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.lblLine3.ForeColor = SystemColors.WindowText;
    Label lblLine3_1 = this.lblLine3;
    point1 = new Point(66, 513);
    Point point31 = point1;
    lblLine3_1.Location = point31;
    this.lblLine3.Name = "lblLine3";
    Label lblLine3_2 = this.lblLine3;
    size1 = new Size(45, 16 /*0x10*/);
    Size size31 = size1;
    lblLine3_2.Size = size31;
    this.lblLine3.TabIndex = 268;
    this.lblLine3.Text = "Line3";
    this.lblLine3.Visible = false;
    this.lblLine2.AutoSize = true;
    this.lblLine2.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.lblLine2.ForeColor = SystemColors.WindowText;
    Label lblLine2_1 = this.lblLine2;
    point1 = new Point(65, 483);
    Point point32 = point1;
    lblLine2_1.Location = point32;
    this.lblLine2.Name = "lblLine2";
    Label lblLine2_2 = this.lblLine2;
    size1 = new Size(45, 16 /*0x10*/);
    Size size32 = size1;
    lblLine2_2.Size = size32;
    this.lblLine2.TabIndex = 267;
    this.lblLine2.Text = "Line2";
    this.lblLine2.Visible = false;
    this.lblLine1.AutoSize = true;
    this.lblLine1.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.lblLine1.ForeColor = SystemColors.WindowText;
    Label lblLine1_1 = this.lblLine1;
    point1 = new Point(65, 453);
    Point point33 = point1;
    lblLine1_1.Location = point33;
    this.lblLine1.Name = "lblLine1";
    Label lblLine1_2 = this.lblLine1;
    size1 = new Size(45, 16 /*0x10*/);
    Size size33 = size1;
    lblLine1_2.Size = size33;
    this.lblLine1.TabIndex = 266;
    this.lblLine1.Text = "Line1";
    this.lblLine1.Visible = false;
    this.lblDisEff.AutoSize = true;
    this.lblDisEff.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.lblDisEff.ForeColor = Color.Navy;
    Label lblDisEff1 = this.lblDisEff;
    point1 = new Point(138, 419);
    Point point34 = point1;
    lblDisEff1.Location = point34;
    this.lblDisEff.Name = "lblDisEff";
    Label lblDisEff2 = this.lblDisEff;
    size1 = new Size(104, 16 /*0x10*/);
    Size size34 = size1;
    lblDisEff2.Size = size34;
    this.lblDisEff.TabIndex = 265;
    this.lblDisEff.Text = "Display Effect";
    this.lblDisEff.Visible = false;
    this.cmbEffect5.BackColor = SystemColors.ButtonFace;
    this.cmbEffect5.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.cmbEffect5.FormattingEnabled = true;
    this.cmbEffect5.Items.AddRange(new object[9]
    {
      (object) "Curtain Bottom to Top",
      (object) "Curtain Left to Right",
      (object) "Curtain Top to Bottom",
      (object) "Flashing",
      (object) "Normal",
      (object) "Running Bottom to Top",
      (object) "Running Right to Left",
      (object) "Running Top to Bottom",
      (object) "Typing Left to Right"
    });
    ComboBox cmbEffect5_1 = this.cmbEffect5;
    point1 = new Point(131, 570);
    Point point35 = point1;
    cmbEffect5_1.Location = point35;
    this.cmbEffect5.Name = "cmbEffect5";
    ComboBox cmbEffect5_2 = this.cmbEffect5;
    size1 = new Size(121, 24);
    Size size35 = size1;
    cmbEffect5_2.Size = size35;
    this.cmbEffect5.Sorted = true;
    this.cmbEffect5.TabIndex = 20;
    this.cmbEffect5.Text = "Normal";
    this.cmbEffect5.Visible = false;
    this.cmbEffect4.BackColor = SystemColors.ButtonFace;
    this.cmbEffect4.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.cmbEffect4.FormattingEnabled = true;
    this.cmbEffect4.Items.AddRange(new object[9]
    {
      (object) "Curtain Bottom to Top",
      (object) "Curtain Left to Right",
      (object) "Curtain Top to Bottom",
      (object) "Flashing",
      (object) "Normal",
      (object) "Running Bottom to Top",
      (object) "Running Right to Left",
      (object) "Running Top to Bottom",
      (object) "Typing Left to Right"
    });
    ComboBox cmbEffect4_1 = this.cmbEffect4;
    point1 = new Point(131, 540);
    Point point36 = point1;
    cmbEffect4_1.Location = point36;
    this.cmbEffect4.Name = "cmbEffect4";
    ComboBox cmbEffect4_2 = this.cmbEffect4;
    size1 = new Size(121, 24);
    Size size36 = size1;
    cmbEffect4_2.Size = size36;
    this.cmbEffect4.Sorted = true;
    this.cmbEffect4.TabIndex = 19;
    this.cmbEffect4.Text = "Normal";
    this.cmbEffect4.Visible = false;
    this.cmbEffect3.BackColor = SystemColors.ButtonFace;
    this.cmbEffect3.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.cmbEffect3.FormattingEnabled = true;
    this.cmbEffect3.Items.AddRange(new object[9]
    {
      (object) "Curtain Bottom to Top",
      (object) "Curtain Left to Right",
      (object) "Curtain Top to Bottom",
      (object) "Flashing",
      (object) "Normal",
      (object) "Running Bottom to Top",
      (object) "Running Right to Left",
      (object) "Running Top to Bottom",
      (object) "Typing Left to Right"
    });
    ComboBox cmbEffect3_1 = this.cmbEffect3;
    point1 = new Point(132, 510);
    Point point37 = point1;
    cmbEffect3_1.Location = point37;
    this.cmbEffect3.Name = "cmbEffect3";
    ComboBox cmbEffect3_2 = this.cmbEffect3;
    size1 = new Size(121, 24);
    Size size37 = size1;
    cmbEffect3_2.Size = size37;
    this.cmbEffect3.Sorted = true;
    this.cmbEffect3.TabIndex = 18;
    this.cmbEffect3.Text = "Normal";
    this.cmbEffect3.Visible = false;
    this.cmbEffect2.BackColor = SystemColors.ButtonFace;
    this.cmbEffect2.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.cmbEffect2.FormattingEnabled = true;
    this.cmbEffect2.Items.AddRange(new object[9]
    {
      (object) "Curtain Bottom to Top",
      (object) "Curtain Left to Right",
      (object) "Curtain Top to Bottom",
      (object) "Flashing",
      (object) "Normal",
      (object) "Running Bottom to Top",
      (object) "Running Right to Left",
      (object) "Running Top to Bottom",
      (object) "Typing Left to Right"
    });
    ComboBox cmbEffect2_1 = this.cmbEffect2;
    point1 = new Point(131, 480);
    Point point38 = point1;
    cmbEffect2_1.Location = point38;
    this.cmbEffect2.Name = "cmbEffect2";
    ComboBox cmbEffect2_2 = this.cmbEffect2;
    size1 = new Size(121, 24);
    Size size38 = size1;
    cmbEffect2_2.Size = size38;
    this.cmbEffect2.Sorted = true;
    this.cmbEffect2.TabIndex = 17;
    this.cmbEffect2.Text = "Normal";
    this.cmbEffect2.Visible = false;
    this.cmbEffect1.BackColor = SystemColors.ButtonFace;
    this.cmbEffect1.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.cmbEffect1.FormattingEnabled = true;
    this.cmbEffect1.Items.AddRange(new object[9]
    {
      (object) "Curtain Bottom to Top",
      (object) "Curtain Left to Right",
      (object) "Curtain Top to Bottom",
      (object) "Flashing",
      (object) "Normal",
      (object) "Running Bottom to Top",
      (object) "Running Right to Left",
      (object) "Running Top to Bottom",
      (object) "Typing Left to Right"
    });
    ComboBox cmbEffect1_1 = this.cmbEffect1;
    point1 = new Point(131, 450);
    Point point39 = point1;
    cmbEffect1_1.Location = point39;
    this.cmbEffect1.Name = "cmbEffect1";
    ComboBox cmbEffect1_2 = this.cmbEffect1;
    size1 = new Size(121, 24);
    Size size39 = size1;
    cmbEffect1_2.Size = size39;
    this.cmbEffect1.Sorted = true;
    this.cmbEffect1.TabIndex = 16 /*0x10*/;
    this.cmbEffect1.Text = "Normal";
    this.cmbEffect1.Visible = false;
    this.txtNoLines.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtNoLines1 = this.txtNoLines;
    point1 = new Point(132, 294);
    Point point40 = point1;
    txtNoLines1.Location = point40;
    this.txtNoLines.MaxLength = 2;
    this.txtNoLines.Name = "txtNoLines";
    this.txtNoLines.ReadOnly = true;
    TextBox txtNoLines2 = this.txtNoLines;
    size1 = new Size(51, 22);
    Size size40 = size1;
    txtNoLines2.Size = size40;
    this.txtNoLines.TabIndex = 14;
    this.txtNoLines.Visible = false;
    this.lblNoLines.AutoSize = true;
    this.lblNoLines.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.lblNoLines.ForeColor = SystemColors.WindowText;
    Label lblNoLines1 = this.lblNoLines;
    point1 = new Point(19, 297);
    Point point41 = point1;
    lblNoLines1.Location = point41;
    this.lblNoLines.Name = "lblNoLines";
    Label lblNoLines2 = this.lblNoLines;
    size1 = new Size(86, 16 /*0x10*/);
    Size size41 = size1;
    lblNoLines2.Size = size41;
    this.lblNoLines.TabIndex = 264;
    this.lblNoLines.Text = "No of Lines";
    this.lblNoLines.Visible = false;
    this.lblAddress.AutoSize = true;
    this.lblAddress.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.lblAddress.ForeColor = SystemColors.WindowText;
    Label lblAddress1 = this.lblAddress;
    point1 = new Point(39, 186);
    Point point42 = point1;
    lblAddress1.Location = point42;
    this.lblAddress.Name = "lblAddress";
    Label lblAddress2 = this.lblAddress;
    size1 = new Size(66, 16 /*0x10*/);
    Size size42 = size1;
    lblAddress2.Size = size42;
    this.lblAddress.TabIndex = 263;
    this.lblAddress.Text = "Address";
    this.lblName.AutoSize = true;
    this.lblName.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.lblName.ForeColor = SystemColors.WindowText;
    Label lblName1 = this.lblName;
    point1 = new Point(52, 146);
    Point point43 = point1;
    lblName1.Location = point43;
    this.lblName.Name = "lblName";
    Label lblName2 = this.lblName;
    size1 = new Size(49, 16 /*0x10*/);
    Size size43 = size1;
    lblName2.Size = size43;
    this.lblName.TabIndex = 262;
    this.lblName.Text = "Name";
    this.btnExit.BackColor = Color.SeaShell;
    this.btnExit.DialogResult = DialogResult.Cancel;
    this.btnExit.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.btnExit.ForeColor = SystemColors.WindowText;
    Button btnExit1 = this.btnExit;
    point1 = new Point(41, 583);
    Point point44 = point1;
    btnExit1.Location = point44;
    this.btnExit.Name = "btnExit";
    Button btnExit2 = this.btnExit;
    size1 = new Size(60, 25);
    Size size44 = size1;
    btnExit2.Size = size44;
    this.btnExit.TabIndex = 22;
    this.btnExit.Text = "Exit";
    this.btnExit.UseVisualStyleBackColor = false;
    this.radFlase.AutoSize = true;
    this.radFlase.Checked = true;
    RadioButton radFlase1 = this.radFlase;
    point1 = new Point(16 /*0x10*/, 547);
    Point point45 = point1;
    radFlase1.Location = point45;
    this.radFlase.Name = "radFlase";
    RadioButton radFlase2 = this.radFlase;
    size1 = new Size(90, 17);
    Size size45 = size1;
    radFlase2.Size = size45;
    this.radFlase.TabIndex = 262;
    this.radFlase.TabStop = true;
    this.radFlase.Text = "RadioButton1";
    this.radFlase.UseVisualStyleBackColor = true;
    this.radFlase.Visible = false;
    this.btnOk.BackColor = Color.SeaShell;
    this.btnOk.DialogResult = DialogResult.Cancel;
    this.btnOk.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.btnOk.ForeColor = SystemColors.WindowText;
    Button btnOk1 = this.btnOk;
    point1 = new Point(106, 617);
    Point point46 = point1;
    btnOk1.Location = point46;
    this.btnOk.Name = "btnOk";
    Button btnOk2 = this.btnOk;
    size1 = new Size(60, 25);
    Size size46 = size1;
    btnOk2.Size = size46;
    this.btnOk.TabIndex = 288;
    this.btnOk.Text = "Ok";
    this.btnOk.UseVisualStyleBackColor = false;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.BackColor = Color.PowderBlue;
    this.CancelButton = (IButtonControl) this.btnExit;
    size1 = new Size(486, 651);
    this.ClientSize = size1;
    this.Controls.Add((Control) this.radFlase);
    this.Controls.Add((Control) this.Panel1);
    this.Controls.Add((Control) this.Label5);
    this.Controls.Add((Control) this.radPdb);
    this.Controls.Add((Control) this.radMldb);
    this.Controls.Add((Control) this.radCgdb);
    this.Controls.Add((Control) this.radAgdb);
    this.Controls.Add((Control) this.btnExit);
    this.FormBorderStyle = FormBorderStyle.FixedToolWindow;
    this.Name = "frmDisplayBoardSettings";
    this.Text = "Display Board Settings";
    this.Panel1.ResumeLayout(false);
    this.Panel1.PerformLayout();
    this.numEffectSpeed.EndInit();
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  internal virtual Label Label5
  {
    [DebuggerNonUserCode] get { return this._Label5; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Label5 = value; }
  }

  internal virtual RadioButton radPdb
  {
    [DebuggerNonUserCode] get { return this._radPdb; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.radPdb_CheckedChanged);
      if (this._radPdb != null)
        this._radPdb.CheckedChanged -= eventHandler;
      this._radPdb = value;
      if (this._radPdb == null)
        return;
      this._radPdb.CheckedChanged += eventHandler;
    }
  }

  internal virtual RadioButton radMldb
  {
    [DebuggerNonUserCode] get { return this._radMldb; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.radMldb_CheckedChanged);
      if (this._radMldb != null)
        this._radMldb.CheckedChanged -= eventHandler;
      this._radMldb = value;
      if (this._radMldb == null)
        return;
      this._radMldb.CheckedChanged += eventHandler;
    }
  }

  internal virtual RadioButton radCgdb
  {
    [DebuggerNonUserCode] get { return this._radCgdb; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.radCgs_CheckedChanged);
      if (this._radCgdb != null)
        this._radCgdb.CheckedChanged -= eventHandler;
      this._radCgdb = value;
      if (this._radCgdb == null)
        return;
      this._radCgdb.CheckedChanged += eventHandler;
    }
  }

  internal virtual RadioButton radAgdb
  {
    [DebuggerNonUserCode] get { return this._radAgdb; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.radAgdb_CheckedChanged);
      if (this._radAgdb != null)
        this._radAgdb.CheckedChanged -= eventHandler;
      this._radAgdb = value;
      if (this._radAgdb == null)
        return;
      this._radAgdb.CheckedChanged += eventHandler;
    }
  }

  internal virtual Panel Panel1
  {
    [DebuggerNonUserCode] get { return this._Panel1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Panel1 = value; }
  }

  internal virtual ComboBox cmbName
  {
    [DebuggerNonUserCode] get { return this._cmbName; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.cmbName_SelectedIndexChanged);
      if (this._cmbName != null)
        this._cmbName.SelectedIndexChanged -= eventHandler;
      this._cmbName = value;
      if (this._cmbName == null)
        return;
      this._cmbName.SelectedIndexChanged += eventHandler;
    }
  }

  internal virtual ComboBox cmbVideo
  {
    [DebuggerNonUserCode] get { return this._cmbVideo; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._cmbVideo = value; }
  }

  internal virtual Label lblVideo
  {
    [DebuggerNonUserCode] get { return this._lblVideo; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._lblVideo = value; }
  }

  internal virtual TextBox txtType
  {
    [DebuggerNonUserCode] get { return this._txtType; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._txtType = value; }
  }

  internal virtual Button btnExit
  {
    [DebuggerNonUserCode] get { return this._btnExit; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnExit_Click);
      if (this._btnExit != null)
        this._btnExit.Click -= eventHandler;
      this._btnExit = value;
      if (this._btnExit == null)
        return;
      this._btnExit.Click += eventHandler;
    }
  }

  internal virtual ComboBox cmbAddress
  {
    [DebuggerNonUserCode] get { return this._cmbAddress; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.cmbAddress_SelectedIndexChanged);
      if (this._cmbAddress != null)
        this._cmbAddress.SelectedIndexChanged -= eventHandler;
      this._cmbAddress = value;
      if (this._cmbAddress == null)
        return;
      this._cmbAddress.SelectedIndexChanged += eventHandler;
    }
  }

  internal virtual Label lblType
  {
    [DebuggerNonUserCode] get { return this._lblType; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._lblType = value; }
  }

  internal virtual Label lblLine5
  {
    [DebuggerNonUserCode] get { return this._lblLine5; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._lblLine5 = value; }
  }

  internal virtual Label lblLine4
  {
    [DebuggerNonUserCode] get { return this._lblLine4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._lblLine4 = value; }
  }

  internal virtual Label lblLine3
  {
    [DebuggerNonUserCode] get { return this._lblLine3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._lblLine3 = value; }
  }

  internal virtual Label lblLine2
  {
    [DebuggerNonUserCode] get { return this._lblLine2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._lblLine2 = value; }
  }

  internal virtual Label lblLine1
  {
    [DebuggerNonUserCode] get { return this._lblLine1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._lblLine1 = value; }
  }

  internal virtual Label lblDisEff
  {
    [DebuggerNonUserCode] get { return this._lblDisEff; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblDisEff = value;
    }
  }

  internal virtual ComboBox cmbEffect5
  {
    [DebuggerNonUserCode] get { return this._cmbEffect5; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._cmbEffect5 = value;
    }
  }

  internal virtual ComboBox cmbEffect4
  {
    [DebuggerNonUserCode] get { return this._cmbEffect4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._cmbEffect4 = value;
    }
  }

  internal virtual ComboBox cmbEffect3
  {
    [DebuggerNonUserCode] get { return this._cmbEffect3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._cmbEffect3 = value;
    }
  }

  internal virtual ComboBox cmbEffect2
  {
    [DebuggerNonUserCode] get { return this._cmbEffect2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._cmbEffect2 = value;
    }
  }

  internal virtual ComboBox cmbEffect1
  {
    [DebuggerNonUserCode] get { return this._cmbEffect1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._cmbEffect1 = value;
    }
  }

  internal virtual TextBox txtNoLines
  {
    [DebuggerNonUserCode] get { return this._txtNoLines; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtNoLines = value;
    }
  }

  internal virtual Label lblNoLines
  {
    [DebuggerNonUserCode] get { return this._lblNoLines; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblNoLines = value;
    }
  }

  internal virtual Label lblAddress
  {
    [DebuggerNonUserCode] get { return this._lblAddress; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblAddress = value;
    }
  }

  internal virtual Label lblName
  {
    [DebuggerNonUserCode] get { return this._lblName; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._lblName = value; }
  }

  internal virtual ComboBox cmbPfno
  {
    [DebuggerNonUserCode] get { return this._cmbPfno; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.cmbPfno_SelectedIndexChanged);
      if (this._cmbPfno != null)
        this._cmbPfno.SelectedIndexChanged -= eventHandler;
      this._cmbPfno = value;
      if (this._cmbPfno == null)
        return;
      this._cmbPfno.SelectedIndexChanged += eventHandler;
    }
  }

  internal virtual Label lblPfno
  {
    [DebuggerNonUserCode] get { return this._lblPfno; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._lblPfno = value; }
  }

  internal virtual RadioButton radFlase
  {
    [DebuggerNonUserCode] get { return this._radFlase; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._radFlase = value; }
  }

  internal virtual CheckBox chkAllPfno
  {
    [DebuggerNonUserCode] get { return this._chkAllPfno; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.chkAllPfno_CheckedChanged);
      if (this._chkAllPfno != null)
        this._chkAllPfno.CheckedChanged -= eventHandler;
      this._chkAllPfno = value;
      if (this._chkAllPfno == null)
        return;
      this._chkAllPfno.CheckedChanged += eventHandler;
    }
  }

  internal virtual Label lblAllPfno
  {
    [DebuggerNonUserCode] get { return this._lblAllPfno; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblAllPfno = value;
    }
  }

  internal virtual TextBox txtSharedPfno
  {
    [DebuggerNonUserCode] get { return this._txtSharedPfno; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtSharedPfno = value;
    }
  }

  internal virtual Label lblSharedPfno
  {
    [DebuggerNonUserCode] get { return this._lblSharedPfno; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblSharedPfno = value;
    }
  }

  internal virtual TextBox txtPDCHAddr
  {
    [DebuggerNonUserCode] get { return this._txtPDCHAddr; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtPDCHAddr = value;
    }
  }

  internal virtual TextBox txtPDCHName
  {
    [DebuggerNonUserCode] get { return this._txtPDCHName; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtPDCHName = value;
    }
  }

  internal virtual Label lblCgdb
  {
    [DebuggerNonUserCode] get { return this._lblCgdb; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._lblCgdb = value; }
  }

  internal virtual Label lblPdch
  {
    [DebuggerNonUserCode] get { return this._lblPdch; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._lblPdch = value; }
  }

  internal virtual TextBox txtDir
  {
    [DebuggerNonUserCode] get { return this._txtDir; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._txtDir = value; }
  }

  internal virtual Label lbldir
  {
    [DebuggerNonUserCode] get { return this._lbldir; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._lbldir = value; }
  }

  internal virtual Label lblEffectSpeed
  {
    [DebuggerNonUserCode] get { return this._lblEffectSpeed; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblEffectSpeed = value;
    }
  }

  internal virtual Label lblMs
  {
    [DebuggerNonUserCode] get { return this._lblMs; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._lblMs = value; }
  }

  internal virtual NumericUpDown numEffectSpeed
  {
    [DebuggerNonUserCode] get { return this._numEffectSpeed; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._numEffectSpeed = value;
    }
  }

  internal virtual Button btnOk
  {
    [DebuggerNonUserCode] get { return this._btnOk; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnOk_Click);
      if (this._btnOk != null)
        this._btnOk.Click -= eventHandler;
      this._btnOk = value;
      if (this._btnOk == null)
        return;
      this._btnOk.Click += eventHandler;
    }
  }

  private void radAgdb_CheckedChanged(object sender, EventArgs e)
  {
    if (!this.radAgdb.Checked)
      return;
    this.lblAllPfno.Visible = true;
    this.chkAllPfno.Visible = true;
    this.cmbPfno.Visible = true;
    this.lblPfno.Visible = true;
    this.lblSharedPfno.Visible = false;
    this.txtSharedPfno.Visible = false;
    this.lblCgdb.Visible = false;
    this.cmbAddress.Visible = true;
    this.cmbName.Visible = true;
    this.lblPdch.Visible = false;
    this.txtPDCHAddr.Visible = false;
    this.txtPDCHName.Visible = false;
    this.lbldir.Visible = false;
    this.txtDir.Visible = false;
    this.txtType.Visible = false;
    this.txtNoLines.Visible = false;
    this.cmbEffect1.Visible = false;
    this.cmbEffect2.Visible = false;
    this.cmbEffect3.Visible = false;
    this.cmbEffect4.Visible = false;
    this.cmbEffect5.Visible = false;
    this.lblType.Visible = false;
    this.lblNoLines.Visible = false;
    this.lblLine1.Visible = false;
    this.lblLine2.Visible = false;
    this.lblLine3.Visible = false;
    this.lblLine4.Visible = false;
    this.lblLine5.Visible = false;
    this.lblDisEff.Visible = false;
    this.lblEffectSpeed.Visible = false;
    this.lblMs.Visible = false;
    this.numEffectSpeed.Visible = false;
    this.cmbAddress.Items.Clear();
    this.cmbName.Items.Clear();
    this.cmbAddress.Text = string.Empty;
    this.cmbName.Text = string.Empty;
    this.cmbVideo.Text = "Normal";
    this.cmbPfno.Text = string.Empty;
  }

  private void radCgs_CheckedChanged(object sender, EventArgs e)
  {
    if (this.radCgdb.Checked)
    {
      this.lblAllPfno.Visible = false;
      this.chkAllPfno.Visible = false;
      this.cmbPfno.Enabled = true;
      this.lblSharedPfno.Visible = false;
      this.txtSharedPfno.Visible = false;
      this.lblDisEff.Visible = false;
      this.cmbPfno.Visible = true;
      this.txtType.Visible = false;
      this.txtNoLines.Visible = false;
      this.cmbEffect1.Visible = false;
      this.cmbEffect2.Visible = false;
      this.cmbEffect3.Visible = false;
      this.cmbEffect4.Visible = false;
      this.cmbEffect5.Visible = false;
      this.lblPfno.Visible = true;
      this.lblType.Visible = false;
      this.lblNoLines.Visible = false;
      this.lblLine1.Visible = false;
      this.lblLine2.Visible = false;
      this.lblLine3.Visible = false;
      this.lblLine4.Visible = false;
      this.lblLine5.Visible = false;
      this.lblPdch.Visible = true;
      this.lblPdch.Visible = true;
      this.txtPDCHAddr.Visible = true;
      this.txtPDCHName.Visible = true;
      this.lblAddress.Visible = true;
      this.lblName.Visible = true;
      this.cmbAddress.Visible = true;
      this.cmbName.Visible = true;
      this.lbldir.Visible = true;
      this.txtDir.Visible = true;
      this.lblEffectSpeed.Visible = false;
      this.lblMs.Visible = false;
      this.numEffectSpeed.Visible = false;
      this.cmbAddress.Items.Clear();
      this.cmbName.Items.Clear();
      this.cmbAddress.Text = string.Empty;
      this.cmbName.Text = string.Empty;
      this.txtPDCHAddr.Text = string.Empty;
      this.txtPDCHName.Text = string.Empty;
      this.cmbName.Visible = false;
      this.cmbAddress.Visible = false;
      this.lblAddress.Visible = false;
      this.lblName.Visible = false;
      this.txtPDCHAddr.Visible = false;
      this.txtPDCHName.Visible = false;
      this.lblPdch.Visible = false;
      this.cmbVideo.Text = "Normal";
      this.txtDir.Text = string.Empty;
      this.cmbPfno.Text = string.Empty;
    }
    else
    {
      this.cmbName.Visible = true;
      this.cmbAddress.Visible = true;
      this.lblAddress.Visible = true;
      this.lblName.Visible = true;
      this.cmbPfno.Text = string.Empty;
    }
  }

  private void radMldb_CheckedChanged(object sender, EventArgs e)
  {
    if (!this.radMldb.Checked)
      return;
    this.chkAllPfno.Visible = false;
    this.lblAllPfno.Visible = false;
    this.cmbPfno.Visible = false;
    this.lblPfno.Visible = false;
    this.txtSharedPfno.Visible = false;
    this.lblSharedPfno.Visible = false;
    this.lblDisEff.Visible = true;
    this.cmbAddress.Visible = true;
    this.lblAddress.Visible = true;
    this.cmbName.Visible = true;
    this.lblName.Visible = true;
    this.cmbVideo.Visible = true;
    this.txtType.Visible = true;
    this.txtNoLines.Visible = true;
    this.cmbEffect1.Visible = true;
    this.cmbEffect2.Visible = true;
    this.cmbEffect3.Visible = true;
    this.cmbEffect4.Visible = true;
    this.cmbEffect5.Visible = true;
    this.lblPdch.Visible = false;
    this.lblCgdb.Visible = false;
    this.txtPDCHAddr.Visible = false;
    this.txtPDCHName.Visible = false;
    this.lbldir.Visible = false;
    this.txtDir.Visible = false;
    this.lblVideo.Visible = true;
    this.lblType.Visible = true;
    this.lblNoLines.Visible = true;
    this.lblLine1.Visible = true;
    this.lblLine2.Visible = true;
    this.lblLine3.Visible = true;
    this.lblLine4.Visible = true;
    this.lblLine5.Visible = true;
    this.lblEffectSpeed.Visible = true;
    this.lblMs.Visible = true;
    this.numEffectSpeed.Visible = true;
    this.cmbName.Items.Clear();
    this.cmbAddress.Items.Clear();
    this.cmbAddress.Text = string.Empty;
    this.cmbName.Text = string.Empty;
    this.txtType.Text = string.Empty;
    this.txtNoLines.Text = string.Empty;
    this.cmbEffect1.Text = string.Empty;
    this.cmbEffect2.Text = string.Empty;
    this.cmbEffect3.Text = string.Empty;
    this.cmbEffect4.Text = string.Empty;
    this.cmbEffect5.Text = string.Empty;
    this.cmbVideo.Text = "Normal";
    this.cmbAddress.Items.Clear();
    this.cmbName.Items.Clear();
    int index = 0;
    while (index < (int) taddb_msg.no_of_mldbs)
    {
      this.cmbAddress.Items.Add((object) taddb_msg.mldb_dis_brd.mdlb[index].mldb_addr);
      this.cmbName.Items.Add((object) taddb_msg.mldb_dis_brd.mdlb[index].mldb_name);
      checked { ++index; }
    }
  }

  private void radPdb_CheckedChanged(object sender, EventArgs e)
  {
    if (!this.radPdb.Checked)
      return;
    this.lblAllPfno.Visible = false;
    this.chkAllPfno.Visible = false;
    this.cmbPfno.Enabled = true;
    this.cmbPfno.Visible = true;
    this.lblPfno.Visible = true;
    this.lblPdch.Visible = false;
    this.lblCgdb.Visible = false;
    this.txtPDCHAddr.Visible = false;
    this.txtPDCHName.Visible = false;
    this.lbldir.Visible = false;
    this.txtDir.Visible = false;
    this.cmbVideo.Visible = true;
    this.lblVideo.Visible = true;
    this.txtType.Visible = false;
    this.lblType.Visible = false;
    this.txtNoLines.Visible = false;
    this.lblNoLines.Visible = false;
    this.lblDisEff.Visible = true;
    this.cmbEffect1.Visible = true;
    this.cmbEffect2.Visible = false;
    this.cmbEffect3.Visible = false;
    this.cmbEffect4.Visible = false;
    this.cmbEffect5.Visible = false;
    this.lblLine1.Visible = true;
    this.lblLine2.Visible = false;
    this.lblLine3.Visible = false;
    this.lblLine4.Visible = false;
    this.lblLine5.Visible = false;
    this.lblEffectSpeed.Visible = true;
    this.lblMs.Visible = true;
    this.numEffectSpeed.Visible = true;
    this.cmbAddress.Visible = false;
    this.cmbName.Visible = false;
    this.lblName.Visible = false;
    this.lblAddress.Visible = false;
    this.cmbPfno.Text = string.Empty;
    this.cmbEffect1.Text = string.Empty;
    this.cmbVideo.Text = "Normal";
    this.cmbPfno.Text = string.Empty;
    this.numEffectSpeed.Value = 60M;
  }

  private void cmbPfno_SelectedIndexChanged(object sender, EventArgs e)
  {
    byte[] numArray1 = new byte[27];
    string[] strArray1 = new string[27];
    bool[] flagArray = new bool[27];
    string[] strArray2 = new string[27];
    byte[] numArray2 = new byte[27];
    byte[] numArray3 = new byte[27];
    byte[] pdch_addr1 = new byte[27];
    string[] pdch_name = new string[27];
    byte[] numArray4 = new byte[27];
    string empty1 = string.Empty;
    string empty2 = string.Empty;
    try
    {
      int index1 = 0;
      while (index1 < 26)
      {
        flagArray[index1] = false;
        numArray1[index1] = (byte) 0;
        strArray1[index1] = string.Empty;
        strArray2[index1] = string.Empty;
        checked { ++index1; }
      }
      byte num1 = 0;
      this.cmbAddress.Visible = true;
      this.cmbName.Visible = true;
      this.cmbAddress.Items.Clear();
      this.cmbName.Items.Clear();
      frmDisplayBoardSettings.platform_no = this.cmbPfno.Text;
      if (this.radAgdb.Checked)
      {
        if (network_db_read.agdb_info_data(this.cmbPfno.Text, ref strArray1, ref numArray1, ref num1) == (byte) 0)
        {
          Log_file.Log("AGDB Information for Platform NO:{frmDisplayBoardSettings.platform_no} is not available");
          int num2 = (int) basMsgBoxEx.MsgBoxMove(this.Handle.ToInt32(), "AGDB information for PLATFORM NO:{frmDisplayBoardSettings.platform_no} is not available\r\nPlease check network configuration", "Msg Box", 0, 0, 0);
        }
        else
        {
          int index2 = 0;
          while (index2 < (int) num1)
          {
            this.cmbAddress.Items.Add((object) numArray1[index2]);
            this.cmbName.Items.Add((object) strArray1[index2]);
            checked { ++index2; }
          }
        }
      }
      else if (this.radCgdb.Checked)
      {
        this.txtPDCHAddr.Text = string.Empty;
        this.txtPDCHName.Text = string.Empty;
        this.cmbVideo.Text = "Normal";
        this.txtDir.Text = string.Empty;
        this.cmbName.Visible = false;
        this.cmbAddress.Visible = false;
        if (network_db_read.cgdb_info_data(this.cmbPfno.Text, ref strArray1, ref numArray1, ref pdch_addr1, ref pdch_name, ref num1) == (byte) 0)
        {
          Log_file.Log("CGS Information for Platform NO:{frmDisplayBoardSettings.platform_no} is not available");
          int num3 = (int) basMsgBoxEx.MsgBoxMove(this.Handle.ToInt32(), "CGS information for PLATFORM NO:{frmDisplayBoardSettings.platform_no} is not available\r\nPlease check network configuration", "Msg Box", 0, 0, 0);
        }
        else
        {
          int index3 = 0;
          while (index3 < (int) num1)
          {
            this.cmbAddress.Items.Add((object) numArray1[index3]);
            this.cmbName.Items.Add((object) strArray1[index3]);
            frmDisplayBoardSettings.cgdb_addrs[index3] = numArray1[index3];
            checked { ++index3; }
          }
          frmDisplayBoardSettings.cgdb_cnt = checked ((byte) index3);
          this.txtPDCHName.Text = string.Empty;
          this.txtPDCHAddr.Text = string.Empty;
          byte pdch_addr2 = 0;
          byte video = 0;
          network_db_read.cgdb_name_data(strArray1[0], ref numArray1[0], this.cmbPfno.Text, ref pdch_addr2, ref empty1, ref video, ref empty2);
          this.txtPDCHAddr.Text = Conversions.ToString(pdch_addr2);
          this.txtPDCHName.Text = empty1;
          this.txtDir.Text = empty2;
          this.cmbVideo.Text = video != (byte) 0 ? "Reverse" : "Normal";
        }
      }
      else if (this.radPdb.Checked)
      {
        this.lblSharedPfno.Visible = false;
        this.txtSharedPfno.Visible = false;
        this.lblSharedPfno.Visible = true;
        this.cmbAddress.Visible = false;
        this.cmbName.Visible = false;
        byte pfnoInt = network_db_read.get_pfno_int(this.cmbPfno.Text);
        if (pfnoInt != (byte) 0)
          checked { --pfnoInt; }
        if (!taddb_msg.pdb_pf_status[(int) pfnoInt])
        {
          int num4 = (int) basMsgBoxEx.MsgBoxMove(this.Handle.ToInt32(), "Platform Doesn't exist", "Msg Box", 0, 0, 0);
        }
        else
        {
          this.cmbAddress.Items.Add((object) taddb_msg.pdb_dis_brd[(int) pfnoInt, 0].multicast_addr);
          if (taddb_msg.pdb_dis_brd[(int) pfnoInt, 0].shared_platform)
          {
            this.txtSharedPfno.Text = taddb_msg.pdb_dis_brd[(int) pfnoInt, 0].shared_platform_no;
            this.txtSharedPfno.Visible = true;
          }
          else
            this.txtSharedPfno.Visible = false;
          this.cmbAddress.Text = Conversions.ToString(taddb_msg.pdb_dis_brd[(int) pfnoInt, 0].multicast_addr);
          switch (taddb_msg.pdb_dis_brd[(int) pfnoInt, 0].effect)
          {
            case 1:
              this.cmbEffect1.Text = "Curtain Left to Right";
              break;
            case 2:
              this.cmbEffect1.Text = "Curtain Top to Bottom";
              break;
            case 3:
              this.cmbEffect1.Text = "Curtain Bottom to Top";
              break;
            case 4:
              this.cmbEffect1.Text = "Typing Left to Right";
              break;
            case 5:
              this.cmbEffect1.Text = "Running Right to Left";
              break;
            case 6:
              this.cmbEffect1.Text = "Running Top to Bottom";
              break;
            case 7:
              this.cmbEffect1.Text = "Running Bottom to Top";
              break;
            case 8:
              this.cmbEffect1.Text = "Flashing";
              break;
            default:
              this.cmbEffect1.Text = "Normal";
              break;
          }
          this.cmbVideo.Text = taddb_msg.pdb_dis_brd[(int) pfnoInt, 0].video_type != (byte) 0 ? "Reverse" : "Normal";
          this.numEffectSpeed.Value = new Decimal((int) taddb_msg.pdb_dis_brd[(int) pfnoInt, 0].effect_speed);
        }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(this.Handle.ToInt32(), exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void cmbName_SelectedIndexChanged(object sender, EventArgs e)
  {
    byte num1 = 0;
    string empty1 = string.Empty;
    string empty2 = string.Empty;
    string empty3 = string.Empty;
    try
    {
      if (this.radAgdb.Checked)
      {
        if (this.chkAllPfno.Checked)
        {
          string text = this.cmbName.Text;
          string str = Conversions.ToString(num1);
          string local1 = str;
          byte num2 = 0;
          byte local2 = num2;
          network_db_read.agdb_com_name_data(text, ref local1, ref local2);
          this.cmbAddress.Text = Conversions.ToString(Conversions.ToByte(str));
          if (num2 == (byte) 0)
            this.cmbVideo.Text = "Normal";
          else
            this.cmbVideo.Text = "Reverse";
        }
        else
        {
          byte video = 0;
          bool shared_platform = false;
          network_db_read.agdb_name_data(this.cmbName.Text, ref num1, this.cmbPfno.Text, ref video, ref shared_platform, ref empty1);
          this.cmbAddress.Text = Conversions.ToString(num1);
          this.cmbVideo.Text = video != (byte) 0 ? "Reverse" : "Normal";
          if (shared_platform)
          {
            this.lblSharedPfno.Visible = true;
            this.txtSharedPfno.Visible = true;
            this.txtSharedPfno.Text = empty1;
          }
          else
          {
            this.lblSharedPfno.Visible = false;
            this.txtSharedPfno.Visible = false;
            this.txtSharedPfno.Text = string.Empty;
          }
        }
      }
      else if (this.radCgdb.Checked)
      {
        this.txtPDCHName.Text = string.Empty;
        this.txtPDCHAddr.Text = string.Empty;
        byte pdch_addr = 0;
        byte video = 0;
        network_db_read.cgdb_name_data(this.cmbName.Text, ref num1, this.cmbPfno.Text, ref pdch_addr, ref empty2, ref video, ref empty3);
        this.cmbAddress.Text = Conversions.ToString(num1);
        this.txtPDCHAddr.Text = Conversions.ToString(pdch_addr);
        this.txtPDCHName.Text = empty2;
        this.txtDir.Text = empty3;
        if (video == (byte) 0)
          this.cmbVideo.Text = "Normal";
        else
          this.cmbVideo.Text = "Reverse";
      }
      else
      {
        if (!this.radMldb.Checked)
          return;
        byte index = 0;
        while ((uint) index < (uint) taddb_msg.no_of_mldbs)
        {
          if (Operators.CompareString(taddb_msg.mldb_dis_brd.mdlb[(int) index].mldb_name, this.cmbName.Text, false) == 0)
          {
            this.cmbAddress.Text = Conversions.ToString(taddb_msg.mldb_dis_brd.mdlb[(int) index].mldb_addr);
            this.cmbVideo.Text = taddb_msg.mldb_dis_brd.mdlb[(int) index].video_type != (byte) 0 ? "Reverse" : "Normal";
            this.numEffectSpeed.Value = new Decimal((int) taddb_msg.mldb_dis_brd.mdlb[(int) index].effect_speed);
            this.txtNoLines.Text = Conversions.ToString(taddb_msg.mldb_dis_brd.mdlb[(int) index].no_of_lines);
            this.txtType.Text = taddb_msg.mldb_dis_brd.mdlb[(int) index].mldb_type;
            this.cmbEffect1.Text = taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[0] != (byte) 1 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[0] != (byte) 2 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[0] != (byte) 3 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[0] != (byte) 4 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[0] != (byte) 5 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[0] != (byte) 6 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[0] != (byte) 7 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[0] != (byte) 8 ? "Normal" : "Flashing") : "Running Bottom to Top") : "Running Top to Bottom") : "Running Right to Left") : "Typing Left to Right") : "Curtain Bottom to Top") : "Curtain Top to Bottom") : "Curtain Left to Right";
            this.cmbEffect2.Text = taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[1] != (byte) 1 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[1] != (byte) 2 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[1] != (byte) 3 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[1] != (byte) 4 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[1] != (byte) 5 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[1] != (byte) 6 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[1] != (byte) 7 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[1] != (byte) 8 ? "Normal" : "Flashing") : "Running Bottom to Top") : "Running Top to Bottom") : "Running Right to Left") : "Typing Left to Right") : "Curtain Bottom to Top") : "Curtain Top to Bottom") : "Curtain Left to Right";
            this.cmbEffect3.Text = taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[2] != (byte) 1 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[2] != (byte) 2 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[2] != (byte) 3 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[2] != (byte) 4 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[2] != (byte) 5 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[2] != (byte) 6 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[2] != (byte) 7 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[2] != (byte) 8 ? "Normal" : "Flashing") : "Running Bottom to Top") : "Running Top to Bottom") : "Running Right to Left") : "Typing Left to Right") : "Curtain Bottom to Top") : "Curtain Top to Bottom") : "Curtain Left to Right";
            this.cmbEffect4.Text = taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[3] != (byte) 1 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[3] != (byte) 2 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[3] != (byte) 3 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[3] != (byte) 4 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[3] != (byte) 5 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[3] != (byte) 6 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[3] != (byte) 7 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[3] != (byte) 8 ? "Normal" : "Flashing") : "Running Bottom to Top") : "Running Top to Bottom") : "Running Right to Left") : "Typing Left to Right") : "Curtain Bottom to Top") : "Curtain Top to Bottom") : "Curtain Left to Right";
            this.cmbEffect5.Text = taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[4] != (byte) 1 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[4] != (byte) 2 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[4] != (byte) 3 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[4] != (byte) 4 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[4] != (byte) 5 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[4] != (byte) 6 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[4] != (byte) 7 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[4] != (byte) 8 ? "Normal" : "Flashing") : "Running Bottom to Top") : "Running Top to Bottom") : "Running Right to Left") : "Typing Left to Right") : "Curtain Bottom to Top") : "Curtain Top to Bottom") : "Curtain Left to Right";
            if (taddb_msg.mldb_dis_brd.mdlb[(int) index].no_of_lines == (byte) 1)
            {
              this.cmbEffect1.Visible = true;
              this.cmbEffect2.Visible = false;
              this.cmbEffect3.Visible = false;
              this.cmbEffect4.Visible = false;
              this.cmbEffect5.Visible = false;
              this.lblLine1.Visible = true;
              this.lblLine2.Visible = false;
              this.lblLine3.Visible = false;
              this.lblLine4.Visible = false;
              this.lblLine5.Visible = false;
            }
            else if (taddb_msg.mldb_dis_brd.mdlb[(int) index].no_of_lines == (byte) 2)
            {
              this.cmbEffect1.Visible = true;
              this.cmbEffect2.Visible = true;
              this.cmbEffect3.Visible = false;
              this.cmbEffect4.Visible = false;
              this.cmbEffect5.Visible = false;
              this.lblLine1.Visible = true;
              this.lblLine2.Visible = true;
              this.lblLine3.Visible = false;
              this.lblLine4.Visible = false;
              this.lblLine5.Visible = false;
            }
            else if (taddb_msg.mldb_dis_brd.mdlb[(int) index].no_of_lines == (byte) 3)
            {
              this.cmbEffect1.Visible = true;
              this.cmbEffect2.Visible = true;
              this.cmbEffect3.Visible = true;
              this.cmbEffect4.Visible = false;
              this.cmbEffect5.Visible = false;
              this.lblLine1.Visible = true;
              this.lblLine2.Visible = true;
              this.lblLine3.Visible = true;
              this.lblLine4.Visible = false;
              this.lblLine5.Visible = false;
            }
            else if (taddb_msg.mldb_dis_brd.mdlb[(int) index].no_of_lines == (byte) 4)
            {
              this.cmbEffect1.Visible = true;
              this.cmbEffect2.Visible = true;
              this.cmbEffect3.Visible = true;
              this.cmbEffect4.Visible = true;
              this.cmbEffect5.Visible = false;
              this.lblLine1.Visible = true;
              this.lblLine2.Visible = true;
              this.lblLine3.Visible = true;
              this.lblLine4.Visible = true;
              this.lblLine5.Visible = false;
            }
            else
            {
              this.cmbEffect1.Visible = true;
              this.cmbEffect2.Visible = true;
              this.cmbEffect3.Visible = true;
              this.cmbEffect4.Visible = true;
              this.cmbEffect5.Visible = true;
              this.lblLine1.Visible = true;
              this.lblLine2.Visible = true;
              this.lblLine3.Visible = true;
              this.lblLine4.Visible = true;
              this.lblLine5.Visible = true;
            }
          }
          checked { ++index; }
        }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num3 = (int) basMsgBoxEx.MsgBoxMove(this.Handle.ToInt32(), exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void cmbAddress_SelectedIndexChanged(object sender, EventArgs e)
  {
    string empty1 = string.Empty;
    string empty2 = string.Empty;
    string empty3 = string.Empty;
    byte pdch_addr = 0;
    string empty4 = string.Empty;
    try
    {
      if (this.radAgdb.Checked)
      {
        if (this.chkAllPfno.Checked)
        {
          byte video = 0;
          network_db_read.agdb_com_addr_data(ref empty1, this.cmbAddress.Text, ref video);
          this.cmbName.Text = empty1;
          if (video == (byte) 0)
            this.cmbVideo.Text = "Normal";
          else
            this.cmbVideo.Text = "Reverse";
        }
        else
        {
          byte video = 0;
          bool shared_platform = false;
          network_db_read.agdb_addr_data(ref empty1, Conversions.ToByte(this.cmbAddress.Text), this.cmbPfno.Text, ref video, ref shared_platform, ref empty2);
          this.cmbName.Text = empty1;
          this.cmbVideo.Text = video != (byte) 0 ? "Reverse" : "Normal";
          if (shared_platform)
          {
            this.lblSharedPfno.Visible = true;
            this.txtSharedPfno.Visible = true;
            this.txtSharedPfno.Text = empty2;
          }
          else
          {
            this.lblSharedPfno.Visible = false;
            this.txtSharedPfno.Visible = false;
            this.txtSharedPfno.Text = string.Empty;
          }
        }
      }
      else if (this.radCgdb.Checked)
      {
        byte video = 0;
        network_db_read.cgdb_addr_data(ref empty1, this.cmbAddress.Text, this.cmbPfno.Text, ref pdch_addr, ref empty4, ref video, ref empty3);
        this.cmbName.Text = empty1;
        this.txtPDCHAddr.Text = Conversions.ToString(pdch_addr);
        this.txtPDCHName.Text = empty4;
        this.txtDir.Text = empty3;
        if (video == (byte) 0)
          this.cmbVideo.Text = "Normal";
        else
          this.cmbVideo.Text = "Reverse";
      }
      else
      {
        if (!this.radMldb.Checked)
          return;
        byte index = 0;
        while ((uint) index < (uint) taddb_msg.no_of_mldbs)
        {
          if ((double) taddb_msg.mldb_dis_brd.mdlb[(int) index].mldb_addr == Conversions.ToDouble(this.cmbAddress.Text))
          {
            this.cmbName.Text = taddb_msg.mldb_dis_brd.mdlb[(int) index].mldb_name;
            this.cmbVideo.Text = taddb_msg.mldb_dis_brd.mdlb[(int) index].video_type != (byte) 0 ? "Reverse" : "Normal";
            this.txtNoLines.Text = Conversions.ToString(taddb_msg.mldb_dis_brd.mdlb[(int) index].no_of_lines);
            this.txtType.Text = taddb_msg.mldb_dis_brd.mdlb[(int) index].mldb_type;
            this.cmbEffect1.Text = taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[0] != (byte) 1 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[0] != (byte) 2 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[0] != (byte) 3 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[0] != (byte) 4 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[0] != (byte) 5 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[0] != (byte) 6 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[0] != (byte) 7 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[0] != (byte) 8 ? "Normal" : "Flashing") : "Running Bottom to Top") : "Running Top to Bottom") : "Running Right to Left") : "Typing Left to Right") : "Curtain Bottom to Top") : "Curtain Top to Bottom") : "Curtain Left to Right";
            this.cmbEffect2.Text = taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[1] != (byte) 1 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[1] != (byte) 2 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[1] != (byte) 3 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[1] != (byte) 4 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[1] != (byte) 5 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[1] != (byte) 6 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[1] != (byte) 7 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[1] != (byte) 8 ? "Normal" : "Flashing") : "Running Bottom to Top") : "Running Top to Bottom") : "Running Right to Left") : "Typing Left to Right") : "Curtain Bottom to Top") : "Curtain Top to Bottom") : "Curtain Left to Right";
            this.cmbEffect3.Text = taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[2] != (byte) 1 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[2] != (byte) 2 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[2] != (byte) 3 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[2] != (byte) 4 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[2] != (byte) 5 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[2] != (byte) 6 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[2] != (byte) 7 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[2] != (byte) 8 ? "Normal" : "Flashing") : "Running Bottom to Top") : "Running Top to Bottom") : "Running Right to Left") : "Typing Left to Right") : "Curtain Bottom to Top") : "Curtain Top to Bottom") : "Curtain Left to Right";
            this.cmbEffect4.Text = taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[3] != (byte) 1 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[3] != (byte) 2 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[3] != (byte) 3 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[3] != (byte) 4 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[3] != (byte) 5 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[3] != (byte) 6 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[3] != (byte) 7 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[3] != (byte) 8 ? "Normal" : "Flashing") : "Running Bottom to Top") : "Running Top to Bottom") : "Running Right to Left") : "Typing Left to Right") : "Curtain Bottom to Top") : "Curtain Top to Bottom") : "Curtain Left to Right";
            this.cmbEffect5.Text = taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[4] != (byte) 1 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[4] != (byte) 2 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[4] != (byte) 3 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[4] != (byte) 4 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[4] != (byte) 5 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[4] != (byte) 6 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[4] != (byte) 7 ? (taddb_msg.mldb_dis_brd.mdlb[(int) index].effect[4] != (byte) 8 ? "Normal" : "Flashing") : "Running Bottom to Top") : "Running Top to Bottom") : "Running Right to Left") : "Typing Left to Right") : "Curtain Bottom to Top") : "Curtain Top to Bottom") : "Curtain Left to Right";
            if (taddb_msg.mldb_dis_brd.mdlb[(int) index].no_of_lines == (byte) 1)
            {
              this.cmbEffect1.Visible = true;
              this.cmbEffect2.Visible = false;
              this.cmbEffect3.Visible = false;
              this.cmbEffect4.Visible = false;
              this.cmbEffect5.Visible = false;
              this.lblLine1.Visible = true;
              this.lblLine2.Visible = false;
              this.lblLine3.Visible = false;
              this.lblLine4.Visible = false;
              this.lblLine5.Visible = false;
            }
            else if (taddb_msg.mldb_dis_brd.mdlb[(int) index].no_of_lines == (byte) 2)
            {
              this.cmbEffect1.Visible = true;
              this.cmbEffect2.Visible = true;
              this.cmbEffect3.Visible = false;
              this.cmbEffect4.Visible = false;
              this.cmbEffect5.Visible = false;
              this.lblLine1.Visible = true;
              this.lblLine2.Visible = true;
              this.lblLine3.Visible = false;
              this.lblLine4.Visible = false;
              this.lblLine5.Visible = false;
            }
            else if (taddb_msg.mldb_dis_brd.mdlb[(int) index].no_of_lines == (byte) 3)
            {
              this.cmbEffect1.Visible = true;
              this.cmbEffect2.Visible = true;
              this.cmbEffect3.Visible = true;
              this.cmbEffect4.Visible = false;
              this.cmbEffect5.Visible = false;
              this.lblLine1.Visible = true;
              this.lblLine2.Visible = true;
              this.lblLine3.Visible = true;
              this.lblLine4.Visible = false;
              this.lblLine5.Visible = false;
            }
            else if (taddb_msg.mldb_dis_brd.mdlb[(int) index].no_of_lines == (byte) 4)
            {
              this.cmbEffect1.Visible = true;
              this.cmbEffect2.Visible = true;
              this.cmbEffect3.Visible = true;
              this.cmbEffect4.Visible = true;
              this.cmbEffect5.Visible = false;
              this.lblLine1.Visible = true;
              this.lblLine2.Visible = true;
              this.lblLine3.Visible = true;
              this.lblLine4.Visible = true;
              this.lblLine5.Visible = false;
            }
            else
            {
              this.cmbEffect1.Visible = true;
              this.cmbEffect2.Visible = true;
              this.cmbEffect3.Visible = true;
              this.cmbEffect4.Visible = true;
              this.cmbEffect5.Visible = true;
              this.lblLine1.Visible = true;
              this.lblLine2.Visible = true;
              this.lblLine3.Visible = true;
              this.lblLine4.Visible = true;
              this.lblLine5.Visible = true;
            }
          }
          checked { ++index; }
        }
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(this.Handle.ToInt32(), exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void btnOk_Click(object sender, EventArgs e)
  {
    byte[] numArray = new byte[5];
    byte index1 = 0;
    byte index2 = 0;
    byte index3 = 0;
    byte index4 = 0;
    try
    {
      if (this.radAgdb.Checked)
      {
        if (Operators.CompareString(this.cmbAddress.Text, "", false) == 0 | Operators.CompareString(this.cmbName.Text, "", false) == 0)
        {
          int num = (int) basMsgBoxEx.MsgBoxMove(this.Handle.ToInt32(), "Select Name or Address", "Msg Box", 0, 0, 0);
          return;
        }
        byte num1 = Operators.CompareString(Strings.Trim(this.cmbVideo.Text), "Normal", false) != 0 ? (byte) 1 : (byte) 0;
        bool hub_type = false;
        network_db_read.agdb_get_details_db(Conversions.ToByte(this.cmbAddress.Text), this.cmbName.Text, ref index1, ref index2, ref index3, ref index4, ref hub_type);
        if (hub_type)
        {
          frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].line1effect = numArray[0];
          frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].video_type = num1;
        }
        else
        {
          frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].pdch_port[(int) index3].dis_board[(int) index4].line1effect = numArray[0];
          frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].pdch_port[(int) index3].dis_board[(int) index4].video_type = num1;
        }
      }
      else if (this.radCgdb.Checked)
      {
        byte num = Operators.CompareString(Strings.Trim(this.cmbVideo.Text), "Normal", false) != 0 ? (byte) 1 : (byte) 0;
        byte index5 = 0;
        while ((uint) index5 < (uint) frmDisplayBoardSettings.cgdb_cnt)
        {
          bool hub_type = false;
          network_db_read.cgdb_get_details_db(Conversions.ToByte(this.cmbAddress.Items[(int) index5]), Conversions.ToString(this.cmbName.Items[(int) index5]), ref hub_type, ref index1, ref index2, ref index3, ref index4);
          frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].pdch_port[(int) index3].dis_board[(int) index4].line1effect = numArray[0];
          frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].pdch_port[(int) index3].dis_board[(int) index4].video_type = num;
          checked { ++index5; }
        }
        network_db_read.get_cgdb_dis_brd_info();
      }
      else if (this.radMldb.Checked)
      {
        if (Operators.CompareString(this.cmbAddress.Text, "", false) == 0 | Operators.CompareString(this.cmbName.Text, "", false) == 0)
        {
          int num = (int) basMsgBoxEx.MsgBoxMove(this.Handle.ToInt32(), "Select Name or Address", "Msg Box", 0, 0, 0);
          return;
        }
        byte index6 = 0;
        while ((uint) index6 < (uint) taddb_msg.no_of_mldbs)
        {
          if ((double) taddb_msg.mldb_dis_brd.mdlb[(int) index6].mldb_addr == Conversions.ToDouble(this.cmbAddress.Text))
          {
            network_db_read.mldb_get_details_db(Conversions.ToByte(this.cmbAddress.Text), this.cmbName.Text, ref index1, ref index2);
            if (Operators.CompareString(this.cmbEffect1.Text, "Curtain Left to Right", false) == 0)
              frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].line1effect = (byte) 1;
            else if (Operators.CompareString(this.cmbEffect1.Text, "Curtain Top to Bottom", false) == 0)
              frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].line1effect = (byte) 2;
            else if (Operators.CompareString(this.cmbEffect1.Text, "Curtain Bottom to Top", false) == 0)
              frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].line1effect = (byte) 3;
            else if (Operators.CompareString(this.cmbEffect1.Text, "Typing Left to Right", false) == 0)
              frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].line1effect = (byte) 4;
            else if (Operators.CompareString(this.cmbEffect1.Text, "Running Right to Left", false) == 0)
              frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].line1effect = (byte) 5;
            else if (Operators.CompareString(this.cmbEffect1.Text, "Running Top to Bottom", false) == 0)
              frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].line1effect = (byte) 6;
            else if (Operators.CompareString(this.cmbEffect1.Text, "Running Bottom to Top", false) == 0)
              frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].line1effect = (byte) 7;
            else if (Operators.CompareString(this.cmbEffect1.Text, "Flashing", false) == 0)
              frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].line1effect = (byte) 8;
            else if (Operators.CompareString(this.cmbEffect1.Text, "Normal", false) == 0)
              frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].line1effect = (byte) 9;
            if (Operators.CompareString(this.cmbEffect2.Text, "Curtain Left to Right", false) == 0)
              frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].line2effect = (byte) 1;
            else if (Operators.CompareString(this.cmbEffect2.Text, "Curtain Top to Bottom", false) == 0)
              frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].line2effect = (byte) 2;
            else if (Operators.CompareString(this.cmbEffect2.Text, "Curtain Bottom to Top", false) == 0)
              frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].line2effect = (byte) 3;
            else if (Operators.CompareString(this.cmbEffect2.Text, "Typing Left to Right", false) == 0)
              frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].line2effect = (byte) 4;
            else if (Operators.CompareString(this.cmbEffect2.Text, "Running Right to Left", false) == 0)
              frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].line2effect = (byte) 5;
            else if (Operators.CompareString(this.cmbEffect2.Text, "Running Top to Bottom", false) == 0)
              frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].line2effect = (byte) 6;
            else if (Operators.CompareString(this.cmbEffect2.Text, "Running Bottom to Top", false) == 0)
              frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].line2effect = (byte) 7;
            else if (Operators.CompareString(this.cmbEffect2.Text, "Flashing", false) == 0)
              frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].line2effect = (byte) 8;
            else if (Operators.CompareString(this.cmbEffect2.Text, "Normal", false) == 0)
              frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].line2effect = (byte) 9;
            if (Operators.CompareString(this.cmbEffect3.Text, "Curtain Left to Right", false) == 0)
              frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].line3effect = (byte) 1;
            else if (Operators.CompareString(this.cmbEffect3.Text, "Curtain Top to Bottom", false) == 0)
              frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].line3effect = (byte) 2;
            else if (Operators.CompareString(this.cmbEffect3.Text, "Curtain Bottom to Top", false) == 0)
              frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].line3effect = (byte) 3;
            else if (Operators.CompareString(this.cmbEffect3.Text, "Typing Left to Right", false) == 0)
              frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].line3effect = (byte) 4;
            else if (Operators.CompareString(this.cmbEffect3.Text, "Running Right to Left", false) == 0)
              frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].line3effect = (byte) 5;
            else if (Operators.CompareString(this.cmbEffect3.Text, "Running Top to Bottom", false) == 0)
              frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].line3effect = (byte) 6;
            else if (Operators.CompareString(this.cmbEffect3.Text, "Running Bottom to Top", false) == 0)
              frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].line3effect = (byte) 7;
            else if (Operators.CompareString(this.cmbEffect3.Text, "Flashing", false) == 0)
              frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].line3effect = (byte) 8;
            else if (Operators.CompareString(this.cmbEffect3.Text, "Normal", false) == 0)
              frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].line3effect = (byte) 9;
            if (Operators.CompareString(this.cmbEffect4.Text, "Curtain Left to Right", false) == 0)
              frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].line4effect = (byte) 1;
            else if (Operators.CompareString(this.cmbEffect4.Text, "Curtain Top to Bottom", false) == 0)
              frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].line4effect = (byte) 2;
            else if (Operators.CompareString(this.cmbEffect4.Text, "Curtain Bottom to Top", false) == 0)
              frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].line4effect = (byte) 3;
            else if (Operators.CompareString(this.cmbEffect4.Text, "Typing Left to Right", false) == 0)
              frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].line4effect = (byte) 4;
            else if (Operators.CompareString(this.cmbEffect4.Text, "Running Right to Left", false) == 0)
              frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].line4effect = (byte) 5;
            else if (Operators.CompareString(this.cmbEffect4.Text, "Running Top to Bottom", false) == 0)
              frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].line4effect = (byte) 6;
            else if (Operators.CompareString(this.cmbEffect4.Text, "Running Bottom to Top", false) == 0)
              frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].line4effect = (byte) 7;
            else if (Operators.CompareString(this.cmbEffect4.Text, "Flashing", false) == 0)
              frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].line4effect = (byte) 8;
            else if (Operators.CompareString(this.cmbEffect4.Text, "Normal", false) == 0)
              frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].line4effect = (byte) 9;
            if (Operators.CompareString(this.cmbEffect5.Text, "Curtain Left to Right", false) == 0)
              frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].line5effect = (byte) 1;
            else if (Operators.CompareString(this.cmbEffect5.Text, "Curtain Top to Bottom", false) == 0)
              frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].line5effect = (byte) 2;
            else if (Operators.CompareString(this.cmbEffect5.Text, "Curtain Bottom to Top", false) == 0)
              frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].line5effect = (byte) 3;
            else if (Operators.CompareString(this.cmbEffect5.Text, "Typing Left to Right", false) == 0)
              frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].line5effect = (byte) 4;
            else if (Operators.CompareString(this.cmbEffect5.Text, "Running Right to Left", false) == 0)
              frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].line5effect = (byte) 5;
            else if (Operators.CompareString(this.cmbEffect5.Text, "Running Top to Bottom", false) == 0)
              frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].line5effect = (byte) 6;
            else if (Operators.CompareString(this.cmbEffect5.Text, "Running Bottom to Top", false) == 0)
              frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].line5effect = (byte) 7;
            else if (Operators.CompareString(this.cmbEffect5.Text, "Flashing", false) == 0)
              frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].line5effect = (byte) 8;
            else if (Operators.CompareString(this.cmbEffect5.Text, "Normal", false) == 0)
              frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].line5effect = (byte) 9;
            frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].video_type = Operators.CompareString(Strings.Trim(this.cmbVideo.Text), "Normal", false) != 0 ? (byte) 1 : (byte) 0;
            frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].effect_speed = Convert.ToByte(this.numEffectSpeed.Value);
          }
          checked { ++index6; }
        }
      }
      else if (this.radPdb.Checked)
      {
        if (Operators.CompareString(this.cmbEffect1.Text, "Curtain Left to Right", false) == 0)
          numArray[0] = (byte) 1;
        else if (Operators.CompareString(this.cmbEffect1.Text, "Curtain Top to Bottom", false) == 0)
          numArray[0] = (byte) 2;
        else if (Operators.CompareString(this.cmbEffect1.Text, "Curtain Bottom to Top", false) == 0)
          numArray[0] = (byte) 3;
        else if (Operators.CompareString(this.cmbEffect1.Text, "Typing Left to Right", false) == 0)
          numArray[0] = (byte) 4;
        else if (Operators.CompareString(this.cmbEffect1.Text, "Running Right to Left", false) == 0)
          numArray[0] = (byte) 5;
        else if (Operators.CompareString(this.cmbEffect1.Text, "Running Top to Bottom", false) == 0)
          numArray[0] = (byte) 6;
        else if (Operators.CompareString(this.cmbEffect1.Text, "Running Bottom to Top", false) == 0)
          numArray[0] = (byte) 7;
        else if (Operators.CompareString(this.cmbEffect1.Text, "Flashing", false) == 0)
          numArray[0] = (byte) 8;
        else if (Operators.CompareString(this.cmbEffect1.Text, "Normal", false) == 0)
          numArray[0] = (byte) 9;
        byte num2 = Operators.CompareString(Strings.Trim(this.cmbVideo.Text), "Normal", false) != 0 ? (byte) 1 : (byte) 0;
        byte pfnoInt = network_db_read.get_pfno_int(this.cmbPfno.Text);
        if (pfnoInt != (byte) 0)
          checked { --pfnoInt; }
        if (!taddb_msg.pdb_pf_status[(int) pfnoInt])
        {
          int num3 = (int) basMsgBoxEx.MsgBoxMove(this.Handle.ToInt32(), "Platform NO doesn't exist", "Msg Box", 0, 0, 0);
          return;
        }
        bool hub_type = false;
        network_db_read.pdb_get_details_db(taddb_msg.pdb_dis_brd[(int) pfnoInt, 0].multicast_addr, taddb_msg.pdb_dis_brd[(int) pfnoInt, 0].pdb_name, ref hub_type, ref index1, ref index2, ref index3, ref index4);
        if (hub_type)
        {
          frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].line1effect = numArray[0];
          frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].video_type = num2;
          frmMainFormIPIS.mdch_db.mdch_port[(int) index1].dis_board[(int) index2].effect_speed = Convert.ToByte(this.numEffectSpeed.Value);
        }
        else
        {
          frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].pdch_port[(int) index3].dis_board[(int) index4].line1effect = numArray[0];
          frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].pdch_port[(int) index3].dis_board[(int) index4].video_type = num2;
          frmMainFormIPIS.mdch_db.mdch_port[(int) index1].pdch[(int) index2].pdch_port[(int) index3].dis_board[(int) index4].effect_speed = Convert.ToByte(this.numEffectSpeed.Value);
        }
      }
      lock (this)
      {
        network_db_read.set_nw_database();
        int num = (int) basMsgBoxEx.MsgBoxMove(this.Handle.ToInt32(), "Information updated", "Msg Box", 0, 0, 0);
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(this.Handle.ToInt32(), exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void btnExit_Click(object sender, EventArgs e)
{
  this.Close();
}

  private void chkAllPfno_CheckedChanged(object sender, EventArgs e)
  {
    byte[] agdb_addrs = new byte[26];
    string[] agdb_names = new string[26];
    try
    {
      if (this.chkAllPfno.Checked)
      {
        this.cmbPfno.Text = string.Empty;
        this.cmbPfno.Enabled = false;
        byte cunt = 0;
        this.cmbAddress.Items.Clear();
        this.cmbName.Items.Clear();
        this.cmbVideo.Text = "";
        if (this.radAgdb.Checked)
        {
          if (network_db_read.agdb_com_info_data(ref agdb_names, ref agdb_addrs, ref cunt) == (byte) 0)
          {
            int num = (int) basMsgBoxEx.MsgBoxMove(this.Handle.ToInt32(), "No address assigned to  Common AGDB\r\nPlease check network configuration", "Msg Box", 0, 0, 0);
          }
          else
          {
            int index = 0;
            while (index < (int) cunt)
            {
              this.cmbAddress.Items.Add((object) agdb_addrs[index]);
              this.cmbName.Items.Add((object) agdb_names[index]);
              checked { ++index; }
            }
          }
        }
      }
      else if (!this.chkAllPfno.Checked)
        this.cmbPfno.Enabled = true;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(this.Handle.ToInt32(), exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void frmDisplayBoardSettings_Load(object sender, EventArgs e)
  {
    int index = 0;
    this.cmbPfno.Items.Clear();
    while (index < frmMainFormIPIS.pfno_cnt)
    {
      this.cmbPfno.Items.Add((object) frmMainFormIPIS.platform_nos[index]);
      checked { ++index; }
    }
  }
}

}