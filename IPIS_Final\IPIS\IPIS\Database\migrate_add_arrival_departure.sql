-- Migration script to add ArrivalDeparture column to AnnouncementTemplates table
-- Run this script to update existing databases

-- Add ArrivalDeparture column if it doesn't exist
ALTER TABLE AnnouncementTemplates ADD COLUMN ArrivalDeparture TEXT NOT NULL DEFAULT 'A';

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_announcement_templates_ad ON AnnouncementTemplates(ArrivalDeparture);

-- Update existing templates to have appropriate A/D values
UPDATE AnnouncementTemplates SET ArrivalDeparture = 'A' WHERE Name IN (
    'IS ARRIVING ON',
    'HAS ARRIVED ON',
    'WILL ARRIVE SHORTLY',
    'RUNNING RIGHT TIME',
    'RUNNING LATE',
    'INDEFINITE LATE',
    'CANCELLED',
    'PLATFORM CHANGED',
    'TERMINATED'
);

UPDATE AnnouncementTemplates SET ArrivalDeparture = 'D' WHERE Name IN (
    'DEPARTING'
);

-- Insert additional departure templates if they don't exist
INSERT OR IGNORE INTO AnnouncementTemplates (Name, Description, ArrivalDeparture) VALUES
('WILL DEPART SHORTLY', 'Train will depart shortly from platform', 'D'),
('HAS DEPARTED', 'Train has departed from platform', 'D'),
('DEPARTURE DELAYED', 'Train departure is delayed', 'D'); 