using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ipis_V2_jules.DisplayFormatters; // For MldbDataFormatter, FormattedDisplayData
using ipis_V2_jules.Services.DisplayBoard.Hardware.Protocols; // For DisplayPacketBuilder
using ipis_V2_jules.Services.DisplayBoard.Hardware.Communication; // For ICommunicationService
using ipis_V2_jules.Models; // For DisplayBoardConfig
using ipis_V2_jules.ApiClients; // For TrainDataErail

// Ensure MldbDataFormatter is in the correct namespace, adjust if it was also moved to the new structure
// Assuming MldbDataFormatter is in ipis_V2_jules.Services.DisplayBoard.DisplayFormatters (from Turn 51-53)


namespace ipis_V2_jules.Services.DisplayBoard.Hardware.Clients
{
    public class MldbClient : IBoardClient
    {
        private readonly ICommunicationService _communicationService;
        private readonly MldbDataFormatter _dataFormatter; // Specific formatter
        private readonly DisplayBoardConfig _boardConfig;

        public BoardStatus Status { get; private set; }

        // Placeholder command codes for sending line data to MLDB
        // These might vary based on actual MLDB protocol.
        // For example, 0x31 could mean "display data on line 1", 0x32 for line 2,
        // or a general display command is used and the board handles line logic.
        // The DisplayPacketBuilder.CMD_MLDB_DISPLAY_TEXT (0x31) is a general one.
        // Let's assume for now specific command codes per line.
        private const byte CMD_MLDB_DISPLAY_LINE_1 = 0x41; // Placeholder 'A' for Line 1
        private const byte CMD_MLDB_DISPLAY_LINE_2 = 0x42; // Placeholder 'B' for Line 2
        private const byte CMD_MLDB_DISPLAY_LINE_3 = 0x43; // Placeholder 'C' for Line 3 (if MLDB supports 3 lines)


        public MldbClient(ICommunicationService communicationService,
                          MldbDataFormatter dataFormatter,
                          DisplayBoardConfig boardConfig)
        {
            _communicationService = communicationService ?? throw new ArgumentNullException(nameof(communicationService));
            _dataFormatter = dataFormatter ?? throw new ArgumentNullException(nameof(dataFormatter));
            _boardConfig = boardConfig ?? throw new ArgumentNullException(nameof(boardConfig));
            Status = new BoardStatus { IsLinkOk = false, StatusMessage = "Initialized", FirmwareVersion = "N/A" };
        }

        private Dictionary<string, string> GetBoardConfigAsDictionary()
        {
            // Ensure all relevant properties from _boardConfig are passed to the formatter
            return _boardConfig.ToDictionary();
        }

        /// <summary>
        /// Core method to send formatted line data to the MLDB.
        /// </summary>
        private async Task<bool> SendSingleLineDataAsync(byte[] lineBitmapData, byte lineCommandCode)
        {
            if (lineBitmapData == null || lineBitmapData.Length == 0)
            {
                // This might be valid if clearing a line or if a line is intentionally blank.
                // However, if it's unexpected, log a warning.
                Console.WriteLine($"MLDB Client ({_boardConfig.BoardName}): No bitmap data provided for line command {lineCommandCode}, sending empty payload if protocol requires.");
                // Some protocols might expect a command even for an empty line.
                // If an empty payload is not allowed, return false or throw.
                // For now, let's assume an empty payload is sent.
                lineBitmapData = Array.Empty<byte>();
            }

            // Using the new BuildMldbBitmapDisplayPacket from DisplayPacketBuilder
            byte[] packet = DisplayPacketBuilder.BuildMldbBitmapDisplayPacket(
                _boardConfig.BoardId,
                lineCommandCode,
                lineBitmapData
            );

            try
            {
                await _communicationService.WriteDataAsync(packet);
                // Status update should reflect the overall message status, not per line.
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"MLDB Client ({_boardConfig.BoardName}) Error: Failed to send line data (Cmd:{lineCommandCode}). Exception: {ex.Message}");
                // Set overall status to failed on any line failure
                Status = new BoardStatus { IsLinkOk = false, StatusMessage = $"Send line (Cmd:{lineCommandCode}) failed: {ex.Message}", FirmwareVersion = Status.FirmwareVersion };
                return false;
            }
        }

        // IBoardClient.SendMessageAsync implementation
        public async Task<bool> SendMessageAsync(FormattedDisplayData data, byte boardAddress, byte subAddress, byte serialNo)
        {
            if (boardAddress != _boardConfig.BoardId)
            {
                Console.WriteLine($"MLDB Client ({_boardConfig.BoardName}) Error: Mismatched boardAddress ({boardAddress}). Expected {_boardConfig.BoardId}.");
                Status = new BoardStatus { IsLinkOk = false, StatusMessage = "Config error: Board ID mismatch.", FirmwareVersion = Status.FirmwareVersion };
                return false;
            }
            // subAddress and serialNo are not used in this MLDB client's current packet building logic.
            // They could be incorporated into AdditionalHeaderBytes by the formatter if needed.

            bool overallSuccess = true;
            // Send Line 1 data if available
            if (data.Line1 != null)
            {
                Console.WriteLine($"MLDB Client ({_boardConfig.BoardName}): Sending Line 1 data.");
                if (!await SendSingleLineDataAsync(data.Line1, CMD_MLDB_DISPLAY_LINE_1)) overallSuccess = false;
            }

            // Send Line 2 data if available and overall success so far
            if (overallSuccess && data.Line2 != null)
            {
                Console.WriteLine($"MLDB Client ({_boardConfig.BoardName}): Sending Line 2 data.");
                if (!await SendSingleLineDataAsync(data.Line2, CMD_MLDB_DISPLAY_LINE_2)) overallSuccess = false;
            }

            // Send Line 3 data if available and overall success so far
            if (overallSuccess && data.Line3 != null)
            {
                Console.WriteLine($"MLDB Client ({_boardConfig.BoardName}): Sending Line 3 data.");
                if (!await SendSingleLineDataAsync(data.Line3, CMD_MLDB_DISPLAY_LINE_3)) overallSuccess = false;
            }

            // Update final status
            if (overallSuccess)
            {
                Status = new BoardStatus { IsLinkOk = true, StatusMessage = "Message sent successfully.", FirmwareVersion = Status.FirmwareVersion };
            }
            // If any SendSingleLineDataAsync failed, it would have already updated Status with an error.

            return overallSuccess;
        }

        public async Task<bool> SendMessageAsync(string message)
        {
            if (string.IsNullOrEmpty(message)) return false;
            Console.WriteLine($"MLDB Client ({_boardConfig.BoardName}): Formatting and sending message: \"{message.Substring(0, Math.Min(message.Length, 20))}...\"");
            FormattedDisplayData formattedData = _dataFormatter.FormatMessage(message, GetBoardConfigAsDictionary());
            return await SendMessageAsync(formattedData, _boardConfig.BoardId, 0, 0);
        }

        public async Task<bool> UpdateTrainDisplayAsync(TrainDataErail trainData, Dictionary<string, string> platformInfo)
        {
            if (trainData == null) return false;
            Console.WriteLine($"MLDB Client ({_boardConfig.BoardName}): Formatting and sending train data for {trainData.TrainNo}");
            FormattedDisplayData formattedData = _dataFormatter.FormatTrainData(trainData, platformInfo, GetBoardConfigAsDictionary());
            return await SendMessageAsync(formattedData, _boardConfig.BoardId, 0, 0);
        }

        public async Task<bool> ClearDisplayAsync() => await ClearDisplayAsync(_boardConfig.BoardId, 0, 0);

        public async Task<bool> ClearDisplayAsync(byte boardAddress, byte subAddress, byte serialNo)
        {
            if (boardAddress != _boardConfig.BoardId)
            {
                 Console.WriteLine($"MLDB Client ({_boardConfig.BoardName}) Error: Mismatched boardAddress ({boardAddress}) for clear.");
                 Status = new BoardStatus { IsLinkOk = false, StatusMessage = "Config error: Board ID mismatch for clear.", FirmwareVersion = Status.FirmwareVersion };
                 return false;
            }
            Console.WriteLine($"MLDB Client ({_boardConfig.BoardName}): Clearing display.");
            byte[] packet = DisplayPacketBuilder.BuildMldbClearScreenPacket(_boardConfig.BoardId);
            try
            {
                await _communicationService.WriteDataAsync(packet);
                Status = new BoardStatus { IsLinkOk = true, StatusMessage = "Display cleared.", FirmwareVersion = Status.FirmwareVersion };
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"MLDB Client ({_boardConfig.BoardName}) Error: Failed to clear display. Exception: {ex.Message}");
                Status = new BoardStatus { IsLinkOk = false, StatusMessage = $"Clear failed: {ex.Message}", FirmwareVersion = Status.FirmwareVersion };
                return false;
            }
        }

        public async Task<BoardStatus> CheckLinkAsync() => await CheckLinkAsync(_boardConfig.BoardId, 0, 0);

        public async Task<BoardStatus> CheckLinkAsync(byte boardAddress, byte subAddress, byte serialNo)
        {
             if (boardAddress != _boardConfig.BoardId)
            {
                 Console.WriteLine($"MLDB Client ({_boardConfig.BoardName}) Error: Mismatched boardAddress ({boardAddress}) for link check.");
                 Status = new BoardStatus { IsLinkOk = false, StatusMessage = "Config error: Board ID mismatch for link check.", FirmwareVersion = Status.FirmwareVersion };
                 return Status;
            }
            Console.WriteLine($"MLDB Client ({_boardConfig.BoardName}): Checking link.");
            byte[] packet = DisplayPacketBuilder.BuildLinkCheckPacket(_boardConfig.BoardId);
            try
            {
                await _communicationService.WriteDataAsync(packet);
                // TODO: Implement response reading for link check.
                Status = new BoardStatus { IsLinkOk = true, StatusMessage = "Link check sent (response check not implemented).", FirmwareVersion = Status.FirmwareVersion };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"MLDB Client ({_boardConfig.BoardName}) Error: Failed to send link check. Exception: {ex.Message}");
                Status = new BoardStatus { IsLinkOk = false, StatusMessage = $"Link check send failed: {ex.Message}", FirmwareVersion = Status.FirmwareVersion };
            }
            return Status;
        }

        // Stub implementations for other IBoardClient methods
        public async Task<bool> SetConfigurationAsync(byte boardAddress, byte subAddress, byte serialNo, byte[] configData)
        {
            Console.WriteLine($"MLDB Client ({_boardConfig.BoardName}): SetConfigurationAsync - NOT IMPLEMENTED.");
            await Task.CompletedTask;
            Status = new BoardStatus { IsLinkOk = Status.IsLinkOk, StatusMessage = "SetConfiguration not implemented.", FirmwareVersion = Status.FirmwareVersion };
            return false;
        }

        public async Task<byte[]> GetConfigurationAsync(byte boardAddress, byte subAddress, byte serialNo)
        {
            Console.WriteLine($"MLDB Client ({_boardConfig.BoardName}): GetConfigurationAsync - NOT IMPLEMENTED.");
            await Task.CompletedTask;
            Status = new BoardStatus { IsLinkOk = Status.IsLinkOk, StatusMessage = "GetConfiguration not implemented.", FirmwareVersion = Status.FirmwareVersion };
            return Array.Empty<byte>();
        }

        public async Task<bool> ResetBoardAsync(byte boardAddress, byte subAddress, byte serialNo)
        {
            Console.WriteLine($"MLDB Client ({_boardConfig.BoardName}): ResetBoardAsync - NOT IMPLEMENTED.");
            await Task.CompletedTask;
            Status = new BoardStatus { IsLinkOk = Status.IsLinkOk, StatusMessage = "ResetBoard not implemented.", FirmwareVersion = Status.FirmwareVersion };
            return false;
        }
    }
}
