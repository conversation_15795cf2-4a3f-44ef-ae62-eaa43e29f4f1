﻿using System;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Windows.Forms;

namespace Announcement
{
	// Token: 0x0200000D RID: 13
	public partial class Station_Details : Form
	{
		// Token: 0x06000061 RID: 97 RVA: 0x0000DF9C File Offset: 0x0000C19C
		public Station_Details()
		{
			this.InitializeComponent();
		}

		// Token: 0x06000062 RID: 98 RVA: 0x0000DFD0 File Offset: 0x0000C1D0
		private void BTN_Add_Click(object sender, EventArgs e)
		{
			this.BTN_Save.Enabled = true;
			this.BTN_Edit.Enabled = false;
			this.TB_SName.Enabled = false;
			this.TB_SC.Enabled = false;
			this.Num_PF.Enabled = true;
			this.CHH_AL.Enabled = true;
			this.CHH_AD.Enabled = true;
			this.GB_1.Enabled = true;
			this.GB_PFC.Enabled = true;
			this.Grp_Lang.Enabled = true;
			this.Grp_Ann.Enabled = true;
		}

		// Token: 0x06000063 RID: 99 RVA: 0x000025C4 File Offset: 0x000007C4
		private void button2_Click(object sender, EventArgs e)
		{
			base.Close();
		}

		// Token: 0x06000064 RID: 100 RVA: 0x000025C1 File Offset: 0x000007C1
		private void BTN_Config_Click(object sender, EventArgs e)
		{
		}

		// Token: 0x06000065 RID: 101 RVA: 0x0000E070 File Offset: 0x0000C270
		private void BTN_Edit_Click(object sender, EventArgs e)
		{
			this.Flag_Edit = true;
			this.BTN_Save.Enabled = true;
			this.BTN_Add.Enabled = false;
			this.Num_PF.Enabled = true;
			this.GB_1.Enabled = true;
			this.GB_PFC.Enabled = true;
			this.Grp_Lang.Enabled = true;
			this.Grp_Ann.Enabled = true;
			this.BTN_Del.Enabled = true;
		}

		// Token: 0x06000066 RID: 102 RVA: 0x000025C1 File Offset: 0x000007C1
		private void CHH_AL_CheckedChanged(object sender, EventArgs e)
		{
		}

		// Token: 0x06000067 RID: 103 RVA: 0x0000E0F0 File Offset: 0x0000C2F0
		private void BTN_Save_Click(object sender, EventArgs e)
		{
			bool flag = this.TB_SName.Text == "";
			if (flag)
			{
				MessageBox.Show("Enter Station Name");
			}
			else
			{
				bool flag2 = this.TB_SC.Text == "";
				if (flag2)
				{
					MessageBox.Show("Enter Station Code");
				}
				else
				{
					this.DB.Insert_Database("DELETE * From Station_Details WHERE Station_Name = '" + this.TB_SName.Text + "'");
					this.DB.Insert_Database(string.Concat(new string[]
					{
						"INSERT INTO Station_Details (Station_Name,Station_Code,Auto_Load,AutoLoad_Interval,AutoDelete_Interval,AutoDeletePost_Interval,Auto_Delete,Avilable_PF,P1,P2,P3,P4,P5,P6,P7,P8,P9,P10,Lang1_Enb,Lang2_Enb,First_Lang,Second_Lang,English,Hindi) VALUES('",
						this.TB_SName.Text,
						"','",
						this.TB_SC.Text,
						"','",
						this.CHH_AL.Checked.ToString(),
						"','",
						this.Num_LoadInterval.Value.ToString(),
						"','",
						this.Num_DeleteInterval.Value.ToString(),
						"','",
						this.Num_AutodeletePost.Value.ToString(),
						"','",
						this.CHH_AD.Checked.ToString(),
						"','",
						this.Num_PF.Text,
						"','",
						this.TB_1.Text,
						"','",
						this.TB_2.Text,
						"','",
						this.TB_3.Text,
						"','",
						this.TB_4.Text,
						"','",
						this.TB_5.Text,
						"','",
						this.TB_6.Text,
						"','",
						this.TB_7.Text,
						"','",
						this.TB_8.Text,
						"','",
						this.TB_9.Text,
						"','",
						this.TB_10.Text,
						"','",
						this.Chk_AnnFirst.Checked.ToString(),
						"','",
						this.Chk_AnnSecond.Checked.ToString(),
						"','",
						this.Cmb_AnnLang1.Text,
						"','",
						this.Cmb_AnnLang2.Text,
						"','",
						this.Chk_LangEng.Checked.ToString(),
						"','",
						this.Chk_LangHnd.Checked.ToString(),
						"')"
					}));
					MessageBox.Show("Station Data Saved Sucessfully");
					this.BTN_Save.Enabled = false;
					this.BTN_Add.Enabled = true;
					this.BTN_Edit.Enabled = true;
					this.GB_1.Enabled = false;
					this.GB_PFC.Enabled = false;
					this.TB_SName.Enabled = false;
					this.Num_PF.Enabled = false;
					this.TB_SC.Enabled = false;
					this.Grp_Lang.Enabled = false;
					this.Grp_Ann.Enabled = false;
					MessageBox.Show("Application Restrat is required to Apply the changes...Application will restart now");
					Application.Restart();
				}
			}
		}

		// Token: 0x06000068 RID: 104 RVA: 0x0000E4B8 File Offset: 0x0000C6B8
		private void Station_Details_Load(object sender, EventArgs e)
		{
			this.DTable = new DataTable();
			this.DTable = this.DB.Read_Database("Select * From Station_Details");
			bool flag = this.DTable.Rows.Count > 0;
			if (flag)
			{
				this.TB_SName.Text = this.DTable.Rows[0]["Station_Name"].ToString();
				this.TB_SC.Text = this.DTable.Rows[0]["Station_Code"].ToString();
				this.Num_PF.Text = this.DTable.Rows[0]["Avilable_PF"].ToString();
				this.Cmb_AnnLang1.Text = this.DTable.Rows[0]["First_Lang"].ToString();
				this.Cmb_AnnLang2.Text = this.DTable.Rows[0]["Second_Lang"].ToString();
				this.TB_1.Text = this.DTable.Rows[0]["P1"].ToString();
				this.TB_2.Text = this.DTable.Rows[0]["P2"].ToString();
				this.TB_3.Text = this.DTable.Rows[0]["P3"].ToString();
				this.TB_4.Text = this.DTable.Rows[0]["P4"].ToString();
				this.TB_5.Text = this.DTable.Rows[0]["P5"].ToString();
				this.TB_6.Text = this.DTable.Rows[0]["P6"].ToString();
				this.TB_7.Text = this.DTable.Rows[0]["P7"].ToString();
				this.TB_8.Text = this.DTable.Rows[0]["P8"].ToString();
				this.TB_9.Text = this.DTable.Rows[0]["P9"].ToString();
				this.TB_10.Text = this.DTable.Rows[0]["P10"].ToString();
				this.Num_PF.Text = this.DTable.Rows[0]["Avilable_PF"].ToString();
				try
				{
					this.Num_LoadInterval.Value = int.Parse(this.DTable.Rows[0]["AutoLoad_interval"].ToString());
					this.Num_DeleteInterval.Value = int.Parse(this.DTable.Rows[0]["AutoDelete_interval"].ToString());
					this.Num_AutodeletePost.Value = int.Parse(this.DTable.Rows[0]["AutoDeletePost_interval"].ToString());
				}
				catch
				{
				}
				bool flag2 = this.DTable.Rows[0]["Auto_Load"].ToString() == "True";
				if (flag2)
				{
					this.CHH_AL.Checked = true;
				}
				else
				{
					this.CHH_AL.Checked = false;
				}
				bool flag3 = this.DTable.Rows[0]["Auto_Delete"].ToString() == "True";
				if (flag3)
				{
					this.CHH_AD.Checked = true;
				}
				else
				{
					this.CHH_AD.Checked = false;
				}
				bool flag4 = this.DTable.Rows[0]["English"].ToString() == "True";
				if (flag4)
				{
					this.Chk_LangEng.Checked = true;
				}
				else
				{
					this.Chk_LangEng.Checked = false;
				}
				bool flag5 = this.DTable.Rows[0]["Hindi"].ToString() == "True";
				if (flag5)
				{
					this.Chk_LangHnd.Checked = true;
				}
				else
				{
					this.Chk_LangHnd.Checked = false;
				}
				bool flag6 = this.DTable.Rows[0]["Lang1_Enb"].ToString() == "True";
				if (flag6)
				{
					this.Chk_AnnFirst.Checked = true;
				}
				else
				{
					this.Chk_AnnFirst.Checked = false;
				}
				bool flag7 = this.DTable.Rows[0]["Lang2_Enb"].ToString() == "True";
				if (flag7)
				{
					this.Chk_AnnSecond.Checked = true;
				}
				else
				{
					this.Chk_AnnSecond.Checked = false;
				}
			}
		}

		// Token: 0x06000069 RID: 105 RVA: 0x000025C1 File Offset: 0x000007C1
		private void TB_SName_TextChanged(object sender, EventArgs e)
		{
		}

		// Token: 0x0600006A RID: 106 RVA: 0x000025C1 File Offset: 0x000007C1
		private void Num_PF_ValueChanged(object sender, EventArgs e)
		{
		}

		// Token: 0x0600006B RID: 107 RVA: 0x0000EA44 File Offset: 0x0000CC44
		private void Num_PF_SelectedIndexChanged(object sender, EventArgs e)
		{
			bool flag = this.Num_PF.Text == "1";
			if (flag)
			{
				this.TB_1.Text = "";
				this.TB_2.Text = "";
				this.TB_3.Text = "";
				this.TB_4.Text = "";
				this.TB_5.Text = "";
				this.TB_6.Text = "";
				this.TB_7.Text = "";
				this.TB_8.Text = "";
				this.TB_9.Text = "";
				this.TB_10.Text = "";
				this.TB_1.Enabled = true;
				this.TB_2.Enabled = false;
				this.TB_3.Enabled = false;
				this.TB_4.Enabled = false;
				this.TB_5.Enabled = false;
				this.TB_6.Enabled = false;
				this.TB_7.Enabled = false;
				this.TB_8.Enabled = false;
				this.TB_9.Enabled = false;
				this.TB_10.Enabled = false;
			}
			else
			{
				bool flag2 = this.Num_PF.Text == "2";
				if (flag2)
				{
					this.TB_1.Text = "";
					this.TB_2.Text = "";
					this.TB_3.Text = "";
					this.TB_4.Text = "";
					this.TB_5.Text = "";
					this.TB_6.Text = "";
					this.TB_7.Text = "";
					this.TB_8.Text = "";
					this.TB_9.Text = "";
					this.TB_10.Text = "";
					this.TB_1.Enabled = true;
					this.TB_2.Enabled = true;
					this.TB_3.Enabled = false;
					this.TB_4.Enabled = false;
					this.TB_5.Enabled = false;
					this.TB_6.Enabled = false;
					this.TB_7.Enabled = false;
					this.TB_8.Enabled = false;
					this.TB_9.Enabled = false;
					this.TB_10.Enabled = false;
				}
				else
				{
					bool flag3 = this.Num_PF.Text == "3";
					if (flag3)
					{
						this.TB_1.Text = "";
						this.TB_2.Text = "";
						this.TB_3.Text = "";
						this.TB_4.Text = "";
						this.TB_5.Text = "";
						this.TB_6.Text = "";
						this.TB_7.Text = "";
						this.TB_8.Text = "";
						this.TB_9.Text = "";
						this.TB_10.Text = "";
						this.TB_1.Enabled = true;
						this.TB_2.Enabled = true;
						this.TB_3.Enabled = true;
						this.TB_4.Enabled = false;
						this.TB_5.Enabled = false;
						this.TB_6.Enabled = false;
						this.TB_7.Enabled = false;
						this.TB_8.Enabled = false;
						this.TB_9.Enabled = false;
						this.TB_10.Enabled = false;
					}
					else
					{
						bool flag4 = this.Num_PF.Text == "4";
						if (flag4)
						{
							this.TB_1.Text = "";
							this.TB_2.Text = "";
							this.TB_3.Text = "";
							this.TB_4.Text = "";
							this.TB_5.Text = "";
							this.TB_6.Text = "";
							this.TB_7.Text = "";
							this.TB_8.Text = "";
							this.TB_9.Text = "";
							this.TB_10.Text = "";
							this.TB_1.Enabled = true;
							this.TB_2.Enabled = true;
							this.TB_3.Enabled = true;
							this.TB_4.Enabled = true;
							this.TB_5.Enabled = false;
							this.TB_6.Enabled = false;
							this.TB_7.Enabled = false;
							this.TB_8.Enabled = false;
							this.TB_9.Enabled = false;
							this.TB_10.Enabled = false;
						}
						else
						{
							bool flag5 = this.Num_PF.Text == "5";
							if (flag5)
							{
								this.TB_1.Text = "";
								this.TB_2.Text = "";
								this.TB_3.Text = "";
								this.TB_4.Text = "";
								this.TB_5.Text = "";
								this.TB_6.Text = "";
								this.TB_7.Text = "";
								this.TB_8.Text = "";
								this.TB_9.Text = "";
								this.TB_10.Text = "";
								this.TB_1.Enabled = true;
								this.TB_2.Enabled = true;
								this.TB_3.Enabled = true;
								this.TB_4.Enabled = true;
								this.TB_5.Enabled = true;
								this.TB_6.Enabled = false;
								this.TB_7.Enabled = false;
								this.TB_8.Enabled = false;
								this.TB_9.Enabled = false;
								this.TB_10.Enabled = false;
							}
							else
							{
								bool flag6 = this.Num_PF.Text == "6";
								if (flag6)
								{
									this.TB_1.Text = "";
									this.TB_2.Text = "";
									this.TB_3.Text = "";
									this.TB_4.Text = "";
									this.TB_5.Text = "";
									this.TB_6.Text = "";
									this.TB_7.Text = "";
									this.TB_8.Text = "";
									this.TB_9.Text = "";
									this.TB_10.Text = "";
									this.TB_1.Enabled = true;
									this.TB_2.Enabled = true;
									this.TB_3.Enabled = true;
									this.TB_4.Enabled = true;
									this.TB_5.Enabled = true;
									this.TB_6.Enabled = true;
									this.TB_7.Enabled = false;
									this.TB_8.Enabled = false;
									this.TB_9.Enabled = false;
									this.TB_10.Enabled = false;
								}
								else
								{
									bool flag7 = this.Num_PF.Text == "7";
									if (flag7)
									{
										this.TB_1.Text = "";
										this.TB_2.Text = "";
										this.TB_3.Text = "";
										this.TB_4.Text = "";
										this.TB_5.Text = "";
										this.TB_6.Text = "";
										this.TB_7.Text = "";
										this.TB_8.Text = "";
										this.TB_9.Text = "";
										this.TB_10.Text = "";
										this.TB_1.Enabled = true;
										this.TB_2.Enabled = true;
										this.TB_3.Enabled = true;
										this.TB_4.Enabled = true;
										this.TB_5.Enabled = true;
										this.TB_6.Enabled = true;
										this.TB_7.Enabled = true;
										this.TB_8.Enabled = false;
										this.TB_9.Enabled = false;
										this.TB_10.Enabled = false;
									}
									else
									{
										bool flag8 = this.Num_PF.Text == "8";
										if (flag8)
										{
											this.TB_1.Text = "";
											this.TB_2.Text = "";
											this.TB_3.Text = "";
											this.TB_4.Text = "";
											this.TB_5.Text = "";
											this.TB_6.Text = "";
											this.TB_7.Text = "";
											this.TB_8.Text = "";
											this.TB_9.Text = "";
											this.TB_10.Text = "";
											this.TB_1.Enabled = true;
											this.TB_2.Enabled = true;
											this.TB_3.Enabled = true;
											this.TB_4.Enabled = true;
											this.TB_5.Enabled = true;
											this.TB_6.Enabled = true;
											this.TB_7.Enabled = true;
											this.TB_8.Enabled = true;
											this.TB_9.Enabled = false;
											this.TB_10.Enabled = false;
										}
										else
										{
											bool flag9 = this.Num_PF.Text == "9";
											if (flag9)
											{
												this.TB_1.Text = "";
												this.TB_2.Text = "";
												this.TB_3.Text = "";
												this.TB_4.Text = "";
												this.TB_5.Text = "";
												this.TB_6.Text = "";
												this.TB_7.Text = "";
												this.TB_8.Text = "";
												this.TB_9.Text = "";
												this.TB_10.Text = "";
												this.TB_1.Enabled = true;
												this.TB_2.Enabled = true;
												this.TB_3.Enabled = true;
												this.TB_4.Enabled = true;
												this.TB_5.Enabled = true;
												this.TB_6.Enabled = true;
												this.TB_7.Enabled = true;
												this.TB_8.Enabled = true;
												this.TB_9.Enabled = true;
												this.TB_10.Enabled = false;
											}
											else
											{
												bool flag10 = this.Num_PF.Text == "10";
												if (flag10)
												{
													this.TB_1.Text = "";
													this.TB_2.Text = "";
													this.TB_3.Text = "";
													this.TB_4.Text = "";
													this.TB_5.Text = "";
													this.TB_6.Text = "";
													this.TB_7.Text = "";
													this.TB_8.Text = "";
													this.TB_9.Text = "";
													this.TB_10.Text = "";
													this.TB_1.Enabled = true;
													this.TB_2.Enabled = true;
													this.TB_3.Enabled = true;
													this.TB_4.Enabled = true;
													this.TB_5.Enabled = true;
													this.TB_6.Enabled = true;
													this.TB_7.Enabled = true;
													this.TB_8.Enabled = true;
													this.TB_9.Enabled = true;
													this.TB_10.Enabled = true;
												}
											}
										}
									}
								}
							}
						}
					}
				}
			}
		}

		// Token: 0x0600006C RID: 108 RVA: 0x000025C1 File Offset: 0x000007C1
		private void Chk_AnnFirst_CheckedChanged(object sender, EventArgs e)
		{
		}

		// Token: 0x0600006D RID: 109 RVA: 0x000025C1 File Offset: 0x000007C1
		private void Chk_AnnSecond_CheckedChanged(object sender, EventArgs e)
		{
		}

		// Token: 0x0600006E RID: 110 RVA: 0x000025C1 File Offset: 0x000007C1
		private void TB_SC_TextChanged(object sender, EventArgs e)
		{
		}

		// Token: 0x0600006F RID: 111 RVA: 0x0000F770 File Offset: 0x0000D970
		private void BTN_Del_Click(object sender, EventArgs e)
		{
			this.DB.Insert_Database("DELETE * From Station_Details WHERE Station_Name = '" + this.TB_SName.Text + "'");
			MessageBox.Show("Station " + this.TB_SName.Text + " Deleted sucessfully");
			this.BTN_Del.Enabled = false;
			this.BTN_Add.Enabled = true;
		}

		// Token: 0x0400007B RID: 123
		private Class_Database DB = new Class_Database();

		// Token: 0x0400007C RID: 124
		private DataTable TrainTable;

		// Token: 0x0400007D RID: 125
		private DataTable StnTable;

		// Token: 0x0400007E RID: 126
		public static int No_PF;

		// Token: 0x0400007F RID: 127
		public static string PFName;

		// Token: 0x04000080 RID: 128
		private bool Flag_Edit = false;

		// Token: 0x04000081 RID: 129
		private bool Flag_New = false;

		// Token: 0x04000082 RID: 130
		private DataTable DTable;
	}
}
