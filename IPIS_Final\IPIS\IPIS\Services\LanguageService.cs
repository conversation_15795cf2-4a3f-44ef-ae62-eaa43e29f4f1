using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using IPIS.Models;
using IPIS.Repositories.Interfaces;

namespace IPIS.Services
{
    public class LanguageService
    {
        private readonly ILanguageRepository _languageRepository;

        public LanguageService(ILanguageRepository languageRepository)
        {
            _languageRepository = languageRepository;
        }

        public async Task<List<Language>> GetAllLanguagesAsync()
        {
            return await _languageRepository.GetAllAsync();
        }

        public async Task<List<Language>> GetActiveLanguagesAsync()
        {
            return await _languageRepository.GetActiveAsync();
        }

        public async Task<Language> GetLanguageByIdAsync(int id)
        {
            return await _languageRepository.GetByIdAsync(id);
        }

        public async Task<Language> GetLanguageByCodeAsync(string code)
        {
            return await _languageRepository.GetByCodeAsync(code);
        }

        public async Task<Language> GetDefaultLanguageAsync()
        {
            return await _languageRepository.GetDefaultAsync();
        }

        public async Task<int> AddLanguageAsync(Language language)
        {
            // Validate language data
            if (string.IsNullOrWhiteSpace(language.Name))
                throw new ArgumentException("Language name is required.");

            if (string.IsNullOrWhiteSpace(language.Code))
                throw new ArgumentException("Language code is required.");

            // Check if language code already exists
            if (await _languageRepository.ExistsAsync(language.Code))
                throw new InvalidOperationException($"Language with code '{language.Code}' already exists.");

            // If this is the first language or marked as default, set it as default
            var allLanguages = await _languageRepository.GetAllAsync();
            if (allLanguages.Count == 0 || language.IsDefault)
            {
                language.IsDefault = true;
            }

            return await _languageRepository.AddAsync(language);
        }

        public async Task<bool> UpdateLanguageAsync(Language language)
        {
            // Validate language data
            if (string.IsNullOrWhiteSpace(language.Name))
                throw new ArgumentException("Language name is required.");

            if (string.IsNullOrWhiteSpace(language.Code))
                throw new ArgumentException("Language code is required.");

            // Check if language exists
            if (!await _languageRepository.ExistsAsync(language.Id))
                throw new InvalidOperationException("Language not found.");

            // Check if code is being changed and if it already exists
            var existingLanguage = await _languageRepository.GetByIdAsync(language.Id);
            if (existingLanguage.Code != language.Code && await _languageRepository.ExistsAsync(language.Code))
                throw new InvalidOperationException($"Language with code '{language.Code}' already exists.");

            return await _languageRepository.UpdateAsync(language);
        }

        public async Task<bool> DeleteLanguageAsync(int id)
        {
            // Check if language exists
            if (!await _languageRepository.ExistsAsync(id))
                throw new InvalidOperationException("Language not found.");

            // Check if it's the default language
            var language = await _languageRepository.GetByIdAsync(id);
            if (language.IsDefault)
                throw new InvalidOperationException("Cannot delete the default language.");

            return await _languageRepository.DeleteAsync(id);
        }

        public async Task<bool> SetDefaultLanguageAsync(int id)
        {
            // Check if language exists
            if (!await _languageRepository.ExistsAsync(id))
                throw new InvalidOperationException("Language not found.");

            return await _languageRepository.SetDefaultAsync(id);
        }

        public async Task<bool> ToggleLanguageStatusAsync(int id)
        {
            var language = await _languageRepository.GetByIdAsync(id);
            if (language == null)
                throw new InvalidOperationException("Language not found.");

            // Don't allow deactivating the default language
            if (language.IsDefault && language.IsActive)
                throw new InvalidOperationException("Cannot deactivate the default language.");

            language.IsActive = !language.IsActive;
            return await _languageRepository.UpdateAsync(language);
        }

        public async Task<List<Language>> GetLanguagesForAnnouncementAsync()
        {
            return await _languageRepository.GetActiveAsync();
        }
    }
} 