using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SQLite;
using System.Text;
using IPIS.Models;
using IPIS.Repositories.Interfaces;
using IPIS.Utils;

namespace IPIS.Repositories
{
    public class SQLiteLoggingRepository : ILoggingRepository
    {
        private readonly string connectionString;

        public SQLiteLoggingRepository()
        {
            connectionString = Database.LogsConnectionString;
        }

        public void AddSystemLog(LogEntry logEntry)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = @"INSERT INTO System_Logs 
                    (Timestamp, Level, Category, Message, Details, UserId, Username, Source, Exception, CreatedAt)
                    VALUES (@Timestamp, @Level, @Category, @Message, @Details, @UserId, @Username, @Source, @Exception, @CreatedAt)";

                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Timestamp", logEntry.Timestamp.ToString("yyyy-MM-dd HH:mm:ss.fff"));
                    command.Parameters.AddWithValue("@Level", logEntry.Level.ToString());
                    command.Parameters.AddWithValue("@Category", logEntry.Category.ToString());
                    command.Parameters.AddWithValue("@Message", logEntry.Message ?? string.Empty);
                    command.Parameters.AddWithValue("@Details", logEntry.Details ?? string.Empty);
                    command.Parameters.AddWithValue("@UserId", logEntry.UserId.HasValue ? (object)logEntry.UserId.Value : DBNull.Value);
                    command.Parameters.AddWithValue("@Username", logEntry.Username ?? string.Empty);
                    command.Parameters.AddWithValue("@Source", logEntry.Source ?? string.Empty);
                    command.Parameters.AddWithValue("@Exception", logEntry.Exception ?? string.Empty);
                    command.Parameters.AddWithValue("@CreatedAt", logEntry.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss"));

                    command.ExecuteNonQuery();
                }
            }
        }

        public void AddUserActivityLog(UserActivityLog activityLog)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = @"INSERT INTO User_Activity_Logs 
                    (Timestamp, UserId, Username, Action, Category, EntityType, EntityId, OldValues, NewValues, 
                     IPAddress, UserAgent, SessionId, CreatedAt)
                    VALUES (@Timestamp, @UserId, @Username, @Action, @Category, @EntityType, @EntityId, @OldValues, 
                            @NewValues, @IPAddress, @UserAgent, @SessionId, @CreatedAt)";

                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Timestamp", activityLog.Timestamp.ToString("yyyy-MM-dd HH:mm:ss.fff"));
                    command.Parameters.AddWithValue("@UserId", activityLog.UserId);
                    command.Parameters.AddWithValue("@Username", activityLog.Username ?? string.Empty);
                    command.Parameters.AddWithValue("@Action", activityLog.Action ?? string.Empty);
                    command.Parameters.AddWithValue("@Category", activityLog.Category.ToString());
                    command.Parameters.AddWithValue("@EntityType", activityLog.EntityType ?? string.Empty);
                    command.Parameters.AddWithValue("@EntityId", activityLog.EntityId ?? string.Empty);
                    command.Parameters.AddWithValue("@OldValues", activityLog.OldValues ?? string.Empty);
                    command.Parameters.AddWithValue("@NewValues", activityLog.NewValues ?? string.Empty);
                    command.Parameters.AddWithValue("@IPAddress", activityLog.IPAddress ?? string.Empty);
                    command.Parameters.AddWithValue("@UserAgent", activityLog.UserAgent ?? string.Empty);
                    command.Parameters.AddWithValue("@SessionId", activityLog.SessionId ?? string.Empty);
                    command.Parameters.AddWithValue("@CreatedAt", activityLog.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss"));

                    command.ExecuteNonQuery();
                }
            }
        }

        public DataTable GetSystemLogs(DateTime? startDate = null, DateTime? endDate = null, LogLevel? level = null,
                                      LogCategory? category = null, string searchText = null, int limit = 1000, int offset = 0)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                var queryBuilder = new StringBuilder(@"
                    SELECT Id, Timestamp, Level, Category, Message, Details, UserId, Username, Source, Exception, CreatedAt
                    FROM System_Logs WHERE 1=1");

                var parameters = new List<SQLiteParameter>();

                if (startDate.HasValue)
                {
                    queryBuilder.Append(" AND Timestamp >= @StartDate");
                    parameters.Add(new SQLiteParameter("@StartDate", startDate.Value.ToString("yyyy-MM-dd HH:mm:ss")));
                }

                if (endDate.HasValue)
                {
                    queryBuilder.Append(" AND Timestamp <= @EndDate");
                    parameters.Add(new SQLiteParameter("@EndDate", endDate.Value.ToString("yyyy-MM-dd HH:mm:ss")));
                }

                if (level.HasValue)
                {
                    queryBuilder.Append(" AND Level = @Level");
                    parameters.Add(new SQLiteParameter("@Level", level.Value.ToString()));
                }

                if (category.HasValue)
                {
                    queryBuilder.Append(" AND Category = @Category");
                    parameters.Add(new SQLiteParameter("@Category", category.Value.ToString()));
                }

                if (!string.IsNullOrEmpty(searchText))
                {
                    queryBuilder.Append(" AND (Message LIKE @SearchText OR Details LIKE @SearchText)");
                    parameters.Add(new SQLiteParameter("@SearchText", $"%{searchText}%"));
                }

                queryBuilder.Append(" ORDER BY Timestamp DESC");

                if (limit > 0)
                {
                    queryBuilder.Append($" LIMIT {limit}");
                    if (offset > 0)
                    {
                        queryBuilder.Append($" OFFSET {offset}");
                    }
                }

                using (var command = new SQLiteCommand(queryBuilder.ToString(), connection))
                {
                    command.Parameters.AddRange(parameters.ToArray());

                    using (var adapter = new SQLiteDataAdapter(command))
                    {
                        var dataTable = new DataTable();
                        adapter.Fill(dataTable);
                        return dataTable;
                    }
                }
            }
        }

        public DataTable GetUserActivityLogs(DateTime? startDate = null, DateTime? endDate = null, LogCategory? category = null,
                                           string username = null, string action = null, int limit = 1000, int offset = 0)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                var queryBuilder = new StringBuilder(@"
                    SELECT Id, Timestamp, UserId, Username, Action, Category, EntityType, EntityId, 
                           OldValues, NewValues, IPAddress, UserAgent, SessionId, CreatedAt
                    FROM User_Activity_Logs WHERE 1=1");

                var parameters = new List<SQLiteParameter>();

                if (startDate.HasValue)
                {
                    queryBuilder.Append(" AND Timestamp >= @StartDate");
                    parameters.Add(new SQLiteParameter("@StartDate", startDate.Value.ToString("yyyy-MM-dd HH:mm:ss")));
                }

                if (endDate.HasValue)
                {
                    queryBuilder.Append(" AND Timestamp <= @EndDate");
                    parameters.Add(new SQLiteParameter("@EndDate", endDate.Value.ToString("yyyy-MM-dd HH:mm:ss")));
                }

                if (category.HasValue)
                {
                    queryBuilder.Append(" AND Category = @Category");
                    parameters.Add(new SQLiteParameter("@Category", category.Value.ToString()));
                }

                if (!string.IsNullOrEmpty(username))
                {
                    queryBuilder.Append(" AND Username LIKE @Username");
                    parameters.Add(new SQLiteParameter("@Username", $"%{username}%"));
                }

                if (!string.IsNullOrEmpty(action))
                {
                    queryBuilder.Append(" AND Action LIKE @Action");
                    parameters.Add(new SQLiteParameter("@Action", $"%{action}%"));
                }

                queryBuilder.Append(" ORDER BY Timestamp DESC");

                if (limit > 0)
                {
                    queryBuilder.Append($" LIMIT {limit}");
                    if (offset > 0)
                    {
                        queryBuilder.Append($" OFFSET {offset}");
                    }
                }

                using (var command = new SQLiteCommand(queryBuilder.ToString(), connection))
                {
                    command.Parameters.AddRange(parameters.ToArray());

                    using (var adapter = new SQLiteDataAdapter(command))
                    {
                        var dataTable = new DataTable();
                        adapter.Fill(dataTable);
                        return dataTable;
                    }
                }
            }
        }

        public int GetLogCount(LogLevel? level = null, LogCategory? category = null, DateTime? startDate = null, DateTime? endDate = null)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                var queryBuilder = new StringBuilder("SELECT COUNT(*) FROM System_Logs WHERE 1=1");
                var parameters = new List<SQLiteParameter>();

                if (level.HasValue)
                {
                    queryBuilder.Append(" AND Level = @Level");
                    parameters.Add(new SQLiteParameter("@Level", level.Value.ToString()));
                }

                if (category.HasValue)
                {
                    queryBuilder.Append(" AND Category = @Category");
                    parameters.Add(new SQLiteParameter("@Category", category.Value.ToString()));
                }

                if (startDate.HasValue)
                {
                    queryBuilder.Append(" AND Timestamp >= @StartDate");
                    parameters.Add(new SQLiteParameter("@StartDate", startDate.Value.ToString("yyyy-MM-dd HH:mm:ss")));
                }

                if (endDate.HasValue)
                {
                    queryBuilder.Append(" AND Timestamp <= @EndDate");
                    parameters.Add(new SQLiteParameter("@EndDate", endDate.Value.ToString("yyyy-MM-dd HH:mm:ss")));
                }

                using (var command = new SQLiteCommand(queryBuilder.ToString(), connection))
                {
                    command.Parameters.AddRange(parameters.ToArray());
                    return Convert.ToInt32(command.ExecuteScalar());
                }
            }
        }

        public Dictionary<LogLevel, int> GetLogCountByLevel(DateTime? startDate = null, DateTime? endDate = null)
        {
            var result = new Dictionary<LogLevel, int>();

            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                var queryBuilder = new StringBuilder("SELECT Level, COUNT(*) as Count FROM System_Logs WHERE 1=1");
                var parameters = new List<SQLiteParameter>();

                if (startDate.HasValue)
                {
                    queryBuilder.Append(" AND Timestamp >= @StartDate");
                    parameters.Add(new SQLiteParameter("@StartDate", startDate.Value.ToString("yyyy-MM-dd HH:mm:ss")));
                }

                if (endDate.HasValue)
                {
                    queryBuilder.Append(" AND Timestamp <= @EndDate");
                    parameters.Add(new SQLiteParameter("@EndDate", endDate.Value.ToString("yyyy-MM-dd HH:mm:ss")));
                }

                queryBuilder.Append(" GROUP BY Level");

                using (var command = new SQLiteCommand(queryBuilder.ToString(), connection))
                {
                    command.Parameters.AddRange(parameters.ToArray());

                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            if (Enum.TryParse<LogLevel>(reader["Level"].ToString(), out var level))
                            {
                                result[level] = Convert.ToInt32(reader["Count"]);
                            }
                        }
                    }
                }
            }

            return result;
        }

        public Dictionary<LogCategory, int> GetLogCountByCategory(DateTime? startDate = null, DateTime? endDate = null)
        {
            var result = new Dictionary<LogCategory, int>();

            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                var queryBuilder = new StringBuilder("SELECT Category, COUNT(*) as Count FROM System_Logs WHERE 1=1");
                var parameters = new List<SQLiteParameter>();

                if (startDate.HasValue)
                {
                    queryBuilder.Append(" AND Timestamp >= @StartDate");
                    parameters.Add(new SQLiteParameter("@StartDate", startDate.Value.ToString("yyyy-MM-dd HH:mm:ss")));
                }

                if (endDate.HasValue)
                {
                    queryBuilder.Append(" AND Timestamp <= @EndDate");
                    parameters.Add(new SQLiteParameter("@EndDate", endDate.Value.ToString("yyyy-MM-dd HH:mm:ss")));
                }

                queryBuilder.Append(" GROUP BY Category");

                using (var command = new SQLiteCommand(queryBuilder.ToString(), connection))
                {
                    command.Parameters.AddRange(parameters.ToArray());

                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            if (Enum.TryParse<LogCategory>(reader["Category"].ToString(), out var category))
                            {
                                result[category] = Convert.ToInt32(reader["Count"]);
                            }
                        }
                    }
                }
            }

            return result;
        }

        public void DeleteOldLogs(DateTime cutoffDate)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();

                // Delete old system logs
                string deleteSystemLogsQuery = "DELETE FROM System_Logs WHERE Timestamp < @CutoffDate";
                using (var command = new SQLiteCommand(deleteSystemLogsQuery, connection))
                {
                    command.Parameters.AddWithValue("@CutoffDate", cutoffDate.ToString("yyyy-MM-dd HH:mm:ss"));
                    command.ExecuteNonQuery();
                }

                // Delete old user activity logs
                string deleteActivityLogsQuery = "DELETE FROM User_Activity_Logs WHERE Timestamp < @CutoffDate";
                using (var command = new SQLiteCommand(deleteActivityLogsQuery, connection))
                {
                    command.Parameters.AddWithValue("@CutoffDate", cutoffDate.ToString("yyyy-MM-dd HH:mm:ss"));
                    command.ExecuteNonQuery();
                }
            }
        }

        public DataTable ExportLogs(DateTime? startDate = null, DateTime? endDate = null, LogLevel? level = null)
        {
            return GetSystemLogs(startDate, endDate, level, null, null, 0); // No limit for export
        }
    }
}
