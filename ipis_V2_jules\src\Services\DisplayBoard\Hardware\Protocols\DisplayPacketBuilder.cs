using System;
using System.Collections.Generic;
using System.Text; // For Encoding.ASCII
using ipis_V2_jules.Hardware.Protocols; // For CrcC<PERSON>cksum if it's in the old location
// If CrcChecksum is moved to the new path, this using might need adjustment or be removed if same namespace.
// Assuming Crc<PERSON><PERSON>cksum is in ipis_V2_jules.Hardware.Protocols based on Turn 31.

namespace ipis_V2_jules.Services.DisplayBoard.Hardware.Protocols
{
    /// <summary>
    /// Constructs command packets for various display boards, initially focusing on MLDB.
    /// </summary>
    public static class DisplayPacketBuilder
    {
        // Common Protocol Bytes (Hypothetical, based on prompt example)
        public const byte SOF = 0x01; // Start Of Frame
        public const byte EOF = 0x04; // End Of Frame
        public const byte STX = 0x02; // Start of Text (Data)
        public const byte ETX = 0x03; // End of Text (Data)

        // Placeholder Command Codes (determine actual values from original system analysis)
        private const byte CMD_MLDB_DISPLAY_TEXT = 0x31;
        private const byte CMD_MLDB_CLEAR_SCREEN = 0x32;
        private const byte CMD_LINK_CHECK = 0x30; // Generic link check command

        // AGDB Specific Command Codes (Placeholders - verify from original system)
        private const byte CMD_AGDB_DISPLAY_3LINE = 0xF1;
        private const byte CMD_AGDB_CLEAR_SCREEN = 0xF2;

        // CGDB Specific Command Codes (Placeholders)
        private const byte CMD_CGDB_DISPLAY_COACHES = 0xD1;
        private const byte CMD_CGDB_CLEAR_SCREEN = 0xD2;

        // TADDB Specific Command Codes (Placeholders - assuming they might differ from MLDB)
        // If they are identical to MLDB, these constants are not strictly needed,
        // and MLDB methods could be reused. For clarity, defining them separately.
        private const byte CMD_TADDB_DISPLAY_LINE_1 = 0x61;
        private const byte CMD_TADDB_DISPLAY_LINE_2 = 0x62;
        private const byte CMD_TADDB_DISPLAY_LINE_3 = 0x63;
        // Add more if TADDB supports more lines with specific commands
        private const byte CMD_TADDB_CLEAR_SCREEN = 0x6F;


        // Note: SOF/EOF for AGDB/CGDB/TADDB might be different from MLDB.
        // For now, using the common SOF/EOF defined (0x01, 0x04).
        // If specific boards have unique SOF/EOF, those should be used in their respective methods.

        /// <summary>
        /// Builds a display packet for MLDB (Multi-Line Display Board).
        /// Packet Structure: SOF | BoardID | Command | DataLength | Data (STX | Text | ETX) | CRC (2 bytes) | EOF
        /// CRC is calculated from BoardID up to and including ETX.
        /// </summary>
        /// <param name="boardId">The ID of the target display board.</param>
        /// <param name="textData">The text string to be displayed. This method assumes text will be framed with STX/ETX.</param>
        /// <param name="maxLineLength">Max characters per line (currently not used for splitting, caller should pre-format).</param>
        /// <returns>A byte array representing the complete packet.</returns>
        public static byte[] BuildMldbTextDisplayPacket(byte boardId, string textData, int maxLineLength)
        {
            // This method is for sending text that will be framed with STX/ETX.
            // If sending raw bitmap data, use BuildMldbBitmapDisplayPacket.
            Console.WriteLine($"Building MLDB Text Display Packet for BoardID: {boardId}, Text: \"{textData.Substring(0, Math.Min(30, textData.Length))}...\"");

            byte[] textDataBytes = Encoding.ASCII.GetBytes(textData);
            List<byte> dataPayloadWithStxEtx = new List<byte>();
            dataPayloadWithStxEtx.Add(STX);
            dataPayloadWithStxEtx.AddRange(textDataBytes);
            dataPayloadWithStxEtx.Add(ETX);

            byte dataLength = (byte)dataPayloadWithStxEtx.Count;

            // --- Start assembling the part of the packet that needs CRC ---
            List<byte> partForCrc = new List<byte>();
            partForCrc.Add(boardId);
            partForCrc.Add(CMD_MLDB_DISPLAY_TEXT); // Use the general text display command
            partForCrc.Add(dataLength);
            partForCrc.AddRange(dataPayloadWithStxEtx);
            // --- End assembling part for CRC ---

            byte[] crcSegment = partForCrc.ToArray();
            byte[] packetWithCrc = new byte[crcSegment.Length + 2];
            Array.Copy(crcSegment, 0, packetWithCrc, 0, crcSegment.Length);
            CrcChecksum.PrepareChecksum(ref packetWithCrc, (ushort)packetWithCrc.Length);

            List<byte> finalPacket = new List<byte>();
            finalPacket.Add(SOF);
            finalPacket.AddRange(packetWithCrc);
            finalPacket.Add(EOF);
            return finalPacket.ToArray();
        }

        /// <summary>
        /// Builds a display packet for MLDB/PDB using pre-formatted bitmap data.
        /// Packet Structure: SOF | BoardID | CommandOrLineNo | DataLength | BitmapDataPayload | CRC (2 bytes) | EOF
        /// CRC is calculated from BoardID up to and including BitmapDataPayload.
        /// </summary>
        /// <param name="boardId">The ID of the target display board.</param>
        /// <param name="commandOrLineNumber">Command code for display or line number.</param>
        /// <param name="bitmapDataPayload">The raw bitmap data for the line/display.</param>
        /// <returns>A byte array representing the complete packet.</returns>
        public static byte[] BuildMldbBitmapDisplayPacket(byte boardId, byte commandOrLineNumber, byte[] bitmapDataPayload)
        {
            Console.WriteLine($"Building MLDB Bitmap Display Packet for BoardID: {boardId}, Command/Line: {commandOrLineNumber}, Payload Length: {bitmapDataPayload?.Length ?? 0}");

            if (bitmapDataPayload == null) bitmapDataPayload = Array.Empty<byte>();

            byte dataLength = (byte)bitmapDataPayload.Length; // Length of the raw bitmap data

            // --- Part for CRC ---
            List<byte> partForCrc = new List<byte>();
            partForCrc.Add(boardId);
            partForCrc.Add(commandOrLineNumber); // This could be CMD_MLDB_DISPLAY_TEXT or a line-specific command
            partForCrc.Add(dataLength);
            partForCrc.AddRange(bitmapDataPayload);
            // --- End part for CRC ---

            byte[] crcSegment = partForCrc.ToArray();
            byte[] packetWithCrc = new byte[crcSegment.Length + 2];
            Array.Copy(crcSegment, 0, packetWithCrc, 0, crcSegment.Length);
            CrcChecksum.PrepareChecksum(ref packetWithCrc, (ushort)packetWithCrc.Length);

            List<byte> finalPacket = new List<byte>();
            finalPacket.Add(SOF);
            finalPacket.AddRange(packetWithCrc);
            finalPacket.Add(EOF);
            return finalPacket.ToArray();
        }


        /// <summary>
        /// Builds a clear screen packet for MLDB.
        /// Packet Structure: SOF | BoardID | Command | DataLength | (Optional: STX | ETX) | CRC (2 bytes) | EOF
        /// CRC is calculated from BoardID up to and including the optional ETX.
        /// </summary>
        /// <param name="boardId">The ID of the target display board.</param>
        /// <returns>A byte array representing the complete packet.</returns>
        public static byte[] BuildMldbClearScreenPacket(byte boardId)
        {
            Console.WriteLine($"Building MLDB Clear Screen Packet for BoardID: {boardId}");

            List<byte> packetConstruct = new List<byte>();
            List<byte> dataPayloadForLengthAndCrc = new List<byte>();

            // Assuming clear command might still need an empty STX/ETX data field as per some protocols.
            // If not, this can be empty and DataLength would be 0.
            bool includeEmptyStxEtxForClear = true; // Make this configurable if necessary
            if (includeEmptyStxEtxForClear)
            {
                dataPayloadForLengthAndCrc.Add(STX);
                dataPayloadForLengthAndCrc.Add(ETX);
            }

            byte dataLength = (byte)dataPayloadForLengthAndCrc.Count;

            // --- Part for CRC ---
            List<byte> partForCrc = new List<byte>();
            partForCrc.Add(boardId);
            partForCrc.Add(CMD_MLDB_CLEAR_SCREEN);
            partForCrc.Add(dataLength);
            partForCrc.AddRange(dataPayloadForLengthAndCrc);
            // --- End part for CRC ---

            byte[] crcSegment = partForCrc.ToArray();
            byte[] packetWithCrc = new byte[crcSegment.Length + 2];
            Array.Copy(crcSegment, 0, packetWithCrc, 0, crcSegment.Length);

            CrcChecksum.PrepareChecksum(ref packetWithCrc, (ushort)packetWithCrc.Length);

            // --- Final Packet ---
            packetConstruct.Add(SOF);
            packetConstruct.AddRange(packetWithCrc);
            packetConstruct.Add(EOF);

            return packetConstruct.ToArray();
        }

        /// <summary>
        /// Builds a link check packet (can be generic for compatible boards).
        /// Packet Structure: SOF | BoardID | Command | DataLength (0) | CRC (2 bytes) | EOF
        /// CRC is calculated from BoardID up to and including DataLength.
        /// </summary>
        /// <param name="boardId">The ID of the target display board.</param>
        /// <returns>A byte array representing the complete packet.</returns>
        public static byte[] BuildLinkCheckPacket(byte boardId)
        {
            Console.WriteLine($"Building Link Check Packet for BoardID: {boardId}");

            List<byte> packetConstruct = new List<byte>();
            byte dataLength = 0x00; // No data payload for link check

            // --- Part for CRC ---
            List<byte> partForCrc = new List<byte>();
            partForCrc.Add(boardId);
            partForCrc.Add(CMD_LINK_CHECK);
            partForCrc.Add(dataLength);
            // --- End part for CRC ---

            byte[] crcSegment = partForCrc.ToArray();
            byte[] packetWithCrc = new byte[crcSegment.Length + 2];
            Array.Copy(crcSegment, 0, packetWithCrc, 0, crcSegment.Length);

            CrcChecksum.PrepareChecksum(ref packetWithCrc, (ushort)packetWithCrc.Length);

            // --- Final Packet ---
            packetConstruct.Add(SOF);
            packetConstruct.AddRange(packetWithCrc);
            packetConstruct.Add(EOF);

            return packetConstruct.ToArray();
        }

        // TODO: Implement other packet builders (SetConfig, GetConfig, Reset, Diagnostic, etc.)
        // following similar patterns, adjusting command codes and data payloads as necessary.
        // For example:
        // public static byte[] BuildMldbSetConfigPacket(byte boardId, byte[] configData) { ... }

        /// <summary>
        /// Builds a display packet for AGDB.
        /// Assumed Packet Structure: SOF | boardId | Command | additionalHeaderBytes | line1Data | line2Data | line3Data | CRC (2 bytes) | EOF
        /// CRC is calculated from boardId up to and including line3Data.
        /// </summary>
        /// <param name="boardId">The ID of the target AGDB board.</param>
        /// <param name="additionalHeaderBytes">Header bytes from AgdbDataFormatter (e.g., boardId repeated, line control).</param>
        /// <param name="line1Data">Byte data for line 1 (CharsPerLine * 5 bytes).</param>
        /// <param name="line2Data">Byte data for line 2 (can be null if not used).</param>
        /// <param name="line3Data">Byte data for line 3 (can be null if not used).</param>
        /// <returns>A byte array representing the complete packet.</returns>
        public static byte[] BuildAgdbDisplayPacket(byte boardId, byte[] additionalHeaderBytes, byte[] line1Data, byte[] line2Data, byte[] line3Data)
        {
            Console.WriteLine($"Building AGDB Display Packet for BoardID: {boardId}");

            List<byte> packetConstruct = new List<byte>();

            // --- Part for CRC ---
            List<byte> partForCrc = new List<byte>();
            partForCrc.Add(boardId);
            partForCrc.Add(CMD_AGDB_DISPLAY_3LINE);

            if (additionalHeaderBytes != null)
            {
                partForCrc.AddRange(additionalHeaderBytes);
            }

            // Add line data. AgdbDataFormatter should provide correctly sized arrays (padded with spaces if text is short).
            // If a line is not used, its data might be null or an empty array. The protocol must define how this is handled.
            // For this example, we assume that if lineXData is null, it's not included, or an empty array is included if the protocol expects fixed structure.
            // Given that AgdbDataFormatter pads lines, we assume lineXData will not be null for active lines.
            if (line1Data != null) partForCrc.AddRange(line1Data);
            if (line2Data != null) partForCrc.AddRange(line2Data);
            if (line3Data != null) partForCrc.AddRange(line3Data);
            // --- End part for CRC ---

            byte[] crcSegment = partForCrc.ToArray();
            byte[] packetWithCrc = new byte[crcSegment.Length + 2];
            Array.Copy(crcSegment, 0, packetWithCrc, 0, crcSegment.Length);

            CrcChecksum.PrepareChecksum(ref packetWithCrc, (ushort)packetWithCrc.Length);

            // --- Final Packet ---
            packetConstruct.Add(SOF); // Assuming common SOF for now
            packetConstruct.AddRange(packetWithCrc);
            packetConstruct.Add(EOF); // Assuming common EOF for now

            return packetConstruct.ToArray();
        }

        /// <summary>
        /// Builds a clear screen packet for AGDB.
        /// Packet Structure: SOF | boardId | CMD_AGDB_CLEAR_SCREEN | OptionalMinimalPayload | CRC (2 bytes) | EOF
        /// CRC is calculated from boardId up to and including CMD_AGDB_CLEAR_SCREEN (and payload if any).
        /// </summary>
        /// <param name="boardId">The ID of the target AGDB board.</param>
        /// <returns>A byte array representing the complete packet.</returns>
        public static byte[] BuildAgdbClearScreenPacket(byte boardId)
        {
            Console.WriteLine($"Building AGDB Clear Screen Packet for BoardID: {boardId}");
            List<byte> packetConstruct = new List<byte>();

            // --- Part for CRC ---
            List<byte> partForCrc = new List<byte>();
            partForCrc.Add(boardId);
            partForCrc.Add(CMD_AGDB_CLEAR_SCREEN);
            // Optional: Add minimal payload if required by AGDB clear command
            // byte[] minimalPayload = new byte[] { 0x00 };
            // partForCrc.AddRange(minimalPayload);
            // --- End part for CRC ---

            byte[] crcSegment = partForCrc.ToArray();
            byte[] packetWithCrc = new byte[crcSegment.Length + 2];
            Array.Copy(crcSegment, 0, packetWithCrc, 0, crcSegment.Length);

            CrcChecksum.PrepareChecksum(ref packetWithCrc, (ushort)packetWithCrc.Length);

            // --- Final Packet ---
            packetConstruct.Add(SOF); // Assuming common SOF
            packetConstruct.AddRange(packetWithCrc);
            packetConstruct.Add(EOF); // Assuming common EOF

            return packetConstruct.ToArray();
        }

        /// <summary>
        /// Builds a display packet for CGDB.
        /// Packet Structure: SOF | boardId | CMD_CGDB_DISPLAY_COACHES | DataLength | AsciiCoachData | CRC | EOF
        /// CRC is calculated from boardId up to and including AsciiCoachData.
        /// </summary>
        public static byte[] BuildCgdbDisplayPacket(byte boardId, byte[] asciiCoachData)
        {
            Console.WriteLine($"Building CGDB Display Packet for BoardID: {boardId}, Data Length: {asciiCoachData?.Length ?? 0}");
            if (asciiCoachData == null) asciiCoachData = Array.Empty<byte>();

            byte dataLength = (byte)asciiCoachData.Length;

            List<byte> partForCrc = new List<byte>();
            partForCrc.Add(boardId);
            partForCrc.Add(CMD_CGDB_DISPLAY_COACHES);
            partForCrc.Add(dataLength);
            partForCrc.AddRange(asciiCoachData);

            byte[] crcSegment = partForCrc.ToArray();
            byte[] packetWithCrc = new byte[crcSegment.Length + 2];
            Array.Copy(crcSegment, 0, packetWithCrc, 0, crcSegment.Length);
            CrcChecksum.PrepareChecksum(ref packetWithCrc, (ushort)packetWithCrc.Length);

            List<byte> finalPacket = new List<byte>();
            finalPacket.Add(SOF);
            finalPacket.AddRange(packetWithCrc);
            finalPacket.Add(EOF);
            return finalPacket.ToArray();
        }

        /// <summary>
        /// Builds a clear screen packet for CGDB.
        /// </summary>
        public static byte[] BuildCgdbClearScreenPacket(byte boardId)
        {
            Console.WriteLine($"Building CGDB Clear Screen Packet for BoardID: {boardId}");
            List<byte> partForCrc = new List<byte>();
            partForCrc.Add(boardId);
            partForCrc.Add(CMD_CGDB_CLEAR_SCREEN);
            // Assuming no data payload for CGDB clear, so DataLength could be 0 or omitted if command implies it.
            // If DataLength byte is still required: partForCrc.Add(0x00);

            byte[] crcSegment = partForCrc.ToArray();
            byte[] packetWithCrc = new byte[crcSegment.Length + 2];
            Array.Copy(crcSegment, 0, packetWithCrc, 0, crcSegment.Length);
            CrcChecksum.PrepareChecksum(ref packetWithCrc, (ushort)packetWithCrc.Length);

            List<byte> finalPacket = new List<byte>();
            finalPacket.Add(SOF);
            finalPacket.AddRange(packetWithCrc);
            finalPacket.Add(EOF);
            return finalPacket.ToArray();
        }

        /// <summary>
        /// Builds a display packet for TADDB using pre-formatted bitmap data.
        /// Structure can be identical to BuildMldbBitmapDisplayPacket, using TADDB-specific line commands.
        /// Packet Structure: SOF | BoardID | CommandOrLineNo | DataLength | BitmapDataPayload | CRC | EOF
        /// </summary>
        public static byte[] BuildTaddbBitmapDisplayPacket(byte boardId, byte commandOrLineNumber, byte[] bitmapDataPayload)
        {
            Console.WriteLine($"Building TADDB Bitmap Display Packet for BoardID: {boardId}, Command/Line: {commandOrLineNumber}, Payload Length: {bitmapDataPayload?.Length ?? 0}");
            // This logic is identical to BuildMldbBitmapDisplayPacket, just uses different command constants (passed in commandOrLineNumber)
            // If packet structure (SOF, EOF, CRC placement) is truly identical, MLDB version could be reused by passing correct command codes.
            // For clarity and future divergence, it's a separate method.

            if (bitmapDataPayload == null) bitmapDataPayload = Array.Empty<byte>();
            byte dataLength = (byte)bitmapDataPayload.Length;

            List<byte> partForCrc = new List<byte>();
            partForCrc.Add(boardId);
            partForCrc.Add(commandOrLineNumber); // e.g., CMD_TADDB_DISPLAY_LINE_1
            partForCrc.Add(dataLength);
            partForCrc.AddRange(bitmapDataPayload);

            byte[] crcSegment = partForCrc.ToArray();
            byte[] packetWithCrc = new byte[crcSegment.Length + 2];
            Array.Copy(crcSegment, 0, packetWithCrc, 0, crcSegment.Length);
            CrcChecksum.PrepareChecksum(ref packetWithCrc, (ushort)packetWithCrc.Length);

            List<byte> finalPacket = new List<byte>();
            finalPacket.Add(SOF);
            finalPacket.AddRange(packetWithCrc);
            finalPacket.Add(EOF);
            return finalPacket.ToArray();
        }

        /// <summary>
        /// Builds a clear screen packet for TADDB.
        /// </summary>
        public static byte[] BuildTaddbClearScreenPacket(byte boardId)
        {
            Console.WriteLine($"Building TADDB Clear Screen Packet for BoardID: {boardId}");
            List<byte> partForCrc = new List<byte>();
            partForCrc.Add(boardId);
            partForCrc.Add(CMD_TADDB_CLEAR_SCREEN);
            // Optional: Add DataLength (0x00) if required by protocol.

            byte[] crcSegment = partForCrc.ToArray();
            byte[] packetWithCrc = new byte[crcSegment.Length + 2];
            Array.Copy(crcSegment, 0, packetWithCrc, 0, crcSegment.Length);
            CrcChecksum.PrepareChecksum(ref packetWithCrc, (ushort)packetWithCrc.Length);

            List<byte> finalPacket = new List<byte>();
            finalPacket.Add(SOF);
            finalPacket.AddRange(packetWithCrc);
            finalPacket.Add(EOF);
            return finalPacket.ToArray();
        }
    }
}
