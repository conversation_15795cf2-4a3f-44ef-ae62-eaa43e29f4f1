using System.Data;
using System.Collections.Generic;
using IPIS.Models;

namespace IPIS.Repositories.Interfaces
{
    public interface ITrainTypeRepository
    {
        DataTable GetAllTrainTypes();
        List<TrainType> GetActiveTrainTypes();
        TrainType GetTrainTypeById(string id);
        TrainType GetTrainTypeByName(string name);
        void AddTrainType(TrainType trainType);
        void UpdateTrainType(TrainType trainType);
        void DeleteTrainType(string id);
        bool TrainTypeExists(string name, string excludeId = null);
    }
}
