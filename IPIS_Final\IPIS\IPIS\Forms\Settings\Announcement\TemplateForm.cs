using System;
using System.Drawing;
using System.Threading.Tasks;
using System.Windows.Forms;
using IPIS.Models;
using IPIS.Services;

namespace IPIS.Forms.Settings.Announcement
{
    public partial class TemplateForm : Form
    {
        private readonly AnnouncementTemplateService _templateService;
        private AnnouncementTemplate _template;
        private bool _isEditMode;

        // Controls
        private TextBox txtName;
        private TextBox txtDescription;
        private ComboBox cboArrivalDeparture;
        private CheckBox chkIsActive;
        private Button btnSave;
        private Button btnCancel;
        private Label lblName;
        private Label lblDescription;
        private Label lblArrivalDeparture;

        public TemplateForm()
        {
            _templateService = new AnnouncementTemplateService(new Repositories.SQLiteAnnouncementTemplateRepository());
            _template = new AnnouncementTemplate();
            _isEditMode = false;
            InitializeComponent();
        }

        public TemplateForm(AnnouncementTemplate template)
        {
            _templateService = new AnnouncementTemplateService(new Repositories.SQLiteAnnouncementTemplateRepository());
            _template = template;
            _isEditMode = true;
            InitializeComponent();
            LoadTemplateData();
        }

        private void InitializeComponent()
        {
            this.txtName = new TextBox();
            this.txtDescription = new TextBox();
            this.cboArrivalDeparture = new ComboBox();
            this.chkIsActive = new CheckBox();
            this.btnSave = new Button();
            this.btnCancel = new Button();
            this.lblName = new Label();
            this.lblDescription = new Label();
            this.lblArrivalDeparture = new Label();

            // Form
            this.ClientSize = new Size(500, 350);
            this.Name = "TemplateForm";
            this.Text = _isEditMode ? "Edit Template" : "Add Template";
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            // Name Label
            this.lblName.AutoSize = true;
            this.lblName.Location = new Point(20, 20);
            this.lblName.Size = new Size(80, 15);
            this.lblName.Text = "Name:";
            this.lblName.Font = new Font("Segoe UI", 9F, FontStyle.Regular);

            // Name TextBox
            this.txtName.Location = new Point(120, 17);
            this.txtName.Size = new Size(350, 23);
            this.txtName.Font = new Font("Segoe UI", 9F, FontStyle.Regular);

            // Description Label
            this.lblDescription.AutoSize = true;
            this.lblDescription.Location = new Point(20, 60);
            this.lblDescription.Size = new Size(80, 15);
            this.lblDescription.Text = "Description:";
            this.lblDescription.Font = new Font("Segoe UI", 9F, FontStyle.Regular);

            // Description TextBox
            this.txtDescription.Location = new Point(120, 57);
            this.txtDescription.Size = new Size(350, 60);
            this.txtDescription.Multiline = true;
            this.txtDescription.ScrollBars = ScrollBars.Vertical;
            this.txtDescription.Font = new Font("Segoe UI", 9F, FontStyle.Regular);

            // Arrival/Departure Label
            this.lblArrivalDeparture.AutoSize = true;
            this.lblArrivalDeparture.Location = new Point(20, 140);
            this.lblArrivalDeparture.Size = new Size(100, 15);
            this.lblArrivalDeparture.Text = "A/D:";
            this.lblArrivalDeparture.Font = new Font("Segoe UI", 9F, FontStyle.Regular);

            // Arrival/Departure ComboBox
            this.cboArrivalDeparture.Location = new Point(120, 137);
            this.cboArrivalDeparture.Size = new Size(100, 25);
            this.cboArrivalDeparture.DropDownStyle = ComboBoxStyle.DropDownList;
            this.cboArrivalDeparture.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
            this.cboArrivalDeparture.Items.AddRange(new object[] { "A", "D" });
            this.cboArrivalDeparture.SelectedIndex = 0; // Default to "A"

            // Active CheckBox
            this.chkIsActive.AutoSize = true;
            this.chkIsActive.Location = new Point(120, 180);
            this.chkIsActive.Size = new Size(100, 19);
            this.chkIsActive.Text = "Active";
            this.chkIsActive.Font = new Font("Segoe UI", 9F, FontStyle.Regular);

            // Save Button
            this.btnSave.Location = new Point(280, 270);
            this.btnSave.Size = new Size(90, 30);
            this.btnSave.Text = "Save";
            this.btnSave.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
            this.btnSave.BackColor = Color.FromArgb(0, 122, 204);
            this.btnSave.ForeColor = Color.White;
            this.btnSave.FlatStyle = FlatStyle.Flat;
            this.btnSave.Click += new EventHandler(this.btnSave_Click);

            // Cancel Button
            this.btnCancel.Location = new Point(380, 270);
            this.btnCancel.Size = new Size(90, 30);
            this.btnCancel.Text = "Cancel";
            this.btnCancel.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
            this.btnCancel.BackColor = Color.FromArgb(108, 117, 125);
            this.btnCancel.ForeColor = Color.White;
            this.btnCancel.FlatStyle = FlatStyle.Flat;
            this.btnCancel.Click += new EventHandler(this.btnCancel_Click);

            // Add controls to form
            this.Controls.AddRange(new Control[] {
                this.lblName,
                this.txtName,
                this.lblDescription,
                this.txtDescription,
                this.lblArrivalDeparture,
                this.cboArrivalDeparture,
                this.chkIsActive,
                this.btnSave,
                this.btnCancel
            });
        }

        private void LoadTemplateData()
        {
            txtName.Text = _template.Name;
            txtDescription.Text = _template.Description;
            chkIsActive.Checked = _template.IsActive;
            
            // Set the A/D value
            if (cboArrivalDeparture.Items.Contains(_template.ArrivalDeparture))
            {
                cboArrivalDeparture.SelectedItem = _template.ArrivalDeparture;
            }
            else
            {
                cboArrivalDeparture.SelectedIndex = 0; // Default to "A"
            }
        }

        private async void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                // Validate input
                if (string.IsNullOrWhiteSpace(txtName.Text))
                {
                    MessageBox.Show("Template name is required.", "Validation Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtName.Focus();
                    return;
                }

                if (cboArrivalDeparture.SelectedItem == null)
                {
                    MessageBox.Show("Please select Arrival/Departure.", "Validation Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    cboArrivalDeparture.Focus();
                    return;
                }

                // Update template object
                _template.Name = txtName.Text.Trim();
                _template.Description = txtDescription.Text.Trim();
                _template.ArrivalDeparture = cboArrivalDeparture.SelectedItem.ToString();
                _template.IsActive = chkIsActive.Checked;

                bool success;
                if (_isEditMode)
                {
                    success = await _templateService.UpdateTemplateAsync(_template);
                }
                else
                {
                    await _templateService.AddTemplateAsync(_template);
                    success = true;
                }

                if (success)
                {
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    MessageBox.Show("Failed to save template.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving template: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
    }
} 