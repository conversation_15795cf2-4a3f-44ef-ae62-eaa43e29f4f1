// Decompiled with JetBrains decompiler
// Type: ipis.frmPortConfig
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using ipis.My;
using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

namespace ipis
{

[DesignerGenerated]
public class frmPortConfig : Form
{
  private static List<WeakReference> __ENCList = new List<WeakReference>();
  private IContainer components;
  [AccessedThroughProperty("radMdch")]
  private RadioButton _radMdch;
  [AccessedThroughProperty("radPdch")]
  private RadioButton _radPdch;
  [AccessedThroughProperty("Panel1")]
  private Panel _Panel1;
  [AccessedThroughProperty("txtName")]
  private TextBox _txtName;
  [AccessedThroughProperty("lblName")]
  private Label _lblName;
  [AccessedThroughProperty("btnExit")]
  private Button _btnExit;
  [AccessedThroughProperty("lblPfno")]
  private Label _lblPfno;
  [AccessedThroughProperty("cmbPfno")]
  private ComboBox _cmbPfno;
  [AccessedThroughProperty("btnSet")]
  private Button _btnSet;
  [AccessedThroughProperty("btnGet")]
  private Button _btnGet;
  [AccessedThroughProperty("txtAddress")]
  private TextBox _txtAddress;
  [AccessedThroughProperty("lblAddress")]
  private Label _lblAddress;
  [AccessedThroughProperty("txtSharedPfno")]
  private TextBox _txtSharedPfno;
  [AccessedThroughProperty("lblSharedPfno")]
  private Label _lblSharedPfno;
  [AccessedThroughProperty("RadioButton1")]
  private RadioButton _RadioButton1;
  private string pdch_name;
  private string platform_no;
  private byte pdch_addr;
  private string pdch_shared_Pfno;
  private byte result;

  [DebuggerNonUserCode]
  static frmPortConfig()
  {
  }

  public frmPortConfig()
  {
    this.Load += new EventHandler(this.frmPortConfig_Load);
    frmPortConfig.__ENCAddToList((object) this);
    this.pdch_name = string.Empty;
    this.platform_no = string.Empty;
    this.pdch_addr = (byte) 0;
    this.pdch_shared_Pfno = string.Empty;
    this.InitializeComponent();
  }

  [DebuggerNonUserCode]
  private static void __ENCAddToList(object value)
  {
    lock (frmPortConfig.__ENCList)
    {
      if (frmPortConfig.__ENCList.Count == frmPortConfig.__ENCList.Capacity)
      {
        int index1 = 0;
        int num = checked (frmPortConfig.__ENCList.Count - 1);
        int index2 = 0;
        while (index2 <= num)
        {
          if (frmPortConfig.__ENCList[index2].IsAlive)
          {
            if (index2 != index1)
              frmPortConfig.__ENCList[index1] = frmPortConfig.__ENCList[index2];
            checked { ++index1; }
          }
          checked { ++index2; }
        }
        frmPortConfig.__ENCList.RemoveRange(index1, checked (frmPortConfig.__ENCList.Count - index1));
        frmPortConfig.__ENCList.Capacity = frmPortConfig.__ENCList.Count;
      }
      frmPortConfig.__ENCList.Add(new WeakReference(RuntimeHelpers.GetObjectValue(value)));
    }
  }

  [DebuggerNonUserCode]
  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  [DebuggerStepThrough]
  private void InitializeComponent()
  {
    this.radMdch = new RadioButton();
    this.radPdch = new RadioButton();
    this.Panel1 = new Panel();
    this.txtSharedPfno = new TextBox();
    this.lblSharedPfno = new Label();
    this.txtName = new TextBox();
    this.lblName = new Label();
    this.lblPfno = new Label();
    this.cmbPfno = new ComboBox();
    this.btnSet = new Button();
    this.btnGet = new Button();
    this.txtAddress = new TextBox();
    this.lblAddress = new Label();
    this.btnExit = new Button();
    this.RadioButton1 = new RadioButton();
    this.Panel1.SuspendLayout();
    this.SuspendLayout();
    this.radMdch.AutoSize = true;
    this.radMdch.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    RadioButton radMdch1 = this.radMdch;
    Point point1 = new Point(23, 71);
    Point point2 = point1;
    radMdch1.Location = point2;
    this.radMdch.Name = "radMdch";
    RadioButton radMdch2 = this.radMdch;
    Size size1 = new Size(70, 20);
    Size size2 = size1;
    radMdch2.Size = size2;
    this.radMdch.TabIndex = 1;
    this.radMdch.TabStop = true;
    this.radMdch.Text = "MDCH";
    this.radMdch.UseVisualStyleBackColor = true;
    this.radPdch.AutoSize = true;
    this.radPdch.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    RadioButton radPdch1 = this.radPdch;
    point1 = new Point(23, 154);
    Point point3 = point1;
    radPdch1.Location = point3;
    this.radPdch.Name = "radPdch";
    RadioButton radPdch2 = this.radPdch;
    size1 = new Size(68, 20);
    Size size3 = size1;
    radPdch2.Size = size3;
    this.radPdch.TabIndex = 2;
    this.radPdch.TabStop = true;
    this.radPdch.Text = "PDCH";
    this.radPdch.UseVisualStyleBackColor = true;
    this.Panel1.BackColor = SystemColors.ButtonHighlight;
    this.Panel1.BorderStyle = BorderStyle.Fixed3D;
    this.Panel1.Controls.Add((Control) this.txtSharedPfno);
    this.Panel1.Controls.Add((Control) this.lblSharedPfno);
    this.Panel1.Controls.Add((Control) this.txtName);
    this.Panel1.Controls.Add((Control) this.lblName);
    this.Panel1.Controls.Add((Control) this.lblPfno);
    this.Panel1.Controls.Add((Control) this.cmbPfno);
    this.Panel1.Controls.Add((Control) this.btnSet);
    this.Panel1.Controls.Add((Control) this.btnGet);
    this.Panel1.Controls.Add((Control) this.txtAddress);
    this.Panel1.Controls.Add((Control) this.lblAddress);
    Panel panel1_1 = this.Panel1;
    point1 = new Point(134, 16 /*0x10*/);
    Point point4 = point1;
    panel1_1.Location = point4;
    this.Panel1.Name = "Panel1";
    Panel panel1_2 = this.Panel1;
    size1 = new Size(286, 297);
    Size size4 = size1;
    panel1_2.Size = size4;
    this.Panel1.TabIndex = 2;
    this.txtSharedPfno.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtSharedPfno1 = this.txtSharedPfno;
    point1 = new Point(177, 85);
    Point point5 = point1;
    txtSharedPfno1.Location = point5;
    this.txtSharedPfno.MaxLength = 2;
    this.txtSharedPfno.Name = "txtSharedPfno";
    this.txtSharedPfno.ReadOnly = true;
    TextBox txtSharedPfno2 = this.txtSharedPfno;
    size1 = new Size(47, 22);
    Size size5 = size1;
    txtSharedPfno2.Size = size5;
    this.txtSharedPfno.TabIndex = 4;
    this.txtSharedPfno.Visible = false;
    this.lblSharedPfno.AutoSize = true;
    this.lblSharedPfno.BackColor = SystemColors.ButtonHighlight;
    this.lblSharedPfno.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.lblSharedPfno.ForeColor = SystemColors.WindowText;
    Label lblSharedPfno1 = this.lblSharedPfno;
    point1 = new Point(3, 85);
    Point point6 = point1;
    lblSharedPfno1.Location = point6;
    this.lblSharedPfno.Name = "lblSharedPfno";
    Label lblSharedPfno2 = this.lblSharedPfno;
    size1 = new Size(143, 16 /*0x10*/);
    Size size6 = size1;
    lblSharedPfno2.Size = size6;
    this.lblSharedPfno.TabIndex = 258;
    this.lblSharedPfno.Text = "Shared Platform No";
    this.lblSharedPfno.Visible = false;
    this.txtName.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtName1 = this.txtName;
    point1 = new Point(177, 175);
    Point point7 = point1;
    txtName1.Location = point7;
    this.txtName.MaxLength = 15;
    this.txtName.Name = "txtName";
    this.txtName.ReadOnly = true;
    TextBox txtName2 = this.txtName;
    size1 = new Size(85, 22);
    Size size7 = size1;
    txtName2.Size = size7;
    this.txtName.TabIndex = 6;
    this.lblName.AutoSize = true;
    this.lblName.BackColor = SystemColors.ButtonHighlight;
    this.lblName.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.lblName.ForeColor = SystemColors.WindowText;
    Label lblName1 = this.lblName;
    point1 = new Point(97, 178);
    Point point8 = point1;
    lblName1.Location = point8;
    this.lblName.Name = "lblName";
    Label lblName2 = this.lblName;
    size1 = new Size(49, 16 /*0x10*/);
    Size size8 = size1;
    lblName2.Size = size8;
    this.lblName.TabIndex = 256 /*0x0100*/;
    this.lblName.Text = "Name";
    this.lblPfno.AutoSize = true;
    this.lblPfno.BackColor = SystemColors.ButtonHighlight;
    this.lblPfno.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.lblPfno.ForeColor = SystemColors.WindowText;
    Label lblPfno1 = this.lblPfno;
    point1 = new Point(57, 32 /*0x20*/);
    Point point9 = point1;
    lblPfno1.Location = point9;
    this.lblPfno.Name = "lblPfno";
    Label lblPfno2 = this.lblPfno;
    size1 = new Size(89, 16 /*0x10*/);
    Size size9 = size1;
    lblPfno2.Size = size9;
    this.lblPfno.TabIndex = 254;
    this.lblPfno.Text = "Platform No";
    this.cmbPfno.DropDownStyle = ComboBoxStyle.DropDownList;
    this.cmbPfno.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.cmbPfno.FormattingEnabled = true;
    ComboBox cmbPfno1 = this.cmbPfno;
    point1 = new Point(177, 29);
    Point point10 = point1;
    cmbPfno1.Location = point10;
    this.cmbPfno.Name = "cmbPfno";
    ComboBox cmbPfno2 = this.cmbPfno;
    size1 = new Size(47, 24);
    Size size10 = size1;
    cmbPfno2.Size = size10;
    this.cmbPfno.TabIndex = 3;
    this.btnSet.BackColor = SystemColors.ButtonFace;
    this.btnSet.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.btnSet.ForeColor = SystemColors.WindowText;
    Button btnSet1 = this.btnSet;
    point1 = new Point(54, 241);
    Point point11 = point1;
    btnSet1.Location = point11;
    this.btnSet.Name = "btnSet";
    Button btnSet2 = this.btnSet;
    size1 = new Size(60, 25);
    Size size11 = size1;
    btnSet2.Size = size11;
    this.btnSet.TabIndex = 7;
    this.btnSet.Text = "&Set";
    this.btnSet.UseVisualStyleBackColor = false;
    this.btnGet.BackColor = SystemColors.ButtonFace;
    this.btnGet.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.btnGet.ForeColor = SystemColors.WindowText;
    Button btnGet1 = this.btnGet;
    point1 = new Point(177, 241);
    Point point12 = point1;
    btnGet1.Location = point12;
    this.btnGet.Name = "btnGet";
    Button btnGet2 = this.btnGet;
    size1 = new Size(60, 25);
    Size size12 = size1;
    btnGet2.Size = size12;
    this.btnGet.TabIndex = 8;
    this.btnGet.Text = "&Get";
    this.btnGet.UseVisualStyleBackColor = false;
    this.txtAddress.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtAddress1 = this.txtAddress;
    point1 = new Point(177, 130);
    Point point13 = point1;
    txtAddress1.Location = point13;
    this.txtAddress.MaxLength = 3;
    this.txtAddress.Name = "txtAddress";
    this.txtAddress.ReadOnly = true;
    TextBox txtAddress2 = this.txtAddress;
    size1 = new Size(55, 22);
    Size size13 = size1;
    txtAddress2.Size = size13;
    this.txtAddress.TabIndex = 5;
    this.lblAddress.AutoSize = true;
    this.lblAddress.BackColor = SystemColors.ButtonHighlight;
    this.lblAddress.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.lblAddress.ForeColor = SystemColors.WindowText;
    Label lblAddress1 = this.lblAddress;
    point1 = new Point(80 /*0x50*/, 133);
    Point point14 = point1;
    lblAddress1.Location = point14;
    this.lblAddress.Name = "lblAddress";
    Label lblAddress2 = this.lblAddress;
    size1 = new Size(66, 16 /*0x10*/);
    Size size14 = size1;
    lblAddress2.Size = size14;
    this.lblAddress.TabIndex = 249;
    this.lblAddress.Text = "Address";
    this.btnExit.BackColor = SystemColors.ButtonFace;
    this.btnExit.DialogResult = DialogResult.Cancel;
    this.btnExit.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.btnExit.ForeColor = SystemColors.WindowText;
    Button btnExit1 = this.btnExit;
    point1 = new Point(33, 259);
    Point point15 = point1;
    btnExit1.Location = point15;
    this.btnExit.Name = "btnExit";
    Button btnExit2 = this.btnExit;
    size1 = new Size(60, 25);
    Size size15 = size1;
    btnExit2.Size = size15;
    this.btnExit.TabIndex = 9;
    this.btnExit.Text = "E&xit";
    this.btnExit.UseVisualStyleBackColor = false;
    this.RadioButton1.AutoSize = true;
    this.RadioButton1.Checked = true;
    RadioButton radioButton1_1 = this.RadioButton1;
    point1 = new Point(12, 295);
    Point point16 = point1;
    radioButton1_1.Location = point16;
    this.RadioButton1.Name = "RadioButton1";
    RadioButton radioButton1_2 = this.RadioButton1;
    size1 = new Size(14, 13);
    Size size16 = size1;
    radioButton1_2.Size = size16;
    this.RadioButton1.TabIndex = 3;
    this.RadioButton1.TabStop = true;
    this.RadioButton1.UseVisualStyleBackColor = true;
    this.RadioButton1.Visible = false;
    this.AcceptButton = (IButtonControl) this.btnSet;
    this.AutoScaleDimensions = new SizeF(7f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.BackColor = Color.PowderBlue;
    this.CancelButton = (IButtonControl) this.btnExit;
    size1 = new Size(434, 320);
    this.ClientSize = size1;
    this.Controls.Add((Control) this.RadioButton1);
    this.Controls.Add((Control) this.Panel1);
    this.Controls.Add((Control) this.radPdch);
    this.Controls.Add((Control) this.radMdch);
    this.Controls.Add((Control) this.btnExit);
    this.Font = new Font("Microsoft Sans Serif", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.FormBorderStyle = FormBorderStyle.FixedToolWindow;
    this.Name = "frmPortConfig";
    this.Text = "HUB Configuration";
    this.Panel1.ResumeLayout(false);
    this.Panel1.PerformLayout();
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  internal virtual RadioButton radMdch
  {
    [DebuggerNonUserCode] get { return this._radMdch; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.radMdch_CheckedChanged);
      if (this._radMdch != null)
        this._radMdch.CheckedChanged -= eventHandler;
      this._radMdch = value;
      if (this._radMdch == null)
        return;
      this._radMdch.CheckedChanged += eventHandler;
    }
  }

  internal virtual RadioButton radPdch
  {
    [DebuggerNonUserCode] get { return this._radPdch; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.radPdch_CheckedChanged);
      if (this._radPdch != null)
        this._radPdch.CheckedChanged -= eventHandler;
      this._radPdch = value;
      if (this._radPdch == null)
        return;
      this._radPdch.CheckedChanged += eventHandler;
    }
  }

  internal virtual Panel Panel1
  {
    [DebuggerNonUserCode] get { return this._Panel1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Panel1 = value; }
  }

  internal virtual TextBox txtName
  {
    [DebuggerNonUserCode] get { return this._txtName; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._txtName = value; }
  }

  internal virtual Label lblName
  {
    [DebuggerNonUserCode] get { return this._lblName; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._lblName = value; }
  }

  internal virtual Button btnExit
  {
    [DebuggerNonUserCode] get { return this._btnExit; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnExit_Click);
      if (this._btnExit != null)
        this._btnExit.Click -= eventHandler;
      this._btnExit = value;
      if (this._btnExit == null)
        return;
      this._btnExit.Click += eventHandler;
    }
  }

  internal virtual Label lblPfno
  {
    [DebuggerNonUserCode] get { return this._lblPfno; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._lblPfno = value; }
  }

  internal virtual ComboBox cmbPfno
  {
    [DebuggerNonUserCode] get { return this._cmbPfno; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.cmbPfno_SelectedIndexChanged);
      if (this._cmbPfno != null)
        this._cmbPfno.SelectedIndexChanged -= eventHandler;
      this._cmbPfno = value;
      if (this._cmbPfno == null)
        return;
      this._cmbPfno.SelectedIndexChanged += eventHandler;
    }
  }

  internal virtual Button btnSet
  {
    [DebuggerNonUserCode] get { return this._btnSet; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._btnSet = value; }
  }

  internal virtual Button btnGet
  {
    [DebuggerNonUserCode] get { return this._btnGet; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnGet_Click);
      if (this._btnGet != null)
        this._btnGet.Click -= eventHandler;
      this._btnGet = value;
      if (this._btnGet == null)
        return;
      this._btnGet.Click += eventHandler;
    }
  }

  internal virtual TextBox txtAddress
  {
    [DebuggerNonUserCode] get { return this._txtAddress; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtAddress = value;
    }
  }

  internal virtual Label lblAddress
  {
    [DebuggerNonUserCode] get { return this._lblAddress; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblAddress = value;
    }
  }

  internal virtual TextBox txtSharedPfno
  {
    [DebuggerNonUserCode] get { return this._txtSharedPfno; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtSharedPfno = value;
    }
  }

  internal virtual Label lblSharedPfno
  {
    [DebuggerNonUserCode] get { return this._lblSharedPfno; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblSharedPfno = value;
    }
  }

  internal virtual RadioButton RadioButton1
  {
    [DebuggerNonUserCode] get { return this._RadioButton1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._RadioButton1 = value;
    }
  }

  private void radMdch_CheckedChanged(object sender, EventArgs e)
  {
    this.cmbPfno.Visible = false;
    this.lblPfno.Visible = false;
    this.txtName.Text = string.Empty;
    this.txtAddress.Text = string.Empty;
    if (!this.radMdch.Checked)
      return;
    this.txtName.Text = frmMainFormIPIS.mdch_db.mdch_name;
    this.txtAddress.Text = Conversions.ToString(frmMainFormIPIS.mdch_db.mdch_addr);
  }

  private void radPdch_CheckedChanged(object sender, EventArgs e)
  {
    this.cmbPfno.Visible = true;
    this.lblPfno.Visible = true;
    this.txtName.Text = string.Empty;
    this.txtAddress.Text = string.Empty;
  }

  private void cmbPfno_SelectedIndexChanged(object sender, EventArgs e)
  {
    try
    {
      this.txtName.Text = string.Empty;
      this.txtAddress.Text = string.Empty;
      this.platform_no = this.cmbPfno.Text;
      bool pdch_shared = false;
      if (network_db_read.pdch_info_data(this.platform_no, ref this.pdch_name, ref this.pdch_addr, ref pdch_shared, ref this.pdch_shared_Pfno) == (byte) 1)
      {
        this.txtAddress.Text = Conversions.ToString(this.pdch_addr);
        this.txtName.Text = this.pdch_name;
        if (pdch_shared)
        {
          this.lblSharedPfno.Visible = true;
          this.txtSharedPfno.Visible = true;
          this.txtSharedPfno.Text = this.pdch_shared_Pfno;
        }
        else
        {
          this.lblSharedPfno.Visible = false;
          this.txtSharedPfno.Visible = false;
        }
      }
      else
      {
        Log_file.Log("PDCH details for requested Platform NO:{this.platform_no} is not available");
        int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "PDCH details for Requested PLATFORM NO:{this.platform_no} is not available\r\nPlease check network configuration", "Msg Box", 0, 0, 0);
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void btnExit_Click(object sender, EventArgs e)
{
  this.Close();
}

  public void mdch_cfg_get_display(
    byte mdch_addr,
    string mdch_name,
    byte mdch_sys_cfg_byte,
    byte[] get_cfg_array)
  {
    int index = 0;
    int num1 = 0;
    string str = string.Empty;
    try
    {
      switch ((byte) ((int) mdch_sys_cfg_byte - 1))
      {
        case 0:
          str = "System Configuration is  available";
          break;
        case 1:
          str = "System configuration with default values";
          break;
        case 2:
          str = "N0 configuration ";
          break;
      }
      MyProject.Forms.frmMdchCfgGetDisplay.txtMdchAddr.Text = Conversions.ToString(mdch_addr);
      MyProject.Forms.frmMdchCfgGetDisplay.txtMdchName.Text = mdch_name;
      MyProject.Forms.frmMdchCfgGetDisplay.txtSystem_status.Text = str;
      int rowIndex = 0;
      num1 = 0;
      while (rowIndex < 16 /*0x10*/)
      {
        MyProject.Forms.frmMdchCfgGetDisplay.mdch_dgv.Rows.Add();
        int num2 = index;
        int columnIndex = 0;
        while (index < checked (num2 + 20 + 3))
        {
          MyProject.Forms.frmMdchCfgGetDisplay.mdch_dgv[columnIndex, rowIndex].Value = columnIndex != 1 ? (object) get_cfg_array[index] : (get_cfg_array[index] != (byte) 0 ? (object) "Multi System" : (object) "Single System");
          checked { ++columnIndex; }
          checked { ++index; }
        }
        checked { ++rowIndex; }
      }
      MyProject.Forms.frmMdchCfgGetDisplay.Show();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num3 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  public void pdch_cfg_get_display(
    byte pdch_addr,
    string pdch_name,
    byte pdch_sys_cfg_byte,
    byte[] get_cfg_array)
  {
    int index = 0;
    int num1 = 0;
    string str = string.Empty;
    try
    {
      switch ((byte) ((int) pdch_sys_cfg_byte - 1))
      {
        case 0:
          str = "System Configuration is  available";
          break;
        case 1:
          str = "System configuration with default values";
          break;
        case 2:
          str = "N0 configuration ";
          break;
      }
      MyProject.Forms.frmPdchCfgGetDisplay.txtPdchAddr.Text = Conversions.ToString(pdch_addr);
      MyProject.Forms.frmPdchCfgGetDisplay.txtPdchName.Text = pdch_name;
      MyProject.Forms.frmPdchCfgGetDisplay.txtSystem_status.Text = str;
      int rowIndex = 0;
      num1 = 0;
      while (rowIndex < 16 /*0x10*/)
      {
        MyProject.Forms.frmPdchCfgGetDisplay.pdch_dgv.Rows.Add();
        int num2 = index;
        int columnIndex = 0;
        while (index < checked (num2 + 20 + 3))
        {
          MyProject.Forms.frmPdchCfgGetDisplay.pdch_dgv[columnIndex, rowIndex].Value = columnIndex != 1 ? (object) get_cfg_array[index] : (get_cfg_array[index] != (byte) 0 ? (object) "Multi System" : (object) "Single System");
          checked { ++columnIndex; }
          checked { ++index; }
        }
        checked { ++rowIndex; }
      }
      MyProject.Forms.frmPdchCfgGetDisplay.Show();
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num3 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void frmPortConfig_Load(object sender, EventArgs e)
  {
    int index = 0;
    this.cmbPfno.Items.Clear();
    while (index < frmMainFormIPIS.pfno_cnt)
    {
      this.cmbPfno.Items.Add((object) frmMainFormIPIS.platform_nos[index]);
      checked { ++index; }
    }
  }

  private void btnGet_Click(object sender, EventArgs e)
  {
  }
}

}