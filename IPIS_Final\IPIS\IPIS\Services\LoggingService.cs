using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using IPIS.Models;
using IPIS.Repositories.Interfaces;
using IPIS.Services.Interfaces;
using IPIS.Utils;

namespace IPIS.Services
{
    public class LoggingService : ILoggingService
    {
        private readonly ILoggingRepository loggingRepository;

        public LoggingService(ILoggingRepository loggingRepository)
        {
            this.loggingRepository = loggingRepository;
        }

        public void LogDebug(LogCategory category, string message, string details = null, string source = null)
        {
            var logEntry = CreateLogEntry(LogLevel.Debug, category, message, details, source);
            loggingRepository.AddSystemLog(logEntry);
        }

        public void LogInfo(LogCategory category, string message, string details = null, string source = null)
        {
            var logEntry = CreateLogEntry(LogLevel.Info, category, message, details, source);
            loggingRepository.AddSystemLog(logEntry);
        }

        public void LogWarning(LogCategory category, string message, string details = null, string source = null)
        {
            var logEntry = CreateLogEntry(LogLevel.Warning, category, message, details, source);
            loggingRepository.AddSystemLog(logEntry);
        }

        public void LogError(LogCategory category, string message, string details = null, string source = null, Exception exception = null)
        {
            var logEntry = CreateLogEntry(LogLevel.Error, category, message, details, source, exception);
            loggingRepository.AddSystemLog(logEntry);
        }

        public void LogCritical(LogCategory category, string message, string details = null, string source = null, Exception exception = null)
        {
            var logEntry = CreateLogEntry(LogLevel.Critical, category, message, details, source, exception);
            loggingRepository.AddSystemLog(logEntry);
        }

        public void LogBatchOperation(LogCategory category, string operationType, string summary, List<string> details, string source = null)
        {
            var message = $"Batch {operationType}";
            var detailsBuilder = new StringBuilder();
            detailsBuilder.AppendLine(summary);

            if (details != null && details.Any())
            {
                detailsBuilder.AppendLine("\nOperation details:");
                var detailsToShow = details.Take(50).ToList();
                foreach (var detail in detailsToShow)
                {
                    detailsBuilder.AppendLine($"- {detail}");
                }
                if (details.Count > 50)
                {
                    detailsBuilder.AppendLine($"... and {details.Count - 50} more items");
                }
            }

            var logEntry = CreateLogEntry(LogLevel.Info, category, message, detailsBuilder.ToString(), source);
            loggingRepository.AddSystemLog(logEntry);
        }

        public void LogSystemBootSummary(List<string> bootSteps, TimeSpan bootTime)
        {
            var message = "System boot completed";
            var detailsBuilder = new StringBuilder();
            detailsBuilder.AppendLine($"Boot completed in {bootTime.TotalSeconds:F2} seconds");

            if (bootSteps != null && bootSteps.Any())
            {
                detailsBuilder.AppendLine("\nBoot sequence:");
                for (int i = 0; i < bootSteps.Count; i++)
                {
                    detailsBuilder.AppendLine($"{i + 1}. {bootSteps[i]}");
                }
            }
            else
            {
                detailsBuilder.AppendLine("Standard boot sequence completed");
            }

            var logEntry = CreateLogEntry(LogLevel.Info, LogCategory.System, message, detailsBuilder.ToString(), "SystemBoot");
            loggingRepository.AddSystemLog(logEntry);
        }

        public void LogDataLoadingSummary(string operationType, int totalRecords, int successCount, int failureCount, TimeSpan duration, List<string> errors = null)
        {
            var message = $"{operationType} completed";

            var detailsBuilder = new StringBuilder();
            detailsBuilder.AppendLine($"Summary: {successCount}/{totalRecords} records processed successfully in {duration.TotalSeconds:F2} seconds");
            detailsBuilder.AppendLine($"Total Records: {totalRecords}");
            detailsBuilder.AppendLine($"Successful: {successCount}");
            detailsBuilder.AppendLine($"Failed: {failureCount}");
            detailsBuilder.AppendLine($"Duration: {duration.TotalSeconds:F2} seconds");

            if (errors != null && errors.Any())
            {
                detailsBuilder.AppendLine("\nErrors encountered:");
                var errorList = errors.Take(10).ToList();
                foreach (var error in errorList)
                {
                    detailsBuilder.AppendLine($"- {error}");
                }
                if (errors.Count > 10)
                {
                    detailsBuilder.AppendLine($"... and {errors.Count - 10} more errors");
                }
            }

            var logLevel = failureCount > 0 ? LogLevel.Warning : LogLevel.Info;
            var logEntry = CreateLogEntry(logLevel, LogCategory.Database, message, detailsBuilder.ToString(), "DataLoading");
            loggingRepository.AddSystemLog(logEntry);
        }

        public void LogUserActivity(string action, LogCategory category, string entityType = null, string entityId = null,
                                   string oldValues = null, string newValues = null)
        {
            if (SessionManager.CurrentUser != null)
            {
                var activityLog = new UserActivityLog(
                    (int)SessionManager.CurrentUser.Id,
                    SessionManager.CurrentUser.Username,
                    action,
                    category,
                    entityType,
                    entityId,
                    oldValues,
                    newValues
                );

                loggingRepository.AddUserActivityLog(activityLog);
            }
        }

        public DataTable GetSystemLogs(DateTime? startDate = null, DateTime? endDate = null, LogLevel? level = null,
                                      LogCategory? category = null, string searchText = null, int limit = 1000, int offset = 0)
        {
            return loggingRepository.GetSystemLogs(startDate, endDate, level, category, searchText, limit, offset);
        }

        public DataTable GetUserActivityLogs(DateTime? startDate = null, DateTime? endDate = null, LogCategory? category = null,
                                           string username = null, string action = null, int limit = 1000, int offset = 0)
        {
            return loggingRepository.GetUserActivityLogs(startDate, endDate, category, username, action, limit, offset);
        }

        public DataTable GetErrorLogs(DateTime? startDate = null, DateTime? endDate = null, LogCategory? category = null,
                                     string searchText = null, int limit = 1000, int offset = 0)
        {
            // Get logs with Error and Critical levels
            var errorLogs = loggingRepository.GetSystemLogs(startDate, endDate, LogLevel.Error, category, searchText, limit / 2, offset / 2);
            var criticalLogs = loggingRepository.GetSystemLogs(startDate, endDate, LogLevel.Critical, category, searchText, limit / 2, offset / 2);

            // Merge the results
            var combinedTable = errorLogs.Clone();
            foreach (DataRow row in errorLogs.Rows)
            {
                combinedTable.ImportRow(row);
            }
            foreach (DataRow row in criticalLogs.Rows)
            {
                combinedTable.ImportRow(row);
            }

            // Sort by timestamp descending
            var sortedView = combinedTable.DefaultView;
            sortedView.Sort = "Timestamp DESC";
            return sortedView.ToTable();
        }

        public DataTable GetInfoLogs(DateTime? startDate = null, DateTime? endDate = null, LogCategory? category = null,
                                    string searchText = null, int limit = 1000, int offset = 0)
        {
            // Get logs with Debug, Info, and Warning levels
            var debugLogs = loggingRepository.GetSystemLogs(startDate, endDate, LogLevel.Debug, category, searchText, limit / 3, offset / 3);
            var infoLogs = loggingRepository.GetSystemLogs(startDate, endDate, LogLevel.Info, category, searchText, limit / 3, offset / 3);
            var warningLogs = loggingRepository.GetSystemLogs(startDate, endDate, LogLevel.Warning, category, searchText, limit / 3, offset / 3);

            // Merge the results
            var combinedTable = debugLogs.Clone();
            foreach (DataRow row in debugLogs.Rows)
            {
                combinedTable.ImportRow(row);
            }
            foreach (DataRow row in infoLogs.Rows)
            {
                combinedTable.ImportRow(row);
            }
            foreach (DataRow row in warningLogs.Rows)
            {
                combinedTable.ImportRow(row);
            }

            // Sort by timestamp descending
            var sortedView = combinedTable.DefaultView;
            sortedView.Sort = "Timestamp DESC";
            return sortedView.ToTable();
        }

        public int GetLogCount(LogLevel? level = null, LogCategory? category = null, DateTime? startDate = null, DateTime? endDate = null)
        {
            return loggingRepository.GetLogCount(level, category, startDate, endDate);
        }

        public Dictionary<LogLevel, int> GetLogCountByLevel(DateTime? startDate = null, DateTime? endDate = null)
        {
            return loggingRepository.GetLogCountByLevel(startDate, endDate);
        }

        public Dictionary<LogCategory, int> GetLogCountByCategory(DateTime? startDate = null, DateTime? endDate = null)
        {
            return loggingRepository.GetLogCountByCategory(startDate, endDate);
        }

        public void CleanupOldLogs(int daysToKeep = 30)
        {
            var cutoffDate = DateTime.Now.AddDays(-daysToKeep);
            loggingRepository.DeleteOldLogs(cutoffDate);

            LogInfo(LogCategory.System, $"Cleaned up logs older than {daysToKeep} days",
                   $"Cutoff date: {cutoffDate:yyyy-MM-dd HH:mm:ss}", "LoggingService.CleanupOldLogs");
        }

        public void ExportLogs(string filePath, DateTime? startDate = null, DateTime? endDate = null, LogLevel? level = null)
        {
            try
            {
                var logs = loggingRepository.ExportLogs(startDate, endDate, level);

                using (var writer = new StreamWriter(filePath, false, Encoding.UTF8))
                {
                    // Write header
                    var headers = new List<string>();
                    foreach (DataColumn column in logs.Columns)
                    {
                        headers.Add(column.ColumnName);
                    }
                    writer.WriteLine(string.Join(",", headers));

                    // Write data
                    foreach (DataRow row in logs.Rows)
                    {
                        var values = new List<string>();
                        foreach (var item in row.ItemArray)
                        {
                            values.Add($"\"{item?.ToString()?.Replace("\"", "\"\"")}\"");
                        }
                        writer.WriteLine(string.Join(",", values));
                    }
                }

                LogInfo(LogCategory.System, $"Logs exported successfully",
                       $"File: {filePath}, Records: {logs.Rows.Count}", "LoggingService.ExportLogs");
            }
            catch (Exception ex)
            {
                LogError(LogCategory.System, "Failed to export logs",
                        $"File: {filePath}", "LoggingService.ExportLogs", ex);
                throw;
            }
        }

        private LogEntry CreateLogEntry(LogLevel level, LogCategory category, string message, string details = null,
                                       string source = null, Exception exception = null)
        {
            var logEntry = new LogEntry(level, category, message, details, source, exception);

            if (SessionManager.CurrentUser != null)
            {
                logEntry.UserId = (int)SessionManager.CurrentUser.Id;
                logEntry.Username = SessionManager.CurrentUser.Username;
            }

            return logEntry;
        }
    }
}
