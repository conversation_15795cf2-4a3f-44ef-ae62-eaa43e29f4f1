using System;

namespace IPIS.Models
{
    public class LogEntry
    {
        public int Id { get; set; }
        public DateTime Timestamp { get; set; }
        public LogLevel Level { get; set; }
        public LogCategory Category { get; set; }
        public string Message { get; set; }
        public string Details { get; set; }
        public int? UserId { get; set; }
        public string Username { get; set; }
        public string Source { get; set; }
        public string Exception { get; set; }
        public DateTime CreatedAt { get; set; }

        public LogEntry()
        {
            Timestamp = DateTime.Now;
            CreatedAt = DateTime.Now;
        }

        public LogEntry(LogLevel level, LogCategory category, string message, string details = null, string source = null, Exception exception = null)
        {
            Timestamp = DateTime.Now;
            CreatedAt = DateTime.Now;
            Level = level;
            Category = category;
            Message = message;
            Details = details;
            Source = source;
            Exception = exception?.ToString();
        }

        public bool IsError => Level == LogLevel.Error || Level == LogLevel.Critical;
        public bool IsInfo => Level == LogLevel.Info || Level == LogLevel.Debug || Level == LogLevel.Warning;

        public string GetLevelDisplayName()
        {
            return Level switch
            {
                LogLevel.Debug => "Debug",
                LogLevel.Info => "Info",
                LogLevel.Warning => "Warning",
                LogLevel.Error => "Error",
                LogLevel.Critical => "Critical",
                _ => "Unknown"
            };
        }

        public string GetCategoryDisplayName()
        {
            return Category switch
            {
                LogCategory.TrainManagement => "Train Management",
                LogCategory.StationManagement => "Station Management",
                LogCategory.UserManagement => "User Management",
                LogCategory.Announcement => "Announcement",
                LogCategory.Advertising => "Advertising",
                LogCategory.System => "System",
                LogCategory.Database => "Database",
                LogCategory.Audio => "Audio",
                LogCategory.Error => "Error",
                LogCategory.Security => "Security",
                _ => "Unknown"
            };
        }
    }
}
