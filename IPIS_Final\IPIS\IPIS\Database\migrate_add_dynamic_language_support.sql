-- Migration: Add dynamic language support for advertising
-- This migration creates a new table to store language-specific wave files for advertisements

-- Drop existing table if it exists to avoid foreign key issues
DROP TABLE IF EXISTS AdvertisementLanguageWaves;

-- Create new table for advertisement language wave files (without foreign key constraints)
CREATE TABLE AdvertisementLanguageWaves (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    Ann_Type TEXT NOT NULL,
    Adver_Name TEXT NOT NULL,
    Language_Code TEXT NOT NULL,
    Wave_File TEXT,
    Created_At TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    Updated_At TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    AdvertisingId INTEGER,
    UNIQUE(Ann_Type, Adver_Name, Language_Code)
);

-- Create index for better performance
CREATE INDEX idx_advertisement_language_waves_lookup 
ON AdvertisementLanguageWaves(Ann_Type, Adver_Name, Language_Code);

-- Create index for foreign key relationship (handled in application layer)
CREATE INDEX idx_advertisement_language_waves_fk 
ON AdvertisementLanguageWaves(Ann_<PERSON>, Adver_Name);

-- Migrate existing Hindi and English wave files to the new structure
INSERT OR IGNORE INTO AdvertisementLanguageWaves (Ann_<PERSON>, Adver_Name, Language_Code, Wave_File)
SELECT 
    Ann_Type, 
    Adver_Name, 
    'HINDI' as Language_Code, 
    Hindi_Wave as Wave_File
FROM Advertising 
WHERE Hindi_Wave IS NOT NULL AND Hindi_Wave != '';

INSERT OR IGNORE INTO AdvertisementLanguageWaves (Ann_Type, Adver_Name, Language_Code, Wave_File)
SELECT 
    Ann_Type, 
    Adver_Name, 
    'ENGLISH' as Language_Code, 
    Eng_Wave as Wave_File
FROM Advertising 
WHERE Eng_Wave IS NOT NULL AND Eng_Wave != '';

-- Populate AdvertisingId based on Ann_Type and Adver_Name
UPDATE AdvertisementLanguageWaves
SET AdvertisingId = (
  SELECT rowid FROM Advertising
  WHERE Advertising.Ann_Type = AdvertisementLanguageWaves.Ann_Type
    AND Advertising.Adver_Name = AdvertisementLanguageWaves.Adver_Name
);

-- Add a trigger to update the Updated_At timestamp
CREATE TRIGGER update_advertisement_language_waves_timestamp 
AFTER UPDATE ON AdvertisementLanguageWaves
BEGIN
    UPDATE AdvertisementLanguageWaves 
    SET Updated_At = CURRENT_TIMESTAMP 
    WHERE Id = NEW.Id;
END; 