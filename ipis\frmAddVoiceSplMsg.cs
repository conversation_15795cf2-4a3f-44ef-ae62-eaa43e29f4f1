// Decompiled with JetBrains decompiler
// Type: ipis.frmAddVoiceSplMsg
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

namespace ipis
{

[DesignerGenerated]
public class frmAddVoiceSplMsg : Form
{
  private static List<WeakReference> __ENCList = new List<WeakReference>();
  private IContainer components;
  [AccessedThroughProperty("btnExit")]
  private Button _btnExit;
  [AccessedThroughProperty("btnSave")]
  private Button _btnSave;
  [AccessedThroughProperty("btnDelete")]
  private Button _btnDelete;
  [AccessedThroughProperty("btnAdd")]
  private Button _btnAdd;
  [AccessedThroughProperty("txtMessageName")]
  private TextBox _txtMessageName;
  [AccessedThroughProperty("lblMsgName")]
  private Label _lblMsgName;
  [AccessedThroughProperty("lblMsgId")]
  private Label _lblMsgId;
  [AccessedThroughProperty("cmbMsgId")]
  private ComboBox _cmbMsgId;
  [AccessedThroughProperty("txtEngPath")]
  private TextBox _txtEngPath;
  [AccessedThroughProperty("lblEngPath")]
  private Label _lblEngPath;
  [AccessedThroughProperty("txtRegPath")]
  private TextBox _txtRegPath;
  [AccessedThroughProperty("lblRegPath")]
  private Label _lblRegPath;
  [AccessedThroughProperty("txtHinPath")]
  private TextBox _txtHinPath;
  [AccessedThroughProperty("Label3")]
  private Label _Label3;
  [AccessedThroughProperty("btnEngBrowse")]
  private Button _btnEngBrowse;
  [AccessedThroughProperty("btnRegBrowse")]
  private Button _btnRegBrowse;
  [AccessedThroughProperty("btnHinBrowse")]
  private Button _btnHinBrowse;
  [AccessedThroughProperty("btnEdit")]
  private Button _btnEdit;
  private int cnt_rows;

  [DebuggerNonUserCode]
  static frmAddVoiceSplMsg()
  {
  }

  [DebuggerNonUserCode]
  public frmAddVoiceSplMsg()
  {
    this.FormClosed += new FormClosedEventHandler(this.frmAddVoiceSplMsg_FormClosed);
    this.Load += new EventHandler(this.frmAddMsg_Load);
    frmAddVoiceSplMsg.__ENCAddToList((object) this);
    this.InitializeComponent();
  }

  [DebuggerNonUserCode]
  private static void __ENCAddToList(object value)
  {
    lock (frmAddVoiceSplMsg.__ENCList)
    {
      if (frmAddVoiceSplMsg.__ENCList.Count == frmAddVoiceSplMsg.__ENCList.Capacity)
      {
        int index1 = 0;
        int num = checked (frmAddVoiceSplMsg.__ENCList.Count - 1);
        int index2 = 0;
        while (index2 <= num)
        {
          if (frmAddVoiceSplMsg.__ENCList[index2].IsAlive)
          {
            if (index2 != index1)
              frmAddVoiceSplMsg.__ENCList[index1] = frmAddVoiceSplMsg.__ENCList[index2];
            checked { ++index1; }
          }
          checked { ++index2; }
        }
        frmAddVoiceSplMsg.__ENCList.RemoveRange(index1, checked (frmAddVoiceSplMsg.__ENCList.Count - index1));
        frmAddVoiceSplMsg.__ENCList.Capacity = frmAddVoiceSplMsg.__ENCList.Count;
      }
      frmAddVoiceSplMsg.__ENCList.Add(new WeakReference(RuntimeHelpers.GetObjectValue(value)));
    }
  }

  [DebuggerNonUserCode]
  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  [DebuggerStepThrough]
  private void InitializeComponent()
  {
    this.btnExit = new Button();
    this.btnSave = new Button();
    this.btnDelete = new Button();
    this.btnAdd = new Button();
    this.txtMessageName = new TextBox();
    this.lblMsgName = new Label();
    this.lblMsgId = new Label();
    this.cmbMsgId = new ComboBox();
    this.txtEngPath = new TextBox();
    this.lblEngPath = new Label();
    this.txtRegPath = new TextBox();
    this.lblRegPath = new Label();
    this.txtHinPath = new TextBox();
    this.Label3 = new Label();
    this.btnEngBrowse = new Button();
    this.btnRegBrowse = new Button();
    this.btnHinBrowse = new Button();
    this.btnEdit = new Button();
    this.SuspendLayout();
    this.btnExit.BackColor = SystemColors.ButtonFace;
    this.btnExit.DialogResult = DialogResult.Cancel;
    this.btnExit.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.btnExit.ForeColor = SystemColors.ControlText;
    Button btnExit1 = this.btnExit;
    Point point1 = new Point(442, 250);
    Point point2 = point1;
    btnExit1.Location = point2;
    this.btnExit.Name = "btnExit";
    Button btnExit2 = this.btnExit;
    Size size1 = new Size(60, 25);
    Size size2 = size1;
    btnExit2.Size = size2;
    this.btnExit.TabIndex = 12;
    this.btnExit.Text = "E&xit";
    this.btnExit.UseVisualStyleBackColor = false;
    this.btnSave.BackColor = SystemColors.ButtonFace;
    this.btnSave.Enabled = false;
    this.btnSave.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.btnSave.ForeColor = SystemColors.ControlText;
    Button btnSave1 = this.btnSave;
    point1 = new Point(253, 250);
    Point point3 = point1;
    btnSave1.Location = point3;
    this.btnSave.Name = "btnSave";
    Button btnSave2 = this.btnSave;
    size1 = new Size(60, 25);
    Size size3 = size1;
    btnSave2.Size = size3;
    this.btnSave.TabIndex = 10;
    this.btnSave.Text = "&Save";
    this.btnSave.UseVisualStyleBackColor = false;
    this.btnDelete.BackColor = SystemColors.ButtonFace;
    this.btnDelete.Enabled = false;
    this.btnDelete.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.btnDelete.ForeColor = SystemColors.ControlText;
    Button btnDelete1 = this.btnDelete;
    point1 = new Point(340, 250);
    Point point4 = point1;
    btnDelete1.Location = point4;
    this.btnDelete.Name = "btnDelete";
    Button btnDelete2 = this.btnDelete;
    size1 = new Size(68, 25);
    Size size4 = size1;
    btnDelete2.Size = size4;
    this.btnDelete.TabIndex = 11;
    this.btnDelete.Text = "&Delete";
    this.btnDelete.UseVisualStyleBackColor = false;
    this.btnAdd.BackColor = SystemColors.ButtonFace;
    this.btnAdd.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.btnAdd.ForeColor = SystemColors.ControlText;
    Button btnAdd1 = this.btnAdd;
    point1 = new Point(57, 250);
    Point point5 = point1;
    btnAdd1.Location = point5;
    this.btnAdd.Name = "btnAdd";
    Button btnAdd2 = this.btnAdd;
    size1 = new Size(60, 25);
    Size size5 = size1;
    btnAdd2.Size = size5;
    this.btnAdd.TabIndex = 9;
    this.btnAdd.Text = "&Add ";
    this.btnAdd.UseVisualStyleBackColor = false;
    this.txtMessageName.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtMessageName1 = this.txtMessageName;
    point1 = new Point(192 /*0xC0*/, 71);
    Point point6 = point1;
    txtMessageName1.Location = point6;
    this.txtMessageName.MaxLength = 100;
    this.txtMessageName.Name = "txtMessageName";
    TextBox txtMessageName2 = this.txtMessageName;
    size1 = new Size(285, 22);
    Size size6 = size1;
    txtMessageName2.Size = size6;
    this.txtMessageName.TabIndex = 2;
    this.lblMsgName.AutoSize = true;
    this.lblMsgName.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.lblMsgName.ForeColor = SystemColors.ControlText;
    Label lblMsgName1 = this.lblMsgName;
    point1 = new Point(54, 74);
    Point point7 = point1;
    lblMsgName1.Location = point7;
    this.lblMsgName.Name = "lblMsgName";
    Label lblMsgName2 = this.lblMsgName;
    size1 = new Size(117, 16 /*0x10*/);
    Size size7 = size1;
    lblMsgName2.Size = size7;
    this.lblMsgName.TabIndex = 71;
    this.lblMsgName.Text = "Message Name";
    this.lblMsgId.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.lblMsgId.ForeColor = SystemColors.ControlText;
    Label lblMsgId1 = this.lblMsgId;
    point1 = new Point(72, 32 /*0x20*/);
    Point point8 = point1;
    lblMsgId1.Location = point8;
    this.lblMsgId.Name = "lblMsgId";
    Label lblMsgId2 = this.lblMsgId;
    size1 = new Size(85, 20);
    Size size8 = size1;
    lblMsgId2.Size = size8;
    this.lblMsgId.TabIndex = 70;
    this.lblMsgId.Text = "Message ID";
    this.cmbMsgId.Enabled = false;
    this.cmbMsgId.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.cmbMsgId.FormattingEnabled = true;
    ComboBox cmbMsgId1 = this.cmbMsgId;
    point1 = new Point(192 /*0xC0*/, 29);
    Point point9 = point1;
    cmbMsgId1.Location = point9;
    this.cmbMsgId.Name = "cmbMsgId";
    ComboBox cmbMsgId2 = this.cmbMsgId;
    size1 = new Size(83, 24);
    Size size9 = size1;
    cmbMsgId2.Size = size9;
    this.cmbMsgId.Sorted = true;
    this.cmbMsgId.TabIndex = 1;
    this.txtEngPath.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtEngPath1 = this.txtEngPath;
    point1 = new Point(192 /*0xC0*/, 107);
    Point point10 = point1;
    txtEngPath1.Location = point10;
    this.txtEngPath.MaxLength = 100;
    this.txtEngPath.Name = "txtEngPath";
    TextBox txtEngPath2 = this.txtEngPath;
    size1 = new Size(285, 22);
    Size size10 = size1;
    txtEngPath2.Size = size10;
    this.txtEngPath.TabIndex = 3;
    this.lblEngPath.AutoSize = true;
    this.lblEngPath.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.lblEngPath.ForeColor = SystemColors.ControlText;
    Label lblEngPath1 = this.lblEngPath;
    point1 = new Point(16 /*0x10*/, 110);
    Point point11 = point1;
    lblEngPath1.Location = point11;
    this.lblEngPath.Name = "lblEngPath";
    Label lblEngPath2 = this.lblEngPath;
    size1 = new Size(162, 16 /*0x10*/);
    Size size11 = size1;
    lblEngPath2.Size = size11;
    this.lblEngPath.TabIndex = 75;
    this.lblEngPath.Text = "English Message Path";
    this.txtRegPath.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtRegPath1 = this.txtRegPath;
    point1 = new Point(192 /*0xC0*/, 140);
    Point point12 = point1;
    txtRegPath1.Location = point12;
    this.txtRegPath.MaxLength = 100;
    this.txtRegPath.Name = "txtRegPath";
    TextBox txtRegPath2 = this.txtRegPath;
    size1 = new Size(285, 22);
    Size size12 = size1;
    txtRegPath2.Size = size12;
    this.txtRegPath.TabIndex = 4;
    this.lblRegPath.AutoSize = true;
    this.lblRegPath.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.lblRegPath.ForeColor = SystemColors.ControlText;
    Label lblRegPath1 = this.lblRegPath;
    point1 = new Point(7, 143);
    Point point13 = point1;
    lblRegPath1.Location = point13;
    this.lblRegPath.Name = "lblRegPath";
    Label lblRegPath2 = this.lblRegPath;
    size1 = new Size(174, 16 /*0x10*/);
    Size size13 = size1;
    lblRegPath2.Size = size13;
    this.lblRegPath.TabIndex = 77;
    this.lblRegPath.Text = "Regional Message Path";
    this.txtHinPath.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtHinPath1 = this.txtHinPath;
    point1 = new Point(192 /*0xC0*/, 183);
    Point point14 = point1;
    txtHinPath1.Location = point14;
    this.txtHinPath.MaxLength = 100;
    this.txtHinPath.Name = "txtHinPath";
    TextBox txtHinPath2 = this.txtHinPath;
    size1 = new Size(285, 22);
    Size size14 = size1;
    txtHinPath2.Size = size14;
    this.txtHinPath.TabIndex = 5;
    this.Label3.AutoSize = true;
    this.Label3.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.Label3.ForeColor = SystemColors.ControlText;
    Label label3_1 = this.Label3;
    point1 = new Point(27, 186);
    Point point15 = point1;
    label3_1.Location = point15;
    this.Label3.Name = "Label3";
    Label label3_2 = this.Label3;
    size1 = new Size(147, 16 /*0x10*/);
    Size size15 = size1;
    label3_2.Size = size15;
    this.Label3.TabIndex = 79;
    this.Label3.Text = "Hindi Message Path";
    this.btnEngBrowse.BackColor = SystemColors.ButtonFace;
    this.btnEngBrowse.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnEngBrowse1 = this.btnEngBrowse;
    point1 = new Point(509, 103);
    Point point16 = point1;
    btnEngBrowse1.Location = point16;
    this.btnEngBrowse.Name = "btnEngBrowse";
    Button btnEngBrowse2 = this.btnEngBrowse;
    size1 = new Size(74, 30);
    Size size16 = size1;
    btnEngBrowse2.Size = size16;
    this.btnEngBrowse.TabIndex = 6;
    this.btnEngBrowse.Text = "Browse";
    this.btnEngBrowse.UseVisualStyleBackColor = false;
    this.btnRegBrowse.BackColor = SystemColors.ButtonFace;
    this.btnRegBrowse.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnRegBrowse1 = this.btnRegBrowse;
    point1 = new Point(509, 143);
    Point point17 = point1;
    btnRegBrowse1.Location = point17;
    this.btnRegBrowse.Name = "btnRegBrowse";
    Button btnRegBrowse2 = this.btnRegBrowse;
    size1 = new Size(74, 23);
    Size size17 = size1;
    btnRegBrowse2.Size = size17;
    this.btnRegBrowse.TabIndex = 7;
    this.btnRegBrowse.Text = "Browse";
    this.btnRegBrowse.UseVisualStyleBackColor = false;
    this.btnHinBrowse.BackColor = SystemColors.ButtonFace;
    this.btnHinBrowse.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnHinBrowse1 = this.btnHinBrowse;
    point1 = new Point(509, 183);
    Point point18 = point1;
    btnHinBrowse1.Location = point18;
    this.btnHinBrowse.Name = "btnHinBrowse";
    Button btnHinBrowse2 = this.btnHinBrowse;
    size1 = new Size(74, 25);
    Size size18 = size1;
    btnHinBrowse2.Size = size18;
    this.btnHinBrowse.TabIndex = 8;
    this.btnHinBrowse.Text = "Browse";
    this.btnHinBrowse.UseVisualStyleBackColor = false;
    this.btnEdit.BackColor = SystemColors.ButtonFace;
    this.btnEdit.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.btnEdit.ForeColor = SystemColors.ControlText;
    Button btnEdit1 = this.btnEdit;
    point1 = new Point(155, 250);
    Point point19 = point1;
    btnEdit1.Location = point19;
    this.btnEdit.Name = "btnEdit";
    Button btnEdit2 = this.btnEdit;
    size1 = new Size(60, 25);
    Size size19 = size1;
    btnEdit2.Size = size19;
    this.btnEdit.TabIndex = 80 /*0x50*/;
    this.btnEdit.Text = "&Edit";
    this.btnEdit.UseVisualStyleBackColor = false;
    this.AcceptButton = (IButtonControl) this.btnAdd;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.BackColor = Color.PowderBlue;
    this.CancelButton = (IButtonControl) this.btnExit;
    size1 = new Size(595, 287);
    this.ClientSize = size1;
    this.Controls.Add((Control) this.btnEdit);
    this.Controls.Add((Control) this.btnHinBrowse);
    this.Controls.Add((Control) this.btnRegBrowse);
    this.Controls.Add((Control) this.btnEngBrowse);
    this.Controls.Add((Control) this.txtHinPath);
    this.Controls.Add((Control) this.Label3);
    this.Controls.Add((Control) this.txtRegPath);
    this.Controls.Add((Control) this.lblRegPath);
    this.Controls.Add((Control) this.txtEngPath);
    this.Controls.Add((Control) this.lblEngPath);
    this.Controls.Add((Control) this.cmbMsgId);
    this.Controls.Add((Control) this.btnExit);
    this.Controls.Add((Control) this.btnSave);
    this.Controls.Add((Control) this.btnDelete);
    this.Controls.Add((Control) this.btnAdd);
    this.Controls.Add((Control) this.txtMessageName);
    this.Controls.Add((Control) this.lblMsgName);
    this.Controls.Add((Control) this.lblMsgId);
    this.FormBorderStyle = FormBorderStyle.FixedToolWindow;
    this.Name = "frmAddVoiceSplMsg";
    this.Text = "Add Voice Special Message";
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  internal virtual Button btnExit
  {
    [DebuggerNonUserCode] get { return this._btnExit; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnExit_Click);
      if (this._btnExit != null)
        this._btnExit.Click -= eventHandler;
      this._btnExit = value;
      if (this._btnExit == null)
        return;
      this._btnExit.Click += eventHandler;
    }
  }

  internal virtual Button btnSave
  {
    [DebuggerNonUserCode] get { return this._btnSave; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnSave_Click);
      if (this._btnSave != null)
        this._btnSave.Click -= eventHandler;
      this._btnSave = value;
      if (this._btnSave == null)
        return;
      this._btnSave.Click += eventHandler;
    }
  }

  internal virtual Button btnDelete
  {
    [DebuggerNonUserCode] get { return this._btnDelete; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnDelete_Click);
      if (this._btnDelete != null)
        this._btnDelete.Click -= eventHandler;
      this._btnDelete = value;
      if (this._btnDelete == null)
        return;
      this._btnDelete.Click += eventHandler;
    }
  }

  internal virtual Button btnAdd
  {
    [DebuggerNonUserCode] get { return this._btnAdd; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnAdd_Click);
      if (this._btnAdd != null)
        this._btnAdd.Click -= eventHandler;
      this._btnAdd = value;
      if (this._btnAdd == null)
        return;
      this._btnAdd.Click += eventHandler;
    }
  }

  internal virtual TextBox txtMessageName
  {
    [DebuggerNonUserCode] get { return this._txtMessageName; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtMessageName = value;
    }
  }

  internal virtual Label lblMsgName
  {
    [DebuggerNonUserCode] get { return this._lblMsgName; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblMsgName = value;
    }
  }

  internal virtual Label lblMsgId
  {
    [DebuggerNonUserCode] get { return this._lblMsgId; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._lblMsgId = value; }
  }

  internal virtual ComboBox cmbMsgId
  {
    [DebuggerNonUserCode] get { return this._cmbMsgId; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.cmbMsgId_SelectedIndexChanged);
      if (this._cmbMsgId != null)
        this._cmbMsgId.SelectedIndexChanged -= eventHandler;
      this._cmbMsgId = value;
      if (this._cmbMsgId == null)
        return;
      this._cmbMsgId.SelectedIndexChanged += eventHandler;
    }
  }

  internal virtual TextBox txtEngPath
  {
    [DebuggerNonUserCode] get { return this._txtEngPath; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtEngPath = value;
    }
  }

  internal virtual Label lblEngPath
  {
    [DebuggerNonUserCode] get { return this._lblEngPath; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblEngPath = value;
    }
  }

  internal virtual TextBox txtRegPath
  {
    [DebuggerNonUserCode] get { return this._txtRegPath; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtRegPath = value;
    }
  }

  internal virtual Label lblRegPath
  {
    [DebuggerNonUserCode] get { return this._lblRegPath; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.Label2_Click);
      if (this._lblRegPath != null)
        this._lblRegPath.Click -= eventHandler;
      this._lblRegPath = value;
      if (this._lblRegPath == null)
        return;
      this._lblRegPath.Click += eventHandler;
    }
  }

  internal virtual TextBox txtHinPath
  {
    [DebuggerNonUserCode] get { return this._txtHinPath; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtHinPath = value;
    }
  }

  internal virtual Label Label3
  {
    [DebuggerNonUserCode] get { return this._Label3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Label3 = value; }
  }

  internal virtual Button btnEngBrowse
  {
    [DebuggerNonUserCode] get { return this._btnEngBrowse; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnEngBrowse_Click);
      if (this._btnEngBrowse != null)
        this._btnEngBrowse.Click -= eventHandler;
      this._btnEngBrowse = value;
      if (this._btnEngBrowse == null)
        return;
      this._btnEngBrowse.Click += eventHandler;
    }
  }

  internal virtual Button btnRegBrowse
  {
    [DebuggerNonUserCode] get { return this._btnRegBrowse; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnRegBrowse_Click);
      if (this._btnRegBrowse != null)
        this._btnRegBrowse.Click -= eventHandler;
      this._btnRegBrowse = value;
      if (this._btnRegBrowse == null)
        return;
      this._btnRegBrowse.Click += eventHandler;
    }
  }

  internal virtual Button btnHinBrowse
  {
    [DebuggerNonUserCode] get { return this._btnHinBrowse; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnHinBrowse_Click);
      if (this._btnHinBrowse != null)
        this._btnHinBrowse.Click -= eventHandler;
      this._btnHinBrowse = value;
      if (this._btnHinBrowse == null)
        return;
      this._btnHinBrowse.Click += eventHandler;
    }
  }

  internal virtual Button btnEdit
  {
    [DebuggerNonUserCode] get { return this._btnEdit; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.Button1_Click);
      if (this._btnEdit != null)
        this._btnEdit.Click -= eventHandler;
      this._btnEdit = value;
      if (this._btnEdit == null)
        return;
      this._btnEdit.Click += eventHandler;
    }
  }

  private void btnAdd_Click(object sender, EventArgs e)
  {
    this.cmbMsgId.Text = string.Empty;
    this.txtMessageName.Text = string.Empty;
    this.txtEngPath.Text = string.Empty;
    this.txtHinPath.Text = string.Empty;
    this.txtRegPath.Text = string.Empty;
    this.btnDelete.Enabled = false;
    this.btnSave.Enabled = true;
    this.btnEdit.Enabled = false;
    this.cmbMsgId.Enabled = true;
  }

  private void btnSave_Click(object sender, EventArgs e)
  {
    bool success = false;
    try
    {
      if (this.btnAdd.Enabled)
      {
        int index = 0;
        while ((double) index < Conversions.ToDouble(frmVoice_Special_Messages.voice_cnt))
        {
          if (Operators.CompareString(frmVoice_Special_Messages.voice_var[index].msg_id, this.cmbMsgId.Text, false) == 0)
          {
            int num = (int) basMsgBoxEx.MsgBoxMove(this.Handle.ToInt32(), "Message_id is already Present", "Msg Box", 0, 0, 0);
            return;
          }
          checked { ++index; }
        }
        network_db_read.set_voice_msg(this.cmbMsgId.Text, this.txtMessageName.Text, this.txtEngPath.Text, this.txtRegPath.Text, this.txtHinPath.Text, ref success);
        if (success)
        {
          int num = (int) basMsgBoxEx.MsgBoxMove(this.Handle.ToInt32(), "Message Added ", "Msg Box", 0, 0, 0);
          network_db_read.get_voice_msgs();
          this.cmbMsgId.Items.Add((object) this.cmbMsgId.Text);
          frmVoice_Special_Messages.spl_voice_msg = true;
        }
        else
        {
          int num1 = (int) basMsgBoxEx.MsgBoxMove(this.Handle.ToInt32(), "Message Not Added", "Msg Box", 0, 0, 0);
        }
      }
      else if (this.btnEdit.Enabled)
      {
        network_db_read.update_voice_msg(this.cmbMsgId.Text, this.txtMessageName.Text, this.txtEngPath.Text, this.txtRegPath.Text, this.txtHinPath.Text, ref success);
        if (success)
        {
          network_db_read.get_voice_msgs();
          int num = (int) basMsgBoxEx.MsgBoxMove(this.Handle.ToInt32(), "Message Updated", "Msg Box", 0, 0, 0);
          frmVoice_Special_Messages.spl_voice_msg = true;
        }
        else
        {
          int num2 = (int) basMsgBoxEx.MsgBoxMove(this.Handle.ToInt32(), "Message Not Added", "Msg Box", 0, 0, 0);
        }
      }
      this.cmbMsgId.Text = string.Empty;
      this.txtMessageName.Text = string.Empty;
      this.txtEngPath.Text = string.Empty;
      this.txtHinPath.Text = string.Empty;
      this.txtRegPath.Text = string.Empty;
      this.btnSave.Enabled = false;
      this.btnDelete.Enabled = true;
      this.btnAdd.Enabled = true;
      this.btnEdit.Enabled = true;
      this.cmbMsgId.Enabled = false;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(this.Handle.ToInt32(), exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    frmVoice_Special_Messages.spl_voice_msg = true;
  }

  private void btnDelete_Click(object sender, EventArgs e)
  {
    this.btnAdd.Enabled = false;
    this.btnSave.Enabled = false;
    this.btnEdit.Enabled = false;
    this.cmbMsgId.Enabled = true;
  }

  private void cmbMsgId_SelectedIndexChanged(object sender, EventArgs e)
  {
    if (this.btnEdit.Enabled)
    {
      string text1 = this.cmbMsgId.Text;
      string text2 = this.txtMessageName.Text;
      string text3 = this.txtEngPath.Text;
      string text4 = this.txtRegPath.Text;
      string text5 = this.txtHinPath.Text;
      network_db_read.get_voice_msg(text1, ref text2, ref text3, ref text4, ref text5);
      this.txtHinPath.Text = text5;
      this.txtRegPath.Text = text4;
      this.txtEngPath.Text = text3;
      this.txtMessageName.Text = text2;
      this.btnSave.Enabled = true;
    }
    else if (this.btnDelete.Enabled)
    {
      bool success = false;
      network_db_read.delete_voice_msg(this.cmbMsgId.Text, ref success);
      if (success)
      {
        int num = (int) basMsgBoxEx.MsgBoxMove(this.Handle.ToInt32(), "Message Deleted Successfully", "Msg Box", 0, 0, 0);
        this.cmbMsgId.Items.Remove((object) Strings.Trim(this.cmbMsgId.Text));
        network_db_read.get_voice_msgs();
        this.cmbMsgId.Text = string.Empty;
        this.txtMessageName.Text = string.Empty;
        this.txtEngPath.Text = string.Empty;
        this.txtHinPath.Text = string.Empty;
        this.txtRegPath.Text = string.Empty;
        this.btnAdd.Enabled = true;
        this.btnEdit.Enabled = true;
        this.btnDelete.Enabled = true;
        this.cmbMsgId.Enabled = false;
        frmVoice_Special_Messages.spl_voice_msg = true;
      }
      else
      {
        int num1 = (int) basMsgBoxEx.MsgBoxMove(this.Handle.ToInt32(), "Message not Deleted", "Msg Box", 0, 0, 0);
      }
    }
  }

  private void frmAddVoiceSplMsg_FormClosed(object sender, FormClosedEventArgs e)
  {
    frmVoice_Special_Messages.spl_voice_msg = true;
  }

  private void frmAddMsg_Load(object sender, EventArgs e)
  {
    int index = 0;
    this.btnSave.Enabled = false;
    this.btnDelete.Enabled = true;
    this.btnAdd.Enabled = true;
    this.btnEdit.Enabled = true;
    this.cnt_rows = Conversions.ToInteger(frmVoice_Special_Messages.voice_cnt);
    while (index < this.cnt_rows)
    {
      this.cmbMsgId.Items.Add((object) Strings.Trim(frmVoice_Special_Messages.voice_var[index].msg_id));
      checked { ++index; }
    }
    network_db_read.get_language_details();
    if (frmMainFormIPIS.language_selection.regional_language_selected)
    {
      this.txtRegPath.Visible = true;
      this.lblRegPath.Visible = true;
      this.btnRegBrowse.Visible = true;
    }
    else
    {
      this.txtRegPath.Visible = false;
      this.lblRegPath.Visible = false;
      this.btnRegBrowse.Visible = false;
    }
  }

  private void Label2_Click(object sender, EventArgs e)
  {
  }

  private void btnEngBrowse_Click(object sender, EventArgs e)
  {
    OpenFileDialog openFileDialog = new OpenFileDialog();
    try
    {
      openFileDialog.InitialDirectory = "C:\\IPIS\\voice\\special_messages\\English";
      openFileDialog.Filter = "txt files (*.txt)|*.txt|All files (*.*)|*.*";
      openFileDialog.FilterIndex = 2;
      openFileDialog.RestoreDirectory = true;
      if (openFileDialog.ShowDialog() != DialogResult.OK)
        return;
      string fileName = openFileDialog.FileName;
      openFileDialog.OpenFile();
      this.txtEngPath.Text = fileName;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(this.Handle.ToInt32(), exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void btnHinBrowse_Click(object sender, EventArgs e)
  {
    OpenFileDialog openFileDialog = new OpenFileDialog();
    try
    {
      openFileDialog.InitialDirectory = "C:\\IPIS\\voice\\special_messages\\Hindi";
      openFileDialog.Filter = "txt files (*.txt)|*.txt|All files (*.*)|*.*";
      openFileDialog.FilterIndex = 2;
      openFileDialog.RestoreDirectory = true;
      if (openFileDialog.ShowDialog() != DialogResult.OK)
        return;
      string fileName = openFileDialog.FileName;
      openFileDialog.OpenFile();
      this.txtHinPath.Text = fileName;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(this.Handle.ToInt32(), exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void btnRegBrowse_Click(object sender, EventArgs e)
  {
    OpenFileDialog openFileDialog = new OpenFileDialog();
    try
    {
      openFileDialog.InitialDirectory = "C:\\IPIS\\voice\\special_messages\\Regional";
      openFileDialog.Filter = "txt files (*.txt)|*.txt|All files (*.*)|*.*";
      openFileDialog.FilterIndex = 2;
      openFileDialog.RestoreDirectory = true;
      if (openFileDialog.ShowDialog() != DialogResult.OK)
        return;
      string fileName = openFileDialog.FileName;
      openFileDialog.OpenFile();
      this.txtRegPath.Text = fileName;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(this.Handle.ToInt32(), exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
  }

  private void btnExit_Click(object sender, EventArgs e)
{
  this.Close();
}

  private void Button1_Click(object sender, EventArgs e)
  {
    this.btnDelete.Enabled = false;
    this.btnSave.Enabled = true;
    this.btnAdd.Enabled = false;
    this.cmbMsgId.Enabled = true;
  }
}

}