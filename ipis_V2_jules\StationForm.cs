using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using ipis_V2_jules.Data; // For DatabaseHelper

namespace ipis_V2_jules
{
    public partial class StationForm : Form
    {
        private DataGridView dgvStations;
        private TextBox txtStationCode;
        private TextBox txtStationName;
        private TextBox txtStationNameHindi;
        private TextBox txtStationNameRegional;
        private TextBox txtRegionalLangName;
        private Button btnAddStation;
        private Button btnSaveStation; // For updates later
        private Button btnLoadStations;
        private Label lblStatus;

        private DatabaseHelper _dbHelper;

        public StationForm()
        {
            InitializeComponent(); // This will be defined in StationForm.Designer.cs
            _dbHelper = new DatabaseHelper("ipis_v2.sqlite"); // Ensure DB name matches

            // Ensure database and schema are ready
            try
            {
                _dbHelper.InitializeDatabase();
            }
            catch (Exception ex)
            {
                lblStatus.Text = $"Database initialization error: {ex.Message}";
                // Disable controls if DB is not ready
                btnAddStation.Enabled = false;
                btnSaveStation.Enabled = false;
                btnLoadStations.Enabled = false;
                dgvStations.Enabled = false;
            }
        }

        private void StationForm_Load(object sender, EventArgs e)
        {
            LoadStations();
        }

        private void LoadStations()
        {
            try
            {
                dgvStations.Rows.Clear(); // Clear existing rows
                string query = "SELECT StationID, StationCode, StationName, StationNameHindi, StationNameRegional, RegionalLangName, APISource, LastUpdated FROM Stations";
                var stations = _dbHelper.ExecuteQuery(query);

                if (stations != null)
                {
                    foreach (var station in stations)
                    {
                        dgvStations.Rows.Add(
                            station["StationID"],
                            station["StationCode"],
                            station["StationName"],
                            station["StationNameHindi"],
                            station["StationNameRegional"],
                            station["RegionalLangName"],
                            station["APISource"],
                            station["LastUpdated"]
                        );
                    }
                }
                lblStatus.Text = $"Loaded {stations?.Count ?? 0} stations.";
            }
            catch (Exception ex)
            {
                lblStatus.ForeColor = Color.Red;
                lblStatus.Text = $"Error loading stations: {ex.Message}";
            }
        }

        private void btnLoadStations_Click(object sender, EventArgs e)
        {
            LoadStations();
        }

        private void btnAddStation_Click(object sender, EventArgs e)
        {
            string stationCode = txtStationCode.Text.Trim();
            string stationName = txtStationName.Text.Trim();
            string stationNameHindi = txtStationNameHindi.Text.Trim();
            string stationNameRegional = txtStationNameRegional.Text.Trim();
            string regionalLangName = txtRegionalLangName.Text.Trim();

            if (string.IsNullOrEmpty(stationCode) || string.IsNullOrEmpty(stationName))
            {
                lblStatus.ForeColor = Color.Red;
                lblStatus.Text = "Station Code and Station Name are required.";
                return;
            }

            try
            {
                string query = "INSERT INTO Stations (StationCode, StationName, StationNameHindi, StationNameRegional, RegionalLangName, APISource, LastUpdated) " +
                               "VALUES (@StationCode, @StationName, @StationNameHindi, @StationNameRegional, @RegionalLangName, @APISource, @LastUpdated)";

                var parameters = new Dictionary<string, object>
                {
                    { "@StationCode", stationCode },
                    { "@StationName", stationName },
                    { "@StationNameHindi", string.IsNullOrEmpty(stationNameHindi) ? (object)DBNull.Value : stationNameHindi },
                    { "@StationNameRegional", string.IsNullOrEmpty(stationNameRegional) ? (object)DBNull.Value : stationNameRegional },
                    { "@RegionalLangName", string.IsNullOrEmpty(regionalLangName) ? (object)DBNull.Value : regionalLangName },
                    { "@APISource", "Manual_Entry" },
                    { "@LastUpdated", DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss") }
                };

                int rowsAffected = _dbHelper.ExecuteNonQuery(query, parameters);

                if (rowsAffected > 0)
                {
                    lblStatus.ForeColor = Color.Green;
                    lblStatus.Text = "Station added successfully!";
                    LoadStations(); // Refresh grid
                    ClearInputFields();
                }
                else
                {
                    lblStatus.ForeColor = Color.Red;
                    lblStatus.Text = "Failed to add station. No rows affected.";
                }
            }
            catch (Exception ex)
            {
                lblStatus.ForeColor = Color.Red;
                // Check for unique constraint violation on StationCode
                if (ex.Message.Contains("UNIQUE constraint failed: Stations.StationCode"))
                {
                    lblStatus.Text = $"Error: Station Code '{stationCode}' already exists.";
                }
                else
                {
                    lblStatus.Text = $"Error adding station: {ex.Message}";
                }
            }
        }

        private void btnSaveStation_Click(object sender, EventArgs e)
        {
            // Placeholder for update logic.
            // This would typically involve:
            // 1. Identifying the selected station (e.g., from dgvStations or an ID field).
            // 2. Retrieving current values from TextBoxes.
            // 3. Constructing an UPDATE SQL query.
            // 4. Using _dbHelper.ExecuteNonQuery().
            // 5. Refreshing the grid and status.
            lblStatus.Text = "Save/Update functionality not yet implemented.";
        }

        private void ClearInputFields()
        {
            txtStationCode.Clear();
            txtStationName.Clear();
            txtStationNameHindi.Clear();
            txtStationNameRegional.Clear();
            txtRegionalLangName.Clear();
        }
    }
}
