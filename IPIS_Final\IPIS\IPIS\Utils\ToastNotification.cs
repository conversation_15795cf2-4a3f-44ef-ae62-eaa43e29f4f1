using System;
using System.Drawing;
using System.Windows.Forms;

namespace IPIS.Utils
{
    public class ToastNotification
    {
        private readonly Label statusLabel;
        private readonly System.Windows.Forms.Timer statusTimer;
        private readonly Form parentForm;

        public ToastNotification(Form form)
        {
            parentForm = form;
            
            // Create and configure the status label
            statusLabel = new Label
            {
                Name = "statusLabel",
                Text = "",
                AutoSize = false,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Bottom,
                Height = 30,
                Font = new Font("Segoe UI", 9, FontStyle.Bold),
                Visible = false,
                BackColor = Color.DimGray,
                ForeColor = Color.White
            };

            // Add the label to the form
            parentForm.Controls.Add(statusLabel);
            statusLabel.BringToFront();

            // Initialize the timer
            statusTimer = new System.Windows.Forms.Timer();
            statusTimer.Interval = 3000; // 3 seconds
            statusTimer.Tick += (s, e) =>
            {
                statusLabel.Visible = false;
                statusTimer.Stop();
            };
        }

        public void ShowMessage(string message, Color backColor)
        {
            statusLabel.Text = message;
            statusLabel.BackColor = backColor;
            statusLabel.Visible = true;
            statusTimer.Start();
        }

        public void ShowSuccess(string message)
        {
            ShowMessage(message, Color.Green);
        }

        public void ShowError(string message)
        {
            ShowMessage(message, Color.Red);
        }

        public void ShowInfo(string message)
        {
            ShowMessage(message, Color.Blue);
        }

        public void ShowWarning(string message)
        {
            ShowMessage(message, Color.Orange);
        }
    }
} 