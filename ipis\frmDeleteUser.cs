// Decompiled with JetBrains decompiler
// Type: ipis.frmDeleteUser
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

namespace ipis
{

[DesignerGenerated]
public class frmDeleteUser : Form
{
  private static List<WeakReference> __ENCList = new List<WeakReference>();
  private IContainer components;
  [AccessedThroughProperty("lblUserName")]
  private Label _lblUserName;
  [AccessedThroughProperty("txtUserName")]
  private TextBox _txtUserName;
  [AccessedThroughProperty("lblMsg")]
  private Label _lblMsg;
  [AccessedThroughProperty("btnOk")]
  private Button _btnOk;
  [AccessedThroughProperty("btnExit")]
  private Button _btnExit;

  [DebuggerNonUserCode]
  static frmDeleteUser()
  {
  }

  [DebuggerNonUserCode]
  public frmDeleteUser()
  {
    frmDeleteUser.__ENCAddToList((object) this);
    this.InitializeComponent();
  }

  [DebuggerNonUserCode]
  private static void __ENCAddToList(object value)
  {
    lock (frmDeleteUser.__ENCList)
    {
      if (frmDeleteUser.__ENCList.Count == frmDeleteUser.__ENCList.Capacity)
      {
        int index1 = 0;
        int num = checked (frmDeleteUser.__ENCList.Count - 1);
        int index2 = 0;
        while (index2 <= num)
        {
          if (frmDeleteUser.__ENCList[index2].IsAlive)
          {
            if (index2 != index1)
              frmDeleteUser.__ENCList[index1] = frmDeleteUser.__ENCList[index2];
            checked { ++index1; }
          }
          checked { ++index2; }
        }
        frmDeleteUser.__ENCList.RemoveRange(index1, checked (frmDeleteUser.__ENCList.Count - index1));
        frmDeleteUser.__ENCList.Capacity = frmDeleteUser.__ENCList.Count;
      }
      frmDeleteUser.__ENCList.Add(new WeakReference(RuntimeHelpers.GetObjectValue(value)));
    }
  }

  [DebuggerNonUserCode]
  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  [DebuggerStepThrough]
  private void InitializeComponent()
  {
    this.lblUserName = new Label();
    this.txtUserName = new TextBox();
    this.lblMsg = new Label();
    this.btnOk = new Button();
    this.btnExit = new Button();
    this.SuspendLayout();
    this.lblUserName.AutoSize = true;
    this.lblUserName.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblUserName1 = this.lblUserName;
    Point point1 = new Point(46, 59);
    Point point2 = point1;
    lblUserName1.Location = point2;
    this.lblUserName.Name = "lblUserName";
    Label lblUserName2 = this.lblUserName;
    Size size1 = new Size(82, 16 /*0x10*/);
    Size size2 = size1;
    lblUserName2.Size = size2;
    this.lblUserName.TabIndex = 0;
    this.lblUserName.Text = "UserName";
    this.txtUserName.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtUserName1 = this.txtUserName;
    point1 = new Point(167, 56);
    Point point3 = point1;
    txtUserName1.Location = point3;
    this.txtUserName.MaxLength = 15;
    this.txtUserName.Name = "txtUserName";
    TextBox txtUserName2 = this.txtUserName;
    size1 = new Size(100, 22);
    Size size3 = size1;
    txtUserName2.Size = size3;
    this.txtUserName.TabIndex = 1;
    this.lblMsg.AutoSize = true;
    this.lblMsg.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblMsg1 = this.lblMsg;
    point1 = new Point(45, 9);
    Point point4 = point1;
    lblMsg1.Location = point4;
    this.lblMsg.Name = "lblMsg";
    Label lblMsg2 = this.lblMsg;
    size1 = new Size(222, 16 /*0x10*/);
    Size size4 = size1;
    lblMsg2.Size = size4;
    this.lblMsg.TabIndex = 2;
    this.lblMsg.Text = "Enter User Name to be deleted";
    this.btnOk.BackColor = SystemColors.ButtonFace;
    this.btnOk.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnOk1 = this.btnOk;
    point1 = new Point(85, 116);
    Point point5 = point1;
    btnOk1.Location = point5;
    this.btnOk.Name = "btnOk";
    Button btnOk2 = this.btnOk;
    size1 = new Size(43, 23);
    Size size5 = size1;
    btnOk2.Size = size5;
    this.btnOk.TabIndex = 3;
    this.btnOk.Text = "Ok";
    this.btnOk.UseVisualStyleBackColor = false;
    this.btnExit.BackColor = SystemColors.ButtonFace;
    this.btnExit.DialogResult = DialogResult.Cancel;
    this.btnExit.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnExit1 = this.btnExit;
    point1 = new Point(158, 116);
    Point point6 = point1;
    btnExit1.Location = point6;
    this.btnExit.Name = "btnExit";
    Button btnExit2 = this.btnExit;
    size1 = new Size(42, 23);
    Size size6 = size1;
    btnExit2.Size = size6;
    this.btnExit.TabIndex = 4;
    this.btnExit.Text = "Exit";
    this.btnExit.UseVisualStyleBackColor = false;
    this.AcceptButton = (IButtonControl) this.btnOk;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.BackColor = Color.PowderBlue;
    this.CancelButton = (IButtonControl) this.btnExit;
    size1 = new Size(293, 151);
    this.ClientSize = size1;
    this.Controls.Add((Control) this.btnExit);
    this.Controls.Add((Control) this.btnOk);
    this.Controls.Add((Control) this.lblMsg);
    this.Controls.Add((Control) this.txtUserName);
    this.Controls.Add((Control) this.lblUserName);
    this.FormBorderStyle = FormBorderStyle.FixedToolWindow;
    this.Name = "frmDeleteUser";
    this.Text = "Delete User";
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  internal virtual Label lblUserName
  {
    [DebuggerNonUserCode] get { return this._lblUserName; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblUserName = value;
    }
  }

  internal virtual TextBox txtUserName
  {
    [DebuggerNonUserCode] get { return this._txtUserName; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtUserName = value;
    }
  }

  internal virtual Label lblMsg
  {
    [DebuggerNonUserCode] get { return this._lblMsg; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._lblMsg = value; }
  }

  internal virtual Button btnOk
  {
    [DebuggerNonUserCode] get { return this._btnOk; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnOk_Click);
      if (this._btnOk != null)
        this._btnOk.Click -= eventHandler;
      this._btnOk = value;
      if (this._btnOk == null)
        return;
      this._btnOk.Click += eventHandler;
    }
  }

  internal virtual Button btnExit
  {
    [DebuggerNonUserCode] get { return this._btnExit; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnExit_Click);
      if (this._btnExit != null)
        this._btnExit.Click -= eventHandler;
      this._btnExit = value;
      if (this._btnExit == null)
        return;
      this._btnExit.Click += eventHandler;
    }
  }

  private void btnOk_Click(object sender, EventArgs e)
  {
    bool found = false;
    network_db_read.delete_pwd(this.txtUserName.Text, ref found);
    if (found)
    {
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Deleted Successfully", "Msg Box", 0, 0, 0);
      int index = 0;
      while (index < (int) frmMainFormIPIS.user_cnt.cnt)
      {
        if (Operators.CompareString(frmMainFormIPIS.user_details[index].user_name, frmChangeUserDetails.user_name, false) == 0)
        {
          while (index < (int) frmMainFormIPIS.user_cnt.cnt)
          {
            frmMainFormIPIS.user_details[index].user_name = frmMainFormIPIS.user_details[checked (index + 1)].user_name;
            frmMainFormIPIS.user_details[index].user_id = frmMainFormIPIS.user_details[checked (index + 1)].user_id;
            frmMainFormIPIS.user_details[index].pwd = frmMainFormIPIS.user_details[checked (index + 1)].pwd;
            frmMainFormIPIS.user_details[index].pwd_length = frmMainFormIPIS.user_details[checked (index + 1)].pwd_length;
            checked { ++index; }
          }
          this.Close();
          return;
        }
        checked { ++index; }
      }
    }
    this.Close();
  }

  private void btnExit_Click(object sender, EventArgs e)
{
  this.Close();
}
}

}