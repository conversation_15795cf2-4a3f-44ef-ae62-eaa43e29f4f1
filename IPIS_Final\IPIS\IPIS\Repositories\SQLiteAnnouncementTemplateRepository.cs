using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SQLite;
using System.Threading.Tasks;
using IPIS.Models;
using IPIS.Repositories.Interfaces;
using IPIS.Utils;

namespace IPIS.Repositories
{
    public class SQLiteAnnouncementTemplateRepository : IAnnouncementTemplateRepository
    {
        public async Task<IEnumerable<AnnouncementTemplate>> GetAllTemplatesAsync()
        {
            var templates = new List<AnnouncementTemplate>();
            var query = "SELECT Id, Name, Description, ArrivalDeparture, IsActive, CreatedAt, UpdatedAt FROM AnnouncementTemplates ORDER BY Name";

            using (var connection = Database.GetConnection())
            {
                using (var command = new SQLiteCommand(query, connection))
                {
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            templates.Add(new AnnouncementTemplate
                            {
                                Id = reader.GetInt32("Id"),
                                Name = reader.GetString("Name"),
                                Description = reader.IsDBNull("Description") ? null : reader.GetString("Description"),
                                ArrivalDeparture = reader.GetString("ArrivalDeparture"),
                                IsActive = reader.GetBoolean("IsActive"),
                                CreatedAt = reader.GetDateTime("CreatedAt"),
                                UpdatedAt = reader.IsDBNull("UpdatedAt") ? null : (DateTime?)reader.GetDateTime("UpdatedAt")
                            });
                        }
                    }
                }
            }

            return templates;
        }

        public async Task<IEnumerable<AnnouncementTemplate>> GetActiveTemplatesAsync()
        {
            var templates = new List<AnnouncementTemplate>();
            var query = "SELECT Id, Name, Description, ArrivalDeparture, IsActive, CreatedAt, UpdatedAt FROM AnnouncementTemplates WHERE IsActive = 1 ORDER BY Name";

            using (var connection = Database.GetConnection())
            {
                using (var command = new SQLiteCommand(query, connection))
                {
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            templates.Add(new AnnouncementTemplate
                            {
                                Id = reader.GetInt32("Id"),
                                Name = reader.GetString("Name"),
                                Description = reader.IsDBNull("Description") ? null : reader.GetString("Description"),
                                ArrivalDeparture = reader.GetString("ArrivalDeparture"),
                                IsActive = reader.GetBoolean("IsActive"),
                                CreatedAt = reader.GetDateTime("CreatedAt"),
                                UpdatedAt = reader.IsDBNull("UpdatedAt") ? null : (DateTime?)reader.GetDateTime("UpdatedAt")
                            });
                        }
                    }
                }
            }

            return templates;
        }

        public async Task<IEnumerable<AnnouncementTemplate>> GetActiveTemplatesByArrivalDepartureAsync(string arrivalDeparture)
        {
            var templates = new List<AnnouncementTemplate>();
            var query = "SELECT Id, Name, Description, ArrivalDeparture, IsActive, CreatedAt, UpdatedAt FROM AnnouncementTemplates WHERE IsActive = 1 AND ArrivalDeparture = @ArrivalDeparture ORDER BY Name";

            using (var connection = Database.GetConnection())
            {
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@ArrivalDeparture", arrivalDeparture);
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            templates.Add(new AnnouncementTemplate
                            {
                                Id = reader.GetInt32("Id"),
                                Name = reader.GetString("Name"),
                                Description = reader.IsDBNull("Description") ? null : reader.GetString("Description"),
                                ArrivalDeparture = reader.GetString("ArrivalDeparture"),
                                IsActive = reader.GetBoolean("IsActive"),
                                CreatedAt = reader.GetDateTime("CreatedAt"),
                                UpdatedAt = reader.IsDBNull("UpdatedAt") ? null : (DateTime?)reader.GetDateTime("UpdatedAt")
                            });
                        }
                    }
                }
            }

            return templates;
        }

        public async Task<AnnouncementTemplate> GetTemplateByIdAsync(int id)
        {
            var query = "SELECT Id, Name, Description, ArrivalDeparture, IsActive, CreatedAt, UpdatedAt FROM AnnouncementTemplates WHERE Id = @Id";

            using (var connection = Database.GetConnection())
            {
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Id", id);

                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        if (await reader.ReadAsync())
                        {
                            return new AnnouncementTemplate
                            {
                                Id = reader.GetInt32("Id"),
                                Name = reader.GetString("Name"),
                                Description = reader.IsDBNull("Description") ? null : reader.GetString("Description"),
                                ArrivalDeparture = reader.GetString("ArrivalDeparture"),
                                IsActive = reader.GetBoolean("IsActive"),
                                CreatedAt = reader.GetDateTime("CreatedAt"),
                                UpdatedAt = reader.IsDBNull("UpdatedAt") ? null : (DateTime?)reader.GetDateTime("UpdatedAt")
                            };
                        }
                    }
                }
            }

            return null;
        }

        public async Task<AnnouncementTemplate> GetTemplateByNameAsync(string name)
        {
            var query = "SELECT Id, Name, Description, ArrivalDeparture, IsActive, CreatedAt, UpdatedAt FROM AnnouncementTemplates WHERE Name = @Name";

            using (var connection = Database.GetConnection())
            {
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Name", name);

                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        if (await reader.ReadAsync())
                        {
                            return new AnnouncementTemplate
                            {
                                Id = reader.GetInt32("Id"),
                                Name = reader.GetString("Name"),
                                Description = reader.IsDBNull("Description") ? null : reader.GetString("Description"),
                                ArrivalDeparture = reader.GetString("ArrivalDeparture"),
                                IsActive = reader.GetBoolean("IsActive"),
                                CreatedAt = reader.GetDateTime("CreatedAt"),
                                UpdatedAt = reader.IsDBNull("UpdatedAt") ? null : (DateTime?)reader.GetDateTime("UpdatedAt")
                            };
                        }
                    }
                }
            }

            return null;
        }

        public async Task<int> AddTemplateAsync(AnnouncementTemplate template)
        {
            var query = @"INSERT INTO AnnouncementTemplates (Name, Description, ArrivalDeparture, IsActive, CreatedAt) 
                         VALUES (@Name, @Description, @ArrivalDeparture, @IsActive, @CreatedAt);
                         SELECT last_insert_rowid();";

            using (var connection = Database.GetConnection())
            {
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Name", template.Name);
                    command.Parameters.AddWithValue("@Description", template.Description ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@ArrivalDeparture", template.ArrivalDeparture);
                    command.Parameters.AddWithValue("@IsActive", template.IsActive);
                    command.Parameters.AddWithValue("@CreatedAt", template.CreatedAt);

                    var result = await command.ExecuteScalarAsync();
                    return Convert.ToInt32(result);
                }
            }
        }

        public async Task<bool> UpdateTemplateAsync(AnnouncementTemplate template)
        {
            var query = @"UPDATE AnnouncementTemplates 
                         SET Name = @Name, Description = @Description, ArrivalDeparture = @ArrivalDeparture, IsActive = @IsActive, UpdatedAt = @UpdatedAt 
                         WHERE Id = @Id";

            using (var connection = Database.GetConnection())
            {
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Id", template.Id);
                    command.Parameters.AddWithValue("@Name", template.Name);
                    command.Parameters.AddWithValue("@Description", template.Description ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@ArrivalDeparture", template.ArrivalDeparture);
                    command.Parameters.AddWithValue("@IsActive", template.IsActive);
                    command.Parameters.AddWithValue("@UpdatedAt", DateTime.Now);

                    var rowsAffected = await command.ExecuteNonQueryAsync();
                    return rowsAffected > 0;
                }
            }
        }

        public async Task<bool> DeleteTemplateAsync(int id)
        {
            var query = "DELETE FROM AnnouncementTemplates WHERE Id = @Id";

            using (var connection = Database.GetConnection())
            {
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Id", id);

                    var rowsAffected = await command.ExecuteNonQueryAsync();
                    return rowsAffected > 0;
                }
            }
        }

        public async Task<bool> ToggleTemplateStatusAsync(int id)
        {
            var query = "UPDATE AnnouncementTemplates SET IsActive = NOT IsActive, UpdatedAt = @UpdatedAt WHERE Id = @Id";

            using (var connection = Database.GetConnection())
            {
                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Id", id);
                    command.Parameters.AddWithValue("@UpdatedAt", DateTime.Now);

                    var rowsAffected = await command.ExecuteNonQueryAsync();
                    return rowsAffected > 0;
                }
            }
        }
    }
} 