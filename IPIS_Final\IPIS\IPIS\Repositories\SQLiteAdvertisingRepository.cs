using System;
using System.Data;
using System.Data.SQLite;
using IPIS.Utils;

namespace IPIS.Repositories
{
    public class SQLiteAdvertisingRepository : IAdvertisingRepository
    {
        private readonly string connectionString;

        public SQLiteAdvertisingRepository()
        {
            connectionString = Database.ConnectionString;
            InitializeDatabase();
        }

        private void InitializeDatabase()
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                using (var command = new SQLiteCommand(connection))
                {
                    // Create Advertising table if it doesn't exist
                    command.CommandText = @"
                        CREATE TABLE IF NOT EXISTS Advertising (
                            Msg_Enable TEXT NOT NULL,
                            Ann_Type TEXT NOT NULL,
                            Adver_Name TEXT NOT NULL,
                            Hindi_Wave TEXT,
                            Eng_Wave TEXT,
                            Adver_Time TEXT,
                            Adver_Count TEXT,
                            Platform TEXT DEFAULT 'ALL',
                            TimeSlot TEXT,
                            TrainNumber TEXT,
                            PlayPosition TEXT DEFAULT 'Before',
                            Rank INTEGER DEFAULT 1,
                            Randomize INTEGER DEFAULT 0,
                            MonthQuota INTEGER DEFAULT 0,
                            WeekQuota INTEGER DEFAULT 0,
                            DayQuota INTEGER DEFAULT 0,
                            SlotQuota INTEGER DEFAULT 0,
                            ExtraQuota INTEGER DEFAULT 0,
                            TotalDuration REAL DEFAULT 0.0,
                            TotalDurationFormatted TEXT DEFAULT '00:00',
                            Days TEXT DEFAULT '',
                            QuotaUsed INTEGER DEFAULT 0,
                            PRIMARY KEY (Ann_Type, Adver_Name)
                        )";
                    command.ExecuteNonQuery();

                    // Check if new columns exist and add them if they don't
                    AddColumnIfNotExists(connection, "Advertising", "Platform", "TEXT DEFAULT 'ALL'");
                    AddColumnIfNotExists(connection, "Advertising", "TimeSlot", "TEXT");
                    AddColumnIfNotExists(connection, "Advertising", "TrainNumber", "TEXT");
                    AddColumnIfNotExists(connection, "Advertising", "PlayPosition", "TEXT DEFAULT 'Before'");
                    AddColumnIfNotExists(connection, "Advertising", "Rank", "INTEGER DEFAULT 1");
                    AddColumnIfNotExists(connection, "Advertising", "Randomize", "INTEGER DEFAULT 0");
                    AddColumnIfNotExists(connection, "Advertising", "MonthQuota", "INTEGER DEFAULT 0");
                    AddColumnIfNotExists(connection, "Advertising", "WeekQuota", "INTEGER DEFAULT 0");
                    AddColumnIfNotExists(connection, "Advertising", "DayQuota", "INTEGER DEFAULT 0");
                    AddColumnIfNotExists(connection, "Advertising", "SlotQuota", "INTEGER DEFAULT 0");
                    AddColumnIfNotExists(connection, "Advertising", "ExtraQuota", "INTEGER DEFAULT 0");
                    AddColumnIfNotExists(connection, "Advertising", "TotalDuration", "REAL DEFAULT 0.0");
                    AddColumnIfNotExists(connection, "Advertising", "TotalDurationFormatted", "TEXT DEFAULT '00:00'");
                    AddColumnIfNotExists(connection, "Advertising", "Days", "TEXT DEFAULT ''");
                    AddColumnIfNotExists(connection, "Advertising", "QuotaUsed", "INTEGER DEFAULT 0");

                    // Create AdvertisementLanguageWaves table for dynamic language support (only if it doesn't exist)
                    command.CommandText = @"
                        CREATE TABLE IF NOT EXISTS AdvertisementLanguageWaves (
                            Id INTEGER PRIMARY KEY AUTOINCREMENT,
                            Ann_Type TEXT NOT NULL,
                            Adver_Name TEXT NOT NULL,
                            Language_Code TEXT NOT NULL,
                            Wave_File TEXT,
                            Created_At TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            Updated_At TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            AdvertisingId INTEGER,
                            UNIQUE(Ann_Type, Adver_Name, Language_Code)
                        )";
                    command.ExecuteNonQuery();

                    // Create indexes only if they don't exist
                    try
                    {
                        command.CommandText = @"
                            CREATE INDEX IF NOT EXISTS idx_advertisement_language_waves_lookup 
                            ON AdvertisementLanguageWaves(Ann_Type, Adver_Name, Language_Code)";
                        command.ExecuteNonQuery();

                        command.CommandText = @"
                            CREATE INDEX IF NOT EXISTS idx_advertisement_language_waves_fk 
                            ON AdvertisementLanguageWaves(Ann_Type, Adver_Name)";
                        command.ExecuteNonQuery();
                    }
                    catch (Exception ex)
                    {
                        // Log the error but don't fail the application
                        System.Diagnostics.Debug.WriteLine($"Error creating indexes: {ex.Message}");
                    }

                    // Create trigger only if it doesn't exist
                    try
                    {
                        command.CommandText = @"
                            CREATE TRIGGER IF NOT EXISTS update_advertisement_language_waves_timestamp 
                            AFTER UPDATE ON AdvertisementLanguageWaves
                            BEGIN
                                UPDATE AdvertisementLanguageWaves 
                                SET Updated_At = CURRENT_TIMESTAMP 
                                WHERE Id = NEW.Id;
                            END";
                        command.ExecuteNonQuery();
                    }
                    catch (Exception ex)
                    {
                        // Log the error but don't fail the application
                        System.Diagnostics.Debug.WriteLine($"Error creating trigger: {ex.Message}");
                    }

                    // Migrate existing data only if AdvertisementLanguageWaves table is empty
                    command.CommandText = "SELECT COUNT(*) FROM AdvertisementLanguageWaves";
                    var count = Convert.ToInt32(command.ExecuteScalar());

                    if (count == 0)
                    {
                        // Migrate existing Hindi wave files
                        command.CommandText = @"
                            INSERT OR IGNORE INTO AdvertisementLanguageWaves (Ann_Type, Adver_Name, Language_Code, Wave_File)
                            SELECT Ann_Type, Adver_Name, 'HINDI' as Language_Code, Hindi_Wave as Wave_File
                            FROM Advertising 
                            WHERE Hindi_Wave IS NOT NULL AND Hindi_Wave != ''";
                        command.ExecuteNonQuery();

                        // Migrate existing English wave files
                        command.CommandText = @"
                            INSERT OR IGNORE INTO AdvertisementLanguageWaves (Ann_Type, Adver_Name, Language_Code, Wave_File)
                            SELECT Ann_Type, Adver_Name, 'ENGLISH' as Language_Code, Eng_Wave as Wave_File
                            FROM Advertising 
                            WHERE Eng_Wave IS NOT NULL AND Eng_Wave != ''";
                        command.ExecuteNonQuery();
                    }
                }
            }
        }

        private void AddColumnIfNotExists(SQLiteConnection connection, string tableName, string columnName, string columnDefinition)
        {
            try
            {
                // Check if column exists
                using (var command = new SQLiteCommand($"PRAGMA table_info({tableName})", connection))
                {
                    using (var reader = command.ExecuteReader())
                    {
                        bool columnExists = false;
                        while (reader.Read())
                        {
                            if (reader.GetString(1) == columnName)
                            {
                                columnExists = true;
                                break;
                            }
                        }
                        reader.Close();

                        if (!columnExists)
                        {
                            // Add the column
                            using (var alterCommand = new SQLiteCommand($"ALTER TABLE {tableName} ADD COLUMN {columnName} {columnDefinition}", connection))
                            {
                                alterCommand.ExecuteNonQuery();
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Log the error but don't fail the application
                System.Diagnostics.Debug.WriteLine($"Error adding column {columnName} to {tableName}: {ex.Message}");
            }
        }

        public DataTable GetAllAdvertisements()
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                using (var command = new SQLiteCommand("SELECT * FROM Advertising", connection))
                {
                    using (var adapter = new SQLiteDataAdapter(command))
                    {
                        var dataTable = new DataTable();
                        adapter.Fill(dataTable);
                        return dataTable;
                    }
                }
            }
        }

        public void AddAdvertisement(string annType, string adverName, string hindiWave, string engWave, string adverTime, string adverCount,
            string platform, string timeSlot, string trainNumber, string playPosition, int rank, int randomize, int monthQuota, int weekQuota, int dayQuota, int slotQuota, int extraQuota, string days)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                using (var command = new SQLiteCommand(connection))
                {
                    command.CommandText = @"
                        INSERT INTO Advertising(Msg_Enable, Ann_Type, Adver_Name, Hindi_Wave, Eng_Wave, Adver_Time, Adver_Count, Platform, TimeSlot, TrainNumber, PlayPosition, Rank, Randomize, MonthQuota, WeekQuota, DayQuota, SlotQuota, ExtraQuota, Days)
                        VALUES(@msgEnable, @annType, @adverName, @hindiWave, @engWave, @adverTime, @adverCount, @platform, @timeSlot, @trainNumber, @playPosition, @rank, @randomize, @monthQuota, @weekQuota, @dayQuota, @slotQuota, @extraQuota, @days)";

                    command.Parameters.AddWithValue("@msgEnable", "True");
                    command.Parameters.AddWithValue("@annType", annType);
                    command.Parameters.AddWithValue("@adverName", adverName);
                    command.Parameters.AddWithValue("@hindiWave", hindiWave);
                    command.Parameters.AddWithValue("@engWave", engWave);
                    command.Parameters.AddWithValue("@adverTime", adverTime);
                    command.Parameters.AddWithValue("@adverCount", adverCount);
                    command.Parameters.AddWithValue("@platform", platform);
                    command.Parameters.AddWithValue("@timeSlot", timeSlot);
                    command.Parameters.AddWithValue("@trainNumber", trainNumber);
                    command.Parameters.AddWithValue("@playPosition", playPosition);
                    command.Parameters.AddWithValue("@rank", rank);
                    command.Parameters.AddWithValue("@randomize", randomize);
                    command.Parameters.AddWithValue("@monthQuota", monthQuota);
                    command.Parameters.AddWithValue("@weekQuota", weekQuota);
                    command.Parameters.AddWithValue("@dayQuota", dayQuota);
                    command.Parameters.AddWithValue("@slotQuota", slotQuota);
                    command.Parameters.AddWithValue("@extraQuota", extraQuota);
                    command.Parameters.AddWithValue("@days", days);

                    command.ExecuteNonQuery();

                    // Debug: Verify the record was added
                    System.Diagnostics.Debug.WriteLine($"AddAdvertisement: Record added - {annType} - {adverName}");

                    // Verify by counting records
                    using (var countCommand = new SQLiteCommand("SELECT COUNT(*) FROM Advertising", connection))
                    {
                        int count = Convert.ToInt32(countCommand.ExecuteScalar());
                        System.Diagnostics.Debug.WriteLine($"AddAdvertisement: Total records in database: {count}");
                    }
                }
            }
        }

        public void UpdateAdvertisement(string annType, string adverName, string hindiWave, string engWave, string adverTime, string adverCount,
            string platform, string timeSlot, string trainNumber, string playPosition, int rank, int randomize, int monthQuota, int weekQuota, int dayQuota, int slotQuota, int extraQuota, string days)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                using (var command = new SQLiteCommand(connection))
                {
                    command.CommandText = @"
                        UPDATE Advertising
                        SET Hindi_Wave = @hindiWave,
                            Eng_Wave = @engWave,
                            Adver_Time = @adverTime,
                            Adver_Count = @adverCount,
                            Platform = @platform,
                            TimeSlot = @timeSlot,
                            TrainNumber = @trainNumber,
                            PlayPosition = @playPosition,
                            Rank = @rank,
                            Randomize = @randomize,
                            MonthQuota = @monthQuota,
                            WeekQuota = @weekQuota,
                            DayQuota = @dayQuota,
                            SlotQuota = @slotQuota,
                            ExtraQuota = @extraQuota,
                            Days = @days
                        WHERE Adver_Name = @adverName AND Ann_Type = @annType";

                    command.Parameters.AddWithValue("@annType", annType);
                    command.Parameters.AddWithValue("@adverName", adverName);
                    command.Parameters.AddWithValue("@hindiWave", hindiWave);
                    command.Parameters.AddWithValue("@engWave", engWave);
                    command.Parameters.AddWithValue("@adverTime", adverTime);
                    command.Parameters.AddWithValue("@adverCount", adverCount);
                    command.Parameters.AddWithValue("@platform", platform);
                    command.Parameters.AddWithValue("@timeSlot", timeSlot);
                    command.Parameters.AddWithValue("@trainNumber", trainNumber);
                    command.Parameters.AddWithValue("@playPosition", playPosition);
                    command.Parameters.AddWithValue("@rank", rank);
                    command.Parameters.AddWithValue("@randomize", randomize);
                    command.Parameters.AddWithValue("@monthQuota", monthQuota);
                    command.Parameters.AddWithValue("@weekQuota", weekQuota);
                    command.Parameters.AddWithValue("@dayQuota", dayQuota);
                    command.Parameters.AddWithValue("@slotQuota", slotQuota);
                    command.Parameters.AddWithValue("@extraQuota", extraQuota);
                    command.Parameters.AddWithValue("@days", days);

                    command.ExecuteNonQuery();
                }
            }
        }

        public void DeleteAdvertisement(string annType, string adverName)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
                {
                    try
                    {
                        // Delete related language wave files first
                        using (var deleteLangCommand = new SQLiteCommand(connection))
                        {
                            deleteLangCommand.CommandText = "DELETE FROM AdvertisementLanguageWaves WHERE Adver_Name = @adverName AND Ann_Type = @annType";
                            deleteLangCommand.Parameters.AddWithValue("@adverName", adverName);
                            deleteLangCommand.Parameters.AddWithValue("@annType", annType);
                            deleteLangCommand.ExecuteNonQuery();
                        }

                        // Delete the advertisement
                        using (var command = new SQLiteCommand(connection))
                        {
                            command.CommandText = "DELETE FROM Advertising WHERE Adver_Name = @adverName AND Ann_Type = @annType";
                            command.Parameters.AddWithValue("@adverName", adverName);
                            command.Parameters.AddWithValue("@annType", annType);
                            command.ExecuteNonQuery();
                        }

                        transaction.Commit();
                    }
                    catch
                    {
                        transaction.Rollback();
                        throw;
                    }
                }
            }
        }

        public Dictionary<string, string> GetAdvertisementLanguageWaves(int advertisingId)
        {
            System.Diagnostics.Debug.WriteLine($"GetAdvertisementLanguageWaves: Retrieving wave files for AdvertisingId={advertisingId}");
            var result = new Dictionary<string, string>();
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                using (var command = new SQLiteCommand(connection))
                {
                    command.CommandText = @"
                        SELECT Language_Code, Wave_File 
                        FROM AdvertisementLanguageWaves 
                        WHERE AdvertisingId = @advertisingId";
                    command.Parameters.AddWithValue("@advertisingId", advertisingId);
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            var languageCode = reader.GetString(0);
                            var waveFile = reader.IsDBNull(1) ? "" : reader.GetString(1);
                            result[languageCode] = waveFile;
                            System.Diagnostics.Debug.WriteLine($"Retrieved: {languageCode} -> {waveFile}");
                        }
                    }
                }
            }
            System.Diagnostics.Debug.WriteLine($"GetAdvertisementLanguageWaves: Found {result.Count} wave files");
            return result;
        }

        public void SaveAdvertisementLanguageWaves(int advertisingId, string annType, string adverName, Dictionary<string, string> languageWaves)
        {
            System.Diagnostics.Debug.WriteLine($"SaveAdvertisementLanguageWaves: Saving {languageWaves.Count} wave files for AdvertisingId={advertisingId}");
            foreach (var kvp in languageWaves)
            {
                System.Diagnostics.Debug.WriteLine($"  {kvp.Key}: {kvp.Value}");
            }
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
                {
                    try
                    {
                        // Delete existing language waves for this advertisement
                        using (var deleteCommand = new SQLiteCommand(connection))
                        {
                            deleteCommand.CommandText = @"
                                DELETE FROM AdvertisementLanguageWaves 
                                WHERE AdvertisingId = @advertisingId";
                            deleteCommand.Parameters.AddWithValue("@advertisingId", advertisingId);
                            deleteCommand.ExecuteNonQuery();
                        }
                        // Insert new language waves
                        using (var insertCommand = new SQLiteCommand(connection))
                        {
                            insertCommand.CommandText = @"
                                INSERT INTO AdvertisementLanguageWaves (Ann_Type, Adver_Name, Language_Code, Wave_File, AdvertisingId)
                                VALUES (@annType, @adverName, @languageCode, @waveFile, @advertisingId)";
                            foreach (var kvp in languageWaves)
                            {
                                if (!string.IsNullOrWhiteSpace(kvp.Value))
                                {
                                    insertCommand.Parameters.Clear();
                                    insertCommand.Parameters.AddWithValue("@annType", annType);
                                    insertCommand.Parameters.AddWithValue("@adverName", adverName);
                                    insertCommand.Parameters.AddWithValue("@languageCode", kvp.Key);
                                    insertCommand.Parameters.AddWithValue("@waveFile", kvp.Value);
                                    insertCommand.Parameters.AddWithValue("@advertisingId", advertisingId);
                                    insertCommand.ExecuteNonQuery();
                                    System.Diagnostics.Debug.WriteLine($"Inserted: {kvp.Key} -> {kvp.Value}");
                                }
                            }
                        }
                        transaction.Commit();
                        System.Diagnostics.Debug.WriteLine("SaveAdvertisementLanguageWaves: Transaction committed successfully");
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"SaveAdvertisementLanguageWaves Error: {ex.Message}");
                        transaction.Rollback();
                        throw;
                    }
                }
            }
        }

        // 6-parameter overload for backward compatibility (calls advanced overload with defaults)
        public void AddAdvertisement(string annType, string adverName, string hindiWave, string engWave, string adverTime, string adverCount)
        {
            AddAdvertisement(annType, adverName, hindiWave, engWave, adverTime, adverCount,
                "ALL", null, null, "Before", 1, 0, 0, 0, 0, 0, 0, "");
        }

        public void IncrementQuotaUsed(string annType, string adverName)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                using (var command = new SQLiteCommand(connection))
                {
                    command.CommandText = @"
                        UPDATE Advertising
                        SET QuotaUsed = QuotaUsed + 1
                        WHERE Ann_Type = @annType AND Adver_Name = @adverName";

                    command.Parameters.AddWithValue("@annType", annType);
                    command.Parameters.AddWithValue("@adverName", adverName);

                    int rowsAffected = command.ExecuteNonQuery();
                    System.Diagnostics.Debug.WriteLine($"IncrementQuotaUsed: Updated {rowsAffected} rows for {annType} - {adverName}");
                }
            }
        }

        public void UpdateAdvertisement(string annType, string adverName, string hindiWave, string engWave, string adverTime, string adverCount)
        {
            UpdateAdvertisement(annType, adverName, hindiWave, engWave, adverTime, adverCount,
                "ALL", null, null, "Before", 1, 0, 0, 0, 0, 0, 0, "");
        }

        public void UpdateTotalDuration(string annType, string adverName, double totalDuration, string formattedDuration)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                using (var command = new SQLiteCommand(connection))
                {
                    command.CommandText = @"
                        UPDATE Advertising
                        SET TotalDuration = @totalDuration,
                            TotalDurationFormatted = @formattedDuration
                        WHERE Adver_Name = @adverName AND Ann_Type = @annType";

                    command.Parameters.AddWithValue("@annType", annType);
                    command.Parameters.AddWithValue("@adverName", adverName);
                    command.Parameters.AddWithValue("@totalDuration", totalDuration);
                    command.Parameters.AddWithValue("@formattedDuration", formattedDuration);

                    command.ExecuteNonQuery();
                }
            }
        }

        public Dictionary<string, string> GetAdvertisementLanguageWaves(string annType, string adverName)
        {
            // Lookup AdvertisingId
            int advertisingId = GetAdvertisingId(annType, adverName);
            if (advertisingId > 0)
                return GetAdvertisementLanguageWaves(advertisingId);
            return new Dictionary<string, string>();
        }

        public void SaveAdvertisementLanguageWaves(string annType, string adverName, Dictionary<string, string> languageWaves)
        {
            int advertisingId = GetAdvertisingId(annType, adverName);
            if (advertisingId > 0)
                SaveAdvertisementLanguageWaves(advertisingId, annType, adverName, languageWaves);
        }

        private int GetAdvertisingId(string annType, string adverName)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                using (var command = new SQLiteCommand(connection))
                {
                    command.CommandText = "SELECT rowid FROM Advertising WHERE Ann_Type = @annType AND Adver_Name = @adverName";
                    command.Parameters.AddWithValue("@annType", annType);
                    command.Parameters.AddWithValue("@adverName", adverName);
                    var result = command.ExecuteScalar();
                    if (result != null && int.TryParse(result.ToString(), out int id))
                        return id;
                }
            }
            return -1;
        }
    }
}