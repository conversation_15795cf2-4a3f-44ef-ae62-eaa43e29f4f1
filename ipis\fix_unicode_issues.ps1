# PowerShell script to fix Unicode escape sequence issues in C# files

# Get all .cs files in the current directory and subdirectories
$csFiles = Get-ChildItem -Path "." -Filter "*.cs" -Recurse

Write-Host "Found $($csFiles.Count) C# files to process for Unicode issues..."

foreach ($file in $csFiles) {
    Write-Host "Processing: $($file.Name)"
    
    # Read the file content as a single string
    $content = Get-Content $file.FullName -Raw
    
    # Skip if file is empty
    if ([string]::IsNullOrWhiteSpace($content)) {
        continue
    }
    
    $modified = $false
    
    # Fix Unicode escape sequences that represent $ characters
    if ($content -match '\\u0024') {
        $content = $content -replace '\\u0024', '_DOLLAR_'
        $modified = $true
        Write-Host "  - Fixed Unicode escape sequences for dollar signs"
    }
    
    # Fix other common Unicode escape sequences
    if ($content -match '\\u002[0-9A-F]') {
        $content = $content -replace '\\u002([0-9A-F])', '_U002$1_'
        $modified = $true
        Write-Host "  - Fixed other Unicode escape sequences"
    }
    
    # Save the file if modified
    if ($modified) {
        Set-Content -Path $file.FullName -Value $content -NoNewline
        Write-Host "  - File updated"
    }
}

Write-Host "Unicode issues processing complete!"
