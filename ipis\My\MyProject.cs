// Decompiled with JetBrains decompiler
// Type: ipis.My.MyProject
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using Microsoft.VisualBasic;
using Microsoft.VisualBasic.ApplicationServices;
using Microsoft.VisualBasic.CompilerServices;
using System;
using System.CodeDom.Compiler;
using System.Collections;
using System.ComponentModel;
using System.ComponentModel.Design;
using System.Diagnostics;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System.Windows.Forms;

namespace ipis.My
{

[StandardModule]
[HideModuleName]
[GeneratedCode("MyTemplate", "********")]
internal sealed class MyProject
{
  private static readonly MyProject.ThreadSafeObjectProvider<MyComputer> m_ComputerObjectProvider = new MyProject.ThreadSafeObjectProvider<MyComputer>();
  private static readonly MyProject.ThreadSafeObjectProvider<MyApplication> m_AppObjectProvider = new MyProject.ThreadSafeObjectProvider<MyApplication>();
  private static readonly MyProject.ThreadSafeObjectProvider<User> m_UserObjectProvider = new MyProject.ThreadSafeObjectProvider<User>();
  private static MyProject.ThreadSafeObjectProvider<MyProject.MyForms> m_MyFormsObjectProvider = new MyProject.ThreadSafeObjectProvider<MyProject.MyForms>();
  private static readonly MyProject.ThreadSafeObjectProvider<MyProject.MyWebServices> m_MyWebServicesObjectProvider = new MyProject.ThreadSafeObjectProvider<MyProject.MyWebServices>();

  [DebuggerNonUserCode]
  static MyProject()
  {
  }

  [HelpKeyword("My.Computer")]
  internal static MyComputer Computer
  {
    [DebuggerHidden] get { return MyProject.m_ComputerObjectProvider.GetInstance; }
  }

  [HelpKeyword("My.Application")]
  internal static MyApplication Application
  {
    [DebuggerHidden] get { return MyProject.m_AppObjectProvider.GetInstance; }
  }

  [HelpKeyword("My.User")]
  internal static User User
  {
    [DebuggerHidden] get { return MyProject.m_UserObjectProvider.GetInstance; }
  }

  [HelpKeyword("My.Forms")]
  internal static MyProject.MyForms Forms
  {
    [DebuggerHidden] get { return MyProject.m_MyFormsObjectProvider.GetInstance; }
  }

  [HelpKeyword("My.WebServices")]
  internal static MyProject.MyWebServices WebServices
  {
    [DebuggerHidden] get { return MyProject.m_MyWebServicesObjectProvider.GetInstance; }
  }

  [MyGroupCollection("System.Windows.Forms.Form", "Create__Instance__", "Dispose__Instance__", "My.MyProject.Forms")]
  [EditorBrowsable(EditorBrowsableState.Never)]
  internal sealed class MyForms
  {
    public adminDashboard m_adminDashboard;
    public adminLogin m_adminLogin;
    public CCTV_NewForm m_CCTV_NewForm;
    public ConfigAdmin m_ConfigAdmin;
    public frm_CgsOnlineForm m_frm_CgsOnlineForm;
    public frmAbout m_frmAbout;
    public frmAddMsg m_frmAddMsg;
    public frmAddNewUser m_frmAddNewUser;
    public frmAddPfno m_frmAddPfno;
    public frmAddTrainNameVoice m_frmAddTrainNameVoice;
    public frmAddVoiceSplMsg m_frmAddVoiceSplMsg;
    public frmCCTV m_frmCCTV;
    public frmCfgInt m_frmCfgInt;
    public frmCgs m_frmCgs;
    public frmChangeAccount m_frmChangeAccount;
    public frmChangeAccountType m_frmChangeAccountType;
    public frmChangeAnotherUserPwd m_frmChangeAnotherUserPwd;
    public frmChangeName m_frmChangeName;
    public frmChangePassword m_frmChangePassword;
    public frmChangeUserDetails m_frmChangeUserDetails;
    public frmChangeUserPwd m_frmChangeUserPwd;
    public frmCom m_frmCom;
    public frmContacts m_frmContacts;
    public frmDeleteTrainNameVoice m_frmDeleteTrainNameVoice;
    public frmDeleteUser m_frmDeleteUser;
    public frmDisplayBoardSettings m_frmDisplayBoardSettings;
    public frmFont m_frmFont;
    public frmHelp m_frmHelp;
    public frmLanguage m_frmLanguage;
    public frmLogin m_frmLogin;
    public frmMainFormIPIS m_frmMainFormIPIS;
    public frmMdchCfgGetDisplay m_frmMdchCfgGetDisplay;
    public frmMsgBoxEx m_frmMsgBoxEx;
    public frmNetworkAGDB m_frmNetworkAGDB;
    public frmNetworkCGDB m_frmNetworkCGDB;
    public frmNetworkMDCH m_frmNetworkMDCH;
    public frmNetworkMLDB m_frmNetworkMLDB;
    public frmNetworkPDB m_frmNetworkPDB;
    public frmNetworkPDCH m_frmNetworkPDCH;
    public frmPassword m_frmPassword;
    public frmPdchCfgGetDisplay m_frmPdchCfgGetDisplay;
    public frmPlatformNo m_frmPlatformNo;
    public frmPortConfig m_frmPortConfig;
    public frmRecordPlay m_frmRecordPlay;
    public frmStationCode m_frmStationCode;
    public frmStationDetails m_frmStationDetails;
    public frmStationNameVoice m_frmStationNameVoice;
    public frmSuryaLogo m_frmSuryaLogo;
    public frmTrainConfig m_frmTrainConfig;
    public frmTrainDetails m_frmTrainDetails;
    public frmTrainStatusMsg m_frmTrainStatusMsg;
    public frmTrainstatusPopup m_frmTrainstatusPopup;
    public frmTrainTimings m_frmTrainTimings;
    public frmUser m_frmUser;
    public frmUserNamePassword m_frmUserNamePassword;
    public frmVoice m_frmVoice;
    public frmVoice_Special_Messages m_frmVoice_Special_Messages;
    [ThreadStatic]
    private static Hashtable m_FormBeingCreated;

    public adminDashboard adminDashboard
    {
      [DebuggerNonUserCode] get
      {
        this.m_adminDashboard = MyProject.MyForms.Create__Instance__<adminDashboard>(this.m_adminDashboard);
        return this.m_adminDashboard;
      }
      [DebuggerNonUserCode] set
      {
        if (value == this.m_adminDashboard)
          return;
        if (value != null)
          throw new ArgumentException("Property can only be set to Nothing");
        this.Dispose__Instance__<adminDashboard>(ref this.m_adminDashboard);
      }
    }

    public adminLogin adminLogin
    {
      [DebuggerNonUserCode] get
      {
        this.m_adminLogin = MyProject.MyForms.Create__Instance__<adminLogin>(this.m_adminLogin);
        return this.m_adminLogin;
      }
      [DebuggerNonUserCode] set
      {
        if (value == this.m_adminLogin)
          return;
        if (value != null)
          throw new ArgumentException("Property can only be set to Nothing");
        this.Dispose__Instance__<adminLogin>(ref this.m_adminLogin);
      }
    }

    public CCTV_NewForm CCTV_NewForm
    {
      [DebuggerNonUserCode] get
      {
        this.m_CCTV_NewForm = MyProject.MyForms.Create__Instance__<CCTV_NewForm>(this.m_CCTV_NewForm);
        return this.m_CCTV_NewForm;
      }
      [DebuggerNonUserCode] set
      {
        if (value == this.m_CCTV_NewForm)
          return;
        if (value != null)
          throw new ArgumentException("Property can only be set to Nothing");
        this.Dispose__Instance__<CCTV_NewForm>(ref this.m_CCTV_NewForm);
      }
    }

    public ConfigAdmin ConfigAdmin
    {
      [DebuggerNonUserCode] get
      {
        this.m_ConfigAdmin = MyProject.MyForms.Create__Instance__<ConfigAdmin>(this.m_ConfigAdmin);
        return this.m_ConfigAdmin;
      }
      [DebuggerNonUserCode] set
      {
        if (value == this.m_ConfigAdmin)
          return;
        if (value != null)
          throw new ArgumentException("Property can only be set to Nothing");
        this.Dispose__Instance__<ConfigAdmin>(ref this.m_ConfigAdmin);
      }
    }

    public frm_CgsOnlineForm frm_CgsOnlineForm
    {
      [DebuggerNonUserCode] get
      {
        this.m_frm_CgsOnlineForm = MyProject.MyForms.Create__Instance__<frm_CgsOnlineForm>(this.m_frm_CgsOnlineForm);
        return this.m_frm_CgsOnlineForm;
      }
      [DebuggerNonUserCode] set
      {
        if (value == this.m_frm_CgsOnlineForm)
          return;
        if (value != null)
          throw new ArgumentException("Property can only be set to Nothing");
        this.Dispose__Instance__<frm_CgsOnlineForm>(ref this.m_frm_CgsOnlineForm);
      }
    }

    public frmAbout frmAbout
    {
      [DebuggerNonUserCode] get
      {
        this.m_frmAbout = MyProject.MyForms.Create__Instance__<frmAbout>(this.m_frmAbout);
        return this.m_frmAbout;
      }
      [DebuggerNonUserCode] set
      {
        if (value == this.m_frmAbout)
          return;
        if (value != null)
          throw new ArgumentException("Property can only be set to Nothing");
        this.Dispose__Instance__<frmAbout>(ref this.m_frmAbout);
      }
    }

    public frmAddMsg frmAddMsg
    {
      [DebuggerNonUserCode] get
      {
        this.m_frmAddMsg = MyProject.MyForms.Create__Instance__<frmAddMsg>(this.m_frmAddMsg);
        return this.m_frmAddMsg;
      }
      [DebuggerNonUserCode] set
      {
        if (value == this.m_frmAddMsg)
          return;
        if (value != null)
          throw new ArgumentException("Property can only be set to Nothing");
        this.Dispose__Instance__<frmAddMsg>(ref this.m_frmAddMsg);
      }
    }

    public frmAddNewUser frmAddNewUser
    {
      [DebuggerNonUserCode] get
      {
        this.m_frmAddNewUser = MyProject.MyForms.Create__Instance__<frmAddNewUser>(this.m_frmAddNewUser);
        return this.m_frmAddNewUser;
      }
      [DebuggerNonUserCode] set
      {
        if (value == this.m_frmAddNewUser)
          return;
        if (value != null)
          throw new ArgumentException("Property can only be set to Nothing");
        this.Dispose__Instance__<frmAddNewUser>(ref this.m_frmAddNewUser);
      }
    }

    public frmAddPfno frmAddPfno
    {
      [DebuggerNonUserCode] get
      {
        this.m_frmAddPfno = MyProject.MyForms.Create__Instance__<frmAddPfno>(this.m_frmAddPfno);
        return this.m_frmAddPfno;
      }
      [DebuggerNonUserCode] set
      {
        if (value == this.m_frmAddPfno)
          return;
        if (value != null)
          throw new ArgumentException("Property can only be set to Nothing");
        this.Dispose__Instance__<frmAddPfno>(ref this.m_frmAddPfno);
      }
    }

    public frmAddTrainNameVoice frmAddTrainNameVoice
    {
      [DebuggerNonUserCode] get
      {
        this.m_frmAddTrainNameVoice = MyProject.MyForms.Create__Instance__<frmAddTrainNameVoice>(this.m_frmAddTrainNameVoice);
        return this.m_frmAddTrainNameVoice;
      }
      [DebuggerNonUserCode] set
      {
        if (value == this.m_frmAddTrainNameVoice)
          return;
        if (value != null)
          throw new ArgumentException("Property can only be set to Nothing");
        this.Dispose__Instance__<frmAddTrainNameVoice>(ref this.m_frmAddTrainNameVoice);
      }
    }

    public frmAddVoiceSplMsg frmAddVoiceSplMsg
    {
      [DebuggerNonUserCode] get
      {
        this.m_frmAddVoiceSplMsg = MyProject.MyForms.Create__Instance__<frmAddVoiceSplMsg>(this.m_frmAddVoiceSplMsg);
        return this.m_frmAddVoiceSplMsg;
      }
      [DebuggerNonUserCode] set
      {
        if (value == this.m_frmAddVoiceSplMsg)
          return;
        if (value != null)
          throw new ArgumentException("Property can only be set to Nothing");
        this.Dispose__Instance__<frmAddVoiceSplMsg>(ref this.m_frmAddVoiceSplMsg);
      }
    }

    public frmCCTV frmCCTV
    {
      [DebuggerNonUserCode] get
      {
        this.m_frmCCTV = MyProject.MyForms.Create__Instance__<frmCCTV>(this.m_frmCCTV);
        return this.m_frmCCTV;
      }
      [DebuggerNonUserCode] set
      {
        if (value == this.m_frmCCTV)
          return;
        if (value != null)
          throw new ArgumentException("Property can only be set to Nothing");
        this.Dispose__Instance__<frmCCTV>(ref this.m_frmCCTV);
      }
    }

    public frmCfgInt frmCfgInt
    {
      [DebuggerNonUserCode] get
      {
        this.m_frmCfgInt = MyProject.MyForms.Create__Instance__<frmCfgInt>(this.m_frmCfgInt);
        return this.m_frmCfgInt;
      }
      [DebuggerNonUserCode] set
      {
        if (value == this.m_frmCfgInt)
          return;
        if (value != null)
          throw new ArgumentException("Property can only be set to Nothing");
        this.Dispose__Instance__<frmCfgInt>(ref this.m_frmCfgInt);
      }
    }

    public frmCgs frmCgs
    {
      [DebuggerNonUserCode] get
      {
        this.m_frmCgs = MyProject.MyForms.Create__Instance__<frmCgs>(this.m_frmCgs);
        return this.m_frmCgs;
      }
      [DebuggerNonUserCode] set
      {
        if (value == this.m_frmCgs)
          return;
        if (value != null)
          throw new ArgumentException("Property can only be set to Nothing");
        this.Dispose__Instance__<frmCgs>(ref this.m_frmCgs);
      }
    }

    public frmChangeAccount frmChangeAccount
    {
      [DebuggerNonUserCode] get
      {
        this.m_frmChangeAccount = MyProject.MyForms.Create__Instance__<frmChangeAccount>(this.m_frmChangeAccount);
        return this.m_frmChangeAccount;
      }
      [DebuggerNonUserCode] set
      {
        if (value == this.m_frmChangeAccount)
          return;
        if (value != null)
          throw new ArgumentException("Property can only be set to Nothing");
        this.Dispose__Instance__<frmChangeAccount>(ref this.m_frmChangeAccount);
      }
    }

    public frmChangeAccountType frmChangeAccountType
    {
      [DebuggerNonUserCode] get
      {
        this.m_frmChangeAccountType = MyProject.MyForms.Create__Instance__<frmChangeAccountType>(this.m_frmChangeAccountType);
        return this.m_frmChangeAccountType;
      }
      [DebuggerNonUserCode] set
      {
        if (value == this.m_frmChangeAccountType)
          return;
        if (value != null)
          throw new ArgumentException("Property can only be set to Nothing");
        this.Dispose__Instance__<frmChangeAccountType>(ref this.m_frmChangeAccountType);
      }
    }

    public frmChangeAnotherUserPwd frmChangeAnotherUserPwd
    {
      [DebuggerNonUserCode] get
      {
        this.m_frmChangeAnotherUserPwd = MyProject.MyForms.Create__Instance__<frmChangeAnotherUserPwd>(this.m_frmChangeAnotherUserPwd);
        return this.m_frmChangeAnotherUserPwd;
      }
      [DebuggerNonUserCode] set
      {
        if (value == this.m_frmChangeAnotherUserPwd)
          return;
        if (value != null)
          throw new ArgumentException("Property can only be set to Nothing");
        this.Dispose__Instance__<frmChangeAnotherUserPwd>(ref this.m_frmChangeAnotherUserPwd);
      }
    }

    public frmChangeName frmChangeName
    {
      [DebuggerNonUserCode] get
      {
        this.m_frmChangeName = MyProject.MyForms.Create__Instance__<frmChangeName>(this.m_frmChangeName);
        return this.m_frmChangeName;
      }
      [DebuggerNonUserCode] set
      {
        if (value == this.m_frmChangeName)
          return;
        if (value != null)
          throw new ArgumentException("Property can only be set to Nothing");
        this.Dispose__Instance__<frmChangeName>(ref this.m_frmChangeName);
      }
    }

    public frmChangePassword frmChangePassword
    {
      [DebuggerNonUserCode] get
      {
        this.m_frmChangePassword = MyProject.MyForms.Create__Instance__<frmChangePassword>(this.m_frmChangePassword);
        return this.m_frmChangePassword;
      }
      [DebuggerNonUserCode] set
      {
        if (value == this.m_frmChangePassword)
          return;
        if (value != null)
          throw new ArgumentException("Property can only be set to Nothing");
        this.Dispose__Instance__<frmChangePassword>(ref this.m_frmChangePassword);
      }
    }

    public frmChangeUserDetails frmChangeUserDetails
    {
      [DebuggerNonUserCode] get
      {
        this.m_frmChangeUserDetails = MyProject.MyForms.Create__Instance__<frmChangeUserDetails>(this.m_frmChangeUserDetails);
        return this.m_frmChangeUserDetails;
      }
      [DebuggerNonUserCode] set
      {
        if (value == this.m_frmChangeUserDetails)
          return;
        if (value != null)
          throw new ArgumentException("Property can only be set to Nothing");
        this.Dispose__Instance__<frmChangeUserDetails>(ref this.m_frmChangeUserDetails);
      }
    }

    public frmChangeUserPwd frmChangeUserPwd
    {
      [DebuggerNonUserCode] get
      {
        this.m_frmChangeUserPwd = MyProject.MyForms.Create__Instance__<frmChangeUserPwd>(this.m_frmChangeUserPwd);
        return this.m_frmChangeUserPwd;
      }
      [DebuggerNonUserCode] set
      {
        if (value == this.m_frmChangeUserPwd)
          return;
        if (value != null)
          throw new ArgumentException("Property can only be set to Nothing");
        this.Dispose__Instance__<frmChangeUserPwd>(ref this.m_frmChangeUserPwd);
      }
    }

    public frmCom frmCom
    {
      [DebuggerNonUserCode] get
      {
        this.m_frmCom = MyProject.MyForms.Create__Instance__<frmCom>(this.m_frmCom);
        return this.m_frmCom;
      }
      [DebuggerNonUserCode] set
      {
        if (value == this.m_frmCom)
          return;
        if (value != null)
          throw new ArgumentException("Property can only be set to Nothing");
        this.Dispose__Instance__<frmCom>(ref this.m_frmCom);
      }
    }

    public frmContacts frmContacts
    {
      [DebuggerNonUserCode] get
      {
        this.m_frmContacts = MyProject.MyForms.Create__Instance__<frmContacts>(this.m_frmContacts);
        return this.m_frmContacts;
      }
      [DebuggerNonUserCode] set
      {
        if (value == this.m_frmContacts)
          return;
        if (value != null)
          throw new ArgumentException("Property can only be set to Nothing");
        this.Dispose__Instance__<frmContacts>(ref this.m_frmContacts);
      }
    }

    public frmDeleteTrainNameVoice frmDeleteTrainNameVoice
    {
      [DebuggerNonUserCode] get
      {
        this.m_frmDeleteTrainNameVoice = MyProject.MyForms.Create__Instance__<frmDeleteTrainNameVoice>(this.m_frmDeleteTrainNameVoice);
        return this.m_frmDeleteTrainNameVoice;
      }
      [DebuggerNonUserCode] set
      {
        if (value == this.m_frmDeleteTrainNameVoice)
          return;
        if (value != null)
          throw new ArgumentException("Property can only be set to Nothing");
        this.Dispose__Instance__<frmDeleteTrainNameVoice>(ref this.m_frmDeleteTrainNameVoice);
      }
    }

    public frmDeleteUser frmDeleteUser
    {
      [DebuggerNonUserCode] get
      {
        this.m_frmDeleteUser = MyProject.MyForms.Create__Instance__<frmDeleteUser>(this.m_frmDeleteUser);
        return this.m_frmDeleteUser;
      }
      [DebuggerNonUserCode] set
      {
        if (value == this.m_frmDeleteUser)
          return;
        if (value != null)
          throw new ArgumentException("Property can only be set to Nothing");
        this.Dispose__Instance__<frmDeleteUser>(ref this.m_frmDeleteUser);
      }
    }

    public frmDisplayBoardSettings frmDisplayBoardSettings
    {
      [DebuggerNonUserCode] get
      {
        this.m_frmDisplayBoardSettings = MyProject.MyForms.Create__Instance__<frmDisplayBoardSettings>(this.m_frmDisplayBoardSettings);
        return this.m_frmDisplayBoardSettings;
      }
      [DebuggerNonUserCode] set
      {
        if (value == this.m_frmDisplayBoardSettings)
          return;
        if (value != null)
          throw new ArgumentException("Property can only be set to Nothing");
        this.Dispose__Instance__<frmDisplayBoardSettings>(ref this.m_frmDisplayBoardSettings);
      }
    }

    public frmFont frmFont
    {
      [DebuggerNonUserCode] get
      {
        this.m_frmFont = MyProject.MyForms.Create__Instance__<frmFont>(this.m_frmFont);
        return this.m_frmFont;
      }
      [DebuggerNonUserCode] set
      {
        if (value == this.m_frmFont)
          return;
        if (value != null)
          throw new ArgumentException("Property can only be set to Nothing");
        this.Dispose__Instance__<frmFont>(ref this.m_frmFont);
      }
    }

    public frmHelp frmHelp
    {
      [DebuggerNonUserCode] get
      {
        this.m_frmHelp = MyProject.MyForms.Create__Instance__<frmHelp>(this.m_frmHelp);
        return this.m_frmHelp;
      }
      [DebuggerNonUserCode] set
      {
        if (value == this.m_frmHelp)
          return;
        if (value != null)
          throw new ArgumentException("Property can only be set to Nothing");
        this.Dispose__Instance__<frmHelp>(ref this.m_frmHelp);
      }
    }

    public frmLanguage frmLanguage
    {
      [DebuggerNonUserCode] get
      {
        this.m_frmLanguage = MyProject.MyForms.Create__Instance__<frmLanguage>(this.m_frmLanguage);
        return this.m_frmLanguage;
      }
      [DebuggerNonUserCode] set
      {
        if (value == this.m_frmLanguage)
          return;
        if (value != null)
          throw new ArgumentException("Property can only be set to Nothing");
        this.Dispose__Instance__<frmLanguage>(ref this.m_frmLanguage);
      }
    }

    public frmLogin frmLogin
    {
      [DebuggerNonUserCode] get
      {
        this.m_frmLogin = MyProject.MyForms.Create__Instance__<frmLogin>(this.m_frmLogin);
        return this.m_frmLogin;
      }
      [DebuggerNonUserCode] set
      {
        if (value == this.m_frmLogin)
          return;
        if (value != null)
          throw new ArgumentException("Property can only be set to Nothing");
        this.Dispose__Instance__<frmLogin>(ref this.m_frmLogin);
      }
    }

    public frmMainFormIPIS frmMainFormIPIS
    {
      [DebuggerNonUserCode] get
      {
        this.m_frmMainFormIPIS = MyProject.MyForms.Create__Instance__<frmMainFormIPIS>(this.m_frmMainFormIPIS);
        return this.m_frmMainFormIPIS;
      }
      [DebuggerNonUserCode] set
      {
        if (value == this.m_frmMainFormIPIS)
          return;
        if (value != null)
          throw new ArgumentException("Property can only be set to Nothing");
        this.Dispose__Instance__<frmMainFormIPIS>(ref this.m_frmMainFormIPIS);
      }
    }

    public frmMdchCfgGetDisplay frmMdchCfgGetDisplay
    {
      [DebuggerNonUserCode] get
      {
        this.m_frmMdchCfgGetDisplay = MyProject.MyForms.Create__Instance__<frmMdchCfgGetDisplay>(this.m_frmMdchCfgGetDisplay);
        return this.m_frmMdchCfgGetDisplay;
      }
      [DebuggerNonUserCode] set
      {
        if (value == this.m_frmMdchCfgGetDisplay)
          return;
        if (value != null)
          throw new ArgumentException("Property can only be set to Nothing");
        this.Dispose__Instance__<frmMdchCfgGetDisplay>(ref this.m_frmMdchCfgGetDisplay);
      }
    }

    public frmMsgBoxEx frmMsgBoxEx
    {
      [DebuggerNonUserCode] get
      {
        this.m_frmMsgBoxEx = MyProject.MyForms.Create__Instance__<frmMsgBoxEx>(this.m_frmMsgBoxEx);
        return this.m_frmMsgBoxEx;
      }
      [DebuggerNonUserCode] set
      {
        if (value == this.m_frmMsgBoxEx)
          return;
        if (value != null)
          throw new ArgumentException("Property can only be set to Nothing");
        this.Dispose__Instance__<frmMsgBoxEx>(ref this.m_frmMsgBoxEx);
      }
    }

    public frmNetworkAGDB frmNetworkAGDB
    {
      [DebuggerNonUserCode] get
      {
        this.m_frmNetworkAGDB = MyProject.MyForms.Create__Instance__<frmNetworkAGDB>(this.m_frmNetworkAGDB);
        return this.m_frmNetworkAGDB;
      }
      [DebuggerNonUserCode] set
      {
        if (value == this.m_frmNetworkAGDB)
          return;
        if (value != null)
          throw new ArgumentException("Property can only be set to Nothing");
        this.Dispose__Instance__<frmNetworkAGDB>(ref this.m_frmNetworkAGDB);
      }
    }

    public frmNetworkCGDB frmNetworkCGDB
    {
      [DebuggerNonUserCode] get
      {
        this.m_frmNetworkCGDB = MyProject.MyForms.Create__Instance__<frmNetworkCGDB>(this.m_frmNetworkCGDB);
        return this.m_frmNetworkCGDB;
      }
      [DebuggerNonUserCode] set
      {
        if (value == this.m_frmNetworkCGDB)
          return;
        if (value != null)
          throw new ArgumentException("Property can only be set to Nothing");
        this.Dispose__Instance__<frmNetworkCGDB>(ref this.m_frmNetworkCGDB);
      }
    }

    public frmNetworkMDCH frmNetworkMDCH
    {
      [DebuggerNonUserCode] get
      {
        this.m_frmNetworkMDCH = MyProject.MyForms.Create__Instance__<frmNetworkMDCH>(this.m_frmNetworkMDCH);
        return this.m_frmNetworkMDCH;
      }
      [DebuggerNonUserCode] set
      {
        if (value == this.m_frmNetworkMDCH)
          return;
        if (value != null)
          throw new ArgumentException("Property can only be set to Nothing");
        this.Dispose__Instance__<frmNetworkMDCH>(ref this.m_frmNetworkMDCH);
      }
    }

    public frmNetworkMLDB frmNetworkMLDB
    {
      [DebuggerNonUserCode] get
      {
        this.m_frmNetworkMLDB = MyProject.MyForms.Create__Instance__<frmNetworkMLDB>(this.m_frmNetworkMLDB);
        return this.m_frmNetworkMLDB;
      }
      [DebuggerNonUserCode] set
      {
        if (value == this.m_frmNetworkMLDB)
          return;
        if (value != null)
          throw new ArgumentException("Property can only be set to Nothing");
        this.Dispose__Instance__<frmNetworkMLDB>(ref this.m_frmNetworkMLDB);
      }
    }

    public frmNetworkPDB frmNetworkPDB
    {
      [DebuggerNonUserCode] get
      {
        this.m_frmNetworkPDB = MyProject.MyForms.Create__Instance__<frmNetworkPDB>(this.m_frmNetworkPDB);
        return this.m_frmNetworkPDB;
      }
      [DebuggerNonUserCode] set
      {
        if (value == this.m_frmNetworkPDB)
          return;
        if (value != null)
          throw new ArgumentException("Property can only be set to Nothing");
        this.Dispose__Instance__<frmNetworkPDB>(ref this.m_frmNetworkPDB);
      }
    }

    public frmNetworkPDCH frmNetworkPDCH
    {
      [DebuggerNonUserCode] get
      {
        this.m_frmNetworkPDCH = MyProject.MyForms.Create__Instance__<frmNetworkPDCH>(this.m_frmNetworkPDCH);
        return this.m_frmNetworkPDCH;
      }
      [DebuggerNonUserCode] set
      {
        if (value == this.m_frmNetworkPDCH)
          return;
        if (value != null)
          throw new ArgumentException("Property can only be set to Nothing");
        this.Dispose__Instance__<frmNetworkPDCH>(ref this.m_frmNetworkPDCH);
      }
    }

    public frmPassword frmPassword
    {
      [DebuggerNonUserCode] get
      {
        this.m_frmPassword = MyProject.MyForms.Create__Instance__<frmPassword>(this.m_frmPassword);
        return this.m_frmPassword;
      }
      [DebuggerNonUserCode] set
      {
        if (value == this.m_frmPassword)
          return;
        if (value != null)
          throw new ArgumentException("Property can only be set to Nothing");
        this.Dispose__Instance__<frmPassword>(ref this.m_frmPassword);
      }
    }

    public frmPdchCfgGetDisplay frmPdchCfgGetDisplay
    {
      [DebuggerNonUserCode] get
      {
        this.m_frmPdchCfgGetDisplay = MyProject.MyForms.Create__Instance__<frmPdchCfgGetDisplay>(this.m_frmPdchCfgGetDisplay);
        return this.m_frmPdchCfgGetDisplay;
      }
      [DebuggerNonUserCode] set
      {
        if (value == this.m_frmPdchCfgGetDisplay)
          return;
        if (value != null)
          throw new ArgumentException("Property can only be set to Nothing");
        this.Dispose__Instance__<frmPdchCfgGetDisplay>(ref this.m_frmPdchCfgGetDisplay);
      }
    }

    public frmPlatformNo frmPlatformNo
    {
      [DebuggerNonUserCode] get
      {
        this.m_frmPlatformNo = MyProject.MyForms.Create__Instance__<frmPlatformNo>(this.m_frmPlatformNo);
        return this.m_frmPlatformNo;
      }
      [DebuggerNonUserCode] set
      {
        if (value == this.m_frmPlatformNo)
          return;
        if (value != null)
          throw new ArgumentException("Property can only be set to Nothing");
        this.Dispose__Instance__<frmPlatformNo>(ref this.m_frmPlatformNo);
      }
    }

    public frmPortConfig frmPortConfig
    {
      [DebuggerNonUserCode] get
      {
        this.m_frmPortConfig = MyProject.MyForms.Create__Instance__<frmPortConfig>(this.m_frmPortConfig);
        return this.m_frmPortConfig;
      }
      [DebuggerNonUserCode] set
      {
        if (value == this.m_frmPortConfig)
          return;
        if (value != null)
          throw new ArgumentException("Property can only be set to Nothing");
        this.Dispose__Instance__<frmPortConfig>(ref this.m_frmPortConfig);
      }
    }

    public frmRecordPlay frmRecordPlay
    {
      [DebuggerNonUserCode] get
      {
        this.m_frmRecordPlay = MyProject.MyForms.Create__Instance__<frmRecordPlay>(this.m_frmRecordPlay);
        return this.m_frmRecordPlay;
      }
      [DebuggerNonUserCode] set
      {
        if (value == this.m_frmRecordPlay)
          return;
        if (value != null)
          throw new ArgumentException("Property can only be set to Nothing");
        this.Dispose__Instance__<frmRecordPlay>(ref this.m_frmRecordPlay);
      }
    }

    public frmStationCode frmStationCode
    {
      [DebuggerNonUserCode] get
      {
        this.m_frmStationCode = MyProject.MyForms.Create__Instance__<frmStationCode>(this.m_frmStationCode);
        return this.m_frmStationCode;
      }
      [DebuggerNonUserCode] set
      {
        if (value == this.m_frmStationCode)
          return;
        if (value != null)
          throw new ArgumentException("Property can only be set to Nothing");
        this.Dispose__Instance__<frmStationCode>(ref this.m_frmStationCode);
      }
    }

    public frmStationDetails frmStationDetails
    {
      [DebuggerNonUserCode] get
      {
        this.m_frmStationDetails = MyProject.MyForms.Create__Instance__<frmStationDetails>(this.m_frmStationDetails);
        return this.m_frmStationDetails;
      }
      [DebuggerNonUserCode] set
      {
        if (value == this.m_frmStationDetails)
          return;
        if (value != null)
          throw new ArgumentException("Property can only be set to Nothing");
        this.Dispose__Instance__<frmStationDetails>(ref this.m_frmStationDetails);
      }
    }

    public frmStationNameVoice frmStationNameVoice
    {
      [DebuggerNonUserCode] get
      {
        this.m_frmStationNameVoice = MyProject.MyForms.Create__Instance__<frmStationNameVoice>(this.m_frmStationNameVoice);
        return this.m_frmStationNameVoice;
      }
      [DebuggerNonUserCode] set
      {
        if (value == this.m_frmStationNameVoice)
          return;
        if (value != null)
          throw new ArgumentException("Property can only be set to Nothing");
        this.Dispose__Instance__<frmStationNameVoice>(ref this.m_frmStationNameVoice);
      }
    }

    public frmSuryaLogo frmSuryaLogo
    {
      [DebuggerNonUserCode] get
      {
        this.m_frmSuryaLogo = MyProject.MyForms.Create__Instance__<frmSuryaLogo>(this.m_frmSuryaLogo);
        return this.m_frmSuryaLogo;
      }
      [DebuggerNonUserCode] set
      {
        if (value == this.m_frmSuryaLogo)
          return;
        if (value != null)
          throw new ArgumentException("Property can only be set to Nothing");
        this.Dispose__Instance__<frmSuryaLogo>(ref this.m_frmSuryaLogo);
      }
    }

    public frmTrainConfig frmTrainConfig
    {
      [DebuggerNonUserCode] get
      {
        this.m_frmTrainConfig = MyProject.MyForms.Create__Instance__<frmTrainConfig>(this.m_frmTrainConfig);
        return this.m_frmTrainConfig;
      }
      [DebuggerNonUserCode] set
      {
        if (value == this.m_frmTrainConfig)
          return;
        if (value != null)
          throw new ArgumentException("Property can only be set to Nothing");
        this.Dispose__Instance__<frmTrainConfig>(ref this.m_frmTrainConfig);
      }
    }

    public frmTrainDetails frmTrainDetails
    {
      [DebuggerNonUserCode] get
      {
        this.m_frmTrainDetails = MyProject.MyForms.Create__Instance__<frmTrainDetails>(this.m_frmTrainDetails);
        return this.m_frmTrainDetails;
      }
      [DebuggerNonUserCode] set
      {
        if (value == this.m_frmTrainDetails)
          return;
        if (value != null)
          throw new ArgumentException("Property can only be set to Nothing");
        this.Dispose__Instance__<frmTrainDetails>(ref this.m_frmTrainDetails);
      }
    }

    public frmTrainStatusMsg frmTrainStatusMsg
    {
      [DebuggerNonUserCode] get
      {
        this.m_frmTrainStatusMsg = MyProject.MyForms.Create__Instance__<frmTrainStatusMsg>(this.m_frmTrainStatusMsg);
        return this.m_frmTrainStatusMsg;
      }
      [DebuggerNonUserCode] set
      {
        if (value == this.m_frmTrainStatusMsg)
          return;
        if (value != null)
          throw new ArgumentException("Property can only be set to Nothing");
        this.Dispose__Instance__<frmTrainStatusMsg>(ref this.m_frmTrainStatusMsg);
      }
    }

    public frmTrainstatusPopup frmTrainstatusPopup
    {
      [DebuggerNonUserCode] get
      {
        this.m_frmTrainstatusPopup = MyProject.MyForms.Create__Instance__<frmTrainstatusPopup>(this.m_frmTrainstatusPopup);
        return this.m_frmTrainstatusPopup;
      }
      [DebuggerNonUserCode] set
      {
        if (value == this.m_frmTrainstatusPopup)
          return;
        if (value != null)
          throw new ArgumentException("Property can only be set to Nothing");
        this.Dispose__Instance__<frmTrainstatusPopup>(ref this.m_frmTrainstatusPopup);
      }
    }

    public frmTrainTimings frmTrainTimings
    {
      [DebuggerNonUserCode] get
      {
        this.m_frmTrainTimings = MyProject.MyForms.Create__Instance__<frmTrainTimings>(this.m_frmTrainTimings);
        return this.m_frmTrainTimings;
      }
      [DebuggerNonUserCode] set
      {
        if (value == this.m_frmTrainTimings)
          return;
        if (value != null)
          throw new ArgumentException("Property can only be set to Nothing");
        this.Dispose__Instance__<frmTrainTimings>(ref this.m_frmTrainTimings);
      }
    }

    public frmUser frmUser
    {
      [DebuggerNonUserCode] get
      {
        this.m_frmUser = MyProject.MyForms.Create__Instance__<frmUser>(this.m_frmUser);
        return this.m_frmUser;
      }
      [DebuggerNonUserCode] set
      {
        if (value == this.m_frmUser)
          return;
        if (value != null)
          throw new ArgumentException("Property can only be set to Nothing");
        this.Dispose__Instance__<frmUser>(ref this.m_frmUser);
      }
    }

    public frmUserNamePassword frmUserNamePassword
    {
      [DebuggerNonUserCode] get
      {
        this.m_frmUserNamePassword = MyProject.MyForms.Create__Instance__<frmUserNamePassword>(this.m_frmUserNamePassword);
        return this.m_frmUserNamePassword;
      }
      [DebuggerNonUserCode] set
      {
        if (value == this.m_frmUserNamePassword)
          return;
        if (value != null)
          throw new ArgumentException("Property can only be set to Nothing");
        this.Dispose__Instance__<frmUserNamePassword>(ref this.m_frmUserNamePassword);
      }
    }

    public frmVoice frmVoice
    {
      [DebuggerNonUserCode] get
      {
        this.m_frmVoice = MyProject.MyForms.Create__Instance__<frmVoice>(this.m_frmVoice);
        return this.m_frmVoice;
      }
      [DebuggerNonUserCode] set
      {
        if (value == this.m_frmVoice)
          return;
        if (value != null)
          throw new ArgumentException("Property can only be set to Nothing");
        this.Dispose__Instance__<frmVoice>(ref this.m_frmVoice);
      }
    }

    public frmVoice_Special_Messages frmVoice_Special_Messages
    {
      [DebuggerNonUserCode] get
      {
        this.m_frmVoice_Special_Messages = MyProject.MyForms.Create__Instance__<frmVoice_Special_Messages>(this.m_frmVoice_Special_Messages);
        return this.m_frmVoice_Special_Messages;
      }
      [DebuggerNonUserCode] set
      {
        if (value == this.m_frmVoice_Special_Messages)
          return;
        if (value != null)
          throw new ArgumentException("Property can only be set to Nothing");
        this.Dispose__Instance__<frmVoice_Special_Messages>(ref this.m_frmVoice_Special_Messages);
      }
    }

    [DebuggerHidden]
    private static T Create__Instance__<T>(T Instance) where T : Form, new()
    {
      if ((object) Instance != null && !Instance.IsDisposed)
        return Instance;
      if (MyProject.MyForms.m_FormBeingCreated != null)
      {
        if (MyProject.MyForms.m_FormBeingCreated.ContainsKey((object) typeof (T)))
          throw new InvalidOperationException(Utils.GetResourceString("WinForms_RecursiveFormCreate"));
      }
      else
        MyProject.MyForms.m_FormBeingCreated = new Hashtable();
      MyProject.MyForms.m_FormBeingCreated.Add((object) typeof (T), (object) null);
      try
      {
        return new T();
      }
      catch (TargetInvocationException ex)
      {
        // ISSUE: unable to correctly present filter
        ProjectData.SetProjectError((Exception) ex);
        if (ex.InnerException != null)
        {
          // SuccessfulFiltering;
        }
        else
          throw;
        throw new InvalidOperationException(Utils.GetResourceString("WinForms_SeeInnerException", ex.InnerException.Message), ex.InnerException);
      }
      finally
      {
        MyProject.MyForms.m_FormBeingCreated.Remove((object) typeof (T));
      }
    }

    [DebuggerHidden]
    private void Dispose__Instance__<T>(ref T instance) where T : Form
    {
      instance.Dispose();
      instance = default (T);
    }

    [EditorBrowsable(EditorBrowsableState.Never)]
    [DebuggerHidden]
    public MyForms()
    {
    }

    [EditorBrowsable(EditorBrowsableState.Never)]
    public override bool Equals(object o)
    {
      return base.Equals(RuntimeHelpers.GetObjectValue(o));
    }

    [EditorBrowsable(EditorBrowsableState.Never)]
    public override int GetHashCode()
    {
      return base.GetHashCode();
    }

    [EditorBrowsable(EditorBrowsableState.Never)]
    internal new System.Type GetType()
    {
      return typeof (MyProject.MyForms);
    }

    [EditorBrowsable(EditorBrowsableState.Never)]
    public override string ToString()
    {
      return base.ToString();
    }
  }

  [EditorBrowsable(EditorBrowsableState.Never)]
  [MyGroupCollection("System.Web.Services.Protocols.SoapHttpClientProtocol", "Create__Instance__", "Dispose__Instance__", "")]
  internal sealed class MyWebServices
  {
    [EditorBrowsable(EditorBrowsableState.Never)]
    [DebuggerHidden]
    public override bool Equals(object o)
    {
      return base.Equals(RuntimeHelpers.GetObjectValue(o));
    }

    [DebuggerHidden]
    [EditorBrowsable(EditorBrowsableState.Never)]
    public override int GetHashCode()
    {
      return base.GetHashCode();
    }

    [DebuggerHidden]
    [EditorBrowsable(EditorBrowsableState.Never)]
    internal new System.Type GetType()
    {
      return typeof (MyProject.MyWebServices);
    }

    [DebuggerHidden]
    [EditorBrowsable(EditorBrowsableState.Never)]
    public override string ToString()
    {
      return base.ToString();
    }

    [DebuggerHidden]
    private static T Create__Instance__<T>(T instance) where T : new()
    {
      return (object) instance == null ? new T() : instance;
    }

    [DebuggerHidden]
    private void Dispose__Instance__<T>(ref T instance)
    {
      instance = default (T);
    }

    [EditorBrowsable(EditorBrowsableState.Never)]
    [DebuggerHidden]
    public MyWebServices()
    {
    }
  }

  [ComVisible(false)]
  [EditorBrowsable(EditorBrowsableState.Never)]
  internal sealed class ThreadSafeObjectProvider<T> where T : new()
  {
    [ThreadStatic]
    private static T m_ThreadStaticValue;
    internal T GetInstance
    {
      [DebuggerHidden] get
      {
        if ((object) ThreadSafeObjectProvider<T>.m_ThreadStaticValue == null)
          ThreadSafeObjectProvider<T>.m_ThreadStaticValue = new T();
        return ThreadSafeObjectProvider<T>.m_ThreadStaticValue;
      }
    }

    [DebuggerHidden]
    [EditorBrowsable(EditorBrowsableState.Never)]
    public ThreadSafeObjectProvider()
    {
    }
  }
}
}
