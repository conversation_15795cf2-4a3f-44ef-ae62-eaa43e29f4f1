using System;
using System.Collections.Generic; // Required for List in BoardStatus
using System.Threading.Tasks;
using ipis_V2_jules.DisplayFormatters;
using ipis_V2_jules.Hardware.Protocols;

namespace ipis_V2_jules.Hardware.Clients
{
    public class PdbClient : IBoardClient
    {
        private readonly ICommunicationService _communicationService;
        private readonly IDisplayDataFormatter _dataFormatter; // Likely AgdbDataFormatter or a specific PDB formatter

        public PdbClient(ICommunicationService communicationService, IDisplayDataFormatter dataFormatter)
        {
            _communicationService = communicationService ?? throw new ArgumentNullException(nameof(communicationService));
            _dataFormatter = dataFormatter ?? throw new ArgumentNullException(nameof(dataFormatter));
        }

        public async Task<bool> SendMessageAsync(FormattedDisplayData data, byte boardAddress, byte subAddress, byte serialNo)
        {
            Console.WriteLine($"PDB Client: Sending message to address {boardAddress}, sub-address {subAddress}, serial {serialNo}.");
            // PDBs (Platform Display Boards) usually display train info on one or two lines.
            // The formatter should prepare data.Line1 and potentially data.Line2.
             if (data.Line1 == null && data.Line2 == null) {
                Console.WriteLine("PDB Client: No data in Line1 or Line2 to send.");
                return false;
            }

            // Assuming PDBs might use a combined payload if two lines are relevant or formatter handles it.
            // For now, prioritize Line1.
            byte[] payload = data.Line1 ?? data.Line2 ?? Array.Empty<byte>();
            if (data.Line1 != null && data.Line2 != null)
            {
                // This is a simplification. Real PDBs might have specific commands for multi-line display.
                // The formatter might also combine lines into a single payload with line break codes.
                payload = data.Line1; // Or combine: data.Line1.Concat(data.Line2).ToArray();
            }

            byte functionCode = 0x01; // Example function code for "display text"
            byte[] packet = DisplayPacketBuilder.BuildMessagePacket(boardAddress, subAddress, serialNo, functionCode, payload, data.AdditionalHeaderBytes);

            bool success = _communicationService.SendData(packet);
            return await Task.FromResult(success);
        }

        public async Task<bool> ClearDisplayAsync(byte boardAddress, byte subAddress, byte serialNo)
        {
            Console.WriteLine($"PDB Client: Clearing display for address {boardAddress}, sub-address {subAddress}, serial {serialNo}.");
            byte[] packet = DisplayPacketBuilder.BuildClearDisplayPacket(boardAddress, subAddress, serialNo);
            bool success = _communicationService.SendData(packet);
            return await Task.FromResult(success);
        }

        public async Task<BoardStatus> CheckLinkAsync(byte boardAddress, byte subAddress, byte serialNo)
        {
            Console.WriteLine($"PDB Client: Checking link for address {boardAddress}, sub-address {subAddress}, serial {serialNo}.");
            byte[] packet = DisplayPacketBuilder.BuildLinkCheckPacket(boardAddress, subAddress, serialNo);
            _communicationService.SendData(packet);

            // Placeholder: Actual link check would involve reading a response.
            await Task.Delay(50); // Simulate async work
            return new BoardStatus { IsLinkOk = true, StatusMessage = "Link check placeholder: OK (No actual response parsing)" };
        }

        public async Task<bool> SetConfigurationAsync(byte boardAddress, byte subAddress, byte serialNo, byte[] configData)
        {
            Console.WriteLine($"PDB Client: Setting configuration for address {boardAddress}, sub-address {subAddress}, serial {serialNo}.");
            byte[] packet = DisplayPacketBuilder.BuildSetConfigPacket(boardAddress, subAddress, serialNo, configData);
            bool success = _communicationService.SendData(packet);
            return await Task.FromResult(success);
        }

        public async Task<byte[]> GetConfigurationAsync(byte boardAddress, byte subAddress, byte serialNo)
        {
            Console.WriteLine($"PDB Client: Getting configuration for address {boardAddress}, sub-address {subAddress}, serial {serialNo}.");
            byte[] packet = DisplayPacketBuilder.BuildGetConfigPacket(boardAddress, subAddress, serialNo);
            _communicationService.SendData(packet);
            await Task.Delay(50);
            return await Task.FromResult(Array.Empty<byte>());
        }

        public async Task<bool> ResetBoardAsync(byte boardAddress, byte subAddress, byte serialNo)
        {
            Console.WriteLine($"PDB Client: Resetting board address {boardAddress}, sub-address {subAddress}, serial {serialNo}.");
            byte[] packet = DisplayPacketBuilder.BuildResetPacket(boardAddress, subAddress, serialNo);
            bool success = _communicationService.SendData(packet);
            return await Task.FromResult(success);
        }
    }
}
