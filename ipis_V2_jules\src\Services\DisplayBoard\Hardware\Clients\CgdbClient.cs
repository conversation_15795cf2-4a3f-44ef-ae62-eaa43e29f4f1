using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ipis_V2_jules.DisplayFormatters; // For CgdbDataFormatter, FormattedDisplayData
using ipis_V2_jules.Services.DisplayBoard.Hardware.Protocols; // For DisplayPacketBuilder
using ipis_V2_jules.Services.DisplayBoard.Hardware.Communication; // For ICommunicationService
using ipis_V2_jules.Models; // For DisplayBoardConfig
using ipis_V2_jules.ApiClients; // For TrainDataErail

// Assuming CgdbDataFormatter is in ipis_V2_jules.Services.DisplayBoard.DisplayFormatters

namespace ipis_V2_jules.Services.DisplayBoard.Hardware.Clients
{
    public class CgdbClient : IBoardClient
    {
        private readonly ICommunicationService _communicationService;
        private readonly CgdbDataFormatter _dataFormatter;
        private readonly DisplayBoardConfig _boardConfig;

        public BoardStatus Status { get; private set; }

        public CgdbClient(ICommunicationService communicationService,
                          CgdbDataFormatter dataFormatter,
                          DisplayBoardConfig boardConfig)
        {
            _communicationService = communicationService ?? throw new ArgumentNullException(nameof(communicationService));
            _dataFormatter = dataFormatter ?? throw new ArgumentNullException(nameof(dataFormatter));
            _boardConfig = boardConfig ?? throw new ArgumentNullException(nameof(boardConfig));
            Status = new BoardStatus { IsLinkOk = false, StatusMessage = "Initialized", FirmwareVersion = "N/A" };
        }

        private Dictionary<string, string> GetBoardConfigAsDictionary()
        {
            return _boardConfig.ToDictionary();
        }

        public async Task<bool> SendMessageAsync(FormattedDisplayData data, byte boardAddress, byte subAddress, byte serialNo)
        {
            if (boardAddress != _boardConfig.BoardId)
            {
                Console.WriteLine($"CGDB Client ({_boardConfig.BoardName}) Error: Mismatched boardAddress ({boardAddress}). Expected {_boardConfig.BoardId}.");
                Status = new BoardStatus { IsLinkOk = false, StatusMessage = "Config error: Board ID mismatch.", FirmwareVersion = Status.FirmwareVersion };
                return false;
            }

            if (data.Line1 == null)
            {
                Console.WriteLine($"CGDB Client ({_boardConfig.BoardName}): No data in Line1 for coach display.");
                Status = new BoardStatus { IsLinkOk = false, StatusMessage = "No data to send for coach display.", FirmwareVersion = Status.FirmwareVersion };
                return false;
            }

            Console.WriteLine($"CGDB Client ({_boardConfig.BoardName}): Sending coach display data.");
            // Using data.AdditionalHeaderBytes if needed by a more complex CGDB protocol,
            // otherwise, the DisplayPacketBuilder for CGDB might have a fixed command.
            // The current BuildCgdbDisplayPacket doesn't take additionalHeaderBytes but could be modified.
            byte[] packet = DisplayPacketBuilder.BuildCgdbDisplayPacket(
                _boardConfig.BoardId,
                data.Line1 // This is the AsciiCoachData
            );

            try
            {
                await _communicationService.WriteDataAsync(packet);
                Status = new BoardStatus { IsLinkOk = true, StatusMessage = "Coach data sent successfully.", FirmwareVersion = Status.FirmwareVersion };
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"CGDB Client ({_boardConfig.BoardName}) Error: Failed to send coach data. Exception: {ex.Message}");
                Status = new BoardStatus { IsLinkOk = false, StatusMessage = $"Send coach data failed: {ex.Message}", FirmwareVersion = Status.FirmwareVersion };
                return false;
            }
        }

        public async Task<bool> SendMessageAsync(string message)
        {
            if (string.IsNullOrEmpty(message)) return false;
            Console.WriteLine($"CGDB Client ({_boardConfig.BoardName}): Formatting and sending message: \"{message.Substring(0, Math.Min(message.Length, 20))}...\"");
            FormattedDisplayData formattedData = _dataFormatter.FormatMessage(message, GetBoardConfigAsDictionary());
            return await SendMessageAsync(formattedData, _boardConfig.BoardId, 0, 0);
        }

        public async Task<bool> UpdateTrainDisplayAsync(TrainDataErail trainData, Dictionary<string, string> platformInfo)
        {
            if (trainData == null) return false;
            Console.WriteLine($"CGDB Client ({_boardConfig.BoardName}): Formatting and sending train coach data for {trainData.TrainNo}");
            FormattedDisplayData formattedData = _dataFormatter.FormatTrainData(trainData, platformInfo, GetBoardConfigAsDictionary());
            return await SendMessageAsync(formattedData, _boardConfig.BoardId, 0, 0);
        }

        public async Task<bool> ClearDisplayAsync() => await ClearDisplayAsync(_boardConfig.BoardId, 0, 0);

        public async Task<bool> ClearDisplayAsync(byte boardAddress, byte subAddress, byte serialNo)
        {
            if (boardAddress != _boardConfig.BoardId)
            {
                 Console.WriteLine($"CGDB Client ({_boardConfig.BoardName}) Error: Mismatched boardAddress ({boardAddress}) for clear.");
                 Status = new BoardStatus { IsLinkOk = false, StatusMessage = "Config error: Board ID mismatch for clear.", FirmwareVersion = Status.FirmwareVersion };
                 return false;
            }
            Console.WriteLine($"CGDB Client ({_boardConfig.BoardName}): Clearing display.");
            byte[] packet = DisplayPacketBuilder.BuildCgdbClearScreenPacket(_boardConfig.BoardId);
            try
            {
                await _communicationService.WriteDataAsync(packet);
                Status = new BoardStatus { IsLinkOk = true, StatusMessage = "Display cleared.", FirmwareVersion = Status.FirmwareVersion };
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"CGDB Client ({_boardConfig.BoardName}) Error: Failed to clear display. Exception: {ex.Message}");
                Status = new BoardStatus { IsLinkOk = false, StatusMessage = $"Clear failed: {ex.Message}", FirmwareVersion = Status.FirmwareVersion };
                return false;
            }
        }

        public async Task<BoardStatus> CheckLinkAsync() => await CheckLinkAsync(_boardConfig.BoardId, 0, 0);

        public async Task<BoardStatus> CheckLinkAsync(byte boardAddress, byte subAddress, byte serialNo)
        {
            if (boardAddress != _boardConfig.BoardId)
            {
                 Console.WriteLine($"CGDB Client ({_boardConfig.BoardName}) Error: Mismatched boardAddress ({boardAddress}) for link check.");
                 Status = new BoardStatus { IsLinkOk = false, StatusMessage = "Config error: Board ID mismatch for link check.", FirmwareVersion = Status.FirmwareVersion };
                 return Status;
            }
            Console.WriteLine($"CGDB Client ({_boardConfig.BoardName}): Checking link.");
            byte[] packet = DisplayPacketBuilder.BuildLinkCheckPacket(_boardConfig.BoardId);
            try
            {
                await _communicationService.WriteDataAsync(packet);
                Status = new BoardStatus { IsLinkOk = true, StatusMessage = "Link check sent (response check not implemented).", FirmwareVersion = Status.FirmwareVersion };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"CGDB Client ({_boardConfig.BoardName}) Error: Failed to send link check. Exception: {ex.Message}");
                Status = new BoardStatus { IsLinkOk = false, StatusMessage = $"Link check send failed: {ex.Message}", FirmwareVersion = Status.FirmwareVersion };
            }
            return Status;
        }

        public async Task<bool> SetConfigurationAsync(byte boardAddress, byte subAddress, byte serialNo, byte[] configData)
        {
            Console.WriteLine($"CGDB Client ({_boardConfig.BoardName}): SetConfigurationAsync - NOT IMPLEMENTED.");
            await Task.CompletedTask;
            Status = new BoardStatus { IsLinkOk = Status.IsLinkOk, StatusMessage = "SetConfiguration not implemented.", FirmwareVersion = Status.FirmwareVersion };
            return false;
        }

        public async Task<byte[]> GetConfigurationAsync(byte boardAddress, byte subAddress, byte serialNo)
        {
            Console.WriteLine($"CGDB Client ({_boardConfig.BoardName}): GetConfigurationAsync - NOT IMPLEMENTED.");
            await Task.CompletedTask;
            Status = new BoardStatus { IsLinkOk = Status.IsLinkOk, StatusMessage = "GetConfiguration not implemented.", FirmwareVersion = Status.FirmwareVersion };
            return Array.Empty<byte>();
        }

        public async Task<bool> ResetBoardAsync(byte boardAddress, byte subAddress, byte serialNo)
        {
            Console.WriteLine($"CGDB Client ({_boardConfig.BoardName}): ResetBoardAsync - NOT IMPLEMENTED.");
            await Task.CompletedTask;
            Status = new BoardStatus { IsLinkOk = Status.IsLinkOk, StatusMessage = "ResetBoard not implemented.", FirmwareVersion = Status.FirmwareVersion };
            return false;
        }
    }
}
