using System.Collections.Generic;
using IPIS.Repositories.Interfaces;
using IPIS.Models;
using IPIS.Utils;

namespace IPIS.Services
{
    public class StationService
    {
        private readonly IStationRepository stationRepository;

        public StationService(IStationRepository stationRepository)
        {
            this.stationRepository = stationRepository;
        }

        public StationDetails GetStationDetails(string stationName)
        {
            return stationRepository.GetStationDetails(stationName);
        }

        public void AddStation(StationDetails station)
        {
            try
            {
                stationRepository.AddStation(station);

                // Log successful station addition
                Logger.LogStationAdded(station.StationCode, station.StationName);
                Logger.LogDatabaseOperation("INSERT", "Station_Details", true, $"Station {station.StationCode} added successfully");
            }
            catch (Exception ex)
            {
                // Log failed station addition
                Logger.LogError(LogCategory.StationManagement, $"Failed to add station: {station.StationCode}",
                               $"Error: {ex.Message}", "StationService.AddStation", ex);
                Logger.LogDatabaseOperation("INSERT", "Station_Details", false, $"Failed to add station {station.StationCode}: {ex.Message}");
                throw;
            }
        }

        public void UpdateStation(StationDetails station)
        {
            try
            {
                // Get old values for logging
                var oldStation = GetStationDetails(station.StationName);
                string oldValues = oldStation != null ?
                    $"Code: {oldStation.StationCode}, Platforms: {oldStation.AvailablePF}, AutoLoad: {oldStation.AutoLoad}" :
                    "Previous values not available";

                string newValues = $"Code: {station.StationCode}, Platforms: {station.AvailablePF}, AutoLoad: {station.AutoLoad}";

                stationRepository.UpdateStation(station);

                // Log successful station update
                Logger.LogStationUpdated(station.StationCode, oldValues, newValues);
                Logger.LogDatabaseOperation("UPDATE", "Station_Details", true, $"Station {station.StationCode} updated successfully");
            }
            catch (Exception ex)
            {
                // Log failed station update
                Logger.LogError(LogCategory.StationManagement, $"Failed to update station: {station.StationCode}",
                               $"Error: {ex.Message}", "StationService.UpdateStation", ex);
                Logger.LogDatabaseOperation("UPDATE", "Station_Details", false, $"Failed to update station {station.StationCode}: {ex.Message}");
                throw;
            }
        }

        public void DeleteStation(string stationName)
        {
            try
            {
                // Get station details before deletion for logging
                var stationToDelete = GetStationDetails(stationName);
                string stationCode = stationToDelete?.StationCode ?? "Unknown";

                stationRepository.DeleteStation(stationName);

                // Log successful station deletion
                Logger.LogStationDeleted(stationCode, stationName);
                Logger.LogDatabaseOperation("DELETE", "Station_Details", true, $"Station {stationCode} deleted successfully");
            }
            catch (Exception ex)
            {
                // Log failed station deletion
                Logger.LogError(LogCategory.StationManagement, $"Failed to delete station: {stationName}",
                               $"Error: {ex.Message}", "StationService.DeleteStation", ex);
                Logger.LogDatabaseOperation("DELETE", "Station_Details", false, $"Failed to delete station {stationName}: {ex.Message}");
                throw;
            }
        }

        public List<string> GetAllStationNames()
        {
            return stationRepository.GetAllStationNames();
        }

        public Dictionary<string, string> GetStationNamesAndCodes()
        {
            return stationRepository.GetStationNamesAndCodes();
        }

        public List<StationDetails> GetAllStations()
        {
            return stationRepository.GetAllStations();
        }

        public StationDetails GetCurrentStation()
        {
            return stationRepository.GetCurrentStation();
        }

        public void SetCurrentStation(string stationName)
        {
            stationRepository.SetCurrentStation(stationName);
        }

        public List<StationDetails> SearchStations(string searchTerm)
        {
            return stationRepository.SearchStations(searchTerm);
        }

        public void ClearAllStations()
        {
            try
            {
                stationRepository.ClearAllStations();
                
                // Log successful station data clearing
                Logger.LogInfo(LogCategory.StationManagement, "All station data cleared",
                              "All records from Station_Details table have been cleared", "StationService.ClearAllStations");
                Logger.LogDatabaseOperation("DELETE", "Station_Details", true, "All station data cleared successfully");
            }
            catch (Exception ex)
            {
                // Log failed station data clearing
                Logger.LogError(LogCategory.StationManagement, "Failed to clear all station data",
                               $"Error: {ex.Message}", "StationService.ClearAllStations", ex);
                Logger.LogDatabaseOperation("DELETE", "Station_Details", false, $"Failed to clear all station data: {ex.Message}");
                throw;
            }
        }
    }
}