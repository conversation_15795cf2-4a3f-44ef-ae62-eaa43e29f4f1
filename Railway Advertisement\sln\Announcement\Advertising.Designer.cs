﻿namespace Announcement
{
	// Token: 0x02000003 RID: 3
	public partial class Advertising : global::System.Windows.Forms.Form
	{
		// Token: 0x06000025 RID: 37 RVA: 0x00004218 File Offset: 0x00002418
		protected override void Dispose(bool disposing)
		{
			bool flag = disposing && this.components != null;
			if (flag)
			{
				this.components.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06000026 RID: 38 RVA: 0x00004250 File Offset: 0x00002450
		private void InitializeComponent()
		{
			global::System.ComponentModel.ComponentResourceManager componentResourceManager = new global::System.ComponentModel.ComponentResourceManager(typeof(global::Announcement.Advertising));
			this.BTN_New = new global::System.Windows.Forms.Button();
			this.BTN_Del = new global::System.Windows.Forms.Button();
			this.BTN_Exit = new global::System.Windows.Forms.Button();
			this.BTN_Save = new global::System.Windows.Forms.Button();
			this.Open_WaveFile = new global::System.Windows.Forms.OpenFileDialog();
			this.BTN_Edit = new global::System.Windows.Forms.Button();
			this.BTN_SEdit = new global::System.Windows.Forms.Button();
			this.BTN_SSave = new global::System.Windows.Forms.Button();
			this.BTN_SDel = new global::System.Windows.Forms.Button();
			this.BTN_SAdd = new global::System.Windows.Forms.Button();
			this.label1 = new global::System.Windows.Forms.Label();
			this.TB_SHwf = new global::System.Windows.Forms.TextBox();
			this.BTN_SEW = new global::System.Windows.Forms.Button();
			this.TB_SEwf = new global::System.Windows.Forms.TextBox();
			this.BTN_SHW = new global::System.Windows.Forms.Button();
			this.CB_SName = new global::System.Windows.Forms.ComboBox();
			this.CB_AName = new global::System.Windows.Forms.ComboBox();
			this.BTN_HW = new global::System.Windows.Forms.Button();
			this.TB_Ewf = new global::System.Windows.Forms.TextBox();
			this.BTN_EW = new global::System.Windows.Forms.Button();
			this.TB_Hwf = new global::System.Windows.Forms.TextBox();
			this.LB_5 = new global::System.Windows.Forms.Label();
			this.groupBox1 = new global::System.Windows.Forms.GroupBox();
			this.groupBox2 = new global::System.Windows.Forms.GroupBox();
			this.label2 = new global::System.Windows.Forms.Label();
			this.BTN_LBEdit = new global::System.Windows.Forms.Button();
			this.CB_MType = new global::System.Windows.Forms.ComboBox();
			this.LB_Messages = new global::System.Windows.Forms.CheckedListBox();
			this.GB_ARC = new global::System.Windows.Forms.GroupBox();
			this.TB_PT = new global::System.Windows.Forms.TextBox();
			this.label4 = new global::System.Windows.Forms.Label();
			this.label3 = new global::System.Windows.Forms.Label();
			this.TB_APC = new global::System.Windows.Forms.TextBox();
			this.groupBox1.SuspendLayout();
			this.groupBox2.SuspendLayout();
			this.GB_ARC.SuspendLayout();
			base.SuspendLayout();
			this.BTN_New.Enabled = false;
			this.BTN_New.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 14.25f, global::System.Drawing.FontStyle.Bold, global::System.Drawing.GraphicsUnit.Point, 0);
			this.BTN_New.ForeColor = global::System.Drawing.Color.FromArgb(0, 192, 0);
			this.BTN_New.Image = (global::System.Drawing.Image)componentResourceManager.GetObject("BTN_New.Image");
			this.BTN_New.ImageAlign = global::System.Drawing.ContentAlignment.MiddleRight;
			this.BTN_New.Location = new global::System.Drawing.Point(6, 140);
			this.BTN_New.Name = "BTN_New";
			this.BTN_New.Size = new global::System.Drawing.Size(115, 46);
			this.BTN_New.TabIndex = 6;
			this.BTN_New.Text = "NEW";
			this.BTN_New.TextAlign = global::System.Drawing.ContentAlignment.MiddleRight;
			this.BTN_New.UseVisualStyleBackColor = true;
			this.BTN_New.Click += new global::System.EventHandler(this.BTN_New_Click);
			this.BTN_Del.Enabled = false;
			this.BTN_Del.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 14.25f, global::System.Drawing.FontStyle.Bold, global::System.Drawing.GraphicsUnit.Point, 0);
			this.BTN_Del.ForeColor = global::System.Drawing.Color.Red;
			this.BTN_Del.Image = (global::System.Drawing.Image)componentResourceManager.GetObject("BTN_Del.Image");
			this.BTN_Del.ImageAlign = global::System.Drawing.ContentAlignment.MiddleLeft;
			this.BTN_Del.Location = new global::System.Drawing.Point(269, 141);
			this.BTN_Del.Name = "BTN_Del";
			this.BTN_Del.Size = new global::System.Drawing.Size(127, 46);
			this.BTN_Del.TabIndex = 7;
			this.BTN_Del.Text = "DELETE";
			this.BTN_Del.TextAlign = global::System.Drawing.ContentAlignment.MiddleRight;
			this.BTN_Del.UseVisualStyleBackColor = true;
			this.BTN_Del.Click += new global::System.EventHandler(this.BTN_Del_Click);
			this.BTN_Exit.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 14.25f, global::System.Drawing.FontStyle.Bold, global::System.Drawing.GraphicsUnit.Point, 0);
			this.BTN_Exit.ForeColor = global::System.Drawing.Color.Red;
			this.BTN_Exit.Image = (global::System.Drawing.Image)componentResourceManager.GetObject("BTN_Exit.Image");
			this.BTN_Exit.ImageAlign = global::System.Drawing.ContentAlignment.MiddleLeft;
			this.BTN_Exit.Location = new global::System.Drawing.Point(949, 11);
			this.BTN_Exit.Name = "BTN_Exit";
			this.BTN_Exit.Size = new global::System.Drawing.Size(114, 46);
			this.BTN_Exit.TabIndex = 8;
			this.BTN_Exit.Text = "EXIT";
			this.BTN_Exit.TextAlign = global::System.Drawing.ContentAlignment.MiddleRight;
			this.BTN_Exit.UseVisualStyleBackColor = true;
			this.BTN_Exit.Click += new global::System.EventHandler(this.BTN_Exit_Click);
			this.BTN_Save.Enabled = false;
			this.BTN_Save.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 14.25f, global::System.Drawing.FontStyle.Bold, global::System.Drawing.GraphicsUnit.Point, 0);
			this.BTN_Save.ForeColor = global::System.Drawing.Color.FromArgb(0, 0, 192);
			this.BTN_Save.Image = (global::System.Drawing.Image)componentResourceManager.GetObject("BTN_Save.Image");
			this.BTN_Save.ImageAlign = global::System.Drawing.ContentAlignment.MiddleLeft;
			this.BTN_Save.Location = new global::System.Drawing.Point(412, 141);
			this.BTN_Save.Name = "BTN_Save";
			this.BTN_Save.Size = new global::System.Drawing.Size(114, 46);
			this.BTN_Save.TabIndex = 10;
			this.BTN_Save.Text = "SAVE";
			this.BTN_Save.TextAlign = global::System.Drawing.ContentAlignment.MiddleRight;
			this.BTN_Save.UseVisualStyleBackColor = true;
			this.BTN_Save.Click += new global::System.EventHandler(this.BTN_Save_Click);
			this.Open_WaveFile.FileName = "Open_WaveFile";
			this.BTN_Edit.Enabled = false;
			this.BTN_Edit.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 12f, global::System.Drawing.FontStyle.Bold, global::System.Drawing.GraphicsUnit.Point, 0);
			this.BTN_Edit.ForeColor = global::System.Drawing.Color.FromArgb(0, 0, 192);
			this.BTN_Edit.Image = (global::System.Drawing.Image)componentResourceManager.GetObject("BTN_Edit.Image");
			this.BTN_Edit.ImageAlign = global::System.Drawing.ContentAlignment.MiddleLeft;
			this.BTN_Edit.Location = new global::System.Drawing.Point(140, 141);
			this.BTN_Edit.Name = "BTN_Edit";
			this.BTN_Edit.Size = new global::System.Drawing.Size(114, 46);
			this.BTN_Edit.TabIndex = 44;
			this.BTN_Edit.Text = "EDIT";
			this.BTN_Edit.TextAlign = global::System.Drawing.ContentAlignment.MiddleRight;
			this.BTN_Edit.UseVisualStyleBackColor = true;
			this.BTN_Edit.Click += new global::System.EventHandler(this.BTN_Edit_Click);
			this.BTN_SEdit.Enabled = false;
			this.BTN_SEdit.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 12f, global::System.Drawing.FontStyle.Bold, global::System.Drawing.GraphicsUnit.Point, 0);
			this.BTN_SEdit.ForeColor = global::System.Drawing.Color.FromArgb(0, 0, 192);
			this.BTN_SEdit.Image = (global::System.Drawing.Image)componentResourceManager.GetObject("BTN_SEdit.Image");
			this.BTN_SEdit.ImageAlign = global::System.Drawing.ContentAlignment.MiddleLeft;
			this.BTN_SEdit.Location = new global::System.Drawing.Point(138, 143);
			this.BTN_SEdit.Name = "BTN_SEdit";
			this.BTN_SEdit.Size = new global::System.Drawing.Size(114, 46);
			this.BTN_SEdit.TabIndex = 48;
			this.BTN_SEdit.Text = "EDIT";
			this.BTN_SEdit.TextAlign = global::System.Drawing.ContentAlignment.MiddleRight;
			this.BTN_SEdit.UseVisualStyleBackColor = true;
			this.BTN_SEdit.Click += new global::System.EventHandler(this.BTN_SEdit_Click);
			this.BTN_SSave.Enabled = false;
			this.BTN_SSave.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 14.25f, global::System.Drawing.FontStyle.Bold, global::System.Drawing.GraphicsUnit.Point, 0);
			this.BTN_SSave.ForeColor = global::System.Drawing.Color.FromArgb(0, 0, 192);
			this.BTN_SSave.Image = (global::System.Drawing.Image)componentResourceManager.GetObject("BTN_SSave.Image");
			this.BTN_SSave.ImageAlign = global::System.Drawing.ContentAlignment.MiddleLeft;
			this.BTN_SSave.Location = new global::System.Drawing.Point(411, 142);
			this.BTN_SSave.Name = "BTN_SSave";
			this.BTN_SSave.Size = new global::System.Drawing.Size(114, 46);
			this.BTN_SSave.TabIndex = 47;
			this.BTN_SSave.Text = "SAVE";
			this.BTN_SSave.TextAlign = global::System.Drawing.ContentAlignment.MiddleRight;
			this.BTN_SSave.UseVisualStyleBackColor = true;
			this.BTN_SSave.Click += new global::System.EventHandler(this.BTN_SSave_Click);
			this.BTN_SDel.Enabled = false;
			this.BTN_SDel.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 14.25f, global::System.Drawing.FontStyle.Bold, global::System.Drawing.GraphicsUnit.Point, 0);
			this.BTN_SDel.ForeColor = global::System.Drawing.Color.Red;
			this.BTN_SDel.Image = (global::System.Drawing.Image)componentResourceManager.GetObject("BTN_SDel.Image");
			this.BTN_SDel.ImageAlign = global::System.Drawing.ContentAlignment.MiddleLeft;
			this.BTN_SDel.Location = new global::System.Drawing.Point(271, 142);
			this.BTN_SDel.Name = "BTN_SDel";
			this.BTN_SDel.Size = new global::System.Drawing.Size(124, 46);
			this.BTN_SDel.TabIndex = 46;
			this.BTN_SDel.Text = "DELETE";
			this.BTN_SDel.TextAlign = global::System.Drawing.ContentAlignment.MiddleRight;
			this.BTN_SDel.UseVisualStyleBackColor = true;
			this.BTN_SDel.Click += new global::System.EventHandler(this.BTN_SDel_Click);
			this.BTN_SAdd.Enabled = false;
			this.BTN_SAdd.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 14.25f, global::System.Drawing.FontStyle.Bold, global::System.Drawing.GraphicsUnit.Point, 0);
			this.BTN_SAdd.ForeColor = global::System.Drawing.Color.FromArgb(0, 192, 0);
			this.BTN_SAdd.Image = (global::System.Drawing.Image)componentResourceManager.GetObject("BTN_SAdd.Image");
			this.BTN_SAdd.ImageAlign = global::System.Drawing.ContentAlignment.MiddleRight;
			this.BTN_SAdd.Location = new global::System.Drawing.Point(10, 143);
			this.BTN_SAdd.Name = "BTN_SAdd";
			this.BTN_SAdd.Size = new global::System.Drawing.Size(115, 46);
			this.BTN_SAdd.TabIndex = 45;
			this.BTN_SAdd.Text = "NEW";
			this.BTN_SAdd.TextAlign = global::System.Drawing.ContentAlignment.MiddleRight;
			this.BTN_SAdd.UseVisualStyleBackColor = true;
			this.BTN_SAdd.Click += new global::System.EventHandler(this.BTN_SAdd_Click);
			this.label1.AutoSize = true;
			this.label1.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 12f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 0);
			this.label1.ForeColor = global::System.Drawing.Color.FromArgb(0, 0, 192);
			this.label1.Location = new global::System.Drawing.Point(6, 32);
			this.label1.Name = "label1";
			this.label1.Size = new global::System.Drawing.Size(156, 20);
			this.label1.TabIndex = 14;
			this.label1.Text = "Enter Slogans Name";
			this.TB_SHwf.Enabled = false;
			this.TB_SHwf.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 12f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 0);
			this.TB_SHwf.Location = new global::System.Drawing.Point(182, 65);
			this.TB_SHwf.Name = "TB_SHwf";
			this.TB_SHwf.Size = new global::System.Drawing.Size(343, 26);
			this.TB_SHwf.TabIndex = 15;
			this.BTN_SEW.Enabled = false;
			this.BTN_SEW.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 12f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 0);
			this.BTN_SEW.ForeColor = global::System.Drawing.Color.Black;
			this.BTN_SEW.Location = new global::System.Drawing.Point(6, 100);
			this.BTN_SEW.Name = "BTN_SEW";
			this.BTN_SEW.Size = new global::System.Drawing.Size(163, 29);
			this.BTN_SEW.TabIndex = 19;
			this.BTN_SEW.Text = "Select English Wave";
			this.BTN_SEW.UseVisualStyleBackColor = true;
			this.BTN_SEW.Click += new global::System.EventHandler(this.BTN_SEW_Click);
			this.TB_SEwf.Enabled = false;
			this.TB_SEwf.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 12f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 0);
			this.TB_SEwf.Location = new global::System.Drawing.Point(182, 102);
			this.TB_SEwf.Name = "TB_SEwf";
			this.TB_SEwf.Size = new global::System.Drawing.Size(343, 26);
			this.TB_SEwf.TabIndex = 16;
			this.BTN_SHW.Enabled = false;
			this.BTN_SHW.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 12f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 0);
			this.BTN_SHW.ForeColor = global::System.Drawing.Color.Black;
			this.BTN_SHW.Location = new global::System.Drawing.Point(6, 64);
			this.BTN_SHW.Name = "BTN_SHW";
			this.BTN_SHW.Size = new global::System.Drawing.Size(163, 29);
			this.BTN_SHW.TabIndex = 18;
			this.BTN_SHW.Text = "Select Hindi Wave";
			this.BTN_SHW.UseVisualStyleBackColor = true;
			this.BTN_SHW.Click += new global::System.EventHandler(this.BTN_SHW_Click);
			this.CB_SName.Enabled = false;
			this.CB_SName.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 12f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 0);
			this.CB_SName.FormattingEnabled = true;
			this.CB_SName.Location = new global::System.Drawing.Point(182, 28);
			this.CB_SName.Name = "CB_SName";
			this.CB_SName.Size = new global::System.Drawing.Size(343, 28);
			this.CB_SName.TabIndex = 17;
			this.CB_SName.SelectedIndexChanged += new global::System.EventHandler(this.CB_SName_SelectedIndexChanged);
			this.CB_AName.Enabled = false;
			this.CB_AName.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 12f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 0);
			this.CB_AName.FormattingEnabled = true;
			this.CB_AName.Location = new global::System.Drawing.Point(177, 28);
			this.CB_AName.Name = "CB_AName";
			this.CB_AName.Size = new global::System.Drawing.Size(349, 28);
			this.CB_AName.TabIndex = 9;
			this.CB_AName.SelectedIndexChanged += new global::System.EventHandler(this.CB_AName_SelectedIndexChanged);
			this.BTN_HW.Enabled = false;
			this.BTN_HW.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 12f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 0);
			this.BTN_HW.ForeColor = global::System.Drawing.Color.Black;
			this.BTN_HW.Location = new global::System.Drawing.Point(1, 64);
			this.BTN_HW.Name = "BTN_HW";
			this.BTN_HW.Size = new global::System.Drawing.Size(163, 28);
			this.BTN_HW.TabIndex = 11;
			this.BTN_HW.Text = "Select Hindi Wave";
			this.BTN_HW.UseVisualStyleBackColor = true;
			this.BTN_HW.Click += new global::System.EventHandler(this.BTN_HW_Click);
			this.TB_Ewf.Enabled = false;
			this.TB_Ewf.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 12f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 0);
			this.TB_Ewf.Location = new global::System.Drawing.Point(177, 102);
			this.TB_Ewf.Name = "TB_Ewf";
			this.TB_Ewf.Size = new global::System.Drawing.Size(349, 26);
			this.TB_Ewf.TabIndex = 5;
			this.BTN_EW.Enabled = false;
			this.BTN_EW.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 12f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 0);
			this.BTN_EW.ForeColor = global::System.Drawing.Color.Black;
			this.BTN_EW.Location = new global::System.Drawing.Point(1, 100);
			this.BTN_EW.Name = "BTN_EW";
			this.BTN_EW.Size = new global::System.Drawing.Size(163, 28);
			this.BTN_EW.TabIndex = 12;
			this.BTN_EW.Text = "Select English Wave";
			this.BTN_EW.UseVisualStyleBackColor = true;
			this.BTN_EW.Click += new global::System.EventHandler(this.BTN_EW_Click);
			this.TB_Hwf.Enabled = false;
			this.TB_Hwf.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 12f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 0);
			this.TB_Hwf.Location = new global::System.Drawing.Point(177, 65);
			this.TB_Hwf.Name = "TB_Hwf";
			this.TB_Hwf.Size = new global::System.Drawing.Size(349, 26);
			this.TB_Hwf.TabIndex = 4;
			this.LB_5.AutoSize = true;
			this.LB_5.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 12f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 0);
			this.LB_5.ForeColor = global::System.Drawing.Color.FromArgb(0, 0, 192);
			this.LB_5.Location = new global::System.Drawing.Point(1, 32);
			this.LB_5.Name = "LB_5";
			this.LB_5.Size = new global::System.Drawing.Size(176, 20);
			this.LB_5.TabIndex = 0;
			this.LB_5.Text = "Enter Advertising Name";
			this.groupBox1.Controls.Add(this.CB_AName);
			this.groupBox1.Controls.Add(this.BTN_New);
			this.groupBox1.Controls.Add(this.BTN_HW);
			this.groupBox1.Controls.Add(this.BTN_Edit);
			this.groupBox1.Controls.Add(this.TB_Ewf);
			this.groupBox1.Controls.Add(this.BTN_Del);
			this.groupBox1.Controls.Add(this.BTN_EW);
			this.groupBox1.Controls.Add(this.BTN_Save);
			this.groupBox1.Controls.Add(this.TB_Hwf);
			this.groupBox1.Controls.Add(this.LB_5);
			this.groupBox1.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 14.25f, global::System.Drawing.FontStyle.Bold, global::System.Drawing.GraphicsUnit.Point, 0);
			this.groupBox1.ForeColor = global::System.Drawing.Color.FromArgb(0, 192, 0);
			this.groupBox1.Location = new global::System.Drawing.Point(12, 56);
			this.groupBox1.Name = "groupBox1";
			this.groupBox1.Size = new global::System.Drawing.Size(532, 204);
			this.groupBox1.TabIndex = 49;
			this.groupBox1.TabStop = false;
			this.groupBox1.Text = "ADVERTISING";
			this.groupBox1.Enter += new global::System.EventHandler(this.groupBox1_Enter);
			this.groupBox2.Controls.Add(this.BTN_SHW);
			this.groupBox2.Controls.Add(this.BTN_SEW);
			this.groupBox2.Controls.Add(this.TB_SHwf);
			this.groupBox2.Controls.Add(this.BTN_SSave);
			this.groupBox2.Controls.Add(this.BTN_SEdit);
			this.groupBox2.Controls.Add(this.BTN_SDel);
			this.groupBox2.Controls.Add(this.TB_SEwf);
			this.groupBox2.Controls.Add(this.CB_SName);
			this.groupBox2.Controls.Add(this.label1);
			this.groupBox2.Controls.Add(this.BTN_SAdd);
			this.groupBox2.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 14.25f, global::System.Drawing.FontStyle.Bold, global::System.Drawing.GraphicsUnit.Point, 0);
			this.groupBox2.ForeColor = global::System.Drawing.Color.FromArgb(0, 192, 0);
			this.groupBox2.Location = new global::System.Drawing.Point(13, 266);
			this.groupBox2.Name = "groupBox2";
			this.groupBox2.Size = new global::System.Drawing.Size(531, 210);
			this.groupBox2.TabIndex = 50;
			this.groupBox2.TabStop = false;
			this.groupBox2.Text = "SLOGANS";
			this.label2.AutoSize = true;
			this.label2.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 12f, global::System.Drawing.FontStyle.Bold, global::System.Drawing.GraphicsUnit.Point, 0);
			this.label2.ForeColor = global::System.Drawing.Color.FromArgb(0, 0, 192);
			this.label2.Location = new global::System.Drawing.Point(11, 16);
			this.label2.Name = "label2";
			this.label2.Size = new global::System.Drawing.Size(161, 20);
			this.label2.TabIndex = 51;
			this.label2.Text = "Add Massage Type";
			this.BTN_LBEdit.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 12f, global::System.Drawing.FontStyle.Bold, global::System.Drawing.GraphicsUnit.Point, 0);
			this.BTN_LBEdit.ForeColor = global::System.Drawing.Color.FromArgb(0, 0, 192);
			this.BTN_LBEdit.Image = (global::System.Drawing.Image)componentResourceManager.GetObject("BTN_LBEdit.Image");
			this.BTN_LBEdit.ImageAlign = global::System.Drawing.ContentAlignment.MiddleLeft;
			this.BTN_LBEdit.Location = new global::System.Drawing.Point(832, 11);
			this.BTN_LBEdit.Name = "BTN_LBEdit";
			this.BTN_LBEdit.Size = new global::System.Drawing.Size(114, 46);
			this.BTN_LBEdit.TabIndex = 54;
			this.BTN_LBEdit.Text = "EDIT";
			this.BTN_LBEdit.TextAlign = global::System.Drawing.ContentAlignment.MiddleRight;
			this.BTN_LBEdit.UseVisualStyleBackColor = true;
			this.BTN_LBEdit.Visible = false;
			this.BTN_LBEdit.Click += new global::System.EventHandler(this.BTN_LBEdit_Click);
			this.CB_MType.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 12f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 0);
			this.CB_MType.ForeColor = global::System.Drawing.Color.Red;
			this.CB_MType.FormattingEnabled = true;
			this.CB_MType.Items.AddRange(new object[]
			{
				"Advertising",
				"Slogans"
			});
			this.CB_MType.Location = new global::System.Drawing.Point(178, 13);
			this.CB_MType.Name = "CB_MType";
			this.CB_MType.Size = new global::System.Drawing.Size(206, 28);
			this.CB_MType.TabIndex = 55;
			this.CB_MType.Text = "Select Messages Type";
			this.CB_MType.SelectedIndexChanged += new global::System.EventHandler(this.CB_MType_SelectedIndexChanged);
			this.LB_Messages.CheckOnClick = true;
			this.LB_Messages.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 9.75f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 0);
			this.LB_Messages.FormattingEnabled = true;
			this.LB_Messages.Location = new global::System.Drawing.Point(554, 64);
			this.LB_Messages.Name = "LB_Messages";
			this.LB_Messages.Size = new global::System.Drawing.Size(520, 412);
			this.LB_Messages.TabIndex = 56;
			this.LB_Messages.SelectedIndexChanged += new global::System.EventHandler(this.LB_Messages_SelectedIndexChanged_1);
			this.GB_ARC.Controls.Add(this.TB_PT);
			this.GB_ARC.Controls.Add(this.label4);
			this.GB_ARC.Controls.Add(this.label3);
			this.GB_ARC.Controls.Add(this.TB_APC);
			this.GB_ARC.Enabled = false;
			this.GB_ARC.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 9.75f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 0);
			this.GB_ARC.ForeColor = global::System.Drawing.Color.Red;
			this.GB_ARC.Location = new global::System.Drawing.Point(401, 5);
			this.GB_ARC.Name = "GB_ARC";
			this.GB_ARC.Size = new global::System.Drawing.Size(425, 53);
			this.GB_ARC.TabIndex = 58;
			this.GB_ARC.TabStop = false;
			this.GB_ARC.Text = "Advertising Repeat Count ";
			this.TB_PT.Enabled = false;
			this.TB_PT.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 12f, global::System.Drawing.FontStyle.Bold, global::System.Drawing.GraphicsUnit.Point, 0);
			this.TB_PT.Location = new global::System.Drawing.Point(336, 21);
			this.TB_PT.MaxLength = 6;
			this.TB_PT.Name = "TB_PT";
			this.TB_PT.Size = new global::System.Drawing.Size(83, 26);
			this.TB_PT.TabIndex = 3;
			this.TB_PT.TextAlign = global::System.Windows.Forms.HorizontalAlignment.Center;
			this.TB_PT.TextChanged += new global::System.EventHandler(this.TB_PT_TextChanged);
			this.label4.AutoSize = true;
			this.label4.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 12f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 0);
			this.label4.ForeColor = global::System.Drawing.Color.Red;
			this.label4.Location = new global::System.Drawing.Point(193, 24);
			this.label4.Name = "label4";
			this.label4.Size = new global::System.Drawing.Size(138, 20);
			this.label4.TabIndex = 2;
			this.label4.Text = "Become less Time";
			this.label3.AutoSize = true;
			this.label3.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 12f, global::System.Drawing.FontStyle.Regular, global::System.Drawing.GraphicsUnit.Point, 0);
			this.label3.ForeColor = global::System.Drawing.Color.Blue;
			this.label3.Location = new global::System.Drawing.Point(4, 24);
			this.label3.Name = "label3";
			this.label3.Size = new global::System.Drawing.Size(85, 20);
			this.label3.TabIndex = 1;
			this.label3.Text = "Play Count";
			this.TB_APC.Font = new global::System.Drawing.Font("Microsoft Sans Serif", 12f, global::System.Drawing.FontStyle.Bold, global::System.Drawing.GraphicsUnit.Point, 0);
			this.TB_APC.Location = new global::System.Drawing.Point(95, 21);
			this.TB_APC.MaxLength = 6;
			this.TB_APC.Name = "TB_APC";
			this.TB_APC.Size = new global::System.Drawing.Size(83, 26);
			this.TB_APC.TabIndex = 0;
			this.TB_APC.TextAlign = global::System.Windows.Forms.HorizontalAlignment.Center;
			this.TB_APC.TextChanged += new global::System.EventHandler(this.textBox1_TextChanged);
			base.AutoScaleDimensions = new global::System.Drawing.SizeF(6f, 13f);
			base.AutoScaleMode = global::System.Windows.Forms.AutoScaleMode.Font;
			this.BackColor = global::System.Drawing.Color.FromArgb(255, 192, 128);
			base.ClientSize = new global::System.Drawing.Size(1082, 484);
			base.ControlBox = false;
			base.Controls.Add(this.GB_ARC);
			base.Controls.Add(this.LB_Messages);
			base.Controls.Add(this.CB_MType);
			base.Controls.Add(this.BTN_LBEdit);
			base.Controls.Add(this.label2);
			base.Controls.Add(this.groupBox2);
			base.Controls.Add(this.groupBox1);
			base.Controls.Add(this.BTN_Exit);
			base.Name = "Advertising";
			base.StartPosition = global::System.Windows.Forms.FormStartPosition.CenterScreen;
			this.Text = "Add Advertising";
			base.Load += new global::System.EventHandler(this.Advertising_Load);
			this.groupBox1.ResumeLayout(false);
			this.groupBox1.PerformLayout();
			this.groupBox2.ResumeLayout(false);
			this.groupBox2.PerformLayout();
			this.GB_ARC.ResumeLayout(false);
			this.GB_ARC.PerformLayout();
			base.ResumeLayout(false);
			base.PerformLayout();
		}

		// Token: 0x04000016 RID: 22
		private global::System.ComponentModel.IContainer components = null;

		// Token: 0x04000017 RID: 23
		private global::System.Windows.Forms.Button BTN_New;

		// Token: 0x04000018 RID: 24
		private global::System.Windows.Forms.Button BTN_Del;

		// Token: 0x04000019 RID: 25
		private global::System.Windows.Forms.Button BTN_Exit;

		// Token: 0x0400001A RID: 26
		private global::System.Windows.Forms.Button BTN_Save;

		// Token: 0x0400001B RID: 27
		private global::System.Windows.Forms.OpenFileDialog Open_WaveFile;

		// Token: 0x0400001C RID: 28
		private global::System.Windows.Forms.Button BTN_Edit;

		// Token: 0x0400001D RID: 29
		private global::System.Windows.Forms.Label label1;

		// Token: 0x0400001E RID: 30
		private global::System.Windows.Forms.TextBox TB_SHwf;

		// Token: 0x0400001F RID: 31
		private global::System.Windows.Forms.Button BTN_SEW;

		// Token: 0x04000020 RID: 32
		private global::System.Windows.Forms.TextBox TB_SEwf;

		// Token: 0x04000021 RID: 33
		private global::System.Windows.Forms.Button BTN_SHW;

		// Token: 0x04000022 RID: 34
		private global::System.Windows.Forms.ComboBox CB_SName;

		// Token: 0x04000023 RID: 35
		private global::System.Windows.Forms.Button BTN_SEdit;

		// Token: 0x04000024 RID: 36
		private global::System.Windows.Forms.Button BTN_SSave;

		// Token: 0x04000025 RID: 37
		private global::System.Windows.Forms.Button BTN_SDel;

		// Token: 0x04000026 RID: 38
		private global::System.Windows.Forms.Button BTN_SAdd;

		// Token: 0x04000027 RID: 39
		private global::System.Windows.Forms.ComboBox CB_AName;

		// Token: 0x04000028 RID: 40
		private global::System.Windows.Forms.Button BTN_HW;

		// Token: 0x04000029 RID: 41
		private global::System.Windows.Forms.TextBox TB_Ewf;

		// Token: 0x0400002A RID: 42
		private global::System.Windows.Forms.Button BTN_EW;

		// Token: 0x0400002B RID: 43
		private global::System.Windows.Forms.TextBox TB_Hwf;

		// Token: 0x0400002C RID: 44
		private global::System.Windows.Forms.Label LB_5;

		// Token: 0x0400002D RID: 45
		private global::System.Windows.Forms.GroupBox groupBox1;

		// Token: 0x0400002E RID: 46
		private global::System.Windows.Forms.GroupBox groupBox2;

		// Token: 0x0400002F RID: 47
		private global::System.Windows.Forms.Label label2;

		// Token: 0x04000030 RID: 48
		private global::System.Windows.Forms.Button BTN_LBEdit;

		// Token: 0x04000031 RID: 49
		private global::System.Windows.Forms.ComboBox CB_MType;

		// Token: 0x04000032 RID: 50
		private global::System.Windows.Forms.CheckedListBox LB_Messages;

		// Token: 0x04000033 RID: 51
		private global::System.Windows.Forms.GroupBox GB_ARC;

		// Token: 0x04000034 RID: 52
		private global::System.Windows.Forms.Label label3;

		// Token: 0x04000035 RID: 53
		private global::System.Windows.Forms.TextBox TB_APC;

		// Token: 0x04000036 RID: 54
		private global::System.Windows.Forms.TextBox TB_PT;

		// Token: 0x04000037 RID: 55
		private global::System.Windows.Forms.Label label4;
	}
}
