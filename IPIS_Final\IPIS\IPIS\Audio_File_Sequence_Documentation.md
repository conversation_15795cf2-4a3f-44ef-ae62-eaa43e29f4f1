# IPIS Audio File Sequence Documentation

## Overview
This document provides a comprehensive guide to the audio file sequence used in the IPIS (Integrated Passenger Information System) for train announcements. The system supports multiple languages (English and Hindi) and different train statuses, each with its own specific audio sequence.

## Audio File Structure

### Base Directory Structure
```
Data/WAVE/
├── SPL/                    # Special audio files
│   ├── TADA.wav           # Attention bell sound
│   ├── Bell.wav           # Bell sound
│   └── Contact*.wav       # Contact sounds
├── TRAIN TYPE/            # Train type announcements
├── ENGLISH/               # English language files
├── HINDI/                 # Hindi language files
└── Alphabets/             # Alphabet pronunciation
```

### Language-Specific Folders
Each language folder contains:
- **TRNO/**: Train number digit pronunciations (0-9, A)
- **TRNAME/**: Train name announcements
- **STD/**: Standard status messages (STD1-STD50)
- **CITY/**: Station/city name announcements
- **SLOGAN/**: Slogans and advertisements
- **PF/**: Platform number pronunciations
- **MIN/**: Minute pronunciations (0-59)
- **HOUR/**: Hour pronunciations (0-23)
- **HOUR_Only/**: Hour-only pronunciations
- **DELAY/**: Delay time pronunciations
- **ADVERTISING/**: Advertising messages

### Language-Specific Keywords
- **English**: EFROM, ETO, EVIA, ETRN, PATO, DN, UP
- **Hindi**: HFROM, HTO, HVIA, HTRN, LTO, DN, UP

## Standard Status Messages (STD Files)

| File | English Content | Hindi Content |
|------|----------------|---------------|
| STD1.wav | "may I have your attention please" | "kirpya dhayn diji" |
| STD2.wav | "is running on time it is expected to arrive here at" | - |
| STD3.wav | "will depart at its schedule" | - |
| STD4.wav | "om" | - |
| STD5.wav | "is arriving on" | - |
| STD6.wav | "is arriving shortly on" | - |
| STD7.wav | "is now ready for departure from" | - |
| STD8.wav | "we wish you all a very happy comfortable and safe journey" | - |
| STD9.wav | "is reported running late by" | - |
| STD10.wav | "inconvenience caused is deeply regretted" | - |
| STD11.wav | "is delayed by" | - |
| STD12.wav | "from its scheduled time" | - |
| STD13.wav | "the platform number of" | - |
| STD14.wav | "has been changed today this train will now Departed at" | - |
| STD15.wav | "passengers are requested to reach the new platform" | - |
| STD16.wav | "the time of" | - |
| STD17.wav | "has not yet been decided" | - |
| STD18.wav | "the passengers will be informed as soon as the information is available" | - |
| STD19.wav | "has been changed today this train will now arrive at" | - |
| STD20.wav | "instead" | - |
| STD21.wav | "has been cancelled today" | - |
| STD22.wav | "which was scheduled to the part" | - |
| STD23.wav | "husband put back ti" | - |
| STD24.wav | "and is expected to arrive" | - |
| STD25.wav | "it is likely to depart from" | - |
| STD26.wav | "has arrive" | - |
| STD27.wav | "passengers are requested to take their respective seats" | - |
| STD28.wav | "who will be terminated" | - |
| STD29.wav | "due to unavoidable reasons" | - |
| STD30.wav | "is diverted today this train will reach" | - |
| STD32.wav | "thank" | - |
| STD33.wav | "from" | - |
| STD35.wav | "passengers of train number" | - |
| STD36.wav | "are hereby informed that today's coach positions of train Rs follows from engine" | - |
| STD37.wav | "passengers of platform number" | - |
| STD38.wav | "are requested to keep away from the platform edge unknown stopping fast train is about to pass from this platform thank you" | - |
| STD39.wav | "arrive" | - |
| STD40.wav | "has been rescheduled at" | - |
| STD41.wav | "first party that" | - |
| STD42.wav | "from platform number" | - |
| STD43.wav | "is standing" | - |
| STD44.wav | "it is expected to arrive here at" | - |
| STD45.wav | "at" | - |
| STD46.wav | "via" | - |
| STD50B.wav | "Mathura railway station per yatriyon ka Swagat hai aapki Yatra Safal sukhd AVN mangalmay Ho Hamen Aisi Kamna Karte Hain" | - |
| STD99.wav | "Mathura Junction Railway Station" | - |

## Audio Sequence by Train Status

### 1. IS ARRIVING ON
**Sequence for both English and Hindi:**
1. `SPL/TADA.wav` - Attention bell
2. `{LANG}/STD/STD1.wav` - "may I have your attention please"
3. `{LANG}/{LANG}TRN.wav` - Train number keyword (ETRN/HTRN)
4. Train number digits (each digit separately)
5. Train name (if available)
6. Station information sequence:
   - `{LANG}/{LANG}FROM.wav` - "from" keyword
   - Source station name
   - `{LANG}/{LANG}TO.wav` - "to" keyword
   - Destination station name
   - `{LANG}/{LANG}VIA.wav` - "via" keyword (if via stations exist)
   - Via station names (if any)
7. `{LANG}/STD/STD5.wav` - "is arriving on"
8. Platform number digits (each digit separately)

### 2. HAS ARRIVED ON
**Sequence for both English and Hindi:**
1. `SPL/TADA.wav` - Attention bell
2. `{LANG}/STD/STD1.wav` - "may I have your attention please"
3. `{LANG}/{LANG}TRN.wav` - Train number keyword
4. Train number digits
5. Train name (if available)
6. Station information sequence (same as above)
7. `{LANG}/STD/STD26.wav` - "has arrive"
8. `{LANG}/STD/STD42.wav` - "from platform number"
9. Platform number digits

### 3. WILL ARRIVE SHORTLY
**Sequence for both English and Hindi:**
1. `SPL/TADA.wav` - Attention bell
2. `{LANG}/STD/STD1.wav` - "may I have your attention please"
3. `{LANG}/{LANG}TRN.wav` - Train number keyword
4. Train number digits
5. Train name (if available)
6. Station information sequence (same as above)
7. `{LANG}/STD/STD6.wav` - "is arriving shortly on"
8. Platform number digits

### 4. RUNNING RIGHT TIME
**Sequence for both English and Hindi:**
1. `SPL/TADA.wav` - Attention bell
2. `{LANG}/STD/STD1.wav` - "may I have your attention please"
3. `{LANG}/{LANG}TRN.wav` - Train number keyword
4. Train number digits
5. Train name (if available)
6. Station information sequence (same as above)
7. `{LANG}/STD/STD2.wav` - "is running on time it is expected to arrive here at"
8. Expected arrival time (hours and minutes)
9. `{LANG}/STD/STD33.wav` - "from"
10. Platform number digits

### 5. RUNNING LATE / INDEFINITE LATE
**Sequence for both English and Hindi:**
1. `SPL/TADA.wav` - Attention bell
2. `{LANG}/STD/STD1.wav` - "may I have your attention please"
3. `{LANG}/{LANG}TRN.wav` - Train number keyword
4. Train number digits
5. Train name (if available)
6. Station information sequence (same as above)
7. `{LANG}/STD/STD9.wav` - "is reported running late by"
8. Delay time (hours and minutes from DELAY folder)
9. `{LANG}/STD/STD10.wav` - "inconvenience caused is deeply regretted"
10. `{LANG}/STD/STD44.wav` - "it is expected to arrive here at"
11. Expected arrival time
12. `{LANG}/STD/STD42.wav` - "from platform number"
13. Platform number digits

### 6. CANCELLED
**Sequence for both English and Hindi:**
1. `SPL/TADA.wav` - Attention bell
2. `{LANG}/STD/STD1.wav` - "may I have your attention please"
3. `{LANG}/{LANG}TRN.wav` - Train number keyword
4. Train number digits
5. Train name (if available)
6. Station information sequence (same as above)
7. `{LANG}/STD/STD21.wav` - "has been cancelled today"
8. `{LANG}/STD/STD29.wav` - "due to unavoidable reasons"

### 7. PLATFORM CHANGED
**Sequence for both English and Hindi:**
1. `SPL/TADA.wav` - Attention bell
2. `{LANG}/STD/STD1.wav` - "may I have your attention please"
3. `{LANG}/{LANG}TRN.wav` - Train number keyword
4. Train number digits
5. Train name (if available)
6. Station information sequence (same as above)
7. `{LANG}/STD/STD13.wav` - "the platform number of"
8. `{LANG}/STD/STD19.wav` - "has been changed today this train will now arrive at"
9. New platform number digits
10. `{LANG}/STD/STD15.wav` - "passengers are requested to reach the new platform"

### 8. TERMINATED
**Sequence for both English and Hindi:**
1. `SPL/TADA.wav` - Attention bell
2. `{LANG}/STD/STD1.wav` - "may I have your attention please"
3. `{LANG}/{LANG}TRN.wav` - Train number keyword
4. Train number digits
5. Train name (if available)
6. Station information sequence (same as above)
7. `{LANG}/STD/STD28.wav` - "who will be terminated"
8. `{LANG}/STD/STD29.wav` - "due to unavoidable reasons"

## Station Information Sequence

The station information sequence is common across all statuses and includes:

1. **Source Station**: `{LANG}/{LANG}FROM.wav` + Source station name
2. **Destination Station**: `{LANG}/{LANG}TO.wav` + Destination station name
3. **Via Stations** (if any): `{LANG}/{LANG}VIA.wav` + Via station names

### Language-Specific Keywords:
- **English**: EFROM, ETO, EVIA
- **Hindi**: HFROM, HTO, HVIA

## Time Announcement Sequences

### Regular Time (HOUR/MIN folders)
- **Hours**: `{LANG}/HOUR/{hour}.wav`
- **Minutes**: `{LANG}/MIN/{minute}.wav`
- **Hour Only**: `{LANG}/HOUR_Only/{hour}.wav` (when minutes = 0)

### Delay Time (DELAY folder)
- **Delay Hours**: `{LANG}/DELAY/HOUR/{hours}.wav`
- **Delay Minutes**: `{LANG}/DELAY/MIN/{minutes}.wav`

## Slogan and Advertising Sequences

Slogans are played at the beginning of the announcement sequence:
1. Selected slogans from `{LANG}/SLOGAN/` folder
2. Followed by train announcements

## Language Differences

### English vs Hindi Sequences
While the basic structure remains the same, the main differences are:

1. **Keywords**: English uses E-prefixed keywords, Hindi uses H-prefixed keywords
2. **Audio Files**: Different pronunciation files for each language
3. **Content**: Some STD messages may have different content or may not exist in both languages

### Language-Specific Folders
- **ENGLISH/**: Contains all English audio files
- **HINDI/**: Contains all Hindi audio files

## Implementation Notes

### File Naming Conventions
- Train numbers: Individual digit files (0.wav, 1.wav, etc.)
- Platform numbers: Individual digit files
- Status messages: STD1.wav, STD2.wav, etc.
- Keywords: Language-specific prefixes (E/H)

### Error Handling
- Missing audio files are logged but don't stop the sequence
- System continues to next file if current file is not found
- Fallback mechanisms for missing train names or station names

### Performance Considerations
- Audio files are queued before playback begins
- Sequential playback ensures proper timing
- Support for repeat functionality
- Volume control (though limited with SoundPlayer)

## Example Complete Sequence

For Train 12345 "Rajdhani Express" from Delhi to Mumbai via Ahmedabad, Platform 3, Status "IS ARRIVING ON":

**English:**
1. `SPL/TADA.wav`
2. `ENGLISH/STD/STD1.wav`
3. `ENGLISH/ETRN.wav`
4. `ENGLISH/TRNO/1.wav`
5. `ENGLISH/TRNO/2.wav`
6. `ENGLISH/TRNO/3.wav`
7. `ENGLISH/TRNO/4.wav`
8. `ENGLISH/TRNO/5.wav`
9. `TRAIN TYPE/Rajdhani Express.wav`
10. `ENGLISH/EFROM.wav`
11. `ENGLISH/CITY/Delhi.wav`
12. `ENGLISH/ETO.wav`
13. `ENGLISH/CITY/Mumbai.wav`
14. `ENGLISH/EVIA.wav`
15. `ENGLISH/CITY/Ahmedabad.wav`
16. `ENGLISH/STD/STD5.wav`
17. `ENGLISH/PF/3.wav`

**Hindi:**
1. `SPL/TADA.wav`
2. `HINDI/STD/STD1.wav`
3. `HINDI/HTRN.wav`
4. `HINDI/TRNO/1.wav`
5. `HINDI/TRNO/2.wav`
6. `HINDI/TRNO/3.wav`
7. `HINDI/TRNO/4.wav`
8. `HINDI/TRNO/5.wav`
9. `TRAIN TYPE/Rajdhani Express.wav`
10. `HINDI/HFROM.wav`
11. `HINDI/CITY/Delhi.wav`
12. `HINDI/HTO.wav`
13. `HINDI/CITY/Mumbai.wav`
14. `HINDI/HVIA.wav`
15. `HINDI/CITY/Ahmedabad.wav`
16. `HINDI/STD/STD5.wav`
17. `HINDI/PF/3.wav`

This documentation provides a complete reference for understanding and implementing the audio announcement system in the IPIS application. 
