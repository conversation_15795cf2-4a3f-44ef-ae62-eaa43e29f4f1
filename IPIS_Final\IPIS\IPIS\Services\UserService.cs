using System;
using System.Collections.Generic;
using System.Data;
using IPIS.Repositories.Interfaces;
using IPIS.Utils;
using IPIS.Models;

namespace IPIS.Services
{
    public class UserService
    {
        private readonly IUserRepository _userRepository;

        public UserService(IUserRepository userRepository)
        {
            _userRepository = userRepository;
        }

        public void AddUser(string username, string password, string role)
        {
            var requestParams = new Dictionary<string, object>
            {
                ["username"] = username,
                ["password"] = "***MASKED***", // Always mask passwords in logs
                ["role"] = role
            };

            try
            {
                // Log request
                Logger.LogRequest(LogCategory.UserManagement, "AddUser", requestParams, "UserService.AddUser");

                _userRepository.AddUser(username, password, role);

                // Log successful response
                var responseData = new { username = username, role = role, status = "created" };
                Logger.LogResponse(LogCategory.UserManagement, "AddUser", true, responseData, null, "UserService.AddUser");
                Logger.LogDatabaseOperation("INSERT", "Users", true, $"User {username} added successfully");
            }
            catch (Exception ex)
            {
                // Log failed response
                Logger.LogResponse(LogCategory.UserManagement, "AddUser", false, null, ex.Message, "UserService.AddUser");
                Logger.LogDatabaseOperation("INSERT", "Users", false, $"Failed to add user {username}: {ex.Message}");
                throw;
            }
        }

        public void UpdateUser(long userId, string username, string password, string role)
        {
            try
            {
                // Get old values for logging
                var oldUser = GetUserById(userId);
                string oldValues = oldUser != null ?
                    $"Username: {oldUser.Username}, Role: {oldUser.Role}, IsActive: {oldUser.IsActive()}" :
                    "Previous values not available";

                string newValues = $"Username: {username}, Role: {role}";

                _userRepository.UpdateUser(userId, username, password, role);

                // Log successful user update
                Logger.LogUserUpdated(username, oldValues, newValues);
                Logger.LogDatabaseOperation("UPDATE", "Users", true, $"User {username} updated successfully");
            }
            catch (Exception ex)
            {
                // Log failed user update
                Logger.LogError(LogCategory.UserManagement, $"Failed to update user: {username}",
                               $"Error: {ex.Message}", "UserService.UpdateUser", ex);
                Logger.LogDatabaseOperation("UPDATE", "Users", false, $"Failed to update user {username}: {ex.Message}");
                throw;
            }
        }

        public void DeleteUser(long userId)
        {
            try
            {
                // Get user details before deletion for logging
                var userToDelete = GetUserById(userId);
                string username = userToDelete?.Username ?? "Unknown";
                string role = userToDelete?.Role ?? "Unknown";

                _userRepository.DeleteUser(userId);

                // Log successful user deletion
                Logger.LogUserDeleted(username, role);
                Logger.LogDatabaseOperation("DELETE", "Users", true, $"User {username} deleted successfully");
            }
            catch (Exception ex)
            {
                // Log failed user deletion
                Logger.LogError(LogCategory.UserManagement, $"Failed to delete user with ID: {userId}",
                               $"Error: {ex.Message}", "UserService.DeleteUser", ex);
                Logger.LogDatabaseOperation("DELETE", "Users", false, $"Failed to delete user with ID {userId}: {ex.Message}");
                throw;
            }
        }

        public DataTable GetAllUsers()
        {
            return _userRepository.GetAllUsers();
        }

        public bool ValidateUser(string username, string password)
        {
            return _userRepository.ValidateUser(username, password);
        }

        public List<string> GetUserPermissions(long userId)
        {
            return _userRepository.GetUserPermissions(userId);
        }

        public User AuthenticateUser(string username, string password)
        {
            return _userRepository.AuthenticateUser(username, password);
        }

        public User GetUserByUsername(string username)
        {
            return _userRepository.GetUserByUsername(username);
        }

        public User GetUserById(long userId)
        {
            return _userRepository.GetUserById(userId);
        }

        public void UpdateLastLogin(long userId)
        {
            _userRepository.UpdateLastLogin(userId);
        }

        public bool UserExists(string username)
        {
            return _userRepository.UserExists(username);
        }

        public bool IsLegacyUser(string username)
        {
            return _userRepository.IsLegacyUser(username);
        }

        public User GetLegacyUser(string username)
        {
            return _userRepository.GetLegacyUser(username);
        }
    }
}