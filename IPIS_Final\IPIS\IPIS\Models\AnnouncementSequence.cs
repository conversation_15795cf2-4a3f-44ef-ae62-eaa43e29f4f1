using System;
using System.Collections.Generic;

namespace IPIS.Models
{
    public class AnnouncementSequence
    {
        public int Id { get; set; }
        public int TemplateId { get; set; }
        public int LanguageId { get; set; }
        public string Name { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }

        // Navigation properties
        public virtual AnnouncementTemplate Template { get; set; }
        public virtual Language Language { get; set; }
        public virtual ICollection<SequenceItem> Items { get; set; }

        public AnnouncementSequence()
        {
            IsActive = true;
            CreatedAt = DateTime.Now;
            Items = new List<SequenceItem>();
        }

        public override string ToString()
        {
            return $"{Name} ({Language?.Name})";
        }
    }
} 