// Decompiled with JetBrains decompiler
// Type: ipis.frmStationCode
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using ipis.My;
using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

namespace ipis
{

[DesignerGenerated]
public class frmStationCode : Form
{
  private static List<WeakReference> __ENCList = new List<WeakReference>();
  private IContainer components;
  [AccessedThroughProperty("lblEnglish")]
  private Label _lblEnglish;
  [AccessedThroughProperty("lblHindi")]
  private Label _lblHindi;
  [AccessedThroughProperty("lblRegional")]
  private Label _lblRegional;
  [AccessedThroughProperty("txtEnglish")]
  private TextBox _txtEnglish;
  [AccessedThroughProperty("txtHindi")]
  private TextBox _txtHindi;
  [AccessedThroughProperty("txtRegional")]
  private TextBox _txtRegional;
  [AccessedThroughProperty("btnAdd")]
  private Button _btnAdd;
  [AccessedThroughProperty("btnExit")]
  private Button _btnExit;
  [AccessedThroughProperty("lblStationName")]
  private Label _lblStationName;
  [AccessedThroughProperty("lblStationCode")]
  private Label _lblStationCode;
  [AccessedThroughProperty("btnEdit")]
  private Button _btnEdit;
  [AccessedThroughProperty("btnDelete")]
  private Button _btnDelete;
  [AccessedThroughProperty("cmbStationCode")]
  private ComboBox _cmbStationCode;
  [AccessedThroughProperty("btnCancel")]
  private Button _btnCancel;
  [AccessedThroughProperty("btnSave")]
  private Button _btnSave;

  [DebuggerNonUserCode]
  static frmStationCode()
  {
  }

  [DebuggerNonUserCode]
  public frmStationCode()
  {
    this.Load += new EventHandler(this.frmStationCode_Load);
    frmStationCode.__ENCAddToList((object) this);
    this.InitializeComponent();
  }

  [DebuggerNonUserCode]
  private static void __ENCAddToList(object value)
  {
    lock (frmStationCode.__ENCList)
    {
      if (frmStationCode.__ENCList.Count == frmStationCode.__ENCList.Capacity)
      {
        int index1 = 0;
        int num = checked (frmStationCode.__ENCList.Count - 1);
        int index2 = 0;
        while (index2 <= num)
        {
          if (frmStationCode.__ENCList[index2].IsAlive)
          {
            if (index2 != index1)
              frmStationCode.__ENCList[index1] = frmStationCode.__ENCList[index2];
            checked { ++index1; }
          }
          checked { ++index2; }
        }
        frmStationCode.__ENCList.RemoveRange(index1, checked (frmStationCode.__ENCList.Count - index1));
        frmStationCode.__ENCList.Capacity = frmStationCode.__ENCList.Count;
      }
      frmStationCode.__ENCList.Add(new WeakReference(RuntimeHelpers.GetObjectValue(value)));
    }
  }

  [DebuggerNonUserCode]
  protected override void Dispose(bool disposing)
  {
    try
    {
      if (!disposing || this.components == null)
        return;
      this.components.Dispose();
    }
    finally
    {
      base.Dispose(disposing);
    }
  }

  [DebuggerStepThrough]
  private void InitializeComponent()
  {
    this.lblEnglish = new Label();
    this.lblHindi = new Label();
    this.lblRegional = new Label();
    this.txtEnglish = new TextBox();
    this.txtHindi = new TextBox();
    this.txtRegional = new TextBox();
    this.btnAdd = new Button();
    this.btnExit = new Button();
    this.lblStationName = new Label();
    this.lblStationCode = new Label();
    this.btnEdit = new Button();
    this.btnDelete = new Button();
    this.cmbStationCode = new ComboBox();
    this.btnCancel = new Button();
    this.btnSave = new Button();
    this.SuspendLayout();
    this.lblEnglish.AutoSize = true;
    this.lblEnglish.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblEnglish1 = this.lblEnglish;
    Point point1 = new Point(352, 57);
    Point point2 = point1;
    lblEnglish1.Location = point2;
    Label lblEnglish2 = this.lblEnglish;
    Padding padding1 = new Padding(4, 0, 4, 0);
    Padding padding2 = padding1;
    lblEnglish2.Margin = padding2;
    this.lblEnglish.Name = "lblEnglish";
    Label lblEnglish3 = this.lblEnglish;
    Size size1 = new Size(59, 16 /*0x10*/);
    Size size2 = size1;
    lblEnglish3.Size = size2;
    this.lblEnglish.TabIndex = 0;
    this.lblEnglish.Text = "English";
    this.lblHindi.AutoSize = true;
    this.lblHindi.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblHindi1 = this.lblHindi;
    point1 = new Point(364, 105);
    Point point3 = point1;
    lblHindi1.Location = point3;
    Label lblHindi2 = this.lblHindi;
    padding1 = new Padding(4, 0, 4, 0);
    Padding padding3 = padding1;
    lblHindi2.Margin = padding3;
    this.lblHindi.Name = "lblHindi";
    Label lblHindi3 = this.lblHindi;
    size1 = new Size(44, 16 /*0x10*/);
    Size size3 = size1;
    lblHindi3.Size = size3;
    this.lblHindi.TabIndex = 1;
    this.lblHindi.Text = "Hindi";
    this.lblRegional.AutoSize = true;
    this.lblRegional.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblRegional1 = this.lblRegional;
    point1 = new Point(343, 150);
    Point point4 = point1;
    lblRegional1.Location = point4;
    Label lblRegional2 = this.lblRegional;
    padding1 = new Padding(4, 0, 4, 0);
    Padding padding4 = padding1;
    lblRegional2.Margin = padding4;
    this.lblRegional.Name = "lblRegional";
    Label lblRegional3 = this.lblRegional;
    size1 = new Size(71, 16 /*0x10*/);
    Size size4 = size1;
    lblRegional3.Size = size4;
    this.lblRegional.TabIndex = 2;
    this.lblRegional.Text = "Regional";
    this.txtEnglish.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtEnglish1 = this.txtEnglish;
    point1 = new Point(428, 54);
    Point point5 = point1;
    txtEnglish1.Location = point5;
    TextBox txtEnglish2 = this.txtEnglish;
    padding1 = new Padding(4);
    Padding padding5 = padding1;
    txtEnglish2.Margin = padding5;
    this.txtEnglish.MaxLength = 18;
    this.txtEnglish.Name = "txtEnglish";
    TextBox txtEnglish3 = this.txtEnglish;
    size1 = new Size(169, 22);
    Size size5 = size1;
    txtEnglish3.Size = size5;
    this.txtEnglish.TabIndex = 3;
    this.txtHindi.Font = new Font("Microsoft Sans Serif", 14.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtHindi1 = this.txtHindi;
    point1 = new Point(428, 102);
    Point point6 = point1;
    txtHindi1.Location = point6;
    TextBox txtHindi2 = this.txtHindi;
    padding1 = new Padding(4);
    Padding padding6 = padding1;
    txtHindi2.Margin = padding6;
    this.txtHindi.MaxLength = 25;
    this.txtHindi.Name = "txtHindi";
    TextBox txtHindi3 = this.txtHindi;
    size1 = new Size(169, 29);
    Size size6 = size1;
    txtHindi3.Size = size6;
    this.txtHindi.TabIndex = 4;
    this.txtRegional.Font = new Font("Microsoft Sans Serif", 14.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtRegional1 = this.txtRegional;
    point1 = new Point(428, 150);
    Point point7 = point1;
    txtRegional1.Location = point7;
    TextBox txtRegional2 = this.txtRegional;
    padding1 = new Padding(4);
    Padding padding7 = padding1;
    txtRegional2.Margin = padding7;
    this.txtRegional.MaxLength = 25;
    this.txtRegional.Name = "txtRegional";
    TextBox txtRegional3 = this.txtRegional;
    size1 = new Size(169, 29);
    Size size7 = size1;
    txtRegional3.Size = size7;
    this.txtRegional.TabIndex = 5;
    this.btnAdd.BackColor = Color.SeaShell;
    this.btnAdd.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnAdd1 = this.btnAdd;
    point1 = new Point(37, 219);
    Point point8 = point1;
    btnAdd1.Location = point8;
    Button btnAdd2 = this.btnAdd;
    padding1 = new Padding(4);
    Padding padding8 = padding1;
    btnAdd2.Margin = padding8;
    this.btnAdd.Name = "btnAdd";
    Button btnAdd3 = this.btnAdd;
    size1 = new Size(65, 28);
    Size size8 = size1;
    btnAdd3.Size = size8;
    this.btnAdd.TabIndex = 6;
    this.btnAdd.Text = "Add";
    this.btnAdd.UseVisualStyleBackColor = false;
    this.btnExit.BackColor = Color.SeaShell;
    this.btnExit.DialogResult = DialogResult.Cancel;
    this.btnExit.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnExit1 = this.btnExit;
    point1 = new Point(508, 219);
    Point point9 = point1;
    btnExit1.Location = point9;
    Button btnExit2 = this.btnExit;
    padding1 = new Padding(4);
    Padding padding9 = padding1;
    btnExit2.Margin = padding9;
    this.btnExit.Name = "btnExit";
    Button btnExit3 = this.btnExit;
    size1 = new Size(63 /*0x3F*/, 28);
    Size size9 = size1;
    btnExit3.Size = size9;
    this.btnExit.TabIndex = 7;
    this.btnExit.Text = "Exit";
    this.btnExit.UseVisualStyleBackColor = false;
    this.lblStationName.AutoSize = true;
    this.lblStationName.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblStationName1 = this.lblStationName;
    point1 = new Point(443, 9);
    Point point10 = point1;
    lblStationName1.Location = point10;
    Label lblStationName2 = this.lblStationName;
    padding1 = new Padding(4, 0, 4, 0);
    Padding padding10 = padding1;
    lblStationName2.Margin = padding10;
    this.lblStationName.Name = "lblStationName";
    Label lblStationName3 = this.lblStationName;
    size1 = new Size(113, 16 /*0x10*/);
    Size size10 = size1;
    lblStationName3.Size = size10;
    this.lblStationName.TabIndex = 8;
    this.lblStationName.Text = " Station Names";
    this.lblStationCode.AutoSize = true;
    this.lblStationCode.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label lblStationCode1 = this.lblStationCode;
    point1 = new Point(43, 56);
    Point point11 = point1;
    lblStationCode1.Location = point11;
    Label lblStationCode2 = this.lblStationCode;
    padding1 = new Padding(4, 0, 4, 0);
    Padding padding11 = padding1;
    lblStationCode2.Margin = padding11;
    this.lblStationCode.Name = "lblStationCode";
    Label lblStationCode3 = this.lblStationCode;
    size1 = new Size(97, 16 /*0x10*/);
    Size size11 = size1;
    lblStationCode3.Size = size11;
    this.lblStationCode.TabIndex = 9;
    this.lblStationCode.Text = "Station Code";
    this.btnEdit.BackColor = Color.SeaShell;
    this.btnEdit.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnEdit1 = this.btnEdit;
    point1 = new Point(220, 219);
    Point point12 = point1;
    btnEdit1.Location = point12;
    Button btnEdit2 = this.btnEdit;
    padding1 = new Padding(4);
    Padding padding12 = padding1;
    btnEdit2.Margin = padding12;
    this.btnEdit.Name = "btnEdit";
    Button btnEdit3 = this.btnEdit;
    size1 = new Size(68, 28);
    Size size12 = size1;
    btnEdit3.Size = size12;
    this.btnEdit.TabIndex = 11;
    this.btnEdit.Text = "Edit";
    this.btnEdit.UseVisualStyleBackColor = false;
    this.btnDelete.BackColor = Color.SeaShell;
    this.btnDelete.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnDelete1 = this.btnDelete;
    point1 = new Point(315, 219);
    Point point13 = point1;
    btnDelete1.Location = point13;
    Button btnDelete2 = this.btnDelete;
    padding1 = new Padding(4);
    Padding padding13 = padding1;
    btnDelete2.Margin = padding13;
    this.btnDelete.Name = "btnDelete";
    Button btnDelete3 = this.btnDelete;
    size1 = new Size(72, 28);
    Size size13 = size1;
    btnDelete3.Size = size13;
    this.btnDelete.TabIndex = 12;
    this.btnDelete.Text = "Delete";
    this.btnDelete.UseVisualStyleBackColor = false;
    this.cmbStationCode.FormattingEnabled = true;
    ComboBox cmbStationCode1 = this.cmbStationCode;
    point1 = new Point(154, 52);
    Point point14 = point1;
    cmbStationCode1.Location = point14;
    ComboBox cmbStationCode2 = this.cmbStationCode;
    padding1 = new Padding(4);
    Padding padding14 = padding1;
    cmbStationCode2.Margin = padding14;
    this.cmbStationCode.Name = "cmbStationCode";
    ComboBox cmbStationCode3 = this.cmbStationCode;
    size1 = new Size(105, 24);
    Size size14 = size1;
    cmbStationCode3.Size = size14;
    this.cmbStationCode.TabIndex = 13;
    this.btnCancel.BackColor = Color.SeaShell;
    this.btnCancel.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnCancel1 = this.btnCancel;
    point1 = new Point(411, 219);
    Point point15 = point1;
    btnCancel1.Location = point15;
    Button btnCancel2 = this.btnCancel;
    padding1 = new Padding(4);
    Padding padding15 = padding1;
    btnCancel2.Margin = padding15;
    this.btnCancel.Name = "btnCancel";
    Button btnCancel3 = this.btnCancel;
    size1 = new Size(77, 28);
    Size size15 = size1;
    btnCancel3.Size = size15;
    this.btnCancel.TabIndex = 14;
    this.btnCancel.Text = "Cancel";
    this.btnCancel.UseVisualStyleBackColor = false;
    this.btnSave.BackColor = Color.SeaShell;
    this.btnSave.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnSave1 = this.btnSave;
    point1 = new Point(129, 219);
    Point point16 = point1;
    btnSave1.Location = point16;
    Button btnSave2 = this.btnSave;
    padding1 = new Padding(4);
    Padding padding16 = padding1;
    btnSave2.Margin = padding16;
    this.btnSave.Name = "btnSave";
    Button btnSave3 = this.btnSave;
    size1 = new Size(68, 28);
    Size size16 = size1;
    btnSave3.Size = size16;
    this.btnSave.TabIndex = 15;
    this.btnSave.Text = "Save";
    this.btnSave.UseVisualStyleBackColor = false;
    this.AcceptButton = (IButtonControl) this.btnAdd;
    this.AutoScaleDimensions = new SizeF(8f, 16f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.BackColor = Color.PowderBlue;
    this.CancelButton = (IButtonControl) this.btnExit;
    size1 = new Size(615, 267);
    this.ClientSize = size1;
    this.Controls.Add((Control) this.btnSave);
    this.Controls.Add((Control) this.btnCancel);
    this.Controls.Add((Control) this.cmbStationCode);
    this.Controls.Add((Control) this.btnDelete);
    this.Controls.Add((Control) this.btnEdit);
    this.Controls.Add((Control) this.lblStationCode);
    this.Controls.Add((Control) this.lblStationName);
    this.Controls.Add((Control) this.btnExit);
    this.Controls.Add((Control) this.btnAdd);
    this.Controls.Add((Control) this.txtRegional);
    this.Controls.Add((Control) this.txtHindi);
    this.Controls.Add((Control) this.txtEnglish);
    this.Controls.Add((Control) this.lblRegional);
    this.Controls.Add((Control) this.lblHindi);
    this.Controls.Add((Control) this.lblEnglish);
    this.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
    this.FormBorderStyle = FormBorderStyle.FixedToolWindow;
    padding1 = new Padding(4);
    this.Margin = padding1;
    this.Name = "frmStationCode";
    this.Text = "Station Code";
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  internal virtual Label lblEnglish
  {
    [DebuggerNonUserCode] get { return this._lblEnglish; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblEnglish = value;
    }
  }

  internal virtual Label lblHindi
  {
    [DebuggerNonUserCode] get { return this._lblHindi; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._lblHindi = value; }
  }

  internal virtual Label lblRegional
  {
    [DebuggerNonUserCode] get { return this._lblRegional; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblRegional = value;
    }
  }

  internal virtual TextBox txtEnglish
  {
    [DebuggerNonUserCode] get { return this._txtEnglish; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtEnglish = value;
    }
  }

  internal virtual TextBox txtHindi
  {
    [DebuggerNonUserCode] get { return this._txtHindi; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._txtHindi = value; }
  }

  internal virtual TextBox txtRegional
  {
    [DebuggerNonUserCode] get { return this._txtRegional; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtRegional = value;
    }
  }

  internal virtual Button btnAdd
  {
    [DebuggerNonUserCode] get { return this._btnAdd; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnAdd_Click);
      if (this._btnAdd != null)
        this._btnAdd.Click -= eventHandler;
      this._btnAdd = value;
      if (this._btnAdd == null)
        return;
      this._btnAdd.Click += eventHandler;
    }
  }

  internal virtual Button btnExit
  {
    [DebuggerNonUserCode] get { return this._btnExit; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnExit_Click);
      if (this._btnExit != null)
        this._btnExit.Click -= eventHandler;
      this._btnExit = value;
      if (this._btnExit == null)
        return;
      this._btnExit.Click += eventHandler;
    }
  }

  internal virtual Label lblStationName
  {
    [DebuggerNonUserCode] get { return this._lblStationName; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblStationName = value;
    }
  }

  internal virtual Label lblStationCode
  {
    [DebuggerNonUserCode] get { return this._lblStationCode; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblStationCode = value;
    }
  }

  internal virtual Button btnEdit
  {
    [DebuggerNonUserCode] get { return this._btnEdit; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnEdit_Click);
      if (this._btnEdit != null)
        this._btnEdit.Click -= eventHandler;
      this._btnEdit = value;
      if (this._btnEdit == null)
        return;
      this._btnEdit.Click += eventHandler;
    }
  }

  internal virtual Button btnDelete
  {
    [DebuggerNonUserCode] get { return this._btnDelete; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnDelete_Click);
      if (this._btnDelete != null)
        this._btnDelete.Click -= eventHandler;
      this._btnDelete = value;
      if (this._btnDelete == null)
        return;
      this._btnDelete.Click += eventHandler;
    }
  }

  internal virtual ComboBox cmbStationCode
  {
    [DebuggerNonUserCode] get { return this._cmbStationCode; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.cmbStationCode_SelectedIndexChanged);
      if (this._cmbStationCode != null)
        this._cmbStationCode.SelectedIndexChanged -= eventHandler;
      this._cmbStationCode = value;
      if (this._cmbStationCode == null)
        return;
      this._cmbStationCode.SelectedIndexChanged += eventHandler;
    }
  }

  internal virtual Button btnCancel
  {
    [DebuggerNonUserCode] get { return this._btnCancel; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btncancel_Click);
      if (this._btnCancel != null)
        this._btnCancel.Click -= eventHandler;
      this._btnCancel = value;
      if (this._btnCancel == null)
        return;
      this._btnCancel.Click += eventHandler;
    }
  }

  internal virtual Button btnSave
  {
    [DebuggerNonUserCode] get { return this._btnSave; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnSave_Click);
      if (this._btnSave != null)
        this._btnSave.Click -= eventHandler;
      this._btnSave = value;
      if (this._btnSave == null)
        return;
      this._btnSave.Click += eventHandler;
    }
  }

  private void btnAdd_Click(object sender, EventArgs e)
  {
    this.btnEdit.Enabled = false;
    this.btnDelete.Enabled = false;
    this.btnSave.Enabled = true;
    this.cmbStationCode.Enabled = true;
    this.txtEnglish.Enabled = true;
    this.txtHindi.Enabled = true;
    if (!frmMainFormIPIS.language_selection.regional_language_selected)
      this.txtRegional.Enabled = false;
    else
      this.txtRegional.Enabled = true;
    this.cmbStationCode.Text = string.Empty;
    this.txtEnglish.Text = string.Empty;
    this.txtHindi.Text = string.Empty;
    this.txtRegional.Text = string.Empty;
  }

  private void btnExit_Click(object sender, EventArgs e)
{
  this.Close();
}

  private void btnEdit_Click(object sender, EventArgs e)
  {
    this.txtEnglish.Text = string.Empty;
    this.txtHindi.Text = string.Empty;
    this.txtRegional.Text = string.Empty;
    this.btnDelete.Enabled = false;
    this.btnAdd.Enabled = false;
    this.btnSave.Enabled = true;
    this.cmbStationCode.Enabled = true;
    this.txtEnglish.Enabled = true;
    this.txtHindi.Enabled = true;
    if (!frmMainFormIPIS.language_selection.regional_language_selected)
      this.txtRegional.Enabled = false;
    else
      this.txtRegional.Enabled = true;
    this.cmbStationCode.Text = string.Empty;
  }

  private void frmStationCode_Load(object sender, EventArgs e)
  {
    this.get_sc_codes();
    this.cmbStationCode.Enabled = false;
    if (Operators.CompareString(frmMainFormIPIS.language_selection.regional_language_name, "Telugu", false) == 0)
    {
      this.txtRegional.Font = new Font("Gautami", 8.25f, FontStyle.Regular, GraphicsUnit.Point);
    }
    else
    {
      if (Operators.CompareString(frmMainFormIPIS.language_selection.regional_language_name, "Oriya", false) != 0)
        return;
      this.txtRegional.Font = new Font("Maan Normal Odia Akhayara", 8.25f, FontStyle.Regular, GraphicsUnit.Point);
    }
  }

  private void get_sc_codes()
  {
    string[] strArray1 = new string[601];
    string[] strArray2 = new string[601];
    int num = 0;
    int index = 0;
    string str = Conversions.ToString(num);
    network_db_read.get_Station_codes(ref strArray1, ref strArray2, ref str);
    int integer = Conversions.ToInteger(str);
    this.cmbStationCode.Items.Clear();
    while (index < integer)
    {
      this.cmbStationCode.Items.Add((object) strArray1[index]);
      checked { ++index; }
    }
  }

  private void btnDelete_Click(object sender, EventArgs e)
  {
    this.btnEdit.Enabled = false;
    this.btnAdd.Enabled = false;
    this.cmbStationCode.Enabled = true;
    this.btnSave.Enabled = false;
    this.txtEnglish.Enabled = false;
    this.txtHindi.Enabled = false;
    this.txtRegional.Enabled = false;
    this.cmbStationCode.Text = string.Empty;
    this.txtEnglish.Text = string.Empty;
    this.txtHindi.Text = string.Empty;
    this.txtRegional.Text = string.Empty;
  }

  private void cmbStationCode_SelectedIndexChanged(object sender, EventArgs e)
  {
    string empty1 = string.Empty;
    string empty2 = string.Empty;
    string empty3 = string.Empty;
    if (!frmMainFormIPIS.language_selection.regional_language_selected)
    {
      this.txtRegional.Enabled = false;
    }
    else
    {
      this.txtRegional.Enabled = true;
      if (frmMainFormIPIS.language_selection.regional_language_selected)
      {
        if (Operators.CompareString(frmMainFormIPIS.language_selection.regional_language_name, "Telugu", false) == 0)
          this.txtRegional.Font = new Font("Gautami", 8.25f, FontStyle.Regular, GraphicsUnit.Point);
        else if (Operators.CompareString(frmMainFormIPIS.language_selection.regional_language_name, "Oriya", false) == 0)
          this.txtRegional.Font = new Font("Maan Normal Odia Akhayara", 8.25f, FontStyle.Regular, GraphicsUnit.Point);
      }
    }
    network_db_read.get_Station_code(this.cmbStationCode.Text, ref empty1, ref empty2, ref empty3);
    this.txtEnglish.Text = empty1;
    this.txtHindi.Text = empty2;
    this.txtRegional.Text = empty3;
    if (!this.btnDelete.Enabled)
      return;
    if (Interaction.MsgBox((object) ("Delete station code " + this.cmbStationCode.Text), MsgBoxStyle.YesNo) == MsgBoxResult.Yes)
    {
      if (network_db_read.Delete_StationCode(this.cmbStationCode.Text))
      {
        int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Station Code{this.cmbStationCode.Text} Deleted", "Msg Box", 0, 0, 0);
        this.get_sc_codes();
        try
        {
          string str = "Z:\\Database\\StationCode_db.mdb";
          string sourceFileName = "C:\\IPIS\\Database\\StationCode_db.mdb";
          if (!File.Exists(str))
            File.Create(str);
          bool overwrite = true;
          MyProject.Computer.FileSystem.CopyFile(sourceFileName, str, overwrite);
        }
        catch (Exception ex)
        {
          ProjectData.SetProjectError(ex);
          ProjectData.ClearProjectError();
        }
      }
      else
      {
        int num1 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Station Code{this.cmbStationCode.Text} not Deleted", "Msg Box", 0, 0, 0);
      }
    }
    this.btnEdit.Enabled = true;
    this.btnAdd.Enabled = true;
    this.btnDelete.Enabled = true;
    this.btnSave.Enabled = false;
    this.cmbStationCode.Enabled = false;
    this.cmbStationCode.Text = string.Empty;
    this.txtEnglish.Text = string.Empty;
    this.txtHindi.Text = string.Empty;
    this.txtRegional.Text = string.Empty;
  }

  private void btncancel_Click(object sender, EventArgs e)
  {
    this.btnEdit.Enabled = true;
    this.btnAdd.Enabled = true;
    this.btnDelete.Enabled = true;
    this.btnSave.Enabled = false;
    this.cmbStationCode.Enabled = false;
    this.txtEnglish.Enabled = false;
    this.txtHindi.Enabled = false;
    this.txtRegional.Enabled = false;
    this.cmbStationCode.Text = string.Empty;
    this.txtEnglish.Text = string.Empty;
    this.txtHindi.Text = string.Empty;
    this.txtRegional.Text = string.Empty;
  }

  private void btnSave_Click(object sender, EventArgs e)
  {
    if (this.btnEdit.Enabled)
    {
      if (network_db_read.edit_StationCode(this.cmbStationCode.Text, this.txtEnglish.Text, this.txtHindi.Text, this.txtRegional.Text))
      {
        int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Station Names are Modified", "Msg Box", 0, 0, 0);
        this.txtEnglish.Text = string.Empty;
        this.txtRegional.Text = string.Empty;
        this.txtHindi.Text = string.Empty;
        this.cmbStationCode.Text = string.Empty;
      }
      else
      {
        int num1 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Station Names are not Modified", "Msg Box", 0, 0, 0);
      }
    }
    else if (this.btnAdd.Enabled)
    {
      if (network_db_read.set_StationCode(this.cmbStationCode.Text, this.txtEnglish.Text, this.txtHindi.Text, this.txtRegional.Text))
      {
        int num2 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Station name{this.cmbStationCode.Text} is added", "Msg Box", 0, 0, 0);
        this.txtEnglish.Text = string.Empty;
        this.txtRegional.Text = string.Empty;
        this.txtHindi.Text = string.Empty;
        this.cmbStationCode.Text = string.Empty;
      }
      else
      {
        int num3 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Station name {this.cmbStationCode.Text} & is not added\r\n Station name might already there", "Msg Box", 0, 0, 0);
      }
    }
    this.get_sc_codes();
    this.btnEdit.Enabled = true;
    this.btnAdd.Enabled = true;
    this.btnDelete.Enabled = true;
    this.btnSave.Enabled = false;
    this.cmbStationCode.Enabled = false;
    try
    {
      string str = "Z:\\Database\\StationCode_db.mdb";
      string sourceFileName = "C:\\IPIS\\Database\\StationCode_db.mdb";
      if (!File.Exists(str))
        File.Create(str);
      bool overwrite = true;
      MyProject.Computer.FileSystem.CopyFile(sourceFileName, str, overwrite);
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
  }
}

}