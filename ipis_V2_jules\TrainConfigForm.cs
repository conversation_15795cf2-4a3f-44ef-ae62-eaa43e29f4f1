using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using ipis_V2_jules.Data; // For DatabaseHelper
using ipis_V2_jules.ApiClients; // For IndianRailApiClient
using System.Text.Json; // For JsonSerializer

namespace ipis_V2_jules
{
    public partial class TrainConfigForm : Form
    {
        private DataGridView dgvTrainSchedules;
        private TextBox txtTrainNo;
        private TextBox txtTrainName;
        private TextBox txtScheduledArrivalTime;
        private TextBox txtScheduledDepartureTime;
        private TextBox txtDaysOfOperation;
        private ComboBox cmbSourceStation;
        private ComboBox cmbDestinationStation;
        private Button btnAddTrain;
        private Button btnSaveTrain;
        private Button btnLoadTrains;
        private Label lblStatus;

        // Labels for TextBoxes and ComboBoxes
        private Label lblTrainNo;
        private Label lblTrainName;
        private Label lblScheduledArrivalTime;
        private Label lblScheduledDepartureTime;
        private Label lblDaysOfOperation;
        private Label lblSourceStation;
        private Label lblDestinationStation;

        // New UI Elements
        private Button btnFetchLiveStatus;
        private RichTextBox rtbLiveStatus;
        private Button btnOpenPnrForm; // Button to open PNR Status Form

        private DatabaseHelper _dbHelper;
        // private IndianRailApiClient _apiClient_old; // Old client
        private ErailApiClient _apiClient; // New client

        public TrainConfigForm()
        {
            InitializeComponent(); // This will be defined in TrainConfigForm.Designer.cs
            _dbHelper = new DatabaseHelper("ipis_v2.sqlite"); // Ensure DB name matches

            _apiClient = new ErailApiClient(); // Instantiate the new client

            // Ensure database and schema are ready
            try
            {
                _dbHelper.InitializeDatabase();
            }
            catch (Exception ex)
            {
                lblStatus.Text = $"Database initialization error: {ex.Message}";
                // Disable controls if DB is not ready
                btnAddTrain.Enabled = false;
                btnSaveTrain.Enabled = false;
                btnLoadTrains.Enabled = false;
                dgvTrainSchedules.Enabled = false;
                cmbSourceStation.Enabled = false;
                cmbDestinationStation.Enabled = false;
                if (btnFetchLiveStatus != null) btnFetchLiveStatus.Enabled = false;
            }
        }

        private void TrainConfigForm_Load(object sender, EventArgs e)
        {
            PopulateStationComboBoxes();
            LoadTrainSchedules();
        }

        private void PopulateStationComboBoxes()
        {
            try
            {
                string query = "SELECT StationID, StationName FROM Stations ORDER BY StationName";
                var stations = _dbHelper.ExecuteQuery(query);

                if (stations != null)
                {
                    var stationList = stations.Select(s => new StationDropDownItem {
                        StationID = Convert.ToInt32(s["StationID"]),
                        StationName = s["StationName"]?.ToString()
                    }).ToList();

                    // For cmbSourceStation
                    cmbSourceStation.DataSource = new List<StationDropDownItem>(stationList); // Create a new list for each
                    cmbSourceStation.DisplayMember = "StationName";
                    cmbSourceStation.ValueMember = "StationID";
                    cmbSourceStation.SelectedIndex = -1; // No default selection

                    // For cmbDestinationStation
                    cmbDestinationStation.DataSource = new List<StationDropDownItem>(stationList); // Create a new list for each
                    cmbDestinationStation.DisplayMember = "StationName";
                    cmbDestinationStation.ValueMember = "StationID";
                    cmbDestinationStation.SelectedIndex = -1; // No default selection
                }
            }
            catch (Exception ex)
            {
                lblStatus.ForeColor = Color.Red;
                lblStatus.Text = $"Error populating station ComboBoxes: {ex.Message}";
            }
        }

        private void LoadTrainSchedules()
        {
            try
            {
                dgvTrainSchedules.Rows.Clear(); // Clear existing rows

                // Query to join Trains with Stations for Source and Destination Station Codes
                string query = @"
                    SELECT
                        t.TrainID, t.TrainNo, t.TrainName,
                        s_src.StationCode AS SourceStationCode,
                        s_dest.StationCode AS DestinationStationCode,
                        t.ScheduledArrivalTimeAtStation, t.ScheduledDepartureTimeAtStation, t.DaysOfOperation,
                        t.APISource, t.LastUpdated
                    FROM Trains t
                    LEFT JOIN Stations s_src ON t.SourceStationID = s_src.StationID
                    LEFT JOIN Stations s_dest ON t.DestinationStationID = s_dest.StationID";

                var trains = _dbHelper.ExecuteQuery(query);

                if (trains != null)
                {
                    foreach (var train in trains)
                    {
                        dgvTrainSchedules.Rows.Add(
                            train["TrainID"], // Keep TrainID for potential future use (e.g., updates)
                            train["TrainNo"],
                            train["TrainName"],
                            train["SourceStationCode"],
                            train["DestinationStationCode"],
                            train["ScheduledArrivalTimeAtStation"],
                            train["ScheduledDepartureTimeAtStation"],
                            train["DaysOfOperation"]
                            // train["APISource"], // Not displayed in DGV as per requirements
                            // train["LastUpdated"] // Not displayed in DGV as per requirements
                        );
                    }
                }
                lblStatus.Text = $"Loaded {trains?.Count ?? 0} train schedules.";
            }
            catch (Exception ex)
            {
                lblStatus.ForeColor = Color.Red;
                lblStatus.Text = $"Error loading train schedules: {ex.Message}";
            }
        }

        private void btnLoadTrains_Click(object sender, EventArgs e)
        {
            LoadTrainSchedules();
        }

        private void btnAddTrain_Click(object sender, EventArgs e)
        {
            string trainNo = txtTrainNo.Text.Trim();
            string trainName = txtTrainName.Text.Trim();
            string arrivalTime = txtScheduledArrivalTime.Text.Trim(); // Should be HH:MM
            string departureTime = txtScheduledDepartureTime.Text.Trim(); // Should be HH:MM
            string daysOfOperation = txtDaysOfOperation.Text.Trim();

            if (string.IsNullOrEmpty(trainNo) || string.IsNullOrEmpty(trainName))
            {
                lblStatus.ForeColor = Color.Red;
                lblStatus.Text = "Train No and Train Name are required.";
                return;
            }

            if (cmbSourceStation.SelectedValue == null || cmbDestinationStation.SelectedValue == null)
            {
                lblStatus.ForeColor = Color.Red;
                lblStatus.Text = "Source and Destination stations must be selected.";
                return;
            }

            int sourceStationID = Convert.ToInt32(cmbSourceStation.SelectedValue);
            int destinationStationID = Convert.ToInt32(cmbDestinationStation.SelectedValue);

            if (sourceStationID == destinationStationID)
            {
                lblStatus.ForeColor = Color.Red;
                lblStatus.Text = "Source and Destination stations cannot be the same.";
                return;
            }

            try
            {
                // Simplified INSERT for now
                string query = @"
                    INSERT INTO Trains
                        (TrainNo, TrainName, SourceStationID, DestinationStationID,
                         ScheduledArrivalTimeAtStation, ScheduledDepartureTimeAtStation, DaysOfOperation,
                         APISource, LastUpdated)
                    VALUES
                        (@TrainNo, @TrainName, @SourceStationID, @DestinationStationID,
                         @ScheduledArrivalTimeAtStation, @ScheduledDepartureTimeAtStation, @DaysOfOperation,
                         @APISource, @LastUpdated)";

                var parameters = new Dictionary<string, object>
                {
                    { "@TrainNo", trainNo },
                    { "@TrainName", trainName },
                    { "@SourceStationID", sourceStationID },
                    { "@DestinationStationID", destinationStationID },
                    { "@ScheduledArrivalTimeAtStation", string.IsNullOrEmpty(arrivalTime) ? (object)DBNull.Value : arrivalTime },
                    { "@ScheduledDepartureTimeAtStation", string.IsNullOrEmpty(departureTime) ? (object)DBNull.Value : departureTime },
                    { "@DaysOfOperation", string.IsNullOrEmpty(daysOfOperation) ? (object)DBNull.Value : daysOfOperation },
                    { "@APISource", "Manual_Entry" },
                    { "@LastUpdated", DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss") }
                    // Other fields like TrainNameHindi, etc., are omitted as per instruction
                };

                int rowsAffected = _dbHelper.ExecuteNonQuery(query, parameters);

                if (rowsAffected > 0)
                {
                    lblStatus.ForeColor = Color.Green;
                    lblStatus.Text = "Train schedule added successfully!";
                    LoadTrainSchedules(); // Refresh grid
                    ClearInputFields();
                }
                else
                {
                    lblStatus.ForeColor = Color.Red;
                    lblStatus.Text = "Failed to add train schedule. No rows affected.";
                }
            }
            catch (Exception ex)
            {
                lblStatus.ForeColor = Color.Red;
                if (ex.Message.Contains("UNIQUE constraint failed: Trains.TrainNo"))
                {
                    lblStatus.Text = $"Error: Train No '{trainNo}' already exists.";
                }
                else
                {
                    lblStatus.Text = $"Error adding train schedule: {ex.Message}";
                }
            }
        }

        private void btnSaveTrain_Click(object sender, EventArgs e)
        {
            // Placeholder for update logic.
            // This would typically involve:
            // 1. Identifying the selected train (e.g., from dgvTrainSchedules using TrainID).
            // 2. Retrieving current values from TextBoxes and ComboBoxes.
            // 3. Constructing an UPDATE SQL query.
            // 4. Using _dbHelper.ExecuteNonQuery().
            // 5. Refreshing the grid and status.
            lblStatus.Text = "Save/Update functionality not yet implemented.";
        }

        private void ClearInputFields()
        {
            txtTrainNo.Clear();
            txtTrainName.Clear();
            txtScheduledArrivalTime.Clear();
            txtScheduledDepartureTime.Clear();
            txtDaysOfOperation.Clear();
            cmbSourceStation.SelectedIndex = -1;
            cmbDestinationStation.SelectedIndex = -1;
        }

        // Helper class for ComboBox items to store both display name and value
        private class StationDropDownItem
        {
            public int StationID { get; set; }
            public string StationName { get; set; }
        }

        private async void btnFetchLiveStatus_Click(object sender, EventArgs e)
        {
            if (dgvTrainSchedules.SelectedRows.Count == 0)
            {
                rtbLiveStatus.Text = "Please select a train schedule from the grid to fetch its live status.";
                return;
            }

            DataGridViewRow selectedRow = dgvTrainSchedules.SelectedRows[0];
            string trainNo = selectedRow.Cells["colTrainNo"].Value?.ToString();

            if (string.IsNullOrWhiteSpace(trainNo))
            {
                rtbLiveStatus.Text = "Selected train schedule does not have a valid Train No.";
                return;
            }

            // Date is not needed for GetTrainDetailsAsync
            // string dateString = DateTime.Now.ToString("yyyyMMdd");

            rtbLiveStatus.Text = $"Fetching train details for Train No: {trainNo}...";
            Application.DoEvents(); // Force UI update

            try
            {
                TrainDataErail trainDetails = await _apiClient.GetTrainDetailsAsync(trainNo);

                if (trainDetails != null)
                {
                    rtbLiveStatus.Clear();
                    rtbLiveStatus.AppendText($"Train No: {trainDetails.TrainNo}\n");
                    rtbLiveStatus.AppendText($"Train Name: {trainDetails.TrainName}\n");
                    rtbLiveStatus.AppendText($"Source: {trainDetails.SourceStationName} ({trainDetails.SourceStationCode})\n");
                    rtbLiveStatus.AppendText($"Destination: {trainDetails.DestinationStationName} ({trainDetails.DestinationStationCode})\n");
                    rtbLiveStatus.AppendText($"Start Time: {trainDetails.StartTime}\n");
                    rtbLiveStatus.AppendText($"End Time: {trainDetails.EndTime}\n");
                    rtbLiveStatus.AppendText($"Travel Time: {trainDetails.TravelTime}\n");
                    rtbLiveStatus.AppendText($"Running Days: {trainDetails.RunningDaysString}\n");
                    if (!string.IsNullOrEmpty(trainDetails.TrainType))
                    {
                        rtbLiveStatus.AppendText($"Train Type: {trainDetails.TrainType}\n");
                    }
                    if (!string.IsNullOrEmpty(trainDetails.PantryCar))
                    {
                         rtbLiveStatus.AppendText($"Pantry Car: {trainDetails.PantryCar}\n");
                    }
                }
                else
                {
                    rtbLiveStatus.Text = $"Failed to fetch train details for Train No: {trainNo}.\nTrain not found or API error. Check console for details.";
                }
            }
            catch (Exception ex)
            {
                rtbLiveStatus.Text = $"An error occurred while fetching train details: {ex.Message}\n\nCheck console output for more details.";
                Console.WriteLine($"Exception in btnFetchLiveStatus_Click: {ex}"); // Log the full exception
            }
        }

        private void btnOpenPnrForm_Click(object sender, EventArgs e)
        {
            PnrStatusForm pnrForm = new PnrStatusForm();
            pnrForm.ShowDialog(); // Show as a modal dialog, or use .Show() for non-modal
        }
    }
}
