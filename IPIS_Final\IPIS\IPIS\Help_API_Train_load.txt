baseUrl = http://localhost:3000/

// get running trains based on station code
{{baseUrl}}trains/stationLive?code=PRYJ
{
    "success": true,
    "time_stamp": 1752204193271,
    "data": [
        {
            "train_no": "63237",
            "train_name": "DDU SFG MEMU",
            "source_stn_name": "Dd Upadhyaya Jn",
            "dstn_stn_name": "Subedarganj",
            "time_at": "09:10",
            "detail": ""
        },
        {
            "train_no": "15483",
            "train_name": "SIKKIMMAHANANDA",
            "source_stn_name": "Alipur Duar Jn",
            "dstn_stn_name": "Delhi",
            "time_at": "09:25",
            "detail": ""
        }
    ]
}

// get train details by train number
{{baseUrl}}trains/getTrain?trainNo=63237
{
    "success": true,
    "time_stamp": 1752204229221,
    "data": {
        "train_no": "63237",
        "train_name": "DDU SFG MEMU",
        "from_stn_code": "DDU",
        "to_stn_code": "SFG",
        "running_days": "1111111",
        "type": "ORDINARY",


        "from_stn_name": "Dd Upadhyaya Jn",
        "to_stn_name": "Subedarganj",
        "from_time": "04.00",
        "to_time": "09.30",
        "travel_time": "05.30",
        "train_id": "6984",
        "distance_from_to": "157",
        "average_speed": "29"
    }
}

// get train route by train number
{{baseUrl}}trains/getRoute?trainNo=63237
{
    "success": true,
    "time_stamp": 1752204364120,
    "data": [
        {
            "source_stn_name": "Dd Upadhyaya Jn",
            "source_stn_code": "DDU",
            "arrive": "First",
            "depart": "04.00",
            "distance": "0",
            "day": "1",
            "zone": "DDU"
        },
        {
            "source_stn_name": "Jeonathpur",
            "source_stn_code": "JEP",
            "arrive": "04.22",
            "depart": "04.23",
            "distance": "8",
            "day": "1",
            "zone": "PRYJ"
        }
        {
            "source_stn_name": "Prayagraj Jn",
            "source_stn_code": "PRYJ",
            "arrive": "09.00",
            "depart": "09.10",
            "distance": "153",
            "day": "1",
            "zone": "PRYJ"
        },
        {
            "source_stn_name": "Subedarganj",
            "source_stn_code": "SFG",
            "arrive": "09.30",
            "depart": "Last",
            "distance": "157",
            "day": "1",
            "zone": "PRYJ"
        }
    ]
}

Train_Data
(
	"Train_No"	TEXT,
	"Train_NameEng"	TEXT,
	"Src_Stn"	TEXT,
	"Desti_Stn"	TEXT,
	"Via1"	TEXT,
	"Via2"	TEXT,
	"Via3"	TEXT,
	"Via4"	TEXT,
	"Train_AD"	TEXT,
	"Sch_AT"	TEXT,
	"Sch_DT"	TEXT,
	"Sch_PF"	TEXT,
	"All_Days"	INTEGER,
	"Chk_Mon"	INTEGER,
	"Chk_Tue"	INTEGER,
	"Chk_Wed"	INTEGER,
	"Chk_Thu"	INTEGER,
	"Chk_Fri"	INTEGER,
	"Chk_Sat"	INTEGER,
	"Chk_Sun"	INTEGER,
	"Merged_Train"	TEXT,
	"Train_Type"	TEXT,
	PRIMARY KEY("Train_No")
)

Train_Types 
 (
    ID VARCHAR(50) PRIMARY KEY,
    Name VARCHAR(50) NOT NULL UNIQUE,
    Description VARCHAR(200),
    IsActive BOOLEAN DEFAULT TRUE,
    CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
    ModifiedDate DATETIME
)

Station_Details
 (
	"ID"	INTEGER,
	"Station_Name"	TEXT,
	"Station_Code"	TEXT UNIQUE,
	"Auto_Load"	BOOLEAN,
	"AutoLoad_Interval"	INTEGER,
	"AutoDelete_Interval"	INTEGER,
	"AutoDeletePost_Interval"	INTEGER,
	"Auto_Delete"	BOOLEAN,
	"Avilable_PF"	INTEGER,
	"P1"	TEXT,
	"P2"	TEXT,
	"P3"	TEXT,
	"P4"	TEXT,
	"P5"	TEXT,
	"P6"	TEXT,
	"P7"	TEXT,
	"P8"	TEXT,
	"P9"	TEXT,
	"P10"	TEXT,
	"Lang1_Enb"	BOOLEAN,
	"Lang2_Enb"	BOOLEAN,
	"First_Lang"	TEXT,
	"Second_Lang"	TEXT,
	"English"	BOOLEAN,
	"Hindi"	BOOLEAN,
	"EngWave_File"	TEXT,
	"HindiWave_File"	TEXT, 
    Is_Current BOOLEAN DEFAULT FALSE,
	PRIMARY KEY("ID" AUTOINCREMENT)
)

create a data load from API functionality.
you can find API details above.
in main menu > Trains Station add another form Load Data.
in this new form:
there is a functionality to load data from API.
when click on load data. this will start getting data from APIs and store them in DB.
you can find DB tables structure as well.

this form will show current station Detail/summary on the form. So that user can easily identify that data is loading for which station.
and user can see the logs and process progrees from start to end below that button.

first get current station code from Station_Details (Is_Current)
if not data found or no flag is true then use PRYJ by default.

from this API you will get list of trains associate with this station.

go through these train numbers.
for each train number get train details by train number
and get train route by train number using from_stn_code and to_stn_code
now we can save: Train number, name, source and destination station, running days and type from getTrain API
if type is not available in Train_Types table then add that type first.
if source or destination station is not in the Station_Details then add that as well.
save Station code and station name from getRoute to Station_Details

and save language specific wave files as well:
loop through languages configured and save this data as well.
WAVE\{language code}\CITY\{source_stn_code}.wav