// Decompiled with JetBrains decompiler
// Type: ipis.frmCfgInt
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

namespace ipis
{

[DesignerGenerated]
public class frmCfgInt : Form
{
  private static List<WeakReference> __ENCList = new List<WeakReference>();
  private IContainer components;
  [AccessedThroughProperty("lblCfgInt")]
  private Label _lblCfgInt;

  [DebuggerNonUserCode]
  static frmCfgInt()
  {
  }

  [DebuggerNonUserCode]
  public frmCfgInt()
  {
    frmCfgInt.__ENCAddToList((object) this);
    this.InitializeComponent();
  }

  [DebuggerNonUserCode]
  private static void __ENCAddToList(object value)
  {
    lock (frmCfgInt.__ENCList)
    {
      if (frmCfgInt.__ENCList.Count == frmCfgInt.__ENCList.Capacity)
      {
        int index1 = 0;
        int num = checked (frmCfgInt.__ENCList.Count - 1);
        int index2 = 0;
        while (index2 <= num)
        {
          if (frmCfgInt.__ENCList[index2].IsAlive)
          {
            if (index2 != index1)
              frmCfgInt.__ENCList[index1] = frmCfgInt.__ENCList[index2];
            checked { ++index1; }
          }
          checked { ++index2; }
        }
        frmCfgInt.__ENCList.RemoveRange(index1, checked (frmCfgInt.__ENCList.Count - index1));
        frmCfgInt.__ENCList.Capacity = frmCfgInt.__ENCList.Count;
      }
      frmCfgInt.__ENCList.Add(new WeakReference(RuntimeHelpers.GetObjectValue(value)));
    }
  }

  [DebuggerNonUserCode]
  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  [DebuggerStepThrough]
  private void InitializeComponent()
  {
    this.lblCfgInt = new Label();
    this.SuspendLayout();
    this.lblCfgInt.AutoSize = true;
    this.lblCfgInt.Font = new Font("Microsoft Sans Serif", 12f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.lblCfgInt.ForeColor = SystemColors.WindowText;
    this.lblCfgInt.Location = new Point(2, 20);
    this.lblCfgInt.Name = "lblCfgInt";
    Label lblCfgInt = this.lblCfgInt;
    Size size1 = new Size(342, 20);
    Size size2 = size1;
    lblCfgInt.Size = size2;
    this.lblCfgInt.TabIndex = 0;
    this.lblCfgInt.Text = "Configuring Intensity to all displaboard ....\r\n";
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    size1 = new Size(376, 67);
    this.ClientSize = size1;
    this.Controls.Add((Control) this.lblCfgInt);
    this.FormBorderStyle = FormBorderStyle.SizableToolWindow;
    this.Name = "frmCfgInt";
    this.RightToLeftLayout = true;
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  internal virtual Label lblCfgInt
  {
    [DebuggerNonUserCode] get { return this._lblCfgInt; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblCfgInt = value;
    }
  }
}

}