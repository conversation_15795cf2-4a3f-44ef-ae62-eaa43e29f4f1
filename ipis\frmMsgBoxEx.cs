// Decompiled with JetBrains decompiler
// Type: ipis.frmMsgBoxEx
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Resources;
using System.Runtime.CompilerServices;
using System.Windows.Forms;

namespace ipis
{

[DesignerGenerated]
internal class frmMsgBoxEx : Form
{
  private static List<WeakReference> __ENCList = new List<WeakReference>();
  private IContainer components;
  public ToolTip ToolTip1;
  [AccessedThroughProperty("cmdMsgBoxPause")]
  private Button _cmdMsgBoxPause;
  [AccessedThroughProperty("cmdMsgBoxMove")]
  private Button _cmdMsgBoxMove;
  private object mResult;

  [DebuggerNonUserCode]
  static frmMsgBoxEx()
  {
  }

  [DebuggerNonUserCode]
  private static void __ENCAddToList(object value)
  {
    lock (frmMsgBoxEx.__ENCList)
    {
      if (frmMsgBoxEx.__ENCList.Count == frmMsgBoxEx.__ENCList.Capacity)
      {
        int index1 = 0;
        int num = checked (frmMsgBoxEx.__ENCList.Count - 1);
        int index2 = 0;
        while (index2 <= num)
        {
          if (frmMsgBoxEx.__ENCList[index2].IsAlive)
          {
            if (index2 != index1)
              frmMsgBoxEx.__ENCList[index1] = frmMsgBoxEx.__ENCList[index2];
            checked { ++index1; }
          }
          checked { ++index2; }
        }
        frmMsgBoxEx.__ENCList.RemoveRange(index1, checked (frmMsgBoxEx.__ENCList.Count - index1));
        frmMsgBoxEx.__ENCList.Capacity = frmMsgBoxEx.__ENCList.Count;
      }
      frmMsgBoxEx.__ENCList.Add(new WeakReference(RuntimeHelpers.GetObjectValue(value)));
    }
  }

  [DebuggerNonUserCode]
  public frmMsgBoxEx()
  {
    frmMsgBoxEx.__ENCAddToList((object) this);
    this.InitializeComponent();
  }

  [DebuggerNonUserCode]
  protected override void Dispose(bool Disposing)
  {
    if (Disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(Disposing);
  }

  public virtual Button cmdMsgBoxPause
  {
    [DebuggerNonUserCode] get { return this._cmdMsgBoxPause; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.CmdMsgBoxPause_Click);
      if (this._cmdMsgBoxPause != null)
        this._cmdMsgBoxPause.Click -= eventHandler;
      this._cmdMsgBoxPause = value;
      if (this._cmdMsgBoxPause == null)
        return;
      this._cmdMsgBoxPause.Click += eventHandler;
    }
  }

  public virtual Button cmdMsgBoxMove
  {
    [DebuggerNonUserCode] get { return this._cmdMsgBoxMove; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.CmdMsgBoxMove_Click);
      if (this._cmdMsgBoxMove != null)
        this._cmdMsgBoxMove.Click -= eventHandler;
      this._cmdMsgBoxMove = value;
      if (this._cmdMsgBoxMove == null)
        return;
      this._cmdMsgBoxMove.Click += eventHandler;
    }
  }

  [DebuggerStepThrough]
  private void InitializeComponent()
  {
    ResourceManager resourceManager = new ResourceManager(typeof (frmMsgBoxEx));
    this.components = (IContainer) new System.ComponentModel.Container();
    this.ToolTip1 = new ToolTip(this.components);
    this.cmdMsgBoxPause = new Button();
    this.cmdMsgBoxMove = new Button();
    this.SuspendLayout();
    this.ToolTip1.Active = true;
    this.FormBorderStyle = FormBorderStyle.FixedSingle;
    this.Text = "MsgBox Function Extended";
    Size size1 = new Size(269, 92);
    this.ClientSize = size1;
    Point point1 = new Point(49, 49);
    this.Location = point1;
    this.MaximizeBox = false;
    this.MinimizeBox = false;
    this.StartPosition = FormStartPosition.CenterScreen;
    this.Font = new Font("Arial", 8f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.BackColor = SystemColors.Control;
    this.ControlBox = true;
    this.Enabled = true;
    this.KeyPreview = false;
    this.Cursor = Cursors.Default;
    this.RightToLeft = RightToLeft.No;
    this.ShowInTaskbar = true;
    this.HelpButton = false;
    this.WindowState = FormWindowState.Normal;
    this.Name = "frmMsgBoxEx";
    this.cmdMsgBoxPause.TextAlign = ContentAlignment.MiddleCenter;
    this.cmdMsgBoxPause.Text = "MsgBoxPause";
    this.cmdMsgBoxPause.Font = new Font("Verdana", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button cmdMsgBoxPause1 = this.cmdMsgBoxPause;
    size1 = new Size(104, 27);
    Size size2 = size1;
    cmdMsgBoxPause1.Size = size2;
    Button cmdMsgBoxPause2 = this.cmdMsgBoxPause;
    point1 = new Point(144 /*0x90*/, 32 /*0x20*/);
    Point point2 = point1;
    cmdMsgBoxPause2.Location = point2;
    this.cmdMsgBoxPause.TabIndex = 1;
    this.cmdMsgBoxPause.BackColor = SystemColors.Control;
    this.cmdMsgBoxPause.CausesValidation = true;
    this.cmdMsgBoxPause.Enabled = true;
    this.cmdMsgBoxPause.ForeColor = SystemColors.ControlText;
    this.cmdMsgBoxPause.Cursor = Cursors.Default;
    this.cmdMsgBoxPause.RightToLeft = RightToLeft.No;
    this.cmdMsgBoxPause.TabStop = true;
    this.cmdMsgBoxPause.Name = "cmdMsgBoxPause";
    this.cmdMsgBoxMove.TextAlign = ContentAlignment.MiddleCenter;
    this.cmdMsgBoxMove.Text = "MsgBoxMove";
    this.cmdMsgBoxMove.Font = new Font("Verdana", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button cmdMsgBoxMove1 = this.cmdMsgBoxMove;
    size1 = new Size(104, 27);
    Size size3 = size1;
    cmdMsgBoxMove1.Size = size3;
    Button cmdMsgBoxMove2 = this.cmdMsgBoxMove;
    point1 = new Point(24, 34);
    Point point3 = point1;
    cmdMsgBoxMove2.Location = point3;
    this.cmdMsgBoxMove.TabIndex = 0;
    this.cmdMsgBoxMove.BackColor = SystemColors.Control;
    this.cmdMsgBoxMove.CausesValidation = true;
    this.cmdMsgBoxMove.Enabled = true;
    this.cmdMsgBoxMove.ForeColor = SystemColors.ControlText;
    this.cmdMsgBoxMove.Cursor = Cursors.Default;
    this.cmdMsgBoxMove.RightToLeft = RightToLeft.No;
    this.cmdMsgBoxMove.TabStop = true;
    this.cmdMsgBoxMove.Name = "cmdMsgBoxMove";
    this.Controls.Add((Control) this.cmdMsgBoxPause);
    this.Controls.Add((Control) this.cmdMsgBoxMove);
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  private void CmdMsgBoxMove_Click(object eventSender, EventArgs eventArgs)
  {
    object obj1 = (object) 100;
    object obj2 = (object) 50;
    this.mResult = (object) basMsgBoxEx.MsgBoxMove(this.Handle.ToInt32(), "Is this message displayed at a location \r\nspecified by you?", "Msg Box", 4, Conversions.ToInteger(obj1), Conversions.ToInteger(obj2));
  }

  private void CmdMsgBoxPause_Click(object eventSender, EventArgs eventArgs)
  {
    short inPause = 3;
    this.mResult = (object) basMsgBoxEx.MsgBoxPause(this.Handle.ToInt32(), "This message will automatically disappear, after pausing\r\na number of seconds specified by you.", "Msg Box Pause", 48 /*0x30*/, inPause);
  }
}

}