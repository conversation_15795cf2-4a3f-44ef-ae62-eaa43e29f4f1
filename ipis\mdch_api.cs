// Decompiled with JetBrains decompiler
// Type: ipis.mdch_api
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Diagnostics;
using System.Threading;

namespace ipis
{

public class mdch_api
{
  private static byte[] pkt_buf = new byte[2000];
  private static byte[] rxbuf = new byte[2000];

  [DebuggerNonUserCode]
  public mdch_api()
  {
  }

  public static byte mdch_link_check(byte mdch_addr, ref byte[] mdch_pkt, ref short length)
  {
    int index1 = 0;
    int index2 = 0;
    byte num1 = 0;
    try
    {
      while (index2 < 2000)
      {
        mdch_api.rxbuf[index2] = (byte) 0;
        checked { ++index2; }
      }
      mdch_api.pkt_buf[0] = (byte) 170;
      mdch_api.pkt_buf[1] = (byte) 204;
      mdch_api.pkt_buf[2] = (byte) 0;
      mdch_api.pkt_buf[3] = (byte) 10;
      mdch_api.pkt_buf[4] = frmMainFormIPIS.ccu_addr;
      mdch_api.pkt_buf[5] = (byte) 0;
      mdch_api.pkt_buf[6] = mdch_addr;
      mdch_api.pkt_buf[7] = (byte) 0;
      mdch_api.pkt_buf[8] = (byte) 0;
      mdch_api.pkt_buf[9] = (byte) 128 /*0x80*/;
      ushort length1 = checked ((ushort) ((int) unchecked ((ushort) ((int) (ushort) (0U << 8) | 10)) + 2));
      Checksum.prepare_checksum(ref mdch_api.pkt_buf, length1);
      length = checked ((short) length1);
      while (index1 < (int) length1)
      {
        mdch_pkt[index1] = mdch_api.pkt_buf[index1];
        checked { ++index1; }
      }
      if (RS232.Serial_Write(ref mdch_api.pkt_buf, (int) length1) != 1)
      {
        Log_file.Log(string.Format("Write to COM port failed, may be COM port deos't exit. Please check the COM PORT settings"));
        num1 = (byte) 3;
      }
      else
        num1 = (byte) 1;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num2 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    return num1;
  }

  public static byte mdch_link_check_res_pkt(
    byte mdch_addr,
    ref byte mdch_sys_cfg,
    ref byte[] link_chk_msg,
    ref byte[] mdch_pkt,
    ref short length)
  {
    byte num1 = 0;
    try
    {
      if (RS232.Serial_Read(ref mdch_api.rxbuf) == (byte) 1)
      {
        ushort Pkt_length = checked ((ushort) unchecked ((int) (ushort) ((uint) (ushort) mdch_api.rxbuf[0] << 8) + (int) mdch_api.rxbuf[1]));
        length = checked ((short) ((int) Pkt_length + 2));
        mdch_pkt[0] = (byte) 170;
        mdch_pkt[1] = (byte) 204;
        int index1 = 0;
        while (index1 < (int) Pkt_length)
        {
          mdch_pkt[checked (index1 + 2)] = mdch_api.rxbuf[index1];
          checked { ++index1; }
        }
        int num2 = 0;
        if (Checksum.Checksum_Calc(ref mdch_api.rxbuf, Pkt_length) == 0)
        {
          Log_file.Log("MDCH Address:{mdch_addr}LINK CHECK RESPONSE PACKET: CHECKSUM FAILED ");
          num2 = 1;
        }
        if ((int) mdch_api.rxbuf[2] != (int) mdch_addr & mdch_api.rxbuf[3] != (byte) 0)
        {
          Log_file.Log("MDCH Address:{mdch_addr}LINK CHECK RESPONSE PACKET: IN CORRECT SOURCE ADDRESS");
          num2 = 1;
        }
        if (Pkt_length != (ushort) 700)
        {
          Log_file.Log("MDCH Address:{mdch_addr}LINK CHECK RESPONSE PACKET: IN VALID DATA LENGTH");
          num2 = 1;
        }
        if (mdch_api.rxbuf[7] != (byte) 192 /*0xC0*/)
        {
          Log_file.Log("MDCH Address:{mdch_addr}LINK CHECK RESPONSE PACKET: IN-VALID RESPONSE PACKET FUNCTION CODE");
          num2 = 1;
        }
        switch (mdch_api.rxbuf[8])
        {
          case 0:
            Log_file.Log("MDCH Address:{mdch_addr}LINK CHECK COMMAND PACKET STATUS: Packet Received and proceed successfully ");
            break;
          case 2:
            Log_file.Log("MDCH Address:{mdch_addr}LINK CHECK COMMAND PACKET STATUS: CRC FAIL");
            num2 = 1;
            break;
          case 6:
            Log_file.Log("MDCH Address:{mdch_addr}LINK CHECK COMMAND PACKET STATUS: IN VALID FUNCTION CODE");
            num2 = 1;
            break;
          case 35:
            Log_file.Log("MDCH Address:{mdch_addr}LINK CHECK COMMAND PACKET STATUS: IN VALID DATA LENGTH");
            num2 = 1;
            break;
        }
        mdch_sys_cfg = mdch_api.rxbuf[9];
        ushort index2 = 0;
        while (index2 < (ushort) 688)
        {
          link_chk_msg[(int) index2] = mdch_api.rxbuf[checked (10 + (int) index2)];
          checked { ++index2; }
        }
        if (num2 != 0)
        {
          Log_file.Log("MDCH Address:{mdch_addr}LINK CHECK COMMAND IS UNSUCCESSFULL");
          num1 = (byte) 0;
        }
        else
        {
          Log_file.Log("MDCH Address:{mdch_addr}LINK CHECK COMMAND IS SUCCESSFUL");
          num1 = (byte) 1;
        }
      }
      else
      {
        Log_file.Log("MDCH Address:{mdch_addr} LINK FAILURE or ADDRESSED MDCH DOESn't EXIST");
        num1 = (byte) 2;
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num3 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    return num1;
  }

  public static byte mdch_set_cfg_send_pkt(
    byte mdch_addr,
    byte[] set_cfg_msg,
    ref short[] mdch_sc_pkt,
    ref short length)
  {
    byte num1 = 0;
    try
    {
      int index1 = 0;
      while (index1 < 2000)
      {
        mdch_api.rxbuf[index1] = (byte) 0;
        checked { ++index1; }
      }
      mdch_api.pkt_buf[0] = (byte) 170;
      mdch_api.pkt_buf[1] = (byte) 204;
      mdch_api.pkt_buf[2] = (byte) 1;
      mdch_api.pkt_buf[3] = (byte) 122;
      mdch_api.pkt_buf[4] = frmMainFormIPIS.ccu_addr;
      mdch_api.pkt_buf[5] = (byte) 0;
      mdch_api.pkt_buf[6] = mdch_addr;
      mdch_api.pkt_buf[7] = (byte) 0;
      mdch_api.pkt_buf[8] = (byte) 0;
      mdch_api.pkt_buf[9] = (byte) 132;
      int index2 = 0;
      while (index2 < 368)
      {
        mdch_api.pkt_buf[checked (10 + index2)] = set_cfg_msg[index2];
        checked { ++index2; }
      }
      ushort length1 = checked ((ushort) ((int) unchecked ((ushort) ((int) (ushort) (1U << 8) | 122)) + 2));
      Checksum.prepare_checksum(ref mdch_api.pkt_buf, length1);
      length = checked ((short) length1);
      int index3 = 0;
      while (index3 < (int) length1)
      {
        mdch_sc_pkt[index3] = (short) mdch_api.pkt_buf[index3];
        checked { ++index3; }
      }
      if (RS232.Serial_Write(ref mdch_api.pkt_buf, (int) length1) != 1)
      {
        Log_file.Log(string.Format("Write to COM port failed, may be COM port deos't exit. Please check the COM PORT settings"));
        num1 = (byte) 3;
      }
      else
        num1 = (byte) 1;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num2 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    return num1;
  }

  public static byte mdch_set_cfg_res_pkt(
    byte mdch_addr,
    ref short[] mdch_sc_pkt,
    ref short length)
  {
    byte num1 = 0;
    try
    {
      if (RS232.Serial_Read(ref mdch_api.rxbuf) == (byte) 1)
      {
        ushort Pkt_length = checked ((ushort) unchecked ((int) (ushort) ((uint) (ushort) mdch_api.rxbuf[0] << 8) + (int) mdch_api.rxbuf[1]));
        length = checked ((short) ((int) Pkt_length + 2));
        int index = 0;
        mdch_sc_pkt[0] = (short) 170;
        mdch_sc_pkt[1] = (short) 204;
        while (index < (int) Pkt_length)
        {
          mdch_sc_pkt[checked (index + 2)] = (short) mdch_api.rxbuf[index];
          checked { ++index; }
        }
        int num2 = 0;
        if (Checksum.Checksum_Calc(ref mdch_api.rxbuf, Pkt_length) == 0)
        {
          Log_file.Log("MDCH Address:{mdch_addr} SET CONFIGURATION RESPONSE PACKET: CHECKSUM FAILED");
          num2 = 1;
        }
        if ((int) mdch_api.rxbuf[2] != (int) mdch_addr & mdch_api.rxbuf[3] != (byte) 0)
        {
          Log_file.Log("MDCH Address:{mdch_addr} SET CONFIGURATION RESPONSE PACKET: IN CORRECT SOURCE ADDRESS");
          num2 = 1;
        }
        if (Pkt_length != (ushort) 11)
        {
          Log_file.Log("MDCH Address:{mdch_addr} SET CONFIGURATION RESPONSE PACKET: IN VALID DATA LENGTH");
          num2 = 1;
        }
        if (mdch_api.rxbuf[7] != (byte) 196)
        {
          Log_file.Log("MDCH Address:{mdch_addr} SET CONFIGURATION RESPONSE PACKET: IN VALID FUNCTION CODE");
          num2 = 1;
        }
        switch (mdch_api.rxbuf[8])
        {
          case 0:
            Log_file.Log("MDCH Address:{mdch_addr} SET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully");
            break;
          case 2:
            Log_file.Log("MDCH Address:{mdch_addr} SET CONFIGURATION COMMAND PACKET: CRC FAIL");
            num2 = 1;
            break;
          case 6:
            Log_file.Log("MDCH Address:{mdch_addr} SET CONFIGURATION COMMAND PACKET: IN VALID FUNCTION CODE");
            num2 = 1;
            break;
          case 35:
            Log_file.Log("MDCH Address:{mdch_addr} SET CONFIGURATION COMMAND PACKET: IN VALID DATA LENGTH");
            num2 = 1;
            break;
        }
        if (num2 != 0)
        {
          Log_file.Log("MDCH Address:{mdch_addr} SET CONFIGURATION COMMAND IS UNSUCCESSFUL");
          num1 = (byte) 0;
        }
        else
        {
          Log_file.Log("MDCH Address:{mdch_addr} SET CONFIGURATION COMMAND IS SUCCESSFUL");
          num1 = (byte) 1;
        }
      }
      else
      {
        Log_file.Log("MDCH Address:{mdch_addr} LINK FAILURE or ADDRESSED MDCH DOESN'T EXIST");
        num1 = (byte) 2;
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num3 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    return num1;
  }

  public static byte mdch_get_cfg_send_pkt(
    byte mdch_addr,
    ref byte mdch_sys_cfg,
    ref byte[] get_cfg_msg,
    ref short length)
  {
    ushort index1 = 0;
    byte cfgSendPkt = 0;
    try
    {
      ushort index2 = 0;
      while (index2 < (ushort) 2000)
      {
        mdch_api.rxbuf[(int) index2] = (byte) 0;
        checked { ++index2; }
      }
      mdch_api.pkt_buf[0] = (byte) 170;
      mdch_api.pkt_buf[1] = (byte) 204;
      mdch_api.pkt_buf[2] = (byte) 0;
      mdch_api.pkt_buf[3] = (byte) 10;
      mdch_api.pkt_buf[4] = frmMainFormIPIS.ccu_addr;
      mdch_api.pkt_buf[5] = (byte) 0;
      mdch_api.pkt_buf[6] = mdch_addr;
      mdch_api.pkt_buf[7] = (byte) 0;
      mdch_api.pkt_buf[8] = (byte) 0;
      mdch_api.pkt_buf[9] = (byte) 133;
      ushort length1 = checked ((ushort) ((int) unchecked ((ushort) ((int) (ushort) (0U << 8) | 10)) + 2));
      Checksum.prepare_checksum(ref mdch_api.pkt_buf, length1);
      length = checked ((short) length1);
      while ((uint) index1 < (uint) length1)
      {
        get_cfg_msg[(int) index1] = mdch_api.pkt_buf[(int) index1];
        checked { ++index1; }
      }
      if (RS232.Serial_Write(ref mdch_api.pkt_buf, (int) length1) != 1)
      {
        Log_file.Log(string.Format("Write to COM port failed, may be COM port deos't exit. Please check the COM PORT settings"));
        cfgSendPkt = (byte) 3;
      }
      else
        cfgSendPkt = (byte) 1;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    return cfgSendPkt;
  }

  public static byte mdch_get_cfg_res_pkt(
    byte mdch_addr,
    ref byte mdch_sys_cfg,
    ref byte[] get_cfg_msg,
    ref short length)
  {
    byte cfgResPkt = 0;
    try
    {
      if (RS232.Serial_Read(ref mdch_api.rxbuf) == (byte) 1)
      {
        ushort Pkt_length = checked ((ushort) unchecked ((int) (ushort) ((uint) (ushort) mdch_api.rxbuf[0] << 8) + (int) mdch_api.rxbuf[1]));
        length = checked ((short) ((int) Pkt_length + 2));
        int index = 0;
        get_cfg_msg[0] = (byte) 170;
        get_cfg_msg[1] = (byte) 204;
        while (index < (int) Pkt_length)
        {
          get_cfg_msg[checked (index + 2)] = mdch_api.rxbuf[index];
          checked { ++index; }
        }
        int num = 0;
        if (Checksum.Checksum_Calc(ref mdch_api.rxbuf, Pkt_length) == 0)
        {
          Log_file.Log("MDCH Address:{mdch_addr} GET CONFIGURATION RESPONSE PACKET: CHECKSUM FAILED");
          num = 1;
        }
        if ((int) mdch_api.rxbuf[2] != (int) mdch_addr & mdch_api.rxbuf[3] != (byte) 0)
        {
          Log_file.Log("MDCH Address:{mdch_addr} GET CONFIGURATION RESPONSE PACKET: IN CORRECT SOURCE ADDRESS");
          num = 1;
        }
        if (Pkt_length != (ushort) 380)
        {
          Log_file.Log("MDCH Address:{mdch_addr} GET CONFIGURATION RESPONSE PACKET: IN VALID DATA LENGTH");
          num = 1;
        }
        if (mdch_api.rxbuf[7] != (byte) 197)
        {
          Log_file.Log("MDCH Address:{mdch_addr} GET CONFIGURATION RESPONSE PACKET: IN VALID FUNCTION CODE");
          num = 1;
        }
        switch (mdch_api.rxbuf[8])
        {
          case 0:
            Log_file.Log("MDCH Address:{mdch_addr} GET CONFIGURATION COMMAND PACKET: Packet Received and Processed Successfully");
            break;
          case 2:
            Log_file.Log("MDCH Address:{mdch_addr} GET CONFIGURATION COMMAND PACKET: CRC FAIL");
            num = 1;
            break;
          case 6:
            Log_file.Log("MDCH Address:{mdch_addr} GET CONFIGURATION COMMAND PACKET: IN VALID FUNCTION CODE");
            num = 1;
            break;
          case 35:
            Log_file.Log("MDCH Address:{mdch_addr} GET CONFIGURATION COMMAND PACKET: IN VALID DATA LENGTH");
            num = 1;
            break;
        }
        mdch_sys_cfg = mdch_api.rxbuf[9];
        if (num != 0)
        {
          Log_file.Log("MDCH Address:{mdch_addr} GET CONFIGURATION COMMAND IS UNSUCCESSFUL");
          cfgResPkt = (byte) 0;
        }
        else
        {
          Log_file.Log("MDCH Address:{mdch_addr} GET CONFIGURATION COMMAND IS SUCCESSFUL");
          cfgResPkt = (byte) 1;
        }
      }
      else
      {
        Log_file.Log("MDCH Address:{mdch_addr} LINK FAILURE or ADDRESSED MDCH DOESN'T EXIST");
        cfgResPkt = (byte) 2;
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    return cfgResPkt;
  }

  public static byte mdch_soft_reset(byte mdch_addr, ref byte[] mdch_pkt, ref short length)
  {
    int index = 0;
    mdch_api.pkt_buf[0] = (byte) 170;
    mdch_api.pkt_buf[1] = (byte) 204;
    mdch_api.pkt_buf[2] = (byte) 0;
    mdch_api.pkt_buf[3] = (byte) 10;
    mdch_api.pkt_buf[4] = frmMainFormIPIS.ccu_addr;
    mdch_api.pkt_buf[5] = (byte) 0;
    mdch_api.pkt_buf[6] = mdch_addr;
    mdch_api.pkt_buf[7] = (byte) 0;
    mdch_api.pkt_buf[8] = (byte) 0;
    mdch_api.pkt_buf[9] = (byte) 134;
    byte num1 = 0;
    try
    {
      ushort length1 = checked ((ushort) ((int) unchecked ((ushort) ((int) (ushort) (0U << 8) | 10)) + 2));
      Checksum.prepare_checksum(ref mdch_api.pkt_buf, length1);
      length = checked ((short) length1);
      while (index < (int) length1)
      {
        mdch_pkt[index] = mdch_api.pkt_buf[index];
        checked { ++index; }
      }
      if (RS232.Serial_Write(ref mdch_api.pkt_buf, (int) length1) != 1)
      {
        Log_file.Log(string.Format("Write to COM port failed, may be COM port deos't exit. Please check the COM PORT settings"));
        num1 = (byte) 3;
      }
      else
      {
        Thread.Sleep(10);
        Log_file.Log("MDCH Address:{mdch_addr} SOFT RESET COMMAND IS SUCCESSFUL");
        num1 = (byte) 1;
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num2 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    return num1;
  }

  public static byte mdch_clr_reset(byte mdch_addr, ref byte[] mdch_pkt, ref short length)
  {
    byte index = 0;
    byte num1 = 0;
    try
    {
      mdch_api.pkt_buf[0] = (byte) 170;
      mdch_api.pkt_buf[1] = (byte) 204;
      mdch_api.pkt_buf[2] = (byte) 0;
      mdch_api.pkt_buf[3] = (byte) 10;
      mdch_api.pkt_buf[4] = frmMainFormIPIS.ccu_addr;
      mdch_api.pkt_buf[5] = (byte) 0;
      mdch_api.pkt_buf[6] = mdch_addr;
      mdch_api.pkt_buf[7] = (byte) 0;
      mdch_api.pkt_buf[8] = (byte) 0;
      mdch_api.pkt_buf[9] = (byte) 135;
      ushort length1 = checked ((ushort) ((int) unchecked ((ushort) ((int) (ushort) (0U << 8) | 10)) + 2));
      Checksum.prepare_checksum(ref mdch_api.pkt_buf, length1);
      length = checked ((short) length1);
      while ((uint) index < (uint) length1)
      {
        mdch_pkt[(int) index] = mdch_api.pkt_buf[(int) index];
        checked { ++index; }
      }
      if (RS232.Serial_Write(ref mdch_api.pkt_buf, (int) length1) != 1)
      {
        Log_file.Log(string.Format("Write to COM port failed, may be COM port deos't exit. Please check the COM PORT settings"));
        num1 = (byte) 3;
      }
      else
        num1 = (byte) 1;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num2 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    return num1;
  }

  public static byte mdch_clr_reset_res_pkt(byte mdch_addr, ref byte[] mdch_pkt, ref short length)
  {
    byte num1 = 0;
    try
    {
      if (RS232.Serial_Read(ref mdch_api.rxbuf) == (byte) 1)
      {
        ushort Pkt_length = checked ((ushort) unchecked ((int) (ushort) ((uint) (ushort) mdch_api.rxbuf[0] << 8) + (int) mdch_api.rxbuf[1]));
        length = checked ((short) ((int) Pkt_length + 2));
        mdch_pkt[0] = (byte) 170;
        mdch_pkt[1] = (byte) 204;
        byte index = 0;
        while ((uint) index < (uint) Pkt_length)
        {
          mdch_pkt[checked ((int) index + 2)] = mdch_api.rxbuf[(int) index];
          checked { ++index; }
        }
        byte num2 = 0;
        if (Checksum.Checksum_Calc(ref mdch_api.rxbuf, Pkt_length) == 0)
        {
          Log_file.Log("MDCH Address:{mdch_addr} CLEAR RESET RESPONSE PACKET: CHECKSUM FAILED");
          num2 = (byte) 1;
        }
        if ((int) mdch_api.rxbuf[2] != (int) mdch_addr & mdch_api.rxbuf[3] != (byte) 0)
        {
          Log_file.Log("MDCH Address:{mdch_addr} CLEAR RESET RESPONSE PACKET: IN CORRECT SOURCE ADDRESS");
          num2 = (byte) 1;
        }
        if (Pkt_length != (ushort) 11)
        {
          Log_file.Log("MDCH Address:{mdch_addr} CLEAR RESET RESPONSE PACKET: IN VALID DATA LENGTH");
          num2 = (byte) 1;
        }
        if (mdch_api.rxbuf[7] != (byte) 199)
        {
          Log_file.Log("<MDCH Address:{mdch_addr} CLEAR RESET RESPONSE PACKET: IN VALID FUNCTION CODE");
          num2 = (byte) 1;
        }
        switch (mdch_api.rxbuf[8])
        {
          case 0:
            Log_file.Log("MDCH Address:{mdch_addr} CLEAR RESET COMMAND PACKET: Packet Received and Processed Successfully");
            break;
          case 2:
            Log_file.Log("MDCH Address:{mdch_addr} CLEAR RESET COMMAND PACKET: CRC FAIL");
            num2 = (byte) 1;
            break;
          case 6:
            Log_file.Log("MDCH Address:{mdch_addr} CLEAR RESET COMMAND PACKET: IN VALID FUNCTION CODE");
            num2 = (byte) 1;
            break;
          case 35:
            Log_file.Log("MDCH Address:{mdch_addr} CLEAR RESET COMMAND PACKET: IN VALID DATA LENGTH");
            num2 = (byte) 1;
            break;
        }
        if (num2 != (byte) 0)
        {
          Log_file.Log("MDCH Address:{mdch_addr} CLEAT RESET COMMAND IS UNSUCCESSFUL");
          num1 = (byte) 0;
        }
        else
        {
          Log_file.Log("MDCH Address:{mdch_addr} CLEAR RESET COMMAND IS SUCCESSFUL");
          num1 = (byte) 1;
        }
      }
      else
      {
        Log_file.Log("MDCH Address:{mdch_addr} LINK FAILURE or ADDRESSED MDCH DOESN'T EXIST");
        num1 = (byte) 2;
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num3 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    return num1;
  }

  public static byte mdch_pre_cmd_status(byte mdch_addr, ref byte[] mdch_pkt, ref short length)
  {
    int index = 0;
    byte num1 = 0;
    try
    {
      mdch_api.pkt_buf[0] = (byte) 170;
      mdch_api.pkt_buf[1] = (byte) 204;
      mdch_api.pkt_buf[2] = (byte) 0;
      mdch_api.pkt_buf[3] = (byte) 10;
      mdch_api.pkt_buf[4] = frmMainFormIPIS.ccu_addr;
      mdch_api.pkt_buf[5] = (byte) 0;
      mdch_api.pkt_buf[6] = mdch_addr;
      mdch_api.pkt_buf[7] = (byte) 0;
      mdch_api.pkt_buf[8] = (byte) 0;
      mdch_api.pkt_buf[9] = (byte) 136;
      ushort length1 = checked ((ushort) ((int) unchecked ((ushort) ((int) (ushort) (0U << 8) | 10)) + 2));
      Checksum.prepare_checksum(ref mdch_api.pkt_buf, length1);
      length = checked ((short) length1);
      while (index < (int) length1)
      {
        mdch_pkt[index] = mdch_api.pkt_buf[index];
        checked { ++index; }
      }
      if (RS232.Serial_Write(ref mdch_api.pkt_buf, (int) length1) != 1)
      {
        Log_file.Log(string.Format("Write to COM port failed, may be COM port deos't exit. Please check the COM PORT settings"));
        num1 = (byte) 3;
      }
      else
        num1 = (byte) 1;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num2 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    return num1;
  }

  public static byte mdch_pre_cmd_res_pkt(
    byte mdch_addr,
    ref byte pre_cmd_status,
    ref byte pre_cmd_serial_no,
    ref byte pre_cmd_fc,
    ref byte[] mdch_pkt,
    ref short length)
  {
    byte num1 = 0;
    try
    {
      if (RS232.Serial_Read(ref mdch_api.rxbuf) == (byte) 1)
      {
        ushort Pkt_length = checked ((ushort) unchecked ((int) (ushort) ((uint) (ushort) mdch_api.rxbuf[0] << 8) + (int) mdch_api.rxbuf[1]));
        length = checked ((short) ((int) Pkt_length + 2));
        mdch_pkt[0] = (byte) 170;
        mdch_pkt[1] = (byte) 204;
        byte index = 0;
        while ((uint) index < (uint) Pkt_length)
        {
          mdch_pkt[checked ((int) index + 2)] = mdch_api.rxbuf[(int) index];
          checked { ++index; }
        }
        byte num2 = 0;
        if (Checksum.Checksum_Calc(ref mdch_api.rxbuf, Pkt_length) == 0)
        {
          Log_file.Log("MDCH Address:{mdch_addr} PREVIOUS COMMAND RESPONSE PACKET: CHECKSUM FAILED");
          num2 = (byte) 1;
        }
        if ((int) mdch_api.rxbuf[2] != (int) mdch_addr & mdch_api.rxbuf[3] != (byte) 0)
        {
          Log_file.Log("MDCH Address:{mdch_addr} PREVIOUS COMMAND RESPONSE PACKET: IN CORRECT SOURCE ADDRESS");
          num2 = (byte) 1;
        }
        if (Pkt_length != (ushort) 14)
        {
          Log_file.Log("MDCH Address:{mdch_addr} PREVIOUS COMMAND RESPONSE PACKET: IN VALID DATA LENGTH");
          num2 = (byte) 1;
        }
        if (mdch_api.rxbuf[7] != (byte) 200)
        {
          Log_file.Log("MDCH Address:{mdch_addr} PREVIOUS COMMAND RESPONSE PACKET: IN VALID FUNCTION CODE");
          num2 = (byte) 1;
        }
        switch (mdch_api.rxbuf[8])
        {
          case 0:
            Log_file.Log("MDCH Address:{mdch_addr} PREVIOUS COMMAND PACKET: Packet Received and Processed Successfully");
            break;
          case 2:
            Log_file.Log("MDCH Address:{mdch_addr} PREVIOUS COMMAND PACKET: CRC FAIL");
            num2 = (byte) 1;
            break;
          case 6:
            Log_file.Log("MDCH Address:{mdch_addr} PREVIOUS COMMAND PACKET: IN VALID FUNCTION CODE");
            num2 = (byte) 1;
            break;
          case 35:
            Log_file.Log("MDCH Address:{mdch_addr} PREVIOUS COMMAND PACKET: IN VALID DATA LENGTH");
            num2 = (byte) 1;
            break;
        }
        pre_cmd_status = mdch_api.rxbuf[9];
        pre_cmd_serial_no = mdch_api.rxbuf[10];
        pre_cmd_fc = mdch_api.rxbuf[11];
        if (num2 != (byte) 0)
        {
          Log_file.Log("MDCH Address:{mdch_addr} PREVIOUS COMMAND IS UNSUCCESSFUL");
          num1 = (byte) 0;
        }
        else
        {
          Log_file.Log("MDCH Address:{mdch_addr} PREVIOUS COMMAND IS SUCCESSFUL");
          num1 = (byte) 1;
        }
      }
      else
      {
        Log_file.Log("MDCH Address:{mdch_addr} LINK FAILURE or ADDRESSED MDCH DOESN'T EXIST");
        num1 = (byte) 2;
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num3 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    return num1;
  }

  public static byte mdch_diag_cmd(byte mdch_addr, ref byte[] mdch_pkt, ref short length)
  {
    int index = 0;
    byte num1 = 0;
    try
    {
      mdch_api.pkt_buf[0] = (byte) 170;
      mdch_api.pkt_buf[1] = (byte) 204;
      mdch_api.pkt_buf[2] = (byte) 0;
      mdch_api.pkt_buf[3] = (byte) 10;
      mdch_api.pkt_buf[4] = frmMainFormIPIS.ccu_addr;
      mdch_api.pkt_buf[5] = (byte) 0;
      mdch_api.pkt_buf[6] = mdch_addr;
      mdch_api.pkt_buf[7] = (byte) 0;
      mdch_api.pkt_buf[8] = (byte) 0;
      mdch_api.pkt_buf[9] = (byte) 138;
      ushort length1 = checked ((ushort) ((int) unchecked ((ushort) ((int) (ushort) (0U << 8) | 10)) + 2));
      Checksum.prepare_checksum(ref mdch_api.pkt_buf, length1);
      length = checked ((short) length1);
      while (index < (int) length1)
      {
        mdch_pkt[index] = mdch_api.pkt_buf[index];
        checked { ++index; }
      }
      if (RS232.Serial_Write(ref mdch_api.pkt_buf, (int) length1) != 1)
      {
        Log_file.Log(string.Format("Write to COM port failed, may be COM port deos't exit. Please check the COM PORT settings"));
        num1 = (byte) 3;
      }
      else
        num1 = (byte) 1;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num2 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    return num1;
  }

  public static byte MDCH_diag_cmd_res_pkt(
    byte mdch_addr,
    ref byte[] manu_id,
    ref byte[] mdch_pkt,
    ref short length)
  {
    byte num1 = 0;
    try
    {
      if (RS232.Serial_Read(ref mdch_api.rxbuf) == (byte) 1)
      {
        ushort Pkt_length = checked ((ushort) unchecked ((int) (ushort) ((uint) (ushort) mdch_api.rxbuf[0] << 8) + (int) mdch_api.rxbuf[1]));
        length = checked ((short) ((int) Pkt_length + 2));
        mdch_pkt[0] = (byte) 170;
        mdch_pkt[1] = (byte) 204;
        byte index = 0;
        while ((uint) index < (uint) Pkt_length)
        {
          mdch_pkt[checked ((int) index + 2)] = mdch_api.rxbuf[(int) index];
          checked { ++index; }
        }
        byte num2 = 0;
        if (Checksum.Checksum_Calc(ref mdch_api.rxbuf, Pkt_length) == 0)
        {
          Log_file.Log("MDCH Address:{mdch_addr} DIAGNOSTIC COMMAND RESPONSE PACKET: CHECKSUM FAILED");
          num2 = (byte) 1;
        }
        if ((int) mdch_api.rxbuf[2] != (int) mdch_addr & mdch_api.rxbuf[3] != (byte) 0)
        {
          Log_file.Log("MDCH Address:{mdch_addr} DIAGNOSTIC COMMAND RESPONSE PACKET: IN CORRECT SOURCE ADDRESS");
          num2 = (byte) 1;
        }
        if (Pkt_length != (ushort) 16 /*0x10*/)
        {
          Log_file.Log("MDCH Address:{mdch_addr} DIAGNOSTIC COMMAND RESPONSE PACKET: IN VALID DATA LENGTH");
          num2 = (byte) 1;
        }
        if (mdch_api.rxbuf[7] != (byte) 202)
        {
          Log_file.Log("MDCH Address:{mdch_addr} DIAGNOSTIC COMMAND RESPONSE PACKET: IN VALID FUNCTION CODE");
          num2 = (byte) 1;
        }
        switch (mdch_api.rxbuf[8])
        {
          case 0:
            Log_file.Log("MDCH Address:{mdch_addr} DIAGNOSTIC COMMAND PACKET: Packet Received and Processed Successfully");
            break;
          case 2:
            Log_file.Log("MDCH Address:{mdch_addr} DIAGNOSTIC COMMAND PACKET: CRC FAIL");
            num2 = (byte) 1;
            break;
          case 6:
            Log_file.Log("MDCH Address:{mdch_addr} DIAGNOSTIC COMMAND PACKET: IN VALID FUNCTION CODE");
            num2 = (byte) 1;
            break;
          case 35:
            Log_file.Log("MDCH Address:{mdch_addr} DIAGNOSTIC COMMAND PACKET: IN VALID DATA LENGTH");
            num2 = (byte) 1;
            break;
        }
        manu_id[0] = mdch_api.rxbuf[9];
        manu_id[1] = mdch_api.rxbuf[10];
        manu_id[2] = mdch_api.rxbuf[11];
        manu_id[3] = mdch_api.rxbuf[12];
        manu_id[4] = mdch_api.rxbuf[13];
        if (num2 != (byte) 0)
        {
          Log_file.Log("MDCH Address:{mdch_addr} DIAGNOSTIC COMMAND IS UNSUCCESSFUL");
          num1 = (byte) 0;
        }
        else
        {
          Log_file.Log("MDCH Address:{mdch_addr} DIAGNOSTIC COMMAND IS SUCCESSFUL");
          num1 = (byte) 1;
        }
      }
      else
      {
        Log_file.Log("MDCH Address:{mdch_addr} LINK FAILURE or ADDRESSED MDCH DOESN'T EXIST");
        num1 = (byte) 2;
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num3 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    return num1;
  }

  public static byte mdch_optional_cmd(byte mdch_addr, ref byte[] mdch_pkt, ref short length)
  {
    int index = 0;
    byte num1 = 0;
    try
    {
      mdch_api.pkt_buf[0] = (byte) 170;
      mdch_api.pkt_buf[1] = (byte) 204;
      mdch_api.pkt_buf[2] = (byte) 0;
      mdch_api.pkt_buf[3] = (byte) 10;
      mdch_api.pkt_buf[4] = frmMainFormIPIS.ccu_addr;
      mdch_api.pkt_buf[5] = (byte) 0;
      mdch_api.pkt_buf[6] = mdch_addr;
      mdch_api.pkt_buf[7] = (byte) 0;
      mdch_api.pkt_buf[8] = (byte) 0;
      mdch_api.pkt_buf[9] = (byte) 139;
      ushort length1 = checked ((ushort) ((int) unchecked ((ushort) ((int) (ushort) (0U << 8) | 10)) + 2));
      Checksum.prepare_checksum(ref mdch_api.pkt_buf, length1);
      length = checked ((short) length1);
      while (index < (int) length1)
      {
        mdch_pkt[index] = mdch_api.pkt_buf[index];
        checked { ++index; }
      }
      if (RS232.Serial_Write(ref mdch_api.pkt_buf, (int) length1) != 1)
      {
        Log_file.Log(string.Format("Write to COM port failed, may be COM port deos't exit. Please check the COM PORT settings"));
        num1 = (byte) 3;
      }
      else
        num1 = (byte) 1;
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num2 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    return num1;
  }

  public static byte MDCH_opt_cmd_res_pkt(
    byte mdch_addr,
    ref byte[] manu_id,
    ref byte[] mdch_pkt,
    ref short length)
  {
    byte num1 = 0;
    try
    {
      if (RS232.Serial_Read(ref mdch_api.rxbuf) == (byte) 1)
      {
        ushort Pkt_length = checked ((ushort) unchecked ((int) (ushort) ((uint) (ushort) mdch_api.rxbuf[0] << 8) + (int) mdch_api.rxbuf[1]));
        length = checked ((short) ((int) Pkt_length + 2));
        mdch_pkt[0] = (byte) 170;
        mdch_pkt[1] = (byte) 204;
        byte index = 0;
        while ((uint) index < (uint) Pkt_length)
        {
          mdch_pkt[checked ((int) index + 2)] = mdch_api.rxbuf[(int) index];
          checked { ++index; }
        }
        byte num2 = 0;
        if (Checksum.Checksum_Calc(ref mdch_api.rxbuf, Pkt_length) == 0)
        {
          Log_file.Log("MDCH Address:{mdch_addr} OPTIONAL COMMAND RESPONSE PACKET: CHECKSUM FAILED");
          num2 = (byte) 1;
        }
        if ((int) mdch_api.rxbuf[2] != (int) mdch_addr & mdch_api.rxbuf[3] != (byte) 0)
        {
          Log_file.Log("MDCH Address:{mdch_addr} OPTIONAL COMMAND RESPONSE PACKET: IN CORRECT SOURCE ADDRESS");
          num2 = (byte) 1;
        }
        if (Pkt_length != (ushort) 16 /*0x10*/)
        {
          Log_file.Log("MDCH Address:{mdch_addr} OPTIONAL COMMAND RESPONSE PACKET: IN VALID DATA LENGTH");
          num2 = (byte) 1;
        }
        if (mdch_api.rxbuf[7] != (byte) 203)
        {
          Log_file.Log("MDCH Address:{mdch_addr} OPTIONAL COMMAND RESPONSE PACKET: IN VALID FUNCTION CODE");
          num2 = (byte) 1;
        }
        switch (mdch_api.rxbuf[8])
        {
          case 0:
            Log_file.Log("MDCH Address:{mdch_addr} OPTIONAL COMMAND PACKET: Packet Received and Processed Successfully");
            break;
          case 2:
            Log_file.Log("MDCH Address:{mdch_addr} OPTIONAL COMMAND PACKET: CRC FAIL");
            num2 = (byte) 1;
            break;
          case 6:
            Log_file.Log("MDCH Address:{mdch_addr} OPTIONAL COMMAND PACKET: IN VALID FUNCTION CODE");
            num2 = (byte) 1;
            break;
          case 35:
            Log_file.Log("MDCH Address:{mdch_addr} OPTIONAL COMMAND PACKET: IN VALID DATA LENGTH");
            num2 = (byte) 1;
            break;
        }
        manu_id[0] = mdch_api.rxbuf[9];
        manu_id[1] = mdch_api.rxbuf[10];
        manu_id[2] = mdch_api.rxbuf[11];
        manu_id[3] = mdch_api.rxbuf[12];
        manu_id[4] = mdch_api.rxbuf[13];
        if (num2 != (byte) 0)
        {
          Log_file.Log("MDCH Address:{mdch_addr} OPTIONAL COMMAND IS UNSUCCESSFUL");
          num1 = (byte) 0;
        }
        else
        {
          Log_file.Log("MDCH Address:{mdch_addr} OPTIONAL COMMAND IS SUCCESSFUL");
          num1 = (byte) 1;
        }
      }
      else
      {
        Log_file.Log("MDCH Address:{mdch_addr} LINK FAILURE or ADDRESSED MDCH DOESN'T EXIST");
        num1 = (byte) 2;
      }
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num3 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    return num1;
  }
}

}