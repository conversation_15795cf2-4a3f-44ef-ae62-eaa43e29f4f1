// Decompiled with JetBrains decompiler
// Type: ipis.frmStationDetails
// Assembly: ipis, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 4CD64086-73CD-4B2B-B45F-8D321DAE3085
// Assembly location: E:\_work\Rail\Docs\ninjamedia\ninjamedia\ipis.exe

using ipis.My;
using Microsoft.VisualBasic;
using Microsoft.VisualBasic.CompilerServices;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Runtime.CompilerServices;
using System.Text;
using System.Windows.Forms;

namespace ipis
{

[DesignerGenerated]
public class frmStationDetails : Form
{
  private static List<WeakReference> __ENCList = new List<WeakReference>();
  private IContainer components;
  [AccessedThroughProperty("Label8")]
  private Label _Label8;
  [AccessedThroughProperty("txtAgdbMsg3")]
  private TextBox _txtAgdbMsg3;
  [AccessedThroughProperty("txtAgdbMsg2")]
  private TextBox _txtAgdbMsg2;
  [AccessedThroughProperty("txtAgdbMsg1")]
  private TextBox _txtAgdbMsg1;
  [AccessedThroughProperty("btnExit")]
  private Button _btnExit;
  [AccessedThroughProperty("btnSave")]
  private Button _btnSave;
  [AccessedThroughProperty("txtRegionCode")]
  private TextBox _txtRegionCode;
  [AccessedThroughProperty("Label4")]
  private Label _Label4;
  [AccessedThroughProperty("txtRegionName")]
  private TextBox _txtRegionName;
  [AccessedThroughProperty("Label3")]
  private Label _Label3;
  [AccessedThroughProperty("txtStationCode")]
  private TextBox _txtStationCode;
  [AccessedThroughProperty("Label2")]
  private Label _Label2;
  [AccessedThroughProperty("txtStationName")]
  private TextBox _txtStationName;
  [AccessedThroughProperty("Label1")]
  private Label _Label1;
  [AccessedThroughProperty("Label10")]
  private Label _Label10;
  [AccessedThroughProperty("txtMldbMsg3")]
  private TextBox _txtMldbMsg3;
  [AccessedThroughProperty("txtMldbMsg1")]
  private TextBox _txtMldbMsg1;
  [AccessedThroughProperty("txtPdbMsg1")]
  private TextBox _txtPdbMsg1;
  [AccessedThroughProperty("Label14")]
  private Label _Label14;
  [AccessedThroughProperty("txtMldbMsg2")]
  private TextBox _txtMldbMsg2;
  [AccessedThroughProperty("cmbPfnoCgdb")]
  private ComboBox _cmbPfnoCgdb;
  [AccessedThroughProperty("lblPfnoDiag")]
  private Label _lblPfnoDiag;
  [AccessedThroughProperty("btnSendCgdb")]
  private Button _btnSendCgdb;
  [AccessedThroughProperty("Panel1")]
  private Panel _Panel1;
  [AccessedThroughProperty("Panel2")]
  private Panel _Panel2;
  [AccessedThroughProperty("btnSendAgdb")]
  private Button _btnSendAgdb;
  [AccessedThroughProperty("cmbPfnoAgdb")]
  private ComboBox _cmbPfnoAgdb;
  [AccessedThroughProperty("Label16")]
  private Label _Label16;
  [AccessedThroughProperty("Panel3")]
  private Panel _Panel3;
  [AccessedThroughProperty("btnSendPdb")]
  private Button _btnSendPdb;
  [AccessedThroughProperty("cmbPfnoPdb")]
  private ComboBox _cmbPfnoPdb;
  [AccessedThroughProperty("Label17")]
  private Label _Label17;
  [AccessedThroughProperty("Panel4")]
  private Panel _Panel4;
  [AccessedThroughProperty("lblAddrDiag")]
  private Label _lblAddrDiag;
  [AccessedThroughProperty("cmbAddrMldb")]
  private ComboBox _cmbAddrMldb;
  [AccessedThroughProperty("lblNameDiag")]
  private Label _lblNameDiag;
  [AccessedThroughProperty("cmbNameMldb")]
  private ComboBox _cmbNameMldb;
  [AccessedThroughProperty("btnSendMldb")]
  private Button _btnSendMldb;
  [AccessedThroughProperty("chkAllPfnoAgdb")]
  private CheckBox _chkAllPfnoAgdb;
  [AccessedThroughProperty("Label18")]
  private Label _Label18;
  [AccessedThroughProperty("cmbAgdbAddr")]
  private ComboBox _cmbAgdbAddr;
  [AccessedThroughProperty("Label19")]
  private Label _Label19;
  [AccessedThroughProperty("cmbAgdbName")]
  private ComboBox _cmbAgdbName;
  [AccessedThroughProperty("Label9")]
  private Label _Label9;
  [AccessedThroughProperty("chkAgdbLine1")]
  private CheckBox _chkAgdbLine1;
  [AccessedThroughProperty("Label20")]
  private Label _Label20;
  [AccessedThroughProperty("chkAgdbLine3")]
  private CheckBox _chkAgdbLine3;
  [AccessedThroughProperty("chkAgdbLine2")]
  private CheckBox _chkAgdbLine2;
  [AccessedThroughProperty("chkMldbLine3")]
  private CheckBox _chkMldbLine3;
  [AccessedThroughProperty("chkMldbLine2")]
  private CheckBox _chkMldbLine2;
  [AccessedThroughProperty("chkMldbLine1")]
  private CheckBox _chkMldbLine1;
  [AccessedThroughProperty("chkPdbLine1")]
  private CheckBox _chkPdbLine1;
  [AccessedThroughProperty("panAgdb")]
  private Panel _panAgdb;
  [AccessedThroughProperty("panPdb")]
  private Panel _panPdb;
  [AccessedThroughProperty("panMldb")]
  private Panel _panMldb;
  [AccessedThroughProperty("panCgdb")]
  private Panel _panCgdb;
  [AccessedThroughProperty("txtCgdbMultiAddr")]
  private TextBox _txtCgdbMultiAddr;
  [AccessedThroughProperty("lblCgdbAddr")]
  private Label _lblCgdbAddr;
  [AccessedThroughProperty("txtPdbMultiAddr")]
  private TextBox _txtPdbMultiAddr;
  [AccessedThroughProperty("Label5")]
  private Label _Label5;
  [AccessedThroughProperty("txtSrdPfnoAgdb")]
  private TextBox _txtSrdPfnoAgdb;
  [AccessedThroughProperty("lblSrdPfAgdb")]
  private Label _lblSrdPfAgdb;
  [AccessedThroughProperty("txtSrdPfnoPdb")]
  private TextBox _txtSrdPfnoPdb;
  [AccessedThroughProperty("lblSrdPfPdb")]
  private Label _lblSrdPfPdb;

  [DebuggerNonUserCode]
  static frmStationDetails()
  {
  }

  [DebuggerNonUserCode]
  public frmStationDetails()
  {
    this.FormClosed += new FormClosedEventHandler(this.frmStationDetails_FormClosed);
    this.Load += new EventHandler(this.frmStationDetails_Load);
    frmStationDetails.__ENCAddToList((object) this);
    this.InitializeComponent();
  }

  [DebuggerNonUserCode]
  private static void __ENCAddToList(object value)
  {
    lock (frmStationDetails.__ENCList)
    {
      if (frmStationDetails.__ENCList.Count == frmStationDetails.__ENCList.Capacity)
      {
        int index1 = 0;
        int num = checked (frmStationDetails.__ENCList.Count - 1);
        int index2 = 0;
        while (index2 <= num)
        {
          if (frmStationDetails.__ENCList[index2].IsAlive)
          {
            if (index2 != index1)
              frmStationDetails.__ENCList[index1] = frmStationDetails.__ENCList[index2];
            checked { ++index1; }
          }
          checked { ++index2; }
        }
        frmStationDetails.__ENCList.RemoveRange(index1, checked (frmStationDetails.__ENCList.Count - index1));
        frmStationDetails.__ENCList.Capacity = frmStationDetails.__ENCList.Count;
      }
      frmStationDetails.__ENCList.Add(new WeakReference(RuntimeHelpers.GetObjectValue(value)));
    }
  }

  [DebuggerNonUserCode]
  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  [DebuggerStepThrough]
  private void InitializeComponent()
  {
    this.Label8 = new Label();
    this.txtAgdbMsg3 = new TextBox();
    this.txtAgdbMsg2 = new TextBox();
    this.txtAgdbMsg1 = new TextBox();
    this.btnExit = new Button();
    this.btnSave = new Button();
    this.txtRegionCode = new TextBox();
    this.Label4 = new Label();
    this.txtRegionName = new TextBox();
    this.Label3 = new Label();
    this.txtStationCode = new TextBox();
    this.Label2 = new Label();
    this.txtStationName = new TextBox();
    this.Label1 = new Label();
    this.Label10 = new Label();
    this.txtMldbMsg3 = new TextBox();
    this.txtMldbMsg1 = new TextBox();
    this.txtPdbMsg1 = new TextBox();
    this.Label14 = new Label();
    this.txtMldbMsg2 = new TextBox();
    this.cmbPfnoCgdb = new ComboBox();
    this.lblPfnoDiag = new Label();
    this.btnSendCgdb = new Button();
    this.Panel1 = new Panel();
    this.panCgdb = new Panel();
    this.txtCgdbMultiAddr = new TextBox();
    this.lblCgdbAddr = new Label();
    this.Label9 = new Label();
    this.Panel2 = new Panel();
    this.panAgdb = new Panel();
    this.txtSrdPfnoAgdb = new TextBox();
    this.lblSrdPfAgdb = new Label();
    this.chkAllPfnoAgdb = new CheckBox();
    this.Label16 = new Label();
    this.Label19 = new Label();
    this.Label18 = new Label();
    this.btnSendAgdb = new Button();
    this.cmbAgdbAddr = new ComboBox();
    this.cmbAgdbName = new ComboBox();
    this.cmbPfnoAgdb = new ComboBox();
    this.chkAgdbLine3 = new CheckBox();
    this.chkAgdbLine2 = new CheckBox();
    this.chkAgdbLine1 = new CheckBox();
    this.Panel3 = new Panel();
    this.panPdb = new Panel();
    this.txtSrdPfnoPdb = new TextBox();
    this.lblSrdPfPdb = new Label();
    this.txtPdbMultiAddr = new TextBox();
    this.Label5 = new Label();
    this.btnSendPdb = new Button();
    this.Label17 = new Label();
    this.cmbPfnoPdb = new ComboBox();
    this.chkPdbLine1 = new CheckBox();
    this.Panel4 = new Panel();
    this.panMldb = new Panel();
    this.btnSendMldb = new Button();
    this.cmbNameMldb = new ComboBox();
    this.lblNameDiag = new Label();
    this.cmbAddrMldb = new ComboBox();
    this.lblAddrDiag = new Label();
    this.chkMldbLine3 = new CheckBox();
    this.chkMldbLine2 = new CheckBox();
    this.chkMldbLine1 = new CheckBox();
    this.Label20 = new Label();
    this.Panel1.SuspendLayout();
    this.panCgdb.SuspendLayout();
    this.Panel2.SuspendLayout();
    this.panAgdb.SuspendLayout();
    this.Panel3.SuspendLayout();
    this.panPdb.SuspendLayout();
    this.Panel4.SuspendLayout();
    this.panMldb.SuspendLayout();
    this.SuspendLayout();
    this.Label8.AutoSize = true;
    this.Label8.Font = new Font("Microsoft Sans Serif", 14.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.Label8.ForeColor = Color.RoyalBlue;
    Label label8_1 = this.Label8;
    Point point1 = new Point(351, 0);
    Point point2 = point1;
    label8_1.Location = point2;
    this.Label8.Name = "Label8";
    Label label8_2 = this.Label8;
    Size size1 = new Size(66, 24);
    Size size2 = size1;
    label8_2.Size = size2;
    this.Label8.TabIndex = 275;
    this.Label8.Text = "AGDB";
    this.txtAgdbMsg3.CharacterCasing = CharacterCasing.Upper;
    this.txtAgdbMsg3.Font = new Font("Microsoft Sans Serif", 12f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtAgdbMsg3_1 = this.txtAgdbMsg3;
    point1 = new Point(151, 129);
    Point point3 = point1;
    txtAgdbMsg3_1.Location = point3;
    this.txtAgdbMsg3.MaxLength = 40;
    this.txtAgdbMsg3.Name = "txtAgdbMsg3";
    TextBox txtAgdbMsg3_2 = this.txtAgdbMsg3;
    size1 = new Size(360, 26);
    Size size3 = size1;
    txtAgdbMsg3_2.Size = size3;
    this.txtAgdbMsg3.TabIndex = 8;
    this.txtAgdbMsg2.CharacterCasing = CharacterCasing.Upper;
    this.txtAgdbMsg2.Font = new Font("Microsoft Sans Serif", 12f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtAgdbMsg2_1 = this.txtAgdbMsg2;
    point1 = new Point(151, 88);
    Point point4 = point1;
    txtAgdbMsg2_1.Location = point4;
    this.txtAgdbMsg2.MaxLength = 40;
    this.txtAgdbMsg2.Name = "txtAgdbMsg2";
    TextBox txtAgdbMsg2_2 = this.txtAgdbMsg2;
    size1 = new Size(360, 26);
    Size size4 = size1;
    txtAgdbMsg2_2.Size = size4;
    this.txtAgdbMsg2.TabIndex = 7;
    this.txtAgdbMsg1.CharacterCasing = CharacterCasing.Upper;
    this.txtAgdbMsg1.Font = new Font("Microsoft Sans Serif", 12f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtAgdbMsg1_1 = this.txtAgdbMsg1;
    point1 = new Point(151, 47);
    Point point5 = point1;
    txtAgdbMsg1_1.Location = point5;
    this.txtAgdbMsg1.MaxLength = 40;
    this.txtAgdbMsg1.Name = "txtAgdbMsg1";
    TextBox txtAgdbMsg1_2 = this.txtAgdbMsg1;
    size1 = new Size(360, 26);
    Size size5 = size1;
    txtAgdbMsg1_2.Size = size5;
    this.txtAgdbMsg1.TabIndex = 6;
    this.btnExit.BackColor = Color.SeaShell;
    this.btnExit.DialogResult = DialogResult.Cancel;
    this.btnExit.Font = new Font("Microsoft Sans Serif", 12f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnExit1 = this.btnExit;
    point1 = new Point(489, 608);
    Point point6 = point1;
    btnExit1.Location = point6;
    this.btnExit.Name = "btnExit";
    Button btnExit2 = this.btnExit;
    size1 = new Size(60, 25);
    Size size6 = size1;
    btnExit2.Size = size6;
    this.btnExit.TabIndex = 10;
    this.btnExit.Text = "Exit";
    this.btnExit.UseVisualStyleBackColor = false;
    this.btnSave.BackColor = Color.SeaShell;
    this.btnSave.DialogResult = DialogResult.Cancel;
    this.btnSave.Font = new Font("Microsoft Sans Serif", 12f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnSave1 = this.btnSave;
    point1 = new Point(369, 608);
    Point point7 = point1;
    btnSave1.Location = point7;
    this.btnSave.Name = "btnSave";
    Button btnSave2 = this.btnSave;
    size1 = new Size(60, 25);
    Size size7 = size1;
    btnSave2.Size = size7;
    this.btnSave.TabIndex = 9;
    this.btnSave.Text = "Save";
    this.btnSave.UseVisualStyleBackColor = false;
    this.txtRegionCode.CharacterCasing = CharacterCasing.Upper;
    this.txtRegionCode.Font = new Font("Microsoft Sans Serif", 12f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtRegionCode1 = this.txtRegionCode;
    point1 = new Point(406, 81);
    Point point8 = point1;
    txtRegionCode1.Location = point8;
    this.txtRegionCode.MaxLength = 4;
    this.txtRegionCode.Name = "txtRegionCode";
    TextBox txtRegionCode2 = this.txtRegionCode;
    size1 = new Size(88, 26);
    Size size8 = size1;
    txtRegionCode2.Size = size8;
    this.txtRegionCode.TabIndex = 4;
    this.Label4.AutoSize = true;
    this.Label4.Font = new Font("Microsoft Sans Serif", 12f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label label4_1 = this.Label4;
    point1 = new Point(283, 81);
    Point point9 = point1;
    label4_1.Location = point9;
    this.Label4.Name = "Label4";
    Label label4_2 = this.Label4;
    size1 = new Size(117, 20);
    Size size9 = size1;
    label4_2.Size = size9;
    this.Label4.TabIndex = 265;
    this.Label4.Text = "Railway Code";
    this.txtRegionName.Font = new Font("Microsoft Sans Serif", 12f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtRegionName1 = this.txtRegionName;
    point1 = new Point(132, 78);
    Point point10 = point1;
    txtRegionName1.Location = point10;
    this.txtRegionName.MaxLength = 18;
    this.txtRegionName.Name = "txtRegionName";
    TextBox txtRegionName2 = this.txtRegionName;
    size1 = new Size(134, 26);
    Size size10 = size1;
    txtRegionName2.Size = size10;
    this.txtRegionName.TabIndex = 2;
    this.Label3.AutoSize = true;
    this.Label3.Font = new Font("Microsoft Sans Serif", 12f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label label3_1 = this.Label3;
    point1 = new Point(0, 78);
    Point point11 = point1;
    label3_1.Location = point11;
    this.Label3.Name = "Label3";
    Label label3_2 = this.Label3;
    size1 = new Size(126, 20);
    Size size11 = size1;
    label3_2.Size = size11;
    this.Label3.TabIndex = 263;
    this.Label3.Text = "Railway Name ";
    this.txtStationCode.CharacterCasing = CharacterCasing.Upper;
    this.txtStationCode.Font = new Font("Microsoft Sans Serif", 12f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtStationCode1 = this.txtStationCode;
    point1 = new Point(406, 24);
    Point point12 = point1;
    txtStationCode1.Location = point12;
    this.txtStationCode.MaxLength = 4;
    this.txtStationCode.Name = "txtStationCode";
    TextBox txtStationCode2 = this.txtStationCode;
    size1 = new Size(88, 26);
    Size size12 = size1;
    txtStationCode2.Size = size12;
    this.txtStationCode.TabIndex = 3;
    this.Label2.AutoSize = true;
    this.Label2.Font = new Font("Microsoft Sans Serif", 12f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label label2_1 = this.Label2;
    point1 = new Point(286, 27);
    Point point13 = point1;
    label2_1.Location = point13;
    this.Label2.Name = "Label2";
    Label label2_2 = this.Label2;
    size1 = new Size(114, 20);
    Size size13 = size1;
    label2_2.Size = size13;
    this.Label2.TabIndex = 261;
    this.Label2.Text = "Station Code";
    this.txtStationName.Font = new Font("Microsoft Sans Serif", 12f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtStationName1 = this.txtStationName;
    point1 = new Point(132, 27);
    Point point14 = point1;
    txtStationName1.Location = point14;
    this.txtStationName.MaxLength = 18;
    this.txtStationName.Name = "txtStationName";
    TextBox txtStationName2 = this.txtStationName;
    size1 = new Size(134, 26);
    Size size14 = size1;
    txtStationName2.Size = size14;
    this.txtStationName.TabIndex = 1;
    this.Label1.AutoSize = true;
    this.Label1.Font = new Font("Microsoft Sans Serif", 12f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Label label1_1 = this.Label1;
    point1 = new Point(8, 27);
    Point point15 = point1;
    label1_1.Location = point15;
    this.Label1.Name = "Label1";
    Label label1_2 = this.Label1;
    size1 = new Size(118, 20);
    Size size15 = size1;
    label1_2.Size = size15;
    this.Label1.TabIndex = 259;
    this.Label1.Text = "Station Name";
    this.Label10.AutoSize = true;
    this.Label10.Font = new Font("Microsoft Sans Serif", 14.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.Label10.ForeColor = Color.RoyalBlue;
    Label label10_1 = this.Label10;
    point1 = new Point(359, 1);
    Point point16 = point1;
    label10_1.Location = point16;
    this.Label10.Name = "Label10";
    Label label10_2 = this.Label10;
    size1 = new Size(56, 24);
    Size size16 = size1;
    label10_2.Size = size16;
    this.Label10.TabIndex = 283;
    this.Label10.Text = "PDB ";
    this.txtMldbMsg3.CharacterCasing = CharacterCasing.Upper;
    this.txtMldbMsg3.Font = new Font("Microsoft Sans Serif", 12f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtMldbMsg3_1 = this.txtMldbMsg3;
    point1 = new Point(151, 130);
    Point point17 = point1;
    txtMldbMsg3_1.Location = point17;
    this.txtMldbMsg3.MaxLength = 33;
    this.txtMldbMsg3.Name = "txtMldbMsg3";
    TextBox txtMldbMsg3_2 = this.txtMldbMsg3;
    size1 = new Size(360, 26);
    Size size17 = size1;
    txtMldbMsg3_2.Size = size17;
    this.txtMldbMsg3.TabIndex = 279;
    this.txtMldbMsg1.CharacterCasing = CharacterCasing.Upper;
    this.txtMldbMsg1.Font = new Font("Microsoft Sans Serif", 12f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtMldbMsg1_1 = this.txtMldbMsg1;
    point1 = new Point(151, 29);
    Point point18 = point1;
    txtMldbMsg1_1.Location = point18;
    this.txtMldbMsg1.MaxLength = 33;
    this.txtMldbMsg1.Name = "txtMldbMsg1";
    TextBox txtMldbMsg1_2 = this.txtMldbMsg1;
    size1 = new Size(360, 26);
    Size size18 = size1;
    txtMldbMsg1_2.Size = size18;
    this.txtMldbMsg1.TabIndex = 278;
    this.txtPdbMsg1.CharacterCasing = CharacterCasing.Upper;
    this.txtPdbMsg1.Font = new Font("Microsoft Sans Serif", 12f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtPdbMsg1_1 = this.txtPdbMsg1;
    point1 = new Point(151, 28);
    Point point19 = point1;
    txtPdbMsg1_1.Location = point19;
    this.txtPdbMsg1.MaxLength = 33;
    this.txtPdbMsg1.Name = "txtPdbMsg1";
    TextBox txtPdbMsg1_2 = this.txtPdbMsg1;
    size1 = new Size(360, 26);
    Size size19 = size1;
    txtPdbMsg1_2.Size = size19;
    this.txtPdbMsg1.TabIndex = 277;
    this.Label14.AutoSize = true;
    this.Label14.Font = new Font("Microsoft Sans Serif", 14.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.Label14.ForeColor = Color.RoyalBlue;
    Label label14_1 = this.Label14;
    point1 = new Point(351, 0);
    Point point20 = point1;
    label14_1.Location = point20;
    this.Label14.Name = "Label14";
    Label label14_2 = this.Label14;
    size1 = new Size(71, 24);
    Size size20 = size1;
    label14_2.Size = size20;
    this.Label14.TabIndex = 284;
    this.Label14.Text = "MLDB ";
    this.txtMldbMsg2.CharacterCasing = CharacterCasing.Upper;
    this.txtMldbMsg2.Font = new Font("Microsoft Sans Serif", 12f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    TextBox txtMldbMsg2_1 = this.txtMldbMsg2;
    point1 = new Point(151, 81);
    Point point21 = point1;
    txtMldbMsg2_1.Location = point21;
    this.txtMldbMsg2.MaxLength = 33;
    this.txtMldbMsg2.Name = "txtMldbMsg2";
    TextBox txtMldbMsg2_2 = this.txtMldbMsg2;
    size1 = new Size(360, 26);
    Size size21 = size1;
    txtMldbMsg2_2.Size = size21;
    this.txtMldbMsg2.TabIndex = 285;
    this.cmbPfnoCgdb.DropDownStyle = ComboBoxStyle.DropDownList;
    this.cmbPfnoCgdb.FormattingEnabled = true;
    this.cmbPfnoCgdb.Items.AddRange(new object[24]
    {
      (object) "1",
      (object) "2",
      (object) "3",
      (object) "4",
      (object) "5",
      (object) "6",
      (object) "7",
      (object) "8",
      (object) "9",
      (object) "10",
      (object) "11",
      (object) "12",
      (object) "13",
      (object) "14",
      (object) "15",
      (object) "16",
      (object) "17",
      (object) "18",
      (object) "19",
      (object) "20",
      (object) "21",
      (object) "22",
      (object) "23",
      (object) "24"
    });
    ComboBox cmbPfnoCgdb1 = this.cmbPfnoCgdb;
    point1 = new Point(130, 10);
    Point point22 = point1;
    cmbPfnoCgdb1.Location = point22;
    this.cmbPfnoCgdb.Name = "cmbPfnoCgdb";
    ComboBox cmbPfnoCgdb2 = this.cmbPfnoCgdb;
    size1 = new Size(64 /*0x40*/, 21);
    Size size22 = size1;
    cmbPfnoCgdb2.Size = size22;
    this.cmbPfnoCgdb.TabIndex = 295;
    this.lblPfnoDiag.AutoSize = true;
    this.lblPfnoDiag.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.lblPfnoDiag.ForeColor = SystemColors.WindowText;
    Label lblPfnoDiag1 = this.lblPfnoDiag;
    point1 = new Point(21, 11);
    Point point23 = point1;
    lblPfnoDiag1.Location = point23;
    this.lblPfnoDiag.Name = "lblPfnoDiag";
    Label lblPfnoDiag2 = this.lblPfnoDiag;
    size1 = new Size(89, 16 /*0x10*/);
    Size size23 = size1;
    lblPfnoDiag2.Size = size23;
    this.lblPfnoDiag.TabIndex = 296;
    this.lblPfnoDiag.Text = "Platform No";
    this.btnSendCgdb.BackColor = Color.LightGray;
    this.btnSendCgdb.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnSendCgdb1 = this.btnSendCgdb;
    point1 = new Point(262, 27);
    Point point24 = point1;
    btnSendCgdb1.Location = point24;
    this.btnSendCgdb.Name = "btnSendCgdb";
    Button btnSendCgdb2 = this.btnSendCgdb;
    size1 = new Size(75, 23);
    Size size24 = size1;
    btnSendCgdb2.Size = size24;
    this.btnSendCgdb.TabIndex = 297;
    this.btnSendCgdb.Text = "Send";
    this.btnSendCgdb.UseVisualStyleBackColor = false;
    this.Panel1.BorderStyle = BorderStyle.Fixed3D;
    this.Panel1.Controls.Add((Control) this.panCgdb);
    this.Panel1.Controls.Add((Control) this.txtStationName);
    this.Panel1.Controls.Add((Control) this.Label1);
    this.Panel1.Controls.Add((Control) this.Label2);
    this.Panel1.Controls.Add((Control) this.txtStationCode);
    this.Panel1.Controls.Add((Control) this.Label3);
    this.Panel1.Controls.Add((Control) this.txtRegionName);
    this.Panel1.Controls.Add((Control) this.Label4);
    this.Panel1.Controls.Add((Control) this.txtRegionCode);
    this.Panel1.Controls.Add((Control) this.Label9);
    Panel panel1_1 = this.Panel1;
    point1 = new Point(12, 27);
    Point point25 = point1;
    panel1_1.Location = point25;
    this.Panel1.Name = "Panel1";
    Panel panel1_2 = this.Panel1;
    size1 = new Size(940, 119);
    Size size25 = size1;
    panel1_2.Size = size25;
    this.Panel1.TabIndex = 298;
    this.panCgdb.BorderStyle = BorderStyle.Fixed3D;
    this.panCgdb.Controls.Add((Control) this.txtCgdbMultiAddr);
    this.panCgdb.Controls.Add((Control) this.lblCgdbAddr);
    this.panCgdb.Controls.Add((Control) this.btnSendCgdb);
    this.panCgdb.Controls.Add((Control) this.lblPfnoDiag);
    this.panCgdb.Controls.Add((Control) this.cmbPfnoCgdb);
    Panel panCgdb1 = this.panCgdb;
    point1 = new Point(534, 17);
    Point point26 = point1;
    panCgdb1.Location = point26;
    this.panCgdb.Name = "panCgdb";
    Panel panCgdb2 = this.panCgdb;
    size1 = new Size(399, 87);
    Size size26 = size1;
    panCgdb2.Size = size26;
    this.panCgdb.TabIndex = 298;
    TextBox txtCgdbMultiAddr1 = this.txtCgdbMultiAddr;
    point1 = new Point(130, 45);
    Point point27 = point1;
    txtCgdbMultiAddr1.Location = point27;
    this.txtCgdbMultiAddr.Name = "txtCgdbMultiAddr";
    this.txtCgdbMultiAddr.ReadOnly = true;
    TextBox txtCgdbMultiAddr2 = this.txtCgdbMultiAddr;
    size1 = new Size(62, 20);
    Size size27 = size1;
    txtCgdbMultiAddr2.Size = size27;
    this.txtCgdbMultiAddr.TabIndex = 308;
    this.lblCgdbAddr.AutoSize = true;
    this.lblCgdbAddr.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.lblCgdbAddr.ForeColor = SystemColors.WindowText;
    Label lblCgdbAddr1 = this.lblCgdbAddr;
    point1 = new Point(-3, 49);
    Point point28 = point1;
    lblCgdbAddr1.Location = point28;
    this.lblCgdbAddr.Name = "lblCgdbAddr";
    Label lblCgdbAddr2 = this.lblCgdbAddr;
    size1 = new Size(133, 16 /*0x10*/);
    Size size28 = size1;
    lblCgdbAddr2.Size = size28;
    this.lblCgdbAddr.TabIndex = 307;
    this.lblCgdbAddr.Text = "MultiCast Address";
    this.Label9.AutoSize = true;
    this.Label9.Font = new Font("Microsoft Sans Serif", 14.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.Label9.ForeColor = Color.RoyalBlue;
    Label label9_1 = this.Label9;
    point1 = new Point(351, -2);
    Point point29 = point1;
    label9_1.Location = point29;
    this.Label9.Name = "Label9";
    Label label9_2 = this.Label9;
    size1 = new Size(66, 24);
    Size size29 = size1;
    label9_2.Size = size29;
    this.Label9.TabIndex = 276;
    this.Label9.Text = "CGDB";
    this.Panel2.BorderStyle = BorderStyle.Fixed3D;
    this.Panel2.Controls.Add((Control) this.panAgdb);
    this.Panel2.Controls.Add((Control) this.chkAgdbLine3);
    this.Panel2.Controls.Add((Control) this.chkAgdbLine2);
    this.Panel2.Controls.Add((Control) this.chkAgdbLine1);
    this.Panel2.Controls.Add((Control) this.Label8);
    this.Panel2.Controls.Add((Control) this.txtAgdbMsg1);
    this.Panel2.Controls.Add((Control) this.txtAgdbMsg2);
    this.Panel2.Controls.Add((Control) this.txtAgdbMsg3);
    Panel panel2_1 = this.Panel2;
    point1 = new Point(12, 156);
    Point point30 = point1;
    panel2_1.Location = point30;
    this.Panel2.Name = "Panel2";
    Panel panel2_2 = this.Panel2;
    size1 = new Size(940, 161);
    Size size30 = size1;
    panel2_2.Size = size30;
    this.Panel2.TabIndex = 299;
    this.panAgdb.BorderStyle = BorderStyle.Fixed3D;
    this.panAgdb.Controls.Add((Control) this.txtSrdPfnoAgdb);
    this.panAgdb.Controls.Add((Control) this.lblSrdPfAgdb);
    this.panAgdb.Controls.Add((Control) this.chkAllPfnoAgdb);
    this.panAgdb.Controls.Add((Control) this.Label16);
    this.panAgdb.Controls.Add((Control) this.Label19);
    this.panAgdb.Controls.Add((Control) this.Label18);
    this.panAgdb.Controls.Add((Control) this.btnSendAgdb);
    this.panAgdb.Controls.Add((Control) this.cmbAgdbAddr);
    this.panAgdb.Controls.Add((Control) this.cmbAgdbName);
    this.panAgdb.Controls.Add((Control) this.cmbPfnoAgdb);
    Panel panAgdb1 = this.panAgdb;
    point1 = new Point(534, 14);
    Point point31 = point1;
    panAgdb1.Location = point31;
    this.panAgdb.Name = "panAgdb";
    Panel panAgdb2 = this.panAgdb;
    size1 = new Size(399, 140);
    Size size31 = size1;
    panAgdb2.Size = size31;
    this.panAgdb.TabIndex = 310;
    TextBox txtSrdPfnoAgdb1 = this.txtSrdPfnoAgdb;
    point1 = new Point(130, 37);
    Point point32 = point1;
    txtSrdPfnoAgdb1.Location = point32;
    this.txtSrdPfnoAgdb.Name = "txtSrdPfnoAgdb";
    this.txtSrdPfnoAgdb.ReadOnly = true;
    TextBox txtSrdPfnoAgdb2 = this.txtSrdPfnoAgdb;
    size1 = new Size(62, 20);
    Size size32 = size1;
    txtSrdPfnoAgdb2.Size = size32;
    this.txtSrdPfnoAgdb.TabIndex = 312;
    this.txtSrdPfnoAgdb.Visible = false;
    this.lblSrdPfAgdb.AutoSize = true;
    this.lblSrdPfAgdb.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.lblSrdPfAgdb.ForeColor = SystemColors.WindowText;
    Label lblSrdPfAgdb1 = this.lblSrdPfAgdb;
    point1 = new Point(13, 38);
    Point point33 = point1;
    lblSrdPfAgdb1.Location = point33;
    this.lblSrdPfAgdb.Name = "lblSrdPfAgdb";
    Label lblSrdPfAgdb2 = this.lblSrdPfAgdb;
    size1 = new Size(93, 16 /*0x10*/);
    Size size33 = size1;
    lblSrdPfAgdb2.Size = size33;
    this.lblSrdPfAgdb.TabIndex = 311;
    this.lblSrdPfAgdb.Text = "Shared Pfno";
    this.lblSrdPfAgdb.Visible = false;
    this.chkAllPfnoAgdb.AutoSize = true;
    this.chkAllPfnoAgdb.CheckAlign = ContentAlignment.MiddleRight;
    this.chkAllPfnoAgdb.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    CheckBox chkAllPfnoAgdb1 = this.chkAllPfnoAgdb;
    point1 = new Point(5, 7);
    Point point34 = point1;
    chkAllPfnoAgdb1.Location = point34;
    this.chkAllPfnoAgdb.Name = "chkAllPfnoAgdb";
    CheckBox chkAllPfnoAgdb2 = this.chkAllPfnoAgdb;
    size1 = new Size(138, 20);
    Size size34 = size1;
    chkAllPfnoAgdb2.Size = size34;
    this.chkAllPfnoAgdb.TabIndex = 302;
    this.chkAllPfnoAgdb.Text = "All Platform Nos";
    this.chkAllPfnoAgdb.UseVisualStyleBackColor = true;
    this.Label16.AutoSize = true;
    this.Label16.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.Label16.ForeColor = SystemColors.WindowText;
    Label label16_1 = this.Label16;
    point1 = new Point(185, 7);
    Point point35 = point1;
    label16_1.Location = point35;
    this.Label16.Name = "Label16";
    Label label16_2 = this.Label16;
    size1 = new Size(89, 16 /*0x10*/);
    Size size35 = size1;
    label16_2.Size = size35;
    this.Label16.TabIndex = 299;
    this.Label16.Text = "Platform No";
    this.Label19.AutoSize = true;
    this.Label19.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.Label19.ForeColor = SystemColors.WindowText;
    Label label19_1 = this.Label19;
    point1 = new Point(59, 105);
    Point point36 = point1;
    label19_1.Location = point36;
    this.Label19.Name = "Label19";
    Label label19_2 = this.Label19;
    size1 = new Size(49, 16 /*0x10*/);
    Size size36 = size1;
    label19_2.Size = size36;
    this.Label19.TabIndex = 304;
    this.Label19.Text = "Name";
    this.Label18.AutoSize = true;
    this.Label18.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.Label18.ForeColor = SystemColors.WindowText;
    Label label18_1 = this.Label18;
    point1 = new Point(42, 67);
    Point point37 = point1;
    label18_1.Location = point37;
    this.Label18.Name = "Label18";
    Label label18_2 = this.Label18;
    size1 = new Size(66, 16 /*0x10*/);
    Size size37 = size1;
    label18_2.Size = size37;
    this.Label18.TabIndex = 303;
    this.Label18.Text = "Address";
    this.btnSendAgdb.BackColor = Color.LightGray;
    this.btnSendAgdb.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnSendAgdb1 = this.btnSendAgdb;
    point1 = new Point(262, 67);
    Point point38 = point1;
    btnSendAgdb1.Location = point38;
    this.btnSendAgdb.Name = "btnSendAgdb";
    Button btnSendAgdb2 = this.btnSendAgdb;
    size1 = new Size(75, 23);
    Size size38 = size1;
    btnSendAgdb2.Size = size38;
    this.btnSendAgdb.TabIndex = 300;
    this.btnSendAgdb.Text = "Send";
    this.btnSendAgdb.UseVisualStyleBackColor = false;
    this.cmbAgdbAddr.DropDownStyle = ComboBoxStyle.DropDownList;
    this.cmbAgdbAddr.FormattingEnabled = true;
    ComboBox cmbAgdbAddr1 = this.cmbAgdbAddr;
    point1 = new Point(128 /*0x80*/, 66);
    Point point39 = point1;
    cmbAgdbAddr1.Location = point39;
    this.cmbAgdbAddr.Name = "cmbAgdbAddr";
    ComboBox cmbAgdbAddr2 = this.cmbAgdbAddr;
    size1 = new Size(85, 21);
    Size size39 = size1;
    cmbAgdbAddr2.Size = size39;
    this.cmbAgdbAddr.TabIndex = 306;
    this.cmbAgdbName.DropDownStyle = ComboBoxStyle.DropDownList;
    this.cmbAgdbName.FormattingEnabled = true;
    ComboBox cmbAgdbName1 = this.cmbAgdbName;
    point1 = new Point(128 /*0x80*/, 101);
    Point point40 = point1;
    cmbAgdbName1.Location = point40;
    this.cmbAgdbName.Name = "cmbAgdbName";
    ComboBox cmbAgdbName2 = this.cmbAgdbName;
    size1 = new Size(85, 21);
    Size size40 = size1;
    cmbAgdbName2.Size = size40;
    this.cmbAgdbName.TabIndex = 305;
    this.cmbPfnoAgdb.DropDownStyle = ComboBoxStyle.DropDownList;
    this.cmbPfnoAgdb.FormattingEnabled = true;
    this.cmbPfnoAgdb.Items.AddRange(new object[24]
    {
      (object) "1",
      (object) "2",
      (object) "3",
      (object) "4",
      (object) "5",
      (object) "6",
      (object) "7",
      (object) "8",
      (object) "9",
      (object) "10",
      (object) "11",
      (object) "12",
      (object) "13",
      (object) "14",
      (object) "15",
      (object) "16",
      (object) "17",
      (object) "18",
      (object) "19",
      (object) "20",
      (object) "21",
      (object) "22",
      (object) "23",
      (object) "24"
    });
    ComboBox cmbPfnoAgdb1 = this.cmbPfnoAgdb;
    point1 = new Point(294, 3);
    Point point41 = point1;
    cmbPfnoAgdb1.Location = point41;
    this.cmbPfnoAgdb.Name = "cmbPfnoAgdb";
    ComboBox cmbPfnoAgdb2 = this.cmbPfnoAgdb;
    size1 = new Size(64 /*0x40*/, 21);
    Size size41 = size1;
    cmbPfnoAgdb2.Size = size41;
    this.cmbPfnoAgdb.TabIndex = 298;
    this.chkAgdbLine3.AutoSize = true;
    this.chkAgdbLine3.Checked = true;
    this.chkAgdbLine3.CheckState = CheckState.Checked;
    this.chkAgdbLine3.Font = new Font("Microsoft Sans Serif", 12f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    CheckBox chkAgdbLine3_1 = this.chkAgdbLine3;
    point1 = new Point(35, 121);
    Point point42 = point1;
    chkAgdbLine3_1.Location = point42;
    this.chkAgdbLine3.Name = "chkAgdbLine3";
    CheckBox chkAgdbLine3_2 = this.chkAgdbLine3;
    size1 = new Size(72, 24);
    Size size42 = size1;
    chkAgdbLine3_2.Size = size42;
    this.chkAgdbLine3.TabIndex = 309;
    this.chkAgdbLine3.Text = "Line3";
    this.chkAgdbLine3.UseVisualStyleBackColor = true;
    this.chkAgdbLine2.AutoSize = true;
    this.chkAgdbLine2.Checked = true;
    this.chkAgdbLine2.CheckState = CheckState.Checked;
    this.chkAgdbLine2.Font = new Font("Microsoft Sans Serif", 12f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    CheckBox chkAgdbLine2_1 = this.chkAgdbLine2;
    point1 = new Point(35, 86);
    Point point43 = point1;
    chkAgdbLine2_1.Location = point43;
    this.chkAgdbLine2.Name = "chkAgdbLine2";
    CheckBox chkAgdbLine2_2 = this.chkAgdbLine2;
    size1 = new Size(72, 24);
    Size size43 = size1;
    chkAgdbLine2_2.Size = size43;
    this.chkAgdbLine2.TabIndex = 308;
    this.chkAgdbLine2.Text = "Line2";
    this.chkAgdbLine2.UseVisualStyleBackColor = true;
    this.chkAgdbLine1.AutoSize = true;
    this.chkAgdbLine1.Checked = true;
    this.chkAgdbLine1.CheckState = CheckState.Checked;
    this.chkAgdbLine1.Font = new Font("Microsoft Sans Serif", 12f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    CheckBox chkAgdbLine1_1 = this.chkAgdbLine1;
    point1 = new Point(35, 49);
    Point point44 = point1;
    chkAgdbLine1_1.Location = point44;
    this.chkAgdbLine1.Name = "chkAgdbLine1";
    CheckBox chkAgdbLine1_2 = this.chkAgdbLine1;
    size1 = new Size(72, 24);
    Size size44 = size1;
    chkAgdbLine1_2.Size = size44;
    this.chkAgdbLine1.TabIndex = 307;
    this.chkAgdbLine1.Text = "Line1";
    this.chkAgdbLine1.UseVisualStyleBackColor = true;
    this.Panel3.BorderStyle = BorderStyle.Fixed3D;
    this.Panel3.Controls.Add((Control) this.panPdb);
    this.Panel3.Controls.Add((Control) this.chkPdbLine1);
    this.Panel3.Controls.Add((Control) this.txtPdbMsg1);
    this.Panel3.Controls.Add((Control) this.Label10);
    Panel panel3_1 = this.Panel3;
    point1 = new Point(12, 323);
    Point point45 = point1;
    panel3_1.Location = point45;
    this.Panel3.Name = "Panel3";
    Panel panel3_2 = this.Panel3;
    size1 = new Size(940, 85);
    Size size45 = size1;
    panel3_2.Size = size45;
    this.Panel3.TabIndex = 300;
    this.panPdb.BorderStyle = BorderStyle.Fixed3D;
    this.panPdb.Controls.Add((Control) this.txtSrdPfnoPdb);
    this.panPdb.Controls.Add((Control) this.lblSrdPfPdb);
    this.panPdb.Controls.Add((Control) this.txtPdbMultiAddr);
    this.panPdb.Controls.Add((Control) this.Label5);
    this.panPdb.Controls.Add((Control) this.btnSendPdb);
    this.panPdb.Controls.Add((Control) this.Label17);
    this.panPdb.Controls.Add((Control) this.cmbPfnoPdb);
    Panel panPdb1 = this.panPdb;
    point1 = new Point(534, 3);
    Point point46 = point1;
    panPdb1.Location = point46;
    this.panPdb.Name = "panPdb";
    Panel panPdb2 = this.panPdb;
    size1 = new Size(399, 75);
    Size size46 = size1;
    panPdb2.Size = size46;
    this.panPdb.TabIndex = 309;
    TextBox txtSrdPfnoPdb1 = this.txtSrdPfnoPdb;
    point1 = new Point(294, 4);
    Point point47 = point1;
    txtSrdPfnoPdb1.Location = point47;
    this.txtSrdPfnoPdb.Name = "txtSrdPfnoPdb";
    this.txtSrdPfnoPdb.ReadOnly = true;
    TextBox txtSrdPfnoPdb2 = this.txtSrdPfnoPdb;
    size1 = new Size(62, 20);
    Size size47 = size1;
    txtSrdPfnoPdb2.Size = size47;
    this.txtSrdPfnoPdb.TabIndex = 312;
    this.txtSrdPfnoPdb.Visible = false;
    this.lblSrdPfPdb.AutoSize = true;
    this.lblSrdPfPdb.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.lblSrdPfPdb.ForeColor = SystemColors.WindowText;
    Label lblSrdPfPdb1 = this.lblSrdPfPdb;
    point1 = new Point(195, 5);
    Point point48 = point1;
    lblSrdPfPdb1.Location = point48;
    this.lblSrdPfPdb.Name = "lblSrdPfPdb";
    Label lblSrdPfPdb2 = this.lblSrdPfPdb;
    size1 = new Size(93, 16 /*0x10*/);
    Size size48 = size1;
    lblSrdPfPdb2.Size = size48;
    this.lblSrdPfPdb.TabIndex = 311;
    this.lblSrdPfPdb.Text = "Shared Pfno";
    this.lblSrdPfPdb.Visible = false;
    TextBox txtPdbMultiAddr1 = this.txtPdbMultiAddr;
    point1 = new Point(130, 46);
    Point point49 = point1;
    txtPdbMultiAddr1.Location = point49;
    this.txtPdbMultiAddr.Name = "txtPdbMultiAddr";
    this.txtPdbMultiAddr.ReadOnly = true;
    TextBox txtPdbMultiAddr2 = this.txtPdbMultiAddr;
    size1 = new Size(62, 20);
    Size size49 = size1;
    txtPdbMultiAddr2.Size = size49;
    this.txtPdbMultiAddr.TabIndex = 310;
    this.Label5.AutoSize = true;
    this.Label5.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.Label5.ForeColor = SystemColors.WindowText;
    Label label5_1 = this.Label5;
    point1 = new Point(-3, 46);
    Point point50 = point1;
    label5_1.Location = point50;
    this.Label5.Name = "Label5";
    Label label5_2 = this.Label5;
    size1 = new Size(133, 16 /*0x10*/);
    Size size50 = size1;
    label5_2.Size = size50;
    this.Label5.TabIndex = 309;
    this.Label5.Text = "MultiCast Address";
    this.btnSendPdb.BackColor = Color.LightGray;
    this.btnSendPdb.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnSendPdb1 = this.btnSendPdb;
    point1 = new Point(262, 43);
    Point point51 = point1;
    btnSendPdb1.Location = point51;
    this.btnSendPdb.Name = "btnSendPdb";
    Button btnSendPdb2 = this.btnSendPdb;
    size1 = new Size(75, 23);
    Size size51 = size1;
    btnSendPdb2.Size = size51;
    this.btnSendPdb.TabIndex = 303;
    this.btnSendPdb.Text = "Send";
    this.btnSendPdb.UseVisualStyleBackColor = false;
    this.Label17.AutoSize = true;
    this.Label17.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.Label17.ForeColor = SystemColors.WindowText;
    Label label17_1 = this.Label17;
    point1 = new Point(17, 8);
    Point point52 = point1;
    label17_1.Location = point52;
    this.Label17.Name = "Label17";
    Label label17_2 = this.Label17;
    size1 = new Size(89, 16 /*0x10*/);
    Size size52 = size1;
    label17_2.Size = size52;
    this.Label17.TabIndex = 302;
    this.Label17.Text = "Platform No";
    this.cmbPfnoPdb.DropDownStyle = ComboBoxStyle.DropDownList;
    this.cmbPfnoPdb.FormattingEnabled = true;
    this.cmbPfnoPdb.Items.AddRange(new object[24]
    {
      (object) "1",
      (object) "2",
      (object) "3",
      (object) "4",
      (object) "5",
      (object) "6",
      (object) "7",
      (object) "8",
      (object) "9",
      (object) "10",
      (object) "11",
      (object) "12",
      (object) "13",
      (object) "14",
      (object) "15",
      (object) "16",
      (object) "17",
      (object) "18",
      (object) "19",
      (object) "20",
      (object) "21",
      (object) "22",
      (object) "23",
      (object) "24"
    });
    ComboBox cmbPfnoPdb1 = this.cmbPfnoPdb;
    point1 = new Point(128 /*0x80*/, 3);
    Point point53 = point1;
    cmbPfnoPdb1.Location = point53;
    this.cmbPfnoPdb.Name = "cmbPfnoPdb";
    ComboBox cmbPfnoPdb2 = this.cmbPfnoPdb;
    size1 = new Size(49, 21);
    Size size53 = size1;
    cmbPfnoPdb2.Size = size53;
    this.cmbPfnoPdb.TabIndex = 301;
    this.chkPdbLine1.AutoSize = true;
    this.chkPdbLine1.Checked = true;
    this.chkPdbLine1.CheckState = CheckState.Checked;
    this.chkPdbLine1.Font = new Font("Microsoft Sans Serif", 12f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    CheckBox chkPdbLine1_1 = this.chkPdbLine1;
    point1 = new Point(35, 30);
    Point point54 = point1;
    chkPdbLine1_1.Location = point54;
    this.chkPdbLine1.Name = "chkPdbLine1";
    CheckBox chkPdbLine1_2 = this.chkPdbLine1;
    size1 = new Size(72, 24);
    Size size54 = size1;
    chkPdbLine1_2.Size = size54;
    this.chkPdbLine1.TabIndex = 308;
    this.chkPdbLine1.Text = "Line1";
    this.chkPdbLine1.UseVisualStyleBackColor = true;
    this.Panel4.BorderStyle = BorderStyle.Fixed3D;
    this.Panel4.Controls.Add((Control) this.panMldb);
    this.Panel4.Controls.Add((Control) this.chkMldbLine3);
    this.Panel4.Controls.Add((Control) this.chkMldbLine2);
    this.Panel4.Controls.Add((Control) this.chkMldbLine1);
    this.Panel4.Controls.Add((Control) this.txtMldbMsg3);
    this.Panel4.Controls.Add((Control) this.txtMldbMsg1);
    this.Panel4.Controls.Add((Control) this.Label14);
    this.Panel4.Controls.Add((Control) this.txtMldbMsg2);
    Panel panel4_1 = this.Panel4;
    point1 = new Point(12, 414);
    Point point55 = point1;
    panel4_1.Location = point55;
    this.Panel4.Name = "Panel4";
    Panel panel4_2 = this.Panel4;
    size1 = new Size(940, 174);
    Size size55 = size1;
    panel4_2.Size = size55;
    this.Panel4.TabIndex = 301;
    this.panMldb.BorderStyle = BorderStyle.Fixed3D;
    this.panMldb.Controls.Add((Control) this.btnSendMldb);
    this.panMldb.Controls.Add((Control) this.cmbNameMldb);
    this.panMldb.Controls.Add((Control) this.lblNameDiag);
    this.panMldb.Controls.Add((Control) this.cmbAddrMldb);
    this.panMldb.Controls.Add((Control) this.lblAddrDiag);
    Panel panMldb1 = this.panMldb;
    point1 = new Point(536, 43);
    Point point56 = point1;
    panMldb1.Location = point56;
    this.panMldb.Name = "panMldb";
    Panel panMldb2 = this.panMldb;
    size1 = new Size(397, 94);
    Size size56 = size1;
    panMldb2.Size = size56;
    this.panMldb.TabIndex = 313;
    this.btnSendMldb.BackColor = Color.LightGray;
    this.btnSendMldb.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    Button btnSendMldb1 = this.btnSendMldb;
    point1 = new Point(260, 36);
    Point point57 = point1;
    btnSendMldb1.Location = point57;
    this.btnSendMldb.Name = "btnSendMldb";
    Button btnSendMldb2 = this.btnSendMldb;
    size1 = new Size(75, 23);
    Size size57 = size1;
    btnSendMldb2.Size = size57;
    this.btnSendMldb.TabIndex = 304;
    this.btnSendMldb.Text = "Send";
    this.btnSendMldb.UseVisualStyleBackColor = false;
    this.cmbNameMldb.DropDownStyle = ComboBoxStyle.DropDownList;
    this.cmbNameMldb.FormattingEnabled = true;
    ComboBox cmbNameMldb1 = this.cmbNameMldb;
    point1 = new Point(117, 50);
    Point point58 = point1;
    cmbNameMldb1.Location = point58;
    this.cmbNameMldb.Name = "cmbNameMldb";
    ComboBox cmbNameMldb2 = this.cmbNameMldb;
    size1 = new Size(85, 21);
    Size size58 = size1;
    cmbNameMldb2.Size = size58;
    this.cmbNameMldb.TabIndex = 289;
    this.lblNameDiag.AutoSize = true;
    this.lblNameDiag.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.lblNameDiag.ForeColor = SystemColors.WindowText;
    Label lblNameDiag1 = this.lblNameDiag;
    point1 = new Point(57, 55);
    Point point59 = point1;
    lblNameDiag1.Location = point59;
    this.lblNameDiag.Name = "lblNameDiag";
    Label lblNameDiag2 = this.lblNameDiag;
    size1 = new Size(49, 16 /*0x10*/);
    Size size59 = size1;
    lblNameDiag2.Size = size59;
    this.lblNameDiag.TabIndex = 288;
    this.lblNameDiag.Text = "Name";
    this.cmbAddrMldb.DropDownStyle = ComboBoxStyle.DropDownList;
    this.cmbAddrMldb.FormattingEnabled = true;
    ComboBox cmbAddrMldb1 = this.cmbAddrMldb;
    point1 = new Point(117, 9);
    Point point60 = point1;
    cmbAddrMldb1.Location = point60;
    this.cmbAddrMldb.Name = "cmbAddrMldb";
    ComboBox cmbAddrMldb2 = this.cmbAddrMldb;
    size1 = new Size(85, 21);
    Size size60 = size1;
    cmbAddrMldb2.Size = size60;
    this.cmbAddrMldb.TabIndex = 290;
    this.lblAddrDiag.AutoSize = true;
    this.lblAddrDiag.Font = new Font("Microsoft Sans Serif", 9.75f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.lblAddrDiag.ForeColor = SystemColors.WindowText;
    Label lblAddrDiag1 = this.lblAddrDiag;
    point1 = new Point(40, 15);
    Point point61 = point1;
    lblAddrDiag1.Location = point61;
    this.lblAddrDiag.Name = "lblAddrDiag";
    Label lblAddrDiag2 = this.lblAddrDiag;
    size1 = new Size(66, 16 /*0x10*/);
    Size size61 = size1;
    lblAddrDiag2.Size = size61;
    this.lblAddrDiag.TabIndex = 287;
    this.lblAddrDiag.Text = "Address";
    this.chkMldbLine3.AutoSize = true;
    this.chkMldbLine3.Checked = true;
    this.chkMldbLine3.CheckState = CheckState.Checked;
    this.chkMldbLine3.Font = new Font("Microsoft Sans Serif", 12f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    CheckBox chkMldbLine3_1 = this.chkMldbLine3;
    point1 = new Point(35, 130);
    Point point62 = point1;
    chkMldbLine3_1.Location = point62;
    this.chkMldbLine3.Name = "chkMldbLine3";
    CheckBox chkMldbLine3_2 = this.chkMldbLine3;
    size1 = new Size(72, 24);
    Size size62 = size1;
    chkMldbLine3_2.Size = size62;
    this.chkMldbLine3.TabIndex = 312;
    this.chkMldbLine3.Text = "Line3";
    this.chkMldbLine3.UseVisualStyleBackColor = true;
    this.chkMldbLine2.AutoSize = true;
    this.chkMldbLine2.Checked = true;
    this.chkMldbLine2.CheckState = CheckState.Checked;
    this.chkMldbLine2.Font = new Font("Microsoft Sans Serif", 12f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    CheckBox chkMldbLine2_1 = this.chkMldbLine2;
    point1 = new Point(35, 83);
    Point point63 = point1;
    chkMldbLine2_1.Location = point63;
    this.chkMldbLine2.Name = "chkMldbLine2";
    CheckBox chkMldbLine2_2 = this.chkMldbLine2;
    size1 = new Size(72, 24);
    Size size63 = size1;
    chkMldbLine2_2.Size = size63;
    this.chkMldbLine2.TabIndex = 311;
    this.chkMldbLine2.Text = "Line2";
    this.chkMldbLine2.UseVisualStyleBackColor = true;
    this.chkMldbLine1.AutoSize = true;
    this.chkMldbLine1.Checked = true;
    this.chkMldbLine1.CheckState = CheckState.Checked;
    this.chkMldbLine1.Font = new Font("Microsoft Sans Serif", 12f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    CheckBox chkMldbLine1_1 = this.chkMldbLine1;
    point1 = new Point(35, 31 /*0x1F*/);
    Point point64 = point1;
    chkMldbLine1_1.Location = point64;
    this.chkMldbLine1.Name = "chkMldbLine1";
    CheckBox chkMldbLine1_2 = this.chkMldbLine1;
    size1 = new Size(72, 24);
    Size size64 = size1;
    chkMldbLine1_2.Size = size64;
    this.chkMldbLine1.TabIndex = 310;
    this.chkMldbLine1.Text = "Line1";
    this.chkMldbLine1.UseVisualStyleBackColor = true;
    this.Label20.AutoSize = true;
    this.Label20.Font = new Font("Microsoft Sans Serif", 14.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.Label20.ForeColor = Color.RoyalBlue;
    Label label20_1 = this.Label20;
    point1 = new Point(297, 0);
    Point point65 = point1;
    label20_1.Location = point65;
    this.Label20.Name = "Label20";
    Label label20_2 = this.Label20;
    size1 = new Size(228, 24);
    Size size65 = size1;
    label20_2.Size = size65;
    this.Label20.TabIndex = 302;
    this.Label20.Text = " DEFAULT MESSAGES";
    this.AcceptButton = (IButtonControl) this.btnSave;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.BackColor = Color.PowderBlue;
    this.CancelButton = (IButtonControl) this.btnExit;
    size1 = new Size(964, 635);
    this.ClientSize = size1;
    this.Controls.Add((Control) this.Label20);
    this.Controls.Add((Control) this.btnExit);
    this.Controls.Add((Control) this.Panel4);
    this.Controls.Add((Control) this.btnSave);
    this.Controls.Add((Control) this.Panel3);
    this.Controls.Add((Control) this.Panel2);
    this.Controls.Add((Control) this.Panel1);
    this.FormBorderStyle = FormBorderStyle.FixedToolWindow;
    this.Name = "frmStationDetails";
    this.Text = "Station Messages ";
    this.Panel1.ResumeLayout(false);
    this.Panel1.PerformLayout();
    this.panCgdb.ResumeLayout(false);
    this.panCgdb.PerformLayout();
    this.Panel2.ResumeLayout(false);
    this.Panel2.PerformLayout();
    this.panAgdb.ResumeLayout(false);
    this.panAgdb.PerformLayout();
    this.Panel3.ResumeLayout(false);
    this.Panel3.PerformLayout();
    this.panPdb.ResumeLayout(false);
    this.panPdb.PerformLayout();
    this.Panel4.ResumeLayout(false);
    this.Panel4.PerformLayout();
    this.panMldb.ResumeLayout(false);
    this.panMldb.PerformLayout();
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  internal virtual Label Label8
  {
    [DebuggerNonUserCode] get { return this._Label8; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Label8 = value; }
  }

  internal virtual TextBox txtAgdbMsg3
  {
    [DebuggerNonUserCode] get { return this._txtAgdbMsg3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtAgdbMsg3 = value;
    }
  }

  internal virtual TextBox txtAgdbMsg2
  {
    [DebuggerNonUserCode] get { return this._txtAgdbMsg2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtAgdbMsg2 = value;
    }
  }

  internal virtual TextBox txtAgdbMsg1
  {
    [DebuggerNonUserCode] get { return this._txtAgdbMsg1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtAgdbMsg1 = value;
    }
  }

  internal virtual Button btnExit
  {
    [DebuggerNonUserCode] get { return this._btnExit; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnExit_Click);
      if (this._btnExit != null)
        this._btnExit.Click -= eventHandler;
      this._btnExit = value;
      if (this._btnExit == null)
        return;
      this._btnExit.Click += eventHandler;
    }
  }

  internal virtual Button btnSave
  {
    [DebuggerNonUserCode] get { return this._btnSave; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnSave_Click);
      if (this._btnSave != null)
        this._btnSave.Click -= eventHandler;
      this._btnSave = value;
      if (this._btnSave == null)
        return;
      this._btnSave.Click += eventHandler;
    }
  }

  internal virtual TextBox txtRegionCode
  {
    [DebuggerNonUserCode] get { return this._txtRegionCode; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtRegionCode = value;
    }
  }

  internal virtual Label Label4
  {
    [DebuggerNonUserCode] get { return this._Label4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Label4 = value; }
  }

  internal virtual TextBox txtRegionName
  {
    [DebuggerNonUserCode] get { return this._txtRegionName; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtRegionName = value;
    }
  }

  internal virtual Label Label3
  {
    [DebuggerNonUserCode] get { return this._Label3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Label3 = value; }
  }

  internal virtual TextBox txtStationCode
  {
    [DebuggerNonUserCode] get { return this._txtStationCode; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtStationCode = value;
    }
  }

  internal virtual Label Label2
  {
    [DebuggerNonUserCode] get { return this._Label2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Label2 = value; }
  }

  internal virtual TextBox txtStationName
  {
    [DebuggerNonUserCode] get { return this._txtStationName; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtStationName = value;
    }
  }

  internal virtual Label Label1
  {
    [DebuggerNonUserCode] get { return this._Label1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Label1 = value; }
  }

  internal virtual Label Label10
  {
    [DebuggerNonUserCode] get { return this._Label10; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Label10 = value; }
  }

  internal virtual TextBox txtMldbMsg3
  {
    [DebuggerNonUserCode] get { return this._txtMldbMsg3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtMldbMsg3 = value;
    }
  }

  internal virtual TextBox txtMldbMsg1
  {
    [DebuggerNonUserCode] get { return this._txtMldbMsg1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtMldbMsg1 = value;
    }
  }

  internal virtual TextBox txtPdbMsg1
  {
    [DebuggerNonUserCode] get { return this._txtPdbMsg1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtPdbMsg1 = value;
    }
  }

  internal virtual Label Label14
  {
    [DebuggerNonUserCode] get { return this._Label14; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Label14 = value; }
  }

  internal virtual TextBox txtMldbMsg2
  {
    [DebuggerNonUserCode] get { return this._txtMldbMsg2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtMldbMsg2 = value;
    }
  }

  public virtual ComboBox cmbPfnoCgdb
  {
    [DebuggerNonUserCode] get { return this._cmbPfnoCgdb; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.cmbPfnoCgdb_SelectedIndexChanged);
      if (this._cmbPfnoCgdb != null)
        this._cmbPfnoCgdb.SelectedIndexChanged -= eventHandler;
      this._cmbPfnoCgdb = value;
      if (this._cmbPfnoCgdb == null)
        return;
      this._cmbPfnoCgdb.SelectedIndexChanged += eventHandler;
    }
  }

  internal virtual Label lblPfnoDiag
  {
    [DebuggerNonUserCode] get { return this._lblPfnoDiag; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblPfnoDiag = value;
    }
  }

  internal virtual Button btnSendCgdb
  {
    [DebuggerNonUserCode] get { return this._btnSendCgdb; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnSendCgdb_Click);
      if (this._btnSendCgdb != null)
        this._btnSendCgdb.Click -= eventHandler;
      this._btnSendCgdb = value;
      if (this._btnSendCgdb == null)
        return;
      this._btnSendCgdb.Click += eventHandler;
    }
  }

  internal virtual Panel Panel1
  {
    [DebuggerNonUserCode] get { return this._Panel1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Panel1 = value; }
  }

  internal virtual Panel Panel2
  {
    [DebuggerNonUserCode] get { return this._Panel2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Panel2 = value; }
  }

  internal virtual Button btnSendAgdb
  {
    [DebuggerNonUserCode] get { return this._btnSendAgdb; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnSendAgdb_Click);
      if (this._btnSendAgdb != null)
        this._btnSendAgdb.Click -= eventHandler;
      this._btnSendAgdb = value;
      if (this._btnSendAgdb == null)
        return;
      this._btnSendAgdb.Click += eventHandler;
    }
  }

  public virtual ComboBox cmbPfnoAgdb
  {
    [DebuggerNonUserCode] get { return this._cmbPfnoAgdb; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.cmbPfnoAgdb_SelectedIndexChanged);
      if (this._cmbPfnoAgdb != null)
        this._cmbPfnoAgdb.SelectedIndexChanged -= eventHandler;
      this._cmbPfnoAgdb = value;
      if (this._cmbPfnoAgdb == null)
        return;
      this._cmbPfnoAgdb.SelectedIndexChanged += eventHandler;
    }
  }

  internal virtual Label Label16
  {
    [DebuggerNonUserCode] get { return this._Label16; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Label16 = value; }
  }

  internal virtual Panel Panel3
  {
    [DebuggerNonUserCode] get { return this._Panel3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Panel3 = value; }
  }

  internal virtual Button btnSendPdb
  {
    [DebuggerNonUserCode] get { return this._btnSendPdb; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnSendPdb_Click);
      if (this._btnSendPdb != null)
        this._btnSendPdb.Click -= eventHandler;
      this._btnSendPdb = value;
      if (this._btnSendPdb == null)
        return;
      this._btnSendPdb.Click += eventHandler;
    }
  }

  public virtual ComboBox cmbPfnoPdb
  {
    [DebuggerNonUserCode] get { return this._cmbPfnoPdb; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.cmbPfnoPdb_SelectedIndexChanged);
      if (this._cmbPfnoPdb != null)
        this._cmbPfnoPdb.SelectedIndexChanged -= eventHandler;
      this._cmbPfnoPdb = value;
      if (this._cmbPfnoPdb == null)
        return;
      this._cmbPfnoPdb.SelectedIndexChanged += eventHandler;
    }
  }

  internal virtual Label Label17
  {
    [DebuggerNonUserCode] get { return this._Label17; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Label17 = value; }
  }

  internal virtual Panel Panel4
  {
    [DebuggerNonUserCode] get { return this._Panel4; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Panel4 = value; }
  }

  internal virtual Label lblAddrDiag
  {
    [DebuggerNonUserCode] get { return this._lblAddrDiag; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblAddrDiag = value;
    }
  }

  public virtual ComboBox cmbAddrMldb
  {
    [DebuggerNonUserCode] get { return this._cmbAddrMldb; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.cmbAddrMldb_SelectedIndexChanged);
      if (this._cmbAddrMldb != null)
        this._cmbAddrMldb.SelectedIndexChanged -= eventHandler;
      this._cmbAddrMldb = value;
      if (this._cmbAddrMldb == null)
        return;
      this._cmbAddrMldb.SelectedIndexChanged += eventHandler;
    }
  }

  internal virtual Label lblNameDiag
  {
    [DebuggerNonUserCode] get { return this._lblNameDiag; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblNameDiag = value;
    }
  }

  public virtual ComboBox cmbNameMldb
  {
    [DebuggerNonUserCode] get { return this._cmbNameMldb; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.cmbNameMldb_SelectedIndexChanged);
      if (this._cmbNameMldb != null)
        this._cmbNameMldb.SelectedIndexChanged -= eventHandler;
      this._cmbNameMldb = value;
      if (this._cmbNameMldb == null)
        return;
      this._cmbNameMldb.SelectedIndexChanged += eventHandler;
    }
  }

  internal virtual Button btnSendMldb
  {
    [DebuggerNonUserCode] get { return this._btnSendMldb; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.btnSendMldb_Click);
      if (this._btnSendMldb != null)
        this._btnSendMldb.Click -= eventHandler;
      this._btnSendMldb = value;
      if (this._btnSendMldb == null)
        return;
      this._btnSendMldb.Click += eventHandler;
    }
  }

  public virtual CheckBox chkAllPfnoAgdb
  {
    [DebuggerNonUserCode] get { return this._chkAllPfnoAgdb; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.chkAllPfnoAgdb_CheckedChanged);
      if (this._chkAllPfnoAgdb != null)
        this._chkAllPfnoAgdb.CheckedChanged -= eventHandler;
      this._chkAllPfnoAgdb = value;
      if (this._chkAllPfnoAgdb == null)
        return;
      this._chkAllPfnoAgdb.CheckedChanged += eventHandler;
    }
  }

  internal virtual Label Label18
  {
    [DebuggerNonUserCode] get { return this._Label18; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Label18 = value; }
  }

  public virtual ComboBox cmbAgdbAddr
  {
    [DebuggerNonUserCode] get { return this._cmbAgdbAddr; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.cmbAgdbAddr_SelectedIndexChanged);
      if (this._cmbAgdbAddr != null)
        this._cmbAgdbAddr.SelectedIndexChanged -= eventHandler;
      this._cmbAgdbAddr = value;
      if (this._cmbAgdbAddr == null)
        return;
      this._cmbAgdbAddr.SelectedIndexChanged += eventHandler;
    }
  }

  internal virtual Label Label19
  {
    [DebuggerNonUserCode] get { return this._Label19; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Label19 = value; }
  }

  public virtual ComboBox cmbAgdbName
  {
    [DebuggerNonUserCode] get { return this._cmbAgdbName; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.cmbAgdbName_SelectedIndexChanged);
      if (this._cmbAgdbName != null)
        this._cmbAgdbName.SelectedIndexChanged -= eventHandler;
      this._cmbAgdbName = value;
      if (this._cmbAgdbName == null)
        return;
      this._cmbAgdbName.SelectedIndexChanged += eventHandler;
    }
  }

  internal virtual Label Label9
  {
    [DebuggerNonUserCode] get { return this._Label9; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Label9 = value; }
  }

  internal virtual CheckBox chkAgdbLine1
  {
    [DebuggerNonUserCode] get { return this._chkAgdbLine1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.chkAgdbLine1_CheckedChanged);
      if (this._chkAgdbLine1 != null)
        this._chkAgdbLine1.CheckedChanged -= eventHandler;
      this._chkAgdbLine1 = value;
      if (this._chkAgdbLine1 == null)
        return;
      this._chkAgdbLine1.CheckedChanged += eventHandler;
    }
  }

  internal virtual Label Label20
  {
    [DebuggerNonUserCode] get { return this._Label20; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Label20 = value; }
  }

  internal virtual CheckBox chkAgdbLine3
  {
    [DebuggerNonUserCode] get { return this._chkAgdbLine3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.chkAgdbLine3_CheckedChanged);
      if (this._chkAgdbLine3 != null)
        this._chkAgdbLine3.CheckedChanged -= eventHandler;
      this._chkAgdbLine3 = value;
      if (this._chkAgdbLine3 == null)
        return;
      this._chkAgdbLine3.CheckedChanged += eventHandler;
    }
  }

  internal virtual CheckBox chkAgdbLine2
  {
    [DebuggerNonUserCode] get { return this._chkAgdbLine2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.chkAgdbLine2_CheckedChanged);
      if (this._chkAgdbLine2 != null)
        this._chkAgdbLine2.CheckedChanged -= eventHandler;
      this._chkAgdbLine2 = value;
      if (this._chkAgdbLine2 == null)
        return;
      this._chkAgdbLine2.CheckedChanged += eventHandler;
    }
  }

  internal virtual CheckBox chkMldbLine3
  {
    [DebuggerNonUserCode] get { return this._chkMldbLine3; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.chkMldbLine3_CheckedChanged);
      if (this._chkMldbLine3 != null)
        this._chkMldbLine3.CheckedChanged -= eventHandler;
      this._chkMldbLine3 = value;
      if (this._chkMldbLine3 == null)
        return;
      this._chkMldbLine3.CheckedChanged += eventHandler;
    }
  }

  internal virtual CheckBox chkMldbLine2
  {
    [DebuggerNonUserCode] get { return this._chkMldbLine2; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.chkMldbLine2_CheckedChanged);
      if (this._chkMldbLine2 != null)
        this._chkMldbLine2.CheckedChanged -= eventHandler;
      this._chkMldbLine2 = value;
      if (this._chkMldbLine2 == null)
        return;
      this._chkMldbLine2.CheckedChanged += eventHandler;
    }
  }

  internal virtual CheckBox chkMldbLine1
  {
    [DebuggerNonUserCode] get { return this._chkMldbLine1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.chkMldbLine1_CheckedChanged);
      if (this._chkMldbLine1 != null)
        this._chkMldbLine1.CheckedChanged -= eventHandler;
      this._chkMldbLine1 = value;
      if (this._chkMldbLine1 == null)
        return;
      this._chkMldbLine1.CheckedChanged += eventHandler;
    }
  }

  internal virtual CheckBox chkPdbLine1
  {
    [DebuggerNonUserCode] get { return this._chkPdbLine1; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      EventHandler eventHandler = new EventHandler(this.chkPdbLine1_CheckedChanged);
      if (this._chkPdbLine1 != null)
        this._chkPdbLine1.CheckedChanged -= eventHandler;
      this._chkPdbLine1 = value;
      if (this._chkPdbLine1 == null)
        return;
      this._chkPdbLine1.CheckedChanged += eventHandler;
    }
  }

  internal virtual Panel panAgdb
  {
    [DebuggerNonUserCode] get { return this._panAgdb; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._panAgdb = value; }
  }

  internal virtual Panel panPdb
  {
    [DebuggerNonUserCode] get { return this._panPdb; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._panPdb = value; }
  }

  internal virtual Panel panMldb
  {
    [DebuggerNonUserCode] get { return this._panMldb; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._panMldb = value; }
  }

  internal virtual Panel panCgdb
  {
    [DebuggerNonUserCode] get { return this._panCgdb; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._panCgdb = value; }
  }

  internal virtual TextBox txtCgdbMultiAddr
  {
    [DebuggerNonUserCode] get { return this._txtCgdbMultiAddr; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtCgdbMultiAddr = value;
    }
  }

  internal virtual Label lblCgdbAddr
  {
    [DebuggerNonUserCode] get { return this._lblCgdbAddr; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblCgdbAddr = value;
    }
  }

  internal virtual TextBox txtPdbMultiAddr
  {
    [DebuggerNonUserCode] get { return this._txtPdbMultiAddr; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtPdbMultiAddr = value;
    }
  }

  internal virtual Label Label5
  {
    [DebuggerNonUserCode] get { return this._Label5; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set { this._Label5 = value; }
  }

  internal virtual TextBox txtSrdPfnoAgdb
  {
    [DebuggerNonUserCode] get { return this._txtSrdPfnoAgdb; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtSrdPfnoAgdb = value;
    }
  }

  internal virtual Label lblSrdPfAgdb
  {
    [DebuggerNonUserCode] get { return this._lblSrdPfAgdb; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblSrdPfAgdb = value;
    }
  }

  internal virtual TextBox txtSrdPfnoPdb
  {
    [DebuggerNonUserCode] get { return this._txtSrdPfnoPdb; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._txtSrdPfnoPdb = value;
    }
  }

  internal virtual Label lblSrdPfPdb
  {
    [DebuggerNonUserCode] get { return this._lblSrdPfPdb; }
    [DebuggerNonUserCode, MethodImpl(MethodImplOptions.Synchronized)] set
    {
      this._lblSrdPfPdb = value;
    }
  }

  private void btnSave_Click(object sender, EventArgs e)
  {
    network_db_read.set_station_details(this.txtStationName.Text, this.txtStationCode.Text, this.txtRegionName.Text, this.txtRegionCode.Text, this.txtAgdbMsg1.Text, this.txtAgdbMsg2.Text, this.txtAgdbMsg3.Text, this.txtPdbMsg1.Text, this.txtMldbMsg1.Text, this.txtMldbMsg2.Text, this.txtMldbMsg3.Text);
    try
    {
      string str = "Z:\\Database\\StationDetails.mdb";
      string sourceFileName = "C:\\IPIS\\Database\\StationDetails.mdb";
      if (!File.Exists(str))
        File.Create(str);
      bool overwrite = true;
      MyProject.Computer.FileSystem.CopyFile(sourceFileName, str, overwrite);
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      ProjectData.ClearProjectError();
    }
  }

  private void btnExit_Click(object sender, EventArgs e)
  {
    frmMainFormIPIS.def_pkt_status = false;
    this.Close();
  }

  private void frmStationDetails_FormClosed(object sender, FormClosedEventArgs e)
  {
    frmMainFormIPIS.def_pkt_status = false;
  }

  private void frmStationDetails_Load(object sender, EventArgs e)
  {
    string empty1 = string.Empty;
    string empty2 = string.Empty;
    string empty3 = string.Empty;
    string empty4 = string.Empty;
    string empty5 = string.Empty;
    string empty6 = string.Empty;
    string empty7 = string.Empty;
    string empty8 = string.Empty;
    string empty9 = string.Empty;
    string empty10 = string.Empty;
    string empty11 = string.Empty;
    frmMainFormIPIS.def_pkt_status = false;
    string empty12 = string.Empty;
    int num1 = 0;
    try
    {
      network_db_read.get_station_details(ref empty1, ref empty2, ref empty3, ref empty4, ref empty5, ref empty6, ref empty7, ref empty8, ref empty9, ref empty10, ref empty11);
      network_db_read.get_platform_details();
      this.txtStationName.Text = Strings.Trim(empty1);
    }
    catch (Exception ex)
    {
      ProjectData.SetProjectError(ex);
      Exception exception = ex;
      int num2 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, exception.Message, "Msg Box", 0, 0, 0);
      ProjectData.ClearProjectError();
    }
    this.txtStationCode.Text = Strings.Trim(empty2);
    this.txtRegionName.Text = Strings.Trim(empty3);
    this.txtRegionCode.Text = Strings.Trim(empty4);
    this.txtAgdbMsg1.Text = Strings.Trim(empty5);
    this.txtAgdbMsg2.Text = Strings.Trim(empty6);
    this.txtAgdbMsg3.Text = Strings.Trim(empty7);
    this.txtPdbMsg1.Text = Strings.Trim(empty8);
    this.txtMldbMsg1.Text = Strings.Trim(empty9);
    this.txtMldbMsg2.Text = Strings.Trim(empty10);
    this.txtMldbMsg3.Text = Strings.Trim(empty11);
    int index1 = 0;
    this.cmbPfnoCgdb.Items.Clear();
    this.cmbPfnoAgdb.Items.Clear();
    this.cmbPfnoPdb.Items.Clear();
    while (index1 < frmMainFormIPIS.pfno_cnt)
    {
      this.cmbPfnoCgdb.Items.Add((object) frmMainFormIPIS.platform_nos[index1]);
      this.cmbPfnoAgdb.Items.Add((object) frmMainFormIPIS.platform_nos[index1]);
      this.cmbPfnoPdb.Items.Add((object) frmMainFormIPIS.platform_nos[index1]);
      checked { ++index1; }
    }
    num1 = 0;
    this.cmbAddrMldb.Items.Clear();
    this.cmbNameMldb.Items.Clear();
    int index2 = 0;
    while (index2 < (int) taddb_msg.mldb_dis_brd.no_of_mldbs)
    {
      this.cmbAddrMldb.Items.Add((object) taddb_msg.mldb_dis_brd.mdlb[index2].mldb_addr);
      this.cmbNameMldb.Items.Add((object) taddb_msg.mldb_dis_brd.mdlb[index2].mldb_name);
      checked { ++index2; }
    }
  }

  private void chkAgdbLine1_CheckedChanged(object sender, EventArgs e)
  {
    if (this.chkAgdbLine1.Checked)
    {
      this.txtAgdbMsg1.Enabled = true;
      this.panAgdb.Enabled = true;
    }
    else
    {
      this.txtAgdbMsg1.Enabled = false;
      if (!this.chkAgdbLine2.Checked & !this.chkAgdbLine3.Checked)
        this.panAgdb.Enabled = false;
    }
  }

  private void cmbPfnoAgdb_SelectedIndexChanged(object sender, EventArgs e)
  {
    string text1 = this.cmbPfnoAgdb.Text;
    string[] strArray = new string[27];
    byte[] numArray = new byte[27];
    this.cmbAgdbAddr.Text = string.Empty;
    this.cmbAgdbAddr.Items.Clear();
    this.cmbAgdbName.Text = string.Empty;
    this.cmbAgdbName.Items.Clear();
    this.txtSrdPfnoAgdb.Text = string.Empty;
    string text2 = this.cmbPfnoAgdb.Text;
    int num1 = 0;
    byte num2 = checked ((byte) num1);
    int num3 = (int) network_db_read.agdb_info_data(text2, ref strArray, ref numArray, ref num2);
    int num4 = (int) num2;
    if (num3 == 0)
    {
      int num5 = (int) basMsgBoxEx.MsgBoxMove(this.Handle.ToInt32(), "AGDB information for PLATFORM NO:{text1} is not available\r\nPlease check network configuration", "Msg Box", 0, 0, 0);
    }
    else
    {
      byte index1 = checked ((byte) ((int) network_db_read.get_pfno_int(this.cmbPfnoAgdb.Text) - 1));
      if (taddb_msg.agdb_dis_brd[(int) index1, 0].shared_platform)
      {
        this.txtSrdPfnoAgdb.Text = taddb_msg.agdb_dis_brd[(int) index1, 0].shared_platform_no;
        this.txtSrdPfnoAgdb.Visible = true;
        this.lblSrdPfAgdb.Visible = true;
      }
      else
      {
        this.txtSrdPfnoAgdb.Visible = false;
        this.lblSrdPfAgdb.Visible = false;
      }
      int index2 = 0;
      while (index2 < num4)
      {
        this.cmbAgdbAddr.Items.Add((object) numArray[index2]);
        this.cmbAgdbName.Items.Add((object) strArray[index2]);
        checked { ++index2; }
      }
      if (num4 > 0)
      {
        this.cmbAgdbName.Text = strArray[0];
        this.cmbAgdbAddr.Text = Conversions.ToString(numArray[0]);
      }
    }
  }

  private void chkAllPfnoAgdb_CheckedChanged(object sender, EventArgs e)
  {
    if (this.chkAllPfnoAgdb.Checked)
    {
      this.txtSrdPfnoAgdb.Visible = false;
      this.lblSrdPfAgdb.Visible = false;
      this.cmbPfnoAgdb.Enabled = false;
      string[] strArray = new string[27];
      byte[] numArray = new byte[27];
      this.cmbAgdbAddr.Items.Clear();
      this.cmbAgdbName.Items.Clear();
      this.txtSrdPfnoAgdb.Text = string.Empty;
      int num1 = 0;
      byte num2 = checked ((byte) num1);
      int num3 = (int) network_db_read.agdb_com_info_data(ref strArray, ref numArray, ref num2);
      int num4 = (int) num2;
      if ((uint) num3 <= 0U)
        return;
      int index = 0;
      while (index < num4)
      {
        this.cmbAgdbAddr.Items.Add((object) numArray[index]);
        this.cmbAgdbName.Items.Add((object) strArray[index]);
        checked { ++index; }
      }
    }
    else
    {
      this.cmbPfnoAgdb.Enabled = true;
      this.cmbAgdbAddr.Items.Clear();
      this.cmbAgdbName.Items.Clear();
    }
  }

  private void chkAgdbLine2_CheckedChanged(object sender, EventArgs e)
  {
    if (this.chkAgdbLine2.Checked)
    {
      this.txtAgdbMsg2.Enabled = true;
      this.panAgdb.Enabled = true;
    }
    else
    {
      this.txtAgdbMsg2.Enabled = false;
      if (!this.chkAgdbLine1.Checked & !this.chkAgdbLine3.Checked)
        this.panAgdb.Enabled = false;
    }
  }

  private void chkAgdbLine3_CheckedChanged(object sender, EventArgs e)
  {
    if (this.chkAgdbLine3.Checked)
    {
      this.txtAgdbMsg3.Enabled = true;
      this.panAgdb.Enabled = true;
    }
    else
    {
      this.txtAgdbMsg3.Enabled = false;
      if (!this.chkAgdbLine1.Checked & !this.chkAgdbLine2.Checked)
        this.panAgdb.Enabled = false;
    }
  }

  private void chkPdbLine1_CheckedChanged(object sender, EventArgs e)
  {
    if (this.chkPdbLine1.Checked)
    {
      this.txtPdbMsg1.Enabled = true;
      this.panPdb.Enabled = true;
    }
    else
    {
      this.txtPdbMsg1.Enabled = false;
      this.panPdb.Enabled = false;
    }
  }

  private void chkMldbLine1_CheckedChanged(object sender, EventArgs e)
  {
    if (this.chkMldbLine1.Checked)
    {
      this.txtMldbMsg1.Enabled = true;
      this.panMldb.Enabled = true;
    }
    else
    {
      this.txtMldbMsg1.Enabled = false;
      if (!this.chkMldbLine2.Checked & !this.chkMldbLine3.Checked)
        this.panMldb.Enabled = false;
    }
  }

  private void chkMldbLine2_CheckedChanged(object sender, EventArgs e)
  {
    if (this.chkMldbLine2.Checked)
    {
      this.txtMldbMsg2.Enabled = true;
      this.panMldb.Enabled = true;
    }
    else
    {
      this.txtMldbMsg2.Enabled = false;
      if (!this.chkMldbLine1.Checked & !this.chkMldbLine3.Checked)
        this.panMldb.Enabled = false;
    }
  }

  private void chkMldbLine3_CheckedChanged(object sender, EventArgs e)
  {
    if (this.chkMldbLine3.Checked)
    {
      this.txtMldbMsg3.Enabled = true;
      this.panMldb.Enabled = true;
    }
    else
    {
      this.txtMldbMsg3.Enabled = false;
      if (!this.chkMldbLine1.Checked & !this.chkMldbLine2.Checked)
        this.panMldb.Enabled = false;
    }
  }

  private void btnSendCgdb_Click(object sender, EventArgs e)
  {
    byte[] station_name = new byte[5];
    byte[] region_name = new byte[5];
    int num1 = 0;
    if (Operators.CompareString(this.txtCgdbMultiAddr.Text, string.Empty, false) == 0)
    {
      int num2 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Please Select CGDB Address", "Msg Box", 0, 0, 0);
    }
    else
    {
      if (this.btnSendCgdb.BackColor == Color.Green)
        return;
      try
      {
        this.btnSendCgdb.BackColor = Color.Green;
        this.btnSendCgdb.UseWaitCursor = true;
        if (Operators.CompareString(this.cmbPfnoCgdb.Text, string.Empty, false) == 0)
        {
          int num3 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Please Select Platform No", "Msg Box", 0, 0, 0);
          return;
        }
        byte pfnoInt = network_db_read.get_pfno_int(this.cmbPfnoCgdb.Text.Trim());
        if (pfnoInt != (byte) 0)
          checked { --pfnoInt; }
        byte multicastAddr = cgdb_dis.cgdb_dis_brd[(int) pfnoInt, 0].multicast_addr;
        byte[] bytes1 = new ASCIIEncoding().GetBytes(this.txtStationCode.Text);
        int length1 = this.txtStationCode.Text.Length;
        int index1 = 0;
        while (index1 < 4)
        {
          station_name[index1] = index1 >= length1 ? (byte) 0 : bytes1[index1];
          checked { ++index1; }
        }
        byte[] bytes2 = new ASCIIEncoding().GetBytes(this.txtRegionCode.Text);
        int index2 = 0;
        int length2 = bytes2.Length;
        while (index2 < 4)
        {
          region_name[index2] = index2 >= length2 ? (byte) 0 : bytes2[index2];
          checked { ++index2; }
        }
        byte pdchAddr = cgdb_dis.cgdb_dis_brd[(int) pfnoInt, 0].pdch_addr;
        byte swithcingTime = cgdb_dis.cgdb_dis_brd[(int) pfnoInt, 0].swithcing_time;
        byte videoType = cgdb_dis.cgdb_dis_brd[(int) pfnoInt, 0].video_type;
        while (!frmMainFormIPIS.def_pkt_status & !frmMainFormIPIS.display_status & !frmMainFormIPIS.setcfg_status & !frmMainFormIPIS.diag_status & !frmMainFormIPIS.msg_pdb_status & !frmMainFormIPIS.msg_mldb_status & !frmMainFormIPIS.cgs_status & !frmMainFormIPIS.link_check_status)
        {
          frmMainFormIPIS.def_pkt_status = true;
          byte num4 = cgdb_api.cgdb_default_msg(pdchAddr, multicastAddr, station_name, region_name, this.cmbPfnoCgdb.Text, swithcingTime, videoType);
          if (num4 == (byte) 3)
          {
            Log_file.Log(string.Format("COM Port Failure"));
            int num5 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "COM PORT FAILURE", "Msg Box", 0, 0, 0);
            goto label_23;
          }
          if (num4 == (byte) 2 | num4 == (byte) 0)
          {
            if (num1 < 0)
            {
              checked { ++num1; }
            }
            else
            {
              cgdb_dis.cgdb_dis_brd[(int) pfnoInt, 0].link_status = (byte) 0;
              int num6 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "PDB Address:{this.txtPdbMultiAddr.Text} DATA PAKCET IS UNSUCCESSFULL", "Msg Box", 0, 0, 0);
              goto label_23;
            }
          }
          else
            goto label_23;
        }
        int num7 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "COM PORT Busy, Please try again Later", "Msg Box", 0, 0, 0);
        return;
      }
      catch (Exception ex)
      {
        ProjectData.SetProjectError(ex);
        int num8 = (int) MessageBox.Show(ex.Message);
        ProjectData.ClearProjectError();
      }
label_23:
      frmMainFormIPIS.def_pkt_status = false;
      this.btnSendCgdb.BackColor = Color.LightGray;
      this.btnSendCgdb.UseWaitCursor = false;
    }
  }

  private void btnSendAgdb_Click(object sender, EventArgs e)
  {
    byte[] agdb_default_msg1 = new byte[241];
    byte[] agdb_default_msg2 = new byte[241];
    byte[] agdb_default_msg3 = new byte[241];
    int num1 = 0;
    if (Operators.CompareString(this.cmbAgdbAddr.Text, string.Empty, false) == 0)
    {
      int num2 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Please Select AGDB Address", "Msg Box", 0, 0, 0);
    }
    else
    {
      if (this.btnSendAgdb.BackColor == Color.Green)
        return;
      try
      {
        this.btnSendAgdb.BackColor = Color.Green;
        this.btnSendAgdb.UseWaitCursor = true;
        if (Operators.CompareString(this.cmbPfnoAgdb.Text, string.Empty, false) == 0 & !this.chkAllPfnoAgdb.Checked)
        {
          int num3 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Please Select Platform No", "Msg Box", 0, 0, 0);
          goto label_24;
        }
        byte agdb_addr = Conversions.ToByte(this.cmbAgdbAddr.Text);
        string msg1 = !this.chkAgdbLine1.Checked ? string.Empty : this.txtAgdbMsg1.Text.Trim();
        string msg2 = !this.chkAgdbLine2.Checked ? string.Empty : this.txtAgdbMsg2.Text.Trim();
        string msg3 = !this.chkAgdbLine3.Checked ? string.Empty : this.txtAgdbMsg3.Text.Trim();
        agdb_byte_construct.agdb_default_msg_bytes(msg1, ref agdb_default_msg1);
        agdb_byte_construct.agdb_default_msg_bytes(msg2, ref agdb_default_msg2);
        agdb_byte_construct.agdb_default_msg_bytes(msg3, ref agdb_default_msg3);
        int pfnoInt = (int) network_db_read.get_pfno_int(this.cmbPfnoAgdb.Text);
        int index1 = pfnoInt != 0 ? checked (pfnoInt - 1) : 0;
        int index2 = 0;
        while (index2 < 5)
        {
          taddb_msg.agdb_dis_brd[index1, index2].agdb_addr = (byte) 0;
          checked { ++index2; }
        }
        while (!frmMainFormIPIS.def_pkt_status & !frmMainFormIPIS.display_status & !frmMainFormIPIS.setcfg_status & !frmMainFormIPIS.diag_status & !frmMainFormIPIS.msg_pdb_status & !frmMainFormIPIS.msg_mldb_status & !frmMainFormIPIS.cgs_status & !frmMainFormIPIS.link_check_status)
        {
          frmMainFormIPIS.def_pkt_status = true;
          byte index3 = 0;
          byte num4 = agdb_api.agdb_message(agdb_addr, agdb_default_msg1, agdb_default_msg2, agdb_default_msg3, (byte) 0, this.cmbPfnoAgdb.Text, taddb_msg.agdb_dis_brd[(int) index3, index1].swithcing_time, taddb_msg.agdb_dis_brd[(int) index3, index1].video_type, (byte) 1);
          if (num4 == (byte) 3)
          {
            Log_file.Log(string.Format("COM Port Failure"));
            int num5 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "COM PORT FAILURE", "Msg Box", 0, 0, 0);
            goto label_23;
          }
          if (num4 == (byte) 2 | num4 == (byte) 0)
          {
            if (num1 < 0)
            {
              checked { ++num1; }
            }
            else
            {
              int index4 = 0;
              while (index4 < (int) taddb_msg.no_of_agdbs_pfno[index1])
              {
                if ((int) agdb_addr == (int) taddb_msg.agdb_dis_brd[index1, index4].agdb_addr)
                  taddb_msg.agdb_dis_brd[index1, index4].link_status = (byte) 0;
                checked { ++index4; }
              }
              int num6 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "AGDB Address:{this.cmbAgdbAddr.Text} DATA PAKCET IS UNSUCCESSFULL", "Msg Box", 0, 0, 0);
              goto label_23;
            }
          }
          else
            goto label_23;
        }
        int num7 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "COM PORT Busy, Please try again Later", "Msg Box", 0, 0, 0);
        goto label_24;
      }
      catch (Exception ex)
      {
        ProjectData.SetProjectError(ex);
        int num8 = (int) MessageBox.Show(ex.Message);
        ProjectData.ClearProjectError();
      }
label_23:
label_24:
      frmMainFormIPIS.def_pkt_status = false;
      this.btnSendAgdb.BackColor = Color.LightGray;
      this.btnSendAgdb.UseWaitCursor = false;
    }
  }

  private void btnSendPdb_Click(object sender, EventArgs e)
  {
    string str = this.txtPdbMsg1.Text.Trim();
    byte[] pdb_msg = new byte[34];
    int num1 = 0;
    if (Operators.CompareString(this.txtPdbMultiAddr.Text, string.Empty, false) == 0)
    {
      int num2 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "Please Select the platform No which has multicast address", "Msg Box", 0, 0, 0);
    }
    else
    {
      if (this.btnSendPdb.BackColor == Color.Green)
        return;
      try
      {
        this.btnSendPdb.BackColor = Color.Green;
        this.btnSendPdb.UseWaitCursor = true;
        int index1 = 0;
        while (index1 < 33)
        {
          pdb_msg[index1] = (byte) 0;
          checked { ++index1; }
        }
        pdb_msg[0] = (byte) 33;
        byte num3 = checked ((byte) Math.Round(unchecked ((double) checked (33 - str.Length) / 2.0)));
        int index2 = num3 != (byte) 0 ? checked ((int) num3 + 1) : 1;
        int index3 = 0;
        while (index3 < str.Length)
        {
          pdb_msg[index2] = checked ((byte) str[index3]);
          checked { ++index2; }
          checked { ++index3; }
        }
        byte pfnoInt = network_db_read.get_pfno_int(this.cmbPfnoPdb.Text.Trim());
        if (pfnoInt == (byte) 0)
          return;
        byte index4 = checked ((byte) ((int) pfnoInt - 1));
        byte multicastAddr = taddb_msg.pdb_dis_brd[(int) index4, 0].multicast_addr;
        byte swithcingTime = taddb_msg.pdb_dis_brd[(int) index4, 0].swithcing_time;
        byte videoType = taddb_msg.pdb_dis_brd[(int) index4, 0].video_type;
        byte effectSpeed = taddb_msg.pdb_dis_brd[(int) index4, 0].effect_speed;
        byte effect = taddb_msg.pdb_dis_brd[(int) index4, 0].effect;
        while (!frmMainFormIPIS.def_pkt_status & !frmMainFormIPIS.display_status & !frmMainFormIPIS.setcfg_status & !frmMainFormIPIS.diag_status & !frmMainFormIPIS.msg_pdb_status & !frmMainFormIPIS.msg_mldb_status & !frmMainFormIPIS.cgs_status & !frmMainFormIPIS.link_check_status)
        {
          frmMainFormIPIS.def_pkt_status = true;
          byte num4 = pdb_api.pdb_message(multicastAddr, ref pdb_msg, 34, (byte) 0, this.cmbPfnoPdb.Text, swithcingTime, videoType, effect, (byte) 1, effectSpeed);
          if (num4 == (byte) 3)
          {
            Log_file.Log(string.Format("COM Port Failure"));
            int num5 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "COM PORT FAILURE", "Msg Box", 0, 0, 0);
            goto label_21;
          }
          if (num4 == (byte) 2 | num4 == (byte) 0)
          {
            if (num1 < 0)
            {
              checked { ++num1; }
            }
            else
            {
              taddb_msg.pdb_dis_brd[(int) index4, 0].link_status = (byte) 0;
              int num6 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "PDB Address:{this.txtPdbMultiAddr.Text} DATA PAKCET IS UNSUCCESSFULL", "Msg Box", 0, 0, 0);
              goto label_21;
            }
          }
          else
            goto label_21;
        }
        int num7 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "COM PORT Busy, Please try again Later", "Msg Box", 0, 0, 0);
        return;
      }
      catch (Exception ex)
      {
        ProjectData.SetProjectError(ex);
        int num8 = (int) MessageBox.Show(ex.Message);
        ProjectData.ClearProjectError();
      }
label_21:
      frmMainFormIPIS.def_pkt_status = false;
      this.btnSendPdb.BackColor = Color.LightGray;
      this.btnSendPdb.UseWaitCursor = false;
    }
  }

  private void btnSendMldb_Click(object sender, EventArgs e)
  {
    byte[] mldb_msg = new byte[103];
    int num1 = 0;
    if (Operators.CompareString(this.cmbAddrMldb.Text, string.Empty, false) == 0)
    {
      int num2 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, " Please Select MLDB Address ", "Msg Box", 0, 0, 0);
    }
    else
    {
      if (this.btnSendMldb.BackColor == Color.Green)
        return;
      try
      {
        this.btnSendMldb.BackColor = Color.Green;
        this.btnSendMldb.UseWaitCursor = true;
        byte mldb_addr = Conversions.ToByte(this.cmbAddrMldb.Text);
        string str1 = !this.chkMldbLine1.Checked ? string.Empty : this.txtMldbMsg1.Text.Trim();
        string str2 = !this.chkMldbLine2.Checked ? string.Empty : this.txtMldbMsg2.Text.Trim();
        string str3 = !this.chkMldbLine3.Checked ? string.Empty : this.txtMldbMsg3.Text.Trim();
        int index1 = 0;
        while (index1 < 99)
        {
          mldb_msg[index1] = (byte) 0;
          checked { ++index1; }
        }
        mldb_msg[0] = (byte) 99;
        if (str1.Length > 0)
        {
          byte num3 = checked ((byte) Math.Round(unchecked ((double) checked (33 - str1.Length) / 2.0)));
          int index2 = num3 != (byte) 0 ? checked ((int) num3 + 1) : 1;
          int index3 = 0;
          while (index3 < str1.Length)
          {
            mldb_msg[index2] = checked ((byte) str1[index3]);
            checked { ++index2; }
            checked { ++index3; }
          }
        }
        if (str2.Length > 0)
        {
          byte num4 = checked ((byte) Math.Round(unchecked ((double) checked (33 - str2.Length) / 2.0)));
          int index4 = num4 != (byte) 0 ? checked (33 + (int) num4 + 1) : 34;
          int index5 = 0;
          while (index5 < str2.Length)
          {
            mldb_msg[index4] = checked ((byte) str2[index5]);
            checked { ++index5; }
            checked { ++index4; }
          }
        }
        if (str3.Length > 0)
        {
          byte num5 = checked ((byte) Math.Round(unchecked ((double) checked (33 - str3.Length) / 2.0)));
          int index6 = num5 != (byte) 0 ? checked (66 + (int) num5 + 1) : 67;
          int index7 = 0;
          while (index7 < str3.Length)
          {
            mldb_msg[index6] = checked ((byte) str3[index7]);
            checked { ++index7; }
            checked { ++index6; }
          }
        }
        int index8 = 0;
        while (index8 < (int) taddb_msg.mldb_dis_brd.no_of_mldbs && (int) mldb_addr != (int) taddb_msg.mldb_dis_brd.mdlb[index8].mldb_addr)
          checked { ++index8; }
        byte mldb_effect = 1;
        byte effectSpeed = taddb_msg.mldb_dis_brd.mdlb[index8].effect_speed;
        while (!frmMainFormIPIS.def_pkt_status & !frmMainFormIPIS.display_status & !frmMainFormIPIS.setcfg_status & !frmMainFormIPIS.diag_status & !frmMainFormIPIS.msg_pdb_status & !frmMainFormIPIS.msg_mldb_status & !frmMainFormIPIS.cgs_status & !frmMainFormIPIS.link_check_status)
        {
          frmMainFormIPIS.def_pkt_status = true;
          byte num6 = mldb_api.mldb_message(mldb_addr, ref mldb_msg, mldb_effect, 100, (byte) 0, taddb_msg.mldb_dis_brd.mdlb[index8].swithcing_time, taddb_msg.mldb_dis_brd.mdlb[index8].video_type, (byte) 1, effectSpeed);
          if (num6 == (byte) 3)
          {
            Log_file.Log(string.Format("COM Port Failure"));
            int num7 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "COM PORT FAILURE", "Msg Box", 0, 0, 0);
            goto label_32;
          }
          if (num6 == (byte) 2 | num6 == (byte) 0)
          {
            if (num1 < 0)
            {
              checked { ++num1; }
            }
            else
            {
              taddb_msg.mldb_dis_brd.mdlb[index8].link_status = (byte) 0;
              int num8 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "MLDB Address:{this.cmbAddrMldb.Text} DATA PAKCET IS UNSUCCESSFULL", "Msg Box", 0, 0, 0);
              goto label_32;
            }
          }
          else
            goto label_32;
        }
        int num9 = (int) basMsgBoxEx.MsgBoxMove(frmMainFormIPIS.main_form_handle, "COM PORT Busy, Please try again Later", "Msg Box", 0, 0, 0);
        return;
      }
      catch (Exception ex)
      {
        ProjectData.SetProjectError(ex);
        int num10 = (int) MessageBox.Show(ex.Message);
        ProjectData.ClearProjectError();
      }
label_32:
      frmMainFormIPIS.def_pkt_status = false;
      this.btnSendMldb.BackColor = Color.LightGray;
      this.btnSendMldb.UseWaitCursor = false;
    }
  }

  private void cmbPfnoCgdb_SelectedIndexChanged(object sender, EventArgs e)
  {
    byte pfnoInt = network_db_read.get_pfno_int(this.cmbPfnoCgdb.Text.Trim());
    if (pfnoInt == (byte) 0)
      return;
    byte index = checked ((byte) ((int) pfnoInt - 1));
    this.txtCgdbMultiAddr.Text = Conversions.ToString(cgdb_dis.cgdb_dis_brd[(int) index, 0].multicast_addr);
  }

  private void cmbPfnoPdb_SelectedIndexChanged(object sender, EventArgs e)
  {
    byte pfnoInt = network_db_read.get_pfno_int(this.cmbPfnoPdb.Text.Trim());
    if (pfnoInt == (byte) 0)
      return;
    byte index = checked ((byte) ((int) pfnoInt - 1));
    this.txtPdbMultiAddr.Text = Conversions.ToString(taddb_msg.pdb_dis_brd[(int) index, 0].multicast_addr);
    if (taddb_msg.pdb_dis_brd[(int) index, 0].shared_platform)
    {
      this.txtSrdPfnoPdb.Text = taddb_msg.pdb_dis_brd[(int) index, 0].shared_platform_no;
      this.txtSrdPfnoPdb.Visible = true;
      this.lblSrdPfPdb.Visible = true;
    }
    else
    {
      this.txtSrdPfnoPdb.Visible = false;
      this.lblSrdPfPdb.Visible = false;
    }
  }

  private void cmbAgdbAddr_SelectedIndexChanged(object sender, EventArgs e)
  {
    byte num = Conversions.ToByte(this.cmbAgdbAddr.Text);
    byte video = 0;
    bool shared_platform = false;
    string empty = string.Empty;
    if (this.chkAllPfnoAgdb.Checked)
    {
      ComboBox cmbAgdbName = this.cmbAgdbName;
      string text = cmbAgdbName.Text;
      network_db_read.agdb_com_addr_data(ref text, Conversions.ToString(num), ref video);
      cmbAgdbName.Text = text;
      this.cmbAgdbAddr.Text = Conversions.ToString(num);
    }
    else
    {
      ComboBox cmbAgdbName = this.cmbAgdbName;
      string text = cmbAgdbName.Text;
      network_db_read.agdb_addr_data(ref text, Conversions.ToByte(this.cmbAgdbAddr.Text), this.cmbPfnoAgdb.Text, ref video, ref shared_platform, ref empty);
      cmbAgdbName.Text = text;
    }
  }

  private void cmbAgdbName_SelectedIndexChanged(object sender, EventArgs e)
  {
    byte agdb_addr = 0;
    string empty = string.Empty;
    byte video = 0;
    if (this.chkAllPfnoAgdb.Checked)
    {
      string text = this.cmbAgdbName.Text;
      string str = Conversions.ToString(agdb_addr);
      network_db_read.agdb_com_name_data(text, ref str, ref video);
      this.cmbAgdbAddr.Text = Conversions.ToString(Conversions.ToByte(str));
    }
    else
    {
      bool shared_platform = false;
      network_db_read.agdb_name_data(this.cmbAgdbName.Text, ref agdb_addr, this.cmbPfnoAgdb.Text, ref video, ref shared_platform, ref empty);
      this.cmbAgdbAddr.Text = agdb_addr.ToString();
    }
  }

  private void cmbAddrMldb_SelectedIndexChanged(object sender, EventArgs e)
  {
    int index = 0;
    while (index < (int) taddb_msg.mldb_dis_brd.no_of_mldbs)
    {
      if ((double) taddb_msg.mldb_dis_brd.mdlb[index].mldb_addr == Conversions.ToDouble(this.cmbAddrMldb.Text))
      {
        this.cmbNameMldb.Text = taddb_msg.mldb_dis_brd.mdlb[index].mldb_name;
        break;
      }
      checked { ++index; }
    }
  }

  private void cmbNameMldb_SelectedIndexChanged(object sender, EventArgs e)
  {
    int index = 0;
    while (index < (int) taddb_msg.mldb_dis_brd.no_of_mldbs)
    {
      if (Operators.CompareString(taddb_msg.mldb_dis_brd.mdlb[index].mldb_name, this.cmbNameMldb.Text, false) == 0)
      {
        this.cmbAddrMldb.Text = Conversions.ToString(taddb_msg.mldb_dis_brd.mdlb[index].mldb_addr);
        break;
      }
      checked { ++index; }
    }
  }
}

}