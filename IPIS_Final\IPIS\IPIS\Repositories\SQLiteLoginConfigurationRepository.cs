using System;
using System.Data.SQLite;
using IPIS.Models;
using IPIS.Repositories.Interfaces;
using IPIS.Utils;

namespace IPIS.Repositories
{
    public class SQLiteLoginConfigurationRepository : ILoginConfigurationRepository
    {
        private readonly string connectionString;

        public SQLiteLoginConfigurationRepository()
        {
            connectionString = Database.ConnectionString;
            EnsureTableExists();
        }

        private void EnsureTableExists()
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string createTableQuery = @"
                    CREATE TABLE IF NOT EXISTS LoginConfiguration (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        StationName TEXT NOT NULL DEFAULT 'IPIS',
                        WelcomeMessage TEXT NOT NULL DEFAULT 'Welcome to',
                        SubtitleMessage TEXT NOT NULL DEFAULT 'Integrated Passenger\nInformation System',
                        LogoPath TEXT DEFAULT '',
                        BackgroundImagePath TEXT DEFAULT '',
                        PrimaryColor TEXT NOT NULL DEFAULT '#007BFF',
                        SecondaryColor TEXT NOT NULL DEFAULT '#C8DCFF',
                        BackgroundColor TEXT NOT NULL DEFAULT '#F0F4F8',
                        StationTextColor TEXT NOT NULL DEFAULT '#333333',
                        UseCustomLogo INTEGER NOT NULL DEFAULT 0,
                        UseBackgroundImage INTEGER NOT NULL DEFAULT 0,
                        CreatedAt TEXT NOT NULL,
                        UpdatedAt TEXT NOT NULL
                    )";

                using (var command = new SQLiteCommand(createTableQuery, connection))
                {
                    command.ExecuteNonQuery();
                }

                // Add StationTextColor column if it doesn't exist (for existing databases)
                EnsureStationTextColorColumn(connection);
            }
        }

        private void EnsureStationTextColorColumn(SQLiteConnection connection)
        {
            try
            {
                // Check if StationTextColor column exists
                string checkColumnQuery = "PRAGMA table_info(LoginConfiguration)";
                bool columnExists = false;

                using (var command = new SQLiteCommand(checkColumnQuery, connection))
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        if (reader["name"].ToString() == "StationTextColor")
                        {
                            columnExists = true;
                            break;
                        }
                    }
                }

                // Add column if it doesn't exist
                if (!columnExists)
                {
                    string addColumnQuery = "ALTER TABLE LoginConfiguration ADD COLUMN StationTextColor TEXT NOT NULL DEFAULT '#333333'";
                    using (var command = new SQLiteCommand(addColumnQuery, connection))
                    {
                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception)
            {
                // If there's any error, we'll handle it gracefully in GetLoginConfiguration
            }
        }

        public LoginConfiguration GetLoginConfiguration()
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = "SELECT * FROM LoginConfiguration ORDER BY Id DESC LIMIT 1";

                using (var command = new SQLiteCommand(query, connection))
                using (var reader = command.ExecuteReader())
                {
                    if (reader.Read())
                    {
                        // Safely get StationTextColor with fallback to default
                        string stationTextColor = "#333333"; // Default value
                        try
                        {
                            stationTextColor = reader["StationTextColor"].ToString();
                        }
                        catch (Exception)
                        {
                            // Column doesn't exist, use default
                        }

                        return new LoginConfiguration
                        {
                            Id = Convert.ToInt32(reader["Id"]),
                            StationName = reader["StationName"].ToString(),
                            WelcomeMessage = reader["WelcomeMessage"].ToString(),
                            SubtitleMessage = reader["SubtitleMessage"].ToString(),
                            LogoPath = reader["LogoPath"].ToString(),
                            BackgroundImagePath = reader["BackgroundImagePath"].ToString(),
                            PrimaryColor = reader["PrimaryColor"].ToString(),
                            SecondaryColor = reader["SecondaryColor"].ToString(),
                            BackgroundColor = reader["BackgroundColor"].ToString(),
                            StationTextColor = stationTextColor,
                            UseCustomLogo = Convert.ToBoolean(reader["UseCustomLogo"]),
                            UseBackgroundImage = Convert.ToBoolean(reader["UseBackgroundImage"]),
                            CreatedAt = DateTime.Parse(reader["CreatedAt"].ToString()),
                            UpdatedAt = DateTime.Parse(reader["UpdatedAt"].ToString())
                        };
                    }
                }
            }

            // Return default configuration if none exists
            return new LoginConfiguration();
        }

        public void SaveLoginConfiguration(LoginConfiguration configuration)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = @"
                    INSERT INTO LoginConfiguration
                    (StationName, WelcomeMessage, SubtitleMessage, LogoPath, BackgroundImagePath,
                     PrimaryColor, SecondaryColor, BackgroundColor, StationTextColor, UseCustomLogo, UseBackgroundImage,
                     CreatedAt, UpdatedAt)
                    VALUES
                    (@StationName, @WelcomeMessage, @SubtitleMessage, @LogoPath, @BackgroundImagePath,
                     @PrimaryColor, @SecondaryColor, @BackgroundColor, @StationTextColor, @UseCustomLogo, @UseBackgroundImage,
                     @CreatedAt, @UpdatedAt)";

                using (var command = new SQLiteCommand(query, connection))
                {
                    AddParameters(command, configuration);
                    command.ExecuteNonQuery();
                }
            }
        }

        public void UpdateLoginConfiguration(LoginConfiguration configuration)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = @"
                    UPDATE LoginConfiguration SET
                    StationName = @StationName,
                    WelcomeMessage = @WelcomeMessage,
                    SubtitleMessage = @SubtitleMessage,
                    LogoPath = @LogoPath,
                    BackgroundImagePath = @BackgroundImagePath,
                    PrimaryColor = @PrimaryColor,
                    SecondaryColor = @SecondaryColor,
                    BackgroundColor = @BackgroundColor,
                    StationTextColor = @StationTextColor,
                    UseCustomLogo = @UseCustomLogo,
                    UseBackgroundImage = @UseBackgroundImage,
                    UpdatedAt = @UpdatedAt
                    WHERE Id = @Id";

                using (var command = new SQLiteCommand(query, connection))
                {
                    AddParameters(command, configuration);
                    command.Parameters.AddWithValue("@Id", configuration.Id);
                    command.ExecuteNonQuery();
                }
            }
        }

        public bool ConfigurationExists()
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = "SELECT COUNT(*) FROM LoginConfiguration";

                using (var command = new SQLiteCommand(query, connection))
                {
                    return Convert.ToInt32(command.ExecuteScalar()) > 0;
                }
            }
        }

        private void AddParameters(SQLiteCommand command, LoginConfiguration configuration)
        {
            command.Parameters.AddWithValue("@StationName", configuration.StationName);
            command.Parameters.AddWithValue("@WelcomeMessage", configuration.WelcomeMessage);
            command.Parameters.AddWithValue("@SubtitleMessage", configuration.SubtitleMessage);
            command.Parameters.AddWithValue("@LogoPath", configuration.LogoPath ?? "");
            command.Parameters.AddWithValue("@BackgroundImagePath", configuration.BackgroundImagePath ?? "");
            command.Parameters.AddWithValue("@PrimaryColor", configuration.PrimaryColor);
            command.Parameters.AddWithValue("@SecondaryColor", configuration.SecondaryColor);
            command.Parameters.AddWithValue("@BackgroundColor", configuration.BackgroundColor);
            command.Parameters.AddWithValue("@StationTextColor", configuration.StationTextColor);
            command.Parameters.AddWithValue("@UseCustomLogo", configuration.UseCustomLogo);
            command.Parameters.AddWithValue("@UseBackgroundImage", configuration.UseBackgroundImage);
            command.Parameters.AddWithValue("@CreatedAt", configuration.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss"));
            command.Parameters.AddWithValue("@UpdatedAt", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
        }
    }
}
