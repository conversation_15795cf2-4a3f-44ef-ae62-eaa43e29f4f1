using System;
using System.Drawing;
using System.Windows.Forms;
using IPIS.Forms.User;
using IPIS.Forms.Configuration;
using IPIS.Models;
using IPIS.Services;
using IPIS.Repositories;

namespace IPIS
{
    /// <summary>
    /// Test class to verify the configurable login form functionality
    /// This can be used to test the login configuration features
    /// </summary>
    public static class TestLoginConfiguration
    {
        /// <summary>
        /// Test method to create a sample configuration and show the login form
        /// </summary>
        public static void TestConfigurableLogin()
        {
            try
            {
                // Create a test configuration
                var testConfig = new LoginConfiguration
                {
                    StationName = "Mumbai Central",
                    WelcomeMessage = "Welcome to",
                    SubtitleMessage = "Mumbai Central Railway Station\nIntegrated Passenger Information System",
                    PrimaryColor = "#FF6B35", // Orange
                    SecondaryColor = "#FFE5D9", // Light orange
                    BackgroundColor = "#F7F3F0", // Light cream
                    UseCustomLogo = false,
                    UseBackgroundImage = false
                };

                // Save the test configuration
                var configService = new LoginConfigurationService(new SQLiteLoginConfigurationRepository());
                configService.SaveLoginConfiguration(testConfig);

                // Show the login form with the new configuration
                using (var loginForm = new LoginForm())
                {
                    loginForm.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error testing login configuration: {ex.Message}", 
                    "Test Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Test method to show the login configuration form
        /// </summary>
        public static void TestConfigurationForm()
        {
            try
            {
                using (var configForm = new LoginConfigurationForm())
                {
                    configForm.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error testing configuration form: {ex.Message}", 
                    "Test Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Test method to reset configuration to defaults
        /// </summary>
        public static void ResetToDefaults()
        {
            try
            {
                var configService = new LoginConfigurationService(new SQLiteLoginConfigurationRepository());
                var defaultConfig = new LoginConfiguration();
                configService.SaveLoginConfiguration(defaultConfig);
                
                MessageBox.Show("Login configuration has been reset to defaults.", 
                    "Reset Complete", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error resetting configuration: {ex.Message}", 
                    "Reset Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Test method to create multiple station configurations
        /// </summary>
        public static void TestMultipleStationConfigs()
        {
            var stations = new[]
            {
                new { Name = "New Delhi", Color = "#E74C3C", Message = "Capital Railway Station" },
                new { Name = "Chennai Central", Color = "#3498DB", Message = "Southern Railway Hub" },
                new { Name = "Kolkata", Color = "#2ECC71", Message = "Eastern Railway Terminal" },
                new { Name = "Bangalore City", Color = "#9B59B6", Message = "Garden City Station" }
            };

            foreach (var station in stations)
            {
                try
                {
                    var config = new LoginConfiguration
                    {
                        StationName = station.Name,
                        WelcomeMessage = "Welcome to",
                        SubtitleMessage = $"{station.Message}\nIntegrated Passenger Information System",
                        PrimaryColor = station.Color,
                        SecondaryColor = "#ECF0F1",
                        BackgroundColor = "#F8F9FA",
                        UseCustomLogo = false,
                        UseBackgroundImage = false
                    };

                    var configService = new LoginConfigurationService(new SQLiteLoginConfigurationRepository());
                    configService.SaveLoginConfiguration(config);

                    // Show a preview of each configuration
                    using (var previewForm = new LoginPreviewForm(config))
                    {
                        previewForm.Text = $"Preview: {station.Name}";
                        if (previewForm.ShowDialog() != DialogResult.OK)
                            break; // User cancelled, stop showing previews
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error creating configuration for {station.Name}: {ex.Message}", 
                        "Configuration Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }
    }
}
